import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { DEFAULT_LANGUAGE, PREFERENCE_LANGUAGE } from '@resola-ai/shared-constants';
import { SUPPORTED_LANGUAGE } from '../constants';
import type { SupportedLanguage } from '../types/i18n';
import { useQueryParams } from './useQueryParams';

export const useSetDefaultLang = () => {
  const navigate = useNavigate();
  const queryParams = useQueryParams();
  const currentLangFromParam = queryParams.getLanguageQueryParam();

  const getPreferenceLanguage = () => {
    return localStorage.getItem(PREFERENCE_LANGUAGE) || DEFAULT_LANGUAGE;
  };

  const [lang, setLang] = useState(getPreferenceLanguage());

  useEffect(() => {
    const isSupported = Object.values(SUPPORTED_LANGUAGE).includes(
      currentLangFromParam as SupportedLanguage
    );

    const preferenceLanguage = getPreferenceLanguage();

    if (!currentLangFromParam || !isSupported || currentLangFromParam !== preferenceLanguage) {
      const url = new URL(window.location.href);
      url.searchParams.set('lang', preferenceLanguage);

      if (window.location.search !== url.search) {
        navigate(`${url.pathname}?${url.searchParams.toString()}`, { replace: true });
      }

      localStorage.setItem(PREFERENCE_LANGUAGE, preferenceLanguage);
      setLang(preferenceLanguage);
    } else {
      setLang(currentLangFromParam);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [navigate, currentLangFromParam]);

  return lang;
};
