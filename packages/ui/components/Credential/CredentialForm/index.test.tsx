import { act, fireEvent, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';
import { useForm } from 'react-hook-form';
import { vi } from 'vitest';

import type { SchemaField } from '../../FormChema/type';
import type { CardStatus, ICredentialPayload } from '../type';
import type { CredentialFormProps, CredentialFormRef } from './index';
import { CredentialForm } from './index';

// Mock Mantine components
vi.mock('@mantine/core', () => ({
  Box: ({ children, className, ...props }) => (
    <div className={className} data-testid="mantine-box" {...props}>
      {children}
    </div>
  ),
  Group: ({ children, className, ...props }) => (
    <div className={className} data-testid="mantine-group" {...props}>
      {children}
    </div>
  ),
  Stack: ({ children, className, ...props }) => (
    <div className={className} data-testid="mantine-stack" {...props}>
      {children}
    </div>
  ),
  Text: ({ children, className, ...props }) => (
    <div className={className} data-testid="mantine-text" {...props}>
      {children}
    </div>
  ),
}));

vi.mock('@mantine/emotion', () => ({
  createStyles: vi.fn(() => () => ({
    classes: {
      boxForm: 'boxForm',
      form: 'form',
      stack: 'stack',
    },
    cx: (...args) => args.filter(Boolean).join(' '),
  })),
}));

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(() => {
    const t = (key: string, options?: any) => {
      const translations: Record<string, string> = {
        'form.type': 'Type',
        'button.remove': 'Remove',
        'button.test': 'Test',
        'button.cancel': 'Cancel',
        'button.save': 'Save',
      };
      return translations[key] || key;
    };
    return { t };
  }),
}));

// Mock react-hook-form
const mockUseForm = {
  control: {},
  handleSubmit: vi.fn((fn) => fn),
  watch: vi.fn(() => 'test-scheme'),
  setValue: vi.fn(),
  getValues: vi.fn(() => ({})),
};

vi.mock('react-hook-form', () => ({
  useForm: vi.fn(() => mockUseForm),
}));

// Mock utility functions
vi.mock('../../../../utils/schema', () => ({
  getDefaultValues: vi.fn(() => ({ testField: 'defaultValue' })),
}));

// Mock child components
vi.mock('../../AlertStatus', () => ({
  default: ({ status, message, onClose }: any) => (
    <div data-testid="alert-status" data-status={status.status} data-message={message}>
      <button type="button" onClick={onClose} data-testid="alert-close">Close</button>
      {message}
    </div>
  ),
}));

vi.mock('../../DecaButton', () => ({
  DecaButton: ({ children, onClick, type, variant, size, ...props }: any) => (
    <button
      type={type}
      onClick={onClick}
      data-testid={`button-${variant || 'default'}`}
      data-variant={variant}
      data-size={size}
      {...props}
    >
      {children}
    </button>
  ),
}));

vi.mock('../CredentialTypeSelector', () => ({
  CredentialTypeSelector: ({ control, credentialTypes, onChange }: any) => (
    <div data-testid="credential-type-selector">
      <select
        onChange={(e) => onChange(e.target.value)}
        data-testid="type-selector"
      >
        <option value="">Empty</option>
        {Object.keys(credentialTypes).map((key) => (
          <option key={key} value={key}>
            {key}
          </option>
        ))}
      </select>
    </div>
  ),
}));

vi.mock('../DynamicSettingsForm', () => ({
  DynamicSettingsForm: React.forwardRef(({ control, selectedCredential, watch, ...props }: any, ref: any) => {
    React.useImperativeHandle(ref, () => ({
      focus: vi.fn(),
    }));
    return (
      <div data-testid="dynamic-settings-form" ref={ref}>
        <input data-testid="settings-input" />
      </div>
    );
  }),
}));


// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  return <div data-testid="test-wrapper">{children}</div>;
};

// Test component
const TestComponent = (props: Partial<CredentialFormProps>) => {
  const defaultSchema: SchemaField = {
    displayName: 'Test Credential',
    description: 'Test description',
    required: true,
    allowInput: false,
    reference: true,
    provider: 'test-provider',
    name: 'test-credential',
    credentials: {
      'test-scheme': {
        displayName: 'Test Scheme',
        description: 'Test scheme description',
        required: true,
        allowInput: false,
        reference: true,
        fields: {
          testField: {
            displayName: 'Test Field',
            description: 'Test field description',
            required: true,
            allowInput: true,
            reference: false,
            type: 'string',
          },
        },
      },
    },
    settings: {
      credential: {
        credentialTypes: ['test-scheme'],
        default: 'test-scheme',
      },
    },
    ...props.schema,
  };

  return (
    <TestWrapper>
      <CredentialForm
        schema={defaultSchema}
        credential={props.credential}
        className={props.className}
        onSubmit={props.onSubmit}
        onCancel={props.onCancel}
        onTest={props.onTest}
        onDelete={props.onDelete}
      />
    </TestWrapper>
  );
};

describe('CredentialForm', () => {
  const mockCredential: ICredentialPayload = {
    id: 'test-id',
    name: 'Test Credential',
    description: 'Test description',
    provider: 'test-provider',
    authenticationScheme: 'test-scheme',
    settings: { testField: 'testValue' },
  };

  const mockCardStatus: CardStatus = {
    status: 'success',
    message: 'Test message',
  };

  const mockSchema = {
    displayName: 'Test Schema',
    credentials: {
      'test-scheme': {
        displayName: 'Test Scheme',
        fields: [
          { name: 'test', type: 'string', required: true },
          { name: 'optional', type: 'string', required: false },
        ] as SchemaField[],
      },
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseForm.watch.mockReturnValue('test-scheme');
    mockUseForm.getValues.mockReturnValue({});
  });

  describe('Basic Rendering', () => {
    it('renders with basic props', () => {
      render(<TestComponent />);

      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
      expect(screen.getByText('Type')).toBeInTheDocument();
      expect(screen.getByTestId('credential-type-selector')).toBeInTheDocument();
      expect(screen.getByTestId('dynamic-settings-form')).toBeInTheDocument();
    });

    it('applies custom className', () => {
      render(<TestComponent className="custom-class" />);

      const form = screen.getByTestId('credential-form');
      expect(form).toBeInTheDocument();
    });

    it('renders without credentials when none provided', () => {
      const schemaWithoutCredentials = {
        credentials: {},
        settings: { credential: { credentialTypes: [] } },
      };

      render(<TestComponent schema={schemaWithoutCredentials} />);

      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
      expect(screen.queryByText('Type')).not.toBeInTheDocument();
    });
  });

  describe('Form Actions', () => {
    it('renders all action buttons', () => {
      render(<TestComponent credential={mockCredential} />);

      expect(screen.getByTestId('delete-button')).toBeInTheDocument(); // Delete button
      expect(screen.getByTestId('test-button')).toBeInTheDocument(); // Test button
      expect(screen.getByTestId('submit-button')).toBeInTheDocument(); // Save button
    });

    it('renders cancel button when onCancel provided', () => {
      const onCancel = vi.fn();
      render(<TestComponent onCancel={onCancel} />);

      expect(screen.getByTestId('cancel-button')).toBeInTheDocument(); // Cancel button
    });

    it('does not render delete button when no credential id', () => {
      const credentialWithoutId = { ...mockCredential, id: undefined };
      render(<TestComponent credential={credentialWithoutId} />);

      expect(screen.queryByTestId('delete-button')).not.toBeInTheDocument();
    });
  });

  describe('Form Submission', () => {
    it('handles form submission', async () => {
      const onSubmit = vi.fn();
      const user = userEvent.setup();

      render(<TestComponent onSubmit={onSubmit} />);

      const form = screen.getByTestId('credential-form');
      await act(async () => {
        await user.click(screen.getByTestId('submit-button'));
      });

      expect(onSubmit).toHaveBeenCalled();
    });

    it('handles test button click', async () => {
      const onTest = vi.fn();
      const user = userEvent.setup();

      render(<TestComponent onTest={onTest} />);

      const testButton = screen.getByTestId('test-button');
      await act(async () => {
        await user.click(testButton);
      });

      expect(onTest).toHaveBeenCalled();
    });

    it('handles delete button click', async () => {
      const onDelete = vi.fn();
      const user = userEvent.setup();

      render(<TestComponent credential={mockCredential} onDelete={onDelete} />);

      const deleteButton = screen.getByTestId('delete-button');
      await act(async () => {
        await user.click(deleteButton);
      });

      expect(onDelete).toHaveBeenCalledWith(mockCredential);
    });

    it('handles cancel button click', async () => {
      const onCancel = vi.fn();
      const user = userEvent.setup();

      render(<TestComponent onCancel={onCancel} />);

      const cancelButton = screen.getByTestId('cancel-button');
      await act(async () => {
        await user.click(cancelButton);
      });

      expect(onCancel).toHaveBeenCalled();
    });
  });

  describe('Status Display', () => {
    it('displays status when provided', () => {
      const TestWithStatus = () => {
        return (
          <TestWrapper>
            <CredentialForm
              schema={{
                displayName: 'Test',
                required: true,
                allowInput: false,
                reference: true,
                credentials: {},
                settings: { credential: { credentialTypes: [] } },
              }}
              onSubmit={(_, setStatus) => {
                setStatus?.(mockCardStatus);
                return Promise.resolve();
              }}
            />
          </TestWrapper>
        );
      };

      render(<TestWithStatus />);

      // Status should be displayed when setStatus is called
      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
    });

    it('handles status close', async () => {
      const TestWithStatus = () => {
        return (
          <TestWrapper>
            <CredentialForm
              schema={{
                displayName: 'Test',
                required: true,
                allowInput: false,
                reference: true,
                credentials: {},
                settings: { credential: { credentialTypes: [] } },
              }}
              onSubmit={(_, setStatus) => {
                setStatus?.(mockCardStatus);
                return Promise.resolve();
              }}
            />
          </TestWrapper>
        );
      };

      render(<TestWithStatus />);

      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
    });
  });

  describe('Credential Type Selection', () => {
    it('handles type change', async () => {
      const user = userEvent.setup();

      render(<TestComponent />);

      const typeSelector = screen.getByTestId('type-selector');
      await act(async () => {
        await user.selectOptions(typeSelector, 'test-scheme');
      });

      expect(mockUseForm.setValue).toHaveBeenCalled();
    });

    it('clears settings when type is empty', async () => {
      const user = userEvent.setup();

      render(<TestComponent />);

      const typeSelector = screen.getByTestId('type-selector');
      await act(async () => {
        await user.selectOptions(typeSelector, '');
      });

      expect(mockUseForm.setValue).toHaveBeenCalledWith('settings', undefined);
    });
  });

  describe('Credential Initialization', () => {
    it('initializes form with credential data', () => {
      render(<TestComponent credential={mockCredential} />);

      expect(mockUseForm.setValue).toHaveBeenCalledWith('id', mockCredential.id);
      expect(mockUseForm.setValue).toHaveBeenCalledWith('authenticationScheme', mockCredential.authenticationScheme);
      expect(mockUseForm.setValue).toHaveBeenCalledWith('name', mockCredential.name);
      expect(mockUseForm.setValue).toHaveBeenCalledWith('description', mockCredential.description);
      expect(mockUseForm.setValue).toHaveBeenCalledWith('provider', mockCredential.provider);
    });

    it('handles credential without settings', () => {
      const credentialWithoutSettings = { ...mockCredential, settings: undefined };
      render(<TestComponent credential={credentialWithoutSettings} />);

      expect(mockUseForm.setValue).toHaveBeenCalled();
    });
  });

  describe('Ref Methods', () => {
    it('exposes focus method through ref', () => {
      const ref = React.createRef<CredentialFormRef>();
      const mockFocus = vi.fn();

      // Mock the dynamic settings form ref
      const mockDynamicSettingsFormRef = { current: { focus: mockFocus } };
      vi.spyOn(React, 'useRef').mockReturnValue(mockDynamicSettingsFormRef);

      render(<TestComponent />);

      if (ref.current) {
        ref.current.focus();
        expect(mockFocus).toHaveBeenCalled();
      }
    });

    it('exposes setValue method through ref', () => {
      const ref = React.createRef<CredentialFormRef>();

      render(<TestComponent />);

      if (ref.current) {
        ref.current.setValue('name', 'new value');
        expect(mockUseForm.setValue).toHaveBeenCalledWith('name', 'new value');
      }
    });
  });

  describe('Edge Cases', () => {
    it('handles undefined schema', () => {
      render(<TestComponent schema={undefined as any} />);

      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
    });

    it('handles empty credentials object', () => {
      const schemaWithEmptyCredentials = {
        credentials: {},
        settings: { credential: { credentialTypes: [] } },
      };

      render(<TestComponent schema={schemaWithEmptyCredentials} />);

      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
    });

    it('handles missing settings', () => {
      const schemaWithoutSettings = {
        credentials: { 'test-scheme': {} },
      };

      render(<TestComponent schema={schemaWithoutSettings} />);

      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
    });

    it('handles undefined credential', () => {
      render(<TestComponent credential={undefined} />);

      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
    });
  });

  describe('Form State Management', () => {
    it('updates form values when credential changes', () => {
      const { rerender } = render(<TestComponent credential={mockCredential} />);

      const newCredential = { ...mockCredential, name: 'New Name' };
      rerender(<TestComponent credential={newCredential} />);

      expect(mockUseForm.setValue).toHaveBeenCalledWith('name', newCredential.name);
    });

    it('handles form reset when credential becomes undefined', () => {
      const { rerender } = render(<TestComponent credential={mockCredential} />);

      rerender(<TestComponent credential={undefined} />);

      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
    });
  });

  describe('Component Integration', () => {
    it('integrates with all child components', () => {
      render(<TestComponent credential={mockCredential} />);

      expect(screen.getByTestId('credential-type-selector')).toBeInTheDocument();
      expect(screen.getByTestId('dynamic-settings-form')).toBeInTheDocument();
      expect(screen.getByTestId('test-button')).toBeInTheDocument(); // Test button
      expect(screen.getByTestId('submit-button')).toBeInTheDocument(); // Save button
    });

    it('passes correct props to child components', () => {
      render(<TestComponent credential={mockCredential} />);

      const typeSelector = screen.getByTestId('credential-type-selector');
      const settingsForm = screen.getByTestId('dynamic-settings-form');

      expect(typeSelector).toBeInTheDocument();
      expect(settingsForm).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper form structure', () => {
      render(<TestComponent />);

      const form = screen.getByTestId('credential-form');
      expect(form.tagName).toBe('FORM');
    });

    it('has proper button types', () => {
      render(<TestComponent />);

      const saveButton = screen.getByTestId('submit-button');
      expect(saveButton).toHaveAttribute('type', 'submit');
    });
  });

  describe('Performance', () => {
    it('memoizes credentials and defaultCredential', () => {
      const { rerender } = render(<TestComponent />);

      // Rerender with same props should not cause unnecessary recalculations
      rerender(<TestComponent />);

      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
    });

    it('handles rapid prop changes', () => {
      const { rerender } = render(<TestComponent />);

      // Rapid changes should not cause errors
      for (let i = 0; i < 5; i++) {
        rerender(<TestComponent credential={{ ...mockCredential, name: `Name ${i}` }} />);
      }

      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
    });
  });

  describe('Ref Methods - Advanced', () => {
    it('calls focus on dynamicSettingsFormRef when focus is called', () => {
      const TestComponentWithRef = () => {
        const ref = React.useRef<CredentialFormRef>(null);
        
        React.useEffect(() => {
          if (ref.current) {
            ref.current.focus();
          }
        }, []);
        
        return <CredentialForm ref={ref} schema={mockSchema} />;
      };
      
      render(<TestComponentWithRef />);
      // The focus method should be available on the ref
      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
    });

    it('calls setValue when setValue is called on ref', () => {
      const setValueMock = vi.fn();
      const TestComponentWithRef = () => {
        const ref = React.useRef<CredentialFormRef>(null);
        
        React.useEffect(() => {
          if (ref.current) {
            ref.current.setValue('name', 'test-value');
          }
        }, []);
        
        return <CredentialForm ref={ref} schema={mockSchema} />;
      };
      
      // Mock the setValue function
      mockUseForm.setValue = setValueMock;
      
      render(<TestComponentWithRef />);
      expect(setValueMock).toHaveBeenCalledWith('name', 'test-value');
    });
  });

  describe('useEffect Logic - Advanced', () => {
    it('handles credential with settings when type changes', () => {
      const credentialWithSettings = {
        ...mockCredential,
        settings: { test: 'value' }
      };
      
      const { rerender } = render(
        <TestComponent 
          credential={credentialWithSettings}
          schema={{
            ...mockSchema,
            credentials: {
              'test-scheme': {
                ...mockSchema.credentials['test-scheme'],
                fields: [{ name: 'test', type: 'string' }]
              }
            }
          }}
        />
      );
      
      // Change the type to trigger the useEffect
      const typeSelector = screen.getByTestId('type-selector');
      act(() => {
        fireEvent.change(typeSelector, { target: { value: 'test-scheme' } });
      });
      
      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
    });

    it('handles credential without settings when type changes', () => {
      const credentialWithoutSettings = {
        ...mockCredential,
        settings: undefined
      };
      
      render(
        <TestComponent 
          credential={credentialWithoutSettings}
          schema={{
            ...mockSchema,
            credentials: {
              'test-scheme': {
                ...mockSchema.credentials['test-scheme'],
                fields: [{ name: 'test', type: 'string' }]
              }
            }
          }}
        />
      );
      
      // Change the type to trigger the useEffect
      const typeSelector = screen.getByTestId('type-selector');
      act(() => {
        fireEvent.change(typeSelector, { target: { value: 'test-scheme' } });
      });
      
      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
    });

    it('handles clearSettings when type is empty', () => {
      
      render(
        <TestComponent 
          credential={mockCredential}
          schema={mockSchema}
        />
      );
      
      // Change the type to empty to trigger clearSettings
      const typeSelector = screen.getByTestId('type-selector');
      act(() => {
        fireEvent.change(typeSelector, { target: { value: '' } });
      });
      
      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
    });
  });

  describe('Component Styling', () => {
    it('applies correct CSS classes and styles', () => {
      render(<TestComponent />);
      
      const form = screen.getByTestId('credential-form');
      expect(form).toBeInTheDocument();
      
      // Check that the form has the proper structure
      expect(form.tagName).toBe('FORM');
    });

    it('renders with proper form structure and styling', () => {
      render(<TestComponent credential={mockCredential} />);
      
      const form = screen.getByTestId('credential-form');
      expect(form).toBeInTheDocument();
      
      // Check that the form has the proper structure
      expect(form.tagName).toBe('FORM');
    });
  });

  describe('Style Coverage - Advanced', () => {
    it('renders with all style classes applied', () => {
      render(<TestComponent credential={mockCredential} />);
      
      const form = screen.getByTestId('credential-form');
      expect(form).toBeInTheDocument();
      
      // Test that the component renders without errors when styles are applied
      expect(form).toHaveAttribute('data-testid', 'credential-form');
    });

    it('handles style variations correctly', () => {
      const { rerender } = render(<TestComponent />);
      
      // Test different prop combinations that might affect styling
      rerender(<TestComponent credential={mockCredential} />);
      rerender(<TestComponent credential={undefined} />);
      rerender(<TestComponent className="custom-class" />);
      
      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
    });
  });

  describe('Advanced useEffect Coverage', () => {
    it('handles complex credential changes', () => {
      const { rerender } = render(<TestComponent />);
      
      // Test various credential change scenarios
      rerender(<TestComponent credential={mockCredential} />);
      rerender(<TestComponent credential={{ ...mockCredential, settings: { new: 'value' } }} />);
      rerender(<TestComponent credential={undefined} />);
      
      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
    });

    it('handles rapid credential changes', () => {
      const { rerender } = render(<TestComponent />);
      
      // Rapid changes to test useEffect dependencies
      for (let i = 0; i < 5; i++) {
        rerender(<TestComponent 
          credential={{ ...mockCredential, name: `Name ${i}`, settings: { test: `value${i}` } }}
        />);
      }
      
      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
    });
  });
});
