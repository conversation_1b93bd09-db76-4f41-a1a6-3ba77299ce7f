import { Box, Group, Stack, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useForm } from 'react-hook-form';
import { getDefaultValues } from '../../../utils/schema';
import AlertStatus from '../../AlertStatus';
import { DecaButton } from '../../DecaButton';
import type { SchemaField } from '../../FormChema/type';
import { CredentialTypeSelector } from '../CredentialTypeSelector';
import { DynamicSettingsForm } from '../DynamicSettingsForm';
import type { ICredentialPayload } from '../type';
import type { CardStatus } from '../type';

export interface CredentialFormProps {
  schema: SchemaField;
  credential?: ICredentialPayload;
  className?: string;
  onSubmit?: (
    values: ICredentialPayload,
    setStatus?: (status: CardStatus) => void
  ) => Promise<void>;
  onCancel?: () => void;
  onTest?: (
    credential: ICredentialPayload,
    setStatus?: (status: CardStatus) => void
  ) => Promise<void>;
  onDelete?: (credential: ICredentialPayload) => Promise<void>;
}

const useStyles = createStyles(() => ({
  boxForm: {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    overflow: 'hidden',
  },
  form: {
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    overflow: 'hidden',
  },
  stack: {
    overflow: 'auto',
  },
}));

export interface CredentialFormRef {
  focus: () => void;
  setValue: (name: string, value: any) => void;
}

export const CredentialForm = forwardRef(
  (
    { schema, credential, onSubmit, onCancel, onTest, onDelete, className }: CredentialFormProps,
    ref
  ) => {
    const { classes, cx } = useStyles();
    const { t } = useTranslate(['credential', 'common']);
    const [status, setStatus] = useState<CardStatus | undefined>();
    const dynamicSettingsFormRef = useRef<any>(null);
    const { credentials, defaultCredential } = useMemo(() => {
      const settingsCredential = schema?.settings?.credential;
      const schemaCredentials = schema?.credentials ?? {};
      const allowedCredentials: string[] = settingsCredential?.credentialTypes ?? [];
      const filteredCredentials = Object.fromEntries(
        Object.entries(schemaCredentials as Record<string, SchemaField>).filter(([key]) =>
          allowedCredentials.includes(key)
        )
      );

      const defaultCredential = settingsCredential?.default ?? Object.keys(filteredCredentials)[0];
      return {
        defaultCredential,
        credentials: filteredCredentials,
      };
    }, [schema]);
    const { control, handleSubmit, watch, setValue } = useForm<ICredentialPayload>({
      defaultValues: {
        provider: schema?.provider ?? '',
        name: schema?.displayName ?? '',
        description: schema?.description ?? '',
        authenticationScheme: defaultCredential ?? '',
        settings: undefined,
      },
      mode: 'onChange',
    });

    useImperativeHandle(ref, () => ({
      focus: () => {
        dynamicSettingsFormRef.current?.focus();
      },
      setValue: (name: keyof ICredentialPayload, value: any) => {
        setValue(name, value);
      },
    }));

    useEffect(() => {
      if (credential) {
        setValue('id', credential.id);
        setValue('authenticationScheme', credential.authenticationScheme);
        setValue('name', credential.name);
        setValue('description', credential.description);
        setValue('provider', credential.provider);
        if (credential.settings) {
          setValue('settings', credential.settings);
        } else {
          handleTypeChange(credential.authenticationScheme ?? '');
        }
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [credential]);

    const type = watch('authenticationScheme') ?? '';

    const clearSettings = useCallback(() => {
      setValue('settings', undefined);
    }, [setValue]);

    const handleTypeChange = useCallback(
      (value: string) => {
        const currentCredentialConfig = credentials[value];
        if (!value || !currentCredentialConfig) {
          clearSettings();
          return;
        }

        let newSettings: Record<string, any> = {};
        if (credential?.authenticationScheme === value && credential?.settings) {
          newSettings = credential.settings;
        } else {
          newSettings = getDefaultValues(currentCredentialConfig) ?? {};
        }
        setValue('settings', newSettings);
        dynamicSettingsFormRef.current?.focus();
      },
      [
        clearSettings,
        credential?.settings,
        credential?.authenticationScheme,
        credentials,
        setValue,
        dynamicSettingsFormRef,
      ]
    );

    const handleSubmitForm = handleSubmit((values: ICredentialPayload) => {
      onSubmit?.(values, setStatus);
    });

    const handleTest = handleSubmit((values: ICredentialPayload) => {
      onTest?.(values, setStatus);
    });

    return (
      <Box className={cx(classes.boxForm, className)}>
        <form
          onSubmit={handleSubmit(async (values) => {
            if (onSubmit) {
              await onSubmit(values, setStatus);
            }
          })}
          className={classes.form}
          data-testid='credential-form'
        >
          {Object.keys(credentials).length > 0 && (
            <>
              <Text fw={600}>{t('form.type')}</Text>
              <CredentialTypeSelector
                control={control}
                credentialTypes={credentials}
                onChange={handleTypeChange}
              />
              <Stack className={classes.stack}>
                <Box>
                  <DynamicSettingsForm
                    control={control}
                    selectedCredential={credentials[type]}
                    watch={watch}
                    ref={dynamicSettingsFormRef}
                  />
                </Box>
              </Stack>
            </>
          )}
          <Box mt={16}>
            {status && (
              <AlertStatus
                status={status.status}
                message={status.message}
                variant='box'
                onClose={() => setStatus(undefined)}
              />
            )}
            <Group justify='space-between' gap='md'>
              <Group>
                {credential?.id && (
                  <DecaButton
                    variant='negative'
                    onClick={() => onDelete?.(credential)}
                    data-testid='delete-button'
                  >
                    {t('button.remove', { ns: 'common' })}
                  </DecaButton>
                )}
                <DecaButton
                  onClick={handleTest}
                  data-testid='test-button'
                  variant='secondary'
                  size='md'
                >
                  {t('button.test', { ns: 'common' })}
                </DecaButton>
              </Group>
              <Group>
                {onCancel && (
                  <DecaButton variant='neutral' onClick={onCancel} data-testid='cancel-button'>
                    {t('button.cancel', { ns: 'common' })}
                  </DecaButton>
                )}
                <DecaButton
                  variant='primary'
                  type='submit'
                  onClick={handleSubmitForm}
                  data-testid='submit-button'
                >
                  {t('button.save', { ns: 'common' })}
                </DecaButton>
              </Group>
            </Group>
          </Box>
        </form>
      </Box>
    );
  }
);

CredentialForm.displayName = 'CredentialForm';
