import {
  ActionIcon,
  Box,
  Flex,
  Group,
  Loader,
  Modal,
  Text,
  TextInput,
  Textarea,
  rem,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCheck, IconPencil } from '@tabler/icons-react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { getDefaultValues } from '../../../utils/schema';
import ErrorBoundary from '../../ErrorBoundary';
import type { SchemaField } from '../../FormChema/type';
import { withTolgee } from '../../hoc/withTolgee';
import { CredentialForm, type CredentialFormRef } from '../CredentialForm';
import type { CardStatus, ICredential, ICredentialPayload } from '../type';
export interface CredentialModalProps {
  opened: boolean;
  schema: SchemaField;
  credential?: ICredential;
  loading?: boolean;
  zIndex?: number;
  onSubmit: (values: ICredentialPayload, setStatus?: (status: CardStatus) => void) => Promise<void>;
  onClose: () => void;
  onTest?: (
    credential: ICredentialPayload,
    setStatus?: (status: CardStatus) => void
  ) => Promise<void>;
  onDelete?: (credential: ICredentialPayload) => Promise<void>;
}

const useStyles = createStyles(() => ({
  modal: {
    section: {
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
    },
    header: {
      h2: {
        flex: 1,
      },
    },
    '.mantine-Modal-body': {
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
      overflow: 'hidden',
    },
  },
  icon: {
    marginRight: rem(8),
  },
  editButton: {
    marginLeft: rem(8),
  },
  nameInput: {
    width: '30%',
  },
  descriptionInput: {
    width: '70%',
    textarea: {
      border: 'none',
      padding: 0,
    },
  },
  credentialForm: {
    padding: 0,
    maxWidth: '100%',
  },
}));

export const CredentialModal = ({
  opened,
  schema,
  credential,
  loading,
  zIndex,
  onClose,
  onSubmit,
  onTest,
  onDelete,
}: CredentialModalProps) => {
  const { classes } = useStyles();
  const [isEditing, setIsEditing] = useState(false);
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const credentialFormRef = useRef<CredentialFormRef>(null);

  const initCredential = useMemo(() => {
    if (!credential) {
      const defaultCredential =
        schema?.settings?.credential?.default ?? Object.keys(schema?.credentials ?? {})[0] ?? '';
      let defaultSettings: any = undefined;
      if (schema?.credentials?.[defaultCredential]) {
        defaultSettings = getDefaultValues(schema?.credentials?.[defaultCredential]);
      }
     
      return {
        provider: schema?.provider ?? schema?.name ?? '',
        name: schema?.displayName ?? '',
        description: schema?.description ?? '',
        authenticationScheme: defaultCredential,
        settings: defaultSettings,
      };
    }

    return credential;
  }, [credential, schema]);

  useEffect(() => {
    setName(credential?.name ?? schema?.displayName ?? '');
    credentialFormRef.current?.setValue('name', credential?.name ?? schema?.displayName ?? '');
    setDescription(credential?.description ?? schema?.description ?? '');
    credentialFormRef.current?.setValue('description', credential?.description ?? schema?.description ?? '');
  }, [credential, schema]);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    credentialFormRef.current?.setValue('name', value);
    setName(value);
  };

  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    credentialFormRef.current?.setValue('description', e.target.value);
    setDescription(e.target.value);
  };

  const handleUpdateName = useCallback(() => {
    let newName = name;
    if (name === '') {
      newName = credential?.name ?? schema?.displayName ?? '';
    }
    credentialFormRef.current?.setValue('name', newName);
    setName(newName);
  }, [name, credential, schema]);

  const handleBlur = () => {
    let newName = name;
    if (name === '') {
      newName = credential?.name ?? schema?.displayName ?? '';
      credentialFormRef.current?.setValue('name', newName);
      setName(newName);
    }
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleUpdateName();
      setIsEditing(false);
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      className={classes.modal}
      data-testid='credential-modal'
      closeButtonProps={{
        'aria-label': 'Close',
      }}
      zIndex={zIndex}
      title={
        <Flex align='center'>
          <Box flex={1}>
            {isEditing ? (
              <Group gap='xs'>
                <TextInput
                  value={name}
                  className={classes.nameInput}
                  onChange={handleNameChange}
                  onBlur={handleBlur}
                  onKeyDown={handleKeyDown}
                  data-testid='title-input'
                  size='xs'
                />
                <ActionIcon
                  aria-label='edit-prompt-name'
                  c={'decaGrey.9'}
                  variant='transparent'
                  onClick={handleBlur}
                >
                  <IconCheck size={14} />
                </ActionIcon>
              </Group>
            ) : (
              <Group align='center' gap='xs'>
                <Text fw={600}>{name}</Text>
                <ActionIcon
                  aria-label='edit-prompt-name'
                  c={'decaGrey.9'}
                  variant='transparent'
                  onClick={() => setIsEditing(!isEditing)}
                >
                  <IconPencil size={14} />
                </ActionIcon>
              </Group>
            )}
            <Textarea
              size='xs'
              c='dimmed'
              value={description}
              className={classes.descriptionInput}
              autosize
              onChange={handleDescriptionChange}
              data-testid='description-input'
            />
          </Box>
        </Flex>
      }
      size='720px'
    >
      <ErrorBoundary fallback={<div>...</div>}>
        {loading ? (
          <Loader />
        ) : (
          <CredentialForm
            schema={schema}
            credential={initCredential}
            onSubmit={onSubmit}
            onCancel={onClose}
            onTest={onTest}
            onDelete={onDelete}
            ref={credentialFormRef}
          />
        )}
      </ErrorBoundary>
    </Modal>
  );
};

export const CredentialModalTolgee = withTolgee(CredentialModal);
