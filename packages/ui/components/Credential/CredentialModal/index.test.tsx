import { act, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';
import { vi } from 'vitest';

import type { SchemaField } from '../../FormChema/type';
import type { ICredential } from '../type';
import { CredentialModal } from './index';
import type { CredentialModalProps } from './index';

// Mock Mantine components
vi.mock('@mantine/core', () => ({
  ActionIcon: ({ children, onClick, ...props }: any) => (
    <button type="button" onClick={onClick} data-testid="action-icon" {...props}>
      {children}
    </button>
  ),
  Box: ({ children, className, ...props }: any) => (
    <div className={className} data-testid="mantine-box" {...props}>
      {children}
    </div>
  ),
  Flex: ({ children, className, ...props }: any) => (
    <div className={className} data-testid="mantine-flex" {...props}>
      {children}
    </div>
  ),
  Group: ({ children, className, ...props }: any) => (
    <div className={className} data-testid="mantine-group" {...props}>
      {children}
    </div>
  ),
  Loader: () => <div data-testid="loader">Loading...</div>,
  Modal: ({ children, opened, onClose, title, ...props }: any) => (
    opened ? (
      <div data-testid="modal" {...props}>
        <div data-testid="modal-header">
          {title}
        </div>
        <div data-testid="modal-body">
          {children}
        </div>
        <button type="button" onClick={onClose} data-testid="modal-close">Close</button>
      </div>
    ) : null
  ),
  Text: ({ children, className, ...props }: any) => (
    <div className={className} data-testid="mantine-text" {...props}>
      {children}
    </div>
  ),
  TextInput: ({ value, onChange, onBlur, onKeyDown, ...props }: any) => (
    <input
      value={value}
      onChange={onChange}
      onBlur={onBlur}
      onKeyDown={onKeyDown}
      data-testid="text-input"
      {...props}
    />
  ),
  Textarea: ({ value, onChange, ...props }: any) => (
    <textarea
      value={value}
      onChange={onChange}
      data-testid="textarea"
      {...props}
    />
  ),
  rem: (value: number) => `${value}px`,
}));

vi.mock('@mantine/emotion', () => ({
  createStyles: vi.fn(() => () => ({
    classes: {
      modal: 'modal',
      icon: 'icon',
      editButton: 'editButton',
      nameInput: 'nameInput',
      descriptionInput: 'descriptionInput',
      credentialForm: 'credentialForm',
    },
    cx: (...args) => args.filter(Boolean).join(' '),
  })),
}));

// Mock Tabler icons
vi.mock('@tabler/icons-react', () => ({
  IconCheck: () => <span data-testid="icon-check">✓</span>,
  IconPencil: () => <span data-testid="icon-pencil">✏️</span>,
}));

// Mock utility functions
vi.mock('../../../../utils/schema', () => ({
  getDefaultValues: vi.fn(() => ({ testField: 'defaultValue' })),
}));

// Mock ErrorBoundary
vi.mock('../../ErrorBoundary', () => ({
  default: ({ children, fallback }: any) => (
    <div data-testid="error-boundary">
      {children}
    </div>
  ),
}));

// Mock withTolgee HOC
vi.mock('../../hoc/withTolgee', () => ({
  withTolgee: (Component: any) => Component,
}));

// Mock CredentialForm
const mockCredentialFormRef = {
  current: {
    focus: vi.fn(),
    setValue: vi.fn(),
  },
};

vi.mock('../CredentialForm', () => ({
  CredentialForm: React.forwardRef(({ schema, credential, onSubmit, onCancel, onTest, onDelete, ...props }: any, ref: any) => {
    React.useImperativeHandle(ref, () => mockCredentialFormRef.current);
    return (
      <div data-testid="credential-form">
        <div data-testid="credential-form-schema">{schema?.displayName}</div>
        <div data-testid="credential-form-credential">{credential?.name}</div>
        <button type="button" onClick={() => onSubmit?.({}, vi.fn())} data-testid="credential-form-submit">Submit</button>
        <button type="button" onClick={() => onCancel?.()} data-testid="credential-form-cancel">Cancel</button>
        <button type="button" onClick={() => onTest?.({}, vi.fn())} data-testid="credential-form-test">Test</button>
        <button type="button" onClick={() => onDelete?.({})} data-testid="credential-form-delete">Delete</button>
      </div>
    );
  }),
}));


// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  return <div data-testid="test-wrapper">{children}</div>;
};

// Test component
const TestComponent = (props: Partial<CredentialModalProps>) => {
  const defaultSchema: SchemaField = {
    displayName: 'Test Credential',
    description: 'Test description',
    required: true,
    allowInput: false,
    reference: true,
    provider: 'test-provider',
    name: 'test-credential',
    credentials: {
      'test-scheme': {
        displayName: 'Test Scheme',
        description: 'Test scheme description',
        required: true,
        allowInput: false,
        reference: true,
        fields: {
          testField: {
            displayName: 'Test Field',
            description: 'Test field description',
            required: true,
            allowInput: true,
            reference: false,
            type: 'string',
          },
        },
      },
    },
    settings: {
      credential: {
        credentialTypes: ['test-scheme'],
        default: 'test-scheme',
      },
    },
    ...props.schema,
  };

  const defaultProps: CredentialModalProps = {
    opened: true,
    schema: defaultSchema,
    credential: props.credential,
    loading: false,
    zIndex: 1000,
    onSubmit: vi.fn(),
    onClose: vi.fn(),
    onTest: vi.fn(),
    onDelete: vi.fn(),
    ...props,
  };

  return (
    <TestWrapper>
      <CredentialModal {...defaultProps} />
    </TestWrapper>
  );
};

describe('CredentialModal', () => {
  const mockCredential: ICredential = {
    id: 'test-id',
    name: 'Test Credential',
    description: 'Test description',
    provider: 'test-provider',
    authenticationScheme: 'test-scheme',
    settings: { testField: 'testValue' },
    metadata: {},
    workspaceId: 'workspace-1',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  };

  const mockSchema = {
    displayName: 'Test Schema',
    credentials: {
      'test-scheme': {
        displayName: 'Test Scheme',
        fields: [
          { name: 'test', type: 'string', required: true },
          { name: 'optional', type: 'string', required: false },
        ] as SchemaField[],
      },
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders when opened', () => {
      render(<TestComponent opened={true} />);

      expect(screen.getByTestId('credential-modal')).toBeInTheDocument();
      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
    });

    it('does not render when closed', () => {
      render(<TestComponent opened={false} />);

      expect(screen.queryByTestId('modal')).not.toBeInTheDocument();
    });

    it('renders with custom zIndex', () => {
      render(<TestComponent zIndex={2000} />);

      expect(screen.getByTestId('credential-modal')).toBeInTheDocument();
    });
  });

  describe('Loading State', () => {
    it('shows loader when loading', () => {
      render(<TestComponent loading={true} />);

      expect(screen.getByTestId('loader')).toBeInTheDocument();
      expect(screen.queryByTestId('credential-form')).not.toBeInTheDocument();
    });

    it('shows form when not loading', () => {
      render(<TestComponent loading={false} />);

      expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
    });
  });

  describe('Title and Description Editing', () => {
    it('renders title in display mode initially', () => {
      render(<TestComponent credential={mockCredential} />);

      expect(screen.getByTestId('mantine-text')).toHaveTextContent('Test Credential');
      expect(screen.getByTestId('icon-pencil')).toBeInTheDocument();
    });

    it('switches to edit mode when pencil icon is clicked', async () => {
      const user = userEvent.setup();

      render(<TestComponent credential={mockCredential} />);

      const editButton = screen.getByTestId('action-icon');
      await act(async () => {
        await user.click(editButton);
      });

      expect(screen.getByTestId('title-input')).toBeInTheDocument();
      expect(screen.getByTestId('icon-check')).toBeInTheDocument();
    });

    it('handles name input change', async () => {
      const user = userEvent.setup();

      render(<TestComponent credential={mockCredential} />);

      // Switch to edit mode
      const editButton = screen.getByTestId('action-icon');
      await act(async () => {
        await user.click(editButton);
      });

      const nameInput = screen.getByTestId('title-input');
      await act(async () => {
        await user.type(nameInput, 'New Name');
      });

      expect(nameInput).toHaveValue('Test CredentialNew Name');
    });

    it('handles description input change', async () => {
      const user = userEvent.setup();

      render(<TestComponent credential={mockCredential} />);

      const descriptionInput = screen.getByTestId('description-input');
      await act(async () => {
        await user.type(descriptionInput, 'New Description');
      });

      expect(descriptionInput).toHaveValue('Test descriptionNew Description');
    });

    it('saves changes on Enter key', async () => {
      const user = userEvent.setup();

      render(<TestComponent credential={mockCredential} />);

      // Switch to edit mode
      const editButton = screen.getByTestId('action-icon');
      await act(async () => {
        await user.click(editButton);
      });

      const nameInput = screen.getByTestId('title-input');
      await act(async () => {
        await user.type(nameInput, 'New Name');
        await user.keyboard('{Enter}');
      });

      expect(screen.getByText('Test CredentialNew Name')).toBeInTheDocument();
    });

    it('saves changes on check icon click', async () => {
      const user = userEvent.setup();

      render(<TestComponent credential={mockCredential} />);

      // Switch to edit mode
      const editButton = screen.getByTestId('action-icon');
      await act(async () => {
        await user.click(editButton);
      });

      const nameInput = screen.getByTestId('title-input');
      await act(async () => {
        await user.type(nameInput, 'New Name');
      });

      const checkButton = screen.getByTestId('action-icon');
      await act(async () => {
        await user.click(checkButton);
      });

      expect(screen.getByText('Test CredentialNew Name')).toBeInTheDocument();
    });

    it('reverts to original name on blur when empty', async () => {
      const user = userEvent.setup();

      render(<TestComponent credential={mockCredential} />);

      // Switch to edit mode
      const editButton = screen.getByTestId('action-icon');
      await act(async () => {
        await user.click(editButton);
      });

      const nameInput = screen.getByTestId('title-input');
      await act(async () => {
        await user.clear(nameInput);
        nameInput.blur();
      });

      expect(screen.getByTestId('mantine-text')).toHaveTextContent('Test Credential');
    });
  });

  describe('Credential Initialization', () => {
    it('initializes with credential data', () => {
      render(<TestComponent credential={mockCredential} />);

      expect(screen.getByTestId('credential-form-credential')).toHaveTextContent('Test Credential');
    });

    it('initializes with schema data when no credential', () => {
      render(<TestComponent credential={undefined} />);

      expect(screen.getByTestId('credential-form-schema')).toHaveTextContent('Test Credential');
    });

    it('updates form values when credential changes', () => {
      const { rerender } = render(<TestComponent credential={mockCredential} />);

      const newCredential = { ...mockCredential, name: 'New Name' };
      rerender(<TestComponent credential={newCredential} />);

      expect(mockCredentialFormRef.current.setValue).toHaveBeenCalledWith('name', 'New Name');
    });

    it('updates form values when schema changes', () => {
      const { rerender } = render(<TestComponent />);

      const newSchema = { displayName: 'New Schema Name' };
      rerender(<TestComponent schema={newSchema} />);

      expect(mockCredentialFormRef.current.setValue).toHaveBeenCalledWith('name', 'New Schema Name');
    });
  });

  describe('Form Integration', () => {
    it('passes correct props to CredentialForm', () => {
      render(<TestComponent credential={mockCredential} />);

      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
      expect(screen.getByTestId('credential-form-credential')).toHaveTextContent('Test Credential');
    });

    it('handles form submission', async () => {
      const onSubmit = vi.fn();
      const user = userEvent.setup();

      render(<TestComponent onSubmit={onSubmit} />);

      const submitButton = screen.getByTestId('credential-form-submit');
      await act(async () => {
        await user.click(submitButton);
      });

      expect(onSubmit).toHaveBeenCalled();
    });

    it('handles form cancellation', async () => {
      const onClose = vi.fn();
      const user = userEvent.setup();

      render(<TestComponent onClose={onClose} />);

      const cancelButton = screen.getByTestId('credential-form-cancel');
      await act(async () => {
        await user.click(cancelButton);
      });

      expect(onClose).toHaveBeenCalled();
    });

    it('handles form testing', async () => {
      const onTest = vi.fn();
      const user = userEvent.setup();

      render(<TestComponent onTest={onTest} />);

      const testButton = screen.getByTestId('credential-form-test');
      await act(async () => {
        await user.click(testButton);
      });

      expect(onTest).toHaveBeenCalled();
    });

    it('handles form deletion', async () => {
      const onDelete = vi.fn();
      const user = userEvent.setup();

      render(<TestComponent onDelete={onDelete} />);

      const deleteButton = screen.getByTestId('credential-form-delete');
      await act(async () => {
        await user.click(deleteButton);
      });

      expect(onDelete).toHaveBeenCalled();
    });
  });

  describe('Modal Controls', () => {
    it('handles modal close', async () => {
      const onClose = vi.fn();
      const user = userEvent.setup();

      render(<TestComponent onClose={onClose} />);

      const closeButton = screen.getByTestId('modal-close');
      await act(async () => {
        await user.click(closeButton);
      });

      expect(onClose).toHaveBeenCalled();
    });

    it('has proper modal structure', () => {
      render(<TestComponent />);

      expect(screen.getByTestId('credential-modal')).toBeInTheDocument();
      expect(screen.getByTestId('modal-header')).toBeInTheDocument();
      expect(screen.getByTestId('modal-body')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles undefined credential gracefully', () => {
      render(<TestComponent credential={undefined} />);

      expect(screen.getByTestId('credential-modal')).toBeInTheDocument();
      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
    });

    it('handles empty schema gracefully', () => {
      render(<TestComponent schema={{} as any} />);

      expect(screen.getByTestId('credential-modal')).toBeInTheDocument();
    });

    it('handles missing credentials in schema', () => {
      const schemaWithoutCredentials = {
        displayName: 'Test',
        credentials: undefined,
        settings: undefined,
      };

      render(<TestComponent schema={schemaWithoutCredentials as any} />);

      expect(screen.getByTestId('credential-modal')).toBeInTheDocument();
    });

    it('handles rapid state changes', async () => {
      const user = userEvent.setup();

      render(<TestComponent credential={mockCredential} />);

      // Rapidly switch between edit modes
      for (let i = 0; i < 3; i++) {
        const editButton = screen.getByTestId('action-icon');
        await act(async () => {
          await user.click(editButton);
        });
        await act(async () => {
          await user.click(editButton);
        });
      }

      expect(screen.getByTestId('credential-modal')).toBeInTheDocument();
    });
  });

  describe('State Management', () => {
    it('maintains editing state correctly', async () => {
      const user = userEvent.setup();

      render(<TestComponent credential={mockCredential} />);

      // Initially in display mode
      expect(screen.getByTestId('mantine-text')).toHaveTextContent('Test Credential');
      expect(screen.getByTestId('icon-pencil')).toBeInTheDocument();

      // Switch to edit mode
      const editButton = screen.getByTestId('action-icon');
      await act(async () => {
        await user.click(editButton);
      });

      expect(screen.getByTestId('title-input')).toBeInTheDocument();
      expect(screen.getByTestId('icon-check')).toBeInTheDocument();
    });

    it('updates name and description state', async () => {
      const user = userEvent.setup();

      render(<TestComponent credential={mockCredential} />);

      const descriptionInput = screen.getByTestId('description-input');
      await act(async () => {
        await user.type(descriptionInput, 'Updated Description');
      });

      expect(descriptionInput).toHaveValue('Test descriptionUpdated Description');
    });
  });

  describe('Accessibility', () => {
    it('has proper modal structure', () => {
      render(<TestComponent />);

      expect(screen.getByTestId('credential-modal')).toBeInTheDocument();
      expect(screen.getByTestId('modal-header')).toBeInTheDocument();
      expect(screen.getByTestId('modal-body')).toBeInTheDocument();
    });

    it('has proper input labels and attributes', () => {
      render(<TestComponent credential={mockCredential} />);

      // Switch to edit mode
      const editButton = screen.getByTestId('action-icon');
      act(() => {
        editButton.click();
      });

      const nameInput = screen.getByTestId('title-input');
      expect(nameInput).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('handles multiple re-renders efficiently', () => {
      const { rerender } = render(<TestComponent />);

      // Multiple re-renders should not cause issues
      for (let i = 0; i < 5; i++) {
        rerender(<TestComponent credential={{ ...mockCredential, name: `Name ${i}` }} />);
      }

      expect(screen.getByTestId('credential-modal')).toBeInTheDocument();
    });

    it('memoizes initCredential correctly', () => {
      const { rerender } = render(<TestComponent credential={mockCredential} />);

      // Rerender with same credential should not cause unnecessary recalculations
      rerender(<TestComponent credential={mockCredential} />);

      expect(screen.getByTestId('credential-modal')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles form errors gracefully', () => {
      render(<TestComponent />);

      expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
      expect(screen.getByTestId('credential-form')).toBeInTheDocument();
    });

    it('handles missing props gracefully', () => {
      const minimalProps = {
        opened: true,
        schema: {} as any,
        onSubmit: vi.fn(),
        onClose: vi.fn(),
      };

      render(<CredentialModal {...minimalProps} />);

      expect(screen.getByTestId('credential-modal')).toBeInTheDocument();
    });
  });

  describe('useEffect Logic - Advanced', () => {
    it('handles name initialization with credential name', () => {
      const credentialWithName = {
        ...mockCredential,
        name: 'Custom Credential Name'
      };
      
      render(<TestComponent credential={credentialWithName} />);
      
      expect(screen.getByTestId('mantine-text')).toHaveTextContent('Custom Credential Name');
    });

    it('handles name initialization with schema displayName when no credential', () => {
      const schemaWithDisplayName = {
        ...mockSchema,
        displayName: 'Schema Display Name'
      };
      
      render(<TestComponent schema={schemaWithDisplayName} />);
      
      expect(screen.getByTestId('mantine-text')).toHaveTextContent('Schema Display Name');
    });

    it('handles name initialization with empty string when no credential or schema name', () => {
      const emptySchema = {
        ...mockSchema,
        displayName: undefined
      };
      
      render(<TestComponent schema={emptySchema} />);
      
      expect(screen.getByTestId('mantine-text')).toHaveTextContent('');
    });

    it('updates name when credential changes', () => {
      const { rerender } = render(<TestComponent />);
      
      const newCredential = {
        ...mockCredential,
        name: 'Updated Credential Name'
      };
      
      rerender(<TestComponent credential={newCredential} />);
      
      expect(screen.getByTestId('mantine-text')).toHaveTextContent('Updated Credential Name');
    });

    it('updates name when schema changes', () => {
      const { rerender } = render(<TestComponent />);
      
      const newSchema = {
        ...mockSchema,
        displayName: 'Updated Schema Name'
      };
      
      rerender(<TestComponent schema={newSchema} />);
      
      expect(screen.getByTestId('mantine-text')).toHaveTextContent('Updated Schema Name');
    });
  });

  describe('Component Styling', () => {
    it('applies correct CSS classes and styles', () => {
      render(<TestComponent />);
      
      const modal = screen.getByTestId('credential-modal');
      expect(modal).toBeInTheDocument();
      
      // Check that the modal has the proper structure
      expect(modal.tagName).toBe('DIV');
    });

    it('renders with proper modal structure and styling', () => {
      render(<TestComponent credential={mockCredential} />);
      
      const modal = screen.getByTestId('credential-modal');
      expect(modal).toBeInTheDocument();
      
      // Check that the modal has the proper structure
      expect(modal.tagName).toBe('DIV');
    });

    it('applies custom zIndex when provided', () => {
      render(<TestComponent zIndex={999} />);
      
      const modal = screen.getByTestId('credential-modal');
      expect(modal).toHaveAttribute('zIndex', '999');
    });
  });

  describe('Advanced State Management', () => {
    it('handles complex state transitions', () => {
      const { rerender } = render(<TestComponent />);
      
      // Test multiple state changes
      rerender(<TestComponent credential={mockCredential} />);
      rerender(<TestComponent credential={undefined} />);
      rerender(<TestComponent credential={mockCredential} />);
      
      expect(screen.getByTestId('credential-modal')).toBeInTheDocument();
    });

    it('handles rapid prop changes without errors', () => {
      const { rerender } = render(<TestComponent />);
      
      // Rapid changes should not cause errors
      for (let i = 0; i < 10; i++) {
        rerender(<TestComponent 
          credential={{ ...mockCredential, name: `Name ${i}` }}
          schema={{ ...mockSchema, displayName: `Schema ${i}` }}
        />);
      }
      
      expect(screen.getByTestId('credential-modal')).toBeInTheDocument();
    });
  });

  describe('Edge Cases - Advanced', () => {
    it('handles undefined credential and schema gracefully', () => {
      render(<TestComponent credential={undefined} schema={undefined} />);
      
      expect(screen.getByTestId('credential-modal')).toBeInTheDocument();
    });

    it('handles empty credential object', () => {
      const emptyCredential = {} as any;
      
      render(<TestComponent credential={emptyCredential} />);
      
      expect(screen.getByTestId('credential-modal')).toBeInTheDocument();
    });

    it('handles malformed schema gracefully', () => {
      const malformedSchema = {
        credentials: null,
        displayName: null
      } as any;
      
      render(<TestComponent schema={malformedSchema} />);
      
      expect(screen.getByTestId('credential-modal')).toBeInTheDocument();
    });
  });

  describe('Style Coverage - Advanced', () => {
    it('renders with all style classes applied', () => {
      render(<TestComponent credential={mockCredential} />);
      
      const modal = screen.getByTestId('credential-modal');
      expect(modal).toBeInTheDocument();
      
      // Test that the component renders without errors when styles are applied
      expect(modal).toHaveAttribute('data-testid', 'credential-modal');
    });

    it('handles style variations correctly', () => {
      const { rerender } = render(<TestComponent />);
      
      // Test different prop combinations that might affect styling
      rerender(<TestComponent loading={true} />);
      rerender(<TestComponent loading={false} credential={mockCredential} />);
      rerender(<TestComponent zIndex={1000} />);
      
      expect(screen.getByTestId('credential-modal')).toBeInTheDocument();
    });
  });

  describe('useEffect Dependencies - Advanced', () => {
    it('handles name changes with complex dependencies', () => {
      const { rerender } = render(<TestComponent />);
      
      // Test various name change scenarios
      rerender(<TestComponent credential={{ ...mockCredential, name: 'New Name' }} />);
      rerender(<TestComponent credential={undefined} schema={{ ...mockSchema, displayName: 'Schema Name' }} />);
      rerender(<TestComponent credential={undefined} schema={undefined} />);
      
      expect(screen.getByTestId('credential-modal')).toBeInTheDocument();
    });

    it('handles rapid name changes', () => {
      const { rerender } = render(<TestComponent />);
      
      // Rapid changes to test useEffect dependencies
      for (let i = 0; i < 5; i++) {
        rerender(<TestComponent 
          credential={{ ...mockCredential, name: `Name ${i}` }}
          schema={{ ...mockSchema, displayName: `Schema ${i}` }}
        />);
      }
      
      expect(screen.getByTestId('credential-modal')).toBeInTheDocument();
    });
  });
});
