import type { CustomIconProps } from '../../Icons';
import { useCatalogStyles } from '../CatalogStyles';
import iconMapper from '../iconMapper';

interface CategoryIconProps extends CustomIconProps {
  name: string;
  size?: number;
  className?: string;
}

const CategoryIcon: React.FC<CategoryIconProps> = ({ name, size = 20, className, ...rest }) => {
  const { classes, cx } = useCatalogStyles();
  const icon = iconMapper[name as keyof typeof iconMapper] ?? iconMapper.default;
  const IconComponent = icon?.component;
  const withPrimaryColor = !!icon?.withPrimaryColor;
  const withStrokeColor = !!icon?.withStrokeColor;
  const addSize = icon?.addSize ?? 0;

  return (
    <IconComponent
      width={size + addSize}
      height={size + addSize}
      className={cx(
        {
          [classes.svgIconPrimary]: withPrimaryColor,
          [classes.svgIconStroke]: withStrokeColor,
        },
        className
      )}
      {...rest}
    />
  );
};

export default CategoryIcon;
