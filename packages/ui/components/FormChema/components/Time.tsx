import { TimeInput } from '@mantine/dates';
import { useEffect, useState } from 'react';
import { withReactHookForm } from '../../hoc/withReactHookForm';
import { Label } from './Label';

export interface SchemaTimeProps {
  schema: Record<string, any>;
  onChange?: (value: any) => void;
  value?: string;
}

export const SchemaTime: React.FC<SchemaTimeProps> = ({ schema, onChange, value: valueProp }) => {
  const [value, setValue] = useState(valueProp || schema.default || '');

  useEffect(() => {
    setValue(valueProp || schema.default || '');
  }, [valueProp, schema.default]);

  return (
    <TimeInput
      label={<Label schema={schema} />}
      description={schema.description}
      readOnly={schema?.readOnly}
      onChange={(e) => {
        const value = e.target.value;
        setValue(value);
        onChange?.(value);
      }}
      value={value}
    />
  );
};

export const FormTime = withReactHookForm(SchemaTime);
