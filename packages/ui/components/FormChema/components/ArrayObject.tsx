import { Box, Button, Group, Paper, Stack, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconPlus, IconTrash } from '@tabler/icons-react';
import type React from 'react';
import { useEffect, useMemo, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { getDefaultValue } from '../../../utils/schema';
import { withContainer } from '../../hoc/withContainer';
import { withReactHookForm } from '../../hoc/withReactHookForm';
import { SCHEMA_CONFIGS } from '../config';
import type { CommonDataEngineProps, SchemaField } from '../type';

interface SchemaArrayObjectProps extends CommonDataEngineProps {
  schema: SchemaField;
  value?: any[];
  onChange?: (value: any[]) => void;
  isDefault?: boolean;
  withContainer?: boolean;
  error?: string;
}

const useStyles = createStyles((theme) => ({
  paper: {
    position: 'relative',
    padding: theme.spacing.xs,
    borderRadius: theme.radius.md,
  },
  removeButton: {
    cursor: 'pointer',
    borderRadius: theme.radius.md,
    border: `1px solid ${theme.colors.decaLight[5]}`,
    padding: '4px 8px',
    '&:hover': {
      backgroundColor: theme.colors.decaLight[5],
    },
  },
}));

export const SchemaArrayObject: React.FC<SchemaArrayObjectProps> = ({
  schema,
  value,
  onChange,
  error,
  ...commonProps
}) => {
  const [localValue, setLocalValue] = useState<any[]>(value || []);
  const { classes } = useStyles();

  const items = schema.items?.properties || {};

  const defaultValue = useMemo(() => getDefaultValue(schema), [schema]);
  useEffect(() => {
    setLocalValue(value || defaultValue);
  }, [value, defaultValue]);

  const handleValueChange = (path: string, newValue: any) => {
    const pathParts = path.split('.');
    const updatedValue = [...localValue];
    let current = updatedValue;
    for (let i = 0; i < pathParts.length; i++) {
      const part = pathParts[i];
      const index = Number.parseInt(part, 10);
      if (!Number.isNaN(index)) {
        current = current[index];
      } else {
        current[part] = newValue;
      }
    }

    setLocalValue(updatedValue);
    onChange?.(updatedValue);
  };

  const handleAddField = () => {
    const newId = uuidv4();
    const updatedValue = { ...defaultValue[0], id: newId };
    const newLocalValue = [...localValue, updatedValue];
    setLocalValue(newLocalValue);
    onChange?.(newLocalValue);
  };

  const handleRemoveField = (id: string) => {
    const updatedValue = localValue.filter((item) => item.id !== id);
    setLocalValue(updatedValue);
    onChange?.(updatedValue);
  };

  const getValue = (prefix: string, value: any) => {
    const pathParts = prefix.split('.');
    let current = value;
    for (let i = 0; i < pathParts.length; i++) {
      const part = pathParts[i];
      if (!Number.isNaN(Number.parseInt(part, 10))) {
        current = current[Number.parseInt(part, 10)];
      } else {
        current = current[part];
      }
    }
    return current;
  };

  const evaluateVisibleIf = (
    visibleIf: { [key: string]: any },
    localValue: any,
    prefix: string
  ) => {
    let visible = false;
    for (const [key, value] of Object.entries(visibleIf)) {
      const paths = prefix.split('.');
      paths[paths.length - 1] = key;
      const path = paths.join('.');
      const fieldValue = getValue(path, localValue);
      if (value?.includes(fieldValue)) {
        visible = true;
        break;
      }
    }
    return visible;
  };

  const renderField = (prefix: string, fieldSchema: any) => {
    const path = `${prefix}.${fieldSchema.name}`;
    const tabConfig = Object.values(SCHEMA_CONFIGS).find((config) =>
      config?.types?.includes(fieldSchema.type)
    );

    if (fieldSchema.visibleIf) {
      const visible = evaluateVisibleIf(fieldSchema.visibleIf, localValue, path);
      if (!visible) {
        return null;
      }
    }

    const value = getValue(path, localValue);

    if (fieldSchema.type === 'arrayObject') {
      return (
        <SchemaArrayObject
          key={path}
          schema={{
            ...fieldSchema,
            name: path,
          }}
          value={value}
          onChange={(newValue) => handleValueChange(path, newValue)}
          withContainer={false}
          {...commonProps}
        />
      );
    }

    if (!tabConfig) {
      return <Text>We don&apos;t support this type yet</Text>;
    }

    return (
      <tabConfig.Component
        key={path}
        schema={{
          ...fieldSchema,
          name: path,
        }}
        value={value}
        onChange={(newValue) => {
          handleValueChange(path, newValue);
        }}
        withContainer={false}
        {...commonProps}
      />
    );
  };

  return (
    <Box>
      <Stack gap='md'>
        <Stack gap='md'>
          {localValue.map((item, index) => {
            return (
              <Paper key={item.id} withBorder className={classes.paper}>
                <Group justify='space-between' align='center' gap='xs' mb='sm'>
                  <Group justify='space-between' align='center'>
                    <Text size='lg' fw={500}>
                      {schema.displayName}
                    </Text>
                  </Group>
                  <Group
                    gap='xs'
                    className={classes.removeButton}
                    onClick={() => handleRemoveField(item.id)}
                  >
                    <IconTrash size={14} />
                    <Text size='xs' c='decaGrey.9' fw={500}>
                      {schema.removeText || 'Remove'}
                    </Text>
                  </Group>
                </Group>
                <Stack gap='md'>
                  {Object.keys(items).map((key) => {
                    return renderField(`${index}`, items[key] || { type: 'text' });
                  })}
                </Stack>
              </Paper>
            );
          })}
        </Stack>
        <Group>
          <Button
            leftSection={<IconPlus size={14} />}
            size='xs'
            variant='light'
            onClick={handleAddField}
          >
            {schema.addText || `Add ${schema.displayName}`}
          </Button>
        </Group>
        {error && <Text c='red'>{error}</Text>}
      </Stack>
    </Box>
  );
};

export const SchemaArrayObjectWithContainer = withContainer(SchemaArrayObject);
export const FormArrayObject = withReactHookForm(SchemaArrayObject);
export const FormArrayObjectWithContainer = withReactHookForm(SchemaArrayObjectWithContainer);
