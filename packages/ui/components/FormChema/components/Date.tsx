import { DatePickerInput } from '@mantine/dates';
import '@mantine/dates/styles.css';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { JA_DATE_FORMAT } from '../../../constants/dateTime';
import { withContainer } from '../../hoc/withContainer';
import { withReactHookForm } from '../../hoc/withReactHookForm';
import { Label } from './Label';

interface SchemaDateProps {
  schema: Record<string, any>;
  onChange?: (value: any) => void;
  value?: string;
}

export const SchemaDate: React.FC<SchemaDateProps> = ({ schema, onChange, value: valueProp }) => {
  const [value, setValue] = useState(valueProp || schema.default || '');
  const format = schema.format || JA_DATE_FORMAT;
  useEffect(() => {
    setValue(valueProp || schema.default || '');
  }, [valueProp, schema.default]);

  const defaultValue = dayjs(value).isValid() ? dayjs(value).toDate() : undefined;

  return (
    <DatePickerInput
      label={<Label schema={schema} />}
      readOnly={schema?.readOnly}
      value={defaultValue}
      onChange={(value) => {
        const valueFormated = dayjs(value).format(format);
        setValue(valueFormated);
        onChange?.(valueFormated);
      }}
      valueFormat={format}
      minDate={schema.onlyFutureDates ? dayjs().toDate() : undefined}
    />
  );
};

export const SchemaDateWithContainer = withContainer(SchemaDate);
export const FormDate = withReactHookForm(SchemaDate);
