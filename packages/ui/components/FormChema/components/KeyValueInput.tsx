import { ActionIcon, Box, Group, Paper, Stack, Text, TextInput, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCheck, IconPencil, IconPlus, IconTrash, IconX } from '@tabler/icons-react';
import type React from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { getDefaultValue } from '../../../utils/schema';
import { DecaButton } from '../../DecaButton';
import { withContainer } from '../../hoc/withContainer';
import { withReactHookForm } from '../../hoc/withReactHookForm';
import type { SchemaField as SchemaFieldType } from '../type';
import { generateKeyValueId, getUniqueKeyName, initializeOrderedFields } from '../utils/field';
import type { ComboboxNode } from './ComboboxSelectDataPoint';
import { FieldHeader } from './FieldUtils';
import { SchemaFieldWrapper } from './SchemaFieldWrapper';

const useStyles = createStyles((theme) => ({
  paper: {
    position: 'relative',
    padding: theme.spacing.xs,
    borderRadius: theme.radius.md,
    boxShadow: 'none',
  },
  container: {
    '& label': {
      display: 'none',
    },
  },
}));

interface KeyHeaderProps {
  keyName: string;
  isEditing: boolean;
  value: string;
  onOpenEdit: (keyName: string) => void;
  onValueChange: (value: string) => void;
  onSave: () => void;
  onCancel: () => void;
}

export const KeyHeader = ({
  keyName,
  isEditing,
  value,
  onOpenEdit,
  onValueChange,
  onSave,
  onCancel,
}: KeyHeaderProps) => {
  if (isEditing) {
    return (
      <>
        <TextInput
          autoFocus
          value={value}
          onChange={(e) => onValueChange(e.target.value)}
          onKeyDown={(e) => {
            switch (e.key) {
              case 'Escape': {
                e.preventDefault();
                onCancel();
                break;
              }
              case 'Enter': {
                if (value) {
                  onSave();
                }
                break;
              }
            }
          }}
          size='xs'
          w='100%'
          maw='50%'
        />
        <ActionIcon size='xs' variant='subtle' c='decaGrey.6' onClick={onSave} disabled={!value}>
          <IconCheck size={14} />
        </ActionIcon>
        <ActionIcon size='xs' variant='subtle' c='decaGrey.6' onClick={onCancel}>
          <IconX size={14} />
        </ActionIcon>
      </>
    );
  }

  return (
    <>
      <Text truncate maw='60%' fz='sm' fw={500}>
        {keyName}
      </Text>
      <ActionIcon size='xs' variant='subtle' c='decaGrey.6' onClick={() => onOpenEdit(keyName)}>
        <IconPencil size={14} />
      </ActionIcon>
    </>
  );
};

interface SchemaKeyValueInputProps {
  schema: SchemaFieldType;
  value?: Record<string, any>;
  onChange?: (value: Record<string, any>) => void;
  isDefault?: boolean;
  withContainer?: boolean;
  showHeader?: boolean;
  error?: string;
  previousNodes?: ComboboxNode[];
}

export const SchemaKeyValueInput: React.FC<SchemaKeyValueInputProps> = ({
  schema,
  value,
  onChange,
  error,
  showHeader = true,
  previousNodes,
}) => {
  const defaultValue = useRef(getDefaultValue(schema));
  const [localValue, setLocalValue] = useState<Record<string, any>>(value || defaultValue.current);
  const [orderedFields, setOrderedFields] = useState(() =>
    initializeOrderedFields(value || defaultValue.current)
  );
  const orderedFieldsKeysRef = useRef<string[]>([]);
  const [editingKey, setEditingKey] = useState<string | null>(null);
  const [newKeyName, setNewKeyName] = useState<string>('');
  const { classes } = useStyles();

  useEffect(() => {
    const currentValue = value || defaultValue.current;
    setLocalValue(currentValue);

    const currentKeys = Object.keys(currentValue || {});
    const prevKeys = orderedFieldsKeysRef.current;

    if (JSON.stringify(currentKeys) !== JSON.stringify(prevKeys)) {
      setOrderedFields(initializeOrderedFields(currentValue));
      orderedFieldsKeysRef.current = currentKeys;
    }
  }, [value]);

  const handleValueChange = useCallback(
    (key: string, newValue: any) => {
      const updated = { ...localValue, [key]: newValue };
      setLocalValue(updated);
      onChange?.(updated);
    },
    [localValue, onChange]
  );

  const handleAddField = useCallback(() => {
    const base = `newField_${orderedFields.length}`;
    const newKey = getUniqueKeyName(base, localValue);
    const updated = { ...localValue, [newKey]: '' };
    setLocalValue(updated);
    onChange?.(updated);
    orderedFieldsKeysRef.current = Object.keys(updated);
    setOrderedFields((prev) => [...prev, { id: generateKeyValueId(), name: newKey }]);
  }, [localValue, onChange, orderedFields]);

  const handleRemoveField = useCallback(
    (name: string) => {
      const { [name]: _, ...rest } = localValue;
      setLocalValue(rest);
      onChange?.(rest);
      orderedFieldsKeysRef.current = Object.keys(rest);
      setOrderedFields((prev) => prev.filter((r) => r.name !== name));
      if (editingKey === name) {
        setEditingKey(null);
        setNewKeyName('');
      }
    },
    [localValue, onChange, editingKey]
  );

  const handleSaveKeyName = useCallback(() => {
    if (!editingKey || !newKeyName) return;
    if (editingKey === newKeyName) return setEditingKey(null);

    const finalKey = getUniqueKeyName(newKeyName, localValue, editingKey);
    const { [editingKey]: moved, ...rest } = localValue;
    const updated = { ...rest, [finalKey]: moved };
    setLocalValue(updated);
    onChange?.(updated);
    orderedFieldsKeysRef.current = Object.keys(updated);
    setOrderedFields((prev) =>
      prev.map((r) => (r.name === editingKey ? { ...r, name: finalKey } : r))
    );
    setEditingKey(null);
  }, [editingKey, newKeyName, localValue, onChange]);

  const handleStartEditing = useCallback((name: string) => {
    setEditingKey(name);
    setNewKeyName(name);
  }, []);

  const handleCancelEditing = useCallback(() => {
    setEditingKey(null);
    setNewKeyName('');
  }, []);

  const renderField = useCallback(
    (name: string, fieldSchema: any) => {
      const v = localValue[name];
      if (fieldSchema.type === 'keyvalue') {
        return (
          <SchemaKeyValueInput
            schema={fieldSchema}
            value={v?.default || v}
            onChange={(nv) => handleValueChange(name, nv)}
            isDefault
            withContainer={false}
            previousNodes={previousNodes}
          />
        );
      }
      return (
        <SchemaFieldWrapper
          propertyKey={name}
          propertySchema={fieldSchema}
          value={v}
          onChange={(nv) => handleValueChange(name, nv)}
          error={error}
          previousNodes={previousNodes}
        />
      );
    },
    [localValue, error, previousNodes, handleValueChange]
  );

  return (
    <Stack gap={0}>
      {showHeader && <FieldHeader schema={schema} />}
      <Stack mt='xs' gap='sm' className={classes.container}>
        {orderedFields.map((row) => {
          const defaultSchema = schema?.default?.[row.name];
          return (
            <Paper key={row.id} withBorder className={classes.paper}>
              <Group gap='xs' align='center' wrap='nowrap' h={rem(30)} mb={rem(6)}>
                <KeyHeader
                  keyName={row.name}
                  isEditing={editingKey === row.name}
                  value={newKeyName}
                  onOpenEdit={handleStartEditing}
                  onValueChange={setNewKeyName}
                  onSave={handleSaveKeyName}
                  onCancel={handleCancelEditing}
                />
                <ActionIcon
                  size='xs'
                  variant='subtle'
                  color='decaNavy.4'
                  ml='auto'
                  onClick={() => handleRemoveField(row.name)}
                >
                  <IconTrash size={14} />
                </ActionIcon>
              </Group>
              <Box mih={rem(48)}>{renderField(row.name, defaultSchema || { type: 'text', name: row.name })}</Box>
            </Paper>
          );
        })}
        <Group>
          <DecaButton
            leftSection={<IconPlus size={14} />}
            size='xs'
            variant='neutral'
            onClick={handleAddField}
          >
            Add Field
          </DecaButton>
        </Group>
        {error && <Text c='red'>{error}</Text>}
      </Stack>
    </Stack>
  );
};

export const SchemaKeyValueInputWithContainer = withContainer(SchemaKeyValueInput);
export const FormKeyValueInput = withReactHookForm(SchemaKeyValueInput);
export const FormKeyValueInputWithContainer = withReactHookForm(SchemaKeyValueInputWithContainer);
