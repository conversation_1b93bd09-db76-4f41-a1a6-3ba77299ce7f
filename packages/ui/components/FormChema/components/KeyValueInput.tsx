import { ActionIcon, Box, Group, Paper, Stack, Text, TextInput, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCheck, IconPencil, IconX } from '@tabler/icons-react';
import type React from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { getDefaultValue } from '../../../utils/schema';
import { withContainer } from '../../hoc/withContainer';
import { withReactHookForm } from '../../hoc/withReactHookForm';
import type { SchemaField as SchemaFieldType } from '../type';
import { generateKeyValueId, getUniqueKeyName } from '../utils/field';
import { parseJsonToKeyValueArray, parseKeyValueArrayToJson } from '../utils/safetyParseJson';
import type { ComboboxNode } from './ComboboxSelectDataPoint';
import { ActionIconRemove, AddNewValueButton } from './FieldUtils';
import { Label } from './Label';
import { SchemaFieldWrapper } from './SchemaFieldWrapper';

const useStyles = createStyles((theme) => ({
  paper: {
    position: 'relative',
    padding: theme.spacing.xs,
    boxShadow: 'none',
    backgroundColor: theme.colors.decaLight[0],
  },
  container: {
    '& label': {
      display: 'none',
    },
  },
}));

interface KeyHeaderProps {
  keyName: string;
  onSave: (newKeyName: string) => void;
  onCancel?: () => void;
}

export const KeyHeader = ({ keyName, onSave, onCancel }: KeyHeaderProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [newKeyName, setNewKeyName] = useState(keyName);

  const handleStartEditing = useCallback(() => {
    setIsEditing(true);
  }, []);

  const handleSaveKeyName = useCallback(() => {
    setIsEditing(false);
    onSave(newKeyName);
  }, [newKeyName, onSave]);

  const handleCancelEditing = useCallback(() => {
    setIsEditing(false);
  }, []);

  useEffect(() => {
    setNewKeyName(keyName);
  }, [keyName]);

  if (isEditing) {
    return (
      <>
        <TextInput
          autoFocus
          value={newKeyName}
          onChange={(e) => setNewKeyName(e.target.value)}
          onKeyDown={(e) => {
            switch (e.key) {
              case 'Escape': {
                e.preventDefault();
                onCancel?.();
                break;
              }
              case 'Enter': {
                if (newKeyName) {
                  handleSaveKeyName();
                }
                break;
              }
            }
          }}
          size='xs'
          w='100%'
          maw='50%'
        />
        <ActionIcon
          size='xs'
          variant='subtle'
          c='decaGrey.6'
          onClick={handleSaveKeyName}
          disabled={!newKeyName}
        >
          <IconCheck size={14} />
        </ActionIcon>
        <ActionIcon size='xs' variant='subtle' c='decaGrey.6' onClick={handleCancelEditing}>
          <IconX size={14} />
        </ActionIcon>
      </>
    );
  }

  return (
    <>
      <Text fz='sm' fw={500}>
        {keyName}
      </Text>
      <ActionIcon size='xs' variant='subtle' c='decaGrey.6' onClick={handleStartEditing}>
        <IconPencil size={14} />
      </ActionIcon>
    </>
  );
};

interface SchemaKeyValueInputProps {
  schema: SchemaFieldType;
  value?: Record<string, any>;
  onChange?: (value: Record<string, any>) => void;
  isDefault?: boolean;
  withContainer?: boolean;
  showHeader?: boolean;
  error?: string;
  previousNodes?: ComboboxNode[];
}

export const SchemaKeyValueInput: React.FC<SchemaKeyValueInputProps> = ({
  schema,
  value,
  onChange,
  error,
  showHeader = true,
  previousNodes,
}) => {
  const defaultValue = useRef(parseJsonToKeyValueArray(getDefaultValue(schema)));
  const [orderedFields, setOrderedFields] = useState(() =>
    parseJsonToKeyValueArray(value || defaultValue.current)
  );

  const listIdRef = useRef<string[]>([]);
  const { classes } = useStyles();

  useEffect(() => {
    const currentValue = parseJsonToKeyValueArray(value || defaultValue.current);
    if (listIdRef.current.length !== currentValue.length) {
      listIdRef.current = currentValue.map(() => generateKeyValueId());
    }
    setOrderedFields(currentValue.map((o, index) => ({ ...o, id: listIdRef.current[index] })));
  }, [value]);

  const handleValueChange = useCallback(
    (id: string, newValue: any) => {
      const updated = orderedFields.map((o) => (o.id === id ? { ...o, value: newValue } : o));
      onChange?.(parseKeyValueArrayToJson(updated));
    },
    [onChange, orderedFields]
  );

  const handleAddField = useCallback(() => {
    const base = `newField_${orderedFields.length}`;
    const newKey = getUniqueKeyName(base, parseJsonToKeyValueArray(orderedFields));
    const newId = generateKeyValueId();
    const updated = [...orderedFields, { id: newId, key: newKey, value: '' }];
    listIdRef.current = [...listIdRef.current, newId];
    setOrderedFields(updated);
    onChange?.(parseKeyValueArrayToJson(updated));
  }, [onChange, orderedFields]);

  const handleRemoveField = useCallback(
    (id: string) => {
      const rest = orderedFields.filter((o) => o.id !== id);
      onChange?.(parseKeyValueArrayToJson(rest));
      listIdRef.current = listIdRef.current.filter((o) => o !== id);
      setOrderedFields((prev) => prev.filter((r) => r.id !== id));
    },
    [onChange, orderedFields]
  );

  const handleChangeKeyName = useCallback(
    (id: string, newKeyName: string) => {
      const updated = orderedFields.map((o) => (o.id === id ? { ...o, key: newKeyName } : o));
      onChange?.(parseKeyValueArrayToJson(updated));
      setOrderedFields(updated);
    },
    [onChange, orderedFields]
  );

  const renderField = useCallback(
    (id: string, fieldSchema: any) => {
      const v = orderedFields.find((o) => o.id === id);
      if (!v || !id) return null;
      if (fieldSchema.type === 'keyvalue') {
        return (
          <SchemaKeyValueInput
            schema={fieldSchema}
            value={v?.value?.default || v?.value}
            onChange={(nv) => handleValueChange(id, nv)}
            isDefault
            withContainer={false}
            previousNodes={previousNodes}
          />
        );
      }
      return (
        <SchemaFieldWrapper
          propertyKey={v.key}
          propertySchema={fieldSchema}
          value={v?.value?.default || v?.value}
          onChange={(nv) => handleValueChange(id, nv)}
          error={error}
          previousNodes={previousNodes}
        />
      );
    },
    [orderedFields, error, previousNodes, handleValueChange]
  );

  return (
    <Stack gap={0}>
      {showHeader && <Label schema={schema} />}
      <Stack mt='xs' gap='sm' className={classes.container}>
        {orderedFields.map((row) => {
          const defaultSchema = schema?.default?.[row.key];
          return defaultSchema ? (
            <Paper key={row.id} className={classes.paper}>
              <Group
                gap='xs'
                align='center'
                wrap='nowrap'
                h={rem(30)}
                mb={rem(6)}
                justify='space-between'
              >
                <Group gap='xs'>
                  <KeyHeader
                    keyName={row.key}
                    onSave={(newKeyName) => handleChangeKeyName(row.id, newKeyName)}
                    onCancel={() => handleRemoveField(row.id)}
                  />
                </Group>
                <ActionIconRemove onClick={() => handleRemoveField(row.id)} />
              </Group>
              <Box mih={rem(48)}>
                {renderField(row.id, defaultSchema || { type: 'text', name: row.key })}
              </Box>
            </Paper>
          ) : (
            <Group key={row.id} gap='xs' align='center' wrap='nowrap' h={rem(30)} mb={rem(6)}>
              <TextInput
                value={row.key}
                size='md'
                onChange={(e) => handleChangeKeyName(row.id, e.target.value)}
              />
              {renderField(row.id, defaultSchema || { type: 'text', name: row.key })}
              <ActionIconRemove onClick={() => handleRemoveField(row.id)} />
            </Group>
          );
        })}
        {error && <Text c='red'>{error}</Text>}
      </Stack>
      <AddNewValueButton onClick={handleAddField} />
    </Stack>
  );
};

export const SchemaKeyValueInputWithContainer = withContainer(SchemaKeyValueInput);
export const FormKeyValueInput = withReactHookForm(SchemaKeyValueInput);
export const FormKeyValueInputWithContainer = withReactHookForm(SchemaKeyValueInputWithContainer);
