import {
  Box,
  Flex,
  Group,
  Loader,
  Combobox as MantineCombobox,
  type ComboboxProps as MantineComboboxProps,
  Radio,
  ScrollArea,
  Stack,
  Text,
  rem,
  useCombobox,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconPlugConnected, IconPlus, IconSearch, IconX } from '@tabler/icons-react';
import { useCallback, useMemo, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { DecaButton } from '../../DecaButton';

const useStyles = createStyles((theme) => ({
  credentialConnected: {
    backgroundColor: theme.colors.decaLight[1],
    padding: rem(8),
  },
  name: {
    fontSize: rem(14),
    fontWeight: 500,
    color: theme.colors.decaGrey[6],
  },
  dropdown: {
    border: `1px solid ${theme.colors.decaBlue[5]}`,
    boxShadow: `0px 0px 4px 0px ${theme.colors.decaBlue[5]}`,
    borderRadius: rem(6),
    minWidth: rem(320),
    maxWidth: rem(320),
    height: rem(440),
    padding: rem(0),
    display: 'flex',
    flexDirection: 'column',
  },
  comboboxInputWrapper: {
    padding: rem(16),
    paddingBottom: rem(0),
  },
  comboboxInput: {
    '--input-height': `${rem(30)} !important`,
    display: 'flex',
    width: '100%',
    '& > [data-position="left"]': {
      '--section-start': rem(10),
      '--section-end': rem(14),
      '--section-size': rem(34),
      position: 'absolute',
      left: 0,
      top: '50%',
      transform: 'translateY(-50%)',
      color: theme.colors.decaGrey[6],
      '&.mantine-Combobox-search': {
        border: '1px solid #3539BC',
      },
      '&::placeholder': {
        color: theme.colors.decaGrey[3],
      },
    },
    input: {
      borderRadius: theme.radius.sm,
      border: '1px solid transparent',
      margin: 0,
      paddingLeft: rem(38),
      '&:focus': {
        borderColor: theme.colors.decaNavy[5],
        boxShadow: `0px 0px 4px 0px ${theme.colors.decaBlue[5]}`,
      },
    },
  },
  comboboxOptions: {
    padding: `${rem(12)} 0`,
    height: '100%',
    overflow: 'hidden',
  },
  comboboxGroup: {
    display: 'flex',
    flexDirection: 'column',
    gap: rem(8),
    padding: `0 ${rem(16)}`,
  },
  comboboxOption: {
    '--combobox-background': 'transparent',
    '--combobox-color': theme.colors.decaGrey[9],
    display: 'flex',
    flexDirection: 'column',
    gap: rem(4),
    padding: `${rem(4)} ${rem(8)}`,
    minHeight: rem(46),
    backgroundColor: 'var(--combobox-background) !important',
    transition: 'background-color 0.2s ease',
    color: 'var(--combobox-color)',
    '&[aria-selected="true"], &[data-selected=""], &:hover': {
      '--combobox-background': theme.colors.decaLight[1],
    },
  },
  createButton: {
    width: '100%',
    position: 'absolute',
    bottom: 0,
    left: 0,
    padding: rem(12),
  },
  credentialInput: {
    border: `1px solid ${theme.colors.decaLight[4]}`,
    borderRadius: theme.radius.sm,
  },
  iconX: {
    cursor: 'pointer',
    marginLeft: rem(4),
    width: rem(16),
    height: rem(16),
    flexShrink: 0,
    '&:hover': {
      color: theme.colors.decaGrey[6],
    },
  },
}));

export type ComboboxOption = {
  value: string;
  label: string;
  description?: string;
  type?: string;
  provider?: string;
};

export interface ChooseCredentialProps {
  id?: string;
  value?: string;
  options: ComboboxOption[];
  position?: MantineComboboxProps['position'];
  offset?: MantineComboboxProps['offset'];
  isLoading?: boolean;
  onChange: (optionValue: string) => void;
  onConnectNewCredential: () => void;
  isRequired?: boolean;
}

export const ChooseCredential = (props: ChooseCredentialProps) => {
  const {
    id,
    value,
    onChange,
    options = [],
    position = 'left-start',
    offset = { crossAxis: 10, mainAxis: 16 },
    isLoading = false,
    onConnectNewCredential,
    isRequired = true,
  } = props;
  const { classes } = useStyles();
  const [searchValue, setSearchValue] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  const combobox = useCombobox({
    opened: isOpen,
    onOpenedChange: setIsOpen,
    onDropdownClose: () => {
      combobox.resetSelectedOption();
      combobox.focusTarget();
      setSearchValue('');
    },
    onDropdownOpen: (eventSource) => {
      combobox.focusSearchInput();
      if (eventSource === 'keyboard') {
        combobox.selectActiveOption();
      } else {
        combobox.updateSelectedOptionIndex('active');
      }
    },
  });

  const selectedOptionLabel = useMemo(() => {
    const selectedOption = options.find((option) => option.value === value);
    return selectedOption?.label || '';
  }, [options, value]);

  const shouldFilterOptions = useMemo(
    () => !options?.some((option) => option.value === searchValue),
    [options, searchValue]
  );

  const filteredOptions = useMemo(() => {
    if (!shouldFilterOptions) {
      return options;
    }

    return options?.filter((option) => {
      const searchTerm = searchValue.toLowerCase().trim();
      return (
        option.value.toLowerCase().includes(searchTerm) ||
        option.label.toLowerCase().includes(searchTerm) ||
        option.description?.toLowerCase().includes(searchTerm)
      );
    });
  }, [options, searchValue, shouldFilterOptions]);

  const onOptionSubmit = useCallback(
    (optionValue: string) => {
      onChange(optionValue);
      combobox.closeDropdown();
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [onChange, combobox.closeDropdown]
  );

  const handleConnectNewCredential = useCallback(
    () => {
      onConnectNewCredential?.();
      combobox.closeDropdown();
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [onConnectNewCredential, combobox.closeDropdown]
  );

  return (
    <MantineCombobox
      store={combobox}
      position={position}
      offset={offset}
      onOptionSubmit={onOptionSubmit}
    >
      <MantineCombobox.Target withAriaAttributes={false}>
        <Stack gap='sm' onClick={() => combobox.toggleDropdown()}>
          <Text size='md' fw='500'>
            Credential{' '}
            <Text span size='md' c='red' inherit>
              {isRequired ? '*' : ''}
            </Text>
          </Text>
          <Group
            justify='space-between'
            p='xs'
            className={classes.credentialInput}
            style={{ flexWrap: 'nowrap' }}
          >
            {selectedOptionLabel ? (
              <Flex gap='xs' className={classes.credentialConnected} style={{ minWidth: 0 }}>
                <IconPlugConnected style={{ width: rem(18), height: rem(18), flexShrink: 0 }} />
                <Text size='sm' truncate='end' style={{ minWidth: 0 }}>
                  {selectedOptionLabel}
                </Text>
                {!isRequired ? <IconX className={classes.iconX} onClick={() => {
                  onOptionSubmit('');
                }} /> : null}
              </Flex>
            ) : (
              <Text size='sm'>Select a credential</Text>
            )}
            <DecaButton
              variant='neutral'
              size='xs'
              onClick={() => combobox.toggleDropdown()}
              style={{ flexShrink: 0 }}
            >
              Change
            </DecaButton>
          </Group>
        </Stack>
      </MantineCombobox.Target>
      <MantineCombobox.Dropdown className={classes.dropdown}>
        <div className={classes.comboboxInputWrapper}>
          <MantineCombobox.Search
            id={id}
            value={searchValue}
            onChange={(event) => setSearchValue(event.currentTarget.value)}
            leftSection={<IconSearch size={16} />}
            leftSectionPointerEvents='none'
            placeholder='Search credentials'
            className={classes.comboboxInput}
            size='sm'
          />
        </div>
        <MantineCombobox.Options className={classes.comboboxOptions}>
          <ScrollArea type='scroll' h='100%' mah='80%'>
            <MantineCombobox.Group className={classes.comboboxGroup}>
              {isLoading ? (
                <Text size='sm' ta='center' py='lg' c='dimmed'>
                  <Loader size={16} />
                </Text>
              ) : filteredOptions?.length > 0 ? (
                filteredOptions.map((option) => (
                  <MantineCombobox.Option
                    key={uuidv4()}
                    value={option.value}
                    className={classes.comboboxOption}
                    data-selected={option.value === value ? '' : undefined}
                  >
                    <Radio
                      checked={option.value === value}
                      onChange={() => onChange(option.value)}
                      label={option.label}
                      description={option.description}
                      size='sm'
                      color='#3539BC'
                      styles={{
                        label: {
                          fontSize: rem(14),
                          fontWeight: 500,
                          color: '#333333',
                        },
                      }}
                    />
                  </MantineCombobox.Option>
                ))
              ) : (
                <MantineCombobox.Empty>No credentials found</MantineCombobox.Empty>
              )}
            </MantineCombobox.Group>
          </ScrollArea>
          <Box className={classes.createButton}>
            <DecaButton
              leftSection={<IconPlus size={14} />}
              variant='neutral'
              size='sm'
              w='100%'
              onClick={handleConnectNewCredential}
            >
              Connect a new credential
            </DecaButton>
          </Box>
        </MantineCombobox.Options>
      </MantineCombobox.Dropdown>
    </MantineCombobox>
  );
};
