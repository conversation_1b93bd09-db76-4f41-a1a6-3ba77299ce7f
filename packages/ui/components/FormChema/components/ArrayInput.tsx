import { Flex, Group, Paper, Stack, Text } from '@mantine/core';
import { debounce } from 'lodash';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import type { Control } from 'react-hook-form';
import { getDefaultValue } from '../../../utils/schema';
import { withContainer } from '../../hoc/withContainer';
import { withReactHookForm } from '../../hoc/withReactHookForm';
import { safetyParseJson } from '../utils/safetyParseJson';
import type { ComboboxNode } from './ComboboxSelectDataPoint';
import { ActionIconRemove, AddNewValueButton } from './FieldUtils';
import { SchemaKeyValueInput } from './KeyValueInput';
import { Label } from './Label';
import { SchemaFieldWrapper } from './SchemaFieldWrapper';

type ArrayItemsType = 'object' | 'keyvalue' | undefined;

export interface SchemaArrayInputBaseProps {
  value?: string;
  schema: Record<string, any>;
  onChange?: (value: any) => void;
  withContainer?: boolean;
  error?: string;
  control?: Control<any>;
  previousNodes?: ComboboxNode[];
}

const getInitialValue = (
  schema: Record<string, any>,
  arrayItemsType: ArrayItemsType,
  value?: string
) => {
  const defaultValues =
    arrayItemsType === 'keyvalue' ? [getDefaultValue(schema.items)] : schema?.default;
  const data = value || defaultValues || '[]';
  const parsedData = typeof data === 'string' ? safetyParseJson(data, []) : data;

  // If value is undefined and schema is not required, keep it undefined
  if ((value === undefined || value === '') && !schema.required) {
    return [];
  }

  return Array.isArray(parsedData) && parsedData.length ? parsedData : [''];
};

const ArrayInputBase = ({
  schema,
  onChange,
  error,
  value: valueProp,
  previousNodes,
}: SchemaArrayInputBaseProps) => {
  const arrayItemsType: ArrayItemsType = useMemo(() => {
    if (schema?.items?.type === 'object' && schema?.items?.properties) return 'object';
    if (
      schema?.items?.type === 'keyvalue' ||
      (schema?.items?.type === 'object' && !schema?.items?.properties)
    )
      return 'keyvalue';
    return undefined;
  }, [schema]);

  const initData = useMemo<any[]>(() => {
    return getInitialValue(schema, arrayItemsType, valueProp);
  }, [schema, arrayItemsType, valueProp]);

  const labelDisplayName = useMemo(() => {
    return schema?.originalDisplayName || schema?.displayName;
  }, [schema?.displayName, schema?.originalDisplayName]);

  const [values, setValues] = useState(initData);
  const [isCleared, setIsCleared] = useState(initData.length === 0 && !schema.required);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const handleOnChangeValuesToJsonString = useCallback(
    (values) => {
      return onChange?.(values);
    },
    [onChange]
  );

  const handleAddNewValue = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsCleared(false);
    let newValue: any[] = [];

    switch (arrayItemsType) {
      case 'object': {
        const newObject = Object.keys(schema.items.properties).reduce(
          (acc, key) => {
            acc[key] = schema.items.properties[key].default || '';
            return acc;
          },
          {} as Record<string, any>
        );
        newValue = [...values, newObject];
        break;
      }
      case 'keyvalue': {
        const newObject = schema.items?.default
          ? Object.keys(schema.items.default).reduce(
              (acc, key) => {
                acc[key] = schema.items.default[key]?.default || '';
                return acc;
              },
              {} as Record<string, any>
            )
          : {};
        newValue = [...values, newObject];
        break;
      }
      default: {
        newValue = [...values, ''];
        break;
      }
    }
    setValues(newValue);
    handleOnChangeValuesToJsonString(newValue);

    // Scroll to the bottom after adding new value
    setTimeout(() => {
      if (scrollContainerRef.current) {
        scrollContainerRef.current.scrollTop = scrollContainerRef.current.scrollHeight;
      }
    }, 100);
  };

  const handleRemoveValue = (index: number) => {
    const newValues = [...values];
    if (values.length === 1) {
      // If schema is not required, set to undefined instead of empty values
      if (!schema.required) {
        setValues([]);
        setIsCleared(true);
        handleOnChangeValuesToJsonString(undefined);
        return;
      }

      switch (arrayItemsType) {
        case 'object': {
          newValues[0] = Object.keys(schema.items.properties).reduce(
            (acc, key) => {
              acc[key] = '';
              return acc;
            },
            {} as Record<string, any>
          );
          break;
        }
        case 'keyvalue': {
          newValues[0] = {};
          break;
        }
        default: {
          newValues[0] = '';
          break;
        }
      }

      setValues(newValues);
      handleOnChangeValuesToJsonString([]);
      return;
    }
    newValues.splice(index, 1);
    setValues(newValues);
    handleOnChangeValuesToJsonString(newValues);
  };

  const debounceChange = useMemo(() => {
    return debounce(handleOnChangeValuesToJsonString, 300);
  }, [handleOnChangeValuesToJsonString]);

  const handleValueChange = (index: number, value: string) => {
    const newValue = [...values];
    newValue[index] = value;
    setValues(newValue);
    debounceChange(newValue);
  };

  // const handleValueBlur = (index: number) => {
  //   const currentValue = values[index];
  //   if (typeof currentValue === 'string') {
  //     const trimmedValue = currentValue.trim();
  //     if (trimmedValue !== currentValue) {
  //       const newValue = [...values];
  //       newValue[index] = trimmedValue;
  //       setValues(newValue);
  //       handleOnChangeValuesToJsonString(newValue);
  //     }
  //   }
  // };

  const handleObjectValueChange = (index: number, propertyKey: string, value: any) => {
    const newValue = [...values];
    if (!newValue[index]) {
      newValue[index] = {};
    }
    newValue[index][propertyKey] = value;
    setValues(newValue);
    handleOnChangeValuesToJsonString(newValue);
  };

  const handleKeyValueChange = (index: number, value: Record<string, any>) => {
    const newValue = [...values];
    newValue[index] = value;
    setValues(newValue);
    handleOnChangeValuesToJsonString(newValue);
  };

  useEffect(() => {
    // Don't reset values if we intentionally cleared them
    if (!isCleared) {
      setValues(initData);
    }
    if (initData.length === 0 && !schema.required) {
      setIsCleared(true);
    }
  }, [initData, isCleared, schema.required]);

  // Render object properties for each array item
  const renderObjectItem = (item: any, index: number) => {
    const properties = schema.items.properties;

    return (
      <Paper key={index} p='md' shadow='none' bg='decaLight.0'>
        <Group justify='space-between' mb='md'>
          <Text fw={500} fz='sm'>
            {`${labelDisplayName} ${index + 1}`}
          </Text>
          <ActionIconRemove onClick={() => handleRemoveValue(index)} />
        </Group>
        <Stack gap='md'>
          {Object.entries(properties).map(([propertyKey, propertySchema]: [string, any]) => (
            <SchemaFieldWrapper
              key={`${index}-${propertyKey}`}
              propertyKey={propertyKey}
              propertySchema={propertySchema}
              value={item?.[propertyKey]}
              onChange={(val: any) => handleObjectValueChange(index, propertyKey, val)}
              error={error}
              index={index}
              previousNodes={previousNodes}
            />
          ))}
        </Stack>
      </Paper>
    );
  };

  // Render key-value pair for each array item using existing KeyValueInput
  const renderKeyValueItem = (item: any, index: number) => {
    return (
      <Paper key={index} p='md' shadow='none' bg='decaLight.0'>
        <Group justify='space-between' mb='md'>
          <Text fw={500} fz='sm'>
            {`${labelDisplayName} ${index + 1}`}
          </Text>
          <ActionIconRemove onClick={() => handleRemoveValue(index)} />
        </Group>
        <SchemaKeyValueInput
          schema={schema.items}
          value={item || {}}
          onChange={(value) => handleKeyValueChange(index, value)}
          withContainer={false}
          error={error}
          previousNodes={previousNodes}
        />
      </Paper>
    );
  };

  return (
    <Stack gap='xs' flex={1}>
      <Label schema={schema} />
      {values.length > 0 && (
        <Flex ref={scrollContainerRef} direction='column' gap='xs'>
          {values.length > 0 &&
            values.map((value, index) => {
              if (arrayItemsType === 'object') {
                return renderObjectItem(value, index);
              }

              if (arrayItemsType === 'keyvalue') {
                return renderKeyValueItem(value, index);
              }

              return (
                // biome-ignore lint/suspicious/noArrayIndexKey: <explanation> avoid re-render -> lost focus on input
                <Group key={index} w='100%' gap='xs'>
                  <SchemaFieldWrapper
                    propertyKey={''}
                    propertySchema={{
                      ...schema.items,
                      type: schema.items?.type || 'text',
                      name: `${schema.name}.${index}`,
                    }}
                    value={value}
                    error={error}
                    previousNodes={previousNodes}
                    onChange={(value) => handleValueChange(index, value)}
                  />

                  {values.length > 1 && (
                    <ActionIconRemove onClick={() => handleRemoveValue(index)} />
                  )}
                </Group>
              );
            })}
        </Flex>
      )}

      <AddNewValueButton onClick={handleAddNewValue} />
    </Stack>
  );
};

export const SchemaArrayInput = ArrayInputBase;
export const SchemaArrayInputWithContainer = withContainer(SchemaArrayInput);
export const FormArrayInput = withReactHookForm(SchemaArrayInput);
export const FormArrayInputWithContainer = withReactHookForm(SchemaArrayInputWithContainer);
