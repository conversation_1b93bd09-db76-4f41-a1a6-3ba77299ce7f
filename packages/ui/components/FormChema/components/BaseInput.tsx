import { NumberInput, TextInput, Textarea } from '@mantine/core';
import { useCallbackRef } from '@mantine/hooks';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import MaskedPasswordInput from '../../MaskedPasswordInput';
import { withContainer } from '../../hoc/withContainer';
import { useFieldStyles } from '../styles';

export interface SchemaBaseInputProps {
  value?: string;
  schema: Record<string, any>;
  onChange?: (value: any) => void;
  withContainer?: boolean;
  error?: string;
}

const NUMBER_TYPES = ['number', 'integer'];
const NUMBER_TYPES_WITH_DECIMAL = ['number'];

const getInitialValue = (schema: any, value: any) => {
  const typeSchema = schema.type;
  if (typeSchema === 'number' || typeSchema === 'integer') {
    return Number.isNaN(Number(value)) ? schema?.default || '' : Number(value);
  }

  return !!value && typeof value === 'string' ? value : schema?.default || '';
};

export const SchemaBaseInput: React.FC<SchemaBaseInputProps> = ({
  schema,
  onChange,
  error,
  value: valueProp,
}: SchemaBaseInputProps) => {
  const { classes, cx } = useFieldStyles();
  const initialValue = getInitialValue(schema, valueProp);
  const [value, setValue] = useState(initialValue);
  const isInitializeRef = useRef(false);

  const onChangeRef = useCallbackRef(onChange);

  const onUpdateValue = useCallback(
    (newValue: any, shouldPropagate = true) => {
      setValue(newValue);
      if (shouldPropagate) {
        onChangeRef?.(newValue);
      }
    },
    [onChangeRef]
  );
  useEffect(() => {
    if (!isInitializeRef.current) {
      if (
        schema.default &&
        typeof schema.default === 'object' &&
        Object.keys(schema.default).length === 0
      ) {
        onUpdateValue('', false);
      } else {
        onUpdateValue(initialValue, false);
      }
      isInitializeRef.current = true;
    } else {
      onUpdateValue(initialValue, true);
    }
  }, [schema.default, schema.required, onUpdateValue, initialValue]);

  const isNotNumber = Number.isNaN(Number(value));
  const haveLinebreak = isNotNumber && typeof value === 'string' && value.includes('\n');

  const InputComponent = useMemo(() => {
    if (haveLinebreak) {
      return Textarea;
    }
    if (schema.type === 'text-masked') {
      return MaskedPasswordInput;
    }
    if (NUMBER_TYPES.includes(schema.type)) {
      return NumberInput;
    }
    return TextInput;
  }, [haveLinebreak, schema.type]);

  const handleChange = (e: any) => {
    let newValue = e?.target?.value ?? '';
    if (NUMBER_TYPES.includes(schema.type)) {
      newValue = e;
    }
    if (schema.type === 'text-masked') {
      newValue = e;
    }
    onUpdateValue(newValue, true);
  };
  return (
    <InputComponent
      name={schema.name}
      value={value}
      onChange={handleChange}
      onBlur={() => {
        if (typeof value === 'string') {
          onChange?.(value.trim());
        } else {
          onChange?.(value);
        }
      }}
      label={schema.displayName}
      placeholder={schema.placeholder}
      labelProps={{
        required: !schema?.alternateAskterisk && schema.required,
      }}
      min={0}
      max={100000000}
      description={schema.description}
      error={error}
      allowDecimal={NUMBER_TYPES_WITH_DECIMAL.includes(schema.type)}
      allowNegative={false}
      disabled={schema?.disabled}
      readOnly={schema?.readOnly}
      hideControls
      size='md'
      classNames={{
        root: cx(classes.root, {
          [classes.hidden]: schema.hidden,
        }),
        label: classes.label,
        description: classes.description,
        input: classes.input,
      }}
      visibleCharacterPrefix={schema.prefixLength}
      visibleCharacterSuffix={schema.suffixLength}
      allowView={schema.allowView}
      maskCharacter={schema.maskCharacter || '*'}
    />
  );
};

export const SchemaBaseInputWithContainer = withContainer(SchemaBaseInput);
