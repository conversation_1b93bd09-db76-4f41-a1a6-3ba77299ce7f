import { NumberInput, TextInput, Textarea } from '@mantine/core';
import { useCallbackRef } from '@mantine/hooks';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import MaskedPasswordInput from '../../MaskedPasswordInput';
import { withContainer } from '../../hoc/withContainer';
import { useFieldStyles } from '../styles';
import { getLabel } from './FieldUtils';

export interface SchemaBaseInputProps {
  value?: string;
  schema: Record<string, any>;
  onChange?: (value: any) => void;
  withContainer?: boolean;
  error?: string;
}

const NUMBER_TYPES = ['number', 'integer'];
const NUMBER_TYPES_WITH_DECIMAL = ['number'];

const getInitialValue = (schema: any, value: any) => {
  if (schema.hidden) {
    return schema.default;
  }

  const typeSchema = schema.type;
  if (typeSchema === 'number' || typeSchema === 'integer') {
    return Number.isNaN(Number(value)) ? schema?.default || '' : Number(value);
  }

  // For string inputs, return undefined instead of empty string when no value is provided
  if (!!value && typeof value === 'string') {
    return value;
  }

  const defaultValue = schema?.default || '';
  return defaultValue === '' ? undefined : defaultValue;
};

export const SchemaBaseInput: React.FC<SchemaBaseInputProps> = ({
  schema,
  onChange,
  error,
  value: valueProp,
}: SchemaBaseInputProps) => {
  const { classes, cx } = useFieldStyles();
  const initialValue = getInitialValue(schema, valueProp);
  const [value, setValue] = useState(initialValue);
  const isInitializeRef = useRef(false);

  const onChangeRef = useCallbackRef(onChange);

  const onUpdateValue = useCallback(
    (newValue: any, shouldPropagate = true) => {
      setValue(newValue);
      if (shouldPropagate) {
        onChangeRef?.(newValue);
      }
    },
    [onChangeRef]
  );
  useEffect(() => {
    if (!isInitializeRef.current) {
      if (
        schema.default &&
        typeof schema.default === 'object' &&
        Object.keys(schema.default).length === 0
      ) {
        // For string inputs, use undefined instead of empty string
        const emptyValue =
          NUMBER_TYPES.includes(schema.type) || schema.type === 'text-masked' ? '' : undefined;
        onUpdateValue(emptyValue, false);
      } else {
        onUpdateValue(initialValue, false);
      }
      isInitializeRef.current = true;
    } else {
      onUpdateValue(initialValue, true);
    }
  }, [schema.default, schema.required, onUpdateValue, initialValue]);

  useEffect(() => {
    if (schema.hidden && schema.default !== undefined) {
      onUpdateValue(schema.default, true);
    }
  }, [schema.hidden, schema.default, onUpdateValue]);

  const isNotNumber = Number.isNaN(Number(value));
  const haveLinebreak = isNotNumber && typeof value === 'string' && value.includes('\n');

  const InputComponent = useMemo(() => {
    if (haveLinebreak) {
      return Textarea;
    }
    if (schema.type === 'text-masked') {
      return MaskedPasswordInput;
    }
    if (NUMBER_TYPES.includes(schema.type)) {
      return NumberInput;
    }
    return TextInput;
  }, [haveLinebreak, schema.type]);

  const handleChange = (e: any) => {
    if (schema.hidden) {
      return;
    }

    let newValue = e?.target?.value ?? '';
    if (NUMBER_TYPES.includes(schema.type)) {
      newValue = e;
    }
    if (schema.type === 'text-masked') {
      newValue = e;
    }
    // For string inputs, return undefined instead of empty string when cleared
    if (!NUMBER_TYPES.includes(schema.type) && schema.type !== 'text-masked' && newValue === '') {
      newValue = undefined;
    }
    onUpdateValue(newValue, true);
  };
  return (
    <InputComponent
      name={schema.name}
      value={value ?? ''}
      onChange={handleChange}
      onBlur={() => {
        if (typeof value === 'string') {
          const trimmedValue = value.trim();
          const finalValue = trimmedValue === '' ? undefined : trimmedValue;
          onChange?.(finalValue);
        } else {
          onChange?.(value);
        }
      }}
      label={getLabel({
        ...schema,
        required: !schema?.alternateAskterisk && schema.required,
      })}
      placeholder={schema.placeholder}
      min={0}
      max={100000000}
      description={null}
      error={error}
      allowDecimal={NUMBER_TYPES_WITH_DECIMAL.includes(schema.type)}
      allowNegative={false}
      disabled={schema?.disabled}
      readOnly={schema?.readOnly}
      size='md'
      classNames={{
        root: cx(classes.root, {
          [classes.hidden]: schema.hidden,
        }),
        label: classes.label,
        description: classes.description,
        input: classes.input,
        controls: classes.numberControls,
        control: classes.numberControl,
      }}
      visibleCharacterPrefix={schema.prefixLength}
      visibleCharacterSuffix={schema.suffixLength}
      allowView={schema.allowView}
      maskCharacter={schema.maskCharacter || '*'}
    />
  );
};

export const SchemaBaseInputWithContainer = withContainer(SchemaBaseInput);
