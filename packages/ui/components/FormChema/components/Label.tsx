import { Flex, Tooltip, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconInfoCircle } from '@tabler/icons-react';

const useStyles = createStyles((theme) => ({
  required: {
    color: theme.colors.red[6],
  },
  infoCircle: {
    color: theme.colors.decaGrey[5],
    cursor: 'pointer',
  },
}));

export const Label = ({ schema }: { schema: Record<string, any> }) => {
  const { classes } = useStyles();
  return (
    <Flex align='center' gap={rem(4)}>
      {schema.displayName}
      {schema.required && <span className={classes.required}>*</span>}
      {schema.description && (
        <Tooltip label={schema.description} multiline arrowPosition='center' withArrow arrowSize={8} w="250px">
          <IconInfoCircle size={10} className={classes.infoCircle} />
        </Tooltip>
      )}
    </Flex>
  );
};
