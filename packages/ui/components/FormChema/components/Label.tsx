import { Group, Tooltip, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import type React from 'react';
import { IconInfoCircle } from '../../Icons';

const useStyles = createStyles((theme) => ({
  required: {
    color: theme.colors.red[6],
  },
  infoCircleContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: rem(10),
    height: rem(10),
    cursor: 'pointer',
  },
}));

interface LabelProps {
  schema: Record<string, any>;
  rightSection?: React.ReactNode;
  classNames?: string;
}

export const Label = ({ schema, rightSection, classNames }: LabelProps) => {
  const { classes } = useStyles();
  return (
    <Group className={classNames} gap={rem(4)} justify='space-between' align='center' fw={500}>
      <Group gap={rem(4)}>
        {schema.displayName}
        {schema.required && !schema.alternateAskterisk && (
          <span className={classes.required}>*</span>
        )}
        {schema.description && (
          <Tooltip
            label={schema.description}
            multiline
            arrowPosition='center'
            withArrow
            arrowSize={8}
          >
            <div className={classes.infoCircleContainer}>
              <IconInfoCircle width={10} height={10} />
            </div>
          </Tooltip>
        )}
      </Group>
      {rightSection}
    </Group>
  );
};
