import { SchemaArrayInput } from './components/ArrayInput';
import { SchemaCheckbox } from './components/Checkbox';
import { SchemaCode } from './components/Code';
import { SchemaComboboxSelectDataPoint } from './components/ComboboxSelectDataPoint';
import { SchemaDate } from './components/Date';
import { SchemaDateTime } from './components/DateTime';
import { SchemaDisplayName } from './components/DisplayName';
import { SchemaObjectInput } from './components/ObjectInput';
import { SchemaPasswordInput } from './components/PasswordInput';
import { SchemaRadio } from './components/Radio';
import { SchemaSelect, SchemaSelectOption } from './components/SelectOption';
import { SchemaTextInput } from './components/TextInput';
import { SchemaTextareaInput } from './components/TextareaInput';
import { SchemaTime } from './components/Time';
import { SchemaURLInput } from './components/URLInput';
import { SCHEMA_CONFIG_DEFINITIONS } from './schemaConfigs';
import type { ComponentConfig } from './type';

export const SCHEMA_CONFIGS: Record<string, ComponentConfig<any>> = {
  textInput: {
    ...SCHEMA_CONFIG_DEFINITIONS.textInput,
    Component: SchemaTextInput,
  },
  passwordInput: {
    ...SCHEMA_CONFIG_DEFINITIONS.passwordInput,
    Component: SchemaPasswordInput,
  },
  checkbox: {
    ...SCHEMA_CONFIG_DEFINITIONS.checkbox,
    Component: SchemaCheckbox,
  },
  displayName: {
    ...SCHEMA_CONFIG_DEFINITIONS.displayName,
    Component: SchemaDisplayName,
  },
  selectOption: {
    ...SCHEMA_CONFIG_DEFINITIONS.selectOption,
    Component: SchemaSelectOption,
  },
  select: {
    ...SCHEMA_CONFIG_DEFINITIONS.select,
    Component: SchemaSelect,
  },
  radio: {
    ...SCHEMA_CONFIG_DEFINITIONS.radio,
    Component: SchemaRadio,
  },
  textarea: {
    ...SCHEMA_CONFIG_DEFINITIONS.textarea,
    Component: SchemaTextareaInput,
  },
  code: {
    ...SCHEMA_CONFIG_DEFINITIONS.code,
    Component: SchemaCode,
  },
  comboboxSelectDataPoint: {
    ...SCHEMA_CONFIG_DEFINITIONS.comboboxSelectDataPoint,
    Component: SchemaComboboxSelectDataPoint,
  },
  urlInput: {
    ...SCHEMA_CONFIG_DEFINITIONS.urlInput,
    Component: SchemaURLInput,
  },
  object: {
    ...SCHEMA_CONFIG_DEFINITIONS.object,
    Component: SchemaObjectInput,
  },
  dateTime: {
    ...SCHEMA_CONFIG_DEFINITIONS.dateTime,
    Component: SchemaDateTime,
  },
  array: {
    ...SCHEMA_CONFIG_DEFINITIONS.array, 
    Component: SchemaArrayInput,
  },
  time: {
    ...SCHEMA_CONFIG_DEFINITIONS.time,
    Component: SchemaTime,
  },
  date: {
    ...SCHEMA_CONFIG_DEFINITIONS.date,
    Component: SchemaDate,
  },
};
