import { FormArrayInput } from './components/ArrayInput';
import { FormArrayObject } from './components/ArrayObject';
import { FormCheckbox } from './components/Checkbox';
import { FormCode } from './components/Code';
import { FormComboboxSelectDataPoint } from './components/ComboboxSelectDataPoint';
import { FormDate } from './components/Date';
import { FormDateTime } from './components/DateTime';
import { SchemaDisplayName } from './components/DisplayName';
import { FormDynamicSelectOption } from './components/DynamicSelectOption';
import { FormGroup } from './components/Group';
import { FormKeyValueInput } from './components/KeyValueInput';
import { FormMultipleSelectOption } from './components/MultipleSelectOption';
import { FormObjectInput } from './components/ObjectInput';
import { FormPasswordInput } from './components/PasswordInput';
import { FormPathCondition } from './components/PathCondition';
import { FormRadio } from './components/Radio';
import { FormSelect, FormSelectOption } from './components/SelectOption';
import { FormTextInput } from './components/TextInput';
import { FormTextareaInput } from './components/TextareaInput';
import { FormTime } from './components/Time';
import { FormURLInput } from './components/URLInput';
import type { ComponentConfig } from './type';

// SchemaEngine config
export const SCHEMA_CONFIGS_ENGINE: Record<string, ComponentConfig<any>> = {
  dateTime: {
    id: 'dateTime',
    types: ['datetime', 'date-time'],
    Component: FormDateTime,
  },
  textInput: {
    id: 'textInput',
    types: ['text', 'number', 'datetime', 'string', 'integer'],
    Component: FormTextInput,
  },
  passwordInput: {
    id: 'passwordInput',
    types: ['password', 'text-masked'],
    Component: FormPasswordInput,
  },
  checkbox: {
    id: 'checkbox',
    types: ['boolean', 'checkbox'],
    Component: FormCheckbox,
  },
  displayName: {
    id: 'displayName',
    types: ['displayName'],
    Component: SchemaDisplayName,
  },
  selectOption: {
    id: 'selectOption',
    types: ['options'],
    Component: FormSelectOption,
  },
  enhancedSelectOption: {
    id: 'dynamicSelectOption',
    types: ['dynamicOptions'],
    Component: FormDynamicSelectOption,
  },
  multipleSelectOption: {
    id: 'multipleSelectOption',
    types: ['multiOptions'],
    Component: FormMultipleSelectOption,
  },
  select: {
    id: 'select',
    types: ['select'],
    Component: FormSelect,
  },
  radio: {
    id: 'radio',
    types: ['radio'],
    Component: FormRadio,
  },
  keyValueInput: {
    id: 'keyValueInput',
    types: ['keyvalue'],
    Component: FormKeyValueInput,
  },
  arrayInput: {
    id: 'arrayObject',
    types: ['arrayObject'],
    Component: FormArrayObject,
  },
  pathCondition: {
    id: 'condition',
    types: ['condition'],
    Component: FormPathCondition,
  },
  textarea: {
    id: 'textarea',
    types: ['textarea'],
    Component: FormTextareaInput,
  },
  code: {
    id: 'code',
    types: ['code'],
    Component: FormCode,
  },
  textArray: {
    id: 'textArray',
    types: ['array'],
    Component: FormArrayInput,
  },
  objectInput: {
    id: 'objectInput',
    types: ['object'],
    Component: FormObjectInput,
  },
  comboboxSelectDataPoint: {
    id: 'comboboxSelectDataPoint',
    types: ['comboboxDataPoint'],
    Component: FormComboboxSelectDataPoint,
  },
  group: {
    id: 'group',
    types: ['group'],
    Component: FormGroup,
  },
  urlInput: {
    id: 'urlInput',
    types: ['url'],
    Component: FormURLInput,
  },
  time: {
    id: 'time',
    types: ['time'],
    Component: FormTime,
  },
  date: {
    id: 'date',
    types: ['date'],
    Component: FormDate,
  },
};
