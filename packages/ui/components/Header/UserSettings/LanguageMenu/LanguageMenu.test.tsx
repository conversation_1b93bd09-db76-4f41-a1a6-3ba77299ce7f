import { renderWithMantine } from '@/utils/unitTest';
import { Menu } from '@mantine/core';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import type React from 'react';
import { MemoryRouter } from 'react-router-dom';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import LanguageMenu from './index';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: vi.fn(),
}));

// Mock @tolgee/react
vi.mock('@tolgee/react', () => ({
  useTolgee: vi.fn(),
  TolgeeProvider: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock react-router-dom
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: vi.fn(),
  };
});

// Mock @resola-ai/shared-constants
vi.mock('@resola-ai/shared-constants', () => ({
  PREFERENCE_LANGUAGE: 'PREFERENCE_LANGUAGE',
  DEFAULT_LANGUAGE: 'en',
}));

// Mock withTolgee HOC
vi.mock('@/components/hoc/withTolgee', () => ({
  withTolgee: (Component: React.ComponentType) => Component,
}));

// Mock tolgee module
vi.mock('@/tolgee', () => ({
  tolgee: {
    changeLanguage: vi.fn(),
    getLanguage: vi.fn().mockReturnValue('en'),
  },
}));

// Mock window.location
const mockLocation = {
  href: 'https://example.com/test?param=value',
  pathname: '/test',
  search: '?param=value',
};

Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true,
});

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock URL constructor
const mockSearchParams = {
  set: vi.fn(),
  toString: vi.fn().mockReturnValue('param=value&lang=en'),
};

const mockURL = vi.fn().mockImplementation((url) => ({
  href: url,
  pathname: '/test',
  search: '?param=value',
  searchParams: mockSearchParams,
}));

// @ts-ignore - Mocking global URL constructor
global.URL = mockURL as any;

/**
 * Custom render function with router wrapper for testing navigation
 */
const renderWithRouter = (ui: React.ReactElement) => {
  return renderWithMantine(
    <MemoryRouter>
      <Menu opened>{ui}</Menu>
    </MemoryRouter>
  );
};

describe('LanguageMenu Component', () => {
  // Store mock functions for access throughout tests
  let mockT: ReturnType<typeof vi.fn>;
  let mockI18n: { changeLanguage: ReturnType<typeof vi.fn> };
  let mockTolgee: {
    changeLanguage: ReturnType<typeof vi.fn>;
    getLanguage: ReturnType<typeof vi.fn>;
  };
  let mockNavigate: ReturnType<typeof vi.fn>;
  let mockUseTranslation: ReturnType<typeof vi.fn>;
  let mockUseTolgee: ReturnType<typeof vi.fn>;
  let mockUseNavigate: ReturnType<typeof vi.fn>;

  beforeEach(async () => {
    // Reset all mocks before each test
    vi.clearAllMocks();

    // Reset URL mock
    mockSearchParams.set.mockClear();
    mockSearchParams.toString.mockReturnValue('param=value&lang=en');

    // Mock translation function
    mockT = vi.fn((key: string) => {
      const translations: Record<string, string> = {
        language: 'Language',
        languageTooltip:
          'To change language across all apps, go to Account Settings to update your account preferences.',
      };
      return translations[key] || key;
    });

    // Mock i18n object
    mockI18n = {
      changeLanguage: vi.fn(),
    };

    // Mock tolgee object
    mockTolgee = {
      changeLanguage: vi.fn(),
      getLanguage: vi.fn().mockReturnValue('en'),
    };

    // Mock navigate function
    mockNavigate = vi.fn();

    // Mock useTranslation hook
    mockUseTranslation = vi.fn().mockReturnValue({
      t: mockT,
      i18n: mockI18n,
    });

    // Mock useTolgee hook
    mockUseTolgee = vi.fn().mockReturnValue(mockTolgee);

    // Mock useNavigate hook
    mockUseNavigate = vi.fn().mockReturnValue(mockNavigate);

    // Apply mocks
    const i18nModule = await import('react-i18next');
    const tolgeeModule = await import('@tolgee/react');
    const routerModule = await import('react-router-dom');

    vi.mocked(i18nModule.useTranslation).mockImplementation(mockUseTranslation);
    vi.mocked(tolgeeModule.useTolgee).mockImplementation(mockUseTolgee);
    vi.mocked(routerModule.useNavigate).mockImplementation(mockUseNavigate);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render language menu with correct structure', () => {
      renderWithRouter(<LanguageMenu />);

      // Check if language label is rendered
      expect(screen.getByText('Language')).toBeInTheDocument();

      // Check if tooltip icon is rendered
      expect(screen.getByTestId('help-icon')).toBeInTheDocument();

      // Check if both language options are rendered
      expect(screen.getByText('Japanese')).toBeInTheDocument();
      expect(screen.getByText('English')).toBeInTheDocument();

      // Check if language icons are rendered
      expect(screen.getByAltText('Japanese')).toBeInTheDocument();
      expect(screen.getByAltText('English')).toBeInTheDocument();
    });

    it('should display current language with checkmark', () => {
      // Set current language to Japanese
      mockTolgee.getLanguage.mockReturnValue('ja');

      renderWithRouter(<LanguageMenu />);

      // Check if checkmark is displayed for Japanese (current language)
      const japaneseItem = screen.getByText('Japanese').closest('[role="menuitem"]');
      expect(japaneseItem).toContainElement(screen.getByTestId('checkmark-icon'));

      // Check if checkmark is not displayed for English
      const englishItem = screen.getByText('English').closest('[role="menuitem"]');
      expect(englishItem).not.toContainElement(screen.getByTestId('checkmark-icon'));
    });

    it('should display current language as English with checkmark', () => {
      // Set current language to English
      mockTolgee.getLanguage.mockReturnValue('en');

      renderWithRouter(<LanguageMenu />);

      // Check if checkmark is displayed for English (current language)
      const englishItem = screen.getByText('English').closest('[role="menuitem"]');
      expect(englishItem).toContainElement(screen.getByTestId('checkmark-icon'));

      // Check if checkmark is not displayed for Japanese
      const japaneseItem = screen.getByText('Japanese').closest('[role="menuitem"]');
      expect(japaneseItem).not.toContainElement(screen.getByTestId('checkmark-icon'));
    });
  });

  describe('Language Switching', () => {
    it('should call changeLanguage with correct parameters when Japanese is clicked', async () => {
      // Update the mock to return the expected URL for this test
      mockSearchParams.toString.mockReturnValue('param=value&lang=ja');

      renderWithRouter(<LanguageMenu />);

      const japaneseItem = screen.getByText('Japanese').closest('[role="menuitem"]');
      expect(japaneseItem).toBeInTheDocument();

      fireEvent.click(japaneseItem!);

      await waitFor(() => {
        expect(mockI18n.changeLanguage).toHaveBeenCalledWith('ja');
        expect(mockTolgee.changeLanguage).toHaveBeenCalledWith('ja');
        expect(localStorageMock.setItem).toHaveBeenCalledWith('PREFERENCE_LANGUAGE', 'ja');
        expect(mockNavigate).toHaveBeenCalledWith('/test?param=value&lang=ja');
      });
    });

    it('should call changeLanguage with correct parameters when English is clicked', async () => {
      renderWithRouter(<LanguageMenu />);

      const englishItem = screen.getByText('English').closest('[role="menuitem"]');
      expect(englishItem).toBeInTheDocument();

      fireEvent.click(englishItem!);

      await waitFor(() => {
        expect(mockI18n.changeLanguage).toHaveBeenCalledWith('en');
        expect(mockTolgee.changeLanguage).toHaveBeenCalledWith('en');
        expect(localStorageMock.setItem).toHaveBeenCalledWith('PREFERENCE_LANGUAGE', 'en');
        expect(mockNavigate).toHaveBeenCalledWith('/test?param=value&lang=en');
      });
    });

    it('should update URL with new language parameter', async () => {
      // Update the mock to return the expected URL for this test
      mockSearchParams.toString.mockReturnValue('param=value&lang=ja');

      renderWithRouter(<LanguageMenu />);

      const japaneseItem = screen.getByText('Japanese').closest('[role="menuitem"]');
      fireEvent.click(japaneseItem!);

      await waitFor(() => {
        expect(mockSearchParams.set).toHaveBeenCalledWith('lang', 'ja');
        expect(mockNavigate).toHaveBeenCalledWith('/test?param=value&lang=ja');
      });
    });
  });

  describe('Tooltip', () => {
    it('should display tooltip with correct text', () => {
      renderWithRouter(<LanguageMenu />);

      const tooltipTrigger = screen.getByTestId('help-icon');
      expect(tooltipTrigger).toBeInTheDocument();

      // Hover over the tooltip trigger to show tooltip
      fireEvent.mouseOver(tooltipTrigger);

      // Check if tooltip is rendered (the actual tooltip content may not be visible in test environment)
      expect(tooltipTrigger).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA roles and labels', () => {
      renderWithRouter(<LanguageMenu />);

      // Check if menu items have proper role
      const menuItems = screen.getAllByRole('menuitem');
      expect(menuItems).toHaveLength(2);

      // Check if images have proper alt text
      expect(screen.getByAltText('Japanese')).toBeInTheDocument();
      expect(screen.getByAltText('English')).toBeInTheDocument();
    });

    it('should be keyboard accessible', () => {
      renderWithRouter(<LanguageMenu />);

      const japaneseItem = screen.getByText('Japanese').closest('[role="menuitem"]');
      const englishItem = screen.getByText('English').closest('[role="menuitem"]');

      // Check if items are focusable
      expect(japaneseItem).toBeInTheDocument();
      expect(englishItem).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should handle missing translation gracefully', () => {
      // Mock translation to return the key when translation is missing
      mockT.mockImplementation((key: string) => key);

      renderWithRouter(<LanguageMenu />);

      // Should still render the component even with missing translations
      expect(screen.getByText('language')).toBeInTheDocument();
      // The help icon should still be rendered
      expect(screen.getByTestId('help-icon')).toBeInTheDocument();
    });

    it('should handle tolgee getLanguage returning null', () => {
      mockTolgee.getLanguage.mockReturnValue(null);

      renderWithRouter(<LanguageMenu />);

      // Should render without checkmarks when no current language
      const checkmarks = screen.queryAllByRole('img', { name: /circle check filled/i });
      expect(checkmarks).toHaveLength(0);
    });
  });
});
