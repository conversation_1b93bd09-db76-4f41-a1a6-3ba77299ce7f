import { Flex, Image, Menu, Tooltip, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { PREFERENCE_LANGUAGE } from '@resola-ai/shared-constants';
import { IconCircleCheckFilled, IconHelp, IconLanguageHiragana } from '@tabler/icons-react';
import { useTolgee } from '@tolgee/react';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { withTolgee } from '../../../hoc/withTolgee';
import enFlag from './en.png';
import jaFlag from './ja.png';

const languages = [
  {
    code: 'ja',
    name: 'Japanese',
    image: jaFlag,
  },
  {
    code: 'en',
    name: 'English',
    image: enFlag,
  },
];

const useStyles = createStyles((theme) => ({
  itemIcon: {
    width: rem(20),
    height: rem(20),
  },
  checkedIcon: {
    color: theme.colors.decaGreen[6],
    width: rem(20),
    height: rem(20),
  },
}));

const LanguageMenu = () => {
  const { t, i18n } = useTranslation('header');
  const { classes } = useStyles();
  const tolgee = useTolgee();
  const navigate = useNavigate();

  const changeLanguage = (lang: string) => {
    i18n.changeLanguage(lang);
    tolgee.changeLanguage(lang);
    localStorage.setItem(PREFERENCE_LANGUAGE, lang);
    // update the language in the url
    const url = new URL(window.location.href);
    url.searchParams.set('lang', lang);
    navigate(`${url.pathname}?${url.searchParams.toString()}`);
  };

  return (
    <>
      <Menu.Label>
        <Flex justify='space-between' align='center'>
          <Flex align='center' gap={8}>
            <IconLanguageHiragana className={classes.itemIcon} />
            {t('language')}
          </Flex>
          <Tooltip multiline withArrow label={t('languageTooltip')} sx={{ maxWidth: '300px' }}>
            <IconHelp className={classes.itemIcon} data-testid='help-icon' />
          </Tooltip>
        </Flex>
      </Menu.Label>
      <Menu.Divider m={0} />
      {languages.map((language) => (
        <React.Fragment key={language.code}>
          <Menu.Item onClick={() => changeLanguage(language.code)}>
            <Flex
              justify='space-between'
              align='center'
              gap={rem(24)}
              sx={{ paddingLeft: rem(28), minWidth: rem(180) }}
            >
              <Flex align='center' gap={rem(8)}>
                <Image src={language.image} alt={language.name} width={rem(20)} height={rem(16)} />
                {language.name}
              </Flex>
              {tolgee.getLanguage() === language.code && (
                <IconCircleCheckFilled
                  className={classes.checkedIcon}
                  data-testid='checkmark-icon'
                />
              )}
            </Flex>
          </Menu.Item>
          <Menu.Divider m={0} />
        </React.Fragment>
      ))}
    </>
  );
};

export default withTolgee(LanguageMenu);
