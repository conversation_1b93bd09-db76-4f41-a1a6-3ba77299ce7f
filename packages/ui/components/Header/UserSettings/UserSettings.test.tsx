import { renderWithMantine } from '@/utils/unitTest';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import type React from 'react';
import { MemoryRouter } from 'react-router-dom';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import UserSettings from './index';

// Mock Auth0
vi.mock('@auth0/auth0-react');

// Mock react-i18next
vi.mock('react-i18next');

// Mock @resola-ai/utils
vi.mock('@resola-ai/utils');

// Mock @tolgee/react
vi.mock('@tolgee/react', () => ({
  useTolgee: vi.fn(),
  TolgeeProvider: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock withTolgee HOC
vi.mock('@/components/hoc/withTolgee', () => ({
  withTolgee: (Component: React.ComponentType) => Component,
}));

// Mock tolgee module
vi.mock('@/tolgee', () => ({
  tolgee: {
    changeLanguage: vi.fn(),
    getLanguage: vi.fn().mockReturnValue('en'),
  },
}));

// Test constants
const MOCK_USER_WITH_PICTURE = {
  picture: 'https://example.com/avatar.jpg',
  name: 'John Doe',
  email: '<EMAIL>',
};

const MOCK_USER_WITHOUT_PICTURE = {
  name: 'Jane Doe',
  email: '<EMAIL>',
};

const EXPECTED_REDIRECT_URI = 'https://example.com/login';

/**
 * Custom render function with router wrapper for testing Link components
 */
const renderWithRouter = (ui: React.ReactElement) => {
  return renderWithMantine(<MemoryRouter>{ui}</MemoryRouter>);
};

describe('UserSettings Component', () => {
  // Store mock functions for access throughout tests
  let mockLogout: ReturnType<typeof vi.fn>;
  let mockT: ReturnType<typeof vi.fn>;
  let mockGetRedirectUri: ReturnType<typeof vi.fn>;
  let mockUseAuth0: ReturnType<typeof vi.fn>;
  let mockUseTranslation: ReturnType<typeof vi.fn>;
  let mockUseTolgee: ReturnType<typeof vi.fn>;

  // Mock functions for window objects
  const mockPushState = vi.fn();
  const mockReplaceState = vi.fn();
  const mockAddEventListener = vi.fn();

  beforeEach(async () => {
    // Mock ResizeObserver for Mantine components
    global.ResizeObserver = vi.fn().mockImplementation(() => ({
      observe: vi.fn(),
      unobserve: vi.fn(),
      disconnect: vi.fn(),
    }));

    // Mock window.matchMedia for Mantine components
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(), // deprecated
        removeListener: vi.fn(), // deprecated
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      })),
    });

    // Create mock functions
    mockLogout = vi.fn();
    mockT = vi.fn();
    mockGetRedirectUri = vi.fn();
    mockUseAuth0 = vi.fn();
    mockUseTranslation = vi.fn();
    mockUseTolgee = vi.fn();

    // Setup mocks
    const auth0Module = await import('@auth0/auth0-react');
    const i18nModule = await import('react-i18next');
    const utilsModule = await import('@resola-ai/utils');
    const tolgeeModule = await import('@tolgee/react');

    vi.mocked(auth0Module.useAuth0).mockImplementation(mockUseAuth0);
    vi.mocked(i18nModule.useTranslation).mockImplementation(mockUseTranslation);
    vi.mocked(utilsModule.getRedirectUri).mockImplementation(mockGetRedirectUri);
    vi.mocked(tolgeeModule.useTolgee).mockImplementation(mockUseTolgee);

    // Setup default return values
    mockUseAuth0.mockReturnValue({
      user: MOCK_USER_WITH_PICTURE,
      logout: mockLogout,
    });

    mockT.mockImplementation((key: string) => {
      const translations: { [key: string]: string } = {
        accountSettings: 'Account Settings',
        logout: 'Logout',
        language: 'Language',
        languageTooltip: 'Select your preferred language',
      };
      return translations[key] || key;
    });

    mockUseTranslation.mockReturnValue({
      t: mockT,
    });

    mockUseTolgee.mockReturnValue({
      changeLanguage: vi.fn(),
      getLanguage: vi.fn().mockReturnValue('en'),
    });

    mockGetRedirectUri.mockReturnValue(EXPECTED_REDIRECT_URI);

    // Mock window.history methods
    Object.defineProperty(window, 'history', {
      value: {
        pushState: mockPushState,
        replaceState: mockReplaceState,
      },
      writable: true,
    });

    // Mock window.addEventListener
    Object.defineProperty(window, 'addEventListener', {
      value: mockAddEventListener,
      writable: true,
    });

    // Mock window.location.pathname
    Object.defineProperty(window, 'location', {
      value: {
        pathname: '/current-path',
        href: '',
      },
      writable: true,
    });

    // Clear all mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Component Rendering', () => {
    it('should render user avatar with picture when user has picture', () => {
      renderWithRouter(<UserSettings />);

      const avatar = screen.getByRole('img', { name: 'avatar' });
      expect(avatar).toBeInTheDocument();
      expect(avatar).toHaveAttribute('src', MOCK_USER_WITH_PICTURE.picture);
    });

    it('should render user avatar without picture when user has no picture', () => {
      mockUseAuth0.mockReturnValue({
        user: MOCK_USER_WITHOUT_PICTURE,
        logout: mockLogout,
      });

      renderWithRouter(<UserSettings />);

      // When no picture, Mantine Avatar shows a placeholder span
      const avatarContainer = screen.getByRole('generic', { name: 'avatar' });
      expect(avatarContainer).toBeInTheDocument();
      expect(avatarContainer).toHaveAttribute('title', 'avatar');
    });

    it('should render dropdown menu when avatar is clicked', async () => {
      renderWithRouter(<UserSettings />);

      // Find the clickable avatar target (div with ARIA attributes)
      const avatarTarget = screen
        .getByRole('img', { name: 'avatar' })
        .closest('div[aria-haspopup="menu"]');
      expect(avatarTarget).toBeInTheDocument();

      fireEvent.click(avatarTarget!);

      await waitFor(() => {
        expect(screen.getByText('Account Settings')).toBeInTheDocument();
        expect(screen.getByText('Logout')).toBeInTheDocument();
      });
    });

    it('should render account settings link with correct attributes', async () => {
      renderWithRouter(<UserSettings />);

      const avatarTarget = screen
        .getByRole('img', { name: 'avatar' })
        .closest('div[aria-haspopup="menu"]');
      fireEvent.click(avatarTarget!);

      await waitFor(() => {
        const accountLink = screen.getByRole('menuitem', { name: /account settings/i });
        expect(accountLink).toBeInTheDocument();
        expect(accountLink.closest('a')).toHaveAttribute('href', '/account');
        expect(accountLink.closest('a')).toHaveAttribute('target', '_blank');
        expect(accountLink.closest('a')).toHaveAttribute('rel', 'noreferrer');
      });
    });
  });

  describe('Translation Integration', () => {
    it('should call translation function with correct keys', () => {
      renderWithRouter(<UserSettings />);

      expect(mockT).toHaveBeenCalledWith('accountSettings');
      expect(mockT).toHaveBeenCalledWith('logout');
    });

    it('should display translated text in menu items', async () => {
      renderWithRouter(<UserSettings />);

      const avatarTarget = screen
        .getByRole('img', { name: 'avatar' })
        .closest('div[aria-haspopup="menu"]');
      fireEvent.click(avatarTarget!);

      await waitFor(() => {
        expect(screen.getByText('Account Settings')).toBeInTheDocument();
        expect(screen.getByText('Logout')).toBeInTheDocument();
      });
    });
  });

  describe('Logout Functionality', () => {
    it('should clear localStorage and sessionStorage when logout is called', async () => {
      const mockLocalStorageClear = vi.fn();
      const mockSessionStorageClear = vi.fn();

      Object.defineProperty(window, 'localStorage', {
        value: { clear: mockLocalStorageClear },
        writable: true,
      });

      Object.defineProperty(window, 'sessionStorage', {
        value: { clear: mockSessionStorageClear },
        writable: true,
      });

      renderWithRouter(<UserSettings />);

      const avatarTarget = screen
        .getByRole('img', { name: 'avatar' })
        .closest('div[aria-haspopup="menu"]');
      fireEvent.click(avatarTarget!);

      await waitFor(() => {
        const logoutButton = screen.getByText('Logout');
        fireEvent.click(logoutButton);
      });

      expect(mockLocalStorageClear).toHaveBeenCalled();
      expect(mockSessionStorageClear).toHaveBeenCalled();
    });

    it('should manipulate browser history to prevent back navigation', async () => {
      renderWithRouter(<UserSettings />);

      const avatarTarget = screen
        .getByRole('img', { name: 'avatar' })
        .closest('div[aria-haspopup="menu"]');
      fireEvent.click(avatarTarget!);

      await waitFor(() => {
        const logoutButton = screen.getByText('Logout');
        fireEvent.click(logoutButton);
      });

      expect(mockReplaceState).toHaveBeenCalledWith(null, '', '/current-path');
      expect(mockPushState).toHaveBeenCalledWith(null, '', '/current-path');
    });

    it('should setup navigation guard with popstate event listener', async () => {
      renderWithRouter(<UserSettings />);

      const avatarTarget = screen
        .getByRole('img', { name: 'avatar' })
        .closest('div[aria-haspopup="menu"]');
      fireEvent.click(avatarTarget!);

      await waitFor(() => {
        const logoutButton = screen.getByText('Logout');
        fireEvent.click(logoutButton);
      });

      expect(mockAddEventListener).toHaveBeenCalledWith('popstate', expect.any(Function));
    });

    it('should call Auth0 logout with correct parameters', async () => {
      renderWithRouter(<UserSettings />);

      const avatarTarget = screen
        .getByRole('img', { name: 'avatar' })
        .closest('div[aria-haspopup="menu"]');
      fireEvent.click(avatarTarget!);

      await waitFor(() => {
        const logoutButton = screen.getByText('Logout');
        fireEvent.click(logoutButton);
      });

      await waitFor(() => {
        expect(mockLogout).toHaveBeenCalledWith({
          logoutParams: {
            returnTo: EXPECTED_REDIRECT_URI,
            federated: true,
          },
        });
      });
    });

    it('should call getRedirectUri to get return URL', async () => {
      renderWithRouter(<UserSettings />);

      const avatarTarget = screen
        .getByRole('img', { name: 'avatar' })
        .closest('div[aria-haspopup="menu"]');
      fireEvent.click(avatarTarget!);

      await waitFor(() => {
        const logoutButton = screen.getByText('Logout');
        fireEvent.click(logoutButton);
      });

      expect(mockGetRedirectUri).toHaveBeenCalled();
    });
  });

  describe('PopState Event Handler', () => {
    it('should redirect to login when popstate event is triggered', async () => {
      const mockAssign = vi.fn();
      Object.defineProperty(window, 'location', {
        value: {
          pathname: '/current-path',
          href: '',
          assign: mockAssign,
        },
        writable: true,
      });

      renderWithRouter(<UserSettings />);

      const avatarTarget = screen
        .getByRole('img', { name: 'avatar' })
        .closest('div[aria-haspopup="menu"]');
      fireEvent.click(avatarTarget!);

      await waitFor(() => {
        const logoutButton = screen.getByText('Logout');
        fireEvent.click(logoutButton);
      });

      // Get the popstate handler that was registered
      const popstateHandler = mockAddEventListener.mock.calls.find(
        (call) => call[0] === 'popstate'
      )?.[1];

      expect(popstateHandler).toBeDefined();

      // Create a mock popstate event
      const mockEvent = {
        preventDefault: vi.fn(),
      };

      if (popstateHandler) {
        popstateHandler(mockEvent);
        expect(mockEvent.preventDefault).toHaveBeenCalled();
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle logout errors gracefully', async () => {
      const mockLogoutError = vi.fn().mockRejectedValue(new Error('Logout failed'));
      const mockAssign = vi.fn();

      // Mock console.error to verify error logging
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Mock window.location.href assignment
      Object.defineProperty(window, 'location', {
        value: {
          pathname: '/current-path',
          href: '',
          assign: mockAssign,
        },
        writable: true,
      });

      mockUseAuth0.mockReturnValue({
        user: MOCK_USER_WITH_PICTURE,
        logout: mockLogoutError,
      });

      renderWithRouter(<UserSettings />);

      const avatarTarget = screen
        .getByRole('img', { name: 'avatar' })
        .closest('div[aria-haspopup="menu"]');
      fireEvent.click(avatarTarget!);

      // Find and click the logout button
      let logoutButton: HTMLElement;
      await waitFor(() => {
        logoutButton = screen.getByText('Logout');
        expect(logoutButton).toBeInTheDocument();
      });

      fireEvent.click(logoutButton!);

      // Wait for the error handling to complete
      await waitFor(() => {
        expect(mockLogoutError).toHaveBeenCalled();
        expect(consoleSpy).toHaveBeenCalledWith('Logout failed:', expect.any(Error));
      });

      consoleSpy.mockRestore();
    });

    it('should handle missing user gracefully', () => {
      mockUseAuth0.mockReturnValue({
        user: null,
        logout: mockLogout,
      });

      renderWithRouter(<UserSettings />);

      // When no user, avatar should show placeholder
      const avatarContainer = screen.getByRole('generic', { name: 'avatar' });
      expect(avatarContainer).toBeInTheDocument();
      expect(avatarContainer).toHaveAttribute('title', 'avatar');
    });
  });

  describe('Menu Interactions', () => {
    it('should close menu after clicking account settings link', async () => {
      renderWithRouter(<UserSettings />);

      const avatarTarget = screen
        .getByRole('img', { name: 'avatar' })
        .closest('div[aria-haspopup="menu"]');
      fireEvent.click(avatarTarget!);

      await waitFor(() => {
        const accountLink = screen.getByRole('menuitem', { name: /account settings/i });
        expect(accountLink).toBeInTheDocument();
      });

      // Menu should be visible
      expect(screen.getByText('Account Settings')).toBeInTheDocument();
    });

    it('should show menu divider between account settings and logout', async () => {
      renderWithRouter(<UserSettings />);

      const avatarTarget = screen
        .getByRole('img', { name: 'avatar' })
        .closest('div[aria-haspopup="menu"]');
      fireEvent.click(avatarTarget!);

      await waitFor(() => {
        // Check that both menu items are present (divider is between them)
        expect(screen.getByText('Account Settings')).toBeInTheDocument();
        expect(screen.getByText('Logout')).toBeInTheDocument();
      });
    });
  });

  describe('Component Styling', () => {
    it('should apply correct CSS classes', () => {
      const { container } = renderWithRouter(<UserSettings />);

      // Check that the component has the expected structure
      expect(container.querySelector('.mantine-Group-root')).toBeInTheDocument();
    });

    it('should have clickable avatar with hover cursor', () => {
      renderWithRouter(<UserSettings />);

      const avatar = screen.getByRole('img', { name: 'avatar' });
      expect(avatar).toBeInTheDocument();

      // Avatar should be clickable (part of Menu.Target which renders as div with ARIA attributes)
      const avatarTarget = avatar.closest('div[aria-haspopup="menu"]');
      expect(avatarTarget).toBeInTheDocument();
      expect(avatarTarget).toHaveAttribute('aria-haspopup', 'menu');
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes for menu', async () => {
      renderWithRouter(<UserSettings />);

      const avatarTarget = screen
        .getByRole('img', { name: 'avatar' })
        .closest('div[aria-haspopup="menu"]');
      fireEvent.click(avatarTarget!);

      await waitFor(() => {
        const menuItems = screen.getAllByRole('menuitem');
        expect(menuItems).toHaveLength(4); // Account Settings and Logout
      });
    });

    it('should have proper alt text for avatar', () => {
      renderWithRouter(<UserSettings />);

      const avatar = screen.getByRole('img', { name: 'avatar' });
      expect(avatar).toHaveAttribute('alt', 'avatar');
    });
  });

  describe('Memoization', () => {
    it('should memoize component to prevent unnecessary re-renders', () => {
      const { rerender } = renderWithRouter(<UserSettings />);

      // Component should render successfully
      expect(screen.getByRole('img', { name: 'avatar' })).toBeInTheDocument();

      // Re-render with same props should not cause issues
      rerender(
        <MemoryRouter>
          <UserSettings />
        </MemoryRouter>
      );
      expect(screen.getByRole('img', { name: 'avatar' })).toBeInTheDocument();
    });
  });
});
