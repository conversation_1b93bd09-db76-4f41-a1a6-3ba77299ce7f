import { useAuth0 } from '@auth0/auth0-react';
import { Avatar, Group, Menu, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { getRedirectUri } from '@resola-ai/utils';
import { IconLogout, IconSettings2 } from '@tabler/icons-react';
import { memo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { defaultThemeColors } from '../../../constants';
import LanguageMenu from './LanguageMenu';

const useStyles = createStyles(() => ({
  container: {
    height: '100%',
    ['& .mantine-Menu-dropdown']: {
      minWidth: rem(194),
      padding: 0,
    },
  },
  avatar: {
    borderRadius: rem(32),
    ['&:hover']: {
      cursor: 'pointer',
    },
  },
  itemGroup: {
    marginTop: rem(14.5),
    color: defaultThemeColors.decaGrey[8],
    ['& .mantine-Menu-item']: {
      padding: `${rem(10)} ${rem(12)}`,
      fontSize: rem(14),
    },
    ['& .mantine-Menu-label']: {
      padding: `${rem(10)} ${rem(12)}`,
      fontSize: rem(14),
    },
  },
  itemLink: {
    textDecoration: 'none',
  },
  itemIcon: {
    width: rem(20),
    height: rem(20),
  },
  logout: {
    color: defaultThemeColors.decaRed[5],
  },
}));

const UserSettings: React.FC = () => {
  const { user, logout } = useAuth0();
  const { classes } = useStyles();
  const { t } = useTranslation('header');

  /**
   * Clears all cached authentication data from browser storage
   */
  const clearAuthenticationCache = useCallback(() => {
    localStorage.clear();
    sessionStorage.clear();
  }, []);

  /**
   * Manipulates browser history to prevent back navigation to authenticated state
   */
  const preventBackNavigation = useCallback(() => {
    // Replace current history entry to prevent back navigation to authenticated state
    window.history.replaceState(null, '', window.location.pathname);

    // Clear the history stack by pushing a new state
    window.history.pushState(null, '', window.location.pathname);
  }, []);

  /**
   * Sets up a listener to prevent back navigation after logout
   * Redirects users to login if they try to navigate back
   */
  const setupNavigationGuard = useCallback(() => {
    const handlePopState = (event: PopStateEvent) => {
      event.preventDefault();
      // Force redirect to login if user tries to go back
      window.location.href = getRedirectUri();
    };

    window.addEventListener('popstate', handlePopState);
  }, []);

  /**
   * Performs Auth0 logout with federated logout enabled
   */
  const performAuth0Logout = useCallback(async () => {
    await logout({
      logoutParams: {
        returnTo: getRedirectUri(),
        federated: true, // This ensures complete logout from Auth0
      },
    });
  }, [logout]);

  /**
   * Main logout function that orchestrates the complete logout process
   */
  const onLogout = useCallback(async () => {
    try {
      // Step 1: Clear any cached authentication data
      clearAuthenticationCache();

      // Step 2: Prevent back navigation to authenticated state
      preventBackNavigation();

      // Step 3: Setup navigation guard to catch back button attempts
      setupNavigationGuard();

      // Step 4: Perform Auth0 logout with federated logout enabled
      await performAuth0Logout();
    } catch (error) {
      // If logout fails, still clear local data and redirect to login
      console.error('Logout failed:', error);
      window.location.href = getRedirectUri();
    }
  }, [clearAuthenticationCache, preventBackNavigation, setupNavigationGuard, performAuth0Logout]);

  return (
    <Group className={classes.container}>
      <Menu shadow='md' position='bottom-end'>
        <Menu.Target>
          <Avatar className={classes.avatar} src={user?.picture} alt='avatar' />
        </Menu.Target>
        <Menu.Dropdown className={classes.itemGroup}>
          <Link to='/account' className={classes.itemLink} target='_blank' rel='noreferrer'>
            <Menu.Item leftSection={<IconSettings2 className={classes.itemIcon} />}>
              {t('accountSettings')}
            </Menu.Item>
          </Link>
          <Menu.Divider m={0} />
          <LanguageMenu />
          <Menu.Item
            className={classes.logout}
            leftSection={<IconLogout className={classes.itemIcon} />}
            onClick={onLogout}
          >
            {t('logout')}
          </Menu.Item>
        </Menu.Dropdown>
      </Menu>
    </Group>
  );
};

export default memo(UserSettings);
