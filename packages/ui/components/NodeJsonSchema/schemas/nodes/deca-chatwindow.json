{"name": "deca-chatwindow", "displayName": "Chat Window", "category": ["deca-cloud-tools"], "description": "Interact with Chat Window to manage chat windows, messages, and more.", "schemas": {"common": {"properties": {"id": {"name": "id", "displayName": "ID", "type": "string", "description": "Unique identifier"}, "name": {"name": "name", "displayName": "Name", "type": "string", "description": "Name"}, "description": {"name": "description", "displayName": "Description", "type": "string", "description": "Description"}, "orgId": {"name": "orgId", "displayName": "Organization ID", "type": "string", "description": "Unique identifier for the organization"}, "workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "Unique identifier for the workspace"}, "objectId": {"name": "objectId", "displayName": "Object ID", "type": "string", "description": "ID of the object the item belongs to"}, "active": {"name": "active", "displayName": "Active", "type": "boolean", "description": "Whether the item is active"}}}, "timestamps": {"properties": {"createdAt": {"name": "createdAt", "displayName": "Created At", "type": "string", "description": "Timestamp when the item was created"}, "updatedAt": {"name": "updatedAt", "displayName": "Updated At", "type": "string", "description": "Timestamp when the item was last updated"}, "deletedAt": {"name": "deletedAt", "displayName": "Deleted At", "type": "string", "description": "Timestamp when the item was deleted"}}}, "settings": {"properties": {"id": {"name": "id", "displayName": "ID", "type": "string", "description": "Unique identifier"}, "title": {"name": "title", "displayName": "Title", "type": "string", "description": "Title"}, "desc": {"name": "desc", "displayName": "Description", "type": "string", "description": "Description"}, "configUrl": {"name": "configUrl", "displayName": "Config URL", "type": "string", "description": "Config URL"}, "config": {"name": "config", "displayName": "Config", "type": "object", "description": "Config"}, "status": {"name": "status", "displayName": "Status", "type": "string", "description": "Status"}, "botConnected": {"name": "botConnected", "displayName": "Bot Connected", "type": "string", "description": "Whether the bot is connected"}}}}, "triggers": {"deca.chatbox.setting.created": {"name": "deca.chatbox.setting.created", "displayName": "Chatbox Setting Created", "description": "When a chatbox setting is created", "data": {"$ref": "#/schemas/settings"}}, "deca.chatbox.setting.updated": {"name": "deca.chatbox.setting.updated", "displayName": "Chatbox Setting Updated", "description": "When a chatbox setting is updated", "data": {"$ref": "#/schemas/settings"}}, "deca.chatbox.setting.deleted": {"name": "deca.chatbox.setting.deleted", "displayName": "Chatbox Setting Deleted", "description": "When a chatbox setting is deleted", "data": {"$ref": "#/schemas/settings"}}, "deca.chatbox.connected.bot": {"name": "deca.chatbox.connected.bot", "displayName": "Chatbox Connected a Chatbot", "description": "When a chatbox is connected to a chatbot", "data": {}}, "deca.chatbox.integration.created": {"name": "deca.chatbox.integration.created", "displayName": "Chatbox Integration Created", "description": "When a chatbox integration is created", "data": {}}, "deca.chatbox.integration.disabled": {"name": "deca.chatbox.integration.disabled", "displayName": "Chatbox Integration Disabled", "description": "When a chatbox integration is disabled", "data": {}}}, "actions": {"setting_list": {"name": "setting_list", "displayName": "List Chatwindows", "description": "Get a list of chatwindows", "properties": {"limit": {"name": "limit", "displayName": "Limit", "type": "number", "description": "Limit the number of chatwindows to return", "required": true, "min": 1, "order": 1, "default": 20}, "next": {"name": "next", "displayName": "Next", "type": "string", "description": "Next cursor", "required": false, "order": 2}}, "data": {"$ref": "#/schemas/settings"}}, "setting_all": {"name": "setting_all", "displayName": "List All Chatwindows", "description": "Get a list of all chatwindows", "properties": {}, "data": {"$ref": "#/schemas/settings"}}, "setting_create": {"name": "setting_create", "displayName": "Create a <PERSON><PERSON><PERSON>ow", "description": "Create a chatwindow", "properties": {"title": {"name": "title", "displayName": "Title", "type": "string", "description": "Title", "required": true, "order": 1}, "desc": {"name": "desc", "displayName": "Description", "type": "string", "description": "Description", "required": true, "order": 2}}, "data": {"$ref": "#/schemas/settings"}}, "setting_update": {"name": "setting_update", "displayName": "Update a Chatwindow", "description": "Update a chatwindow", "properties": {"settingId": {"name": "settingId", "displayName": "Chatwindow ID", "type": "string", "description": "ID of the setting to update the chatwindow from", "required": true, "order": 1}, "title": {"name": "title", "displayName": "Title", "type": "string", "description": "Title", "required": true, "order": 2}, "desc": {"name": "desc", "displayName": "Description", "type": "string", "description": "Description", "required": true, "order": 3}}, "data": {"$ref": "#/schemas/settings"}}, "setting_delete": {"name": "setting_delete", "displayName": "Delete a Chatwindow", "description": "Delete a chatwindow", "properties": {"settingId": {"name": "settingId", "displayName": "Chatwindow ID", "type": "string", "description": "ID of the setting to delete the chatwindow from", "required": true}}, "data": {"$ref": "#/schemas/settings"}}, "setting_retrieve": {"name": "setting_retrieve", "displayName": "Retrieve a Chatwindow", "description": "Retrieve a chatwindow", "properties": {"settingId": {"name": "settingId", "displayName": "Chatwindow ID", "type": "string", "description": "ID of the setting to retrieve the chatwindow from", "required": true}}, "data": {"$ref": "#/schemas/settings"}}, "setting_connect_bot": {"name": "setting_connect_bot", "displayName": "Connect a Chatbot to a Chatwindow", "description": "Connect a chatbot to a chatwindow", "properties": {"settingId": {"name": "settingId", "displayName": "Chatwindow ID", "type": "string", "description": "ID of the chatwindow to connect the chatbot to", "required": true, "order": 1}, "newBotId": {"name": "newBotId", "displayName": "The Bot ID to connect", "type": "string", "description": "ID of the chatbot to connect to the chatwindow", "required": true, "order": 2}, "botId": {"name": "botId", "displayName": "Existing Bot ID", "type": "string", "description": "ID of the chatbot to connect existing bot to the chatwindow. If the first time connect, you can leave it blank.", "required": false, "order": 3}}, "data": {"$ref": "#/schemas/settings"}}}}