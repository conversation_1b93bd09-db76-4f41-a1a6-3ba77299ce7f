{"name": "gmail", "type": "gmail", "displayName": "Gmail", "icon": "📧", "group": "app", "category": ["builtin-tools", "communication"], "description": "Interact with Gmail messages and labels", "settings": {"credential": {"name": "credential", "displayName": "Credential", "description": "The credential to use for authentication", "type": "credential", "credentialTypes": ["oauth2"], "required": true, "order": 1}}, "schemas": {"gmailMessage": {"properties": {"id": {"name": "id", "displayName": "Message ID", "description": "The unique identifier for the message", "type": "string", "order": 1}, "threadId": {"name": "threadId", "displayName": "Thread ID", "description": "The ID of the thread the message belongs to", "type": "string", "order": 2}, "labelIds": {"name": "labelIds", "displayName": "Label IDs", "description": "List of IDs of labels applied to this message", "type": "array", "items": {"type": "string"}, "order": 3}, "snippet": {"name": "snippet", "displayName": "Snippet", "description": "A short part of the message text", "type": "string", "order": 4}, "historyId": {"name": "historyId", "displayName": "History ID", "description": "The ID of the last history record that modified this message", "type": "string", "order": 5}, "internalDate": {"name": "internalDate", "displayName": "Internal Date", "description": "The internal message creation timestamp", "type": "string", "order": 6}, "payload": {"name": "payload", "displayName": "Payload", "description": "The parsed email structure in the message parts", "type": "object", "order": 7}, "sizeEstimate": {"name": "sizeEstimate", "displayName": "Size Estimate", "description": "Estimated size in bytes of the message", "type": "integer", "order": 8}}}, "gmailLabel": {"properties": {"id": {"name": "id", "displayName": "Label ID", "description": "The immutable ID of the label", "type": "string", "order": 1}, "name": {"name": "name", "displayName": "Name", "description": "The display name of the label", "type": "string", "order": 2}, "messageListVisibility": {"name": "messageListVisibility", "displayName": "Message List Visibility", "description": "The visibility of the label in the message list", "type": "string", "enum": ["show", "hide"], "order": 3}, "labelListVisibility": {"name": "labelListVisibility", "displayName": "Label List Visibility", "description": "The visibility of the label in the label list", "type": "string", "enum": ["labelShow", "labelHide"], "order": 4}, "type": {"name": "type", "displayName": "Type", "description": "The owner type for the label", "type": "string", "enum": ["system", "user"], "order": 5}}}}, "credentials": {"oauth2": {"name": "oauth2", "displayName": "OAuth 2.0", "description": "OAuth 2.0 authentication for Gmail API", "order": 1, "properties": {"clientID": {"name": "clientID", "displayName": "Client ID", "description": "The client ID from your Google OAuth app", "type": "text-masked", "required": true, "order": 1}, "clientSecret": {"name": "clientSecret", "displayName": "Client Secret", "description": "The client secret from your Google OAuth app", "type": "text-masked", "required": true, "order": 2}, "scopes": {"name": "scopes", "displayName": "<PERSON><PERSON><PERSON>", "description": "The scopes for the Gmail API, default: https://www.googleapis.com/auth/gmail.readonly, https://www.googleapis.com/auth/gmail.send, https://www.googleapis.com/auth/gmail.modify", "type": "text", "required": true, "order": 3}, "callbackURL": {"name": "callbackURL", "displayName": "Callback URL", "description": "Use this callback URL for Google OAuth2 authentication", "type": "text", "required": true, "order": 6, "default": "https://api.studio.deca-dev.com/oauth/callback"}}}}, "actions": {"sendMessage": {"name": "sendMessage", "displayName": "Send Message", "description": "Send an email message", "order": 1, "properties": {"message": {"name": "message", "displayName": "Message", "description": "The message to send", "type": "object", "properties": {"to": {"name": "to", "displayName": "To", "description": "Recipient email address", "type": "text", "required": true, "order": 1}, "subject": {"name": "subject", "displayName": "Subject", "description": "Email subject", "type": "text", "required": true, "order": 2}, "body": {"name": "body", "displayName": "Body", "description": "Email body content", "type": "text", "required": true, "order": 3}, "cc": {"name": "cc", "displayName": "CC", "description": "CC recipients (comma-separated)", "type": "text", "required": false, "order": 4}, "bcc": {"name": "bcc", "displayName": "BCC", "description": "BCC recipients (comma-separated)", "type": "text", "required": false, "order": 5}}}}, "data": {"$ref": "#/schemas/gmailMessage"}}}}