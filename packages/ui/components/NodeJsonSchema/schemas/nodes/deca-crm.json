{"name": "deca-crm", "displayName": "CRM", "category": ["deca-cloud-tools"], "description": "Interact with CRM to manage workspaces, objects, records, and more.", "schemas": {"common": {"properties": {"id": {"name": "id", "displayName": "ID", "type": "string", "description": "Unique identifier"}, "name": {"name": "name", "displayName": "Name", "type": "string", "description": "Name"}, "description": {"name": "description", "displayName": "Description", "type": "string", "description": "Description"}, "orgId": {"name": "orgId", "displayName": "Organization ID", "type": "string", "description": "Unique identifier for the organization"}, "workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "Unique identifier for the workspace"}, "objectId": {"name": "objectId", "displayName": "Object ID", "type": "string", "description": "ID of the object the item belongs to"}, "active": {"name": "active", "displayName": "Active", "type": "boolean", "description": "Whether the item is active"}}}, "timestamps": {"properties": {"createdAt": {"name": "createdAt", "displayName": "Created At", "type": "string", "description": "Timestamp when the item was created"}, "updatedAt": {"name": "updatedAt", "displayName": "Updated At", "type": "string", "description": "Timestamp when the item was last updated"}, "deletedAt": {"name": "deletedAt", "displayName": "Deleted At", "type": "string", "description": "Timestamp when the item was deleted"}}}, "user": {"properties": {"id": {"name": "id", "displayName": "ID", "type": "string", "description": "Unique identifier for the user"}, "email": {"name": "email", "displayName": "Email", "type": "string", "description": "Email of the user"}, "name": {"name": "name", "displayName": "Name", "type": "string", "description": "Name of the user"}, "picture": {"name": "picture", "displayName": "Picture", "type": "string", "description": "URL to the user's profile picture"}}}, "workspace": {"properties": {"id": {"name": "id", "displayName": "ID", "type": "string", "description": "Unique identifier for the workspace"}, "orgId": {"name": "orgId", "displayName": "Organization ID", "type": "string"}, "name": {"name": "name", "displayName": "Name", "type": "string", "description": "Name of the workspace"}, "description": {"name": "description", "displayName": "Description", "type": "string", "description": "Description of the workspace"}, "active": {"name": "active", "displayName": "Active", "type": "boolean", "description": "Whether the workspace is active"}, "createdAt": {"name": "createdAt", "displayName": "Created At", "type": "string", "description": "Timestamp when the workspace was created"}, "updatedAt": {"name": "updatedAt", "displayName": "Updated At", "type": "string", "description": "Timestamp when the workspace was last updated"}, "deletedAt": {"name": "deletedAt", "displayName": "Deleted At", "type": "string", "description": "Timestamp when the workspace was deleted, null if not deleted"}}}, "object": {"properties": {"id": {"name": "id", "displayName": "ID", "type": "string", "description": "Unique identifier for the object"}, "icon": {"name": "icon", "displayName": "Icon", "type": "string", "description": "Icon identifier for the object"}, "hasAvatar": {"name": "has<PERSON><PERSON><PERSON>", "displayName": "Has <PERSON>", "type": "boolean", "description": "Whether the object has an avatar"}, "orgId": {"name": "orgId", "displayName": "Organization ID", "type": "string", "description": "ID of the organization this object belongs to"}, "workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "ID of the workspace this object belongs to"}, "name": {"name": "name", "displayName": "Name", "type": "object", "description": "Object name in singular and plural forms", "properties": {"singular": {"name": "singular", "displayName": "Singular", "type": "string", "description": "Singular form of the object name"}, "plural": {"name": "plural", "displayName": "Plural", "type": "string", "description": "Plural form of the object name"}}}, "description": {"name": "description", "displayName": "Description", "type": "string", "description": "Description of the object"}, "displaySettings": {"name": "displaySettings", "displayName": "Display Settings", "type": "object", "description": "Visual display configuration for the object"}, "messaging": {"name": "messaging", "displayName": "Messaging", "type": "object", "description": "Messaging configuration options", "properties": {"sms": {"name": "sms", "displayName": "SMS", "type": "boolean"}, "email": {"name": "email", "displayName": "Email", "type": "boolean"}, "line": {"name": "line", "displayName": "Line", "type": "boolean"}}}, "profileSettings": {"name": "profileSettings", "displayName": "Profile Settings", "type": "array", "description": "Profile settings for the object", "items": {"type": "object", "properties": {"type": {"name": "type", "displayName": "Type", "type": "string", "description": "Type of profile setting"}, "enabled": {"name": "enabled", "displayName": "Enabled", "type": "boolean", "description": "Whether the profile setting is enabled"}}}}, "childObjects": {"name": "childObjects", "displayName": "Child Objects", "type": "array", "description": "List of child object identifiers"}, "fields": {"name": "fields", "displayName": "Fields", "type": "array", "description": "List of field definitions for the object"}, "views": {"name": "views", "displayName": "Views", "type": "array", "description": "List of view configurations for the object"}, "viewGroups": {"name": "viewGroups", "displayName": "View Groups", "type": "array", "description": "List of view group configurations for the object"}, "references": {"name": "references", "displayName": "References", "type": "object", "description": "Map of fields to references from other objects"}, "createdAt": {"name": "createdAt", "displayName": "Created At", "type": "string", "description": "Timestamp when the object was created"}, "updatedAt": {"name": "updatedAt", "displayName": "Updated At", "type": "string", "description": "Timestamp when the object was last updated"}}}, "objectMetadata": {"properties": {"name": {"name": "name", "displayName": "Name", "type": "object", "description": "Name of the object", "required": true, "properties": {"singular": {"name": "singular", "displayName": "Singular Name", "type": "string", "description": "Singular form of the object name", "required": true}, "plural": {"name": "plural", "displayName": "Plural Name", "type": "string", "description": "Plural form of the object name", "required": true}}}, "icon": {"name": "icon", "displayName": "Icon", "type": "string", "description": "Icon identifier for the object", "required": false}, "hasAvatar": {"name": "has<PERSON><PERSON><PERSON>", "displayName": "Has <PERSON>", "type": "boolean", "description": "Whether the object has an avatar", "required": false}, "description": {"name": "description", "displayName": "Description", "type": "string", "description": "Description of the object", "required": false}, "displaySettings": {"name": "displaySettings", "displayName": "Display Settings", "type": "object", "description": "Display settings for the object", "required": false}, "profileSettings": {"name": "profileSettings", "displayName": "Profile Settings", "type": "array", "description": "Profile settings for the object", "required": false, "items": {"type": "object", "properties": {"type": {"name": "type", "displayName": "Type", "type": "string", "description": "Type of the profile setting", "required": true}, "enabled": {"name": "enabled", "displayName": "Enabled", "type": "boolean", "description": "Whether the profile setting is enabled", "required": true}}}}, "messaging": {"name": "messaging", "displayName": "Messaging", "type": "object", "description": "Messaging settings for the object", "required": false, "properties": {"sms": {"name": "sms", "displayName": "SMS", "type": "boolean", "description": "Whether the SMS is enabled", "required": true}, "email": {"name": "email", "displayName": "Email", "type": "boolean", "description": "Whether the email is enabled", "required": true}, "line": {"name": "line", "displayName": "Line", "type": "boolean", "description": "Whether the line is enabled", "required": true}}}, "fields": {"name": "fields", "displayName": "Fields", "type": "array", "description": "Fields for the object", "required": false, "items": {"type": "object", "$ref": "#/definitions/field"}}}}, "field": {"properties": {"id": {"name": "id", "displayName": "ID", "type": "string", "description": "Unique identifier for the field"}, "type": {"name": "type", "displayName": "Type", "type": "string", "description": "Type of the field (e.g., text, number, currency, etc.)"}, "name": {"name": "name", "displayName": "Name", "type": "string", "description": "Display name of the field"}, "isProtected": {"name": "isProtected", "displayName": "Is Protected", "type": "boolean", "description": "Whether the field is protected from modifications"}, "required": {"name": "required", "displayName": "Required", "type": "boolean", "description": "Whether the field is required"}, "uniqued": {"name": "uniqued", "displayName": "Uniqued", "type": "boolean", "description": "Whether the field is uniqued"}, "description": {"name": "description", "displayName": "Description", "type": "string", "description": "Description of the field's purpose"}, "options": {"name": "options", "displayName": "Options", "type": "object", "description": "Configuration options specific to the field type"}}}, "view": {"properties": {"id": {"name": "id", "displayName": "ID", "type": "string", "description": "Unique identifier for the view"}, "name": {"name": "name", "displayName": "Name", "type": "string", "description": "Name of the view"}, "orgId": {"name": "orgId", "displayName": "Organization ID", "type": "string", "description": "ID of the organization this view belongs to"}, "objectId": {"name": "objectId", "displayName": "Object ID", "type": "string", "description": "ID of the object the view belongs to"}, "type": {"name": "type", "displayName": "Type", "type": "string", "description": "Type of view (e.g., grid, kanban, etc.)"}, "icon": {"name": "icon", "displayName": "Icon", "type": "string", "description": "Icon identifier for the view"}, "rowHeight": {"name": "rowHeight", "displayName": "Row Height", "type": "string", "description": "Height of rows in the view"}, "description": {"name": "description", "displayName": "Description", "type": "string", "description": "Description of the view"}, "createdBy": {"name": "created<PERSON>y", "displayName": "Created By", "type": "string", "description": "ID of the user who created the view"}, "fields": {"name": "fields", "displayName": "Fields", "type": "array", "description": "List of field configurations in the view"}, "fieldOrder": {"name": "fieldOrder", "displayName": "Field Order", "type": "array", "description": "Ordered list of field IDs defining their display order"}, "detailFieldOrder": {"name": "detailFieldOrder", "displayName": "Detail Field Order", "type": "array", "description": "Ordered list of field IDs for the detail view"}, "filters": {"name": "filters", "displayName": "Filters", "type": "object", "description": "Filter conditions applied to the view"}, "textSearch": {"name": "textSearch", "displayName": "Text Search", "type": "string", "description": "Text search query applied to the view"}, "sort": {"name": "sort", "displayName": "Sort", "type": "array", "description": "Sort configurations for the view"}, "createdAt": {"name": "createdAt", "displayName": "Created At", "type": "string", "description": "Timestamp when the view was created"}, "updatedAt": {"name": "updatedAt", "displayName": "Updated At", "type": "string", "description": "Timestamp when the view was last updated"}}}, "record": {"properties": {"id": {"name": "id", "displayName": "ID", "type": "string", "description": "Unique identifier for the record"}, "autoId": {"name": "autoId", "displayName": "Auto ID", "type": "number", "description": "Automatically generated sequential ID for the record"}, "createdBy": {"name": "created<PERSON>y", "displayName": "Created By", "type": "object", "description": "User who created the record", "properties": {"id": {"name": "id", "displayName": "ID", "type": "string", "description": "Unique identifier for the user"}, "email": {"name": "email", "displayName": "Email", "type": "string", "description": "Email of the user"}, "name": {"name": "name", "displayName": "Name", "type": "string", "description": "Name of the user"}, "picture": {"name": "picture", "displayName": "Picture", "type": "string", "description": "URL to the user's profile picture"}}}, "updatedBy": {"name": "updatedBy", "displayName": "Updated By", "type": "object", "description": "User who last updated the record", "properties": {"id": {"name": "id", "displayName": "ID", "type": "string", "description": "Unique identifier for the user"}, "email": {"name": "email", "displayName": "Email", "type": "string", "description": "Email of the user"}, "name": {"name": "name", "displayName": "Name", "type": "string", "description": "Name of the user"}, "picture": {"name": "picture", "displayName": "Picture", "type": "string", "description": "URL to the user's profile picture"}}}, "createdAt": {"name": "createdAt", "displayName": "Created At", "type": "string", "description": "Timestamp when the record was created"}, "updatedAt": {"name": "updatedAt", "displayName": "Updated At", "type": "string", "description": "Timestamp when the record was last updated"}}}, "event": {"properties": {"orgId": {"name": "orgId", "displayName": "Organization ID", "type": "string", "description": "ID of the organization"}, "workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "ID of the workspace"}, "objectId": {"name": "objectId", "displayName": "Object ID", "type": "string", "description": "ID of the object"}, "recordId": {"name": "recordId", "displayName": "Record ID", "type": "string", "description": "ID of the record"}, "changes": {"newValue": {"$ref": "#/schemas/record"}, "oldValue": {"$ref": "#/schemas/record"}}, "user": {"$ref": "#/schemas/user"}, "sentAt": {"name": "sentAt", "displayName": "<PERSON><PERSON>", "type": "string", "description": "Timestamp when the event was sent"}}}}, "triggers": {"deca.crm.record.created": {"name": "deca.crm.record.created", "displayName": "Record Created", "description": "When a record is created", "data": {"$ref": "#/schemas/event"}}, "deca.crm.record.updated": {"name": "deca.crm.record.updated", "displayName": "Record Updated", "description": "When a record is updated", "data": {"$ref": "#/schemas/event"}}, "deca.crm.record.deleted": {"name": "deca.crm.record.deleted", "displayName": "Record Deleted", "description": "When a record is deleted", "data": {"$ref": "#/schemas/event"}}}, "actions": {"workspace_list": {"name": "workspace_list", "displayName": "List Workspaces", "description": "Get a list of all workspaces", "properties": {}, "data": {"$ref": "#/schemas/workspace"}}, "workspace_retrieve": {"name": "workspace_retrieve", "displayName": "Get Workspace", "description": "Get a workspace by ID", "properties": {"workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "ID of the workspace to retrieve", "required": true}}, "data": {"$ref": "#/schemas/workspace"}}, "workspace_create": {"name": "workspace_create", "displayName": "Create Workspace", "description": "Create a new workspace", "properties": {"data": {"name": "data", "displayName": "Data", "type": "object", "description": "Data of the workspace", "required": true, "properties": {"name": {"name": "name", "displayName": "Name", "type": "string", "description": "Name of the workspace", "required": true}, "description": {"name": "description", "displayName": "Description", "type": "string", "description": "Description of the workspace", "required": false}}}}, "data": {"$ref": "#/schemas/workspace"}}, "workspace_update": {"name": "workspace_update", "displayName": "Update Workspace", "description": "Update an existing workspace", "properties": {"workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "ID of the workspace to update", "required": true, "order": 1}, "data": {"name": "data", "displayName": "Data", "type": "object", "description": "Data of the workspace", "required": true, "order": 2, "properties": {"name": {"name": "name", "displayName": "Name", "type": "string", "description": "Name of the workspace", "required": true}, "description": {"name": "description", "displayName": "Description", "type": "string", "description": "Description of the workspace", "required": false}}}}, "data": {"$ref": "#/schemas/workspace"}}, "object_list": {"name": "object_list", "displayName": "List Objects", "description": "Get a list of all objects in a workspace", "properties": {"workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "ID of the workspace to list objects from", "required": true}}, "data": {"$ref": "#/schemas/object"}}, "object_retrieve": {"name": "object_retrieve", "displayName": "Get Object", "description": "Get an object by ID", "properties": {"workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "ID of the workspace to list objects from", "required": true, "order": 1}, "objectId": {"name": "objectId", "displayName": "Object ID", "type": "string", "description": "ID of the object to retrieve", "required": true, "order": 2}}, "data": {"$ref": "#/schemas/object"}}, "object_create": {"name": "object_create", "displayName": "Create Object", "description": "Create a new object in a workspace", "properties": {"workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "ID of the workspace to create the object in", "required": true, "order": 1}, "data": {"name": "data", "displayName": "Data", "type": "object", "description": "Data of the object", "required": true, "order": 2}}, "data": {"$ref": "#/schemas/object"}}, "object_update": {"name": "object_update", "displayName": "Update Object", "description": "Update an existing object", "properties": {"workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "ID of the workspace to list objects from", "required": true, "order": 1}, "objectId": {"name": "objectId", "displayName": "Object ID", "type": "string", "description": "ID of the object to update", "required": true, "order": 2}, "data": {"name": "data", "displayName": "Data", "type": "object", "description": "Data of the object", "required": true, "order": 3}}, "data": {"$ref": "#/schemas/object"}}, "object_delete": {"name": "object_delete", "displayName": "Delete Object", "description": "Delete an object", "properties": {"workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "ID of the workspace to list objects from", "required": true, "order": 1}, "objectId": {"name": "objectId", "displayName": "Object ID", "type": "string", "description": "ID of the object to delete", "required": true, "order": 2}}, "data": {}}, "view_list": {"name": "view_list", "displayName": "List Views", "description": "Get a list of all views of an object in a workspace", "properties": {"workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "ID of the workspace to list objects from", "required": true, "order": 1}, "objectId": {"name": "objectId", "displayName": "Object ID", "type": "string", "description": "ID of the object to list views from", "required": true, "order": 2}}, "data": {"$ref": "#/schemas/view"}}, "view_retrieve": {"name": "view_retrieve", "displayName": "Get View", "description": "Get a view by ID", "properties": {"workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "ID of the workspace", "required": true, "order": 1}, "objectId": {"name": "objectId", "displayName": "Object ID", "type": "string", "description": "ID of the object containing the view", "required": true, "order": 2}, "viewId": {"name": "viewId", "displayName": "View ID", "type": "string", "description": "ID of the view to retrieve", "required": true, "order": 3}}, "data": {"$ref": "#/schemas/view"}}, "view_create": {"name": "view_create", "displayName": "Create View", "description": "Create a new view", "properties": {"workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "ID of the workspace", "required": true, "order": 1}, "objectId": {"name": "objectId", "displayName": "Object ID", "type": "string", "description": "ID of the object containing the view", "required": true, "order": 2}, "data": {"name": "data", "displayName": "Data", "type": "object", "description": "View creation data", "required": true, "order": 3}}, "data": {"$ref": "#/schemas/view"}}, "view_delete": {"name": "view_delete", "displayName": "Delete View", "description": "Delete a view", "properties": {"workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "ID of the workspace", "required": true, "order": 1}, "objectId": {"name": "objectId", "displayName": "Object ID", "type": "string", "description": "ID of the object containing the view", "required": true, "order": 2}, "viewId": {"name": "viewId", "displayName": "View ID", "type": "string", "description": "ID of the view to retrieve", "required": true, "order": 3}}, "data": {}}, "field_create": {"name": "field_create", "displayName": "Create Field", "description": "Create a new field", "properties": {"workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "ID of the workspace", "required": true, "order": 1}, "objectId": {"name": "objectId", "displayName": "Object ID", "type": "string", "description": "ID of the object containing the view", "required": true, "order": 2}, "data": {"name": "data", "displayName": "Data", "type": "object", "description": "Field definition data", "required": true, "order": 3}}, "data": {"$ref": "#/schemas/field"}}, "field_update": {"name": "field_update", "displayName": "Update Field", "description": "Update an existing field", "properties": {"workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "ID of the workspace", "required": true, "order": 1}, "objectId": {"name": "objectId", "displayName": "Object ID", "type": "string", "description": "ID of the object containing the view", "required": true, "order": 2}, "fieldId": {"name": "fieldId", "displayName": "Field ID", "type": "string", "description": "ID of the field to update", "required": true, "order": 3}, "data": {"name": "data", "displayName": "Data", "type": "object", "description": "Field update data", "required": true, "order": 4}}, "data": {"$ref": "#/schemas/field"}}, "field_delete": {"name": "field_delete", "displayName": "Delete Field", "description": "Delete a field", "properties": {"workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "ID of the workspace", "required": true, "order": 1}, "objectId": {"name": "objectId", "displayName": "Object ID", "type": "string", "description": "ID of the object", "required": true, "order": 2}, "fieldId": {"name": "fieldId", "displayName": "Field ID", "type": "string", "description": "ID of the field to delete", "required": true, "order": 3}}, "data": {}}, "record_list": {"name": "record_list", "displayName": "List Records", "description": "Get a list of all records of an object in a workspace", "oneRequired": ["objectId", "viewId"], "properties": {"workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "ID of the workspace the record belongs to", "required": true, "order": 1}, "objectId": {"name": "objectId", "displayName": "Object ID", "type": "string", "description": "ID of the object to list records from. Either objectId or viewId must be provided. Use this when you need filtering and sorting capabilities.", "required": false, "oneRequired": true, "order": 2}, "viewId": {"name": "viewId", "displayName": "View ID", "type": "string", "description": "ID of the view to list records from. Either objectId or viewId must be provided. Note: filters and sort parameters are ignored when using viewId.", "required": false, "oneRequired": true, "order": 3}, "filters": {"name": "filters", "displayName": "Filters", "type": "object", "description": "Filter conditions to apply to the record list. Only supported when using objectId (ignored when using viewId).", "required": false, "order": 4}, "limit": {"name": "limit", "displayName": "Limit", "type": "number", "description": "Maximum number of records to return", "required": false, "order": 5}, "offset": {"name": "offset", "displayName": "Offset", "type": "number", "description": "Number of records to skip before returning results", "required": false, "order": 6}, "sort": {"name": "sort", "displayName": "Sort", "type": "array", "description": "Sort configurations for the record list. Only supported when using objectId (ignored when using viewId).", "required": false, "items": {"type": "object"}, "order": 7}}, "data": {"$ref": "#/schemas/record"}}, "record_retrieve": {"name": "record_retrieve", "displayName": "Get Record", "description": "Get a record by ID", "properties": {"workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "ID of the workspace", "required": true, "order": 1}, "objectId": {"name": "objectId", "displayName": "Object ID", "type": "string", "description": "ID of the object", "required": true, "order": 2}, "recordId": {"name": "recordId", "displayName": "Record ID", "type": "string", "description": "ID of the record", "required": true, "order": 3}}, "data": {"$ref": "#/schemas/record"}}, "record_create": {"name": "record_create", "displayName": "Create Record", "description": "Create a new record", "properties": {"workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "ID of the workspace", "required": true, "order": 1}, "objectId": {"name": "objectId", "displayName": "Object ID", "type": "string", "description": "ID of the object", "required": true, "order": 2}, "data": {"name": "data", "displayName": "Data", "type": "object", "description": "Record data with field values", "required": true, "order": 3}}, "data": {"$ref": "#/schemas/record"}}, "record_update": {"name": "record_update", "displayName": "Update Record", "description": "Update an existing record", "properties": {"workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "ID of the workspace", "required": true, "order": 1}, "objectId": {"name": "objectId", "displayName": "Object ID", "type": "string", "description": "ID of the object", "required": true, "order": 2}, "recordId": {"name": "recordId", "displayName": "Record ID", "type": "string", "description": "ID of the record", "required": true, "order": 3}, "data": {"name": "data", "displayName": "Data", "type": "object", "description": "Record data with field values to update", "required": true, "order": 4}}, "data": {"$ref": "#/schemas/record"}}, "record_delete": {"name": "record_delete", "displayName": "Delete Record", "description": "Delete a record", "properties": {"workspaceId": {"name": "workspaceId", "displayName": "Workspace ID", "type": "string", "description": "ID of the workspace", "required": true, "order": 1}, "objectId": {"name": "objectId", "displayName": "Object ID", "type": "string", "description": "ID of the object", "required": true, "order": 2}, "recordId": {"name": "recordId", "displayName": "Record ID", "type": "string", "description": "ID of the record", "required": true, "order": 3}}, "data": {}}}}