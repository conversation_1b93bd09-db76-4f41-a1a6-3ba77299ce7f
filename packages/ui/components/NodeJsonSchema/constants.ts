import nodeSchemas from './schemas/node-schemas.json';

import code from './schemas/nodes/code.json';
import decaAiWidgets from './schemas/nodes/deca-ai-widgets.json';
import decaChatbot from './schemas/nodes/deca-chatbot.json';
import decaChatwindow from './schemas/nodes/deca-chatwindow.json';
import decaCrm from './schemas/nodes/deca-crm.json';
import decaForms from './schemas/nodes/deca-forms.json';
import decaKb from './schemas/nodes/deca-kb.json';
import decaLivechat from './schemas/nodes/deca-livechat.json';
import decaMa from './schemas/nodes/deca-ma.json';
import decaPages from './schemas/nodes/deca-pages.json';
import decaTables from './schemas/nodes/deca-tables.json';
import filter from './schemas/nodes/filter.json';
import firecrawl from './schemas/nodes/firecrawl.json';
import formatter from './schemas/nodes/formatter.json';
import functions from './schemas/nodes/function.json';
import gmail from './schemas/nodes/gmail.json';
import http from './schemas/nodes/http.json';
import line from './schemas/nodes/line.json';
import looping from './schemas/nodes/looping.json';
import mail from './schemas/nodes/mail.json';
import manual from './schemas/nodes/manual.json';
import newTrigger from './schemas/nodes/new-trigger.json';
import openai from './schemas/nodes/openai.json';
import path from './schemas/nodes/path.json';
import salesforce from './schemas/nodes/salesforce.json';
import schedule from './schemas/nodes/schedule.json';
import virtualStore from './schemas/nodes/virtual-store.json';
import wait from './schemas/nodes/wait.json';
import webhook from './schemas/nodes/webhook.json';
import zendeskTicket from './schemas/nodes/zendesk.json';
import zoomMeetings from './schemas/nodes/zoom-meetings.json';

// Get fallback nodes
const fallbackNodes: any[] = [
  code,
  decaAiWidgets,
  decaChatbot,
  decaCrm,
  decaKb,
  decaLivechat,
  decaTables,
  decaForms,
  decaMa,
  filter,
  formatter,
  functions,
  http,
  looping,
  newTrigger,
  openai,
  decaPages,
  path,
  schedule,
  wait,
  zoomMeetings,
  webhook,
  manual,
  virtualStore,
  salesforce,
  zendeskTicket,
  mail,
  line,
  decaChatwindow,
  firecrawl,
  gmail,
];

const getAllNodes = () => {
  for (const node of fallbackNodes) {
    if (!nodeSchemas[node.name]) {
      nodeSchemas[node.name] = node;
    }
  }

  return nodeSchemas;
};

const allNodeSchemas = getAllNodes();

export { nodeSchemas, fallbackNodes, allNodeSchemas };
