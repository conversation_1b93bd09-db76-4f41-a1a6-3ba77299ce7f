import { DEFAULT_LANGUAGE } from '@resola-ai/shared-constants';
import { cloneDeep } from 'lodash';
import { useCallback, useEffect, useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import type { INode } from '../../../models/collection/node';
import { allNodeSchemas } from './constants';
import { translateDeep } from './helper';

const GLOBAL_NODE_SCHEMAS_KEY = 'globalNodeSchemas';
// key: lang
const cache = new Map<string, any>();

const useTranslatedNodeSchemas = () => {
  const [searchParams] = useSearchParams();
  const lang = searchParams.get('lang') ?? DEFAULT_LANGUAGE;

  const translatedNodeSchemas: Record<string, INode> = useMemo(() => {
    const hit = cache.get(lang);
    if (hit) return hit;
    // in-place, one-time per lang
    const draft = cloneDeep(allNodeSchemas);
    translateDeep(draft, lang);
    cache.set(lang, draft);
    return draft;
  }, [lang]);

  return translatedNodeSchemas;
};

const useNodeSchemas = () => {
  const nodeSchemas = useTranslatedNodeSchemas();

  const getAllNodes = useCallback((): INode[] => {
    if (!nodeSchemas) return [];
    return Object.values(nodeSchemas);
  }, [nodeSchemas]);

  const getNodeSchema = useCallback(
    (nodeName: string) => {
      if (!nodeSchemas) return null;
      return nodeSchemas[nodeName];
    },
    [nodeSchemas]
  );

  const getCredentialNodes = useCallback(() => {
    const nodes = getAllNodes();
    return nodes.filter((node) => node.credentials && Object.keys(node.credentials).length > 0);
  }, [getAllNodes]);

  const getTriggerNodes = useCallback(() => {
    const nodes = getAllNodes();
    return nodes
      .filter(
        (node) =>
          // biome-ignore lint/suspicious/noPrototypeBuiltins: <explanation>
          node?.hasOwnProperty('triggers') &&
          typeof node.triggers === 'object' &&
          Object.keys(node.triggers).length > 0
      )
      .map((node) => node.name);
  }, [getAllNodes]);

  const getActionNodes = useCallback(() => {
    const nodes = getAllNodes();
    const path = getNodeSchema('path');
    const actionNodes = nodes
      .filter(
        (node) =>
          // biome-ignore lint/suspicious/noPrototypeBuiltins: <explanation>
          node?.hasOwnProperty('actions') &&
          typeof node.actions === 'object' &&
          Object.keys(node.actions).length > 0
      )
      .map((node) => node.name);
    return path ? [path.name, ...actionNodes] : actionNodes;
  }, [getAllNodes, getNodeSchema]);

  const getLoopingNodes = useCallback(() => {
    const loopingNode = getNodeSchema('loop');
    return loopingNode ? [loopingNode.name] : [];
  }, [getNodeSchema]);

  const getControlNodes = useCallback(() => {
    const nodes = getAllNodes();
    return nodes
      .filter((node) => !node?.triggers && !node?.actions && node?.name !== 'path') // the path node has been handled above lines
      .map((node) => node.name);
  }, [getAllNodes]);

  return {
    getAllNodes,
    getNodeSchema,
    getCredentialNodes,
    getTriggerNodes,
    getActionNodes,
    getLoopingNodes,
    getControlNodes,
    nodeSchemas,
  };
};

interface UseNodeSchemasDebugOptions {
  enabled: boolean;
}

const useNodeSchemasDebug = (options: UseNodeSchemasDebugOptions) => {
  const { enabled = false } = options || {};
  const { nodeSchemas } = useNodeSchemas();

  useEffect(() => {
    if (!enabled || !nodeSchemas) return;
    window[GLOBAL_NODE_SCHEMAS_KEY] = {
      schemas: allNodeSchemas,
      translatedSchemas: nodeSchemas,
    };
  }, [enabled, nodeSchemas]);
};

export { useNodeSchemas, useNodeSchemasDebug };
