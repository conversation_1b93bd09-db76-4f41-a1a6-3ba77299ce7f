import { Box, Flex, Text, rem, useMantineTheme } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconFilter } from '@tabler/icons-react';
import clsx from 'clsx';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import isEqual from 'lodash/isEqual';
import { memo, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { type Field, type Option, type RuleGroupType, toFullOption } from 'react-querybuilder';
import { parseMongoDB } from 'react-querybuilder/dist/parseMongoDB';
import {
  CUSTOM_FILTER_OPERATORS,
  formatQueryPreservingGroups,
  getQueryBuilderFieldOptions,
} from '../../../../../constants';
import { uiI18nInstance } from '../../../../../i18n';
import { useTableContext } from '../../../../../providers';
import { DecaButton } from '../../../../DecaButton';
import { QueryBuilder } from '../../../../QueryBuilder';
import { DECA_TABLE_CLASSES, DEFAULT_TABLE_FILTERS, TABLE_TOOLBAR_ITEMS } from '../../../constants';
import { FieldTypes } from '../../../types';
import type { View, ViewColumnFields } from '../../../types';
import { PERMISSION_KEYS, isPermissionAllowed } from '../../../utils';
import { useToolbarContext } from '../ToolbarContext';
import ToolbarItem, { type BaseToolbarItemProps } from '../ToolbarItem';
import { DefaultTableToolbarChangeTypes } from '../types';

export const TableFilterChangeTypes = {
  UPDATE_VIEW: DefaultTableToolbarChangeTypes.UPDATE_VIEW,
  FILTER_VIEW: 'filterView',
} as const;

const defaultFields: ViewColumnFields[] = [];

type TableFilterI18nMessages = {
  buttonText: string;
  filter: string;
  noColumnHasBeenAddedYet: string;
};

export interface TableFilterProps {
  i18nMessages?: Partial<TableFilterI18nMessages>;
  initialFilters?: Record<string, any>;
  onCancel?: () => void;
  unfilterableFieldTypes?: string[];
  // Props to replace useTableContext and useToolbarContext
  currentView?: View;
  onViewChange: (id: string, view: View, type?: string) => Promise<void>;
  tagsFilter?: { label: string; value: string }[];
  setSelectedItem?: (item: string) => void;
  enablePermissions?: boolean;
}

const useStyles = createStyles(() => ({
  queryBuilder: {
    boxShadow: 'none',
    border: 'none',
    padding: 0,
  },
}));

const relativeDateOperator = (field, value, t) => ({
  label: t(CUSTOM_FILTER_OPERATORS.relativeDate),
  customValue: {
    ...value,
  },
  operator: CUSTOM_FILTER_OPERATORS.relativeDate,
  field: field,
  value: '',
});

const TableFilter = (props: TableFilterProps) => {
  const {
    i18nMessages,
    initialFilters: externalInitialFilters,
    onCancel,
    unfilterableFieldTypes = [],
    currentView,
    onViewChange,
    tagsFilter,
    setSelectedItem,
    enablePermissions = true,
  } = props;
  const { t } = useTranslation('table');
  const { classes } = useStyles();
  const initialFilters = externalInitialFilters ?? currentView?.filters ?? DEFAULT_TABLE_FILTERS;
  const [query, setQuery] = useState<RuleGroupType>(initialFilters as RuleGroupType);
  const isDirty = JSON.stringify(query) !== JSON.stringify(initialFilters);
  
  // Permission checks that respect enablePermissions
  const viewPermissions = currentView?.permission || {};
  const canUpdateView = enablePermissions ? isPermissionAllowed(viewPermissions, PERMISSION_KEYS.VIEW_UPDATE) : true;
  const queryBuilderFieldOptions = useMemo(() => {
    // Use UI i18n instance to ensure proper translation of operators
    const uiLanguage = uiI18nInstance.language.includes('ja') ? 'ja' : 'en';
    const uiT = uiI18nInstance.getFixedT(uiLanguage, 'query-builder') as any;
    return getQueryBuilderFieldOptions(uiLanguage, uiT);
  }, []);

  const fields = useMemo(
    () =>
      (currentView?.fields || defaultFields)
        .filter(
          ({ fieldMetaId, isVisible, type }) =>
            !!fieldMetaId && isVisible && type && !unfilterableFieldTypes?.includes(type)
        )
        .concat(
          tagsFilter?.length
            ? [
                {
                  fieldMetaId: 'tags',
                  header: t('tags'),
                  type: FieldTypes.TAG,
                  id: 'tags',
                  isVisible: true,
                  options: {
                    choices: tagsFilter as [],
                    default: '',
                  },
                },
              ]
            : []
        ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [currentView?.fields, tagsFilter, t]
  );

  const fieldsById = useMemo(
    () =>
      fields.reduce<Record<string, ViewColumnFields>>(
        (previous, current) => {
          previous[current.fieldMetaId] = current;
          return previous;
        },
        {}
      ),
    [fields]
  );

  const selectValues = useMemo(
    () =>
      fields.reduce<Record<string, Option[]>>((previous, current) => {
        if (current.type !== 'select' && current.type !== 'multiSelect') return previous;

        const id = current.fieldMetaId;
        const options = fieldsById[id].options!.choices.map(({ id, label }) => ({
          label,
          name: id,
        }));

        previous[id] = (previous[id] || []).concat(options);
        return previous;
      }, {}),
    [fields, fieldsById]
  );

  const queryBuilderFields = useMemo(
    () =>
      fields.map(({ fieldMetaId, header, type, options }) =>
        toFullOption({
          label: header,
          name: fieldMetaId,
          ...(type ? queryBuilderFieldOptions[type] : {}),
          values: fieldMetaId === 'tags' ? options?.choices : selectValues[fieldMetaId],
        })
      ),
    [fields, queryBuilderFieldOptions, selectValues]
  );

  const handleViewChange = useCallback(() => {
    if (!currentView || !isDirty) return;

    const filters = currentView.filters;
    const queryPreservingGroups = formatQueryPreservingGroups(query);
    const formattedQuery =
      queryPreservingGroups === null ? DEFAULT_TABLE_FILTERS : queryPreservingGroups;

    if (isEmpty(filters) && isEqual(formattedQuery, DEFAULT_TABLE_FILTERS)) return;

    onViewChange(
      currentView.id,
      {
        ...currentView,
        filters: isEqual(formattedQuery, DEFAULT_TABLE_FILTERS) ? {} : formattedQuery,
      },
      TableFilterChangeTypes.FILTER_VIEW
    );
  }, [currentView, isDirty, query, onViewChange]);

  if (!fields.length)
    return (
      <Box p={rem(8)}>
        <Text>{i18nMessages?.noColumnHasBeenAddedYet ?? t('noColumnHasBeenAddedYet')}</Text>
      </Box>
    );

  return (
    <Box sx={{ minWidth: rem(500) }}>
      <QueryBuilder
        initialQuery={parseMongoDB(initialFilters, {
          additionalOperators: {
            // custom MongoDB operator for query builder
            $relativeDate: (field, _operator, value) => relativeDateOperator(field, value, t),
          },
        })}
        fields={queryBuilderFields as Field[]}
        onQueryChange={setQuery}
        className={classes.queryBuilder}
        disabled={!canUpdateView}
      />
      <Flex
        align='center'
        bg='decaLight.1'
        justify='flex-end'
        px={rem(16)}
        py={rem(12)}
        gap={rem(16)}>
        <DecaButton
          size='sm'
          variant='neutral'
          onClick={() => {
            setQuery(initialFilters as RuleGroupType);
            setSelectedItem?.('');
            onCancel?.();
          }}>
          {t('cancel')}
        </DecaButton>
        {canUpdateView && (
          <DecaButton
            size='sm'
            onClick={() => {
              handleViewChange();
              setSelectedItem?.('');
              onCancel?.();
            }}>
            {t('apply')}
          </DecaButton>
        )}
      </Flex>
    </Box>
  );
};

// Interface for the main toolbar item - this excludes internal context props
export interface TableFilterToolbarItemProps extends BaseToolbarItemProps {
  i18nMessages?: Partial<TableFilterI18nMessages>;
  initialFilters?: Record<string, any>;
  unfilterableFieldTypes?: string[];
  // These props will be provided by the consumer (Table or Kanban)
  currentView?: View;
  onViewChange: (id: string, view: View, type?: string) => Promise<void>;
  tagsFilter?: { label: string; value: string }[];
  setSelectedItem?: (item: string) => void;
  enablePermissions?: boolean;
}

const TableFilterToolbarItem = (props: TableFilterToolbarItemProps) => {
  const {
    i18nMessages,
    className,
    initialFilters,
    onOpenChange,
    opened,
    unfilterableFieldTypes,
    currentView,
    onViewChange,
    tagsFilter,
    setSelectedItem,
    enablePermissions = true,
    ...rest
  } = props;
  const { t } = useTranslation('table');
  const theme = useMantineTheme();

  const isFiltering = useMemo(() => {
    const filters = currentView?.filters;
    return (
      filters &&
      Object.keys(filters).length > 0 &&
      JSON.stringify(filters) !== JSON.stringify(DEFAULT_TABLE_FILTERS)
    );
  }, [currentView?.filters]);

  const filterConditions: any[] = useMemo(() => {
    if (!isFiltering) return [];
    return get(currentView, 'filters.$and', get(currentView, 'filters.$or', []));
  }, [isFiltering, currentView]);

  return (
    <ToolbarItem
      opened={opened}
      onOpenChange={onOpenChange}
      name={TABLE_TOOLBAR_ITEMS.TABLE_FILTER}
      label={i18nMessages?.buttonText ?? t('filter')}
      leftIcon={<IconFilter size={16} />}
      popoverComponent={
        <TableFilter
          i18nMessages={i18nMessages}
          initialFilters={initialFilters}
          unfilterableFieldTypes={unfilterableFieldTypes}
          onCancel={() => onOpenChange?.(false)}
          currentView={currentView}
          onViewChange={onViewChange}
          tagsFilter={tagsFilter}
          setSelectedItem={setSelectedItem}
          enablePermissions={enablePermissions}
        />
      }
      badge={isFiltering ? filterConditions.length || 1 : undefined}
      className={clsx(DECA_TABLE_CLASSES.TOOLBAR_ITEM_FILTER, className)}
      color={isFiltering ? theme.colors.decaViolet[4] : undefined}
      bgColor={isFiltering ? theme.colors.decaViolet[0] : undefined}
      // Add missing props that ToolbarItem needs
      currentView={currentView}
      setSelectedItem={setSelectedItem}
      enablePermissions={enablePermissions}
      {...rest}
    />
  );
};

// Legacy wrapper that uses TableContext - for use within DecaTable
const TableFilterToolbarItemWithContext = (props: Omit<TableFilterToolbarItemProps, 'currentView' | 'onViewChange' | 'tagsFilter' | 'setSelectedItem'>) => {
  
  const { currentView } = useTableContext();
  const { onViewChange, tagsFilter, setSelectedItem } = useToolbarContext();

  // Wrap onViewChange to return a Promise
  const handleViewChangeAsync = useCallback(async (id: string, view: View, type?: string) => {
    return Promise.resolve(onViewChange(id, view, type));
  }, [onViewChange]);

  return (
    <TableFilterToolbarItem
      {...props}
      currentView={currentView}
      onViewChange={handleViewChangeAsync}
      tagsFilter={tagsFilter}
      setSelectedItem={setSelectedItem}
    />
  );
};

export { TableFilterToolbarItem };
export default memo(TableFilterToolbarItemWithContext);
