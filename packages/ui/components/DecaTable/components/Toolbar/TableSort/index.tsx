import { Box, Flex, rem, useMantineTheme } from '@mantine/core';
import { IconSortAscendingLetters } from '@tabler/icons-react';
import clsx from 'clsx';
import { memo, useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useTableContext } from '../../../../../providers';
import { DecaButton } from '../../../../DecaButton';
import { DecaSwitch } from '../../../../DecaSwitch';
import { DecaTooltip } from '../../../../DecaTooltip';
import SortingFieldSelector from '../../../../SortingFieldSelector';
import type { GetSortOrderOptionsCallback } from '../../../../SortingFieldSelector/components/SortRow';
import { DECA_TABLE_CLASSES, TABLE_TOOLBAR_ITEMS } from '../../../constants';
import type { SortingField, View, ViewColumnFields } from '../../../types';
import { PERMISSION_KEYS, isPermissionAllowed } from '../../../utils';
import { useToolbarContext } from '../ToolbarContext';
import ToolbarItem, { type BaseToolbarItemProps } from '../ToolbarItem';
import { DefaultTableToolbarChangeTypes } from '../types';

export const TableSortChangeTypes = {
  FILTER_VIEW: 'filterView',
  UPDATE_VIEW: DefaultTableToolbarChangeTypes.UPDATE_VIEW,
} as const;

const defaultFields: ViewColumnFields[] = [];

export interface TableSortProps {
  onCancel?: () => void;
  getSortOrderOptions?: GetSortOrderOptionsCallback;
  unsortableFieldTypes?: string[];
  // Props to replace useTableContext and useToolbarContext
  currentView?: View;
  onViewChange: (id: string, view: View, type?: string) => Promise<void>;
  setSelectedItem?: (item: string) => void;
  selectedItem?: string;
  enablePermissions?: boolean;
}

const TableSort = (props: TableSortProps) => {
  const { 
    onCancel, 
    getSortOrderOptions, 
    unsortableFieldTypes = [], 
    currentView, 
    onViewChange, 
    setSelectedItem, 
    selectedItem, 
    enablePermissions = true 
  } = props;
  const { t } = useTranslation('table');
  const fields = currentView?.fields || defaultFields;
  const initialSort = currentView?.sort ?? [];
  const [currentSort, setCurrentSort] = useState(initialSort);
  const autoSorting = currentView?.autoSort;
  
  // Permission checks that respect enablePermissions
  const viewPermissions = currentView?.permission || {};
  const canUpdateView = enablePermissions ? isPermissionAllowed(viewPermissions, PERMISSION_KEYS.VIEW_UPDATE) : true;
  const ref = useRef<HTMLDivElement>(null);
  const selectorFields: SortingField[] = fields
    .filter(({ isVisible, type }) => isVisible && type && !unsortableFieldTypes?.includes(type))
    .map(({ fieldMetaId, header, headerIcon, type }) => ({
      icon: headerIcon,
      label: header,
      value: fieldMetaId,
      type: type!,
    }));

  const _onViewChange = useCallback(() => {
    if (!currentView || (autoSorting && selectedItem === TABLE_TOOLBAR_ITEMS.TABLE_SORT)) return;

    onViewChange(
      currentView.id,
      {
        ...currentView,
        sort: currentSort.map(({ fieldId, order }) => ({ fieldId, order })),
      },
      TableSortChangeTypes.FILTER_VIEW
    );
  }, [autoSorting, currentSort, currentView, onViewChange, selectedItem]);

  const handleAutoSort = event => {
    const {
      target: { checked },
    } = event;
    if (!currentView) return;
    onViewChange(
      currentView.id,
      { ...currentView, autoSort: checked },
      TableSortChangeTypes.UPDATE_VIEW
    );
  };
  useEffect(() => {
    const isMounted = ref.current !== null;
    return () => {
      if (isMounted && autoSorting) {
        _onViewChange();
      }
    };
  }, [ref, autoSorting, _onViewChange]);

  return (
    <Box ref={ref} sx={{ minWidth: rem(500) }}>
      <SortingFieldSelector
        key={JSON.stringify(currentSort)}
        initialSorting={currentSort}
        fields={selectorFields}
        onChange={setCurrentSort}
        getSortOrderOptions={getSortOrderOptions}
        disabled={!canUpdateView}
      />

      <Flex align='center' bg='decaLight.1' justify='space-between' px={rem(16)} py={rem(12)}>
        <DecaTooltip
          refProp='rootRef'
          position='bottom-start'
          withArrow={false}
          px={rem(8)}
          py={rem(4)}
          offset={{
            crossAxis: 60,
            mainAxis: 4,
          }}
          disabled={!autoSorting}
          label={t('automaticSortingTooltip')}>
          <DecaSwitch
            disabled={!canUpdateView}
            checked={autoSorting}
            label={t('automaticSorting')}
            onChange={handleAutoSort}
          />
        </DecaTooltip>

        {!autoSorting && (
          <Flex align='center' gap={rem(16)}>
            <DecaButton
              size='sm'
              variant='neutral'
                        onClick={() => {
            setCurrentSort(initialSort);
            setSelectedItem?.('');
            onCancel?.();
          }}>
              {t('cancel')}
            </DecaButton>
            {canUpdateView && (
              <DecaButton
                size='sm'
                onClick={() => {
                  _onViewChange();
                  setSelectedItem?.('');
                  onCancel?.();
                }}>
                {t('sort')}
              </DecaButton>
            )}
          </Flex>
        )}
      </Flex>
    </Box>
  );
};

// Interface for the main toolbar item - this excludes internal context props
export interface TableSortToolbarItemProps extends BaseToolbarItemProps {
  getSortOrderOptions?: GetSortOrderOptionsCallback;
  unsortableFieldTypes?: string[];
  // These props will be provided by the consumer (Table or Kanban)
  currentView?: View;
  onViewChange: (id: string, view: View, type?: string) => Promise<void>;
  setSelectedItem?: (item: string) => void;
  selectedItem?: string;
  enablePermissions?: boolean;
}

const TableSortToolbarItem = (props: TableSortToolbarItemProps) => {
  const { 
    className, 
    opened, 
    onOpenChange, 
    getSortOrderOptions, 
    unsortableFieldTypes, 
    currentView,
    onViewChange,
    setSelectedItem,
    selectedItem,
    enablePermissions = true,
    ...rest 
  } = props;
  const { t } = useTranslation('table');
  const theme = useMantineTheme();
  const numberOfSort = currentView?.sort?.length;
  const shouldDisplayBadge = numberOfSort && numberOfSort > 0;

  return (
    <ToolbarItem
      opened={opened}
      onOpenChange={onOpenChange}
      name={TABLE_TOOLBAR_ITEMS.TABLE_SORT}
      label={t('sort')}
      leftIcon={<IconSortAscendingLetters />}
      popoverComponent={
        <TableSort
          onCancel={() => onOpenChange?.(false)}
          getSortOrderOptions={getSortOrderOptions}
          unsortableFieldTypes={unsortableFieldTypes}
          currentView={currentView}
          onViewChange={onViewChange}
          setSelectedItem={setSelectedItem}
          selectedItem={selectedItem}
          enablePermissions={enablePermissions}
        />
      }
      badge={shouldDisplayBadge ? numberOfSort : undefined}
      className={clsx(DECA_TABLE_CLASSES.TOOLBAR_ITEM_SORT, className)}
      color={numberOfSort ? theme.colors.decaBlue[5] : undefined}
      bgColor={numberOfSort ? theme.colors.decaBlue[0] : undefined}
      // Add missing props that ToolbarItem needs
      currentView={currentView}
      setSelectedItem={setSelectedItem}
      selectedItem={selectedItem}
      enablePermissions={enablePermissions}
      {...rest}
    />
  );
};

// Legacy wrapper that uses TableContext - for use within DecaTable
const TableSortToolbarItemWithContext = (props: Omit<TableSortToolbarItemProps, 'currentView' | 'onViewChange' | 'setSelectedItem' | 'selectedItem'>) => {
  
  const { currentView } = useTableContext();
  const { onViewChange, setSelectedItem, selectedItem } = useToolbarContext();

  // Wrap onViewChange to return a Promise
  const handleViewChangeAsync = useCallback(async (id: string, view: View, type?: string) => {
    return Promise.resolve(onViewChange(id, view, type));
  }, [onViewChange]);

  return (
    <TableSortToolbarItem
      {...props}
      currentView={currentView}
      onViewChange={handleViewChangeAsync}
      setSelectedItem={setSelectedItem}
      selectedItem={selectedItem}
    />
  );
};

export { TableSortToolbarItem };
export default memo(TableSortToolbarItemWithContext);
