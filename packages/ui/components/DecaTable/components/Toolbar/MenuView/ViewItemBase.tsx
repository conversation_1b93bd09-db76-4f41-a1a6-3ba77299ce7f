import { Box, Flex, Text, rem, useMantineTheme } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import {
  IconCircleCheckFilled,
  IconLayoutKanban,
  IconLock,
  IconLockOpen,
  IconTable,
} from '@tabler/icons-react';
import { memo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useTableContext } from '../../../../../providers';
import { getTimeAgo } from '../../../../../utils/dateTime';
import { DecaStatus } from '../../../../DecaStatus';
import { DecaTooltip } from '../../../../DecaTooltip';
import { TableMenuViewChangeTypes } from '../../../constants';
import type { View } from '../../../types';
import { useToolbarContext } from '../ToolbarContext';
import { useMenuViewToolbarContext } from './MenuViewToolbarContext';

// Safe context hooks that don't throw errors
const useSafeTableContext = () => {
  try {
    return useTableContext();
  } catch {
    return { currentView: null, objectSettings: null };
  }
};

const useSafeToolbarContext = () => {
  try {
    return useToolbarContext();
  } catch {
    return { onViewChange: () => {} };
  }
};

const useSafeMenuViewToolbarContext = () => {
  try {
    return useMenuViewToolbarContext();
  } catch {
    return { defaultView: '' };
  }
};

const useStyles = createStyles((theme) => ({
  text: {
    fontSize: rem(14),
    fontWeight: 500,
    color: theme.colors.decaGrey[9],
  },
  circleCheck: {
    color: theme.colors.decaGreen[6],
    flexShrink: 0,
  },
  icon: {
    color: theme.colors.decaBlue[5],
    flexShrink: 0,
  },
  manageViewItem: {
    padding: `${rem(8)} ${rem(4)}`,
    borderRadius: theme.radius.sm,
    '&:hover': {
      backgroundColor: `${theme.colors.decaLight[1]} !important`,
    },
  },
  defaultView: {
    backgroundColor: theme.colors.decaGreen[0],
    padding: `${rem(4)} ${rem(8)}`,
    borderRadius: theme.radius.lg,
    color: theme.colors.decaGreen[9],
    flexShrink: 0,
  },
  recordCount: {
    fontWeight: 500,
    padding: `0 ${rem(6)}`,
    minWidth: 'fit-content',
    width: 'auto',
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
}));

export interface ViewItemBaseProps {
  item?: View;
  closeMenuView?: () => void;
  variant?: 'sidebar' | 'manage';
  menuActions?: React.ReactNode;
  className?: string;
  dragIcon?: React.ReactNode;
  // Optional props to replace context dependencies
  currentView?: View;
  objectSettings?: any;
  onViewChange?: (id: string, view: View, type?: string) => void | Promise<void>;
  defaultView?: string;
}

const ViewItemBase = ({
  item,
  closeMenuView,
  variant = 'sidebar',
  menuActions,
  className,
  dragIcon,
  // Optional context props
  currentView: propCurrentView,
  objectSettings: propObjectSettings,
  onViewChange: propOnViewChange,
  defaultView: propDefaultView,
}: ViewItemBaseProps) => {
  const { t } = useTranslation('table');
  const { classes, cx } = useStyles();
  const theme = useMantineTheme();

  // Get context data safely
  const tableContext = useSafeTableContext();
  const toolbarContext = useSafeToolbarContext();
  const menuViewContext = useSafeMenuViewToolbarContext();

  // Use props if provided, otherwise fall back to context
  const currentView = propCurrentView ?? tableContext.currentView;
  const objectSettings = propObjectSettings ?? tableContext.objectSettings;
  const onViewChange = propOnViewChange ?? toolbarContext.onViewChange;
  const defaultView = propDefaultView ?? menuViewContext.defaultView;

  const switchView = async (view: View) => {
    closeMenuView?.();
    setTimeout(() => {
      onViewChange(
        view.id,
        {
          ...view,
        },
        TableMenuViewChangeTypes.SWITCH_VIEW
      );
    });
  };

  const isManageView = variant === 'manage';

  const getTooltipContent = useCallback(
    (viewItem: View) => {
      const timeAgo = getTimeAgo(viewItem.lastUpdated ?? '');

      let relativeText = timeAgo.value;
      if (timeAgo.key === 'timeAgo.justNow') {
        relativeText = t('timeAgo.justNow', { ns: 'common' });
      } else if (timeAgo.key === 'timeAgo.minute') {
        relativeText = t('timeAgo.minute', { ns: 'common', time: timeAgo.value });
      } else if (timeAgo.key === 'timeAgo.hour') {
        relativeText = t('timeAgo.hour', { ns: 'common', time: timeAgo.value });
      } else if (timeAgo.key === 'timeAgo.day') {
        relativeText = t('timeAgo.day', { ns: 'common', time: timeAgo.value });
      }

      return (
        <Flex gap={rem(4)} align='center'>
          <Text size='sm'>
            {viewItem.recordsCount} {t('recordsInView')}.
          </Text>
          <Text size='sm'>
            {t('lastUpdated')}: {relativeText}
          </Text>
        </Flex>
      );
    },
    [t]
  );

  if (!item) return null;

  const renderRightContent = () => {
    const shouldShowRecordCount = objectSettings?.enableRecordCounter;

    const recordCountElement =
      shouldShowRecordCount && typeof item.recordsCount === 'number' && item.recordsCount >= 0 ? (
        item.lastUpdated ? (
          <DecaTooltip label={getTooltipContent(item)} position='right'>
            <Box>
              <DecaStatus className={classes.recordCount} text={item.recordsCount.toString()} />
            </Box>
          </DecaTooltip>
        ) : (
          <DecaStatus className={classes.recordCount} text={item.recordsCount.toString()} />
        )
      ) : null;

    if (isManageView) {
      return (
        <Flex align='center' gap={rem(8)}>
          {item.id === defaultView && (
            <Text className={classes.defaultView} size='sm'>
              {t('defaultView')}
            </Text>
          )}
          {recordCountElement}
        </Flex>
      );
    }

    return (
      <Flex align='center' gap={rem(8)}>
        {item.isDefault && <IconCircleCheckFilled size={16} className={classes.circleCheck} />}
        {recordCountElement}
      </Flex>
    );
  };

  return (
    <Flex
      bg={item.id === currentView?.id ? theme.colors.decaLight[1] : 'unset'}
      className={cx(classes.manageViewItem, className)}
      align='center'
      justify='space-between'
      gap={rem(4)}
      onClick={() => !isManageView && switchView(item)}
    >
      <Flex gap={rem(4)} align='center'>
        {dragIcon}
        <Flex
          align={'center'}
          p={rem(4)}
          bg={theme.colors.decaLight[1]}
          style={{ borderRadius: rem(4) }}
        >
          {item.type === 'kanban' ? (
            <IconLayoutKanban
              size={16}
              className={classes.icon}
              color={theme.colors.decaViolet[5]}
            />
          ) : (
            <IconTable size={16} className={classes.icon} color={theme.colors.decaBlue[5]} />
          )}
        </Flex>
        <Flex align={'center'} gap={rem(8)} data-view-id={item.id}>
          {item.locked ? (
            <IconLock
              size={16}
              className={classes.icon}
              color={theme.colors.decaGrey[6]}
              opacity={0.8}
            />
          ) : (
            <IconLockOpen size={16} className={classes.icon} color={theme.colors.decaGreen[6]} />
          )}
          <DecaTooltip
            label={`${item.type === 'kanban' ? t('kanban') : t('table')} ${t('view')} (${item.locked ? t('locked') : t('unlocked')}): ${item.name}`}
            position='right'
          >
            <Text className={classes.text}>{item.name}</Text>
          </DecaTooltip>
        </Flex>
        {menuActions}
      </Flex>

      <Flex style={{ flexShrink: 0 }} align='center' justify='center'>
        {renderRightContent()}
      </Flex>
    </Flex>
  );
};

export default memo(ViewItemBase);
