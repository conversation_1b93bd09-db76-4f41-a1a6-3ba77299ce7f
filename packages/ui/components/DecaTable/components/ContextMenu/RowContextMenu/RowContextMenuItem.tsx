import { Flex, NumberInput, rem } from '@mantine/core';
import { type KeyboardEvent, useCallback, useRef, useState } from 'react';
import type { MouseEvent, ReactNode } from 'react';
import { Item, Separator } from 'react-contexify';
import { Trans, useTranslation } from 'react-i18next';
import { useTableContext } from '../../../../../providers/TableProvider';
import { MAX_ROW_INSERT_LIMIT } from '../../../constants';
import { useMenuStyles } from '../../../styles';
import { FieldTypes, type View } from '../../../types';
import { useRowContextMenuContext } from './RowContextMenuContext';

const RowContextMenuSeparator = Separator;

type RowContextMenuItemOnSelectCallbackParams = {
  rowIds: string[];
  selectedRowIds: string[];
  event: MouseEvent<HTMLElement>;
};

export type RowContextMenuItemOnSelectCallback = (
  params: RowContextMenuItemOnSelectCallbackParams
) => void;

export interface RowContextMenuItemProps {
  icon: ReactNode;
  label: ReactNode;
  color?: 'default' | 'red';
  onSelect?: RowContextMenuItemOnSelectCallback;
  className?: string;
}

const RowContextMenuItem = (props: RowContextMenuItemProps) => {
  const { icon, label, onSelect, color = 'default', className, ...rest } = props;
  const { classes, cx } = useMenuStyles();
  const { currentTableProps } = useTableContext();

  const handleItemSelect = useCallback(
    ({ event }) => {
      if (!currentTableProps?.row || !currentTableProps?.table) return;
      const rowSelectedList = currentTableProps.table?.getSelectedRowModel()?.rows || [];
      const selectedRowIds = rowSelectedList.map(row => row.id);
      const rowIds = selectedRowIds.length > 0 ? selectedRowIds : [currentTableProps.row?.id];
      onSelect?.({ rowIds, selectedRowIds, event });
    },
    [currentTableProps?.row, currentTableProps?.table, onSelect]
  );

  return (
    <Item
      onClick={handleItemSelect}
      className={cx(color === 'red' && [classes.itemRed], className)}
      {...rest}>
      <Flex className={classes.item}>
        {icon}
        {label}
      </Flex>
    </Item>
  );
};

export interface ConfirmRowContextMenuItemProps extends Omit<RowContextMenuItemProps, 'onSelect'> {
  onConfirm: ({ rowIds }: { rowIds: string[] }) => Promise<void>;
  confirmModalProps?: {
    title: string;
    message: ((rowName: string, currentView: View) => string) | string;
    isRemoving?: boolean;
  };
}

const ConfirmRowContextMenuItem = (props: ConfirmRowContextMenuItemProps) => {
  const { onConfirm, confirmModalProps, ...rest } = props;
  const { open, setModalProps, currentView, currentTableProps, close, setIsSubmitting } =
    useTableContext();
  const { t } = useTranslation('table');

  const getRowsName = useCallback(() => {
    const primaryField = currentView?.fields.find(
      field => field.options?.isPrimary && field.type === FieldTypes.SINGLE_LINE_TEXT
    );
    let unNamedCount = 0;
    const namedArray: string[] = [];
    if (primaryField) {
      currentTableProps?.table?.getSelectedRowModel().rows.forEach(row => {
        const value: string = row.original?.[primaryField.id];
        if (value) {
          namedArray.push(value);
        } else {
          unNamedCount++;
        }
      });
    } else {
      unNamedCount = currentTableProps?.table?.getSelectedRowModel().rows.length || 0;
    }
    return (
      namedArray.join(', ') +
      (unNamedCount > 0 ? ` ${namedArray.length > 0 ? t('and') : ''} ${unNamedCount}` : '')
    );
  }, [currentTableProps, currentView?.fields, t]);

  const getModalMessage = useCallback(() => {
    const message = confirmModalProps?.message;

    if (typeof message === 'function' && currentView) {
      return message(getRowsName(), currentView);
    }

    return message;
  }, [getRowsName, currentView, confirmModalProps?.message]);

  const handleDeleteRow = useCallback(
    async ({ rowIds }) => {
      try {
        setIsSubmitting(true);
        await onConfirm?.({ rowIds });
        currentTableProps?.table?.setRowSelection({});
      } catch (error) {
        console.error(error);
      } finally {
        setIsSubmitting(false);
        close();
      }
    },
    [onConfirm, currentTableProps?.table, setIsSubmitting, close]
  );

  const handleSelect = useCallback(
    async ({ rowIds }) => {
      setModalProps({
        title: confirmModalProps?.title,
        message: getModalMessage(),
        isRemoving: confirmModalProps?.isRemoving ?? true,
        onConfirm: () => handleDeleteRow({ rowIds }),
      });

      open();
    },
    [
      confirmModalProps?.title,
      confirmModalProps?.isRemoving,
      open,
      setModalProps,
      getModalMessage,
      handleDeleteRow,
    ]
  );

  return <RowContextMenuItem onSelect={handleSelect} {...rest} />;
};

const SelectRowContextMenuItem = (props: RowContextMenuItemProps) => {
  const { onSelect, label, ...rest } = props;
  const { currentTableProps, handleCellClick } = useTableContext();
  const { setPersistRowSelectionOnAnimate, hasRowSelected } = useRowContextMenuContext();
  const { t } = useTranslation('table');

  const handleSelectRow = useCallback(
    ({ rowIds, selectedRowIds, event }) => {
      handleCellClick(null, null);
      setPersistRowSelectionOnAnimate(true);

      if (selectedRowIds.length > 0) {
        currentTableProps?.table?.setRowSelection({});
      } else {
        currentTableProps?.table?.setRowSelection({ [rowIds[0]]: true });
      }

      onSelect?.({ rowIds, selectedRowIds, event });
    },
    [currentTableProps?.table, handleCellClick, setPersistRowSelectionOnAnimate, onSelect]
  );

  return (
    <RowContextMenuItem
      label={label ?? t(hasRowSelected ? 'deselectRow' : 'selectRow')}
      onSelect={handleSelectRow}
      {...rest}
    />
  );
};

type NumberRowContextMenuItemOnSelectCallbackParams = RowContextMenuItemOnSelectCallbackParams & {
  insertNumber: number;
};

type NumberRowContextMenuItemOnSelectCallback = (
  params: NumberRowContextMenuItemOnSelectCallbackParams
) => void;

export interface NumberRowContextMenuItemProps extends Omit<RowContextMenuItemProps, 'onSelect'> {
  defaultValue?: number;
  max?: number;
  min?: number;
  onSelect?: NumberRowContextMenuItemOnSelectCallback;
}

const NumberInputRowContextMenuItem = (props: NumberRowContextMenuItemProps) => {
  const { onSelect, label, defaultValue = 1, max = MAX_ROW_INSERT_LIMIT, min = 1, ...rest } = props;
  const inputInsertBelowRef = useRef<HTMLInputElement>(null);
  const [value, setValue] = useState(1);

  const handleKeyDown = useCallback((e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
      e.stopPropagation();
    }
    if ((e.key < '0' || e.key > '9') && !['Backspace', 'Delete'].includes(e.key)) {
      e.preventDefault();
    }
  }, []);

  const handleChange = useCallback((val: number) => {
    setValue(val || 1);
  }, []);

  const handleSelect: RowContextMenuItemOnSelectCallback = useCallback(
    params => {
      onSelect?.({ ...params, insertNumber: value });
    },
    [onSelect, value]
  );

  return (
    <RowContextMenuItem
      onSelect={handleSelect}
      label={
        <Trans
          i18nKey={label ? String(label) as any : 'addAbove'}
          components={{
            input: (
              <NumberInput
                ref={inputInsertBelowRef}
                onFocus={() => {
                  if (inputInsertBelowRef.current) {
                    inputInsertBelowRef.current.select();
                  }
                }}
                value={value}
                onChange={(value: number | string) => handleChange(Number(value))}
                min={min}
                max={max}
                hideControls
                onClick={e => {
                  e.stopPropagation();
                }}
                onKeyDown={handleKeyDown}
                fz='sm'
                styles={theme => ({
                  input: {
                    width: rem(36),
                    height: rem(20),
                    minHeight: rem(20),
                    textAlign: 'center',
                    borderColor: theme.colors.decaLight[6],
                    padding: rem(4),
                    '&:focus': {
                      borderColor: theme.colors.blue[5],
                    },
                  },
                })}
              />
            ),
          }}
          ns='table'
        />
      }
      {...rest}
    />
  );
};

export {
  RowContextMenuItem,
  ConfirmRowContextMenuItem,
  SelectRowContextMenuItem,
  RowContextMenuSeparator,
  NumberInputRowContextMenuItem,
};
