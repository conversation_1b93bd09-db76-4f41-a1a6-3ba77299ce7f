import { useMemo } from 'react';
import { NODE_NAME } from '../../../constants/nodes';
import { getDefaultValues, resolveRefs } from '../../../utils/schema';
import type { Schema, SchemaFormInput } from '../types';

export const useSchema = (schemaRoot: Schema, formRaw: SchemaFormInput) => {
  const schema = useMemo(() => {
    if (!schemaRoot || typeof schemaRoot !== 'object') return schemaRoot;
    return resolveRefs(schemaRoot, schemaRoot);
  }, [schemaRoot]);

  const form = useMemo(() => {
    let schemaTmp = schema.settings;
    if (schema.name === NODE_NAME.PATH) {
      schemaTmp = schema.settings?.paths?.items?.properties || {};
    }
    return {
      ...formRaw,
      settings: {
        ...getDefaultValues(schemaTmp),
        ...formRaw.settings,
      },
    };
  }, [formRaw, schema]);
  return {
    schema,
    form,
  };
};
