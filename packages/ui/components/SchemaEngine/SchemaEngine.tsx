import { Flex } from '@mantine/core';
import { useMemo, useState } from 'react';
import { NODE_NAME } from '../../constants';
import type { ICredential, ICredentialPayload } from '../Credential/type';
import type { ComboboxNode } from '../FormChema/components/ComboboxSelectDataPoint';
import { withTolgee } from '../hoc/withTolgee';
import ConfigureForm from './components/ConfigureForm';
import SetupForm from './components/SetupForm';
import type { Step } from './components/Stepper';
import { useSchema } from './hooks/useSchema';
import type {
  NodeApiCallCallback,
  Schema,
  SchemaFormCallback,
  SchemaFormInput,
  StepChangeCallback,
} from './types';

const STEPS = {
  SETUP: {
    id: 'setup',
    label: 'Select',
  },
  CONFIGURE: {
    id: 'configure',
    label: 'Configure',
  },
  TEST: {
    id: 'test',
    label: 'Test',
  },
};

export const ACTION_FIELD = 'action';
export const TRIGGER_FIELD = 'trigger';

const isSchemaActionsOrTriggers = (schema: Record<string, any>) => {
  return schema?.actions && Object.keys(schema?.actions).length > 0 ? ACTION_FIELD : TRIGGER_FIELD;
};

interface SchemaEngineProps {
  schema?: Schema;
  activeStep?: number;
  completedStep?: number;
  onStepChange?: StepChangeCallback;
  onClose?: () => void;
  credentials: ICredential[];
  isCredentialsLoading: boolean;
  createCredential?: (credential: ICredentialPayload) => Promise<ICredential | null>;
  onOpenAppCatalog?: () => void;
  // Form
  form: SchemaFormInput;
  onFormChange?: SchemaFormCallback;
  previousNodes?: ComboboxNode[];
  nodeContext?: typeof ACTION_FIELD | typeof TRIGGER_FIELD;
  // API Call
  onNodeApiCall: NodeApiCallCallback;
}

const SchemaEngine: React.FC<SchemaEngineProps> = (props) => {
  const {
    form: formRaw,
    schema: schemaRoot = {},
    credentials,
    previousNodes,
    activeStep = 0,
    completedStep = -1,
    isCredentialsLoading,
    onStepChange,
    onFormChange,
    onOpenAppCatalog,
    createCredential,
    onClose,
    nodeContext,
    onNodeApiCall,
  } = props;

  const { schema, form } = useSchema(schemaRoot, formRaw);
  const schemaName = schema?.name;
  const [nodeApiCallResults, setNodeApiCallResults] = useState<Record<string, any>>({});
  const [completedApiCalls, setCompletedApiCalls] = useState<Record<string, boolean>>({});

  const objectType = useMemo(() => {
    if (nodeContext === ACTION_FIELD) return ACTION_FIELD;
    if (nodeContext === TRIGGER_FIELD) return TRIGGER_FIELD;
    return isSchemaActionsOrTriggers(schema);
  }, [nodeContext, schema]);

  const showSetupStep = schemaName !== NODE_NAME.PATH;

  const shouldRenderConfigureForm = useMemo(() => {
    const itemObject = nodeContext === ACTION_FIELD ? schema?.actions : schema?.triggers;
    const hasSettingsProperties = Object.keys(schema?.settings || {}).length > 0;

    // check if the itemObject has properties
    const hasItemObjectProperties =
      itemObject &&
      Object.values(itemObject).some((item: any) => {
        return item?.properties && Object.keys(item.properties).length > 0;
      });
    return hasSettingsProperties || hasItemObjectProperties;
  }, [nodeContext, schema]);

  const schemaEngineSteps = useMemo(() => {
    const steps: Step[] = [];

    if (showSetupStep) {
      steps.push(STEPS.SETUP);
    }

    if (shouldRenderConfigureForm) {
      steps.push(STEPS.CONFIGURE);
    }

    return steps;
  }, [showSetupStep, shouldRenderConfigureForm]);

  const currentStep = schemaEngineSteps[activeStep] ?? schemaEngineSteps[0];

  return (
    <Flex direction='column' h='100%' gap='0' pos='relative'>
      {currentStep?.id === STEPS.SETUP.id && (
        <SetupForm
          schema={schema}
          steps={schemaEngineSteps}
          activeStep={activeStep}
          completedStep={completedStep}
          objectType={objectType}
          credentials={credentials}
          isCredentialsLoading={isCredentialsLoading}
          createCredential={createCredential}
          onOpenAppCatalog={onOpenAppCatalog}
          onStepChange={(step) => {
            if (shouldRenderConfigureForm) {
              onStepChange?.(step);
            } else {
              onFormChange?.({ forceCompleted: true }, 'forceCompleted');
              onClose?.();
            }
          }}
          form={form}
          onFormChange={onFormChange}
          onNodeApiCall={onNodeApiCall}
          completedApiCalls={completedApiCalls}
          onNodeApiCallResultChange={setNodeApiCallResults}
          onCompletedApiCallChange={setCompletedApiCalls}
          hideStepper={!shouldRenderConfigureForm}
        />
      )}

      {shouldRenderConfigureForm && currentStep.id === STEPS.CONFIGURE.id && (
        <ConfigureForm
          schema={schema}
          steps={schemaEngineSteps}
          activeStep={activeStep}
          completedStep={completedStep}
          objectType={objectType}
          onStepChange={onStepChange}
          form={form}
          onFormChange={onFormChange}
          defaultToFirstObject={!showSetupStep}
          previousNodes={previousNodes}
          onClose={onClose}
          nodeApiCallResults={nodeApiCallResults}
          onNodeApiCall={onNodeApiCall}
          onNodeApiCallResultChange={setNodeApiCallResults}
        />
      )}
    </Flex>
  );
};

export default SchemaEngine;
export const SchemaEngineWithTolgee = withTolgee(SchemaEngine);
