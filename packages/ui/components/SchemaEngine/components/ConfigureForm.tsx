import { Stack } from '@mantine/core';
import { ScrollArea } from '@mantine/core';
import { useCallbackRef } from '@mantine/hooks';
import { useTranslate } from '@tolgee/react';
import set from 'lodash/set';
import { useCallback, useEffect, useMemo } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { NODE_NAME } from '../../../constants';
import {
  getSelectedProperties,
  resolveFieldDependencies,
  resolvePropertiesApiCallResults,
} from '../../../utils/schema';
import type { ComboboxNode } from '../../FormChema/components/ComboboxSelectDataPoint';
import { useScrollAreaStyles } from '../styles';
import type {
  ConfigureFormInput,
  ObjectType,
  Schema,
  SchemaFormCallback,
  StepChangeCallback,
} from '../types';
import FormEngine from './FormEngine';
import { type Step, Stepper, StepperActions } from './Stepper';

interface ConfigureFormProps {
  schema: Schema;
  objectType: ObjectType;
  activeStep?: number;
  completedStep?: number;
  steps?: Step[];
  onStepChange?: StepChangeCallback;
  form: ConfigureFormInput;
  onFormChange?: SchemaFormCallback;
  defaultToFirstObject?: boolean;
  previousNodes?: ComboboxNode[];
  onClose?: () => void;
  nodeApiCallResults?: Record<string, any>;
  onNodeApiCall?: (node: string, attribute: string, payload: any) => Promise<any>;
  onNodeApiCallResultChange?: (results: Record<string, any>) => void;
}

const ConfigureForm = (props: ConfigureFormProps) => {
  const {
    schema,
    form = {},
    steps = [],
    activeStep = 0,
    completedStep = -1,
    objectType,
    onStepChange,
    onFormChange,
    defaultToFirstObject = false,
    previousNodes,
    onClose,
    nodeApiCallResults,
    onNodeApiCall,
    onNodeApiCallResultChange,
  } = props;
  const { t } = useTranslate();
  const { classes } = useScrollAreaStyles();
  const itemObject = objectType === 'action' ? schema?.actions : schema?.triggers;
  const selectedObject = form[objectType];

  const defaultValues = {
    ...form.settings,
  };

  const methods = useForm({
    criteriaMode: 'all',
    mode: 'onChange',
    defaultValues,
  });
  const { watch, formState, trigger } = methods;
  const { isValid } = formState;

  const commonProperties = useMemo(() => {
    let properties = schema?.settings;
    if (!properties) return {};

    if (schema.name === NODE_NAME.PATH) {
      properties = properties?.paths?.items?.properties || {};
    }

    return Object.fromEntries(
      Object.entries(properties).filter(([_, v]: [string, any]) => v?.type !== 'credential')
    );
  }, [schema.name, schema?.settings]);

  const selectedProperties = useMemo(() => {
    const result = getSelectedProperties(itemObject, selectedObject, defaultToFirstObject);
    return resolvePropertiesApiCallResults(nodeApiCallResults || {}, result);
  }, [itemObject, selectedObject, defaultToFirstObject, nodeApiCallResults]);

  const formChangeCallback = useCallbackRef(onFormChange);

  const handleRefreshOptions = useCallback(
    async (fieldName: string) => {
      if (!onNodeApiCall) {
        console.warn('onNodeApiCall is not defined');
        return;
      }

      try {
        // Find the field schema to get the API call configuration
        const fieldSchema = selectedProperties[fieldName] || commonProperties[fieldName];
        if (!fieldSchema) {
          console.warn(`Field schema not found for ${fieldName}`);
          return;
        }

        const apiCallConfig = fieldSchema.apiCall;
        if (!apiCallConfig) {
          console.warn(`No API call configuration found for ${fieldName}`);
          return;
        }

        // Build the payload with current form values, resolving dependencies
        const payload = resolveFieldDependencies(apiCallConfig.payload, watch());

        // Make the API call to refresh options
        const result = await onNodeApiCall(schema.name, `${form[objectType]}.${fieldName}`, {
          ...payload,
          credential: form?.settings?.credential,
        });

        // Update the nodeApiCallResults with the new data
        if (result && onNodeApiCallResultChange) {
          const updatedNodeApiCallResults = { ...nodeApiCallResults };
          set(updatedNodeApiCallResults, `${fieldName}.apiCall`, result);
          onNodeApiCallResultChange(updatedNodeApiCallResults);
        }
      } catch (error) {
        console.warn(`Failed to refresh options for ${fieldName}:`, error);
      }
    },
    [
      onNodeApiCall,
      selectedProperties,
      commonProperties,
      schema.name,
      form,
      objectType,
      onNodeApiCallResultChange,
      nodeApiCallResults,
      watch,
    ]
  );

  const handleUpdateCompletedStep = useCallback(
    (step: number) => {
      if (!formChangeCallback) return;
      formChangeCallback({ completedFormStep: step }, 'completedFormStep');
    },
    [formChangeCallback]
  );

  const handleNext = useCallback(() => {
    // current step is completed
    handleUpdateCompletedStep(activeStep);
    onClose?.();
  }, [onClose, handleUpdateCompletedStep, activeStep]);

  const handleOnStepChange = useCallback(
    (step: number) => {
      onStepChange?.(step);
    },
    [onStepChange]
  );

  const updateCompletedStepWhenFormValid = useCallback(() => {
    handleUpdateCompletedStep(isValid ? activeStep : activeStep - 1);
  }, [isValid, activeStep, handleUpdateCompletedStep]);

  useEffect(() => {
    updateCompletedStepWhenFormValid();
  }, [updateCompletedStepWhenFormValid]);

  // trigger form validation on mount
  useEffect(() => {
    trigger();
  }, [trigger]);

  useEffect(() => {
    const { unsubscribe } = watch((value, { name }) => {
      if (!name) return;
      formChangeCallback(value, name);
    });
    return () => unsubscribe();
  }, [watch, formChangeCallback]);

  useEffect(() => {
    const initialFormValue = watch();
    const initialFormValueKeys = Object.keys(initialFormValue || {});
    if (initialFormValueKeys.length === 0) return;
    initialFormValueKeys.forEach(key => {
      const value = initialFormValue[key];
      if (value && value !== '') {
        formChangeCallback(initialFormValue, key);
      }
    });
  }, [watch, formChangeCallback]);

  return (
    <FormProvider {...methods}>
      <ScrollArea sx={{ flex: 1 }} className={classes.scrollArea}>
        <Stack gap='lg'>
          <Stepper
            steps={steps}
            activeStep={activeStep}
            completedStep={completedStep}
            currentStepValid={isValid}
            onStepChange={handleOnStepChange}
          />
          <FormEngine
            commonProperties={commonProperties}
            selectedProperties={selectedProperties}
            previousNodes={previousNodes}
            onRefreshOptions={handleRefreshOptions}
          />
        </Stack>
      </ScrollArea>

      <StepperActions
        activeStep={activeStep}
        nextBtnLabel={isValid ? t('button.continue') : t('form.requiredFields')}
        disabledNextBtn={!isValid}
        showBackBtn={false}
        onNext={handleNext}
      />
    </FormProvider>
  );
};

export default ConfigureForm;
