import type { CustomIconProps } from '.';

const IconAppDefault = (props: CustomIconProps) => {
  return (
    <svg
      width='48'
      height='48'
      viewBox='0 0 48 48'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M6 10C6 7.79086 7.79086 6 10 6H18C20.2091 6 22 7.79086 22 10V18C22 20.2091 20.2091 22 18 22H10C7.79086 22 6 20.2091 6 18V10ZM18 10H10V18H18V10ZM26 10C26 7.79086 27.7909 6 30 6H38C40.2091 6 42 7.79086 42 10V18C42 20.2091 40.2091 22 38 22H30C27.7909 22 26 20.2091 26 18V10ZM38 10H30V18H38V10ZM6 30C6 27.7909 7.79086 26 10 26H18C20.2091 26 22 27.7909 22 30V38C22 40.2091 20.2091 42 18 42H10C7.79086 42 6 40.2091 6 38V30ZM18 30H10V38H18V30ZM26 30C26 27.7909 27.7909 26 30 26H38C40.2091 26 42 27.7909 42 30V38C42 40.2091 40.2091 42 38 42H30C27.7909 42 26 40.2091 26 38V30ZM38 30H30V38H38V30Z'
        fill='#A3A3A3'
      />
    </svg>
  );
};

export default IconAppDefault;
