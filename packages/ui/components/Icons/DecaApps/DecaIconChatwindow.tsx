import { CustomIconProps } from '..';

const DecaIconChatwindow = (props: CustomIconProps) => {
  return (
    <svg
      width='40'
      height='40'
      viewBox='0 0 40 40'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}>
      <path
        d='M23.2666 7.55597H4.9063C3.29804 7.55597 2 8.78109 2 10.294V18.4587C2 19.9716 3.29804 21.1967 4.9063 21.1967H23.2666C24.8749 21.1967 26.1729 19.9716 26.1729 18.4587V10.294C26.1729 8.78109 24.8749 7.55597 23.2666 7.55597Z'
        fill='#3539BC'
      />
      <path
        d='M29.669 22.9328H11.3086C9.70038 22.9328 8.40234 24.1579 8.40234 25.6708V31.262C8.40234 32.7749 9.70038 34 11.3086 34H29.669C31.2772 34 32.5753 32.7749 32.5753 31.262V25.6708C32.5753 24.1579 31.2772 22.9328 29.669 22.9328Z'
        fill='#6DAFF8'
      />
      <path
        d='M5.33594 14.7018H23.2718'
        stroke='white'
        strokeWidth='0.769231'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M5.33594 17.9259H16.8632'
        stroke='white'
        strokeWidth='0.769231'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M12.168 26.3923H29.1486'
        stroke='white'
        strokeWidth='0.769231'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M12.168 30.6678H29.1486'
        stroke='white'
        strokeWidth='0.769231'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M29.8204 6.0925L30.351 7.81918C30.3673 7.86851 30.4 7.9014 30.449 7.91784L32.1634 8.45229C32.2858 8.4934 32.2858 8.65785 32.1634 8.69896L30.449 9.23341C30.4 9.24985 30.3673 9.28274 30.351 9.33207L29.8204 11.0588C29.7796 11.1821 29.6163 11.1821 29.5755 11.0588L29.0448 9.33207C29.0285 9.28274 28.9958 9.24985 28.9468 9.23341L27.2325 8.69896C27.11 8.65785 27.11 8.4934 27.2325 8.45229L28.9468 7.91784C28.9958 7.9014 29.0285 7.86851 29.0448 7.81918L29.5755 6.0925C29.6163 5.96917 29.7796 5.96917 29.8204 6.0925Z'
        fill='#E4007F'
      />
      <path
        d='M8.92315 12.0298C9.49125 12.0298 9.95179 11.566 9.95179 10.9938C9.95179 10.4217 9.49125 9.95782 8.92315 9.95782C8.35505 9.95782 7.89453 10.4217 7.89453 10.9938C7.89453 11.566 8.35505 12.0298 8.92315 12.0298Z'
        fill='white'
      />
      <path
        d='M12.4818 12.0298C13.0499 12.0298 13.5104 11.566 13.5104 10.9938C13.5104 10.4217 13.0499 9.95782 12.4818 9.95782C11.9137 9.95782 11.4531 10.4217 11.4531 10.9938C11.4531 11.566 11.9137 12.0298 12.4818 12.0298Z'
        fill='white'
      />
      <path
        d='M5.78643 12.0298C6.35453 12.0298 6.81508 11.566 6.81508 10.9938C6.81508 10.4217 6.35453 9.95782 5.78643 9.95782C5.21833 9.95782 4.75781 10.4217 4.75781 10.9938C4.75781 11.566 5.21833 12.0298 5.78643 12.0298Z'
        fill='#E4007F'
      />
      <path
        d='M34.0778 12.4322L35.1227 15.2442L37.9147 16.2967C38.029 16.3378 38.029 16.5022 37.9147 16.5434L35.1227 17.5958L34.0778 20.4078C34.0369 20.5229 33.8737 20.5229 33.8328 20.4078L32.7879 17.5958L29.9959 16.5434C29.8816 16.5022 29.8816 16.3378 29.9959 16.2967L32.7879 15.2442L33.8328 12.4322C33.8737 12.3171 34.0369 12.3171 34.0778 12.4322Z'
        fill='#E4007F'
      />
    </svg>
  );
};

export default DecaIconChatwindow;