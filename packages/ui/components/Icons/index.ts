import type { CSSProperties } from 'react';

import DecaIconCsv from './DecaIconCsv';
import DecaIconDebounceDot from './DecaIconDebounceDot';
import DecaIconDoc from './DecaIconDoc';
import DecaIconDocx from './DecaIconDocx';
import DecaIconMd from './DecaIconMd';
import DecaIconPdf from './DecaIconPdf';
import DecaIconTxt from './DecaIconTxt';
import IconAIRedoSpark from './IconAIRedoSpark';
import IconAppDefault from './IconAppDefault';
import IconClipboardTypography from './IconClipboardTypography';
import IconErrorDoc from './IconErrorDoc';
import IconErrorImage from './IconErrorImage';
import IconErrorVideo from './IconErrorVideo';
import IconFileTypeJpg from './IconFileTypeJpg';
import IconFileTypePdf from './IconFileTypePdf';
import IconFileTypePng from './IconFileTypePng';
import IconFileTypePpt from './IconFileTypePptx';
import IconFileTypeXls from './IconFileTypeXls';
import IconFolderEmpty from './IconFolderEmpty';
import IconGif from './IconGif';
import IconLine from './IconLine';
import IconMediumResize from './IconMediumResize';
import IconNoResultFound from './IconNoResultFound';
import IconNumberInput from './IconNumberInput';
import IconPrivacy from './IconPrivacy';
import IconShortResize from './IconShortResize';
import IconStroke from './IconStroke';

export interface CustomIconProps {
  className?: string;
  style?: CSSProperties;
  fill?: string;
  width?: string | number;
  height?: string | number;
  onClick?: () => void;
}

export {
  IconPrivacy,
  IconMediumResize,
  IconShortResize,
  IconStroke,
  IconFileTypePdf,
  IconFileTypeJpg,
  IconFileTypePng,
  IconFileTypePpt,
  IconGif,
  IconFileTypeXls,
  IconClipboardTypography,
  IconFolderEmpty,
  DecaIconPdf,
  DecaIconMd,
  DecaIconDoc,
  DecaIconDocx,
  DecaIconCsv,
  DecaIconTxt,
  IconLine,
  IconNoResultFound,
  IconNumberInput,
  IconErrorImage,
  IconErrorVideo,
  IconErrorDoc,
  DecaIconDebounceDot,
  IconAIRedoSpark,
  IconAppDefault,
};

export * from './BuildInTools';
export * from './ExternalTools';
export * from './DecaApps';
