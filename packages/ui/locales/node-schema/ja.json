{"code": {"actions.run_javascript.description": "Executes the provided JavaScript code", "actions.run_javascript.displayName": "Run JavaScript", "actions.run_javascript.properties.code.description": "The code to execute", "actions.run_javascript.properties.code.displayName": "Code", "actions.run_javascript.properties.input.default.var1.description": "The input params to process", "actions.run_javascript.properties.input.default.var1.displayName": "Variable 1", "actions.run_javascript.properties.input.description": "The input params to process", "actions.run_javascript.properties.input.displayName": "Input Params", "description": "Execute custom script code", "displayName": "Code", "schemas.codeOutput.properties.duration.description": "The duration of the code execution in milliseconds", "schemas.codeOutput.properties.duration.displayName": "Duration", "schemas.codeOutput.properties.error.description": "The error message if the code execution fails", "schemas.codeOutput.properties.error.displayName": "Error", "schemas.codeOutput.properties.logs.description": "The logs of the code execution", "schemas.codeOutput.properties.logs.displayName": "Logs", "schemas.codeOutput.properties.results.description": "The results of the code execution", "schemas.codeOutput.properties.results.displayName": "Results"}, "deca-ai-widgets": {"actions.explanation.description": "Explain a text or paragraph", "actions.explanation.displayName": "Explanation", "actions.explanation.properties.apiVersion.description": "The API version for the request (e.g., '2024-08-15'). Passed in x-api-version header.", "actions.explanation.properties.apiVersion.displayName": "API Version", "actions.explanation.properties.instanceId.displayName": "Instance ID", "actions.explanation.properties.text.displayName": "Text", "actions.proof_reading.description": "Proof-read a text or paragraph", "actions.proof_reading.displayName": "Proof-reading", "actions.proof_reading.properties.apiVersion.description": "The API version for the request (e.g., '2024-08-15'). Passed in x-api-version header.", "actions.proof_reading.properties.apiVersion.displayName": "API Version", "actions.proof_reading.properties.instanceId.displayName": "Instance ID", "actions.proof_reading.properties.text.displayName": "Text", "actions.proof_reading.properties.useAI.displayName": "Use AI", "actions.proof_reading.properties.useUserDictionary.displayName": "Use User Dictionary", "actions.reply_assistant.description": "Reply to a text or paragraph", "actions.reply_assistant.displayName": "Reply Assistant", "actions.reply_assistant.properties.apiVersion.description": "The API version for the request (e.g., '2024-08-15'). Passed in x-api-version header.", "actions.reply_assistant.properties.apiVersion.displayName": "API Version", "actions.reply_assistant.properties.instanceId.displayName": "Instance ID", "actions.reply_assistant.properties.isKB.displayName": "Is KB", "actions.reply_assistant.properties.maxReferences.displayName": "Max References", "actions.reply_assistant.properties.text.displayName": "Text", "actions.summarization.description": "Summarize a text or paragraph", "actions.summarization.displayName": "Summarization", "actions.summarization.properties.apiVersion.description": "The API version for the request (e.g., '2024-08-15'). Passed in x-api-version header.", "actions.summarization.properties.apiVersion.displayName": "API Version", "actions.summarization.properties.instanceId.displayName": "Instance ID", "actions.summarization.properties.text.displayName": "Text", "actions.tone_adjustment.description": "Adjust the tone of a text or paragraph", "actions.tone_adjustment.displayName": "Tone Adjustment", "actions.tone_adjustment.properties.apiVersion.description": "The API version for the request (e.g., '2024-08-15'). Passed in x-api-version header.", "actions.tone_adjustment.properties.apiVersion.displayName": "API Version", "actions.tone_adjustment.properties.text.displayName": "Text", "actions.tone_adjustment.properties.tone.displayName": "<PERSON><PERSON>", "actions.translation.description": "Translate a text or paragraph", "actions.translation.displayName": "Translation", "actions.translation.properties.apiVersion.description": "The API version for the request (e.g., '2024-08-15'). Passed in x-api-version header.", "actions.translation.properties.apiVersion.displayName": "API Version", "actions.translation.properties.instanceId.displayName": "Instance ID", "actions.translation.properties.sourceLanguage.description": "The source language of the text (e.g., 'en', 'es', 'fr').", "actions.translation.properties.sourceLanguage.displayName": "Source Language", "actions.translation.properties.targetLanguage.description": "The target language for translation (e.g., 'en', 'es', 'fr').", "actions.translation.properties.targetLanguage.displayName": "Target Language", "actions.translation.properties.text.displayName": "Text", "actions.widget_create.description": "Create a new widget", "actions.widget_create.displayName": "Create Widget", "actions.widget_create.properties.apiVersion.description": "The API version for the request (e.g., '2024-08-15'). Passed in x-api-version header.", "actions.widget_create.properties.apiVersion.displayName": "API Version", "actions.widget_create.properties.description.displayName": "Description", "actions.widget_create.properties.name.displayName": "Name", "actions.widget_create.properties.type.description": "The type of the widget.", "actions.widget_create.properties.type.displayName": "Type", "actions.widget_custom_list.description": "List all custom widgets", "actions.widget_custom_list.displayName": "List Widget Customs", "actions.widget_custom_list.properties.apiVersion.description": "The API version for the request (e.g., '2024-08-15'). Passed in x-api-version header.", "actions.widget_custom_list.properties.apiVersion.displayName": "API Version", "actions.widget_custom_retrieve.description": "Get a custom widget by ID", "actions.widget_custom_retrieve.displayName": "Get Widget Custom", "actions.widget_custom_retrieve.properties.apiVersion.description": "The API version for the request (e.g., '2024-08-15'). Passed in x-api-version header.", "actions.widget_custom_retrieve.properties.apiVersion.displayName": "API Version", "actions.widget_custom_retrieve.properties.widgetCustomId.displayName": "Widget Custom ID", "actions.widget_instance_install.description": "Install a widget instance", "actions.widget_instance_install.displayName": "Install Widget", "actions.widget_instance_install.properties.apiVersion.description": "The API version for the request (e.g., '2024-08-15'). Passed in x-api-version header.", "actions.widget_instance_install.properties.apiVersion.displayName": "API Version", "actions.widget_instance_install.properties.description.displayName": "Description", "actions.widget_instance_install.properties.name.displayName": "Name", "actions.widget_instance_install.properties.status.description": "The status of the widget instance.", "actions.widget_instance_install.properties.status.displayName": "Status", "actions.widget_instance_install.properties.type.description": "The type of the widget.", "actions.widget_instance_install.properties.type.displayName": "Type", "actions.widget_instance_install.properties.widgetId.displayName": "Widget ID", "actions.widget_instance_install.properties.widgetSettings.description": "JSON object for widget instance settings.", "actions.widget_instance_install.properties.widgetSettings.displayName": "Settings", "actions.widget_instance_install.properties.workspaceId.displayName": "Workspace ID", "actions.widget_instance_list.description": "List all installed widget instances", "actions.widget_instance_list.displayName": "List Installed Widgets", "actions.widget_instance_list.properties.apiVersion.description": "The API version for the request (e.g., '2024-08-15'). Passed in x-api-version header.", "actions.widget_instance_list.properties.apiVersion.displayName": "API Version", "actions.widget_instance_list.properties.type.description": "The type of the widget.", "actions.widget_instance_list.properties.type.displayName": "Type", "actions.widget_instance_list.properties.workspaceId.displayName": "Workspace ID", "actions.widget_instance_settings_update.description": "Update the settings of a widget instance", "actions.widget_instance_settings_update.displayName": "Update Widget Instance Settings", "actions.widget_instance_settings_update.properties.apiVersion.description": "The API version for the request (e.g., '2024-08-15'). Passed in x-api-version header.", "actions.widget_instance_settings_update.properties.apiVersion.displayName": "API Version", "actions.widget_instance_settings_update.properties.type.description": "The type of the widget.", "actions.widget_instance_settings_update.properties.type.displayName": "Type", "actions.widget_instance_settings_update.properties.widgetInstanceId.displayName": "Widget Instance ID", "actions.widget_instance_settings_update.properties.widgetSettings.description": "JSON object for widget instance settings.", "actions.widget_instance_settings_update.properties.widgetSettings.displayName": "Settings", "actions.widget_instance_settings_update.properties.workspaceId.displayName": "Workspace ID", "actions.widget_instance_uninstall.description": "Uninstall a widget instance", "actions.widget_instance_uninstall.displayName": "Uninstall Widget Instance", "actions.widget_instance_uninstall.properties.apiVersion.description": "The API version for the request (e.g., '2024-08-15'). Passed in x-api-version header.", "actions.widget_instance_uninstall.properties.apiVersion.displayName": "API Version", "actions.widget_instance_uninstall.properties.widgetInstanceId.displayName": "Widget Instance ID", "actions.widget_instance_uninstall.properties.workspaceId.displayName": "Workspace ID", "actions.widget_instance_update.description": "Update a widget instance", "actions.widget_instance_update.displayName": "Update Widget Instance", "actions.widget_instance_update.properties.apiVersion.description": "The API version for the request (e.g., '2024-08-15'). Passed in x-api-version header.", "actions.widget_instance_update.properties.apiVersion.displayName": "API Version", "actions.widget_instance_update.properties.description.displayName": "Description", "actions.widget_instance_update.properties.name.displayName": "Name", "actions.widget_instance_update.properties.status.description": "The status of the widget instance.", "actions.widget_instance_update.properties.status.displayName": "Status", "actions.widget_instance_update.properties.type.description": "The type of the widget.", "actions.widget_instance_update.properties.type.displayName": "Type", "actions.widget_instance_update.properties.widgetInstanceId.displayName": "Widget Instance ID", "actions.widget_instance_update.properties.workspaceId.displayName": "Workspace ID", "actions.widget_list.description": "List all widgets", "actions.widget_list.displayName": "List Widgets", "actions.widget_list.properties.apiVersion.description": "The API version for the request (e.g., '2024-08-15'). Passed in x-api-version header.", "actions.widget_list.properties.apiVersion.displayName": "API Version", "actions.widget_retrieve.description": "Get a widget by ID", "actions.widget_retrieve.displayName": "Get Widget", "actions.widget_retrieve.properties.apiVersion.description": "The API version for the request (e.g., '2024-08-15'). Passed in x-api-version header.", "actions.widget_retrieve.properties.apiVersion.displayName": "API Version", "actions.widget_retrieve.properties.type.description": "The type of the widget.", "actions.widget_retrieve.properties.type.displayName": "Type", "actions.widget_retrieve.properties.widgetId.displayName": "Widget ID", "actions.widget_update.description": "Update a widget by ID", "actions.widget_update.displayName": "Update Widget", "actions.widget_update.properties.apiVersion.description": "The API version for the request (e.g., '2024-08-15'). Passed in x-api-version header.", "actions.widget_update.properties.apiVersion.displayName": "API Version", "actions.widget_update.properties.description.displayName": "Description", "actions.widget_update.properties.name.displayName": "Name", "actions.widget_update.properties.type.description": "The type of the widget.", "actions.widget_update.properties.type.displayName": "Type", "actions.widget_update.properties.widgetId.displayName": "Widget ID", "description": "Interact with the AI Widgets service.", "displayName": "AI Widgets", "schemas.common.properties.widgetEvent.description": "Widget event data", "schemas.common.properties.widgetEvent.displayName": "Widget Event", "schemas.common.properties.widgetEvent.properties.name.description": "Name of the widget", "schemas.common.properties.widgetEvent.properties.name.displayName": "Widget Name", "schemas.common.properties.widgetEvent.properties.timestamp.description": "When the event occurred", "schemas.common.properties.widgetEvent.properties.timestamp.displayName": "Event Timestamp", "schemas.common.properties.widgetEvent.properties.type.description": "Type of the widget", "schemas.common.properties.widgetEvent.properties.type.displayName": "Widget Type", "schemas.common.properties.widgetEvent.properties.widgetId.description": "Unique identifier for the widget", "schemas.common.properties.widgetEvent.properties.widgetId.displayName": "Widget ID", "schemas.common.properties.widgetInstanceEvent.description": "Widget Instance event data", "schemas.common.properties.widgetInstanceEvent.displayName": "Widget Instance Event", "schemas.common.properties.widgetInstanceEvent.properties.name.description": "Name of the instance", "schemas.common.properties.widgetInstanceEvent.properties.name.displayName": "Instance Name", "schemas.common.properties.widgetInstanceEvent.properties.status.description": "Status of the instance", "schemas.common.properties.widgetInstanceEvent.properties.status.displayName": "Instance Status", "schemas.common.properties.widgetInstanceEvent.properties.timestamp.description": "When the event occurred", "schemas.common.properties.widgetInstanceEvent.properties.timestamp.displayName": "Event Timestamp", "schemas.common.properties.widgetInstanceEvent.properties.type.description": "Type of the widget", "schemas.common.properties.widgetInstanceEvent.properties.type.displayName": "Widget Type", "schemas.common.properties.widgetInstanceEvent.properties.widgetId.description": "Identifier of the widget", "schemas.common.properties.widgetInstanceEvent.properties.widgetId.displayName": "Widget ID", "schemas.common.properties.widgetInstanceEvent.properties.widgetInstanceId.description": "Unique identifier for the widget instance", "schemas.common.properties.widgetInstanceEvent.properties.widgetInstanceId.displayName": "Widget Instance ID", "schemas.common.properties.widgetInstanceEvent.properties.workspaceId.description": "Workspace containing the instance", "schemas.common.properties.widgetInstanceEvent.properties.workspaceId.displayName": "Workspace ID", "schemas.output.properties.result.description": "The result from the API call.", "schemas.output.properties.result.displayName": "Result"}, "deca-chatbot": {"actions.create_bot.description": "Create a new chatbot", "actions.create_bot.displayName": "Create <PERSON><PERSON>", "actions.create_bot.properties.avatar.description": "Avatar URL for the bot", "actions.create_bot.properties.avatar.displayName": "Avatar", "actions.create_bot.properties.description.description": "Description of the bot", "actions.create_bot.properties.description.displayName": "Description", "actions.create_bot.properties.isConnected.description": "Whether the bot should be connected", "actions.create_bot.properties.isConnected.displayName": "Is Connected", "actions.create_bot.properties.metadata.description": "Additional metadata for the bot", "actions.create_bot.properties.metadata.displayName": "<PERSON><PERSON><PERSON>", "actions.create_bot.properties.name.description": "Name of the bot", "actions.create_bot.properties.name.displayName": "Name", "actions.create_diagram_version.description": "Create a new version of the diagram", "actions.create_diagram_version.displayName": "Create Diagram Version", "actions.create_diagram_version.properties.botId.description": "ID of the bot to create the diagram version for", "actions.create_diagram_version.properties.botId.displayName": "Bot ID", "actions.create_diagram_version.properties.name.description": "Name of the diagram version", "actions.create_diagram_version.properties.name.displayName": "Name", "actions.create_intent.description": "Create an intent", "actions.create_intent.displayName": "Create Intent", "actions.create_intent.properties.botId.description": "ID of the bot to create the intent for", "actions.create_intent.properties.botId.displayName": "Bot ID", "actions.create_intent.properties.label.description": "Label of the intent", "actions.create_intent.properties.label.displayName": "Label", "actions.create_intent.properties.language.description": "Language of the intent", "actions.create_intent.properties.language.displayName": "Language", "actions.create_intent.properties.utterances.description": "List of utterances for the intent", "actions.create_intent.properties.utterances.displayName": "Utterances", "actions.create_variable.description": "Create a variable", "actions.create_variable.displayName": "Create Variable", "actions.create_variable.properties.botId.description": "ID of the bot to create the variable for", "actions.create_variable.properties.botId.displayName": "Bot ID", "actions.create_variable.properties.defaultValue.description": "Default value of the variable", "actions.create_variable.properties.defaultValue.displayName": "Default Value", "actions.create_variable.properties.description.description": "Description of the variable", "actions.create_variable.properties.description.displayName": "Description", "actions.create_variable.properties.name.description": "Name of the variable", "actions.create_variable.properties.name.displayName": "Name", "actions.delete_bot.description": "Delete an existing chatbot", "actions.delete_bot.displayName": "Delete Bot", "actions.delete_bot.properties.botId.description": "ID of the bot to delete", "actions.delete_bot.properties.botId.displayName": "Bot ID", "actions.delete_diagram_version.description": "Delete a version of the diagram", "actions.delete_diagram_version.displayName": "Delete Diagram Version", "actions.delete_diagram_version.properties.botId.description": "ID of the bot to delete the diagram version for", "actions.delete_diagram_version.properties.botId.displayName": "Bot ID", "actions.delete_diagram_version.properties.version.description": "Version number of the diagram to delete", "actions.delete_diagram_version.properties.version.displayName": "Version", "actions.delete_flow.description": "Delete a flow", "actions.delete_flow.displayName": "Delete Flow", "actions.delete_flow.properties.botId.description": "ID of the bot to delete the flow for", "actions.delete_flow.properties.botId.displayName": "Bot ID", "actions.delete_flow.properties.flowId.description": "ID of the flow to delete", "actions.delete_flow.properties.flowId.displayName": "Flow ID", "actions.delete_integration.description": "Delete a 3rd party integration", "actions.delete_integration.displayName": "Delete 3rd Party Integration", "actions.delete_integration.properties.botId.description": "ID of the bot to delete the integration for", "actions.delete_integration.properties.botId.displayName": "Bot ID", "actions.delete_integration.properties.integrationId.description": "ID of the integration to delete", "actions.delete_integration.properties.integrationId.displayName": "Integration ID", "actions.delete_intent.description": "Delete an intent", "actions.delete_intent.displayName": "Delete Intent", "actions.delete_intent.properties.botId.description": "ID of the bot to delete the intent for", "actions.delete_intent.properties.botId.displayName": "Bot ID", "actions.delete_intent.properties.intentId.description": "ID of the intent to delete", "actions.delete_intent.properties.intentId.displayName": "Intent ID", "actions.delete_variable.description": "Delete a variable", "actions.delete_variable.displayName": "Delete Variable", "actions.delete_variable.properties.botId.description": "ID of the bot to delete the variable for", "actions.delete_variable.properties.botId.displayName": "Bot ID", "actions.delete_variable.properties.variableId.description": "ID of the variable to delete", "actions.delete_variable.properties.variableId.displayName": "Variable ID", "actions.disable_integration.description": "Disable an integration", "actions.disable_integration.displayName": "Disable Integration", "actions.disable_integration.properties.botId.description": "ID of the bot to disable the integration for", "actions.disable_integration.properties.botId.displayName": "Bot ID", "actions.disable_integration.properties.integrationId.description": "ID of the integration to disable", "actions.disable_integration.properties.integrationId.displayName": "Integration ID", "actions.duplicate_flow.description": "Duplicate a flow", "actions.duplicate_flow.displayName": "Duplicate Flow", "actions.duplicate_flow.properties.botId.description": "ID of the bot to duplicate the flow for", "actions.duplicate_flow.properties.botId.displayName": "Bot ID", "actions.duplicate_flow.properties.flowId.description": "ID of the flow to duplicate", "actions.duplicate_flow.properties.flowId.displayName": "Flow ID", "actions.duplicate_flow.properties.name.description": "Name of the flow", "actions.duplicate_flow.properties.name.displayName": "Name", "actions.get_bot.description": "Get an existing chatbot", "actions.get_bot.displayName": "Get Bot", "actions.get_bot.properties.botId.description": "ID of the bot to get", "actions.get_bot.properties.botId.displayName": "Bot ID", "actions.get_bots.description": "Get all chatbots", "actions.get_bots.displayName": "<PERSON>ts", "actions.get_bots.properties.limit.description": "Limit the number of bots to get", "actions.get_bots.properties.limit.displayName": "Limit", "actions.get_bots.properties.next.description": "Next cursor to get the next page of bots", "actions.get_bots.properties.next.displayName": "Next", "actions.get_diagram.description": "Get the diagram for a chatbot", "actions.get_diagram.displayName": "Get Diagram", "actions.get_diagram.properties.botId.description": "ID of the bot to get the diagram for", "actions.get_diagram.properties.botId.displayName": "Bot ID", "actions.get_diagram_version.description": "Get a version of the diagram", "actions.get_diagram_version.displayName": "Get Diagram Version", "actions.get_diagram_version.properties.botId.description": "ID of the bot to get the diagram version for", "actions.get_diagram_version.properties.botId.displayName": "Bot ID", "actions.get_diagram_version.properties.version.description": "Version number of the diagram to get", "actions.get_diagram_version.properties.version.displayName": "Version", "actions.get_diagram_versions.description": "Get all versions of the diagram for a chatbot", "actions.get_diagram_versions.displayName": "Get Diagram Versions", "actions.get_diagram_versions.properties.botId.description": "ID of the bot to get the diagram versions for", "actions.get_diagram_versions.properties.botId.displayName": "Bot ID", "actions.get_diagram_versions.properties.limit.description": "Limit the number of versions to get", "actions.get_diagram_versions.properties.limit.displayName": "Limit", "actions.get_diagram_versions.properties.next.description": "Next cursor to get the next page of versions", "actions.get_diagram_versions.properties.next.displayName": "Next", "actions.get_diagram_versions.properties.type.description": "Type of the diagram version", "actions.get_diagram_versions.properties.type.displayName": "Type", "actions.get_integration.description": "Get an integration", "actions.get_integration.displayName": "Get Integration", "actions.get_integration.properties.botId.description": "ID of the bot to get the integration for", "actions.get_integration.properties.botId.displayName": "Bot ID", "actions.get_integration.properties.integrationId.description": "ID of the integration to get", "actions.get_integration.properties.integrationId.displayName": "Integration ID", "actions.get_integrations.description": "Get all integrations", "actions.get_integrations.displayName": "Get Integrations", "actions.get_integrations.properties.botId.description": "ID of the bot to get the integrations for", "actions.get_integrations.properties.botId.displayName": "Bot ID", "actions.get_integrations.properties.limit.description": "Limit the number of integrations to get", "actions.get_integrations.properties.limit.displayName": "Limit", "actions.get_integrations.properties.next.description": "Next cursor to get the next page of integrations", "actions.get_integrations.properties.next.displayName": "Next", "actions.get_integrations.properties.prev.description": "Prev cursor to get the previous page of integrations", "actions.get_integrations.properties.prev.displayName": "Prev", "actions.get_intent.description": "Get an intent", "actions.get_intent.displayName": "Get Intent", "actions.get_intent.properties.botId.description": "ID of the bot to get the intent for", "actions.get_intent.properties.botId.displayName": "Bot ID", "actions.get_intent.properties.intentId.description": "ID of the intent to get", "actions.get_intent.properties.intentId.displayName": "Intent ID", "actions.get_intents.description": "Get all intents by pagination", "actions.get_intents.displayName": "Get Intents", "actions.get_intents.properties.botId.description": "ID of the bot to get the intents for", "actions.get_intents.properties.botId.displayName": "Bot ID", "actions.get_intents.properties.limit.description": "Limit the number of intents to get", "actions.get_intents.properties.limit.displayName": "Limit", "actions.get_intents.properties.next.description": "Next cursor to get the next page of intents", "actions.get_intents.properties.next.displayName": "Next", "actions.get_intents.properties.prev.description": "Prev cursor to get the previous page of intents", "actions.get_intents.properties.prev.displayName": "Prev", "actions.get_variables.data.description": "Data of the items", "actions.get_variables.data.displayName": "Data", "actions.get_variables.description": "Get all variables by pagination", "actions.get_variables.displayName": "Get Variables", "actions.get_variables.properties.botId.description": "ID of the bot to get the variables for", "actions.get_variables.properties.botId.displayName": "Bot ID", "actions.restore_diagram_version.description": "Restore a version of the diagram", "actions.restore_diagram_version.displayName": "Restore Diagram Version", "actions.restore_diagram_version.properties.botId.description": "ID of the bot to restore the diagram version for", "actions.restore_diagram_version.properties.botId.displayName": "Bot ID", "actions.restore_diagram_version.properties.version.description": "Version number of the diagram to restore", "actions.restore_diagram_version.properties.version.displayName": "Version", "actions.update_bot.description": "Update an existing chatbot", "actions.update_bot.displayName": "Update Bot", "actions.update_bot.properties.avatar.description": "Avatar URL for the bot", "actions.update_bot.properties.avatar.displayName": "Avatar", "actions.update_bot.properties.botId.description": "ID of the bot to update", "actions.update_bot.properties.botId.displayName": "Bot ID", "actions.update_bot.properties.botSettings.description": "Configuration settings for the bot", "actions.update_bot.properties.botSettings.displayName": "<PERSON><PERSON>", "actions.update_bot.properties.botSettings.properties.endConversationMessage.description": "End conversation message settings", "actions.update_bot.properties.botSettings.properties.endConversationMessage.displayName": "End Conversation Message", "actions.update_bot.properties.botSettings.properties.endConversationMessage.properties.enable.description": "Whether to enable end conversation message", "actions.update_bot.properties.botSettings.properties.endConversationMessage.properties.enable.displayName": "Enable", "actions.update_bot.properties.botSettings.properties.endConversationMessage.properties.message.description": "End conversation message text", "actions.update_bot.properties.botSettings.properties.endConversationMessage.properties.message.displayName": "Message", "actions.update_bot.properties.botSettings.properties.intentGlobalNoMatch.description": "Global no match intent setting", "actions.update_bot.properties.botSettings.properties.intentGlobalNoMatch.displayName": "Intent Global No Match", "actions.update_bot.properties.botSettings.properties.timeoutFlowEnabled.description": "Whether timeout flow is enabled", "actions.update_bot.properties.botSettings.properties.timeoutFlowEnabled.displayName": "Timeout Flow Enabled", "actions.update_bot.properties.description.description": "Description of the bot", "actions.update_bot.properties.description.displayName": "Description", "actions.update_bot.properties.isConnected.description": "Whether the bot should be connected", "actions.update_bot.properties.isConnected.displayName": "Is Connected", "actions.update_bot.properties.metadata.description": "Additional metadata for the bot", "actions.update_bot.properties.metadata.displayName": "<PERSON><PERSON><PERSON>", "actions.update_bot.properties.name.description": "Name of the bot", "actions.update_bot.properties.name.displayName": "Name", "actions.update_bot.properties.status.description": "Status of the bot", "actions.update_bot.properties.status.displayName": "Status", "actions.update_flow.description": "Update a flow", "actions.update_flow.displayName": "Update Flow", "actions.update_flow.properties.botId.description": "ID of the bot to update the flow for", "actions.update_flow.properties.botId.displayName": "Bot ID", "actions.update_flow.properties.category.description": "Category of the flow", "actions.update_flow.properties.category.displayName": "Category", "actions.update_flow.properties.description.description": "Description of the flow", "actions.update_flow.properties.description.displayName": "Description", "actions.update_flow.properties.flowId.description": "ID of the flow to update", "actions.update_flow.properties.flowId.displayName": "Flow ID", "actions.update_flow.properties.name.description": "Name of the flow", "actions.update_flow.properties.name.displayName": "Name", "actions.update_intent.description": "Update an intent", "actions.update_intent.displayName": "Update Intent", "actions.update_intent.properties.botId.description": "ID of the bot to update the intent for", "actions.update_intent.properties.botId.displayName": "Bot ID", "actions.update_intent.properties.intentId.description": "ID of the intent to update", "actions.update_intent.properties.intentId.displayName": "Intent ID", "actions.update_intent.properties.label.description": "Label of the intent", "actions.update_intent.properties.label.displayName": "Label", "actions.update_intent.properties.language.description": "Language of the intent", "actions.update_intent.properties.language.displayName": "Language", "actions.update_intent.properties.utterances.description": "List of utterances for the intent", "actions.update_intent.properties.utterances.displayName": "Utterances", "actions.update_variable.description": "Update a variable", "actions.update_variable.displayName": "Update Variable", "actions.update_variable.properties.botId.description": "ID of the bot to update the variable for", "actions.update_variable.properties.botId.displayName": "Bot ID", "actions.update_variable.properties.defaultValue.description": "Default value of the variable", "actions.update_variable.properties.defaultValue.displayName": "Default Value", "actions.update_variable.properties.description.description": "Description of the variable", "actions.update_variable.properties.description.displayName": "Description", "actions.update_variable.properties.name.description": "Name of the variable", "actions.update_variable.properties.name.displayName": "Name", "actions.update_variable.properties.variableId.description": "ID of the variable to update", "actions.update_variable.properties.variableId.displayName": "Variable ID", "description": "Interact with <PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "schemas.bot.properties.avatar.description": "Avatar URL for the bot", "schemas.bot.properties.avatar.displayName": "Avatar", "schemas.bot.properties.created.description": "Timestamp when the bot was created", "schemas.bot.properties.created.displayName": "Created", "schemas.bot.properties.description.description": "Description of the bot", "schemas.bot.properties.description.displayName": "Description", "schemas.bot.properties.diagramVersion.description": "Version number of the bot's diagram", "schemas.bot.properties.diagramVersion.displayName": "Diagram Version", "schemas.bot.properties.id.description": "Unique identifier for the bot", "schemas.bot.properties.id.displayName": "ID", "schemas.bot.properties.isConnected.description": "Whether the bot is currently connected", "schemas.bot.properties.isConnected.displayName": "Is Connected", "schemas.bot.properties.metadata.description": "Additional metadata for the bot", "schemas.bot.properties.metadata.displayName": "<PERSON><PERSON><PERSON>", "schemas.bot.properties.name.description": "Name of the bot", "schemas.bot.properties.name.displayName": "Name", "schemas.bot.properties.organizationId.description": "ID of the organization that owns the bot", "schemas.bot.properties.organizationId.displayName": "Organization ID", "schemas.bot.properties.settings.description": "Configuration settings for the bot", "schemas.bot.properties.settings.displayName": "Settings", "schemas.bot.properties.settings.properties.endConversationMessage.description": "End conversation message settings", "schemas.bot.properties.settings.properties.endConversationMessage.displayName": "End Conversation Message", "schemas.bot.properties.settings.properties.endConversationMessage.properties.enable.description": "Whether to enable end conversation message", "schemas.bot.properties.settings.properties.endConversationMessage.properties.enable.displayName": "Enable", "schemas.bot.properties.settings.properties.endConversationMessage.properties.message.description": "End conversation message text", "schemas.bot.properties.settings.properties.endConversationMessage.properties.message.displayName": "Message", "schemas.bot.properties.settings.properties.intentGlobalNoMatch.description": "Global no match intent setting", "schemas.bot.properties.settings.properties.intentGlobalNoMatch.displayName": "Intent Global No Match", "schemas.bot.properties.settings.properties.timeoutFlowEnabled.description": "Whether timeout flow is enabled", "schemas.bot.properties.settings.properties.timeoutFlowEnabled.displayName": "Timeout Flow Enabled", "schemas.bot.properties.status.description": "Current status of the bot (DRAFT, PUBLISHED, UNPUBLISHED)", "schemas.bot.properties.status.displayName": "Status", "schemas.bot.properties.updated.description": "Timestamp when the bot was last updated", "schemas.bot.properties.updated.displayName": "Updated", "schemas.botsPagination.properties.data.description": "Data of the items", "schemas.botsPagination.properties.data.displayName": "Data", "schemas.botsPagination.properties.pagination.description": "Pagination information", "schemas.botsPagination.properties.pagination.displayName": "Pagination", "schemas.botsPagination.properties.pagination.properties.limit.description": "Number of items to get per page", "schemas.botsPagination.properties.pagination.properties.limit.displayName": "Limit", "schemas.botsPagination.properties.pagination.properties.next.description": "Next cursor to get the next page of items", "schemas.botsPagination.properties.pagination.properties.next.displayName": "Next", "schemas.diagram.properties.flows.description": "Flows in the diagram", "schemas.diagram.properties.flows.displayName": "Flows", "schemas.diagram.properties.flows.properties.botId.description": "ID of the bot this flow belongs to", "schemas.diagram.properties.flows.properties.botId.displayName": "Bot ID", "schemas.diagram.properties.flows.properties.category.description": "Category of the flow", "schemas.diagram.properties.flows.properties.category.displayName": "Category", "schemas.diagram.properties.flows.properties.configs.description": "Configuration for the flow", "schemas.diagram.properties.flows.properties.configs.displayName": "Configs", "schemas.diagram.properties.flows.properties.configs.properties.intentNodes.description": "Intent nodes configuration", "schemas.diagram.properties.flows.properties.configs.properties.intentNodes.displayName": "Intent Nodes", "schemas.diagram.properties.flows.properties.configs.properties.intentNodes.properties.ID.description": "ID of the flow", "schemas.diagram.properties.flows.properties.configs.properties.intentNodes.properties.ID.displayName": "ID", "schemas.diagram.properties.flows.properties.configs.properties.intentNodes.properties.intentId.description": "ID of the intent", "schemas.diagram.properties.flows.properties.configs.properties.intentNodes.properties.intentId.displayName": "Intent ID", "schemas.diagram.properties.flows.properties.configs.properties.intentNodes.properties.intentScope.description": "Scope of the intent", "schemas.diagram.properties.flows.properties.configs.properties.intentNodes.properties.intentScope.displayName": "Intent <PERSON>", "schemas.diagram.properties.flows.properties.created.description": "Timestamp when the flow was created", "schemas.diagram.properties.flows.properties.created.displayName": "Created", "schemas.diagram.properties.flows.properties.id.description": "ID of the flow", "schemas.diagram.properties.flows.properties.id.displayName": "ID", "schemas.diagram.properties.flows.properties.name.description": "Name of the flow", "schemas.diagram.properties.flows.properties.name.displayName": "Name", "schemas.diagram.properties.flows.properties.nodes.description": "Nodes in the flow", "schemas.diagram.properties.flows.properties.nodes.displayName": "Nodes", "schemas.diagram.properties.flows.properties.type.description": "Type of the flow", "schemas.diagram.properties.flows.properties.type.displayName": "Type", "schemas.diagram.properties.flows.properties.updated.description": "Timestamp when the flow was last updated", "schemas.diagram.properties.flows.properties.updated.displayName": "Updated", "schemas.diagram.properties.intents.description": "Intents in the diagram", "schemas.diagram.properties.intents.displayName": "Intents", "schemas.diagram.properties.intents.properties.botId.description": "ID of the bot this intent belongs to", "schemas.diagram.properties.intents.properties.botId.displayName": "Bot ID", "schemas.diagram.properties.intents.properties.created.description": "Timestamp when the intent was created", "schemas.diagram.properties.intents.properties.created.displayName": "Created", "schemas.diagram.properties.intents.properties.id.description": "ID of the intent", "schemas.diagram.properties.intents.properties.id.displayName": "ID", "schemas.diagram.properties.intents.properties.label.description": "Label of the intent", "schemas.diagram.properties.intents.properties.label.displayName": "Label", "schemas.diagram.properties.intents.properties.language.description": "Language of the intent", "schemas.diagram.properties.intents.properties.language.displayName": "Language", "schemas.diagram.properties.intents.properties.updated.description": "Timestamp when the intent was last updated", "schemas.diagram.properties.intents.properties.updated.displayName": "Updated", "schemas.diagram.properties.intents.properties.utterances.description": "List of utterances for the intent", "schemas.diagram.properties.intents.properties.utterances.displayName": "Utterances", "schemas.diagram.properties.settings.description": "Settings for the diagram", "schemas.diagram.properties.settings.displayName": "Settings", "schemas.diagram.properties.variables.description": "Variables in the diagram", "schemas.diagram.properties.variables.displayName": "Variables", "schemas.diagram.properties.variables.properties.botId.description": "ID of the bot this variable belongs to", "schemas.diagram.properties.variables.properties.botId.displayName": "Bot ID", "schemas.diagram.properties.variables.properties.created.description": "Timestamp when the variable was created", "schemas.diagram.properties.variables.properties.created.displayName": "Created", "schemas.diagram.properties.variables.properties.id.description": "ID of the variable", "schemas.diagram.properties.variables.properties.id.displayName": "ID", "schemas.diagram.properties.variables.properties.name.description": "Name of the variable", "schemas.diagram.properties.variables.properties.name.displayName": "Name", "schemas.diagram.properties.variables.properties.updated.description": "Timestamp when the variable was last updated", "schemas.diagram.properties.variables.properties.updated.displayName": "Updated", "schemas.diagram.properties.version.description": "Version number of the diagram", "schemas.diagram.properties.version.displayName": "Version", "schemas.diagramVersion.properties.botId.description": "ID of the bot this diagram version belongs to", "schemas.diagramVersion.properties.botId.displayName": "Bot ID", "schemas.diagramVersion.properties.created.description": "Timestamp when the diagram version was created", "schemas.diagramVersion.properties.created.displayName": "Created", "schemas.diagramVersion.properties.name.description": "Name of the diagram version", "schemas.diagramVersion.properties.name.displayName": "Name", "schemas.diagramVersion.properties.type.description": "Type of the diagram version", "schemas.diagramVersion.properties.type.displayName": "Type", "schemas.diagramVersion.properties.updated.description": "Timestamp when the diagram version was last updated", "schemas.diagramVersion.properties.updated.displayName": "Updated", "schemas.diagramVersion.properties.version.description": "Version number of the diagram", "schemas.diagramVersion.properties.version.displayName": "Version", "schemas.diagramVersionsPagination.properties.data.description": "Data of the items", "schemas.diagramVersionsPagination.properties.data.displayName": "Data", "schemas.diagramVersionsPagination.properties.pagination.description": "Pagination information", "schemas.diagramVersionsPagination.properties.pagination.displayName": "Pagination", "schemas.diagramVersionsPagination.properties.pagination.properties.limit.description": "Number of items to get per page", "schemas.diagramVersionsPagination.properties.pagination.properties.limit.displayName": "Limit", "schemas.diagramVersionsPagination.properties.pagination.properties.next.description": "Next cursor to get the next page of items", "schemas.diagramVersionsPagination.properties.pagination.properties.next.displayName": "Next", "schemas.flow.properties.botId.description": "ID of the bot that owns the flow", "schemas.flow.properties.botId.displayName": "Bot ID", "schemas.flow.properties.category.description": "Category of the flow", "schemas.flow.properties.category.displayName": "Category", "schemas.flow.properties.configs.description": "Configuration for the flow", "schemas.flow.properties.configs.displayName": "Configs", "schemas.flow.properties.configs.properties.intentNodes.description": "Intent nodes configuration", "schemas.flow.properties.configs.properties.intentNodes.displayName": "Intent Nodes", "schemas.flow.properties.configs.properties.intentNodes.properties.ID.description": "ID of the flow", "schemas.flow.properties.configs.properties.intentNodes.properties.ID.displayName": "ID", "schemas.flow.properties.configs.properties.intentNodes.properties.intentId.description": "ID of the intent", "schemas.flow.properties.configs.properties.intentNodes.properties.intentId.displayName": "Intent ID", "schemas.flow.properties.configs.properties.intentNodes.properties.intentScope.description": "Scope of the intent", "schemas.flow.properties.configs.properties.intentNodes.properties.intentScope.displayName": "Intent <PERSON>", "schemas.flow.properties.creator.description": "Creator of the flow", "schemas.flow.properties.creator.displayName": "Creator", "schemas.flow.properties.description.description": "Description of the flow", "schemas.flow.properties.description.displayName": "Description", "schemas.flow.properties.id.description": "ID of the flow", "schemas.flow.properties.id.displayName": "ID", "schemas.flow.properties.name.description": "Name of the flow", "schemas.flow.properties.name.displayName": "Name", "schemas.flow.properties.nodes.description": "Nodes of the flow", "schemas.flow.properties.nodes.displayName": "Nodes", "schemas.flow.properties.type.description": "Type of the flow ('start', 'timeout', 'error', 'end', 'normal')", "schemas.flow.properties.type.displayName": "Type", "schemas.integration.properties.botId.description": "ID of the bot that owns the integration", "schemas.integration.properties.botId.displayName": "Bot ID", "schemas.integration.properties.configs.description": "Configuration for the integration", "schemas.integration.properties.configs.displayName": "Configs", "schemas.integration.properties.created.description": "Timestamp when the integration was created", "schemas.integration.properties.created.displayName": "Created", "schemas.integration.properties.externalId.description": "External ID of the integration", "schemas.integration.properties.externalId.displayName": "External ID", "schemas.integration.properties.integrationId.description": "ID of the integration", "schemas.integration.properties.integrationId.displayName": "Integration ID", "schemas.integration.properties.integrationType.description": "Type of the integration", "schemas.integration.properties.integrationType.displayName": "Integration Type", "schemas.integration.properties.metadata.description": "Additional metadata for the integration", "schemas.integration.properties.metadata.displayName": "<PERSON><PERSON><PERSON>", "schemas.integration.properties.name.description": "Name of the integration", "schemas.integration.properties.name.displayName": "Name", "schemas.integration.properties.ocsChannelId.description": "OCS Channel ID of the integration", "schemas.integration.properties.ocsChannelId.displayName": "OCS Channel ID", "schemas.integration.properties.organizationId.description": "ID of the organization that owns the integration", "schemas.integration.properties.organizationId.displayName": "Organization ID", "schemas.integration.properties.platform.description": "Platform of the integration, values 'CHATBOX', 'WEB', 'LINE'", "schemas.integration.properties.platform.displayName": "Platform", "schemas.integration.properties.status.description": "Status of the integration, values 'ENABLE', 'DISABLE', 'PENDING'", "schemas.integration.properties.status.displayName": "Status", "schemas.integration.properties.updated.description": "Timestamp when the integration was last updated", "schemas.integration.properties.updated.displayName": "Updated", "schemas.integrationsPagination.properties.data.description": "Data of the items", "schemas.integrationsPagination.properties.data.displayName": "Data", "schemas.integrationsPagination.properties.pagination.description": "Pagination information", "schemas.integrationsPagination.properties.pagination.displayName": "Pagination", "schemas.integrationsPagination.properties.pagination.properties.limit.description": "Number of items to get per page", "schemas.integrationsPagination.properties.pagination.properties.limit.displayName": "Limit", "schemas.integrationsPagination.properties.pagination.properties.next.description": "Next cursor to get the next page of items", "schemas.integrationsPagination.properties.pagination.properties.next.displayName": "Next", "schemas.intent.properties.botId.description": "ID of the bot that owns the intent", "schemas.intent.properties.botId.displayName": "Bot ID", "schemas.intent.properties.id.description": "ID of the intent", "schemas.intent.properties.id.displayName": "ID", "schemas.intent.properties.label.description": "Label of the intent", "schemas.intent.properties.label.displayName": "Label", "schemas.intent.properties.language.description": "Language of the intent", "schemas.intent.properties.language.displayName": "Language", "schemas.intent.properties.utterances.description": "List of utterances for the intent", "schemas.intent.properties.utterances.displayName": "Utterances", "schemas.intentsPagination.properties.data.description": "Data of the items", "schemas.intentsPagination.properties.data.displayName": "Data", "schemas.intentsPagination.properties.pagination.description": "Pagination information", "schemas.intentsPagination.properties.pagination.displayName": "Pagination", "schemas.intentsPagination.properties.pagination.properties.limit.description": "Number of items to get per page", "schemas.intentsPagination.properties.pagination.properties.limit.displayName": "Limit", "schemas.intentsPagination.properties.pagination.properties.next.description": "Next cursor to get the next page of items", "schemas.intentsPagination.properties.pagination.properties.next.displayName": "Next", "schemas.receivedMessageEvent.properties.botId.description": "ID of the bot", "schemas.receivedMessageEvent.properties.botId.displayName": "Bot ID", "schemas.receivedMessageEvent.properties.data.properties.events.description": "Events of the conversation", "schemas.receivedMessageEvent.properties.data.properties.events.displayName": "Events", "schemas.receivedMessageEvent.properties.data.properties.events.items.properties.message.properties.data.description": "Data of the message", "schemas.receivedMessageEvent.properties.data.properties.events.items.properties.message.properties.data.displayName": "Data", "schemas.receivedMessageEvent.properties.data.properties.events.items.properties.message.properties.type.description": "Type of the message", "schemas.receivedMessageEvent.properties.data.properties.events.items.properties.message.properties.type.displayName": "Type", "schemas.receivedMessageEvent.properties.data.properties.events.items.properties.source.properties.type.description": "Type of the source", "schemas.receivedMessageEvent.properties.data.properties.events.items.properties.source.properties.type.displayName": "Type", "schemas.receivedMessageEvent.properties.data.properties.events.items.properties.source.properties.userId.description": "ID of the user", "schemas.receivedMessageEvent.properties.data.properties.events.items.properties.source.properties.userId.displayName": "User ID", "schemas.receivedMessageEvent.properties.data.properties.events.items.properties.type.description": "Type of the event", "schemas.receivedMessageEvent.properties.data.properties.events.items.properties.type.displayName": "Type", "schemas.receivedMessageEvent.properties.data.properties.events.items.properties.variables.description": "Variables", "schemas.receivedMessageEvent.properties.data.properties.events.items.properties.variables.displayName": "Variables", "schemas.receivedMessageEvent.properties.integrationId.description": "ID of the integration", "schemas.receivedMessageEvent.properties.integrationId.displayName": "Integration ID", "schemas.receivedMessageEvent.properties.organizationId.description": "ID of the organization", "schemas.receivedMessageEvent.properties.organizationId.displayName": "Organization ID", "schemas.receivedMessageEvent.properties.platform.description": "Platform converastion (line, web)", "schemas.receivedMessageEvent.properties.platform.displayName": "Platform", "schemas.receivedMessageEvent.properties.userId.description": "ID of the user", "schemas.receivedMessageEvent.properties.userId.displayName": "User ID", "schemas.sentMessageEvent.properties.channelId.description": "ID of the channel", "schemas.sentMessageEvent.properties.channelId.displayName": "Channel ID", "schemas.sentMessageEvent.properties.content.properties.channels.description": "Websocket channels (Use in message sent to WEB)", "schemas.sentMessageEvent.properties.content.properties.channels.displayName": "Channels", "schemas.sentMessageEvent.properties.content.properties.data.description": "Data of the sent message (Use in message sent to WEB)", "schemas.sentMessageEvent.properties.content.properties.data.displayName": "Data", "schemas.sentMessageEvent.properties.content.properties.data.properties.messages.description": "Messages of the event (Use in message sent to WEB)", "schemas.sentMessageEvent.properties.content.properties.data.properties.messages.displayName": "Messages", "schemas.sentMessageEvent.properties.content.properties.data.properties.messages.items.properties.data.description": "Data of the message (The structure is different depending on the type of message)", "schemas.sentMessageEvent.properties.content.properties.data.properties.messages.items.properties.data.displayName": "Data", "schemas.sentMessageEvent.properties.content.properties.data.properties.messages.items.properties.from.description": "The message sent from", "schemas.sentMessageEvent.properties.content.properties.data.properties.messages.items.properties.from.displayName": "From", "schemas.sentMessageEvent.properties.content.properties.data.properties.messages.items.properties.type.description": "Type of the message", "schemas.sentMessageEvent.properties.content.properties.data.properties.messages.items.properties.type.displayName": "Type", "schemas.sentMessageEvent.properties.content.properties.messages.description": "Messages of the event (Use in message sent to LINE)", "schemas.sentMessageEvent.properties.content.properties.messages.displayName": "Messages", "schemas.sentMessageEvent.properties.content.properties.replyToken.description": "Reply token of the event (Use in message sent to LINE)", "schemas.sentMessageEvent.properties.content.properties.replyToken.displayName": "<PERSON><PERSON>", "schemas.sentMessageEvent.properties.content.properties.userId.description": "ID of the user (Use in message sent to LINE)", "schemas.sentMessageEvent.properties.content.properties.userId.displayName": "User ID", "schemas.sentMessageEvent.properties.traits.description": "Traits of the event", "schemas.sentMessageEvent.properties.traits.displayName": "Trai<PERSON>", "schemas.sentMessageEvent.properties.traits.properties.botId.description": "ID of the bot", "schemas.sentMessageEvent.properties.traits.properties.botId.displayName": "Bot ID", "schemas.sentMessageEvent.properties.traits.properties.integrationId.description": "ID of the integration", "schemas.sentMessageEvent.properties.traits.properties.integrationId.displayName": "Integration ID", "schemas.sentMessageEvent.properties.traits.properties.organizationId.description": "ID of the organization", "schemas.sentMessageEvent.properties.traits.properties.organizationId.displayName": "Organization ID", "schemas.variable.properties.botId.description": "ID of the bot that owns the variable", "schemas.variable.properties.botId.displayName": "Bot ID", "schemas.variable.properties.created.description": "Timestamp when the variable was created", "schemas.variable.properties.created.displayName": "Created", "schemas.variable.properties.defaultValue.description": "Default value of the variable", "schemas.variable.properties.defaultValue.displayName": "Default Value", "schemas.variable.properties.description.description": "Description of the variable", "schemas.variable.properties.description.displayName": "Description", "schemas.variable.properties.id.description": "ID of the variable", "schemas.variable.properties.id.displayName": "ID", "schemas.variable.properties.name.description": "Name of the variable", "schemas.variable.properties.name.displayName": "Name", "schemas.variable.properties.updated.description": "Timestamp when the variable was last updated", "schemas.variable.properties.updated.displayName": "Updated", "triggers.deca.chatbot.message.received.description": "", "triggers.deca.chatbot.message.received.displayName": "", "triggers.deca.chatbot.message.sent.description": "", "triggers.deca.chatbot.message.sent.displayName": ""}, "deca-crm": {"actions.field_create.description": "Create a new field", "actions.field_create.displayName": "Create Field", "actions.field_create.properties.data.description": "Field definition data", "actions.field_create.properties.data.displayName": "Data", "actions.field_create.properties.objectId.description": "ID of the object containing the view", "actions.field_create.properties.objectId.displayName": "Object ID", "actions.field_create.properties.workspaceId.description": "ID of the workspace", "actions.field_create.properties.workspaceId.displayName": "Workspace ID", "actions.field_delete.description": "Delete a field", "actions.field_delete.displayName": "Delete Field", "actions.field_delete.properties.fieldId.description": "ID of the field to delete", "actions.field_delete.properties.fieldId.displayName": "Field ID", "actions.field_delete.properties.objectId.description": "ID of the object", "actions.field_delete.properties.objectId.displayName": "Object ID", "actions.field_delete.properties.workspaceId.description": "ID of the workspace", "actions.field_delete.properties.workspaceId.displayName": "Workspace ID", "actions.field_update.description": "Update an existing field", "actions.field_update.displayName": "Update Field", "actions.field_update.properties.data.description": "Field update data", "actions.field_update.properties.data.displayName": "Data", "actions.field_update.properties.fieldId.description": "ID of the field to update", "actions.field_update.properties.fieldId.displayName": "Field ID", "actions.field_update.properties.objectId.description": "ID of the object containing the view", "actions.field_update.properties.objectId.displayName": "Object ID", "actions.field_update.properties.workspaceId.description": "ID of the workspace", "actions.field_update.properties.workspaceId.displayName": "Workspace ID", "actions.object_create.description": "Create a new object in a workspace", "actions.object_create.displayName": "Create Object", "actions.object_create.properties.data.description": "Data of the object", "actions.object_create.properties.data.displayName": "Data", "actions.object_create.properties.workspaceId.description": "ID of the workspace to create the object in", "actions.object_create.properties.workspaceId.displayName": "Workspace ID", "actions.object_delete.description": "Delete an object", "actions.object_delete.displayName": "Delete Object", "actions.object_delete.properties.objectId.description": "ID of the object to delete", "actions.object_delete.properties.objectId.displayName": "Object ID", "actions.object_delete.properties.workspaceId.description": "ID of the workspace to list objects from", "actions.object_delete.properties.workspaceId.displayName": "Workspace ID", "actions.object_list.description": "Get a list of all objects in a workspace", "actions.object_list.displayName": "List Objects", "actions.object_list.properties.workspaceId.description": "ID of the workspace to list objects from", "actions.object_list.properties.workspaceId.displayName": "Workspace ID", "actions.object_retrieve.description": "Get an object by ID", "actions.object_retrieve.displayName": "Get Object", "actions.object_retrieve.properties.objectId.description": "ID of the object to retrieve", "actions.object_retrieve.properties.objectId.displayName": "Object ID", "actions.object_retrieve.properties.workspaceId.description": "ID of the workspace to list objects from", "actions.object_retrieve.properties.workspaceId.displayName": "Workspace ID", "actions.object_update.description": "Update an existing object", "actions.object_update.displayName": "Update Object", "actions.object_update.properties.data.description": "Data of the object", "actions.object_update.properties.data.displayName": "Data", "actions.object_update.properties.objectId.description": "ID of the object to update", "actions.object_update.properties.objectId.displayName": "Object ID", "actions.object_update.properties.workspaceId.description": "ID of the workspace to list objects from", "actions.object_update.properties.workspaceId.displayName": "Workspace ID", "actions.record_create.description": "Create a new record", "actions.record_create.displayName": "Create Record", "actions.record_create.properties.data.description": "Record data with field values", "actions.record_create.properties.data.displayName": "Data", "actions.record_create.properties.objectId.description": "ID of the object", "actions.record_create.properties.objectId.displayName": "Object ID", "actions.record_create.properties.workspaceId.description": "ID of the workspace", "actions.record_create.properties.workspaceId.displayName": "Workspace ID", "actions.record_delete.description": "Delete a record", "actions.record_delete.displayName": "Delete Record", "actions.record_delete.properties.objectId.description": "ID of the object", "actions.record_delete.properties.objectId.displayName": "Object ID", "actions.record_delete.properties.recordId.description": "ID of the record", "actions.record_delete.properties.recordId.displayName": "Record ID", "actions.record_delete.properties.workspaceId.description": "ID of the workspace", "actions.record_delete.properties.workspaceId.displayName": "Workspace ID", "actions.record_list.description": "Get a list of all records of an object in a workspace", "actions.record_list.displayName": "List Records", "actions.record_list.properties.filters.description": "Filter conditions to apply to the record list. Only supported when using objectId (ignored when using viewId).", "actions.record_list.properties.filters.displayName": "Filters", "actions.record_list.properties.limit.description": "Maximum number of records to return", "actions.record_list.properties.limit.displayName": "Limit", "actions.record_list.properties.objectId.description": "ID of the object to list records from. Either objectId or viewId must be provided. Use this when you need filtering and sorting capabilities.", "actions.record_list.properties.objectId.displayName": "Object ID", "actions.record_list.properties.offset.description": "Number of records to skip before returning results", "actions.record_list.properties.offset.displayName": "Offset", "actions.record_list.properties.sort.description": "Sort configurations for the record list. Only supported when using objectId (ignored when using viewId).", "actions.record_list.properties.sort.displayName": "Sort", "actions.record_list.properties.viewId.description": "ID of the view to list records from. Either objectId or viewId must be provided. Note: filters and sort parameters are ignored when using viewId.", "actions.record_list.properties.viewId.displayName": "View ID", "actions.record_list.properties.workspaceId.description": "ID of the workspace the record belongs to", "actions.record_list.properties.workspaceId.displayName": "Workspace ID", "actions.record_retrieve.description": "Get a record by ID", "actions.record_retrieve.displayName": "Get Record", "actions.record_retrieve.properties.objectId.description": "ID of the object", "actions.record_retrieve.properties.objectId.displayName": "Object ID", "actions.record_retrieve.properties.recordId.description": "ID of the record", "actions.record_retrieve.properties.recordId.displayName": "Record ID", "actions.record_retrieve.properties.workspaceId.description": "ID of the workspace", "actions.record_retrieve.properties.workspaceId.displayName": "Workspace ID", "actions.record_update.description": "Update an existing record", "actions.record_update.displayName": "Update Record", "actions.record_update.properties.data.description": "Record data with field values to update", "actions.record_update.properties.data.displayName": "Data", "actions.record_update.properties.objectId.description": "ID of the object", "actions.record_update.properties.objectId.displayName": "Object ID", "actions.record_update.properties.recordId.description": "ID of the record", "actions.record_update.properties.recordId.displayName": "Record ID", "actions.record_update.properties.workspaceId.description": "ID of the workspace", "actions.record_update.properties.workspaceId.displayName": "Workspace ID", "actions.view_create.description": "Create a new view", "actions.view_create.displayName": "Create View", "actions.view_create.properties.data.description": "View creation data", "actions.view_create.properties.data.displayName": "Data", "actions.view_create.properties.objectId.description": "ID of the object containing the view", "actions.view_create.properties.objectId.displayName": "Object ID", "actions.view_create.properties.workspaceId.description": "ID of the workspace", "actions.view_create.properties.workspaceId.displayName": "Workspace ID", "actions.view_delete.description": "Delete a view", "actions.view_delete.displayName": "Delete View", "actions.view_delete.properties.objectId.description": "ID of the object containing the view", "actions.view_delete.properties.objectId.displayName": "Object ID", "actions.view_delete.properties.viewId.description": "ID of the view to retrieve", "actions.view_delete.properties.viewId.displayName": "View ID", "actions.view_delete.properties.workspaceId.description": "ID of the workspace", "actions.view_delete.properties.workspaceId.displayName": "Workspace ID", "actions.view_list.description": "Get a list of all views of an object in a workspace", "actions.view_list.displayName": "List Views", "actions.view_list.properties.objectId.description": "ID of the object to list views from", "actions.view_list.properties.objectId.displayName": "Object ID", "actions.view_list.properties.workspaceId.description": "ID of the workspace to list objects from", "actions.view_list.properties.workspaceId.displayName": "Workspace ID", "actions.view_retrieve.description": "Get a view by ID", "actions.view_retrieve.displayName": "Get View", "actions.view_retrieve.properties.objectId.description": "ID of the object containing the view", "actions.view_retrieve.properties.objectId.displayName": "Object ID", "actions.view_retrieve.properties.viewId.description": "ID of the view to retrieve", "actions.view_retrieve.properties.viewId.displayName": "View ID", "actions.view_retrieve.properties.workspaceId.description": "ID of the workspace", "actions.view_retrieve.properties.workspaceId.displayName": "Workspace ID", "actions.workspace_create.description": "Create a new workspace", "actions.workspace_create.displayName": "Create Workspace", "actions.workspace_create.properties.data.description": "Data of the workspace", "actions.workspace_create.properties.data.displayName": "Data", "actions.workspace_create.properties.data.properties.description.description": "Description of the workspace", "actions.workspace_create.properties.data.properties.description.displayName": "Description", "actions.workspace_create.properties.data.properties.name.description": "Name of the workspace", "actions.workspace_create.properties.data.properties.name.displayName": "Name", "actions.workspace_list.description": "Get a list of all workspaces", "actions.workspace_list.displayName": "List Workspaces", "actions.workspace_retrieve.description": "Get a workspace by ID", "actions.workspace_retrieve.displayName": "Get Workspace", "actions.workspace_retrieve.properties.workspaceId.description": "ID of the workspace to retrieve", "actions.workspace_retrieve.properties.workspaceId.displayName": "Workspace ID", "actions.workspace_update.description": "Update an existing workspace", "actions.workspace_update.displayName": "Update Workspace", "actions.workspace_update.properties.data.description": "Data of the workspace", "actions.workspace_update.properties.data.displayName": "Data", "actions.workspace_update.properties.data.properties.description.description": "Description of the workspace", "actions.workspace_update.properties.data.properties.description.displayName": "Description", "actions.workspace_update.properties.data.properties.name.description": "Name of the workspace", "actions.workspace_update.properties.data.properties.name.displayName": "Name", "actions.workspace_update.properties.workspaceId.description": "ID of the workspace to update", "actions.workspace_update.properties.workspaceId.displayName": "Workspace ID", "description": "Interact with CRM to manage workspaces, objects, records, and more.", "displayName": "CRM", "schemas.common.properties.active.description": "Whether the item is active", "schemas.common.properties.active.displayName": "Active", "schemas.common.properties.description.description": "Description", "schemas.common.properties.description.displayName": "Description", "schemas.common.properties.id.description": "Unique identifier", "schemas.common.properties.id.displayName": "ID", "schemas.common.properties.name.description": "Name", "schemas.common.properties.name.displayName": "Name", "schemas.common.properties.objectId.description": "ID of the object the item belongs to", "schemas.common.properties.objectId.displayName": "Object ID", "schemas.common.properties.orgId.description": "Unique identifier for the organization", "schemas.common.properties.orgId.displayName": "Organization ID", "schemas.common.properties.workspaceId.description": "Unique identifier for the workspace", "schemas.common.properties.workspaceId.displayName": "Workspace ID", "schemas.event.properties.objectId.description": "ID of the object", "schemas.event.properties.objectId.displayName": "Object ID", "schemas.event.properties.orgId.description": "ID of the organization", "schemas.event.properties.orgId.displayName": "Organization ID", "schemas.event.properties.recordId.description": "ID of the record", "schemas.event.properties.recordId.displayName": "Record ID", "schemas.event.properties.sentAt.description": "Timestamp when the event was sent", "schemas.event.properties.sentAt.displayName": "<PERSON><PERSON>", "schemas.event.properties.workspaceId.description": "ID of the workspace", "schemas.event.properties.workspaceId.displayName": "Workspace ID", "schemas.field.properties.description.description": "Description of the field's purpose", "schemas.field.properties.description.displayName": "Description", "schemas.field.properties.id.description": "Unique identifier for the field", "schemas.field.properties.id.displayName": "ID", "schemas.field.properties.isProtected.description": "Whether the field is protected from modifications", "schemas.field.properties.isProtected.displayName": "Is Protected", "schemas.field.properties.name.description": "Display name of the field", "schemas.field.properties.name.displayName": "Name", "schemas.field.properties.options.description": "Configuration options specific to the field type", "schemas.field.properties.options.displayName": "Options", "schemas.field.properties.required.description": "Whether the field is required", "schemas.field.properties.required.displayName": "Required", "schemas.field.properties.type.description": "Type of the field (e.g., text, number, currency, etc.)", "schemas.field.properties.type.displayName": "Type", "schemas.field.properties.uniqued.description": "Whether the field is uniqued", "schemas.field.properties.uniqued.displayName": "Uniqued", "schemas.object.properties.childObjects.description": "List of child object identifiers", "schemas.object.properties.childObjects.displayName": "Child Objects", "schemas.object.properties.createdAt.description": "Timestamp when the object was created", "schemas.object.properties.createdAt.displayName": "Created At", "schemas.object.properties.description.description": "Description of the object", "schemas.object.properties.description.displayName": "Description", "schemas.object.properties.displaySettings.description": "Visual display configuration for the object", "schemas.object.properties.displaySettings.displayName": "Display Settings", "schemas.object.properties.fields.description": "List of field definitions for the object", "schemas.object.properties.fields.displayName": "Fields", "schemas.object.properties.hasAvatar.description": "Whether the object has an avatar", "schemas.object.properties.hasAvatar.displayName": "Has <PERSON>", "schemas.object.properties.icon.description": "Icon identifier for the object", "schemas.object.properties.icon.displayName": "Icon", "schemas.object.properties.id.description": "Unique identifier for the object", "schemas.object.properties.id.displayName": "ID", "schemas.object.properties.messaging.description": "Messaging configuration options", "schemas.object.properties.messaging.displayName": "Messaging", "schemas.object.properties.messaging.properties.email.displayName": "Email", "schemas.object.properties.messaging.properties.line.displayName": "Line", "schemas.object.properties.messaging.properties.sms.displayName": "SMS", "schemas.object.properties.name.description": "Object name in singular and plural forms", "schemas.object.properties.name.displayName": "Name", "schemas.object.properties.name.properties.plural.description": "Plural form of the object name", "schemas.object.properties.name.properties.plural.displayName": "Plural", "schemas.object.properties.name.properties.singular.description": "Singular form of the object name", "schemas.object.properties.name.properties.singular.displayName": "Singular", "schemas.object.properties.orgId.description": "ID of the organization this object belongs to", "schemas.object.properties.orgId.displayName": "Organization ID", "schemas.object.properties.profileSettings.description": "Profile settings for the object", "schemas.object.properties.profileSettings.displayName": "Profile Settings", "schemas.object.properties.profileSettings.items.properties.enabled.description": "Whether the profile setting is enabled", "schemas.object.properties.profileSettings.items.properties.enabled.displayName": "Enabled", "schemas.object.properties.profileSettings.items.properties.type.description": "Type of profile setting", "schemas.object.properties.profileSettings.items.properties.type.displayName": "Type", "schemas.object.properties.references.description": "Map of fields to references from other objects", "schemas.object.properties.references.displayName": "References", "schemas.object.properties.updatedAt.description": "Timestamp when the object was last updated", "schemas.object.properties.updatedAt.displayName": "Updated At", "schemas.object.properties.viewGroups.description": "List of view group configurations for the object", "schemas.object.properties.viewGroups.displayName": "View Groups", "schemas.object.properties.views.description": "List of view configurations for the object", "schemas.object.properties.views.displayName": "Views", "schemas.object.properties.workspaceId.description": "ID of the workspace this object belongs to", "schemas.object.properties.workspaceId.displayName": "Workspace ID", "schemas.objectMetadata.properties.description.description": "Description of the object", "schemas.objectMetadata.properties.description.displayName": "Description", "schemas.objectMetadata.properties.displaySettings.description": "Display settings for the object", "schemas.objectMetadata.properties.displaySettings.displayName": "Display Settings", "schemas.objectMetadata.properties.fields.description": "Fields for the object", "schemas.objectMetadata.properties.fields.displayName": "Fields", "schemas.objectMetadata.properties.hasAvatar.description": "Whether the object has an avatar", "schemas.objectMetadata.properties.hasAvatar.displayName": "Has <PERSON>", "schemas.objectMetadata.properties.icon.description": "Icon identifier for the object", "schemas.objectMetadata.properties.icon.displayName": "Icon", "schemas.objectMetadata.properties.messaging.description": "Messaging settings for the object", "schemas.objectMetadata.properties.messaging.displayName": "Messaging", "schemas.objectMetadata.properties.messaging.properties.email.description": "Whether the email is enabled", "schemas.objectMetadata.properties.messaging.properties.email.displayName": "Email", "schemas.objectMetadata.properties.messaging.properties.line.description": "Whether the line is enabled", "schemas.objectMetadata.properties.messaging.properties.line.displayName": "Line", "schemas.objectMetadata.properties.messaging.properties.sms.description": "Whether the SMS is enabled", "schemas.objectMetadata.properties.messaging.properties.sms.displayName": "SMS", "schemas.objectMetadata.properties.name.description": "Name of the object", "schemas.objectMetadata.properties.name.displayName": "Name", "schemas.objectMetadata.properties.name.properties.plural.description": "Plural form of the object name", "schemas.objectMetadata.properties.name.properties.plural.displayName": "Plural Name", "schemas.objectMetadata.properties.name.properties.singular.description": "Singular form of the object name", "schemas.objectMetadata.properties.name.properties.singular.displayName": "Singular Name", "schemas.objectMetadata.properties.profileSettings.description": "Profile settings for the object", "schemas.objectMetadata.properties.profileSettings.displayName": "Profile Settings", "schemas.objectMetadata.properties.profileSettings.items.properties.enabled.description": "Whether the profile setting is enabled", "schemas.objectMetadata.properties.profileSettings.items.properties.enabled.displayName": "Enabled", "schemas.objectMetadata.properties.profileSettings.items.properties.type.description": "Type of the profile setting", "schemas.objectMetadata.properties.profileSettings.items.properties.type.displayName": "Type", "schemas.record.properties.autoId.description": "Automatically generated sequential ID for the record", "schemas.record.properties.autoId.displayName": "Auto ID", "schemas.record.properties.createdAt.description": "Timestamp when the record was created", "schemas.record.properties.createdAt.displayName": "Created At", "schemas.record.properties.createdBy.description": "User who created the record", "schemas.record.properties.createdBy.displayName": "Created By", "schemas.record.properties.createdBy.properties.email.description": "Email of the user", "schemas.record.properties.createdBy.properties.email.displayName": "Email", "schemas.record.properties.createdBy.properties.id.description": "Unique identifier for the user", "schemas.record.properties.createdBy.properties.id.displayName": "ID", "schemas.record.properties.createdBy.properties.name.description": "Name of the user", "schemas.record.properties.createdBy.properties.name.displayName": "Name", "schemas.record.properties.createdBy.properties.picture.description": "URL to the user's profile picture", "schemas.record.properties.createdBy.properties.picture.displayName": "Picture", "schemas.record.properties.id.description": "Unique identifier for the record", "schemas.record.properties.id.displayName": "ID", "schemas.record.properties.updatedAt.description": "Timestamp when the record was last updated", "schemas.record.properties.updatedAt.displayName": "Updated At", "schemas.record.properties.updatedBy.description": "User who last updated the record", "schemas.record.properties.updatedBy.displayName": "Updated By", "schemas.record.properties.updatedBy.properties.email.description": "Email of the user", "schemas.record.properties.updatedBy.properties.email.displayName": "Email", "schemas.record.properties.updatedBy.properties.id.description": "Unique identifier for the user", "schemas.record.properties.updatedBy.properties.id.displayName": "ID", "schemas.record.properties.updatedBy.properties.name.description": "Name of the user", "schemas.record.properties.updatedBy.properties.name.displayName": "Name", "schemas.record.properties.updatedBy.properties.picture.description": "URL to the user's profile picture", "schemas.record.properties.updatedBy.properties.picture.displayName": "Picture", "schemas.timestamps.properties.createdAt.description": "Timestamp when the item was created", "schemas.timestamps.properties.createdAt.displayName": "Created At", "schemas.timestamps.properties.deletedAt.description": "Timestamp when the item was deleted", "schemas.timestamps.properties.deletedAt.displayName": "Deleted At", "schemas.timestamps.properties.updatedAt.description": "Timestamp when the item was last updated", "schemas.timestamps.properties.updatedAt.displayName": "Updated At", "schemas.user.properties.email.description": "Email of the user", "schemas.user.properties.email.displayName": "Email", "schemas.user.properties.id.description": "Unique identifier for the user", "schemas.user.properties.id.displayName": "ID", "schemas.user.properties.name.description": "Name of the user", "schemas.user.properties.name.displayName": "Name", "schemas.user.properties.picture.description": "URL to the user's profile picture", "schemas.user.properties.picture.displayName": "Picture", "schemas.view.properties.createdAt.description": "Timestamp when the view was created", "schemas.view.properties.createdAt.displayName": "Created At", "schemas.view.properties.createdBy.description": "ID of the user who created the view", "schemas.view.properties.createdBy.displayName": "Created By", "schemas.view.properties.description.description": "Description of the view", "schemas.view.properties.description.displayName": "Description", "schemas.view.properties.detailFieldOrder.description": "Ordered list of field IDs for the detail view", "schemas.view.properties.detailFieldOrder.displayName": "Detail Field Order", "schemas.view.properties.fieldOrder.description": "Ordered list of field IDs defining their display order", "schemas.view.properties.fieldOrder.displayName": "Field Order", "schemas.view.properties.fields.description": "List of field configurations in the view", "schemas.view.properties.fields.displayName": "Fields", "schemas.view.properties.filters.description": "Filter conditions applied to the view", "schemas.view.properties.filters.displayName": "Filters", "schemas.view.properties.icon.description": "Icon identifier for the view", "schemas.view.properties.icon.displayName": "Icon", "schemas.view.properties.id.description": "Unique identifier for the view", "schemas.view.properties.id.displayName": "ID", "schemas.view.properties.name.description": "Name of the view", "schemas.view.properties.name.displayName": "Name", "schemas.view.properties.objectId.description": "ID of the object the view belongs to", "schemas.view.properties.objectId.displayName": "Object ID", "schemas.view.properties.orgId.description": "ID of the organization this view belongs to", "schemas.view.properties.orgId.displayName": "Organization ID", "schemas.view.properties.rowHeight.description": "Height of rows in the view", "schemas.view.properties.rowHeight.displayName": "Row Height", "schemas.view.properties.sort.description": "Sort configurations for the view", "schemas.view.properties.sort.displayName": "Sort", "schemas.view.properties.textSearch.description": "Text search query applied to the view", "schemas.view.properties.textSearch.displayName": "Text Search", "schemas.view.properties.type.description": "Type of view (e.g., grid, kanban, etc.)", "schemas.view.properties.type.displayName": "Type", "schemas.view.properties.updatedAt.description": "Timestamp when the view was last updated", "schemas.view.properties.updatedAt.displayName": "Updated At", "schemas.workspace.properties.active.description": "Whether the workspace is active", "schemas.workspace.properties.active.displayName": "Active", "schemas.workspace.properties.createdAt.description": "Timestamp when the workspace was created", "schemas.workspace.properties.createdAt.displayName": "Created At", "schemas.workspace.properties.deletedAt.description": "Timestamp when the workspace was deleted, null if not deleted", "schemas.workspace.properties.deletedAt.displayName": "Deleted At", "schemas.workspace.properties.description.description": "Description of the workspace", "schemas.workspace.properties.description.displayName": "Description", "schemas.workspace.properties.id.description": "Unique identifier for the workspace", "schemas.workspace.properties.id.displayName": "ID", "schemas.workspace.properties.name.description": "Name of the workspace", "schemas.workspace.properties.name.displayName": "Name", "schemas.workspace.properties.orgId.displayName": "Organization ID", "schemas.workspace.properties.updatedAt.description": "Timestamp when the workspace was last updated", "schemas.workspace.properties.updatedAt.displayName": "Updated At", "triggers.deca.crm.record.created.description": "", "triggers.deca.crm.record.created.displayName": "", "triggers.deca.crm.record.deleted.description": "", "triggers.deca.crm.record.deleted.displayName": "", "triggers.deca.crm.record.updated.description": "", "triggers.deca.crm.record.updated.displayName": ""}, "deca-forms": {"actions.form_create.description": "Create a new form", "actions.form_create.displayName": "Create Form", "actions.form_create.properties.expiredAt.description": "Form end date & time", "actions.form_create.properties.expiredAt.displayName": "Expired At", "actions.form_create.properties.name.description": "Form name", "actions.form_create.properties.name.displayName": "Name", "actions.form_create.properties.startAt.description": "Form start date & time", "actions.form_create.properties.startAt.displayName": "Form Start At", "actions.form_create.properties.tags.description": "Form tags", "actions.form_create.properties.tags.displayName": "Tags", "actions.form_delete.description": "Delete a form", "actions.form_delete.displayName": "Delete Form", "actions.form_delete.properties.resourceId.description": "The form ID to delete", "actions.form_delete.properties.resourceId.displayName": "Resource ID", "actions.form_duplicate.description": "Duplicate a form for a specific workspace", "actions.form_duplicate.displayName": "Duplicate Form", "actions.form_duplicate.properties.resourceId.description": "The form ID to duplicate", "actions.form_duplicate.properties.resourceId.displayName": "Resource ID", "actions.form_list.description": "List forms with optional filtering and pagination", "actions.form_list.displayName": "List Forms", "actions.form_list.properties.filter.description": "Filter by a JSON object", "actions.form_list.properties.filter.displayName": "Filter", "actions.form_list.properties.isFavorited.description": "Select favorited forms", "actions.form_list.properties.isFavorited.displayName": "Is Favorited", "actions.form_list.properties.page.description": "Selected page for list display", "actions.form_list.properties.page.displayName": "Page", "actions.form_list.properties.perPage.description": "Number of item per page", "actions.form_list.properties.perPage.displayName": "Per Page", "actions.form_list.properties.search.description": "Search term to filter forms", "actions.form_list.properties.search.displayName": "Search", "actions.form_list.properties.sort.description": "Sort by field_name for ASC or -field_name for DESC, multiple values are supported by \",\" separator", "actions.form_list.properties.sort.displayName": "Sort", "actions.form_list.properties.templateSource.description": "Select template source when is_template true", "actions.form_list.properties.templateSource.displayName": "Template Source", "actions.form_list_page.description": "List forms with optional filtering and pagination", "actions.form_list_page.displayName": "List all form pages", "actions.form_list_page.properties.resourceId.description": "The form ID to list all pages", "actions.form_list_page.properties.resourceId.displayName": "Resource ID", "actions.form_list_question.description": "List all questions of a form", "actions.form_list_question.displayName": "List all questions of a form", "actions.form_list_question.properties.filter.description": "Filter by a JSON object", "actions.form_list_question.properties.filter.displayName": "Filter", "actions.form_list_question.properties.resourceId.description": "The form ID to list all questions", "actions.form_list_question.properties.resourceId.displayName": "Resource ID", "actions.form_list_question.properties.search.description": "Search term to filter questions", "actions.form_list_question.properties.search.displayName": "Search", "actions.form_list_question.properties.sort.description": "Sort by field_name for ASC or -field_name for DESC, multiple values are supported by \",\" separator", "actions.form_list_question.properties.sort.displayName": "Sort", "actions.form_retrieve.description": "Retrieve the form detail by form ID or form slug", "actions.form_retrieve.displayName": "Retrieve Form", "actions.form_retrieve.properties.resourceId.description": "The form ID or form slug to retrieve", "actions.form_retrieve.properties.resourceId.displayName": "Resource ID", "actions.form_update.description": "Update an existing form", "actions.form_update.displayName": "Update Form", "actions.form_update.properties.expiredAt.description": "Form end date & time", "actions.form_update.properties.expiredAt.displayName": "Expired At", "actions.form_update.properties.isPinned.description": "Form is pinned", "actions.form_update.properties.isPinned.displayName": "Is Pinned", "actions.form_update.properties.name.description": "Form name", "actions.form_update.properties.name.displayName": "Name", "actions.form_update.properties.resourceId.description": "The form ID to update", "actions.form_update.properties.resourceId.displayName": "Resource ID", "actions.form_update.properties.startAt.description": "Form start date & time", "actions.form_update.properties.startAt.displayName": "Start At", "actions.form_update.properties.status.description": "Form status", "actions.form_update.properties.status.displayName": "Status", "actions.form_update.properties.tags.description": "Form tags", "actions.form_update.properties.tags.displayName": "Tags", "actions.form_update_page.description": "Update a form page", "actions.form_update_page.displayName": "Update Form Page", "actions.form_update_page.properties.pages.description": "Array of page updates", "actions.form_update_page.properties.pages.displayName": "Pages", "actions.form_update_page.properties.pages.items.properties.content.description": "List of questions on the page", "actions.form_update_page.properties.pages.items.properties.content.displayName": "Content", "actions.form_update_page.properties.pages.items.properties.content.items.properties.dateFormat.description": "Date format for date-related questions", "actions.form_update_page.properties.pages.items.properties.content.items.properties.dateFormat.displayName": "Date Format", "actions.form_update_page.properties.pages.items.properties.content.items.properties.description.description": "Question description", "actions.form_update_page.properties.pages.items.properties.content.items.properties.description.displayName": "Description", "actions.form_update_page.properties.pages.items.properties.content.items.properties.descriptionEnabled.description": "Whether description is enabled", "actions.form_update_page.properties.pages.items.properties.content.items.properties.descriptionEnabled.displayName": "Description Enabled", "actions.form_update_page.properties.pages.items.properties.content.items.properties.dobEnabled.description": "Whether date of birth is enabled", "actions.form_update_page.properties.pages.items.properties.content.items.properties.dobEnabled.displayName": "DOB Enabled", "actions.form_update_page.properties.pages.items.properties.content.items.properties.fields.description": "Sub-fields for complex questions", "actions.form_update_page.properties.pages.items.properties.content.items.properties.fields.displayName": "Fields", "actions.form_update_page.properties.pages.items.properties.content.items.properties.fixedDateTitleEnabled.description": "Whether fixed date title is enabled", "actions.form_update_page.properties.pages.items.properties.content.items.properties.fixedDateTitleEnabled.displayName": "Fixed Date Title Enabled", "actions.form_update_page.properties.pages.items.properties.content.items.properties.footerHide.description": "Whether to hide the footer", "actions.form_update_page.properties.pages.items.properties.content.items.properties.footerHide.displayName": "<PERSON><PERSON>", "actions.form_update_page.properties.pages.items.properties.content.items.properties.footerLabel.description": "Footer label configuration", "actions.form_update_page.properties.pages.items.properties.content.items.properties.footerLabel.displayName": "Footer Label", "actions.form_update_page.properties.pages.items.properties.content.items.properties.footerLabel.properties.left.description": "Left footer label", "actions.form_update_page.properties.pages.items.properties.content.items.properties.footerLabel.properties.left.displayName": "Left", "actions.form_update_page.properties.pages.items.properties.content.items.properties.footerLabel.properties.middle.description": "Middle footer label", "actions.form_update_page.properties.pages.items.properties.content.items.properties.footerLabel.properties.middle.displayName": "Middle", "actions.form_update_page.properties.pages.items.properties.content.items.properties.footerLabel.properties.right.description": "Right footer label", "actions.form_update_page.properties.pages.items.properties.content.items.properties.footerLabel.properties.right.displayName": "Right", "actions.form_update_page.properties.pages.items.properties.content.items.properties.hideLabel.description": "Whether to hide the label", "actions.form_update_page.properties.pages.items.properties.content.items.properties.hideLabel.displayName": "Hide Label", "actions.form_update_page.properties.pages.items.properties.content.items.properties.icon.description": "Question icon", "actions.form_update_page.properties.pages.items.properties.content.items.properties.icon.displayName": "Icon", "actions.form_update_page.properties.pages.items.properties.content.items.properties.id.description": "Question ID", "actions.form_update_page.properties.pages.items.properties.content.items.properties.id.displayName": "ID", "actions.form_update_page.properties.pages.items.properties.content.items.properties.index.description": "Order index of the question", "actions.form_update_page.properties.pages.items.properties.content.items.properties.index.displayName": "Index", "actions.form_update_page.properties.pages.items.properties.content.items.properties.inputDirectlyEnabled.description": "Whether direct input is enabled", "actions.form_update_page.properties.pages.items.properties.content.items.properties.inputDirectlyEnabled.displayName": "Input Directly Enabled", "actions.form_update_page.properties.pages.items.properties.content.items.properties.isHide.description": "Whether the question is hidden", "actions.form_update_page.properties.pages.items.properties.content.items.properties.isHide.displayName": "Is Hide", "actions.form_update_page.properties.pages.items.properties.content.items.properties.isOther.description": "Whether this is an 'other' option", "actions.form_update_page.properties.pages.items.properties.content.items.properties.isOther.displayName": "Is Other", "actions.form_update_page.properties.pages.items.properties.content.items.properties.label.description": "Question label", "actions.form_update_page.properties.pages.items.properties.content.items.properties.label.displayName": "Label", "actions.form_update_page.properties.pages.items.properties.content.items.properties.layout.description": "Question layout configuration", "actions.form_update_page.properties.pages.items.properties.content.items.properties.layout.displayName": "Layout", "actions.form_update_page.properties.pages.items.properties.content.items.properties.maxScale.description": "Maximum scale value for rating questions", "actions.form_update_page.properties.pages.items.properties.content.items.properties.maxScale.displayName": "Max Scale", "actions.form_update_page.properties.pages.items.properties.content.items.properties.name.description": "Question name", "actions.form_update_page.properties.pages.items.properties.content.items.properties.name.displayName": "Name", "actions.form_update_page.properties.pages.items.properties.content.items.properties.options.description": "Question options for choice-based questions", "actions.form_update_page.properties.pages.items.properties.content.items.properties.options.displayName": "Options", "actions.form_update_page.properties.pages.items.properties.content.items.properties.options.items.properties.defaultCheck.description": "Whether this option is checked by default", "actions.form_update_page.properties.pages.items.properties.content.items.properties.options.items.properties.defaultCheck.displayName": "Default <PERSON>", "actions.form_update_page.properties.pages.items.properties.content.items.properties.options.items.properties.imageUrl.description": "URL for option image", "actions.form_update_page.properties.pages.items.properties.content.items.properties.options.items.properties.imageUrl.displayName": "Image URL", "actions.form_update_page.properties.pages.items.properties.content.items.properties.options.items.properties.label.description": "Option label", "actions.form_update_page.properties.pages.items.properties.content.items.properties.options.items.properties.label.displayName": "Label", "actions.form_update_page.properties.pages.items.properties.content.items.properties.placeholder.description": "Placeholder text for the question", "actions.form_update_page.properties.pages.items.properties.content.items.properties.placeholder.displayName": "Placeholder", "actions.form_update_page.properties.pages.items.properties.content.items.properties.rightIcon.description": "Whether icon is positioned on the right", "actions.form_update_page.properties.pages.items.properties.content.items.properties.rightIcon.displayName": "Right Icon", "actions.form_update_page.properties.pages.items.properties.content.items.properties.shape.description": "Rating shape for rating questions", "actions.form_update_page.properties.pages.items.properties.content.items.properties.shape.displayName": "<PERSON><PERSON><PERSON>", "actions.form_update_page.properties.pages.items.properties.content.items.properties.showNumber.description": "Whether to show question number", "actions.form_update_page.properties.pages.items.properties.content.items.properties.showNumber.displayName": "Show Number", "actions.form_update_page.properties.pages.items.properties.content.items.properties.style.description": "Question styling configuration", "actions.form_update_page.properties.pages.items.properties.content.items.properties.style.displayName": "Style", "actions.form_update_page.properties.pages.items.properties.content.items.properties.supportedTypes.description": "Supported file types for file upload questions", "actions.form_update_page.properties.pages.items.properties.content.items.properties.supportedTypes.displayName": "Supported Types", "actions.form_update_page.properties.pages.items.properties.content.items.properties.theme.description": "File upload theme size", "actions.form_update_page.properties.pages.items.properties.content.items.properties.theme.displayName": "Theme", "actions.form_update_page.properties.pages.items.properties.content.items.properties.timeFormat.description": "Time format for time-related questions", "actions.form_update_page.properties.pages.items.properties.content.items.properties.timeFormat.displayName": "Time Format", "actions.form_update_page.properties.pages.items.properties.content.items.properties.type.description": "Type of the question", "actions.form_update_page.properties.pages.items.properties.content.items.properties.type.displayName": "Type", "actions.form_update_page.properties.pages.items.properties.content.items.properties.validators.description": "Question validation rules", "actions.form_update_page.properties.pages.items.properties.content.items.properties.validators.displayName": "Validators", "actions.form_update_page.properties.pages.items.properties.content.items.properties.validators.items.properties.message.description": "Validation error message", "actions.form_update_page.properties.pages.items.properties.content.items.properties.validators.items.properties.message.displayName": "Message", "actions.form_update_page.properties.pages.items.properties.content.items.properties.validators.items.properties.type.description": "Validator type", "actions.form_update_page.properties.pages.items.properties.content.items.properties.validators.items.properties.type.displayName": "Type", "actions.form_update_page.properties.pages.items.properties.content.items.properties.validators.items.properties.value.description": "Validator value", "actions.form_update_page.properties.pages.items.properties.content.items.properties.validators.items.properties.value.displayName": "Value", "actions.form_update_page.properties.pages.items.properties.id.description": "Page ID", "actions.form_update_page.properties.pages.items.properties.id.displayName": "ID", "actions.form_update_page.properties.pages.items.properties.isModified.description": "Whether the page needs to be modified", "actions.form_update_page.properties.pages.items.properties.isModified.displayName": "Is Modified", "actions.form_update_page.properties.pages.items.properties.layout.description": "Page layout configuration", "actions.form_update_page.properties.pages.items.properties.layout.displayName": "Layout", "actions.form_update_page.properties.pages.items.properties.layout.properties.fieldWidth.description": "Width of form fields", "actions.form_update_page.properties.pages.items.properties.layout.properties.fieldWidth.displayName": "Field Width", "actions.form_update_page.properties.pages.items.properties.layout.properties.imageUrl.description": "URL for layout image", "actions.form_update_page.properties.pages.items.properties.layout.properties.imageUrl.displayName": "Image URL", "actions.form_update_page.properties.pages.items.properties.layout.properties.showProgressBar.description": "Whether to show progress bar", "actions.form_update_page.properties.pages.items.properties.layout.properties.showProgressBar.displayName": "Show Progress Bar", "actions.form_update_page.properties.pages.items.properties.layout.properties.type.description": "Layout type", "actions.form_update_page.properties.pages.items.properties.layout.properties.type.displayName": "Type", "actions.form_update_page.properties.pages.items.properties.name.description": "Page name", "actions.form_update_page.properties.pages.items.properties.name.displayName": "Name", "actions.form_update_page.properties.pages.items.properties.type.description": "Page type", "actions.form_update_page.properties.pages.items.properties.type.displayName": "Type", "actions.form_update_page.properties.resourceId.description": "The form ID to update pages for", "actions.form_update_page.properties.resourceId.displayName": "Resource ID", "actions.response_create.description": "Create a response for a form", "actions.response_create.displayName": "Response Create", "actions.response_create.properties.answers.description": "Array of answers for the form questions", "actions.response_create.properties.answers.displayName": "Answers", "actions.response_create.properties.answers.items.properties.pageId.description": "ID of the page containing the question", "actions.response_create.properties.answers.items.properties.pageId.displayName": "Page ID", "actions.response_create.properties.answers.items.properties.questionId.description": "ID of the question being answered", "actions.response_create.properties.answers.items.properties.questionId.displayName": "Question ID", "actions.response_create.properties.answers.items.properties.values.description": "Array of answer values for the question", "actions.response_create.properties.answers.items.properties.values.displayName": "Values", "actions.response_create.properties.answers.items.properties.values.items.properties.asset.description": "File asset information for file upload questions", "actions.response_create.properties.answers.items.properties.values.items.properties.asset.displayName": "<PERSON><PERSON>", "actions.response_create.properties.answers.items.properties.values.items.properties.asset.properties.id.description": "Asset ID", "actions.response_create.properties.answers.items.properties.values.items.properties.asset.properties.id.displayName": "ID", "actions.response_create.properties.answers.items.properties.values.items.properties.asset.properties.mimeType.description": "Asset MIME type", "actions.response_create.properties.answers.items.properties.values.items.properties.asset.properties.mimeType.displayName": "MIME Type", "actions.response_create.properties.answers.items.properties.values.items.properties.asset.properties.name.description": "Asset name", "actions.response_create.properties.answers.items.properties.values.items.properties.asset.properties.name.displayName": "Name", "actions.response_create.properties.answers.items.properties.values.items.properties.asset.properties.path.description": "Asset file path", "actions.response_create.properties.answers.items.properties.values.items.properties.asset.properties.path.displayName": "Path", "actions.response_create.properties.answers.items.properties.values.items.properties.asset.properties.resourceId.description": "Resource ID associated with the asset", "actions.response_create.properties.answers.items.properties.values.items.properties.asset.properties.resourceId.displayName": "Resource ID", "actions.response_create.properties.answers.items.properties.values.items.properties.asset.properties.resourceType.description": "Type of resource (form or response)", "actions.response_create.properties.answers.items.properties.values.items.properties.asset.properties.resourceType.displayName": "Resource Type", "actions.response_create.properties.answers.items.properties.values.items.properties.asset.properties.size.description": "Asset size in bytes", "actions.response_create.properties.answers.items.properties.values.items.properties.asset.properties.size.displayName": "Size", "actions.response_create.properties.answers.items.properties.values.items.properties.asset.properties.url.description": "Asset URL", "actions.response_create.properties.answers.items.properties.values.items.properties.asset.properties.url.displayName": "URL", "actions.response_create.properties.answers.items.properties.values.items.properties.isOther.description": "Whether this is an 'other' option response", "actions.response_create.properties.answers.items.properties.values.items.properties.isOther.displayName": "Is Other", "actions.response_create.properties.answers.items.properties.values.items.properties.value.description": "The answer value (can be string, number, integer, or boolean)", "actions.response_create.properties.answers.items.properties.values.items.properties.value.displayName": "Value", "actions.response_create.properties.resourceId.description": "The form ID to create a response", "actions.response_create.properties.resourceId.displayName": "Resource ID", "actions.response_list.description": "List all responses of a form", "actions.response_list.displayName": "Response List", "actions.response_list.properties.resourceId.description": "The form ID to list all responses", "actions.response_list.properties.resourceId.displayName": "Resource ID", "actions.response_retrieve.description": "Retrieve a response of a form", "actions.response_retrieve.displayName": "Response Retrieve", "actions.response_retrieve.properties.resourceId.description": "The form ID to retrieve a response", "actions.response_retrieve.properties.resourceId.displayName": "Resource ID", "actions.response_retrieve.properties.responseId.description": "The response ID to retrieve", "actions.response_retrieve.properties.responseId.displayName": "Response ID", "actions.view_create.description": "Create a new view", "actions.view_create.displayName": "View Create", "actions.view_create.properties.columns.description": "List of questions to be displayed in the view", "actions.view_create.properties.columns.displayName": "Columns", "actions.view_create.properties.filters.description": "Filters to be applied to the view", "actions.view_create.properties.filters.displayName": "Filters", "actions.view_create.properties.name.description": "View Name", "actions.view_create.properties.name.displayName": "Name", "actions.view_create.properties.resourceId.description": "The form ID to create a view", "actions.view_create.properties.resourceId.displayName": "Resource ID", "actions.view_delete.description": "Delete a view", "actions.view_delete.displayName": "View Delete", "actions.view_delete.properties.resourceId.description": "The form ID to delete a view", "actions.view_delete.properties.resourceId.displayName": "Resource ID", "actions.view_delete.properties.viewId.description": "The view ID to delete", "actions.view_delete.properties.viewId.displayName": "View ID", "actions.view_list.description": "List all views of a form", "actions.view_list.displayName": "View List", "actions.view_list.properties.resourceId.description": "The form ID to list all views", "actions.view_list.properties.resourceId.displayName": "Resource ID", "actions.view_retrieve.description": "Retrieve a view by view ID", "actions.view_retrieve.displayName": "View Retrieve", "actions.view_retrieve.properties.resourceId.description": "The form ID to retrieve a view", "actions.view_retrieve.properties.resourceId.displayName": "Resource ID", "actions.view_retrieve.properties.viewId.description": "The view ID to retrieve", "actions.view_retrieve.properties.viewId.displayName": "View ID", "actions.view_update.description": "Update a view", "actions.view_update.displayName": "View Update", "actions.view_update.properties.columns.description": "List of questions to be displayed in the view", "actions.view_update.properties.columns.displayName": "Columns", "actions.view_update.properties.filters.description": "Filters to be applied to the view", "actions.view_update.properties.filters.displayName": "Filters", "actions.view_update.properties.isPrivate.description": "Visibility of a view", "actions.view_update.properties.isPrivate.displayName": "Is Private", "actions.view_update.properties.name.description": "View Name", "actions.view_update.properties.name.displayName": "Name", "actions.view_update.properties.resourceId.description": "The form ID to update a view", "actions.view_update.properties.resourceId.displayName": "Resource ID", "actions.view_update.properties.viewId.description": "The view ID to update", "actions.view_update.properties.viewId.displayName": "View ID", "description": "Integrate with DECA Forms API", "displayName": "Forms", "schemas.appearance.description": "Form appearance settings", "schemas.appearance.displayName": "Appearance", "schemas.appearance.properties.button_style.description": "Button appearance settings", "schemas.appearance.properties.button_style.displayName": "Button Style", "schemas.appearance.properties.button_style.properties.background_color.description": "Button background color", "schemas.appearance.properties.button_style.properties.background_color.displayName": "Background Color", "schemas.appearance.properties.button_style.properties.font_family.description": "Button font family", "schemas.appearance.properties.button_style.properties.font_family.displayName": "Font Family", "schemas.appearance.properties.button_style.properties.font_size.description": "Button font size in pixels", "schemas.appearance.properties.button_style.properties.font_size.displayName": "Font Size", "schemas.appearance.properties.button_style.properties.full_width.description": "Whether button should take full width", "schemas.appearance.properties.button_style.properties.full_width.displayName": "Full Width", "schemas.appearance.properties.button_style.properties.text_color.description": "Button text color", "schemas.appearance.properties.button_style.properties.text_color.displayName": "Text Color", "schemas.appearance.properties.button_style.properties.type.description": "Button type", "schemas.appearance.properties.button_style.properties.type.displayName": "Type", "schemas.appearance.properties.customize.description": "Whether appearance is customized", "schemas.appearance.properties.customize.displayName": "Customize", "schemas.appearance.properties.default_settings.description": "Default appearance settings", "schemas.appearance.properties.default_settings.displayName": "<PERSON><PERSON><PERSON>", "schemas.appearance.properties.default_settings.properties.color.description": "Default color", "schemas.appearance.properties.default_settings.properties.color.displayName": "Color", "schemas.appearance.properties.default_settings.properties.font_family.description": "Default font family", "schemas.appearance.properties.default_settings.properties.font_family.displayName": "Font Family", "schemas.appearance.properties.default_settings.properties.input_style.description": "Default input field style", "schemas.appearance.properties.default_settings.properties.input_style.displayName": "Input Style", "schemas.appearance.properties.footer_style.description": "Footer appearance settings", "schemas.appearance.properties.footer_style.displayName": "Footer Style", "schemas.appearance.properties.footer_style.properties.is_using_text.description": "Whether to use text instead of logo", "schemas.appearance.properties.footer_style.properties.is_using_text.displayName": "Is Using Text", "schemas.appearance.properties.footer_style.properties.logo_align.description": "Logo alignment position", "schemas.appearance.properties.footer_style.properties.logo_align.displayName": "Logo Align", "schemas.appearance.properties.footer_style.properties.logo_image.description": "URL or path to the logo image", "schemas.appearance.properties.footer_style.properties.logo_image.displayName": "Logo Image", "schemas.appearance.properties.footer_style.properties.logo_size.description": "Size of the logo", "schemas.appearance.properties.footer_style.properties.logo_size.displayName": "Logo Size", "schemas.appearance.properties.footer_style.properties.text.description": "Footer text content", "schemas.appearance.properties.footer_style.properties.text.displayName": "Text", "schemas.appearance.properties.form_field_style.description": "Form field appearance settings", "schemas.appearance.properties.form_field_style.displayName": "Form Field Style", "schemas.appearance.properties.form_field_style.properties.color.description": "Color settings for form fields", "schemas.appearance.properties.form_field_style.properties.color.displayName": "Color", "schemas.appearance.properties.form_field_style.properties.color.properties.answer.description": "Answer text color", "schemas.appearance.properties.form_field_style.properties.color.properties.answer.displayName": "Answer", "schemas.appearance.properties.form_field_style.properties.color.properties.description.description": "Description text color", "schemas.appearance.properties.form_field_style.properties.color.properties.description.displayName": "Description", "schemas.appearance.properties.form_field_style.properties.color.properties.field_background.description": "Field background color", "schemas.appearance.properties.form_field_style.properties.color.properties.field_background.displayName": "Field Background", "schemas.appearance.properties.form_field_style.properties.color.properties.field_stroke.description": "Field border color", "schemas.appearance.properties.form_field_style.properties.color.properties.field_stroke.displayName": "Field Stroke", "schemas.appearance.properties.form_field_style.properties.color.properties.icon.description": "Icon color", "schemas.appearance.properties.form_field_style.properties.color.properties.icon.displayName": "Icon", "schemas.appearance.properties.form_field_style.properties.color.properties.placeholder.description": "Placeholder text color", "schemas.appearance.properties.form_field_style.properties.color.properties.placeholder.displayName": "Placeholder", "schemas.appearance.properties.form_field_style.properties.color.properties.question.description": "Question text color", "schemas.appearance.properties.form_field_style.properties.color.properties.question.displayName": "Question", "schemas.appearance.properties.form_field_style.properties.font_family.description": "Font family settings for form fields", "schemas.appearance.properties.form_field_style.properties.font_family.displayName": "Font Family", "schemas.appearance.properties.form_field_style.properties.font_family.properties.answer.description": "Answer font family", "schemas.appearance.properties.form_field_style.properties.font_family.properties.answer.displayName": "Answer", "schemas.appearance.properties.form_field_style.properties.font_family.properties.question.description": "Question font family", "schemas.appearance.properties.form_field_style.properties.font_family.properties.question.displayName": "Question", "schemas.appearance.properties.form_field_style.properties.font_family.properties.text.description": "Text font family", "schemas.appearance.properties.form_field_style.properties.font_family.properties.text.displayName": "Text", "schemas.appearance.properties.form_field_style.properties.font_size.description": "Font size settings for form fields", "schemas.appearance.properties.form_field_style.properties.font_size.displayName": "Font Size", "schemas.appearance.properties.form_field_style.properties.font_size.properties.answer.description": "Answer font size in pixels", "schemas.appearance.properties.form_field_style.properties.font_size.properties.answer.displayName": "Answer", "schemas.appearance.properties.form_field_style.properties.font_size.properties.question.description": "Question font size in pixels", "schemas.appearance.properties.form_field_style.properties.font_size.properties.question.displayName": "Question", "schemas.appearance.properties.form_field_style.properties.font_size.properties.text.description": "Text font size in pixels", "schemas.appearance.properties.form_field_style.properties.font_size.properties.text.displayName": "Text", "schemas.appearance.properties.header_style.description": "Header appearance settings", "schemas.appearance.properties.header_style.displayName": "Header Style", "schemas.appearance.properties.header_style.properties.is_using_text.description": "Whether to use text instead of logo", "schemas.appearance.properties.header_style.properties.is_using_text.displayName": "Is Using Text", "schemas.appearance.properties.header_style.properties.logo_align.description": "Logo alignment position", "schemas.appearance.properties.header_style.properties.logo_align.displayName": "Logo Align", "schemas.appearance.properties.header_style.properties.logo_image.description": "URL or path to the logo image", "schemas.appearance.properties.header_style.properties.logo_image.displayName": "Logo Image", "schemas.appearance.properties.header_style.properties.logo_size.description": "Size of the logo", "schemas.appearance.properties.header_style.properties.logo_size.displayName": "Logo Size", "schemas.appearance.properties.header_style.properties.position.description": "Header position", "schemas.appearance.properties.header_style.properties.position.displayName": "Position", "schemas.appearance.properties.header_style.properties.text.description": "Header text content", "schemas.appearance.properties.header_style.properties.text.displayName": "Text", "schemas.appearance.properties.heading_style.description": "Heading appearance settings", "schemas.appearance.properties.heading_style.displayName": "Heading Style", "schemas.appearance.properties.heading_style.properties.color.description": "Heading text color", "schemas.appearance.properties.heading_style.properties.color.displayName": "Color", "schemas.appearance.properties.heading_style.properties.font_family.description": "Heading font family", "schemas.appearance.properties.heading_style.properties.font_family.displayName": "Font Family", "schemas.appearance.properties.heading_style.properties.font_size.description": "Heading font size in pixels", "schemas.appearance.properties.heading_style.properties.font_size.displayName": "Font Size", "schemas.appearance.properties.paragraph_style.description": "Paragraph appearance settings", "schemas.appearance.properties.paragraph_style.displayName": "Paragraph Style", "schemas.appearance.properties.paragraph_style.properties.color.description": "Paragraph text color", "schemas.appearance.properties.paragraph_style.properties.color.displayName": "Color", "schemas.appearance.properties.paragraph_style.properties.font_family.description": "Paragraph font family", "schemas.appearance.properties.paragraph_style.properties.font_family.displayName": "Font Family", "schemas.appearance.properties.paragraph_style.properties.font_size.description": "Paragraph font size in pixels", "schemas.appearance.properties.paragraph_style.properties.font_size.displayName": "Font Size", "schemas.column.description": "Column object representing a question in a view", "schemas.column.displayName": "Column", "schemas.column.properties.isDeleted.description": "Is deleted", "schemas.column.properties.isDeleted.displayName": "Is Deleted", "schemas.column.properties.isHidden.description": "Question's hidden status", "schemas.column.properties.isHidden.displayName": "Is Hidden", "schemas.column.properties.isPinned.description": "Question's pin status", "schemas.column.properties.isPinned.displayName": "Is Pinned", "schemas.column.properties.label.description": "Question label", "schemas.column.properties.label.displayName": "Label", "schemas.column.properties.labelText.description": "Question label without html tags", "schemas.column.properties.labelText.displayName": "Label Text", "schemas.column.properties.questionId.description": "Question ID", "schemas.column.properties.questionId.displayName": "Question ID", "schemas.column.properties.type.description": "Question type", "schemas.column.properties.type.displayName": "Type", "schemas.column_create.description": "Column create object for creating view columns", "schemas.column_create.displayName": "Column Create", "schemas.column_create.properties.isHidden.description": "Question's hidden status", "schemas.column_create.properties.isHidden.displayName": "Is Hidden", "schemas.column_create.properties.isPinned.description": "Question's pin status", "schemas.column_create.properties.isPinned.displayName": "Is Pinned", "schemas.column_create.properties.questionId.description": "Question ID", "schemas.column_create.properties.questionId.displayName": "Question ID", "schemas.form.properties.expired_at.description": "Expired time of the form", "schemas.form.properties.expired_at.displayName": "Expired At", "schemas.form.properties.id.description": "Unique identifier for the form", "schemas.form.properties.id.displayName": "ID", "schemas.form.properties.is_template.description": "Whether the form is a template", "schemas.form.properties.is_template.displayName": "Is Template", "schemas.form.properties.metadata.description": "Form metadata", "schemas.form.properties.metadata.displayName": "<PERSON><PERSON><PERSON>", "schemas.form.properties.metadata.properties.org_name.description": "Name of the organization", "schemas.form.properties.metadata.properties.org_name.displayName": "Organization Name", "schemas.form.properties.metadata.properties.organization_id.description": "Unique identifier for the organization", "schemas.form.properties.metadata.properties.organization_id.displayName": "Organization ID", "schemas.form.properties.metadata.properties.workspace_id.description": "Unique identifier for the workspace", "schemas.form.properties.metadata.properties.workspace_id.displayName": "Workspace ID", "schemas.form.properties.name.description": "Name of the form", "schemas.form.properties.name.displayName": "Name", "schemas.form.properties.responses.description": "Number of responses", "schemas.form.properties.responses.displayName": "Responses", "schemas.form.properties.screenshot.description": "Form thumbnail", "schemas.form.properties.screenshot.displayName": "Screenshot", "schemas.form.properties.screenshot.properties.original.description": "URL to the original screenshot image", "schemas.form.properties.screenshot.properties.original.displayName": "Original", "schemas.form.properties.screenshot.properties.preview.description": "URL to the preview version of the screenshot", "schemas.form.properties.screenshot.properties.preview.displayName": "Preview", "schemas.form.properties.screenshot.properties.thumbnail.description": "URL to the thumbnail version of the screenshot", "schemas.form.properties.screenshot.properties.thumbnail.displayName": "<PERSON><PERSON><PERSON><PERSON>", "schemas.form.properties.setting.description": "Form settings", "schemas.form.properties.setting.displayName": "Setting", "schemas.form.properties.start_at.description": "Start time of the form", "schemas.form.properties.start_at.displayName": "Start At", "schemas.form.properties.status.description": "Form status", "schemas.form.properties.status.displayName": "Status", "schemas.page.description": "The page object", "schemas.page.displayName": "Page", "schemas.page.properties.content.description": "List of questions on the page", "schemas.page.properties.content.displayName": "Content", "schemas.page.properties.created_at.description": "Page creation timestamp", "schemas.page.properties.created_at.displayName": "Created At", "schemas.page.properties.id.description": "Page ID", "schemas.page.properties.id.displayName": "ID", "schemas.page.properties.index.description": "Page order index", "schemas.page.properties.index.displayName": "Index", "schemas.page.properties.layout.description": "Page layout configuration", "schemas.page.properties.layout.displayName": "Layout", "schemas.page.properties.layout.properties.field_width.description": "Width of form fields", "schemas.page.properties.layout.properties.field_width.displayName": "Field Width", "schemas.page.properties.layout.properties.image_url.description": "URL for layout image", "schemas.page.properties.layout.properties.image_url.displayName": "Image URL", "schemas.page.properties.layout.properties.show_progress_bar.description": "Whether to show progress bar", "schemas.page.properties.layout.properties.show_progress_bar.displayName": "Show Progress Bar", "schemas.page.properties.layout.properties.type.description": "Layout type", "schemas.page.properties.layout.properties.type.displayName": "Type", "schemas.page.properties.name.description": "Page name", "schemas.page.properties.name.displayName": "Name", "schemas.page.properties.type.description": "Page type", "schemas.page.properties.type.displayName": "Type", "schemas.page.properties.updated_at.description": "Page last update timestamp", "schemas.page.properties.updated_at.displayName": "Updated At", "schemas.page_question.description": "The question item on a specific page", "schemas.page_question.displayName": "Question", "schemas.page_question.properties.date_format.description": "Date format for date-related questions", "schemas.page_question.properties.date_format.displayName": "Date Format", "schemas.page_question.properties.description.description": "Question description", "schemas.page_question.properties.description.displayName": "Description", "schemas.page_question.properties.description_enabled.description": "Whether description is enabled", "schemas.page_question.properties.description_enabled.displayName": "Description Enabled", "schemas.page_question.properties.dob_enabled.description": "Whether date of birth is enabled", "schemas.page_question.properties.dob_enabled.displayName": "DOB Enabled", "schemas.page_question.properties.fields.description": "Sub-fields for complex questions", "schemas.page_question.properties.fields.displayName": "Fields", "schemas.page_question.properties.fixed_date_title_enabled.description": "Whether fixed date title is enabled", "schemas.page_question.properties.fixed_date_title_enabled.displayName": "Fixed Date Title Enabled", "schemas.page_question.properties.footer_hide.description": "Whether to hide the footer", "schemas.page_question.properties.footer_hide.displayName": "<PERSON><PERSON>", "schemas.page_question.properties.footer_label.description": "Footer label configuration", "schemas.page_question.properties.footer_label.displayName": "Footer Label", "schemas.page_question.properties.footer_label.properties.left.description": "Left footer label", "schemas.page_question.properties.footer_label.properties.left.displayName": "Left", "schemas.page_question.properties.footer_label.properties.middle.description": "Middle footer label", "schemas.page_question.properties.footer_label.properties.middle.displayName": "Middle", "schemas.page_question.properties.footer_label.properties.right.description": "Right footer label", "schemas.page_question.properties.footer_label.properties.right.displayName": "Right", "schemas.page_question.properties.hide_label.description": "Whether to hide the label", "schemas.page_question.properties.hide_label.displayName": "Hide Label", "schemas.page_question.properties.icon.description": "Question icon", "schemas.page_question.properties.icon.displayName": "Icon", "schemas.page_question.properties.id.description": "Unique identifier for the question", "schemas.page_question.properties.id.displayName": "ID", "schemas.page_question.properties.index.description": "Order index of the question", "schemas.page_question.properties.index.displayName": "Index", "schemas.page_question.properties.input_directly_enabled.description": "Whether direct input is enabled", "schemas.page_question.properties.input_directly_enabled.displayName": "Input Directly Enabled", "schemas.page_question.properties.is_hide.description": "Whether the question is hidden", "schemas.page_question.properties.is_hide.displayName": "Is Hide", "schemas.page_question.properties.is_other.description": "Whether this is an 'other' option", "schemas.page_question.properties.is_other.displayName": "Is Other", "schemas.page_question.properties.label.description": "Question label", "schemas.page_question.properties.label.displayName": "Label", "schemas.page_question.properties.layout.description": "Question layout configuration", "schemas.page_question.properties.layout.displayName": "Layout", "schemas.page_question.properties.max_scale.description": "Maximum scale value for rating questions", "schemas.page_question.properties.max_scale.displayName": "Max Scale", "schemas.page_question.properties.name.description": "Question name", "schemas.page_question.properties.name.displayName": "Name", "schemas.page_question.properties.options.description": "Question options for choice-based questions", "schemas.page_question.properties.options.displayName": "Options", "schemas.page_question.properties.options.items.properties.default_check.description": "Whether this option is checked by default", "schemas.page_question.properties.options.items.properties.default_check.displayName": "Default <PERSON>", "schemas.page_question.properties.options.items.properties.image_url.description": "URL for option image", "schemas.page_question.properties.options.items.properties.image_url.displayName": "Image URL", "schemas.page_question.properties.options.items.properties.label.description": "Option label", "schemas.page_question.properties.options.items.properties.label.displayName": "Label", "schemas.page_question.properties.placeholder.description": "Placeholder text for the question", "schemas.page_question.properties.placeholder.displayName": "Placeholder", "schemas.page_question.properties.right_icon.description": "Whether icon is positioned on the right", "schemas.page_question.properties.right_icon.displayName": "Right Icon", "schemas.page_question.properties.shape.description": "Rating shape for rating questions", "schemas.page_question.properties.shape.displayName": "<PERSON><PERSON><PERSON>", "schemas.page_question.properties.show_number.description": "Whether to show question number", "schemas.page_question.properties.show_number.displayName": "Show Number", "schemas.page_question.properties.style.description": "Question styling configuration", "schemas.page_question.properties.style.displayName": "Style", "schemas.page_question.properties.supported_types.description": "Supported file types for file upload questions", "schemas.page_question.properties.supported_types.displayName": "Supported Types", "schemas.page_question.properties.theme.description": "File upload theme size", "schemas.page_question.properties.theme.displayName": "Theme", "schemas.page_question.properties.time_format.description": "Time format for time-related questions", "schemas.page_question.properties.time_format.displayName": "Time Format", "schemas.page_question.properties.type.description": "Type of the question", "schemas.page_question.properties.type.displayName": "Type", "schemas.page_question.properties.validators.description": "Question validation rules", "schemas.page_question.properties.validators.displayName": "Validators", "schemas.page_question.properties.validators.items.properties.message.description": "Validation error message", "schemas.page_question.properties.validators.items.properties.message.displayName": "Message", "schemas.page_question.properties.validators.items.properties.type.description": "Validator type", "schemas.page_question.properties.validators.items.properties.type.displayName": "Type", "schemas.page_question.properties.validators.items.properties.value.description": "Validator value", "schemas.page_question.properties.validators.items.properties.value.displayName": "Value", "schemas.question.description": "Question object", "schemas.question.displayName": "Question", "schemas.question.properties.form_id.description": "ID of the form containing the question", "schemas.question.properties.form_id.displayName": "Form ID", "schemas.question.properties.id.description": "Unique identifier for the question", "schemas.question.properties.id.displayName": "ID", "schemas.question.properties.is_deleted.description": "Whether the question is deleted", "schemas.question.properties.is_deleted.displayName": "Is Deleted", "schemas.question.properties.is_hidden.description": "Whether the question is hidden", "schemas.question.properties.is_hidden.displayName": "Is Hidden", "schemas.question.properties.is_pinned.description": "Whether the question is pinned", "schemas.question.properties.is_pinned.displayName": "Is Pinned", "schemas.question.properties.label.description": "Question label", "schemas.question.properties.label.displayName": "Label", "schemas.question.properties.label_text.description": "Question label without HTML tags", "schemas.question.properties.label_text.displayName": "Label Text", "schemas.question.properties.name.description": "Question name", "schemas.question.properties.name.displayName": "Name", "schemas.question.properties.options.description": "Question options for choice-based questions", "schemas.question.properties.options.displayName": "Options", "schemas.question.properties.page_id.description": "ID of the page containing the question", "schemas.question.properties.page_id.displayName": "Page ID", "schemas.question.properties.page_name.description": "Name of the page containing the question", "schemas.question.properties.page_name.displayName": "Page Name", "schemas.question.properties.type.description": "Type of the question", "schemas.question.properties.type.displayName": "Type", "schemas.response.additionalProperties.description": "Dynamic question responses where the key is the question ID", "schemas.response.additionalProperties.properties.index.description": "Order index of the question response", "schemas.response.additionalProperties.properties.index.displayName": "Index", "schemas.response.additionalProperties.properties.page_id.description": "ID of the page containing the question", "schemas.response.additionalProperties.properties.page_id.displayName": "Page ID", "schemas.response.additionalProperties.properties.question_label.description": "Label of the question", "schemas.response.additionalProperties.properties.question_label.displayName": "Question Label", "schemas.response.additionalProperties.properties.values.description": "Array of answer values for the question", "schemas.response.additionalProperties.properties.values.displayName": "Values", "schemas.response.additionalProperties.properties.values.items.properties.is_other.description": "Whether this is an 'other' option response", "schemas.response.additionalProperties.properties.values.items.properties.is_other.displayName": "Is Other", "schemas.response.additionalProperties.properties.values.items.properties.value.description": "The answer value", "schemas.response.additionalProperties.properties.values.items.properties.value.displayName": "Value", "schemas.response.description": "Response object for a question in a form", "schemas.response.displayName": "Response", "schemas.response.properties.created_at.description": "Response creation timestamp", "schemas.response.properties.created_at.displayName": "Created At", "schemas.response.properties.created_by.description": "Information about who created the response", "schemas.response.properties.created_by.displayName": "Created By", "schemas.response.properties.created_by.properties.ip.description": "IP address of the respondent", "schemas.response.properties.created_by.properties.ip.displayName": "IP Address", "schemas.response.properties.created_by.properties.profile.description": "LINE Profile if provided", "schemas.response.properties.created_by.properties.profile.displayName": "LINE Profile if provided", "schemas.response.properties.created_by.properties.profile.properties.avatar.description": "URL to the user's avatar image", "schemas.response.properties.created_by.properties.profile.properties.avatar.displayName": "Avatar", "schemas.response.properties.created_by.properties.profile.properties.id.description": "Unique identifier for the user profile", "schemas.response.properties.created_by.properties.profile.properties.id.displayName": "ID", "schemas.response.properties.created_by.properties.profile.properties.name.description": "Display name of the user", "schemas.response.properties.created_by.properties.profile.properties.name.displayName": "Name", "schemas.response.properties.created_by.properties.profile.properties.type.description": "Type of user profile (e.g., line, google, etc.)", "schemas.response.properties.created_by.properties.profile.properties.type.displayName": "Type", "schemas.response.properties.created_by.properties.user_agent.description": "User agent string of the respondent's browser", "schemas.response.properties.created_by.properties.user_agent.displayName": "User Agent", "schemas.response.properties.id.description": "Unique identifier for the response", "schemas.response.properties.id.displayName": "ID", "schemas.task.description": "Background task object", "schemas.task.displayName": "Task", "schemas.task.properties.created_at.description": "Task creation timestamp", "schemas.task.properties.created_at.displayName": "Created At", "schemas.task.properties.external_id.description": "Task ID, this is the external id", "schemas.task.properties.external_id.displayName": "External ID", "schemas.task.properties.finished_at.description": "Task finish timestamp", "schemas.task.properties.finished_at.displayName": "Finished At", "schemas.task.properties.fn.description": "Task function", "schemas.task.properties.fn.displayName": "Function", "schemas.task.properties.id.description": "Task ID, this is the internal id", "schemas.task.properties.id.displayName": "ID", "schemas.task.properties.started_at.description": "Task start timestamp", "schemas.task.properties.started_at.displayName": "Started At", "schemas.task.properties.status.description": "Task status", "schemas.task.properties.status.displayName": "Status", "schemas.task.properties.updated_at.description": "Task last update timestamp", "schemas.task.properties.updated_at.displayName": "Updated At", "schemas.view.description": "View object", "schemas.view.displayName": "View", "schemas.view.properties.columns.description": "List of questions to be displayed in the view", "schemas.view.properties.columns.displayName": "Columns", "schemas.view.properties.creatorId.description": "Result View Creator ID", "schemas.view.properties.creatorId.displayName": "Creator ID", "schemas.view.properties.filters.description": "Filters to be applied to the view", "schemas.view.properties.filters.displayName": "Filters", "schemas.view.properties.id.description": "Result View ID", "schemas.view.properties.id.displayName": "ID", "schemas.view.properties.isPrivate.description": "Visibility of a view", "schemas.view.properties.isPrivate.displayName": "Is Private", "schemas.view.properties.name.description": "View Name", "schemas.view.properties.name.displayName": "Name", "triggers.form.created.description": "", "triggers.form.created.displayName": "", "triggers.response.created.description": "", "triggers.response.created.displayName": ""}, "deca-kb": {"actions.agent.description": "Use an agent to interact with the knowledge base (based on OpenAPI AgentRequest schema)", "actions.agent.displayName": "Agent", "actions.agent.properties.allowSearchPrivateBases.description": "Allow searching private knowledge bases (default: false)", "actions.agent.properties.allowSearchPrivateBases.displayName": "Allow Search Private Bases", "actions.agent.properties.baseIds.description": "List of Base IDs (required by OpenAPI spec) (array of strings)", "actions.agent.properties.baseIds.displayName": "Base IDs", "actions.agent.properties.customPrompt.description": "Custom prompt for RAG mode. Must include '{documents}' and '{question}' placeholders", "actions.agent.properties.customPrompt.displayName": "Custom Prompt", "actions.agent.properties.debug.description": "Enable debug mode (default: false)", "actions.agent.properties.debug.displayName": "Debug", "actions.agent.properties.documentIds.description": "List of Document IDs (array of strings)", "actions.agent.properties.documentIds.displayName": "Document IDs", "actions.agent.properties.folderIds.description": "List of Folder IDs (required by OpenAPI spec) (array of strings)", "actions.agent.properties.folderIds.displayName": "Folder IDs", "actions.agent.properties.maxReferences.description": "Maximum references to return. RAG mode: 5-10, Search mode: 1 (default: 5)", "actions.agent.properties.maxReferences.displayName": "Max References", "actions.agent.properties.metadata.description": "Request metadata (required by OpenAPI spec)", "actions.agent.properties.metadata.displayName": "<PERSON><PERSON><PERSON>", "actions.agent.properties.mode.description": "Mode to use for asking", "actions.agent.properties.mode.displayName": "Mode", "actions.agent.properties.question.description": "Question to ask (max 2000 characters)", "actions.agent.properties.question.displayName": "Question", "actions.agent.properties.scoreThreshold.description": "Accuracy threshold for RAG mode (0.1 - 1.0, default: 0.5)", "actions.agent.properties.scoreThreshold.displayName": "Score Threshold", "actions.agent.properties.similarityThreshold.description": "Similarity threshold for Search mode (0.1 - 1.0, default: 0.5)", "actions.agent.properties.similarityThreshold.displayName": "Similarity <PERSON><PERSON><PERSON><PERSON>", "actions.agent.properties.stream.description": "Whether to stream the response (default: false)", "actions.agent.properties.stream.displayName": "Stream", "actions.article_create.description": "Create a new article in a knowledge base (based on OpenAPI CreateArticleRequest)", "actions.article_create.displayName": "Create Article", "actions.article_create.properties.baseId.description": "Knowledge base ID where the article will be created", "actions.article_create.properties.baseId.displayName": "Knowledge Base ID", "actions.article_create.properties.content.description": "Content of the article (max 5000 characters, min 1 character, required by OpenAPI spec)", "actions.article_create.properties.content.displayName": "Content", "actions.article_create.properties.contentRaw.description": "Raw content of the article - JSON/HTML/Markdown string (max 50000 characters, required by OpenAPI spec)", "actions.article_create.properties.contentRaw.displayName": "Raw Content", "actions.article_create.properties.customData.description": "Custom data fields for additional metadata (array of CustomDataInput objects, default: [])", "actions.article_create.properties.customData.displayName": "Custom Data", "actions.article_create.properties.keywords.description": "Keywords of the article for better searchability (array of strings, default: [])", "actions.article_create.properties.keywords.displayName": "Keywords", "actions.article_create.properties.relatedArticles.description": "Related article IDs (array of strings, max 5 items, default: [])", "actions.article_create.properties.relatedArticles.displayName": "Related Articles", "actions.article_create.properties.status.description": "Article status (default: published, based on OpenAPI ArticleStatus enum)", "actions.article_create.properties.status.displayName": "Status", "actions.article_create.properties.title.description": "Title of the article (max 255 characters, min 1 character, required by OpenAPI spec)", "actions.article_create.properties.title.displayName": "Title", "actions.article_delete.description": "Delete an article by ID", "actions.article_delete.displayName": "Delete Article", "actions.article_delete.properties.articleId.description": "Article ID to delete", "actions.article_delete.properties.articleId.displayName": "Article ID", "actions.article_delete.properties.baseId.description": "Knowledge base ID containing the article", "actions.article_delete.properties.baseId.displayName": "Knowledge Base ID", "actions.article_list.description": "Retrieve a list of articles", "actions.article_list.displayName": "List Articles", "actions.article_list.properties.baseId.description": "Knowledge base ID to list articles from", "actions.article_list.properties.baseId.displayName": "Knowledge Base ID", "actions.article_list.properties.query.description": "Search query", "actions.article_list.properties.query.displayName": "Search Query", "actions.article_list.properties.status.description": "Article status filter", "actions.article_list.properties.status.displayName": "Status", "actions.article_retrieve.description": "Retrieve a specific article by ID", "actions.article_retrieve.displayName": "Retrieve Article", "actions.article_retrieve.properties.articleId.description": "Article ID to retrieve", "actions.article_retrieve.properties.articleId.displayName": "Article ID", "actions.article_shortcut_create.description": "Create a shortcut to an existing article", "actions.article_shortcut_create.displayName": "Create Article Shortcut", "actions.article_shortcut_create.properties.articleId.description": "Article ID to create shortcut for", "actions.article_shortcut_create.properties.articleId.displayName": "Article ID", "actions.article_shortcut_create.properties.kbId.description": "Knowledge base ID to create shortcut in", "actions.article_shortcut_create.properties.kbId.displayName": "Knowledge Base ID", "actions.article_template_create.description": "Create a new article template", "actions.article_template_create.displayName": "Create Article Template", "actions.article_template_create.properties.article.description": "Article content structure (required by OpenAPI spec)", "actions.article_template_create.properties.article.displayName": "Article Content", "actions.article_template_create.properties.customDataOrder.description": "Array of custom data IDs defining their display order (based on OpenAPI array of strings)", "actions.article_template_create.properties.customDataOrder.displayName": "Custom Data Order", "actions.article_template_create.properties.description.description": "Template description (max 1000 characters, required by API)", "actions.article_template_create.properties.description.displayName": "Description", "actions.article_template_create.properties.isDefault.description": "Indicates if this is the default template (required by OpenAPI spec)", "actions.article_template_create.properties.isDefault.displayName": "<PERSON>", "actions.article_template_create.properties.templateTitle.description": "Title of the template (max 255 characters, required by OpenAPI spec)", "actions.article_template_create.properties.templateTitle.displayName": "Template Title", "actions.article_template_delete.description": "Delete an article template by ID", "actions.article_template_delete.displayName": "Delete Article Template", "actions.article_template_delete.properties.templateId.description": "Template ID to delete", "actions.article_template_delete.properties.templateId.displayName": "Template ID", "actions.article_template_list.description": "Retrieve a list of article templates", "actions.article_template_list.displayName": "List Article Templates", "actions.article_template_list.properties.cursor.description": "Cursor for pagination (max: 350 chars)", "actions.article_template_list.properties.cursor.displayName": "<PERSON><PERSON><PERSON>", "actions.article_template_list.properties.direction.description": "Direction to retrieve items: 'forward' or 'backward' (default: forward)", "actions.article_template_list.properties.direction.displayName": "Direction", "actions.article_template_list.properties.take.description": "How many items to retrieve (max: 100, default: 10)", "actions.article_template_list.properties.take.displayName": "Take", "actions.article_template_retrieve.description": "Retrieve a specific article template by ID", "actions.article_template_retrieve.displayName": "Retrieve Article Template", "actions.article_template_retrieve.properties.templateId.description": "Template ID to retrieve", "actions.article_template_retrieve.properties.templateId.displayName": "Template ID", "actions.article_template_update.description": "Update an existing article template", "actions.article_template_update.displayName": "Update Article Template", "actions.article_template_update.properties.article.description": "Updated article content structure (required by OpenAPI spec)", "actions.article_template_update.properties.article.displayName": "Article Content", "actions.article_template_update.properties.customDataOrder.description": "Array of custom data IDs defining their display order (based on OpenAPI array of strings)", "actions.article_template_update.properties.customDataOrder.displayName": "Custom Data Order", "actions.article_template_update.properties.description.description": "Updated template description (max 1000 characters, required by OpenAPI spec)", "actions.article_template_update.properties.description.displayName": "Description", "actions.article_template_update.properties.isDefault.description": "Indicates if this is the default template (required by OpenAPI spec)", "actions.article_template_update.properties.isDefault.displayName": "<PERSON>", "actions.article_template_update.properties.templateId.description": "Template ID to update", "actions.article_template_update.properties.templateId.displayName": "Template ID", "actions.article_template_update.properties.templateTitle.description": "Updated template title (max 255 characters, required by OpenAPI spec)", "actions.article_template_update.properties.templateTitle.displayName": "Template Title", "actions.article_update.description": "Update an existing article", "actions.article_update.displayName": "Update Article", "actions.article_update.properties.articleId.description": "Article ID to update", "actions.article_update.properties.articleId.displayName": "Article ID", "actions.article_update.properties.baseId.description": "Knowledge base ID for the article (present in API schema; not required by update endpoint).", "actions.article_update.properties.baseId.displayName": "Knowledge Base ID", "actions.article_update.properties.content.description": "New article content (max 5000 characters)", "actions.article_update.properties.content.displayName": "Content", "actions.article_update.properties.contentRaw.description": "Raw content (JSON/HTML/Markdown, max 50000 chars, required by OpenAPI UpdateArticleRequest)", "actions.article_update.properties.contentRaw.displayName": "Raw Content", "actions.article_update.properties.customData.description": "Custom data fields for additional metadata (array of CustomDataInput objects)", "actions.article_update.properties.customData.displayName": "Custom Data", "actions.article_update.properties.keywords.description": "Article keywords for better searchability (array of strings)", "actions.article_update.properties.keywords.displayName": "Keywords", "actions.article_update.properties.relatedArticles.description": "Related article IDs (array of strings, max 5 items)", "actions.article_update.properties.relatedArticles.displayName": "Related Articles", "actions.article_update.properties.status.description": "Article status (default: published)", "actions.article_update.properties.status.displayName": "Status", "actions.article_update.properties.title.description": "New article title (max 255 characters)", "actions.article_update.properties.title.displayName": "Title", "actions.ask.description": "Ask a question to the knowledge base", "actions.ask.displayName": "Ask", "actions.ask.properties.baseIds.description": "List of Base IDs (array of strings)", "actions.ask.properties.baseIds.displayName": "Base IDs", "actions.ask.properties.customPrompt.description": "Custom prompt for the request", "actions.ask.properties.customPrompt.displayName": "Custom Prompt", "actions.ask.properties.debug.description": "Enable debug mode", "actions.ask.properties.debug.displayName": "Debug", "actions.ask.properties.documentIds.description": "List of Document IDs (array of strings)", "actions.ask.properties.documentIds.displayName": "Document IDs", "actions.ask.properties.folderIds.description": "List of Folder IDs (array of strings)", "actions.ask.properties.folderIds.displayName": "Folder IDs", "actions.ask.properties.maxReferences.description": "Maximum number of references to return", "actions.ask.properties.maxReferences.displayName": "Max References", "actions.ask.properties.metadata.description": "Request metadata (required by OpenAPI spec)", "actions.ask.properties.metadata.displayName": "<PERSON><PERSON><PERSON>", "actions.ask.properties.mode.description": "Mode to use for asking", "actions.ask.properties.mode.displayName": "Mode", "actions.ask.properties.question.description": "Question to ask", "actions.ask.properties.question.displayName": "Question", "actions.ask.properties.scoreThreshold.description": "Score threshold", "actions.ask.properties.scoreThreshold.displayName": "Score Threshold", "actions.ask.properties.similarityThreshold.description": "Similarity threshold", "actions.ask.properties.similarityThreshold.displayName": "Similarity <PERSON><PERSON><PERSON><PERSON>", "actions.ask.properties.stream.description": "Whether to stream the response", "actions.ask.properties.stream.displayName": "Stream", "actions.comment_create.description": "Create a new comment on an article", "actions.comment_create.displayName": "Create Comment", "actions.comment_create.properties.articleId.description": "Article ID to comment on", "actions.comment_create.properties.articleId.displayName": "Article ID", "actions.comment_create.properties.baseId.description": "Knowledge base ID containing the article", "actions.comment_create.properties.baseId.displayName": "Knowledge Base ID", "actions.comment_create.properties.parentCommentId.description": "Parent comment ID for threaded comments (optional)", "actions.comment_create.properties.parentCommentId.displayName": "Parent Comment ID", "actions.comment_create.properties.text.description": "Comment text content", "actions.comment_create.properties.text.displayName": "Text", "actions.comment_delete.description": "Delete a comment by ID", "actions.comment_delete.displayName": "Delete Comment", "actions.comment_delete.properties.articleId.description": "Article ID containing the comment", "actions.comment_delete.properties.articleId.displayName": "Article ID", "actions.comment_delete.properties.baseId.description": "Knowledge base ID containing the article", "actions.comment_delete.properties.baseId.displayName": "Knowledge Base ID", "actions.comment_delete.properties.commentId.description": "Comment ID to delete", "actions.comment_delete.properties.commentId.displayName": "Comment ID", "actions.comment_list.description": "Retrieve comments for an article with pagination", "actions.comment_list.displayName": "List Comments", "actions.comment_list.properties.articleId.description": "Article ID to get comments for", "actions.comment_list.properties.articleId.displayName": "Article ID", "actions.comment_list.properties.baseId.description": "Knowledge base ID containing the article", "actions.comment_list.properties.baseId.displayName": "Knowledge Base ID", "actions.comment_list.properties.cursor.description": "Cursor for pagination (max: 400 chars)", "actions.comment_list.properties.cursor.displayName": "<PERSON><PERSON><PERSON>", "actions.comment_list.properties.direction.description": "Direction to retrieve items: 'forward' or 'backward' (default: forward)", "actions.comment_list.properties.direction.displayName": "Direction", "actions.comment_list.properties.take.description": "How many items to retrieve (max: 100, default: 10)", "actions.comment_list.properties.take.displayName": "Take", "actions.comment_update.description": "Update an existing comment", "actions.comment_update.displayName": "Update Comment", "actions.comment_update.properties.articleId.description": "Article ID containing the comment", "actions.comment_update.properties.articleId.displayName": "Article ID", "actions.comment_update.properties.baseId.description": "Knowledge base ID containing the article", "actions.comment_update.properties.baseId.displayName": "Knowledge Base ID", "actions.comment_update.properties.commentId.description": "Comment ID to update", "actions.comment_update.properties.commentId.displayName": "Comment ID", "actions.comment_update.properties.text.description": "Updated comment text content", "actions.comment_update.properties.text.displayName": "Text", "actions.custom_ask.description": "Ask a question to the knowledge base with custom parameters", "actions.custom_ask.displayName": "Custom Ask", "actions.custom_ask.properties.allowSearchPrivateBases.description": "Allow searching private knowledge bases", "actions.custom_ask.properties.allowSearchPrivateBases.displayName": "Allow Search Private Bases", "actions.custom_ask.properties.baseIds.description": "List of Base IDs (array of strings)", "actions.custom_ask.properties.baseIds.displayName": "Base IDs", "actions.custom_ask.properties.customPrompt.description": "Custom prompt for the request", "actions.custom_ask.properties.customPrompt.displayName": "Custom Prompt", "actions.custom_ask.properties.debug.description": "Enable debug mode", "actions.custom_ask.properties.debug.displayName": "Debug", "actions.custom_ask.properties.documentIds.description": "List of Document IDs (array of strings)", "actions.custom_ask.properties.documentIds.displayName": "Document IDs", "actions.custom_ask.properties.entities.description": "List of entity types to search (array of strings)", "actions.custom_ask.properties.entities.displayName": "Entities", "actions.custom_ask.properties.folderIds.description": "List of Folder IDs (array of strings)", "actions.custom_ask.properties.folderIds.displayName": "Folder IDs", "actions.custom_ask.properties.maxReferences.description": "Maximum number of references to return", "actions.custom_ask.properties.maxReferences.displayName": "Max References", "actions.custom_ask.properties.metadata.description": "Request metadata", "actions.custom_ask.properties.metadata.displayName": "<PERSON><PERSON><PERSON>", "actions.custom_ask.properties.mode.description": "Mode to use for asking", "actions.custom_ask.properties.mode.displayName": "Mode", "actions.custom_ask.properties.question.description": "Question to ask", "actions.custom_ask.properties.question.displayName": "Question", "actions.custom_ask.properties.scoreThreshold.description": "Score threshold", "actions.custom_ask.properties.scoreThreshold.displayName": "Score Threshold", "actions.custom_ask.properties.similarityThreshold.description": "Similarity threshold", "actions.custom_ask.properties.similarityThreshold.displayName": "Similarity <PERSON><PERSON><PERSON><PERSON>", "actions.custom_ask.properties.stream.description": "Whether to stream the response", "actions.custom_ask.properties.stream.displayName": "Stream", "actions.document_delete.description": "Delete a document by ID", "actions.document_delete.displayName": "Delete Document", "actions.document_delete.properties.documentId.description": "Document ID to delete", "actions.document_delete.properties.documentId.displayName": "Document ID", "actions.document_download_link.description": "Get a download link for a document", "actions.document_download_link.displayName": "Get Document Download Link", "actions.document_download_link.properties.documentId.description": "Document ID to get download link for", "actions.document_download_link.properties.documentId.displayName": "Document ID", "actions.document_list.description": "Retrieve a paginated list of documents by parent directory ID (based on OpenAPI /documents)", "actions.document_list.displayName": "List Documents", "actions.document_list.properties.baseId.description": "", "actions.document_list.properties.baseId.displayName": "", "actions.document_retrieve.description": "Retrieve a specific document by ID (based on OpenAPI /documents/'{documentId}')", "actions.document_retrieve.displayName": "Retrieve Document", "actions.document_retrieve.properties.documentId.description": "Document ID to retrieve", "actions.document_retrieve.properties.documentId.displayName": "Document ID", "actions.document_update.description": "Update an existing document", "actions.document_update.displayName": "Update Document", "actions.document_update.properties.accessLevel.description": "Document access level (default: private)", "actions.document_update.properties.accessLevel.displayName": "Access Level", "actions.document_update.properties.documentId.description": "Document ID to update", "actions.document_update.properties.documentId.displayName": "Document ID", "actions.document_update.properties.name.description": "New document name (max 255 chars)", "actions.document_update.properties.name.displayName": "Name", "actions.document_update.properties.status.description": "Document upload status", "actions.document_update.properties.status.displayName": "Status", "actions.document_upload.description": "Generate presigned URLs for document upload and create document records (based on OpenAPI /documents/generateUploadUrl)", "actions.document_upload.displayName": "Generate Document Upload URL", "actions.document_upload.properties.accessLevel.description": "Document access level (default: public)", "actions.document_upload.properties.accessLevel.displayName": "Access Level", "actions.document_upload.properties.contentLength.description": "File size in bytes (required by OpenAPI spec)", "actions.document_upload.properties.contentLength.displayName": "Content Length", "actions.document_upload.properties.contentType.description": "File MIME type (required by OpenAPI spec)", "actions.document_upload.properties.contentType.displayName": "Content Type", "actions.document_upload.properties.description.description": "Document description (max 255 characters, optional)", "actions.document_upload.properties.description.displayName": "Description", "actions.document_upload.properties.file.description": "", "actions.document_upload.properties.file.displayName": "", "actions.document_upload.properties.folderId.description": "Folder ID to upload document to (max 27 characters, required by OpenAPI spec)", "actions.document_upload.properties.folderId.displayName": "Folder ID", "actions.document_upload.properties.name.description": "Document name (max 255 characters, required by OpenAPI spec)", "actions.document_upload.properties.name.displayName": "Name", "actions.folder_create.description": "Create a new folder", "actions.folder_create.displayName": "Create Folder", "actions.folder_create.properties.name.description": "Folder name (max 60 characters, min 1 character, required by OpenAPI spec)", "actions.folder_create.properties.name.displayName": "Name", "actions.folder_create.properties.parentDirId.description": "Parent directory ID (max 27 characters, default: /root)", "actions.folder_create.properties.parentDirId.displayName": "Parent Directory ID", "actions.folder_delete.description": "Delete a folder by ID", "actions.folder_delete.displayName": "Delete Folder", "actions.folder_delete.properties.folderId.description": "Folder ID to delete", "actions.folder_delete.properties.folderId.displayName": "Folder ID", "actions.folder_explorer.description": "Advanced folder exploration with breadcrumb support and cursor pagination", "actions.folder_explorer.displayName": "Folder Explorer", "actions.folder_explorer.properties.cursor.description": "Exclusive cursor key for pagination (max: 400 chars)", "actions.folder_explorer.properties.cursor.displayName": "<PERSON><PERSON><PERSON>", "actions.folder_explorer.properties.direction.description": "Direction to retrieve items: 'forward' or 'backward' (default: forward)", "actions.folder_explorer.properties.direction.displayName": "Direction", "actions.folder_explorer.properties.parentDirId.description": "Parent directory ID (default: /root)", "actions.folder_explorer.properties.parentDirId.displayName": "Parent Directory ID", "actions.folder_explorer.properties.resolveBreadcrumb.description": "Whether to resolve breadcrumb for each document (default: false)", "actions.folder_explorer.properties.resolveBreadcrumb.displayName": "Resolve <PERSON><PERSON>", "actions.folder_explorer.properties.take.description": "How many items to retrieve (max: 100, default: 10)", "actions.folder_explorer.properties.take.displayName": "Take", "actions.folder_list.description": "Retrieve a list of folders", "actions.folder_list.displayName": "List Folders", "actions.folder_list.properties.depth.description": "Maximum depth to traverse (max: 5, default: 2)", "actions.folder_list.properties.depth.displayName": "De<PERSON><PERSON>", "actions.folder_list.properties.parentDirId.description": "Parent directory ID (default: /root)", "actions.folder_list.properties.parentDirId.displayName": "Parent Directory ID", "actions.folder_list.properties.take.description": "Number of items to take (max: 200, default: 100)", "actions.folder_list.properties.take.displayName": "Take", "actions.folder_retrieve.description": "Retrieve a specific folder by ID", "actions.folder_retrieve.displayName": "Retrieve Folder", "actions.folder_retrieve.properties.folderId.description": "Folder ID to retrieve", "actions.folder_retrieve.properties.folderId.displayName": "Folder ID", "actions.folder_update.description": "Update an existing folder", "actions.folder_update.displayName": "Update Folder", "actions.folder_update.properties.folderId.description": "Folder ID to update (max 27 characters)", "actions.folder_update.properties.folderId.displayName": "Folder ID", "actions.folder_update.properties.name.description": "New folder name (max 60 characters, min 1 character, required by OpenAPI spec)", "actions.folder_update.properties.name.displayName": "Name", "actions.kb_create.description": "Create a new knowledge base", "actions.kb_create.displayName": "Create Knowledge Base", "actions.kb_create.properties.accessLevel.description": "Access level (private or public)", "actions.kb_create.properties.accessLevel.displayName": "Access Level", "actions.kb_create.properties.baseType.description": "Type of knowledge base (document, article, or from_datasource)", "actions.kb_create.properties.baseType.displayName": "Base Type", "actions.kb_create.properties.datasourceId.description": "External datasource ID (required by OpenAPI CreateKnowledgeBase spec)", "actions.kb_create.properties.datasourceId.displayName": "Datasource ID", "actions.kb_create.properties.description.description": "Knowledge base description (max 255 characters, optional - can be null)", "actions.kb_create.properties.description.displayName": "Description", "actions.kb_create.properties.name.description": "Knowledge base name (max 60 characters, min 1 character, required by OpenAPI spec)", "actions.kb_create.properties.name.displayName": "Name", "actions.kb_create.properties.parentDirId.description": "Parent directory ID (max 27 characters, default: /root)", "actions.kb_create.properties.parentDirId.displayName": "Parent Directory ID", "actions.kb_delete.description": "Delete a knowledge base by ID", "actions.kb_delete.displayName": "Delete Knowledge Base", "actions.kb_delete.properties.baseId.description": "Knowledge base ID to delete", "actions.kb_delete.properties.baseId.displayName": "Knowledge Base ID", "actions.kb_list.description": "Retrieve a list of knowledge bases", "actions.kb_list.displayName": "List Knowledge Bases", "actions.kb_list.properties.cursor.description": "Pagination cursor", "actions.kb_list.properties.cursor.displayName": "<PERSON><PERSON><PERSON>", "actions.kb_list.properties.direction.description": "Pagination direction", "actions.kb_list.properties.direction.displayName": "Direction", "actions.kb_list.properties.parentDirId.description": "Parent directory ID (default: /root)", "actions.kb_list.properties.parentDirId.displayName": "Parent Directory ID", "actions.kb_list.properties.take.description": "Number of items to take (max: 100, default: 10)", "actions.kb_list.properties.take.displayName": "Take", "actions.kb_retrieve.description": "Retrieve a specific knowledge base by ID", "actions.kb_retrieve.displayName": "Retrieve Knowledge Base", "actions.kb_retrieve.properties.baseId.description": "Knowledge base ID to retrieve", "actions.kb_retrieve.properties.baseId.displayName": "Knowledge Base ID", "actions.kb_update.description": "Update an existing knowledge base", "actions.kb_update.displayName": "Update Knowledge Base", "actions.kb_update.properties.accessLevel.description": "Access level (private or public)", "actions.kb_update.properties.accessLevel.displayName": "Access Level", "actions.kb_update.properties.baseId.description": "Knowledge base ID to update", "actions.kb_update.properties.baseId.displayName": "Knowledge Base ID", "actions.kb_update.properties.description.description": "New knowledge base description (max 255 characters, optional - can be null)", "actions.kb_update.properties.description.displayName": "Description", "actions.kb_update.properties.name.description": "New knowledge base name (max 60 characters, min 1 character, required by OpenAPI spec)", "actions.kb_update.properties.name.displayName": "Name", "actions.kb_update.properties.parentDirId.description": "Parent directory ID (max 27 characters, deprecated - we don't allow change folder for K<PERSON> yet)", "actions.kb_update.properties.parentDirId.displayName": "Parent Directory ID", "actions.search.description": "Search across all entities", "actions.search.displayName": "General Search", "actions.search.properties.entities.description": "Entity types to search (array of strings)", "actions.search.properties.entities.displayName": "Entities", "actions.search.properties.query.description": "Search query", "actions.search.properties.query.displayName": "Search Query", "actions.search_articles.description": "Search specifically for articles", "actions.search_articles.displayName": "Search Articles", "actions.search_articles.properties.baseId.description": "Limit search to specific knowledge base", "actions.search_articles.properties.baseId.displayName": "Knowledge Base ID", "actions.search_articles.properties.query.description": "Search query for articles", "actions.search_articles.properties.query.displayName": "Search Query", "description": "Interact with Knowledge Base API to manage folders, knowledge bases, articles, templates, documents, comments, and perform searches with AI-powered assistance.", "displayName": "Knowledge Base", "schemas.agentResponse.description": "Agent results data", "schemas.agentResponse.displayName": "Agent Response", "schemas.agentResponse.properties.answer.description": "Generated answer", "schemas.agentResponse.properties.answer.displayName": "Answer", "schemas.agentResponse.properties.references.description": "Reference sources", "schemas.agentResponse.properties.references.displayName": "References", "schemas.agentResponse.properties.type.description": "Response type (text or html)", "schemas.agentResponse.properties.type.displayName": "Type", "schemas.articleContent.description": "Article content structure", "schemas.articleContent.displayName": "Article Content", "schemas.articleContent.properties.content.description": "Article content (max 5000 chars)", "schemas.articleContent.properties.content.displayName": "Content", "schemas.articleContent.properties.contentRaw.description": "Article raw content (max 50000 chars)", "schemas.articleContent.properties.contentRaw.displayName": "Raw Content", "schemas.articleContent.properties.keywords.description": "Article keywords", "schemas.articleContent.properties.keywords.displayName": "Keywords", "schemas.articleContent.properties.title.description": "Article title", "schemas.articleContent.properties.title.displayName": "Title", "schemas.articleResponse.description": "Article response data", "schemas.articleResponse.displayName": "Article Response", "schemas.articleResponse.properties.baseId.description": "Knowledge base ID", "schemas.articleResponse.properties.baseId.displayName": "Knowledge Base ID", "schemas.articleResponse.properties.content.description": "Article content", "schemas.articleResponse.properties.content.displayName": "Content", "schemas.articleResponse.properties.contentRaw.description": "Article raw content", "schemas.articleResponse.properties.contentRaw.displayName": "Raw Content", "schemas.articleResponse.properties.createdAt.description": "Creation timestamp", "schemas.articleResponse.properties.createdAt.displayName": "Created At", "schemas.articleResponse.properties.createdBy.description": "User who created the article", "schemas.articleResponse.properties.createdBy.displayName": "Created By", "schemas.articleResponse.properties.customData.description": "Custom data fields", "schemas.articleResponse.properties.customData.displayName": "Custom Data", "schemas.articleResponse.properties.datasourceId.description": "Datasource ID", "schemas.articleResponse.properties.datasourceId.displayName": "Datasource ID", "schemas.articleResponse.properties.dislikeCount.description": "Number of dislikes", "schemas.articleResponse.properties.dislikeCount.displayName": "Dislike Count", "schemas.articleResponse.properties.entityType.description": "Type of entity (article or article-shortcut)", "schemas.articleResponse.properties.entityType.displayName": "Entity Type", "schemas.articleResponse.properties.id.description": "Unique identifier for the article", "schemas.articleResponse.properties.id.displayName": "Article ID", "schemas.articleResponse.properties.isShortcut.description": "Whether this article is a shortcut", "schemas.articleResponse.properties.isShortcut.displayName": "Is Shortcut", "schemas.articleResponse.properties.keywords.description": "Article keywords", "schemas.articleResponse.properties.keywords.displayName": "Keywords", "schemas.articleResponse.properties.likeCount.description": "Number of likes", "schemas.articleResponse.properties.likeCount.displayName": "Like Count", "schemas.articleResponse.properties.originArticleId.description": "Original article ID if this is a shortcut", "schemas.articleResponse.properties.originArticleId.displayName": "Origin Article ID", "schemas.articleResponse.properties.relatedArticles.description": "Related articles", "schemas.articleResponse.properties.relatedArticles.displayName": "Related Articles", "schemas.articleResponse.properties.shortcutArticleIds.description": "List of shortcut article IDs", "schemas.articleResponse.properties.shortcutArticleIds.displayName": "Shortcut Article IDs", "schemas.articleResponse.properties.status.description": "Article status (draft, published, etc.)", "schemas.articleResponse.properties.status.displayName": "Status", "schemas.articleResponse.properties.title.description": "Article title", "schemas.articleResponse.properties.title.displayName": "Title", "schemas.articleResponse.properties.updatedAt.description": "Last update timestamp", "schemas.articleResponse.properties.updatedAt.displayName": "Updated At", "schemas.articleResponse.properties.updatedBy.description": "User who last updated the article", "schemas.articleResponse.properties.updatedBy.displayName": "Updated By", "schemas.articleResponse.properties.version.description": "Article version", "schemas.articleResponse.properties.version.displayName": "Version", "schemas.articleResponse.properties.viewCount.description": "Number of views", "schemas.articleResponse.properties.viewCount.displayName": "View Count", "schemas.askResponse.description": "Ask results data", "schemas.askResponse.displayName": "Ask Response", "schemas.askResponse.properties.answer.description": "Generated answer", "schemas.askResponse.properties.answer.displayName": "Answer", "schemas.askResponse.properties.debugInfo.description": "Debug information", "schemas.askResponse.properties.debugInfo.displayName": "Debug Info", "schemas.askResponse.properties.references.description": "Reference sources", "schemas.askResponse.properties.references.displayName": "References", "schemas.askResponse.properties.type.description": "Response type (text or html)", "schemas.askResponse.properties.type.displayName": "Type", "schemas.commentResponse.description": "Comment response data", "schemas.commentResponse.displayName": "Comment Response", "schemas.commentResponse.properties.articleId.description": "ID of the article being commented on", "schemas.commentResponse.properties.articleId.displayName": "Article ID", "schemas.commentResponse.properties.authorId.description": "ID of the comment author", "schemas.commentResponse.properties.authorId.displayName": "Author ID", "schemas.commentResponse.properties.createdAt.description": "Comment creation timestamp", "schemas.commentResponse.properties.createdAt.displayName": "Created At", "schemas.commentResponse.properties.id.description": "Unique identifier for the comment", "schemas.commentResponse.properties.id.displayName": "Comment ID", "schemas.commentResponse.properties.parentCommentId.description": "ID of the parent comment (for threaded comments)", "schemas.commentResponse.properties.parentCommentId.displayName": "Parent Comment ID", "schemas.commentResponse.properties.text.description": "Comment text content", "schemas.commentResponse.properties.text.displayName": "Text", "schemas.commentResponse.properties.updatedAt.description": "Comment last update timestamp", "schemas.commentResponse.properties.updatedAt.displayName": "Updated At", "schemas.customAskResponse.description": "Custom Ask results data", "schemas.customAskResponse.displayName": "Custom Ask Response", "schemas.customAskResponse.properties.answer.description": "Generated answer", "schemas.customAskResponse.properties.answer.displayName": "Answer", "schemas.customAskResponse.properties.references.description": "Reference sources", "schemas.customAskResponse.properties.references.displayName": "References", "schemas.customAskResponse.properties.type.description": "Response type (text or html)", "schemas.customAskResponse.properties.type.displayName": "Type", "schemas.customDataInput.description": "Custom data input structure (based on OpenAPI TextCustomDataInput and ListCustomDataInput)", "schemas.customDataInput.displayName": "Custom Data Input", "schemas.customDataInput.properties.dataType.description": "Type of custom data: 'text' for string values or 'list' for array values (required by OpenAPI spec)", "schemas.customDataInput.properties.dataType.displayName": "Data Type", "schemas.customDataInput.properties.description.description": "Description of the custom data (optional, default: empty string)", "schemas.customDataInput.properties.description.displayName": "Description", "schemas.customDataInput.properties.title.description": "Title of the custom data (max 255 characters, required by OpenAPI spec)", "schemas.customDataInput.properties.title.displayName": "Title", "schemas.customDataInput.properties.value.description": "Value content - string for 'text' dataType or array of strings for 'list' dataType (default: empty string for text, empty array for list)", "schemas.customDataInput.properties.value.displayName": "Value", "schemas.deleteResponse.description": "Delete operation response", "schemas.deleteResponse.displayName": "Delete Response", "schemas.deleteResponse.properties.id.description": "ID of deleted item", "schemas.deleteResponse.properties.id.displayName": "ID", "schemas.deleteResponse.properties.message.description": "Success message", "schemas.deleteResponse.properties.message.displayName": "Message", "schemas.documentDownloadLinkResponse.description": "Document download link response", "schemas.documentDownloadLinkResponse.displayName": "Document Download Link Response", "schemas.documentDownloadLinkResponse.properties.downloadUrl.description": "Download URL for the document", "schemas.documentDownloadLinkResponse.properties.downloadUrl.displayName": "Download URL", "schemas.documentDownloadLinkResponse.properties.downloadUrlExpires.description": "Expiration time for the download URL", "schemas.documentDownloadLinkResponse.properties.downloadUrlExpires.displayName": "Download URL Expires", "schemas.documentResponse.description": "Document response data", "schemas.documentResponse.displayName": "Document Response", "schemas.documentResponse.properties.accessLevel.description": "Document access level (private, public)", "schemas.documentResponse.properties.accessLevel.displayName": "Access Level", "schemas.documentResponse.properties.createdAt.description": "Creation timestamp", "schemas.documentResponse.properties.createdAt.displayName": "Created At", "schemas.documentResponse.properties.folderId.description": "Folder ID (deprecated)", "schemas.documentResponse.properties.folderId.displayName": "Folder ID", "schemas.documentResponse.properties.id.description": "Unique identifier for the document", "schemas.documentResponse.properties.id.displayName": "Document ID", "schemas.documentResponse.properties.metadata.description": "Document metadata", "schemas.documentResponse.properties.metadata.displayName": "<PERSON><PERSON><PERSON>", "schemas.documentResponse.properties.parentDirBreadcrumb.description": "Path by name (deprecated)", "schemas.documentResponse.properties.parentDirBreadcrumb.displayName": "Parent Directory Breadcrumb", "schemas.documentResponse.properties.parentDirBreadcrumbArray.description": "Path by name array", "schemas.documentResponse.properties.parentDirBreadcrumbArray.displayName": "Parent Directory Breadcrumb Array", "schemas.documentResponse.properties.parentDirId.description": "Parent directory ID", "schemas.documentResponse.properties.parentDirId.displayName": "Parent Directory ID", "schemas.documentResponse.properties.parentDirPath.description": "Path by ULID", "schemas.documentResponse.properties.parentDirPath.displayName": "Parent Directory Path", "schemas.documentResponse.properties.type.description": "Document type", "schemas.documentResponse.properties.type.displayName": "Type", "schemas.documentResponse.properties.updatedAt.description": "Last update timestamp", "schemas.documentResponse.properties.updatedAt.displayName": "Updated At", "schemas.folderResponse.description": "Folder response data", "schemas.folderResponse.displayName": "Folder Response", "schemas.folderResponse.properties.breadcrumb.description": "Path by name (deprecated)", "schemas.folderResponse.properties.breadcrumb.displayName": "Breadcrumb", "schemas.folderResponse.properties.breadcrumbArray.description": "Path by name array", "schemas.folderResponse.properties.breadcrumbArray.displayName": "Breadcrumb Array", "schemas.folderResponse.properties.childFolderCount.description": "Count of child folders", "schemas.folderResponse.properties.childFolderCount.displayName": "Child Folder Count", "schemas.folderResponse.properties.childKbCount.description": "Count of child knowledge bases", "schemas.folderResponse.properties.childKbCount.displayName": "Child Knowledge Base Count", "schemas.folderResponse.properties.count.description": "Count of child items (deprecated)", "schemas.folderResponse.properties.count.displayName": "Count", "schemas.folderResponse.properties.createdAt.description": "Creation timestamp", "schemas.folderResponse.properties.createdAt.displayName": "Created At", "schemas.folderResponse.properties.createdBy.description": "User who created the folder", "schemas.folderResponse.properties.createdBy.displayName": "Created By", "schemas.folderResponse.properties.id.description": "Unique identifier for the folder", "schemas.folderResponse.properties.id.displayName": "Folder ID", "schemas.folderResponse.properties.name.description": "Folder name", "schemas.folderResponse.properties.name.displayName": "Name", "schemas.folderResponse.properties.parentDirId.description": "Parent directory ID", "schemas.folderResponse.properties.parentDirId.displayName": "Parent Directory ID", "schemas.folderResponse.properties.path.description": "Full path of the folder by ULID", "schemas.folderResponse.properties.path.displayName": "Path", "schemas.folderResponse.properties.updatedAt.description": "Last update timestamp", "schemas.folderResponse.properties.updatedAt.displayName": "Updated At", "schemas.folderResponse.properties.updatedBy.description": "User who last updated the folder", "schemas.folderResponse.properties.updatedBy.displayName": "Updated By", "schemas.kbResponse.description": "Knowledge base response data", "schemas.kbResponse.displayName": "Knowledge Base Response", "schemas.kbResponse.properties.accessLevel.description": "Access level (private, public)", "schemas.kbResponse.properties.accessLevel.displayName": "Access Level", "schemas.kbResponse.properties.baseType.description": "Type of knowledge base (document, article, from_datasource)", "schemas.kbResponse.properties.baseType.displayName": "Base Type", "schemas.kbResponse.properties.createdAt.description": "Creation timestamp", "schemas.kbResponse.properties.createdAt.displayName": "Created At", "schemas.kbResponse.properties.createdBy.description": "User who created the knowledge base", "schemas.kbResponse.properties.createdBy.displayName": "Created By", "schemas.kbResponse.properties.datasourceId.description": "Datasource ID", "schemas.kbResponse.properties.datasourceId.displayName": "Datasource ID", "schemas.kbResponse.properties.description.description": "Knowledge base description", "schemas.kbResponse.properties.description.displayName": "Description", "schemas.kbResponse.properties.id.description": "Unique identifier for the knowledge base", "schemas.kbResponse.properties.id.displayName": "Knowledge Base ID", "schemas.kbResponse.properties.name.description": "Knowledge base name", "schemas.kbResponse.properties.name.displayName": "Name", "schemas.kbResponse.properties.parentDirBreadcrumb.description": "Path by name (deprecated)", "schemas.kbResponse.properties.parentDirBreadcrumb.displayName": "Parent Directory Breadcrumb", "schemas.kbResponse.properties.parentDirBreadcrumbArray.description": "Path by name array", "schemas.kbResponse.properties.parentDirBreadcrumbArray.displayName": "Parent Directory Breadcrumb Array", "schemas.kbResponse.properties.parentDirId.description": "Parent directory ID", "schemas.kbResponse.properties.parentDirId.displayName": "Parent Directory ID", "schemas.kbResponse.properties.parentDirPath.description": "Path by ULID", "schemas.kbResponse.properties.parentDirPath.displayName": "Parent Directory Path", "schemas.kbResponse.properties.processType.description": "Knowledge base processing type", "schemas.kbResponse.properties.processType.displayName": "Process Type", "schemas.kbResponse.properties.updatedAt.description": "Last update timestamp", "schemas.kbResponse.properties.updatedAt.displayName": "Updated At", "schemas.kbResponse.properties.updatedBy.description": "User who last updated the knowledge base", "schemas.kbResponse.properties.updatedBy.displayName": "Updated By", "schemas.listResponse.description": "Generic list response structure", "schemas.listResponse.displayName": "List Response", "schemas.listResponse.properties.cursor.description": "Pagination cursor", "schemas.listResponse.properties.cursor.displayName": "<PERSON><PERSON><PERSON>", "schemas.listResponse.properties.data.description": "List of items", "schemas.listResponse.properties.data.displayName": "Data", "schemas.listResponse.properties.hasMore.description": "Whether there are more items", "schemas.listResponse.properties.hasMore.displayName": "Has <PERSON>", "schemas.listResponse.properties.total.description": "Total number of items", "schemas.listResponse.properties.total.displayName": "Total", "schemas.searchResponse.description": "Search results data", "schemas.searchResponse.displayName": "Search Response", "schemas.searchResponse.properties.query.description": "Search query", "schemas.searchResponse.properties.query.displayName": "Query", "schemas.searchResponse.properties.results.description": "Search results", "schemas.searchResponse.properties.results.displayName": "Results", "schemas.searchResponse.properties.took.description": "Time taken for search in milliseconds", "schemas.searchResponse.properties.took.displayName": "Took", "schemas.searchResponse.properties.total.description": "Total number of results", "schemas.searchResponse.properties.total.displayName": "Total", "triggers.deca.kb.article.created.description": "", "triggers.deca.kb.article.created.displayName": "", "triggers.deca.kb.article.deleted.description": "", "triggers.deca.kb.article.deleted.displayName": "", "triggers.deca.kb.article.updated.description": "", "triggers.deca.kb.article.updated.displayName": "", "triggers.deca.kb.document.created.description": "", "triggers.deca.kb.document.created.displayName": "", "triggers.deca.kb.document.deleted.description": "", "triggers.deca.kb.document.deleted.displayName": "", "triggers.deca.kb.document.updated.description": "", "triggers.deca.kb.document.updated.displayName": "", "triggers.deca.kb.folder.created.description": "", "triggers.deca.kb.folder.created.displayName": "", "triggers.deca.kb.folder.deleted.description": "", "triggers.deca.kb.folder.deleted.displayName": "", "triggers.deca.kb.folder.updated.description": "", "triggers.deca.kb.folder.updated.displayName": "", "triggers.deca.kb.knowledgebase.created.description": "", "triggers.deca.kb.knowledgebase.created.displayName": "", "triggers.deca.kb.knowledgebase.deleted.description": "", "triggers.deca.kb.knowledgebase.deleted.displayName": "", "triggers.deca.kb.knowledgebase.updated.description": "", "triggers.deca.kb.knowledgebase.updated.displayName": ""}, "deca-livechat": {"actions.conversation_add_memo.description": "Add a memo to a conversation", "actions.conversation_add_memo.displayName": "Add Conversation Memo", "actions.conversation_add_memo.properties.conversationId.description": "ID of the conversation to add memo", "actions.conversation_add_memo.properties.conversationId.displayName": "Conversation ID", "actions.conversation_add_memo.properties.memo.description": "Memo of the conversation", "actions.conversation_add_memo.properties.memo.displayName": "Memo", "actions.conversation_detail.description": "Get the detail of a conversation", "actions.conversation_detail.displayName": "Get Conversation Detail", "actions.conversation_detail.properties.conversationId.description": "ID of the conversation to get detail from", "actions.conversation_detail.properties.conversationId.displayName": "Conversation ID", "actions.conversation_get_messages.description": "Get all messages in a conversation", "actions.conversation_get_messages.displayName": "Get Conversation Messages", "actions.conversation_get_messages.properties.activityInclude.description": "Whether to include activities messages in the response", "actions.conversation_get_messages.properties.activityInclude.displayName": "Include Activities", "actions.conversation_get_messages.properties.conversationId.description": "ID of the conversation to get messages from", "actions.conversation_get_messages.properties.conversationId.displayName": "Conversation ID", "actions.conversation_get_messages.properties.next.description": "Next cursor to get the next page of items", "actions.conversation_get_messages.properties.next.displayName": "Next", "actions.conversation_get_messages.properties.perPage.description": "Number of items to get per page", "actions.conversation_get_messages.properties.perPage.displayName": "Per Page", "actions.conversation_list.description": "Get a list of all conversations", "actions.conversation_list.displayName": "List Conversations", "actions.conversation_list.properties.assigned.description": "Whether the conversations are assigned", "actions.conversation_list.properties.assigned.displayName": "Assigned", "actions.conversation_list.properties.next.description": "Next cursor to get the next page of items", "actions.conversation_list.properties.next.displayName": "Next", "actions.conversation_list.properties.perPage.description": "Number of items to get per page", "actions.conversation_list.properties.perPage.displayName": "Per Page", "actions.conversation_list.properties.status.description": "Status of the conversations", "actions.conversation_list.properties.status.displayName": "Status", "actions.conversation_list.properties.team.description": "Team ID of the conversations", "actions.conversation_list.properties.team.displayName": "Team ID", "actions.conversation_send_image_message.description": "Send an image message to a conversation", "actions.conversation_send_image_message.displayName": "Send Image Message", "actions.conversation_send_image_message.properties.conversationId.description": "ID of the conversation to send message to", "actions.conversation_send_image_message.properties.conversationId.displayName": "Conversation ID", "actions.conversation_send_image_message.properties.imageUrl.description": "URL of the original image", "actions.conversation_send_image_message.properties.imageUrl.displayName": "Image URL", "actions.conversation_send_text_message.description": "Send a text message to a conversation", "actions.conversation_send_text_message.displayName": "Send Text Message", "actions.conversation_send_text_message.properties.conversationId.description": "ID of the conversation to send message to", "actions.conversation_send_text_message.properties.conversationId.displayName": "Conversation ID", "actions.conversation_send_text_message.properties.message.description": "Text message to send", "actions.conversation_send_text_message.properties.message.displayName": "Message", "actions.conversation_update_assignee_and_team.description": "Update both the assignee and team of a conversation", "actions.conversation_update_assignee_and_team.displayName": "Update Conversation Assignee and Team", "actions.conversation_update_assignee_and_team.properties.assigneeId.description": "ID of the assignee", "actions.conversation_update_assignee_and_team.properties.assigneeId.displayName": "Assignee ID", "actions.conversation_update_assignee_and_team.properties.conversationId.description": "ID of the conversation to update", "actions.conversation_update_assignee_and_team.properties.conversationId.displayName": "Conversation ID", "actions.conversation_update_assignee_and_team.properties.teamId.description": "ID of the team", "actions.conversation_update_assignee_and_team.properties.teamId.displayName": "Team ID", "actions.conversation_update_memo.description": "Update the memo of a conversation", "actions.conversation_update_memo.displayName": "Update Conversation Memo", "actions.conversation_update_memo.properties.conversationId.description": "ID of the conversation to update", "actions.conversation_update_memo.properties.conversationId.displayName": "Conversation ID", "actions.conversation_update_memo.properties.memo.description": "Memo of the conversation", "actions.conversation_update_memo.properties.memo.displayName": "Memo", "actions.conversation_update_memo.properties.memoId.description": "ID of the memo to update", "actions.conversation_update_memo.properties.memoId.displayName": "Memo ID", "actions.conversation_update_status.description": "Update the status of a conversation", "actions.conversation_update_status.displayName": "Update Conversation Status", "actions.conversation_update_status.properties.conversationId.description": "ID of the conversation to update", "actions.conversation_update_status.properties.conversationId.displayName": "Conversation ID", "actions.conversation_update_status.properties.status.description": "Status of the conversation", "actions.conversation_update_status.properties.status.displayName": "Status", "description": "Interact with LiveChat to manage conversations, send messages, and more.", "displayName": "LiveChat", "schemas.conversation.properties.assignee.description": "Information about the assignee", "schemas.conversation.properties.assignee.displayName": "Assignee", "schemas.conversation.properties.assignee.properties.id.description": "ID of the assignee", "schemas.conversation.properties.assignee.properties.id.displayName": "ID", "schemas.conversation.properties.assignee.properties.name.description": "Name of the assignee", "schemas.conversation.properties.assignee.properties.name.displayName": "Name", "schemas.conversation.properties.assigneeId.description": "ID of the team member assigned to the conversation", "schemas.conversation.properties.assigneeId.displayName": "Assignee ID", "schemas.conversation.properties.created.description": "Timestamp when the conversation was created", "schemas.conversation.properties.created.displayName": "Created", "schemas.conversation.properties.enduser.description": "Information about the end user", "schemas.conversation.properties.enduser.displayName": "End User", "schemas.conversation.properties.enduser.properties.id.description": "Unique identifier of the end user", "schemas.conversation.properties.enduser.properties.id.displayName": "ID", "schemas.conversation.properties.enduser.properties.name.description": "Name of the end user", "schemas.conversation.properties.enduser.properties.name.displayName": "Name", "schemas.conversation.properties.enduser.properties.picture.description": "URL to the end user's profile picture", "schemas.conversation.properties.enduser.properties.picture.displayName": "Picture", "schemas.conversation.properties.enduser.properties.platform.description": "Platform the end user is using (e.g., line)", "schemas.conversation.properties.enduser.properties.platform.displayName": "Platform", "schemas.conversation.properties.enduserId.description": "ID of the end user", "schemas.conversation.properties.enduserId.displayName": "End User ID", "schemas.conversation.properties.fromChatbot.description": "Whether the conversation was initiated by a chatbot", "schemas.conversation.properties.fromChatbot.displayName": "From Chatbot", "schemas.conversation.properties.id.description": "Unique identifier for the conversation", "schemas.conversation.properties.id.displayName": "ID", "schemas.conversation.properties.integrationId.description": "ID of the integration being used", "schemas.conversation.properties.integrationId.displayName": "Integration ID", "schemas.conversation.properties.isBookmark.description": "Whether the conversation is bookmarked", "schemas.conversation.properties.isBookmark.displayName": "Is Bookmark", "schemas.conversation.properties.isNewConversation.description": "Whether this is a new conversation", "schemas.conversation.properties.isNewConversation.displayName": "Is New Conversation", "schemas.conversation.properties.name.description": "Name of the conversation", "schemas.conversation.properties.name.displayName": "Name", "schemas.conversation.properties.ocsChannel.description": "Channel identifier for OCS", "schemas.conversation.properties.ocsChannel.displayName": "OCS Channel", "schemas.conversation.properties.orgId.description": "ID of the organization", "schemas.conversation.properties.orgId.displayName": "Organization ID", "schemas.conversation.properties.platform.description": "Information about the platform", "schemas.conversation.properties.platform.displayName": "Platform", "schemas.conversation.properties.status.description": "Current status of the conversation (e.g., new)", "schemas.conversation.properties.status.displayName": "Status", "schemas.conversation.properties.team.description": "Information about the team", "schemas.conversation.properties.team.displayName": "Team", "schemas.conversation.properties.team.properties.id.description": "ID of the team", "schemas.conversation.properties.team.properties.id.displayName": "ID", "schemas.conversation.properties.team.properties.name.description": "Name of the team", "schemas.conversation.properties.team.properties.name.displayName": "Name", "schemas.conversation.properties.teamId.description": "ID of the team handling the conversation", "schemas.conversation.properties.teamId.displayName": "Team ID", "schemas.conversation.properties.unread.description": "Whether the conversation has unread messages", "schemas.conversation.properties.unread.displayName": "Unread", "schemas.conversationPagination.properties.data.description": "Array of items", "schemas.conversationPagination.properties.data.displayName": "Data", "schemas.conversationPagination.properties.pagination.description": "Pagination information", "schemas.conversationPagination.properties.pagination.displayName": "Pagination", "schemas.conversationPagination.properties.pagination.properties.next.description": "Next cursor to get the next page of items", "schemas.conversationPagination.properties.pagination.properties.next.displayName": "Next", "schemas.conversationPagination.properties.pagination.properties.perPage.description": "Number of items to get per page", "schemas.conversationPagination.properties.pagination.properties.perPage.displayName": "Per Page", "schemas.event.properties.assignee.properties.id.description": "Unique identifier for the assignee", "schemas.event.properties.assignee.properties.id.displayName": "ID", "schemas.event.properties.conversation.properties.autoClosedConversation.description": "Whether the conversation is auto closed", "schemas.event.properties.conversation.properties.autoClosedConversation.displayName": "Auto Closed Conversation", "schemas.event.properties.conversation.properties.created.description": "Timestamp when the conversation was created", "schemas.event.properties.conversation.properties.created.displayName": "Created", "schemas.event.properties.conversation.properties.id.description": "Unique identifier for the conversation", "schemas.event.properties.conversation.properties.id.displayName": "ID", "schemas.event.properties.conversation.properties.isAssigned.description": "Whether the conversation is assigned", "schemas.event.properties.conversation.properties.isAssigned.displayName": "Is Assigned", "schemas.event.properties.conversation.properties.memos.description": "Array of memos", "schemas.event.properties.conversation.properties.memos.displayName": "Memos", "schemas.event.properties.conversation.properties.name.description": "Name of the conversation", "schemas.event.properties.conversation.properties.name.displayName": "Name", "schemas.event.properties.conversation.properties.ocsChannel.description": "OCS channel of the conversation", "schemas.event.properties.conversation.properties.ocsChannel.displayName": "OCS Channel", "schemas.event.properties.conversation.properties.orgId.description": "ID of the organization", "schemas.event.properties.conversation.properties.orgId.displayName": "Organization ID", "schemas.event.properties.conversation.properties.platform.description": "Information about the platform", "schemas.event.properties.conversation.properties.platform.displayName": "Platform", "schemas.event.properties.conversation.properties.status.description": "Status of the conversation", "schemas.event.properties.conversation.properties.status.displayName": "Status", "schemas.event.properties.conversation.properties.statusUpdatedAt.description": "Timestamp when the status was updated", "schemas.event.properties.conversation.properties.statusUpdatedAt.displayName": "Status Updated At", "schemas.event.properties.conversation.properties.tags.description": "Array of tags", "schemas.event.properties.conversation.properties.tags.displayName": "Tags", "schemas.event.properties.conversation.properties.teamId.description": "ID of the team", "schemas.event.properties.conversation.properties.teamId.displayName": "Team ID", "schemas.event.properties.enduser.properties.id.description": "Unique identifier for the enduser", "schemas.event.properties.enduser.properties.id.displayName": "ID", "schemas.event.properties.enduser.properties.recurrent.description": "Whether the enduser is recurrent", "schemas.event.properties.enduser.properties.recurrent.displayName": "Recurrent", "schemas.event.properties.team.properties.id.description": "Unique identifier for the team", "schemas.event.properties.team.properties.id.displayName": "ID", "schemas.memo.properties.created.description": "Timestamp when the memo was created", "schemas.memo.properties.created.displayName": "Created", "schemas.memo.properties.id.description": "Unique identifier for the memo", "schemas.memo.properties.id.displayName": "ID", "schemas.memo.properties.sender.properties.email.description": "Email of the sender", "schemas.memo.properties.sender.properties.email.displayName": "Email", "schemas.memo.properties.sender.properties.id.description": "Unique identifier for the sender", "schemas.memo.properties.sender.properties.id.displayName": "ID", "schemas.memo.properties.sender.properties.name.description": "Name of the sender", "schemas.memo.properties.sender.properties.name.displayName": "Name", "schemas.memo.properties.text.description": "Text of the memo", "schemas.memo.properties.text.displayName": "Text", "schemas.message.properties.conversationId.description": "ID of the conversation this message belongs to", "schemas.message.properties.conversationId.displayName": "Conversation ID", "schemas.message.properties.created.description": "Timestamp when the message was created", "schemas.message.properties.created.displayName": "Created", "schemas.message.properties.data.description": "Message data content", "schemas.message.properties.data.displayName": "Data", "schemas.message.properties.data.properties.id.description": "Unique identifier for the message data", "schemas.message.properties.data.properties.id.displayName": "ID", "schemas.message.properties.data.properties.metadata.description": "Additional metadata about the message", "schemas.message.properties.data.properties.metadata.displayName": "<PERSON><PERSON><PERSON>", "schemas.message.properties.data.properties.metadata.properties.sender.description": "Information about the message sender", "schemas.message.properties.data.properties.metadata.properties.sender.displayName": "Sender", "schemas.message.properties.data.properties.metadata.properties.sender.properties.id.description": "Unique identifier of the sender", "schemas.message.properties.data.properties.metadata.properties.sender.properties.id.displayName": "ID", "schemas.message.properties.data.properties.metadata.properties.sender.properties.name.description": "Name of the sender", "schemas.message.properties.data.properties.metadata.properties.sender.properties.name.displayName": "Name", "schemas.message.properties.data.properties.metadata.properties.sender.properties.picture.description": "URL to the sender's profile picture", "schemas.message.properties.data.properties.metadata.properties.sender.properties.picture.displayName": "Picture", "schemas.message.properties.data.properties.metadata.properties.sender.properties.type.description": "Type of the sender (e.g., enduser)", "schemas.message.properties.data.properties.metadata.properties.sender.properties.type.displayName": "Type", "schemas.message.properties.data.properties.text.description": "Text content of the message", "schemas.message.properties.data.properties.text.displayName": "Text", "schemas.message.properties.data.properties.type.description": "Type of the message data", "schemas.message.properties.data.properties.type.displayName": "Type", "schemas.message.properties.from.description": "Source of the message (e.g., user, bot)", "schemas.message.properties.from.displayName": "From", "schemas.message.properties.id.description": "Unique identifier for the message", "schemas.message.properties.id.displayName": "ID", "schemas.message.properties.isFirstMessage.description": "Whether this is the first message in the conversation", "schemas.message.properties.isFirstMessage.displayName": "Is First Message", "schemas.message.properties.sender.description": "Information about the message sender", "schemas.message.properties.sender.displayName": "Sender", "schemas.message.properties.sender.properties.id.description": "Unique identifier of the sender", "schemas.message.properties.sender.properties.id.displayName": "ID", "schemas.message.properties.sender.properties.name.description": "Name of the sender", "schemas.message.properties.sender.properties.name.displayName": "Name", "schemas.message.properties.sender.properties.picture.description": "URL to the sender's profile picture", "schemas.message.properties.sender.properties.picture.displayName": "Picture", "schemas.message.properties.sender.properties.type.description": "Type of the sender (e.g., enduser)", "schemas.message.properties.sender.properties.type.displayName": "Type", "schemas.message.properties.type.description": "Type of the message (e.g., text)", "schemas.message.properties.type.displayName": "Type", "schemas.messagePagination.properties.data.description": "Array of items", "schemas.messagePagination.properties.data.displayName": "Data", "schemas.messagePagination.properties.pagination.description": "Pagination information", "schemas.messagePagination.properties.pagination.displayName": "Pagination", "schemas.messagePagination.properties.pagination.properties.next.description": "Next cursor to get the next page of items", "schemas.messagePagination.properties.pagination.properties.next.displayName": "Next", "schemas.messagePagination.properties.pagination.properties.perPage.description": "Number of items to get per page", "schemas.messagePagination.properties.pagination.properties.perPage.displayName": "Per Page", "schemas.platform.properties.channelId.description": "ID of the channel", "schemas.platform.properties.channelId.displayName": "Channel ID", "schemas.platform.properties.channelName.description": "Name of the channel", "schemas.platform.properties.channelName.displayName": "Channel Name", "schemas.platform.properties.name.description": "Name of the platform", "schemas.platform.properties.name.displayName": "Name", "triggers.deca.livechat.conversation.assignee.updated.description": "", "triggers.deca.livechat.conversation.assignee.updated.displayName": "", "triggers.deca.livechat.conversation.closed.description": "", "triggers.deca.livechat.conversation.closed.displayName": "", "triggers.deca.livechat.conversation.created.description": "", "triggers.deca.livechat.conversation.created.displayName": "", "triggers.deca.livechat.conversation.message.received.description": "", "triggers.deca.livechat.conversation.message.received.displayName": ""}, "deca-ma": {"actions.deca.ma.crm.views.description": "", "actions.deca.ma.crm.views.displayName": "", "actions.deca.ma.crm.views.properties.project_id.description": "", "actions.deca.ma.crm.views.properties.project_id.displayName": "", "actions.deca.ma.marketing_action.create.description": "", "actions.deca.ma.marketing_action.create.displayName": "", "actions.deca.ma.marketing_action.create.properties.data.default.description": "", "actions.deca.ma.marketing_action.create.properties.data.default.settings.description": "", "actions.deca.ma.marketing_action.create.properties.data.description": "", "actions.deca.ma.marketing_action.create.properties.data.displayName": "", "actions.deca.ma.marketing_action.create.properties.project_id.description": "", "actions.deca.ma.marketing_action.create.properties.project_id.displayName": "", "actions.deca.ma.marketing_action.list.description": "", "actions.deca.ma.marketing_action.list.displayName": "", "actions.deca.ma.marketing_action.list.properties.description.description": "", "actions.deca.ma.marketing_action.list.properties.description.displayName": "", "actions.deca.ma.marketing_action.list.properties.project_id.description": "", "actions.deca.ma.marketing_action.list.properties.project_id.displayName": "", "actions.deca.ma.project.list.description": "", "actions.deca.ma.project.list.displayName": "", "actions.deca.ma.segment.create.description": "", "actions.deca.ma.segment.create.displayName": "", "actions.deca.ma.segment.create.properties.data.default.description": "", "actions.deca.ma.segment.create.properties.data.description": "", "actions.deca.ma.segment.create.properties.data.displayName": "", "actions.deca.ma.segment.create.properties.project_id.description": "", "actions.deca.ma.segment.create.properties.project_id.displayName": "", "actions.deca.ma.segment.list.description": "", "actions.deca.ma.segment.list.displayName": "", "actions.deca.ma.segment.list.properties.page.description": "", "actions.deca.ma.segment.list.properties.page.displayName": "", "actions.deca.ma.segment.list.properties.per_page.description": "", "actions.deca.ma.segment.list.properties.per_page.displayName": "", "actions.deca.ma.segment.list.properties.project_id.description": "", "actions.deca.ma.segment.list.properties.project_id.displayName": "", "actions.deca.ma.segment.list.properties.search.description": "", "actions.deca.ma.segment.list.properties.search.displayName": "", "actions.deca.ma.segment.list.properties.sort.description": "", "actions.deca.ma.segment.list.properties.sort.displayName": "", "description": "Interact with DECA MA (Marketing Action) to manage marketing actions.", "displayName": "MA", "schemas.crmView.properties.deca_org_id.description": "DECA organization ID", "schemas.crmView.properties.deca_org_id.displayName": "DECA Org ID", "schemas.crmView.properties.id.description": "Unique identifier for the CRM view", "schemas.crmView.properties.id.displayName": "ID", "schemas.crmView.properties.name.description": "CRM view name", "schemas.crmView.properties.name.displayName": "Name", "schemas.crmView.properties.object_id.description": "Object ID", "schemas.crmView.properties.object_id.displayName": "Object ID", "schemas.crmView.properties.object_name.description": "Object name", "schemas.crmView.properties.object_name.displayName": "Object Name", "schemas.crmView.properties.workspace_id.description": "Workspace identifier", "schemas.crmView.properties.workspace_id.displayName": "Workspace ID", "schemas.marketingAction.properties.created_at.description": "Timestamp when the marketing action was created", "schemas.marketingAction.properties.created_at.displayName": "Created At", "schemas.marketingAction.properties.description.description": "Marketing action description", "schemas.marketingAction.properties.description.displayName": "Description", "schemas.marketingAction.properties.end_at.description": "End time of the marketing action", "schemas.marketingAction.properties.end_at.displayName": "End At", "schemas.marketingAction.properties.id.description": "Unique identifier for the marketing action", "schemas.marketingAction.properties.id.displayName": "ID", "schemas.marketingAction.properties.marketing_action_type.description": "Marketing action type details", "schemas.marketingAction.properties.marketing_action_type.displayName": "Marketing Action Type", "schemas.marketingAction.properties.marketing_action_type_id.description": "ID of the marketing action type", "schemas.marketingAction.properties.marketing_action_type_id.displayName": "Marketing Action Type ID", "schemas.marketingAction.properties.organization_id.description": "Organization identifier", "schemas.marketingAction.properties.organization_id.displayName": "Organization ID", "schemas.marketingAction.properties.project_id.description": "Project identifier", "schemas.marketingAction.properties.project_id.displayName": "Project ID", "schemas.marketingAction.properties.settings.description": "Marketing action settings", "schemas.marketingAction.properties.settings.displayName": "Settings", "schemas.marketingAction.properties.start_at.description": "Start time of the marketing action", "schemas.marketingAction.properties.start_at.displayName": "Start At", "schemas.marketingAction.properties.status.description": "Status of the marketing action", "schemas.marketingAction.properties.status.displayName": "Status", "schemas.marketingAction.properties.target_segments.description": "Target segments for the marketing action", "schemas.marketingAction.properties.target_segments.displayName": "Target Segments", "schemas.marketingAction.properties.updated_at.description": "Timestamp when the marketing action was last updated", "schemas.marketingAction.properties.updated_at.displayName": "Updated At", "schemas.project.properties.created_at.description": "Timestamp when the project was created", "schemas.project.properties.created_at.displayName": "Created At", "schemas.project.properties.id.description": "Unique identifier for the project", "schemas.project.properties.id.displayName": "ID", "schemas.project.properties.name.description": "Project name", "schemas.project.properties.name.displayName": "Name", "schemas.project.properties.organization_id.description": "Organization identifier", "schemas.project.properties.organization_id.displayName": "Organization ID", "schemas.project.properties.settings.description": "Project settings", "schemas.project.properties.settings.displayName": "Settings", "schemas.project.properties.updated_at.description": "Timestamp when the project was last updated", "schemas.project.properties.updated_at.displayName": "Updated At", "schemas.segment.properties.auto_update_flag.description": "Whether the segment auto-updates", "schemas.segment.properties.auto_update_flag.displayName": "Auto Update Flag", "schemas.segment.properties.conditions.description": "Segment conditions", "schemas.segment.properties.conditions.displayName": "Conditions", "schemas.segment.properties.conditions_combination.description": "How conditions are combined", "schemas.segment.properties.conditions_combination.displayName": "Conditions Combination", "schemas.segment.properties.created_at.description": "Timestamp when the segment was created", "schemas.segment.properties.created_at.displayName": "Created At", "schemas.segment.properties.id.description": "Unique identifier for the segment", "schemas.segment.properties.id.displayName": "ID", "schemas.segment.properties.matched_type.description": "Type of matching (e.g., matched_all, matched_any)", "schemas.segment.properties.matched_type.displayName": "Matched Type", "schemas.segment.properties.name.description": "Segment name", "schemas.segment.properties.name.displayName": "Name", "schemas.segment.properties.updated_at.description": "Timestamp when the segment was last updated", "schemas.segment.properties.updated_at.displayName": "Updated At"}, "deca-pages": {"actions.delete_site.data.description": "Empty response on successful deletion", "actions.delete_site.description": "Delete a site by ID", "actions.delete_site.displayName": "Delete Site", "actions.delete_site.properties.siteId.description": "The ID of the site to delete", "actions.delete_site.properties.siteId.displayName": "Site ID", "actions.list_sites.description": "Get a list of all sites", "actions.list_sites.displayName": "List Sites", "actions.list_sites.properties.filter.description": "JSON filter object (e.g., '{\"$or\": [{\"created_at\": {\"$ne\": \"2020-09-03\"}}]}')", "actions.list_sites.properties.filter.displayName": "Filter", "actions.list_sites.properties.page.description": "Page number for pagination", "actions.list_sites.properties.page.displayName": "Page", "actions.list_sites.properties.perPage.description": "Number of items per page", "actions.list_sites.properties.perPage.displayName": "Per Page", "actions.list_sites.properties.search.description": "Search term to filter sites", "actions.list_sites.properties.search.displayName": "Search", "actions.list_sites.properties.sort.description": "Sort by field_name for ASC or -field_name for DESC (e.g., -id)", "actions.list_sites.properties.sort.displayName": "Sort", "actions.list_versions.description": "Get a list of all versions", "actions.list_versions.displayName": "List Versions", "actions.list_versions.properties.filter.description": "Filter by a JSON object (e.g., '{\"$or\": [{\"created_at\": {\"$ne\": \"2020-09-03\"}}]}')", "actions.list_versions.properties.filter.displayName": "Filter", "actions.list_versions.properties.page.description": "Selected page for list display", "actions.list_versions.properties.page.displayName": "Page", "actions.list_versions.properties.perPage.description": "Number of item per page", "actions.list_versions.properties.perPage.displayName": "Per Page", "actions.list_versions.properties.search.description": "A generic search field", "actions.list_versions.properties.search.displayName": "Search", "actions.list_versions.properties.sort.description": "Sort by field_name for ASC or -field_name for DESC, multiple values are supported by \",\" separator (e.g., -id)", "actions.list_versions.properties.sort.displayName": "Sort", "actions.publish_site.description": "Publish a site by ID", "actions.publish_site.displayName": "Publish Site", "actions.publish_site.properties.siteId.description": "The ID of the site to publish", "actions.publish_site.properties.siteId.displayName": "Site ID", "actions.restore_version.description": "Restore a version of a site", "actions.restore_version.displayName": "Restore Version", "actions.restore_version.properties.resourceId.description": "The ID of the resource to restore the version to", "actions.restore_version.properties.resourceId.displayName": "Resource ID", "actions.restore_version.properties.resourceType.description": "The type of resource (default: site)", "actions.restore_version.properties.resourceType.displayName": "Resource Type", "actions.restore_version.properties.saveAsNewVersion.description": "Save the current version as a new version", "actions.restore_version.properties.saveAsNewVersion.displayName": "Save As New Version", "actions.restore_version.properties.versionId.description": "The ID of the version to restore", "actions.restore_version.properties.versionId.displayName": "Version ID", "actions.retrieve_site.description": "Get a site by ID", "actions.retrieve_site.displayName": "Retrieve Site", "actions.retrieve_site.properties.siteId.description": "The ID of the site to retrieve", "actions.retrieve_site.properties.siteId.displayName": "Site ID", "actions.retrieve_site.properties.versionId.description": "Optional version ID to retrieve a specific version of the site", "actions.retrieve_site.properties.versionId.displayName": "Version ID", "actions.unpublish_site.description": "Unpublish a site by ID", "actions.unpublish_site.displayName": "Unpublish Site", "actions.unpublish_site.properties.siteId.description": "The ID of the site to unpublish", "actions.unpublish_site.properties.siteId.displayName": "Site ID", "description": "Interact with DECA Pages.", "displayName": "Pages", "schemas.list_sites_response.properties.pagination.description": "Pagination metadata", "schemas.list_sites_response.properties.pagination.displayName": "Pagination", "schemas.list_sites_response.properties.response.description": "Array of site objects", "schemas.list_sites_response.properties.response.displayName": "Response", "schemas.list_versions_response.properties.pagination.description": "Pagination metadata", "schemas.list_versions_response.properties.pagination.displayName": "Pagination", "schemas.list_versions_response.properties.response.description": "Array of version objects", "schemas.list_versions_response.properties.response.displayName": "Response", "schemas.pagination.properties.current_page.description": "Current page number", "schemas.pagination.properties.current_page.displayName": "Current Page", "schemas.pagination.properties.per_page.description": "Number of items per page", "schemas.pagination.properties.per_page.displayName": "Per Page", "schemas.pagination.properties.total_item.description": "Total number of items available", "schemas.pagination.properties.total_item.displayName": "Total Items", "schemas.pagination.properties.total_page.description": "Total number of pages available", "schemas.pagination.properties.total_page.displayName": "Total Pages", "schemas.publish_site_response.properties.response.description": "Schema for task in document and response", "schemas.publish_site_response.properties.response.displayName": "Response", "schemas.publish_site_response.properties.response.properties.external_id.description": "External task ID", "schemas.publish_site_response.properties.response.properties.external_id.displayName": "External ID", "schemas.publish_site_response.properties.response.properties.id.description": "Task ID", "schemas.publish_site_response.properties.response.properties.id.displayName": "ID", "schemas.publish_site_response.properties.response.properties.request_id.description": "Request ID", "schemas.publish_site_response.properties.response.properties.request_id.displayName": "Request ID", "schemas.retrieve_site_response.properties.response.description": "Site object", "schemas.retrieve_site_response.properties.response.displayName": "Response", "schemas.site.properties.created_at.description": "Timestamp when the site was created", "schemas.site.properties.created_at.displayName": "Created At", "schemas.site.properties.creator_id.description": "ID of the user who created the site", "schemas.site.properties.creator_id.displayName": "Creator ID", "schemas.site.properties.current_version_id.description": "ID of the current version", "schemas.site.properties.current_version_id.displayName": "Current Version ID", "schemas.site.properties.domains.description": "List of domains associated with the site", "schemas.site.properties.domains.displayName": "Domains", "schemas.site.properties.id.description": "Unique identifier for the site", "schemas.site.properties.id.displayName": "ID", "schemas.site.properties.metadata.description": "Additional metadata for the site", "schemas.site.properties.metadata.displayName": "<PERSON><PERSON><PERSON>", "schemas.site.properties.name.description": "Name of the site", "schemas.site.properties.name.displayName": "Name", "schemas.site.properties.publish_task_id.description": "ID of the last publishing task", "schemas.site.properties.publish_task_id.displayName": "Publish Task ID", "schemas.site.properties.published_version_id.description": "ID of the currently published version", "schemas.site.properties.published_version_id.displayName": "Published Version ID", "schemas.site.properties.status.description": "Status of the site", "schemas.site.properties.status.displayName": "Status", "schemas.site.properties.theme.description": "Theme configuration", "schemas.site.properties.theme.displayName": "Theme", "schemas.site.properties.updated_at.description": "Timestamp when the site was last updated", "schemas.site.properties.updated_at.displayName": "Updated At", "schemas.site.properties.visual_assets.description": "Visual assets configuration", "schemas.site.properties.visual_assets.displayName": "Visual Assets", "schemas.version.properties.id.description": "Unique identifier for the version", "schemas.version.properties.id.displayName": "ID", "schemas.version.properties.name.description": "Name of the version", "schemas.version.properties.name.displayName": "Name", "schemas.version.properties.published_at.description": "Timestamp when the version was published", "schemas.version.properties.published_at.displayName": "Published At", "schemas.version.properties.published_by.description": "User who published the version", "schemas.version.properties.published_by.displayName": "Published By", "schemas.version.properties.resource_id.description": "ID of the resource this version belongs to", "schemas.version.properties.resource_id.displayName": "Resource ID", "schemas.version.properties.resource_type.description": "Type of the resource", "schemas.version.properties.resource_type.displayName": "Resource Type", "schemas.version.properties.status.description": "Status of the version", "schemas.version.properties.status.displayName": "Status", "schemas.version.properties.updated_at.description": "Timestamp when the version was last updated", "schemas.version.properties.updated_at.displayName": "Updated At", "schemas.version.properties.updated_by.description": "User who last updated the version", "schemas.version.properties.updated_by.displayName": "Updated By"}, "deca-tables": {"actions.base_create.description": "Create a new base", "actions.base_create.displayName": "Create Base", "actions.base_create.properties.description.description": "Description of the base", "actions.base_create.properties.description.displayName": "Description", "actions.base_create.properties.name.description": "Name of the base", "actions.base_create.properties.name.displayName": "Name", "actions.base_delete.description": "Delete a base", "actions.base_delete.displayName": "Delete Base", "actions.base_delete.properties.baseId.description": "ID of the base to delete", "actions.base_delete.properties.baseId.displayName": "Base ID", "actions.base_list.description": "Get a list of all bases", "actions.base_list.displayName": "List Bases", "actions.base_retrieve.description": "Get a base by ID", "actions.base_retrieve.displayName": "Get Base", "actions.base_retrieve.properties.baseId.description": "ID of the base to retrieve", "actions.base_retrieve.properties.baseId.displayName": "Base ID", "actions.base_update.description": "Update an existing base", "actions.base_update.displayName": "Update Base", "actions.base_update.properties.baseId.description": "ID of the base to update", "actions.base_update.properties.baseId.displayName": "Base ID", "actions.base_update.properties.description.description": "Description of the base", "actions.base_update.properties.description.displayName": "Description", "actions.base_update.properties.name.description": "Name of the base", "actions.base_update.properties.name.displayName": "Name", "actions.field_create.description": "Create a new field in a table", "actions.field_create.displayName": "Create Field", "actions.field_create.properties.baseId.description": "ID of the base to list tables from", "actions.field_create.properties.baseId.displayName": "Base ID", "actions.field_create.properties.name.description": "Name of the field", "actions.field_create.properties.name.displayName": "Name", "actions.field_create.properties.options.description": "Options for the field", "actions.field_create.properties.options.displayName": "Options", "actions.field_create.properties.tableId.description": "ID of the table to create the field in", "actions.field_create.properties.tableId.displayName": "Table ID", "actions.field_create.properties.type.description": "Type of the field", "actions.field_create.properties.type.displayName": "Type", "actions.field_delete.description": "Delete a field from a table", "actions.field_delete.displayName": "Delete Field", "actions.field_delete.properties.baseId.description": "ID of the base to list tables from", "actions.field_delete.properties.baseId.displayName": "Base ID", "actions.field_delete.properties.fieldId.description": "ID of the field to delete", "actions.field_delete.properties.fieldId.displayName": "Field ID", "actions.field_delete.properties.tableId.description": "ID of the table to delete the field from", "actions.field_delete.properties.tableId.displayName": "Table ID", "actions.field_retrieve.description": "Get a field by ID", "actions.field_retrieve.displayName": "Get Field", "actions.field_retrieve.properties.baseId.description": "ID of the base to list tables from", "actions.field_retrieve.properties.baseId.displayName": "Base ID", "actions.field_retrieve.properties.fieldId.description": "ID of the field to retrieve", "actions.field_retrieve.properties.fieldId.displayName": "Field ID", "actions.field_retrieve.properties.tableId.description": "ID of the table to retrieve", "actions.field_retrieve.properties.tableId.displayName": "Table ID", "actions.field_update.description": "Update an existing field in a table", "actions.field_update.displayName": "Update Field", "actions.field_update.properties.baseId.description": "ID of the base to list tables from", "actions.field_update.properties.baseId.displayName": "Base ID", "actions.field_update.properties.fieldId.description": "ID of the field to update", "actions.field_update.properties.fieldId.displayName": "Field ID", "actions.field_update.properties.name.description": "Name of the field", "actions.field_update.properties.name.displayName": "Name", "actions.field_update.properties.options.description": "Options for the field", "actions.field_update.properties.options.displayName": "Options", "actions.field_update.properties.tableId.description": "ID of the table to update the field in", "actions.field_update.properties.tableId.displayName": "Table ID", "actions.field_update.properties.type.description": "Type of the field", "actions.field_update.properties.type.displayName": "Type", "actions.record_create.description": "Create a new record in a table", "actions.record_create.displayName": "Create Record", "actions.record_create.properties.baseId.description": "ID of the base to list tables from", "actions.record_create.properties.baseId.displayName": "Base ID", "actions.record_create.properties.records.description": "Records Data", "actions.record_create.properties.records.displayName": "Records", "actions.record_create.properties.tableId.description": "ID of the table to create the record in", "actions.record_create.properties.tableId.displayName": "Table ID", "actions.record_delete.description": "Delete a record from a table", "actions.record_delete.displayName": "Delete Record", "actions.record_delete.properties.baseId.description": "ID of the base to list tables from", "actions.record_delete.properties.baseId.displayName": "Base ID", "actions.record_delete.properties.recordId.description": "ID of the record to delete", "actions.record_delete.properties.recordId.displayName": "Record ID", "actions.record_delete.properties.tableId.description": "ID of the table to delete the record from", "actions.record_delete.properties.tableId.displayName": "Table ID", "actions.record_list.description": "Get a list of all records in a table", "actions.record_list.displayName": "List Records", "actions.record_list.properties.baseId.description": "ID of the base to list tables from", "actions.record_list.properties.baseId.displayName": "Base ID", "actions.record_list.properties.distinct.description": "Distinct fields", "actions.record_list.properties.distinct.displayName": "Distinct", "actions.record_list.properties.filter.description": "Filter Data", "actions.record_list.properties.filter.displayName": "Filter", "actions.record_list.properties.sort.description": "Sort Data", "actions.record_list.properties.sort.displayName": "Sort", "actions.record_list.properties.tableId.description": "ID of the table to list records from", "actions.record_list.properties.tableId.displayName": "Table ID", "actions.record_retrieve.description": "Get a record by ID", "actions.record_retrieve.displayName": "Get Record", "actions.record_retrieve.properties.baseId.description": "ID of the base to list tables from", "actions.record_retrieve.properties.baseId.displayName": "Base ID", "actions.record_retrieve.properties.recordId.description": "ID of the record to retrieve", "actions.record_retrieve.properties.recordId.displayName": "Record ID", "actions.record_retrieve.properties.tableId.description": "ID of the table to list records from", "actions.record_retrieve.properties.tableId.displayName": "Table ID", "actions.record_update.description": "Update an existing record in a table", "actions.record_update.displayName": "Update Record", "actions.record_update.properties.baseId.description": "ID of the base to list tables from", "actions.record_update.properties.baseId.displayName": "Base ID", "actions.record_update.properties.data.description": "Data to update the record", "actions.record_update.properties.data.displayName": "Data to update", "actions.record_update.properties.recordId.description": "ID of the record to update", "actions.record_update.properties.recordId.displayName": "Record ID", "actions.record_update.properties.tableId.description": "ID of the table to update the record in", "actions.record_update.properties.tableId.displayName": "Table ID", "actions.table_create.description": "Create a new table in a base", "actions.table_create.displayName": "Create Table", "actions.table_create.properties.baseId.description": "ID of the base to create the table in", "actions.table_create.properties.baseId.displayName": "Base ID", "actions.table_create.properties.description.description": "Description of the table", "actions.table_create.properties.description.displayName": "Description", "actions.table_create.properties.fields.description": "Fields to create in the table", "actions.table_create.properties.fields.displayName": "Fields", "actions.table_create.properties.fields.items.properties.description.description": "Field description", "actions.table_create.properties.fields.items.properties.description.displayName": "Description", "actions.table_create.properties.fields.items.properties.name.description": "Field name", "actions.table_create.properties.fields.items.properties.name.displayName": "Name", "actions.table_create.properties.fields.items.properties.required.description": "Whether the field is required", "actions.table_create.properties.fields.items.properties.required.displayName": "Required", "actions.table_create.properties.fields.items.properties.type.description": "Field type", "actions.table_create.properties.fields.items.properties.type.displayName": "Type", "actions.table_create.properties.name.description": "Name of the table", "actions.table_create.properties.name.displayName": "Name", "actions.table_delete.description": "Delete a table", "actions.table_delete.displayName": "Delete Table", "actions.table_delete.properties.baseId.description": "ID of the base to list tables from", "actions.table_delete.properties.baseId.displayName": "Base ID", "actions.table_delete.properties.tableId.description": "ID of the table to delete", "actions.table_delete.properties.tableId.displayName": "Table ID", "actions.table_list.description": "Get a list of all tables in a base", "actions.table_list.displayName": "List Tables", "actions.table_list.properties.baseId.description": "ID of the base to list tables from", "actions.table_list.properties.baseId.displayName": "Base ID", "actions.table_retrieve.description": "Get a table by ID", "actions.table_retrieve.displayName": "Get Table", "actions.table_retrieve.properties.baseId.description": "ID of the base to list tables from", "actions.table_retrieve.properties.baseId.displayName": "Base ID", "actions.table_retrieve.properties.tableId.description": "ID of the table to retrieve", "actions.table_retrieve.properties.tableId.displayName": "Table ID", "actions.table_update.description": "Update an existing table", "actions.table_update.displayName": "Update Table", "actions.table_update.properties.baseId.description": "ID of the base to list tables from", "actions.table_update.properties.baseId.displayName": "Base ID", "actions.table_update.properties.description.description": "Description of the table", "actions.table_update.properties.description.displayName": "Description", "actions.table_update.properties.fields.description": "Fields to create in the table", "actions.table_update.properties.fields.displayName": "Fields", "actions.table_update.properties.fields.items.properties.description.description": "Field description", "actions.table_update.properties.fields.items.properties.description.displayName": "Description", "actions.table_update.properties.fields.items.properties.name.description": "Field name", "actions.table_update.properties.fields.items.properties.name.displayName": "Name", "actions.table_update.properties.fields.items.properties.required.description": "Whether the field is required", "actions.table_update.properties.fields.items.properties.required.displayName": "Required", "actions.table_update.properties.fields.items.properties.type.description": "Field type", "actions.table_update.properties.fields.items.properties.type.displayName": "Type", "actions.table_update.properties.name.description": "Name of the table", "actions.table_update.properties.name.displayName": "Name", "actions.table_update.properties.tableId.description": "ID of the table to update", "actions.table_update.properties.tableId.displayName": "Table ID", "description": "Interact with Tables to manage tables, fields, views, and more.", "displayName": "Tables", "schemas.base.properties.createdAt.description": "Timestamp when the base was created", "schemas.base.properties.createdAt.displayName": "Created At", "schemas.base.properties.deletedAt.description": "Timestamp when the base was deleted, null if not deleted", "schemas.base.properties.deletedAt.displayName": "Deleted At", "schemas.base.properties.description.description": "Description of the base", "schemas.base.properties.description.displayName": "Description", "schemas.base.properties.id.description": "Unique identifier for the base", "schemas.base.properties.id.displayName": "ID", "schemas.base.properties.name.description": "Name of the base", "schemas.base.properties.name.displayName": "Name", "schemas.base.properties.options.description": "Options for the base", "schemas.base.properties.options.displayName": "Options", "schemas.base.properties.updatedAt.description": "Timestamp when the base was last updated", "schemas.base.properties.updatedAt.displayName": "Updated At", "schemas.common.properties.active.description": "Whether the item is active", "schemas.common.properties.active.displayName": "Active", "schemas.common.properties.description.description": "Description", "schemas.common.properties.description.displayName": "Description", "schemas.common.properties.id.description": "Unique identifier", "schemas.common.properties.id.displayName": "ID", "schemas.common.properties.name.description": "Name", "schemas.common.properties.name.displayName": "Name", "schemas.common.properties.objectId.description": "ID of the object the item belongs to", "schemas.common.properties.objectId.displayName": "Object ID", "schemas.common.properties.orgId.description": "Unique identifier for the organization", "schemas.common.properties.orgId.displayName": "Organization ID", "schemas.common.properties.workspaceId.description": "Unique identifier for the workspace", "schemas.common.properties.workspaceId.displayName": "Workspace ID", "schemas.field.properties.createdAt.description": "Timestamp when the field was created", "schemas.field.properties.createdAt.displayName": "Created At", "schemas.field.properties.deletedAt.description": "Timestamp when the field was deleted", "schemas.field.properties.deletedAt.displayName": "Deleted At", "schemas.field.properties.description.description": "Description of the field's purpose", "schemas.field.properties.description.displayName": "Description", "schemas.field.properties.id.description": "Unique identifier for the field", "schemas.field.properties.id.displayName": "ID", "schemas.field.properties.name.description": "Display name of the field", "schemas.field.properties.name.displayName": "Name", "schemas.field.properties.options.description": "Configuration options specific to the field type", "schemas.field.properties.options.displayName": "Options", "schemas.field.properties.system.description": "Whether this is a system-generated field", "schemas.field.properties.system.displayName": "System Field", "schemas.field.properties.type.description": "Type of the field (e.g., text, number, currency, etc.)", "schemas.field.properties.type.displayName": "Type", "schemas.field.properties.updatedAt.description": "Timestamp when the field was last updated", "schemas.field.properties.updatedAt.displayName": "Updated At", "schemas.record.properties.autoId.description": "Automatically generated sequential ID for the record", "schemas.record.properties.autoId.displayName": "Auto ID", "schemas.record.properties.createdAt.description": "Timestamp when the record was created", "schemas.record.properties.createdAt.displayName": "Created At", "schemas.record.properties.createdBy.description": "User who created the record", "schemas.record.properties.createdBy.displayName": "Created By", "schemas.record.properties.createdBy.properties.email.description": "Email of the user", "schemas.record.properties.createdBy.properties.email.displayName": "Email", "schemas.record.properties.createdBy.properties.id.description": "Unique identifier for the user", "schemas.record.properties.createdBy.properties.id.displayName": "ID", "schemas.record.properties.createdBy.properties.name.description": "Name of the user", "schemas.record.properties.createdBy.properties.name.displayName": "Name", "schemas.record.properties.createdBy.properties.picture.description": "URL to the user's profile picture", "schemas.record.properties.createdBy.properties.picture.displayName": "Picture", "schemas.record.properties.id.description": "Unique identifier for the record", "schemas.record.properties.id.displayName": "ID", "schemas.record.properties.limitedAt.description": "Timestamp when the record was limited", "schemas.record.properties.limitedAt.displayName": "Limited At", "schemas.record.properties.updatedAt.description": "Timestamp when the record was last updated", "schemas.record.properties.updatedAt.displayName": "Updated At", "schemas.record.properties.updatedBy.description": "User who last updated the record", "schemas.record.properties.updatedBy.displayName": "Updated By", "schemas.record.properties.updatedBy.properties.email.description": "Email of the user", "schemas.record.properties.updatedBy.properties.email.displayName": "Email", "schemas.record.properties.updatedBy.properties.id.description": "Unique identifier for the user", "schemas.record.properties.updatedBy.properties.id.displayName": "ID", "schemas.record.properties.updatedBy.properties.name.description": "Name of the user", "schemas.record.properties.updatedBy.properties.name.displayName": "Name", "schemas.record.properties.updatedBy.properties.picture.description": "URL to the user's profile picture", "schemas.record.properties.updatedBy.properties.picture.displayName": "Picture", "schemas.table.properties.baseId.description": "ID of the base this table belongs to", "schemas.table.properties.baseId.displayName": "Base ID", "schemas.table.properties.createdAt.description": "Timestamp when the object was created", "schemas.table.properties.createdAt.displayName": "Created At", "schemas.table.properties.description.description": "Description of the object", "schemas.table.properties.description.displayName": "Description", "schemas.table.properties.fields.description": "Object fields", "schemas.table.properties.fields.displayName": "Fields", "schemas.table.properties.fields.items.properties.id.description": "Field ID", "schemas.table.properties.fields.items.properties.id.displayName": "ID", "schemas.table.properties.fields.items.properties.name.description": "Field name", "schemas.table.properties.fields.items.properties.name.displayName": "Name", "schemas.table.properties.fields.items.properties.options.description": "Field options", "schemas.table.properties.fields.items.properties.options.displayName": "Options", "schemas.table.properties.fields.items.properties.system.description": "Whether the field is a system field", "schemas.table.properties.fields.items.properties.system.displayName": "System", "schemas.table.properties.fields.items.properties.type.description": "Field type", "schemas.table.properties.fields.items.properties.type.displayName": "Type", "schemas.table.properties.id.description": "Unique identifier for the table", "schemas.table.properties.id.displayName": "ID", "schemas.table.properties.name.description": "Name of the table", "schemas.table.properties.name.displayName": "Name", "schemas.table.properties.updatedAt.description": "Timestamp when the object was last updated", "schemas.table.properties.updatedAt.displayName": "Updated At", "schemas.timestamps.properties.createdAt.description": "Timestamp when the item was created", "schemas.timestamps.properties.createdAt.displayName": "Created At", "schemas.timestamps.properties.deletedAt.description": "Timestamp when the item was deleted", "schemas.timestamps.properties.deletedAt.displayName": "Deleted At", "schemas.timestamps.properties.updatedAt.description": "Timestamp when the item was last updated", "schemas.timestamps.properties.updatedAt.displayName": "Updated At", "schemas.view.properties.baseId.description": "ID of the base this view belongs to", "schemas.view.properties.baseId.displayName": "Base ID", "schemas.view.properties.createdAt.description": "Timestamp when the view was created", "schemas.view.properties.createdAt.displayName": "Created At", "schemas.view.properties.deletedAt.description": "Timestamp when the view was deleted", "schemas.view.properties.deletedAt.displayName": "Deleted At", "schemas.view.properties.id.description": "Unique identifier for the view", "schemas.view.properties.id.displayName": "ID", "schemas.view.properties.name.description": "Name of the view", "schemas.view.properties.name.displayName": "Name", "schemas.view.properties.options.description": "Options for the view", "schemas.view.properties.options.displayName": "Options", "schemas.view.properties.sort.description": "Sort configurations for the view", "schemas.view.properties.sort.displayName": "Sort", "schemas.view.properties.tableId.description": "ID of the table this view belongs to", "schemas.view.properties.tableId.displayName": "Table ID", "schemas.view.properties.type.description": "Type of view (e.g., grid, kanban, etc.)", "schemas.view.properties.type.displayName": "Type", "schemas.view.properties.updatedAt.description": "Timestamp when the view was last updated", "schemas.view.properties.updatedAt.displayName": "Updated At", "triggers.deca.tables.base.created.description": "", "triggers.deca.tables.base.created.displayName": "", "triggers.deca.tables.base.deleted.description": "", "triggers.deca.tables.base.deleted.displayName": "", "triggers.deca.tables.base.updated.description": "", "triggers.deca.tables.base.updated.displayName": "", "triggers.deca.tables.field.created.description": "", "triggers.deca.tables.field.created.displayName": "", "triggers.deca.tables.field.deleted.description": "", "triggers.deca.tables.field.deleted.displayName": "", "triggers.deca.tables.field.updated.description": "", "triggers.deca.tables.field.updated.displayName": "", "triggers.deca.tables.record.created.description": "", "triggers.deca.tables.record.created.displayName": "", "triggers.deca.tables.record.deleted.description": "", "triggers.deca.tables.record.deleted.displayName": "", "triggers.deca.tables.record.updated.description": "", "triggers.deca.tables.record.updated.displayName": "", "triggers.deca.tables.table.created.description": "", "triggers.deca.tables.table.created.displayName": "", "triggers.deca.tables.table.deleted.description": "", "triggers.deca.tables.table.deleted.displayName": "", "triggers.deca.tables.table.updated.description": "", "triggers.deca.tables.table.updated.displayName": ""}, "filter": {"actions.filter.description": "Filter data based on specified conditions", "actions.filter.displayName": "Filter", "actions.filter.output.properties.filtered.description": "The filtered array of items that match the conditions", "actions.filter.properties.conditions.description": "The conditions to filter by (OR groups with AND conditions)", "actions.filter.properties.conditions.displayName": "Filter Conditions", "actions.filter.properties.continueOnEmpty.description": "Whether to continue the workflow if no items match the filter", "actions.filter.properties.continueOnEmpty.displayName": "Continue on Empty Results", "actions.filter.properties.input.description": "The array of items to filter (supports arrays of objects or arrays of strings/primitives)", "actions.filter.properties.input.displayName": "Input Data", "description": "Filter, map, and reduce operations on data collections", "displayName": "Filter", "schemas.condition_group.properties.and.description": "The conditions to evaluate for and mode", "schemas.condition_group.properties.and.displayName": "AND", "schemas.condition_item.properties.field.description": "The field to check", "schemas.condition_item.properties.field.displayName": "Field", "schemas.condition_item.properties.operator.description": "The comparison operator", "schemas.condition_item.properties.operator.displayName": "Operator", "schemas.condition_item.properties.value.description": "The value to compare against (not required for empty/exist checks)", "schemas.condition_item.properties.value.displayName": "Value", "settings.conditions.description": "The conditions to filter by (OR groups with AND conditions)", "settings.conditions.displayName": "Filter Conditions", "settings.continueOnEmpty.description": "Whether to continue the workflow if no items match the filter", "settings.continueOnEmpty.displayName": "Continue on Empty Results", "settings.input.description": "The array of items to filter (supports arrays of objects or arrays of strings/primitives)", "settings.input.displayName": "Input Data"}, "formatter": {"actions.formatter_dt_add.description": "Add to a date/time value", "actions.formatter_dt_add.displayName": "DateTime Add", "actions.formatter_dt_add.properties.category.description": "The category of the operation", "actions.formatter_dt_add.properties.category.displayName": "Category", "actions.formatter_dt_add.properties.expression.description": "Date math expression", "actions.formatter_dt_add.properties.expression.displayName": "Expression", "actions.formatter_dt_add.properties.from_format.description": "Input date format", "actions.formatter_dt_add.properties.from_format.displayName": "From Format", "actions.formatter_dt_add.properties.input_date.description": "Input date string", "actions.formatter_dt_add.properties.input_date.displayName": "Input Date", "actions.formatter_dt_add.properties.operation.description": "The operation to perform", "actions.formatter_dt_add.properties.operation.displayName": "Operation", "actions.formatter_dt_add.properties.to_format.description": "Output date format", "actions.formatter_dt_add.properties.to_format.displayName": "To Format", "actions.formatter_dt_compare.description": "Compare two date/time values", "actions.formatter_dt_compare.displayName": "DateTime Compare", "actions.formatter_dt_compare.properties.category.description": "The category of the operation", "actions.formatter_dt_compare.properties.category.displayName": "Category", "actions.formatter_dt_compare.properties.end_date.description": "The date to compare.", "actions.formatter_dt_compare.properties.end_date.displayName": "End Date", "actions.formatter_dt_compare.properties.end_date_format.description": "The format of the end date.", "actions.formatter_dt_compare.properties.end_date_format.displayName": "End Date Format", "actions.formatter_dt_compare.properties.operation.description": "The operation to perform", "actions.formatter_dt_compare.properties.operation.displayName": "Operation", "actions.formatter_dt_compare.properties.start_date.description": "The date to compare.", "actions.formatter_dt_compare.properties.start_date.displayName": "Start Date", "actions.formatter_dt_compare.properties.start_date_format.description": "The format of the start date.", "actions.formatter_dt_compare.properties.start_date_format.displayName": "Start Date Format", "actions.formatter_dt_extract.description": "Extract a part of a date/time value (year, month, day, hour, minute, second).", "actions.formatter_dt_extract.displayName": "DateTime Extract", "actions.formatter_dt_extract.properties.category.description": "The category of the operation", "actions.formatter_dt_extract.properties.category.displayName": "Category", "actions.formatter_dt_extract.properties.from_format.description": "The format of the date to extract from (optional, default RFC3339).", "actions.formatter_dt_extract.properties.from_format.displayName": "From Format", "actions.formatter_dt_extract.properties.input_date.description": "The date to extract from.", "actions.formatter_dt_extract.properties.input_date.displayName": "Input Date", "actions.formatter_dt_extract.properties.operation.description": "The operation to perform", "actions.formatter_dt_extract.properties.operation.displayName": "Operation", "actions.formatter_dt_extract.properties.part.description": "The part of the date to extract (e.g., 'year', 'month', 'day', 'hour', 'minute', 'second').", "actions.formatter_dt_extract.properties.part.displayName": "Part", "actions.formatter_dt_format.description": "Format a date/time value to a specified format and timezone", "actions.formatter_dt_format.displayName": "DateTime Format", "actions.formatter_dt_format.properties.category.description": "The category of the operation", "actions.formatter_dt_format.properties.category.displayName": "Category", "actions.formatter_dt_format.properties.from_format.description": "The format of the date to format.", "actions.formatter_dt_format.properties.from_format.displayName": "From Format", "actions.formatter_dt_format.properties.from_timezone.description": "The timezone of the date to format.", "actions.formatter_dt_format.properties.from_timezone.displayName": "From Timezone", "actions.formatter_dt_format.properties.input_date.description": "The date to format.", "actions.formatter_dt_format.properties.input_date.displayName": "Input Date", "actions.formatter_dt_format.properties.operation.description": "The operation to perform", "actions.formatter_dt_format.properties.operation.displayName": "Operation", "actions.formatter_dt_format.properties.to_format.description": "The format to format the date to.", "actions.formatter_dt_format.properties.to_format.displayName": "To Format", "actions.formatter_dt_format.properties.to_timezone.description": "The timezone to format the date to.", "actions.formatter_dt_format.properties.to_timezone.displayName": "To Timezone", "actions.formatter_dt_timezoneConvert.description": "Convert a date/time value from one timezone to another.", "actions.formatter_dt_timezoneConvert.displayName": "DateTime Timezone Convert", "actions.formatter_dt_timezoneConvert.properties.category.description": "The category of the operation", "actions.formatter_dt_timezoneConvert.properties.category.displayName": "Category", "actions.formatter_dt_timezoneConvert.properties.format.description": "The output format (optional, default RFC3339).", "actions.formatter_dt_timezoneConvert.properties.format.displayName": "Format", "actions.formatter_dt_timezoneConvert.properties.from_timezone.description": "The timezone of the input date (IANA name).", "actions.formatter_dt_timezoneConvert.properties.from_timezone.displayName": "From Timezone", "actions.formatter_dt_timezoneConvert.properties.input_date.description": "The date/time string to convert.", "actions.formatter_dt_timezoneConvert.properties.input_date.displayName": "Input Date", "actions.formatter_dt_timezoneConvert.properties.input_format.description": "The input format for parsing input_date (optional, default RFC3339).", "actions.formatter_dt_timezoneConvert.properties.input_format.displayName": "Input Format", "actions.formatter_dt_timezoneConvert.properties.operation.description": "The operation to perform", "actions.formatter_dt_timezoneConvert.properties.operation.displayName": "Operation", "actions.formatter_dt_timezoneConvert.properties.to_timezone.description": "The target timezone (IANA name).", "actions.formatter_dt_timezoneConvert.properties.to_timezone.displayName": "To Timezone", "actions.formatter_number_currencyFormat.description": "Format a number as a currency string.", "actions.formatter_number_currencyFormat.displayName": "Number Currency Format", "actions.formatter_number_currencyFormat.properties.category.description": "The category of the operation", "actions.formatter_number_currencyFormat.properties.category.displayName": "Category", "actions.formatter_number_currencyFormat.properties.currency.description": "Currency code (e.g., USD, EUR, JPY).", "actions.formatter_number_currencyFormat.properties.currency.displayName": "<PERSON><PERSON><PERSON><PERSON>", "actions.formatter_number_currencyFormat.properties.input_number.description": "The number to format as currency.", "actions.formatter_number_currencyFormat.properties.input_number.displayName": "Input Number", "actions.formatter_number_currencyFormat.properties.locale.description": "Locale for formatting (optional, default en-US).", "actions.formatter_number_currencyFormat.properties.locale.displayName": "Locale", "actions.formatter_number_currencyFormat.properties.maximum_fraction_digits.description": "Maximum decimal places (optional).", "actions.formatter_number_currencyFormat.properties.maximum_fraction_digits.displayName": "Maximum Fraction Digits", "actions.formatter_number_currencyFormat.properties.minimum_fraction_digits.description": "Minimum decimal places (optional).", "actions.formatter_number_currencyFormat.properties.minimum_fraction_digits.displayName": "Minimum Fraction Digits", "actions.formatter_number_currencyFormat.properties.operation.description": "The operation to perform", "actions.formatter_number_currencyFormat.properties.operation.displayName": "Operation", "actions.formatter_number_math.description": "Perform a math operation on one or more numbers", "actions.formatter_number_math.displayName": "Number Math", "actions.formatter_number_math.properties.category.description": "The category of the operation", "actions.formatter_number_math.properties.category.displayName": "Category", "actions.formatter_number_math.properties.input_numbers.description": "The numbers to perform the math operation on.", "actions.formatter_number_math.properties.input_numbers.displayName": "Input Numbers", "actions.formatter_number_math.properties.operation.description": "The operation to perform", "actions.formatter_number_math.properties.operation.displayName": "Operation", "actions.formatter_number_math.properties.operation_type.description": "The math operation to perform (add, subtract, multiply, divide, negative, positive).", "actions.formatter_number_math.properties.operation_type.displayName": "Operation Type", "actions.formatter_number_parse.description": "Parse a value (string, int, float, bool) to a number.", "actions.formatter_number_parse.displayName": "Number Parse", "actions.formatter_number_parse.properties.category.description": "The category of the operation", "actions.formatter_number_parse.properties.category.displayName": "Category", "actions.formatter_number_parse.properties.operation.description": "The operation to perform", "actions.formatter_number_parse.properties.operation.displayName": "Operation", "actions.formatter_number_parse.properties.value.description": "The value to parse to a float number (string, int, float, bool).", "actions.formatter_number_parse.properties.value.displayName": "Value", "actions.formatter_number_round.description": "Round a number to the nearest integer or to a specified number of decimal places.", "actions.formatter_number_round.displayName": "Number Round", "actions.formatter_number_round.properties.category.description": "The category of the operation", "actions.formatter_number_round.properties.category.displayName": "Category", "actions.formatter_number_round.properties.input_number.description": "The number to round.", "actions.formatter_number_round.properties.input_number.displayName": "Input Number", "actions.formatter_number_round.properties.operation.description": "The operation to perform", "actions.formatter_number_round.properties.operation.displayName": "Operation", "actions.formatter_number_round.properties.precision.description": "Number of decimal places to round to (optional, default 0).", "actions.formatter_number_round.properties.precision.displayName": "Precision", "actions.formatter_text_capitalize.description": "Capitalize the input text", "actions.formatter_text_capitalize.displayName": "Text Capitalize", "actions.formatter_text_capitalize.properties.category.description": "The category of the operation", "actions.formatter_text_capitalize.properties.category.displayName": "Category", "actions.formatter_text_capitalize.properties.input_text.description": "Input text", "actions.formatter_text_capitalize.properties.input_text.displayName": "Input Text", "actions.formatter_text_capitalize.properties.operation.description": "The operation to perform", "actions.formatter_text_capitalize.properties.operation.displayName": "Operation", "actions.formatter_text_extractEmails.description": "Extract all email addresses from the input text.", "actions.formatter_text_extractEmails.displayName": "Text Extract Emails", "actions.formatter_text_extractEmails.properties.category.description": "The category of the operation", "actions.formatter_text_extractEmails.properties.category.displayName": "Category", "actions.formatter_text_extractEmails.properties.input_text.description": "Input text to extract emails from.", "actions.formatter_text_extractEmails.properties.input_text.displayName": "Input Text", "actions.formatter_text_extractEmails.properties.operation.description": "The operation to perform", "actions.formatter_text_extractEmails.properties.operation.displayName": "Operation", "actions.formatter_text_extractPhones.description": "Extract all phone numbers from the input text.", "actions.formatter_text_extractPhones.displayName": "Text Extract Phones", "actions.formatter_text_extractPhones.properties.category.description": "The category of the operation", "actions.formatter_text_extractPhones.properties.category.displayName": "Category", "actions.formatter_text_extractPhones.properties.input_text.description": "Input text to extract phone numbers from.", "actions.formatter_text_extractPhones.properties.input_text.displayName": "Input Text", "actions.formatter_text_extractPhones.properties.operation.description": "The operation to perform", "actions.formatter_text_extractPhones.properties.operation.displayName": "Operation", "actions.formatter_text_extractUrls.description": "Extract all URLs from the input text.", "actions.formatter_text_extractUrls.displayName": "Text Extract URLs", "actions.formatter_text_extractUrls.properties.category.description": "The category of the operation", "actions.formatter_text_extractUrls.properties.category.displayName": "Category", "actions.formatter_text_extractUrls.properties.input_text.description": "Input text to extract URLs from.", "actions.formatter_text_extractUrls.properties.input_text.displayName": "Input Text", "actions.formatter_text_extractUrls.properties.operation.description": "The operation to perform", "actions.formatter_text_extractUrls.properties.operation.displayName": "Operation", "actions.formatter_text_htmlToMarkdown.description": "Convert HTML to Markdown", "actions.formatter_text_htmlToMarkdown.displayName": "HTML to Markdown", "actions.formatter_text_htmlToMarkdown.properties.category.description": "The category of the operation", "actions.formatter_text_htmlToMarkdown.properties.category.displayName": "Category", "actions.formatter_text_htmlToMarkdown.properties.operation.description": "The operation to perform", "actions.formatter_text_htmlToMarkdown.properties.operation.displayName": "Operation", "actions.formatter_text_htmlToMarkdown.properties.value.description": "The HTML to convert to Markdown.", "actions.formatter_text_htmlToMarkdown.properties.value.displayName": "Value", "actions.formatter_text_join.description": "Join an array of strings with a delimiter", "actions.formatter_text_join.displayName": "Text Join", "actions.formatter_text_join.properties.category.description": "The category of the operation", "actions.formatter_text_join.properties.category.displayName": "Category", "actions.formatter_text_join.properties.delimiter.description": "Delimiter to use for joining.", "actions.formatter_text_join.properties.delimiter.displayName": "Delimiter", "actions.formatter_text_join.properties.operation.description": "The operation to perform", "actions.formatter_text_join.properties.operation.displayName": "Operation", "actions.formatter_text_join.properties.parts.description": "Array of strings to join.", "actions.formatter_text_join.properties.parts.displayName": "Parts", "actions.formatter_text_markdownToHtml.description": "Convert Markdown to HTML", "actions.formatter_text_markdownToHtml.displayName": "Markdown to HTML", "actions.formatter_text_markdownToHtml.properties.category.description": "The category of the operation", "actions.formatter_text_markdownToHtml.properties.category.displayName": "Category", "actions.formatter_text_markdownToHtml.properties.operation.description": "The operation to perform", "actions.formatter_text_markdownToHtml.properties.operation.displayName": "Operation", "actions.formatter_text_markdownToHtml.properties.value.description": "The Markdown to convert to HTML.", "actions.formatter_text_markdownToHtml.properties.value.displayName": "Value", "actions.formatter_text_removeHtml.description": "Remove all HTML tags from the input text.", "actions.formatter_text_removeHtml.displayName": "Text Remove HTML", "actions.formatter_text_removeHtml.properties.category.description": "The category of the operation", "actions.formatter_text_removeHtml.properties.category.displayName": "Category", "actions.formatter_text_removeHtml.properties.input_text.description": "Input text to remove HTML from.", "actions.formatter_text_removeHtml.properties.input_text.displayName": "Input Text", "actions.formatter_text_removeHtml.properties.operation.description": "The operation to perform", "actions.formatter_text_removeHtml.properties.operation.displayName": "Operation", "actions.formatter_text_replace.description": "Replace occurrences of a substring with another substring in the input string.", "actions.formatter_text_replace.displayName": "Text Replace", "actions.formatter_text_replace.properties.category.description": "The category of the operation", "actions.formatter_text_replace.properties.category.displayName": "Category", "actions.formatter_text_replace.properties.count.description": "Number of replacements to make (optional, 0 or negative means replace all).", "actions.formatter_text_replace.properties.count.displayName": "Count", "actions.formatter_text_replace.properties.new.description": "The replacement string.", "actions.formatter_text_replace.properties.new.displayName": "New", "actions.formatter_text_replace.properties.old.description": "The substring to replace.", "actions.formatter_text_replace.properties.old.displayName": "Old", "actions.formatter_text_replace.properties.operation.description": "The operation to perform", "actions.formatter_text_replace.properties.operation.displayName": "Operation", "actions.formatter_text_replace.properties.value.description": "The input string.", "actions.formatter_text_replace.properties.value.displayName": "Value", "actions.formatter_text_split.description": "Split text by a delimiter", "actions.formatter_text_split.displayName": "Text Split", "actions.formatter_text_split.properties.category.description": "The category of the operation", "actions.formatter_text_split.properties.category.displayName": "Category", "actions.formatter_text_split.properties.delimiter.description": "The delimiter to split by.", "actions.formatter_text_split.properties.delimiter.displayName": "Delimiter", "actions.formatter_text_split.properties.operation.description": "The operation to perform", "actions.formatter_text_split.properties.operation.displayName": "Operation", "actions.formatter_text_split.properties.value.description": "The text to split.", "actions.formatter_text_split.properties.value.displayName": "Value", "actions.formatter_text_substring.description": "Extract a substring from the input text.", "actions.formatter_text_substring.displayName": "Text Substring", "actions.formatter_text_substring.properties.category.description": "The category of the operation", "actions.formatter_text_substring.properties.category.displayName": "Category", "actions.formatter_text_substring.properties.input_text.description": "Input text to extract substring from.", "actions.formatter_text_substring.properties.input_text.displayName": "Input Text", "actions.formatter_text_substring.properties.length.description": "Number of characters to extract (optional, if omitted or negative, go to end).", "actions.formatter_text_substring.properties.length.displayName": "Length", "actions.formatter_text_substring.properties.operation.description": "The operation to perform", "actions.formatter_text_substring.properties.operation.displayName": "Operation", "actions.formatter_text_substring.properties.start.description": "Starting index (0-based).", "actions.formatter_text_substring.properties.start.displayName": "Start", "actions.formatter_text_toLower.description": "Convert text to lower case.", "actions.formatter_text_toLower.displayName": "Text To Lower Case", "actions.formatter_text_toLower.properties.category.description": "The category of the operation", "actions.formatter_text_toLower.properties.category.displayName": "Category", "actions.formatter_text_toLower.properties.input_text.description": "Input text to convert to lower case.", "actions.formatter_text_toLower.properties.input_text.displayName": "Input Text", "actions.formatter_text_toLower.properties.operation.description": "The operation to perform", "actions.formatter_text_toLower.properties.operation.displayName": "Operation", "actions.formatter_text_toUpper.description": "Convert text to upper case.", "actions.formatter_text_toUpper.displayName": "Text To Upper Case", "actions.formatter_text_toUpper.properties.category.description": "The category of the operation", "actions.formatter_text_toUpper.properties.category.displayName": "Category", "actions.formatter_text_toUpper.properties.input_text.description": "Input text to convert to upper case.", "actions.formatter_text_toUpper.properties.input_text.displayName": "Input Text", "actions.formatter_text_toUpper.properties.operation.description": "The operation to perform", "actions.formatter_text_toUpper.properties.operation.displayName": "Operation", "actions.formatter_text_trim.description": "Trim whitespace or specified characters from the input text.", "actions.formatter_text_trim.displayName": "Text Trim", "actions.formatter_text_trim.properties.category.description": "The category of the operation", "actions.formatter_text_trim.properties.category.displayName": "Category", "actions.formatter_text_trim.properties.chars.description": "Characters to trim (optional, default is whitespace).", "actions.formatter_text_trim.properties.chars.displayName": "Chars", "actions.formatter_text_trim.properties.input_text.description": "Input text to trim.", "actions.formatter_text_trim.properties.input_text.displayName": "Input Text", "actions.formatter_text_trim.properties.operation.description": "The operation to perform", "actions.formatter_text_trim.properties.operation.displayName": "Operation", "actions.formatter_utilities_generateRandom.description": "Generate a random integer or float in a given range.", "actions.formatter_utilities_generateRandom.displayName": "Generate Random", "actions.formatter_utilities_generateRandom.properties.category.description": "The category of the operation", "actions.formatter_utilities_generateRandom.properties.category.displayName": "Category", "actions.formatter_utilities_generateRandom.properties.charset.description": "Characters to use for string generation. Optional. Default: alphanumeric.", "actions.formatter_utilities_generateRandom.properties.charset.displayName": "Charset", "actions.formatter_utilities_generateRandom.properties.length.description": "Length of the generated string (required for type=string).", "actions.formatter_utilities_generateRandom.properties.length.displayName": "Length", "actions.formatter_utilities_generateRandom.properties.max.description": "Maximum value (exclusive for int, exclusive for float). Optional. Default: 100 for int, 1 for float. Ignored for string.", "actions.formatter_utilities_generateRandom.properties.max.displayName": "Max", "actions.formatter_utilities_generateRandom.properties.min.description": "Minimum value (inclusive for int, inclusive for float). Optional. Default: 0. Ignored for string.", "actions.formatter_utilities_generateRandom.properties.min.displayName": "Min", "actions.formatter_utilities_generateRandom.properties.operation.description": "The operation to perform", "actions.formatter_utilities_generateRandom.properties.operation.displayName": "Operation", "actions.formatter_utilities_generateRandom.properties.type.description": "Type of random value to generate: int, float, or string.", "actions.formatter_utilities_generateRandom.properties.type.displayName": "Type", "actions.formatter_utilities_lookupTable.description": "Given a key and table, find the matching value.", "actions.formatter_utilities_lookupTable.displayName": "Lookup Table", "actions.formatter_utilities_lookupTable.properties.category.description": "The category of the operation", "actions.formatter_utilities_lookupTable.properties.category.displayName": "Category", "actions.formatter_utilities_lookupTable.properties.default.description": "Default value to return if key is not found.", "actions.formatter_utilities_lookupTable.properties.default.displayName": "<PERSON><PERSON><PERSON>", "actions.formatter_utilities_lookupTable.properties.key.description": "The key to look up in the table.", "actions.formatter_utilities_lookupTable.properties.key.displayName": "Key", "actions.formatter_utilities_lookupTable.properties.operation.description": "The operation to perform", "actions.formatter_utilities_lookupTable.properties.operation.displayName": "Operation", "actions.formatter_utilities_lookupTable.properties.table.description": "The lookup table as an object (map).", "actions.formatter_utilities_lookupTable.properties.table.displayName": "Table", "actions.formatter_utilities_pickFromList.description": "Pick an item from a list using an operation (first, last, random).", "actions.formatter_utilities_pickFromList.displayName": "Pick From List", "actions.formatter_utilities_pickFromList.properties.category.description": "The category of the operation", "actions.formatter_utilities_pickFromList.properties.category.displayName": "Category", "actions.formatter_utilities_pickFromList.properties.default.description": "Default value to return if input is empty.", "actions.formatter_utilities_pickFromList.properties.default.displayName": "<PERSON><PERSON><PERSON>", "actions.formatter_utilities_pickFromList.properties.input.description": "List of items to pick from.", "actions.formatter_utilities_pickFromList.properties.input.displayName": "Input", "actions.formatter_utilities_pickFromList.properties.operation.description": "The operation to perform", "actions.formatter_utilities_pickFromList.properties.operation.displayName": "Operation", "actions.formatter_utilities_pickFromList.properties.operation_type.description": "The operation to perform", "actions.formatter_utilities_pickFromList.properties.operation_type.displayName": "Operation Type", "description": "Format data based on specified conditions", "displayName": "<PERSON><PERSON><PERSON>", "schemas.dt_add_data.description": "Data for the DateTime Add operation", "schemas.dt_add_data.displayName": "DateTime Add Data", "schemas.dt_add_data.properties.output_date.description": "Resulting date string", "schemas.dt_add_data.properties.output_date.displayName": "Output Date", "schemas.dt_compare_data.description": "Data for the DateTime Compare operation", "schemas.dt_compare_data.displayName": "DateTime Compare Data", "schemas.dt_compare_data.properties.dates_swapped.description": "True if start_date is after end_date and the dates were swapped.", "schemas.dt_compare_data.properties.dates_swapped.displayName": "Dates Swapped", "schemas.dt_compare_data.properties.days_difference.description": "Difference in days.", "schemas.dt_compare_data.properties.days_difference.displayName": "Days Difference", "schemas.dt_compare_data.properties.hours_difference.description": "Difference in hours.", "schemas.dt_compare_data.properties.hours_difference.displayName": "Hours Difference", "schemas.dt_compare_data.properties.minutes_difference.description": "Difference in minutes.", "schemas.dt_compare_data.properties.minutes_difference.displayName": "Minutes Difference", "schemas.dt_compare_data.properties.same_date.description": "True if start_date and end_date are the same.", "schemas.dt_compare_data.properties.same_date.displayName": "Same Date", "schemas.dt_compare_data.properties.seconds_difference.description": "Difference in seconds.", "schemas.dt_compare_data.properties.seconds_difference.displayName": "Seconds Difference", "schemas.dt_extract_data.description": "Data for the DateTime Extract operation", "schemas.dt_extract_data.displayName": "DateTime Extract Data", "schemas.dt_extract_data.properties.value.description": "The extracted value.", "schemas.dt_extract_data.properties.value.displayName": "Value", "schemas.dt_format_data.description": "Data for the DateTime Format operation", "schemas.dt_format_data.displayName": "DateTime Format Data", "schemas.dt_format_data.properties.formatted_date.description": "The formatted date.", "schemas.dt_format_data.properties.formatted_date.displayName": "Formatted Date", "schemas.dt_timezoneConvert_data.description": "Data for the DateTime Timezone Convert operation", "schemas.dt_timezoneConvert_data.displayName": "DateTime Timezone Convert Data", "schemas.dt_timezoneConvert_data.properties.output_date.description": "The converted date/time string.", "schemas.dt_timezoneConvert_data.properties.output_date.displayName": "Output Date", "schemas.number_currencyFormat_data.description": "Data for the Number Currency Format operation", "schemas.number_currencyFormat_data.displayName": "Number Currency Format Data", "schemas.number_currencyFormat_data.properties.output.description": "The formatted currency string.", "schemas.number_currencyFormat_data.properties.output.displayName": "Output", "schemas.number_math_data.description": "Data for the Number Math operation", "schemas.number_math_data.displayName": "Number Math Data", "schemas.number_math_data.properties.result.description": "Resulting number", "schemas.number_math_data.properties.result.displayName": "Result", "schemas.number_parse_data.description": "Data for the Number Parse operation", "schemas.number_parse_data.displayName": "Number Parse Data", "schemas.number_parse_data.properties.output_number.description": "The parsed number.", "schemas.number_parse_data.properties.output_number.displayName": "Output Number", "schemas.number_round_data.description": "Data for the Number Round operation", "schemas.number_round_data.displayName": "Number Round Data", "schemas.number_round_data.properties.output_number.description": "The rounded number.", "schemas.number_round_data.properties.output_number.displayName": "Output Number", "schemas.text_capitalize_data.description": "Data for the Text Capitalize operation", "schemas.text_capitalize_data.displayName": "Text Capitalize Data", "schemas.text_capitalize_data.properties.output_text.description": "Capitalized text", "schemas.text_capitalize_data.properties.output_text.displayName": "Output Text", "schemas.text_extractEmails_data.description": "Data for the Text Extract Emails operation", "schemas.text_extractEmails_data.displayName": "Text Extract Emails Data", "schemas.text_extractEmails_data.properties.matches.description": "Extracted emails.", "schemas.text_extractEmails_data.properties.matches.displayName": "Matches", "schemas.text_extractPhones_data.description": "Data for the Text Extract Phones operation", "schemas.text_extractPhones_data.displayName": "Text Extract Phones Data", "schemas.text_extractPhones_data.properties.matches.description": "Extracted phone numbers.", "schemas.text_extractPhones_data.properties.matches.displayName": "Matches", "schemas.text_extractUrls_data.description": "Data for the Text Extract URLs operation", "schemas.text_extractUrls_data.displayName": "Text Extract URLs Data", "schemas.text_extractUrls_data.properties.matches.description": "Extracted URLs.", "schemas.text_extractUrls_data.properties.matches.displayName": "Matches", "schemas.text_htmlToMarkdown_data.description": "Data for the Text HTML to Markdown operation", "schemas.text_htmlToMarkdown_data.displayName": "Text HTML to Markdown Data", "schemas.text_htmlToMarkdown_data.properties.result.description": "The resulting Markdown.", "schemas.text_htmlToMarkdown_data.properties.result.displayName": "Result", "schemas.text_join_data.description": "Data for the Text Join operation", "schemas.text_join_data.displayName": "Text Join Data", "schemas.text_join_data.properties.result.description": "The joined string.", "schemas.text_join_data.properties.result.displayName": "Result", "schemas.text_markdownToHtml_data.description": "Data for the Text Markdown to HTML operation", "schemas.text_markdownToHtml_data.displayName": "Text Markdown to HTML Data", "schemas.text_markdownToHtml_data.properties.result.description": "The resulting HTML.", "schemas.text_markdownToHtml_data.properties.result.displayName": "Result", "schemas.text_removeHtml_data.description": "Data for the Text Remove HTML operation", "schemas.text_removeHtml_data.displayName": "Text Remove HTML Data", "schemas.text_removeHtml_data.properties.output_text.description": "Text with HTML removed.", "schemas.text_removeHtml_data.properties.output_text.displayName": "Output Text", "schemas.text_replace_data.description": "Data for the Text Replace operation", "schemas.text_replace_data.displayName": "Text Replace Data", "schemas.text_replace_data.properties.result.description": "The resulting string after replacement.", "schemas.text_replace_data.properties.result.displayName": "Result", "schemas.text_split_data.description": "Data for the Text Split operation", "schemas.text_split_data.displayName": "Text Split Data", "schemas.text_split_data.properties.parts.description": "The resulting parts after splitting.", "schemas.text_split_data.properties.parts.displayName": "Parts", "schemas.text_substring_data.description": "Data for the Text Substring operation", "schemas.text_substring_data.displayName": "Text Substring Data", "schemas.text_substring_data.properties.output_text.description": "Extracted substring.", "schemas.text_substring_data.properties.output_text.displayName": "Output Text", "schemas.text_toLower_data.description": "Data for the Text To Lower operation", "schemas.text_toLower_data.displayName": "Text To Lower Data", "schemas.text_toLower_data.properties.output_text.description": "Lower case text.", "schemas.text_toLower_data.properties.output_text.displayName": "Output Text", "schemas.text_toUpper_data.description": "Data for the Text To Upper operation", "schemas.text_toUpper_data.displayName": "Text To Upper Data", "schemas.text_toUpper_data.properties.output_text.description": "Upper case text.", "schemas.text_toUpper_data.properties.output_text.displayName": "Output Text", "schemas.text_trim_data.description": "Data for the Text Trim operation", "schemas.text_trim_data.displayName": "Text Trim Data", "schemas.text_trim_data.properties.output_text.description": "Trimmed text.", "schemas.text_trim_data.properties.output_text.displayName": "Output Text", "schemas.utilities_generateRandom_data.description": "Data for the Generate Random operation", "schemas.utilities_generateRandom_data.displayName": "Generate Random Data", "schemas.utilities_generateRandom_data.properties.value.description": "The generated random value.", "schemas.utilities_generateRandom_data.properties.value.displayName": "Value", "schemas.utilities_lookupTable_data.description": "Data for the Lookup Table operation", "schemas.utilities_lookupTable_data.displayName": "Lookup Table Data", "schemas.utilities_lookupTable_data.properties.value.description": "The value found for the key, or the default if not found.", "schemas.utilities_lookupTable_data.properties.value.displayName": "Value", "schemas.utilities_pickFromList_data.description": "Data for the Pick From List operation", "schemas.utilities_pickFromList_data.displayName": "Pick From List Data", "schemas.utilities_pickFromList_data.properties.picked.description": "The picked item from the list.", "schemas.utilities_pickFromList_data.properties.picked.displayName": "Picked"}, "function": {"actions.executeFunction.description": "Execute a function from the function server", "actions.executeFunction.displayName": "Execute Function", "actions.executeFunction.properties.functionId.description": "The ID of the function to execute", "actions.executeFunction.properties.functionId.displayName": "Function ID", "actions.executeFunction.properties.input.description": "Input data for the function", "actions.executeFunction.properties.input.displayName": "Input", "description": "Execute a function from the function server", "displayName": "Function", "schemas.functionOutput.properties.error.description": "The error message if the code execution fails", "schemas.functionOutput.properties.error.displayName": "Error", "schemas.functionOutput.properties.logs.description": "The logs of the code execution", "schemas.functionOutput.properties.logs.displayName": "Logs", "schemas.functionOutput.properties.output.description": "Output data from the function execution", "schemas.functionOutput.properties.output.displayName": "Output"}, "google_calendar": {"actions.addAttendee.description": "Add an attendee to a calendar event", "actions.addAttendee.displayName": "<PERSON><PERSON>", "actions.addAttendee.properties.attendee.description": "The attendee to add to the event", "actions.addAttendee.properties.attendee.displayName": "<PERSON><PERSON><PERSON>", "actions.addAttendee.properties.attendee.properties.displayName.description": "The display name of the attendee", "actions.addAttendee.properties.attendee.properties.displayName.displayName": "Display Name", "actions.addAttendee.properties.attendee.properties.email.description": "The email address of the attendee", "actions.addAttendee.properties.attendee.properties.email.displayName": "Email", "actions.addAttendee.properties.calendarId.description": "The calendar identifier", "actions.addAttendee.properties.calendarId.displayName": "Calendar ID", "actions.addAttendee.properties.eventId.description": "The ID of the event to add the attendee to", "actions.addAttendee.properties.eventId.displayName": "Event ID", "actions.createEvent.description": "Create a new calendar event", "actions.createEvent.displayName": "Create Event", "actions.createEvent.properties.attendees.description": "Email addresses of attendees", "actions.createEvent.properties.attendees.displayName": "Attendees", "actions.createEvent.properties.calendarId.description": "The calendar identifier", "actions.createEvent.properties.calendarId.displayName": "Calendar ID", "actions.createEvent.properties.description.description": "The event description", "actions.createEvent.properties.description.displayName": "Description", "actions.createEvent.properties.end.description": "The event end time", "actions.createEvent.properties.end.displayName": "End Time", "actions.createEvent.properties.location.description": "The event location", "actions.createEvent.properties.location.displayName": "Location", "actions.createEvent.properties.reminders.description": "Event reminders", "actions.createEvent.properties.reminders.displayName": "Reminders", "actions.createEvent.properties.start.description": "The event start time", "actions.createEvent.properties.start.displayName": "Start Time", "actions.createEvent.properties.summary.description": "The event title", "actions.createEvent.properties.summary.displayName": "Summary", "actions.getEvent.description": "Get details of a calendar event", "actions.getEvent.displayName": "Get Event", "actions.getEvent.properties.calendarId.description": "The calendar identifier", "actions.getEvent.properties.calendarId.displayName": "Calendar ID", "actions.getEvent.properties.eventId.description": "The ID of the event to retrieve", "actions.getEvent.properties.eventId.displayName": "Event ID", "actions.listEvents.data.items.description": "List of events", "actions.listEvents.data.items.displayName": "Events", "actions.listEvents.data.nextPageToken.description": "Token for retrieving the next page of results", "actions.listEvents.data.nextPageToken.displayName": "Next Page Token", "actions.listEvents.description": "List calendar events based on filters", "actions.listEvents.displayName": "List Events", "actions.listEvents.properties.calendarId.description": "The calendar identifier", "actions.listEvents.properties.calendarId.displayName": "Calendar ID", "actions.listEvents.properties.maxResults.description": "Maximum number of events to return", "actions.listEvents.properties.maxResults.displayName": "Max Results", "actions.listEvents.properties.timeMax.description": "End time for filtering events", "actions.listEvents.properties.timeMax.displayName": "End Time", "actions.listEvents.properties.timeMin.description": "Start time for filtering events", "actions.listEvents.properties.timeMin.displayName": "Start Time", "actions.removeAttendee.description": "Remove an attendee from a calendar event", "actions.removeAttendee.displayName": "<PERSON><PERSON><PERSON>", "actions.removeAttendee.properties.attendee.description": "The attendee to remove from the event", "actions.removeAttendee.properties.attendee.displayName": "<PERSON><PERSON><PERSON>", "actions.removeAttendee.properties.attendee.properties.email.description": "The email address of the attendee to remove", "actions.removeAttendee.properties.attendee.properties.email.displayName": "Email", "actions.removeAttendee.properties.calendarId.description": "The calendar identifier", "actions.removeAttendee.properties.calendarId.displayName": "Calendar ID", "actions.removeAttendee.properties.eventId.description": "The ID of the event to remove the attendee from", "actions.removeAttendee.properties.eventId.displayName": "Event ID", "credentials.api_key.description": "API Key authentication for Google Calendar API", "credentials.api_key.displayName": "API Key", "credentials.api_key.properties.apiKey.description": "Your Google Calendar API key", "credentials.api_key.properties.apiKey.displayName": "API Key", "credentials.oauth2.description": "OAuth 2.0 authentication for Google Calendar API", "credentials.oauth2.displayName": "OAuth 2.0", "credentials.oauth2.properties.accessToken.description": "The OAuth access token", "credentials.oauth2.properties.accessToken.displayName": "Access Token", "credentials.oauth2.properties.clientId.description": "The client ID from your Google OAuth app", "credentials.oauth2.properties.clientId.displayName": "Client ID", "credentials.oauth2.properties.clientSecret.description": "The client secret from your Google OAuth app", "credentials.oauth2.properties.clientSecret.displayName": "Client Secret", "credentials.oauth2.properties.refreshToken.description": "The OAuth refresh token", "credentials.oauth2.properties.refreshToken.displayName": "Refresh <PERSON>", "credentials.oauth2.properties.tokenExpiresAt.description": "The timestamp when the token expires (ISO format)", "credentials.oauth2.properties.tokenExpiresAt.displayName": "Token Expires At", "description": "Interact with Google Calendar events", "displayName": "Google Calendar", "schemas.calendarEvent.properties.attendees.description": "The event attendees", "schemas.calendarEvent.properties.attendees.displayName": "Attendees", "schemas.calendarEvent.properties.creator.description": "The event creator", "schemas.calendarEvent.properties.creator.displayName": "Creator", "schemas.calendarEvent.properties.description.description": "The event description", "schemas.calendarEvent.properties.description.displayName": "Description", "schemas.calendarEvent.properties.end.description": "The end time of the event", "schemas.calendarEvent.properties.end.displayName": "End Time", "schemas.calendarEvent.properties.id.description": "The unique identifier for the event", "schemas.calendarEvent.properties.id.displayName": "Event ID", "schemas.calendarEvent.properties.location.description": "The event location", "schemas.calendarEvent.properties.location.displayName": "Location", "schemas.calendarEvent.properties.reminders.description": "Event reminders", "schemas.calendarEvent.properties.reminders.displayName": "Reminders", "schemas.calendarEvent.properties.start.description": "The start time of the event", "schemas.calendarEvent.properties.start.displayName": "Start Time", "schemas.calendarEvent.properties.status.description": "The event status", "schemas.calendarEvent.properties.status.displayName": "Status", "schemas.calendarEvent.properties.summary.description": "The event title", "schemas.calendarEvent.properties.summary.displayName": "Summary", "settings.credential.description": "The credential to use for authentication", "settings.credential.displayName": "Credential", "triggers.event_created.description": "Triggers when a new calendar event is created", "triggers.event_created.displayName": "Event Created", "triggers.event_created.properties.event_ts.description": "The timestamp when the event occurred", "triggers.event_created.properties.event_ts.displayName": "Event Timestamp", "triggers.event_created.properties.event_type.description": "The type of event that occurred", "triggers.event_created.properties.event_type.displayName": "Event Type", "triggers.event_created.properties.payload.description": "Event payload data", "triggers.event_created.properties.payload.displayName": "Payload", "triggers.event_deleted.description": "Triggers when a calendar event is deleted", "triggers.event_deleted.displayName": "Event Deleted", "triggers.event_deleted.properties.event_ts.description": "The timestamp when the event occurred", "triggers.event_deleted.properties.event_ts.displayName": "Event Timestamp", "triggers.event_deleted.properties.event_type.description": "The type of event that occurred", "triggers.event_deleted.properties.event_type.displayName": "Event Type", "triggers.event_deleted.properties.payload.description": "Event payload data", "triggers.event_deleted.properties.payload.displayName": "Payload", "triggers.event_updated.description": "Triggers when a calendar event is updated", "triggers.event_updated.displayName": "Event Updated", "triggers.event_updated.properties.event_ts.description": "The timestamp when the event occurred", "triggers.event_updated.properties.event_ts.displayName": "Event Timestamp", "triggers.event_updated.properties.event_type.description": "The type of event that occurred", "triggers.event_updated.properties.event_type.displayName": "Event Type", "triggers.event_updated.properties.payload.description": "Event payload data", "triggers.event_updated.properties.payload.displayName": "Payload"}, "http": {"actions.delete.description": "Send a DELETE request", "actions.delete.displayName": "DELETE", "actions.delete.properties.followRedirects.description": "Whether to follow HTTP redirects (3xx responses)", "actions.delete.properties.followRedirects.displayName": "Follow Redirects", "actions.delete.properties.headers.description": "HTTP headers to include in the request", "actions.delete.properties.headers.displayName": "Headers", "actions.delete.properties.queryParams.description": "Parameters to add to the URL query string", "actions.delete.properties.queryParams.displayName": "Query Parameters", "actions.delete.properties.timeout.description": "Request timeout in milliseconds. 0 means no timeout.", "actions.delete.properties.timeout.displayName": "Timeout (ms)", "actions.delete.properties.url.description": "The URL to send the DELETE request to. Must start with http:// or https://", "actions.delete.properties.url.displayName": "URL", "actions.get.description": "Send a GET request", "actions.get.displayName": "GET", "actions.get.properties.cursorParamName.description": "Name of the query parameter for the cursor.", "actions.get.properties.cursorParamName.displayName": "Cursor Parameter Name", "actions.get.properties.cursorPath.description": "JSONPath to the next cursor value in the response body.", "actions.get.properties.cursorPath.displayName": "Cursor Path in Response", "actions.get.properties.dataPath.description": "JSONPath to the array of data in the response.", "actions.get.properties.dataPath.displayName": "Data Path in Response", "actions.get.properties.followRedirects.description": "Whether to follow HTTP redirects (3xx responses)", "actions.get.properties.followRedirects.displayName": "Follow Redirects", "actions.get.properties.hasMorePath.description": "JSONPath to a boolean indicating if there are more pages.", "actions.get.properties.hasMorePath.displayName": "'Has More' Path in Response", "actions.get.properties.headers.description": "HTTP headers to include in the request", "actions.get.properties.headers.displayName": "Headers", "actions.get.properties.maxPages.description": "Maximum number of pages to fetch. 0 for no limit.", "actions.get.properties.maxPages.displayName": "<PERSON> to Fetch", "actions.get.properties.pageParamName.description": "Name of the query parameter for page number or offset.", "actions.get.properties.pageParamName.displayName": "Page/Offset Parameter Name", "actions.get.properties.pageSize.description": "Number of items per page.", "actions.get.properties.pageSize.displayName": "<PERSON>", "actions.get.properties.pageSizeParamName.description": "Name of the query parameter for page size.", "actions.get.properties.pageSizeParamName.displayName": "Page Size Parameter Name", "actions.get.properties.pagination.description": "Enable automatic handling of paginated responses. Only for GET requests.", "actions.get.properties.pagination.displayName": "Enable Pagination", "actions.get.properties.paginationType.description": "The type of pagination to use.", "actions.get.properties.paginationType.displayName": "Pagination Type", "actions.get.properties.queryParams.description": "Parameters to add to the URL query string", "actions.get.properties.queryParams.displayName": "Query Parameters", "actions.get.properties.startOffset.description": "The offset to start from.", "actions.get.properties.startOffset.displayName": "Start Offset", "actions.get.properties.startPage.description": "The page number to start from.", "actions.get.properties.startPage.displayName": "Start Page", "actions.get.properties.timeout.description": "Request timeout in milliseconds. 0 means no timeout.", "actions.get.properties.timeout.displayName": "Timeout (ms)", "actions.get.properties.totalPath.description": "JSONPath to the total number of items.", "actions.get.properties.totalPath.displayName": "Total Items Path in Response", "actions.get.properties.url.description": "The URL to send the GET request to. Must start with http:// or https://", "actions.get.properties.url.displayName": "URL", "actions.options.description": "Send an OPTIONS request", "actions.options.displayName": "OPTIONS", "actions.options.properties.followRedirects.description": "Whether to follow HTTP redirects (3xx responses)", "actions.options.properties.followRedirects.displayName": "Follow Redirects", "actions.options.properties.headers.description": "HTTP headers to include in the request", "actions.options.properties.headers.displayName": "Headers", "actions.options.properties.queryParams.description": "Parameters to add to the URL query string", "actions.options.properties.queryParams.displayName": "Query Parameters", "actions.options.properties.timeout.description": "Request timeout in milliseconds. 0 means no timeout.", "actions.options.properties.timeout.displayName": "Timeout (ms)", "actions.options.properties.url.description": "The URL to send the OPTIONS request to. Must start with http:// or https://", "actions.options.properties.url.displayName": "URL", "actions.patch.description": "Send a PATCH request", "actions.patch.displayName": "PATCH", "actions.patch.properties.body.description": "Request body. Can be a JSON string, form data, or raw text.", "actions.patch.properties.body.displayName": "Body", "actions.patch.properties.followRedirects.description": "Whether to follow HTTP redirects (3xx responses)", "actions.patch.properties.followRedirects.displayName": "Follow Redirects", "actions.patch.properties.headers.description": "HTTP headers to include in the request", "actions.patch.properties.headers.displayName": "Headers", "actions.patch.properties.queryParams.description": "Parameters to add to the URL query string", "actions.patch.properties.queryParams.displayName": "Query Parameters", "actions.patch.properties.timeout.description": "Request timeout in milliseconds. 0 means no timeout.", "actions.patch.properties.timeout.displayName": "Timeout (ms)", "actions.patch.properties.url.description": "The URL to send the PATCH request to. Must start with http:// or https://", "actions.patch.properties.url.displayName": "URL", "actions.post.description": "Send a POST request", "actions.post.displayName": "POST", "actions.post.properties.body.description": "Request body. Can be a JSON string, form data, or raw text.", "actions.post.properties.body.displayName": "Body", "actions.post.properties.followRedirects.description": "Whether to follow HTTP redirects (3xx responses)", "actions.post.properties.followRedirects.displayName": "Follow Redirects", "actions.post.properties.headers.description": "HTTP headers to include in the request", "actions.post.properties.headers.displayName": "Headers", "actions.post.properties.queryParams.description": "Parameters to add to the URL query string", "actions.post.properties.queryParams.displayName": "Query Parameters", "actions.post.properties.timeout.description": "Request timeout in milliseconds. 0 means no timeout.", "actions.post.properties.timeout.displayName": "Timeout (ms)", "actions.post.properties.url.description": "The URL to send the POST request to. Must start with http:// or https://", "actions.post.properties.url.displayName": "URL", "actions.put.description": "Send a PUT request", "actions.put.displayName": "PUT", "actions.put.properties.body.description": "Request body. Can be a JSON string, form data, or raw text.", "actions.put.properties.body.displayName": "Body", "actions.put.properties.followRedirects.description": "Whether to follow HTTP redirects (3xx responses)", "actions.put.properties.followRedirects.displayName": "Follow Redirects", "actions.put.properties.headers.description": "HTTP headers to include in the request", "actions.put.properties.headers.displayName": "Headers", "actions.put.properties.queryParams.description": "Parameters to add to the URL query string", "actions.put.properties.queryParams.displayName": "Query Parameters", "actions.put.properties.timeout.description": "Request timeout in milliseconds. 0 means no timeout.", "actions.put.properties.timeout.displayName": "Timeout (ms)", "actions.put.properties.url.description": "The URL to send the PUT request to. Must start with http:// or https://", "actions.put.properties.url.displayName": "URL", "credentials.http.api_key.description": "", "credentials.http.api_key.displayName": "", "api_key": "", "credentials.http.aws_sig_v4.description": "", "credentials.http.aws_sig_v4.displayName": "", "aws_sig_v4": "", "credentials.http.basic_auth.description": "", "credentials.http.basic_auth.displayName": "", "basic_auth": "", "credentials.http.bearer_token.description": "", "credentials.http.bearer_token.displayName": "", "bearer_token": "", "credentials.http.digest_auth.description": "", "credentials.http.digest_auth.displayName": "", "digest_auth": "", "credentials.http.ntlm_auth.description": "", "credentials.http.ntlm_auth.displayName": "", "ntlm_auth": "", "credentials.http.oauth2.description": "", "credentials.http.oauth2.displayName": "", "oauth2": "", "credentials.http.wsse_auth.description": "", "credentials.http.wsse_auth.displayName": "", "wsse_auth": "", "description": "Make HTTP requests to external APIs and services", "displayName": "HTTP", "schemas.httpResponse.properties.allData.description": "For paginated requests, a combined list of data items from all pages.", "schemas.httpResponse.properties.allData.displayName": "All Data", "schemas.httpResponse.properties.body.description": "The body of the response, parsed if JSON or XML", "schemas.httpResponse.properties.body.displayName": "Response Body", "schemas.httpResponse.properties.duration.description": "The time taken for the request in milliseconds", "schemas.httpResponse.properties.duration.displayName": "Duration", "schemas.httpResponse.properties.headers.description": "The HTTP headers from the response", "schemas.httpResponse.properties.headers.displayName": "Response Headers", "schemas.httpResponse.properties.pages.description": "For paginated requests, a list of responses from each page.", "schemas.httpResponse.properties.pages.displayName": "Pages", "schemas.httpResponse.properties.rawBody.description": "The unparsed body of the response as a string", "schemas.httpResponse.properties.rawBody.displayName": "Raw Response Body", "schemas.httpResponse.properties.statusCode.description": "The HTTP status code of the response", "schemas.httpResponse.properties.statusCode.displayName": "Status Code", "schemas.httpResponse.properties.url.description": "The final URL that was requested (after any redirects)", "schemas.httpResponse.properties.url.displayName": "Request URL", "settings.authentication.description": "Authentication settings for the request", "settings.authentication.displayName": "Authentication", "settings.authentication.properties.type.description": "The type of authentication to use", "settings.authentication.properties.type.displayName": "Authentication Type", "settings.credential.description": "Credential to use", "settings.credential.displayName": "Credential", "settings.retryConfig.description": "Configure retry behavior for failed requests", "settings.retryConfig.displayName": "Retry Configuration", "settings.retryConfig.properties.initialInterval.description": "Initial interval in milliseconds between retries.", "settings.retryConfig.properties.initialInterval.displayName": "Initial Interval (ms)", "settings.retryConfig.properties.maxElapsedTime.description": "Maximum total time in milliseconds for all retries.", "settings.retryConfig.properties.maxElapsedTime.displayName": "Max Elapsed Time (ms)", "settings.retryConfig.properties.maxInterval.description": "Maximum interval in milliseconds between retries.", "settings.retryConfig.properties.maxInterval.displayName": "<PERSON> (ms)", "settings.retryConfig.properties.maxRetries.description": "Maximum number of retries for a failed request. Set to 0 to disable retries.", "settings.retryConfig.properties.maxRetries.displayName": "Max Retries", "settings.retryConfig.properties.multiplier.description": "Multiplier for the backoff interval.", "settings.retryConfig.properties.multiplier.displayName": "Multiplier"}, "line": {"actions.line.get_profile.description": "", "actions.line.get_profile.displayName": "", "get_profile": "", "actions.line.get_profile.properties.user_id.description": "", "actions.line.get_profile.properties.user_id.displayName": "", "actions.line.get_quota.description": "", "actions.line.get_quota.displayName": "", "get_quota": "", "actions.line.send_message.data.properties.message.description": "", "actions.line.send_message.data.properties.response.description": "", "actions.line.send_message.data.properties.success.description": "", "actions.line.send_message.description": "", "actions.line.send_message.displayName": "", "send_message": "", "actions.line.send_message.properties.message.description": "", "actions.line.send_message.properties.message.displayName": "", "actions.line.send_message.properties.to.description": "", "actions.line.send_message.properties.to.displayName": "", "credentials.line.access_token.description": "", "credentials.line.access_token.displayName": "", "access_token": "", "description": "LINE Messaging API for sending messages and managing LINE Official Account", "displayName": "LINE", "schemas.Consumption.properties.totalUsage.description": "Total usage count", "schemas.Message.properties.address.description": "Address for location messages", "schemas.Message.properties.altText.description": "Alternative text for template and flex messages", "schemas.Message.properties.contents.description": "Contents object for flex messages", "schemas.Message.properties.latitude.description": "Latitude for location messages", "schemas.Message.properties.longitude.description": "Longitude for location messages", "schemas.Message.properties.originalContentUrl.description": "URL of the media content for image/video/audio messages", "schemas.Message.properties.packageId.description": "Package ID for sticker messages", "schemas.Message.properties.previewImageUrl.description": "URL of the preview image for media messages", "schemas.Message.properties.stickerId.description": "Sticker ID for sticker messages", "schemas.Message.properties.template.description": "Template object for template messages", "schemas.Message.properties.text.description": "Text content for text messages", "schemas.Message.properties.title.description": "Title for location messages", "schemas.Message.properties.type.description": "Type of message", "schemas.Profile.properties.displayName.description": "User's display name", "schemas.Profile.properties.language.description": "User's language", "schemas.Profile.properties.pictureUrl.description": "URL of user's profile picture", "schemas.Profile.properties.statusMessage.description": "User's status message", "schemas.Profile.properties.userId.description": "User ID", "schemas.Quota.properties.type.description": "Type of quota", "schemas.Quota.properties.value.description": "Quota value", "settings.credential.description": "LINE channel access token credential", "settings.credential.displayName": "Credential"}, "loop": {"actions.loopItems.description": "Loop through a collection of items", "actions.loopItems.displayName": "Loop Items", "actions.loopItems.properties.maxIterations.description": "Maximum number of iterations", "actions.loopItems.properties.maxIterations.displayName": "Maximum Iterations", "actions.loopItems.properties.trimSpaces.description": "Whether to trim spaces from the value", "actions.loopItems.properties.trimSpaces.displayName": "Trim Spaces", "actions.loopItems.properties.value.description": "Reference to a collection to loop through", "actions.loopItems.properties.value.displayName": "Value", "actions.loopItems.properties.value.items.properties.id.description": "The id of the item", "actions.loopItems.properties.value.items.properties.id.displayName": "ID", "actions.loopItems.properties.value.items.properties.value.description": "The value of the item", "actions.loopItems.properties.value.items.properties.value.displayName": "Value", "actions.loopNumbers.description": "Loop through a range of numbers", "actions.loopNumbers.displayName": "Loop from Numbers", "actions.loopNumbers.properties.end.description": "Ending number", "actions.loopNumbers.properties.end.displayName": "End", "actions.loopNumbers.properties.maxIterations.description": "Maximum number of iterations", "actions.loopNumbers.properties.maxIterations.displayName": "Maximum Iterations", "actions.loopNumbers.properties.start.description": "Starting number", "actions.loopNumbers.properties.start.displayName": "Start", "actions.loopNumbers.properties.step.description": "Step size", "actions.loopNumbers.properties.step.displayName": "Step", "actions.loopText.description": "Loop through text character by character", "actions.loopText.displayName": "Loop Text", "actions.loopText.properties.delimiter.description": "The delimiter to use to split the text", "actions.loopText.properties.delimiter.displayName": "Delimiter", "actions.loopText.properties.maxIterations.description": "Maximum number of iterations", "actions.loopText.properties.maxIterations.displayName": "Maximum Iterations", "actions.loopText.properties.trimSpaces.description": "Whether to trim spaces from the text", "actions.loopText.properties.trimSpaces.displayName": "Trim Spaces", "actions.loopText.properties.value.description": "The text to loop through", "actions.loopText.properties.value.displayName": "Value", "description": "Loop node for iterating over items, numbers, or text", "displayName": "Loop", "schemas.loopItemsOutput.properties.currentIndex.description": "The index of the current item", "schemas.loopItemsOutput.properties.currentIndex.displayName": "Current Index", "schemas.loopItemsOutput.properties.currentItem.description": "The current item in the iteration", "schemas.loopItemsOutput.properties.currentItem.displayName": "Current Item", "schemas.loopItemsOutput.properties.isFirst.description": "Whether this is the first iteration (always true for first item)", "schemas.loopItemsOutput.properties.isFirst.displayName": "Is First", "schemas.loopItemsOutput.properties.isLast.description": "Whether this is the last iteration for the current item", "schemas.loopItemsOutput.properties.isLast.displayName": "Is Last", "schemas.loopItemsOutput.properties.iterationCount.description": "The number of iterations completed", "schemas.loopItemsOutput.properties.iterationCount.displayName": "Iteration Count", "schemas.loopNumbersOutput.properties.currentNumber.description": "The current number in the iteration", "schemas.loopNumbersOutput.properties.currentNumber.displayName": "Current Number", "schemas.loopNumbersOutput.properties.isFirst.description": "Whether this is the first iteration (always true for first number)", "schemas.loopNumbersOutput.properties.isFirst.displayName": "Is First", "schemas.loopNumbersOutput.properties.isLast.description": "Whether this is the last iteration for the first number", "schemas.loopNumbersOutput.properties.isLast.displayName": "Is Last", "schemas.loopNumbersOutput.properties.iterationCount.description": "The number of iterations completed", "schemas.loopNumbersOutput.properties.iterationCount.displayName": "Iteration Count", "schemas.loopNumbersOutput.properties.maxReached.description": "Whether the maximum iteration count was reached", "schemas.loopNumbersOutput.properties.maxReached.displayName": "Maximum Reached", "schemas.loopTextOutput.properties.currentIndex.description": "The index of the current item", "schemas.loopTextOutput.properties.currentIndex.displayName": "Current Index", "schemas.loopTextOutput.properties.currentItem.description": "The current item in the iteration", "schemas.loopTextOutput.properties.currentItem.displayName": "Current Item", "schemas.loopTextOutput.properties.isFirst.description": "Whether this is the first iteration (always true for first item)", "schemas.loopTextOutput.properties.isFirst.displayName": "Is First", "schemas.loopTextOutput.properties.isLast.description": "Whether this is the last iteration for the current item", "schemas.loopTextOutput.properties.isLast.displayName": "Is Last", "schemas.loopTextOutput.properties.iterationCount.description": "The number of iterations completed", "schemas.loopTextOutput.properties.iterationCount.displayName": "Iteration Count", "settings.nextChildID.description": "The ID of the next child node in the loop", "settings.nextChildID.displayName": "Next Child ID"}, "mail": {"actions.sendMail.description": "Send mail message", "actions.sendMail.displayName": "Send Mail", "actions.sendMail.properties.bcc.description": "The BCC email address of the email", "actions.sendMail.properties.bcc.displayName": "BCC", "actions.sendMail.properties.cc.description": "The CC email address of the email", "actions.sendMail.properties.cc.displayName": "CC", "actions.sendMail.properties.subject.description": "The subject of the email", "actions.sendMail.properties.subject.displayName": "Subject", "actions.sendMail.properties.text.description": "The body of the email", "actions.sendMail.properties.text.displayName": "Text", "actions.sendMail.properties.to.description": "The email address of the recipient", "actions.sendMail.properties.to.displayName": "To", "credentials.mail.ocs.description": "", "credentials.mail.ocs.displayName": "", "ocs": "", "credentials.mail.ocs.properties.from.description": "", "credentials.mail.ocs.properties.from.displayName": "", "credentials.mail.ocs.properties.ocsChannelId.description": "", "credentials.mail.ocs.properties.ocsChannelId.displayName": "", "credentials.mail.smtp.description": "", "credentials.mail.smtp.displayName": "", "smtp": "", "credentials.mail.smtp.properties.smtpEncryption.description": "", "credentials.mail.smtp.properties.smtpEncryption.displayName": "", "credentials.mail.smtp.properties.smtpHost.description": "", "credentials.mail.smtp.properties.smtpHost.displayName": "", "credentials.mail.smtp.properties.smtpPassword.description": "", "credentials.mail.smtp.properties.smtpPassword.displayName": "", "credentials.mail.smtp.properties.smtpPort.description": "", "credentials.mail.smtp.properties.smtpPort.displayName": "", "credentials.mail.smtp.properties.smtpUsername.description": "", "credentials.mail.smtp.properties.smtpUsername.displayName": "", "description": "Send mail message", "displayName": "Mail", "schemas.sendMailOutput.properties.bcc.description": "The BCC email address of the email", "schemas.sendMailOutput.properties.bcc.displayName": "BCC", "schemas.sendMailOutput.properties.cc.description": "The CC email address of the email", "schemas.sendMailOutput.properties.cc.displayName": "CC", "schemas.sendMailOutput.properties.subject.description": "The subject of the email", "schemas.sendMailOutput.properties.subject.displayName": "Subject", "schemas.sendMailOutput.properties.text.description": "The body of the email", "schemas.sendMailOutput.properties.text.displayName": "Text", "schemas.sendMailOutput.properties.to.description": "The email address of the recipient", "schemas.sendMailOutput.properties.to.displayName": "To", "settings.credential.description": "The credential to use for authentication", "settings.credential.displayName": "Credential", "settings.nextChildID.description": "The ID of the next child node", "settings.nextChildID.displayName": "Next Child ID"}, "manual": {"description": "Description for Manual node", "displayName": "Manual", "settings.data.description": "Data to be passed to the flow", "settings.data.displayName": "Data", "triggers.runFlow.description": "Run a flow", "triggers.runFlow.displayName": "Run Flow"}, "map": {"description": "Transform each item in a data collection", "displayName": "Map", "schemas.errorBehavior.description": "How to handle transformation errors", "schemas.errorBehavior.displayName": "Error <PERSON>", "schemas.input.default.data.description": "The array of items to transform", "schemas.input.default.data.displayName": "Data", "schemas.input.description": "The input data to transform", "schemas.input.displayName": "Input Data", "schemas.output.description": "The output data", "schemas.output.displayName": "Output Data", "schemas.transform.description": "JavaScript-like expression to transform each item", "schemas.transform.displayName": "Transform Expression", "settings.errorBehavior.description": "How to handle transformation errors", "settings.errorBehavior.displayName": "Error <PERSON>", "settings.input.description": "The array of items to filter", "settings.input.displayName": "Input Data", "settings.transform.description": "JavaScript-like expression to transform each item", "settings.transform.displayName": "Transform Expression"}, "openai": {"actions.classify_text_for_violations.description": "Identify and flag content that might be harmful using OpenAI's moderation API", "actions.classify_text_for_violations.displayName": "Classify Text for Violations", "actions.classify_text_for_violations.properties.options.description": "Additional options for text moderation", "actions.classify_text_for_violations.properties.options.displayName": "Options", "actions.classify_text_for_violations.properties.options.properties.use_stable_model.description": "Use the stable version of the model instead of the latest version (accuracy may be slightly lower)", "actions.classify_text_for_violations.properties.options.properties.use_stable_model.displayName": "Use Stable Model", "actions.classify_text_for_violations.properties.text_input.description": "Enter text to classify if it violates the moderation policy", "actions.classify_text_for_violations.properties.text_input.displayName": "Text Input", "actions.create_response.description": "Create a new response using OpenAI's Responses API for stateful conversations", "actions.create_response.displayName": "Create Response", "actions.create_response.properties.background.description": "Whether to process the response in the background. When true, the API returns immediately with a response ID that can be used to retrieve the result later.", "actions.create_response.properties.background.displayName": "Background Processing", "actions.create_response.properties.input.description": "Input text or messages for the response", "actions.create_response.properties.input.displayName": "Input", "actions.create_response.properties.instructions.description": "Instructions for the response", "actions.create_response.properties.instructions.displayName": "Instructions", "actions.create_response.properties.max_output_tokens.description": "Maximum tokens to generate", "actions.create_response.properties.max_output_tokens.displayName": "<PERSON> Output Tokens", "actions.create_response.properties.max_tool_calls.description": "Maximum number of tool calls", "actions.create_response.properties.max_tool_calls.displayName": "<PERSON>l Calls", "actions.create_response.properties.metadata.description": "Metadata for the response", "actions.create_response.properties.metadata.displayName": "<PERSON><PERSON><PERSON>", "actions.create_response.properties.model.description": "The model to use for the response", "actions.create_response.properties.model.displayName": "Model", "actions.create_response.properties.previous_response_id.description": "ID of the previous response to use as context", "actions.create_response.properties.previous_response_id.displayName": "Previous Response ID", "actions.create_response.properties.reasoning_effort.description": "Reasoning effort to use", "actions.create_response.properties.reasoning_effort.displayName": "Reasoning Effort", "actions.create_response.properties.store.description": "Whether to store the response in the database", "actions.create_response.properties.store.displayName": "Store Response", "actions.create_response.properties.stream.description": "Enable streaming responses", "actions.create_response.properties.stream.displayName": "Stream Response", "actions.create_response.properties.temperature.description": "Controls randomness (0.0 to 2.0)", "actions.create_response.properties.temperature.displayName": "Temperature", "actions.create_response.properties.tools.description": "Built-in tools to use (web_search)", "actions.create_response.properties.tools.displayName": "Tools", "actions.create_response.properties.tools.items.properties.type.description": "Type of tool to use", "actions.create_response.properties.tools.items.properties.type.displayName": "Tool Type", "actions.generate_image.description": "Generate an image from a text prompt using OpenAI's image models (DALL-E)", "actions.generate_image.displayName": "Generate Image", "actions.generate_image.properties.model.description": "The model to use for image generation", "actions.generate_image.properties.model.displayName": "Model", "actions.generate_image.properties.num_images.description": "Number of images to generate", "actions.generate_image.properties.num_images.displayName": "Number of Images", "actions.generate_image.properties.output_field.description": "Name of the output field for binary data (if not using URLs)", "actions.generate_image.properties.output_field.displayName": "Put Output in Field", "actions.generate_image.properties.prompt.description": "Text description of the desired image(s)", "actions.generate_image.properties.prompt.displayName": "Prompt", "actions.generate_image.properties.quality.description": "Image quality (HD only for DALL-E 3)", "actions.generate_image.properties.quality.displayName": "Quality", "actions.generate_image.properties.resolution.description": "Resolution of the generated image", "actions.generate_image.properties.resolution.displayName": "Resolution", "actions.generate_image.properties.response_format.description": "Whether to return image URLs or binary data", "actions.generate_image.properties.response_format.displayName": "Respond with image URL(s)", "actions.generate_image.properties.style.description": "Style of the generated image (DALL-E 3 only)", "actions.generate_image.properties.style.displayName": "Style", "actions.retrieve_response.description": "Retrieve the status and results of a response from OpenAI's Responses API", "actions.retrieve_response.displayName": "Retrieve Response", "actions.retrieve_response.properties.response_id.description": "ID of the response to retrieve", "actions.retrieve_response.properties.response_id.displayName": "Response ID", "actions.text_chat.description": "Chat with AI", "actions.text_chat.displayName": "Text Message", "actions.text_chat.properties.frequency_penalty.description": "The frequency penalty to use", "actions.text_chat.properties.frequency_penalty.displayName": "Frequency Penalty", "actions.text_chat.properties.max_tokens.description": "The maximum number of tokens to generate", "actions.text_chat.properties.max_tokens.displayName": "<PERSON>", "actions.text_chat.properties.messages.description": "The messages to send to the model", "actions.text_chat.properties.messages.displayName": "Messages", "actions.text_chat.properties.messages.items.properties.content.description": "The content of the message", "actions.text_chat.properties.messages.items.properties.content.displayName": "Content", "actions.text_chat.properties.messages.items.properties.role.description": "The role of the message", "actions.text_chat.properties.messages.items.properties.role.displayName": "Role", "actions.text_chat.properties.model.description": "The model to use", "actions.text_chat.properties.model.displayName": "Model", "actions.text_chat.properties.presence_penalty.description": "The presence penalty to use", "actions.text_chat.properties.presence_penalty.displayName": "Presence Penalty", "actions.text_chat.properties.reasoning_effort.description": "The reasoning effort to use", "actions.text_chat.properties.reasoning_effort.displayName": "Reasoning Effort", "actions.text_chat.properties.stop.description": "The stop sequence to use", "actions.text_chat.properties.stop.displayName": "Stop", "actions.text_chat.properties.temperature.description": "The temperature to use", "actions.text_chat.properties.temperature.displayName": "Temperature", "actions.text_chat.properties.top_p.description": "The top P to use", "actions.text_chat.properties.top_p.displayName": "Top P", "actions.text_chat.properties.verbosity.description": "The verbosity to use", "actions.text_chat.properties.verbosity.displayName": "Verbosity", "credentials.openai.api_key.description": "", "credentials.openai.api_key.displayName": "", "api_key": "", "description": "OpenAI API", "displayName": "OpenAI", "schemas.ChatCompletion.properties.choices.description": "A list of chat completion choices", "schemas.ChatCompletion.properties.choices.items.properties.finish_reason.description": "The reason the model stopped generating tokens", "schemas.ChatCompletion.properties.choices.items.properties.index.description": "The index of the choice in the list", "schemas.ChatCompletion.properties.choices.items.properties.message.description": "The message generated by the model", "schemas.ChatCompletion.properties.choices.items.properties.message.properties.content.description": "The contents of the message", "schemas.ChatCompletion.properties.choices.items.properties.message.properties.role.description": "The role of the message author", "schemas.ChatCompletion.properties.created.description": "The Unix timestamp (in seconds) of when the chat completion was created", "schemas.ChatCompletion.properties.id.description": "A unique identifier for the chat completion", "schemas.ChatCompletion.properties.model.description": "The model used for the chat completion", "schemas.ChatCompletion.properties.object.description": "The object type, which is always 'chat.completion'", "schemas.ChatCompletion.properties.usage.description": "Usage statistics for the completion request", "schemas.ChatCompletion.properties.usage.properties.completion_tokens.description": "Number of tokens in the generated completion", "schemas.ChatCompletion.properties.usage.properties.prompt_tokens.description": "Number of tokens in the prompt", "schemas.ChatCompletion.properties.usage.properties.total_tokens.description": "Total number of tokens used in the request", "schemas.Response.properties.conversation_id.description": "ID of the conversation this response belongs to", "schemas.Response.properties.created_at.description": "The Unix timestamp (in seconds) of when the response was created", "schemas.Response.properties.error.description": "Error information if the response failed", "schemas.Response.properties.error.properties.message.description": "Error message", "schemas.Response.properties.error.properties.type.description": "Error type", "schemas.Response.properties.id.description": "A unique identifier for the response", "schemas.Response.properties.metadata.description": "Additional metadata for the response", "schemas.Response.properties.model.description": "The model used for the response", "schemas.Response.properties.object.description": "The object type, which is always 'response'", "schemas.Response.properties.output.description": "Array of output messages or content", "schemas.Response.properties.output.items.properties.content.description": "The content of the message", "schemas.Response.properties.output.items.properties.role.description": "The role of the message author", "schemas.Response.properties.status.description": "The current status of the response", "schemas.Response.properties.usage.description": "Usage statistics for the response", "schemas.Response.properties.usage.properties.completion_tokens.description": "Number of tokens in the generated completion", "schemas.Response.properties.usage.properties.prompt_tokens.description": "Number of tokens in the prompt", "schemas.Response.properties.usage.properties.total_tokens.description": "Total number of tokens used", "settings.credential.description": "Credential to use", "settings.credential.displayName": "Credential", "settings.openai_base_url.description": "The base URL for the OpenAI API", "settings.openai_base_url.displayName": "OpenAI Base URL", "settings.simplify_response.description": "Simplify the response from the model", "settings.simplify_response.displayName": "Simplify Response"}, "path": {"description": "Routes workflow execution based on conditions, controlling the flow path", "displayName": "Path", "schemas.condition.properties.and.description": "The conditions to evaluate for and mode", "schemas.condition.properties.and.displayName": "AND", "schemas.condition_item.properties.field.description": "The field from input data to evaluate", "schemas.condition_item.properties.field.displayName": "Field", "schemas.condition_item.properties.operator.description": "The comparison operator", "schemas.condition_item.properties.operator.displayName": "Operator", "schemas.condition_item.properties.value.description": "The value to compare against (not required for empty/exist checks)", "schemas.condition_item.properties.value.displayName": "Value", "schemas.path.properties.conditions.description": "The conditions to evaluate for condition mode", "schemas.path.properties.conditions.displayName": "Conditions", "schemas.path.properties.mode.description": "How this path should be evaluated", "schemas.path.properties.mode.displayName": "Path Mode", "settings.paths.description": "Define multiple execution paths with conditions", "settings.paths.displayName": "Execution Paths"}, "salesforce": {"actions.addContactToCampaign.data.campaignMember.description": "The created campaign member record", "actions.addContactToCampaign.data.campaignMember.displayName": "Campaign Member", "actions.addContactToCampaign.description": "Add a contact to a campaign", "actions.addContactToCampaign.displayName": "Add Contact to Campaign", "actions.addContactToCampaign.properties.campaignId.description": "The ID of the campaign to add the contact to", "actions.addContactToCampaign.properties.campaignId.displayName": "Campaign ID", "actions.addContactToCampaign.properties.contactId.description": "The ID of the contact to add to the campaign", "actions.addContactToCampaign.properties.contactId.displayName": "Contact ID", "actions.addLeadToCampaign.data.campaignMember.description": "The created campaign member record", "actions.addLeadToCampaign.data.campaignMember.displayName": "Campaign Member", "actions.addLeadToCampaign.description": "Add a lead to a campaign", "actions.addLeadToCampaign.displayName": "Add Lead to Campaign", "actions.addLeadToCampaign.properties.campaignId.description": "The ID of the campaign to add the lead to", "actions.addLeadToCampaign.properties.campaignId.displayName": "Campaign ID", "actions.addLeadToCampaign.properties.leadId.description": "The ID of the lead to add to the campaign", "actions.addLeadToCampaign.properties.leadId.displayName": "Lead ID", "actions.addNoteToAccount.data.note.description": "The created note", "actions.addNoteToAccount.data.note.displayName": "Created Note", "actions.addNoteToAccount.description": "Add a note to an account", "actions.addNoteToAccount.displayName": "Add Note to Account", "actions.addNoteToAccount.properties.accountId.description": "The ID of the account to add the note to", "actions.addNoteToAccount.properties.accountId.displayName": "Account ID", "actions.addNoteToAccount.properties.isPrivate.description": "Whether the note is private", "actions.addNoteToAccount.properties.isPrivate.displayName": "Is Private", "actions.addNoteToAccount.properties.noteContent.description": "The content of the note to add", "actions.addNoteToAccount.properties.noteContent.displayName": "Note Content", "actions.addNoteToAccount.properties.noteTitle.description": "The title of the note", "actions.addNoteToAccount.properties.noteTitle.displayName": "Note Title", "actions.addNoteToContact.data.note.description": "The created note", "actions.addNoteToContact.data.note.displayName": "Created Note", "actions.addNoteToContact.description": "Add a note to a contact", "actions.addNoteToContact.displayName": "Add Note to Contact", "actions.addNoteToContact.properties.contactId.description": "The ID of the contact to add the note to", "actions.addNoteToContact.properties.contactId.displayName": "Contact ID", "actions.addNoteToContact.properties.isPrivate.description": "Whether the note is private", "actions.addNoteToContact.properties.isPrivate.displayName": "Is Private", "actions.addNoteToContact.properties.noteContent.description": "The content of the note to add", "actions.addNoteToContact.properties.noteContent.displayName": "Note Content", "actions.addNoteToContact.properties.noteTitle.description": "The title of the note", "actions.addNoteToContact.properties.noteTitle.displayName": "Note Title", "actions.convertLead.data.conversion.description": "The result of the lead conversion", "actions.convertLead.data.conversion.displayName": "Conversion Result", "actions.convertLead.data.conversion.properties.accountId.description": "The ID of the created or associated account", "actions.convertLead.data.conversion.properties.accountId.displayName": "Account ID", "actions.convertLead.data.conversion.properties.contactId.description": "The ID of the created or associated contact", "actions.convertLead.data.conversion.properties.contactId.displayName": "Contact ID", "actions.convertLead.data.conversion.properties.opportunityId.description": "The ID of the created or associated opportunity", "actions.convertLead.data.conversion.properties.opportunityId.displayName": "Opportunity ID", "actions.convertLead.description": "Convert a lead to account, contact, and optionally opportunity", "actions.convertLead.displayName": "Convert Lead", "actions.convertLead.properties.accountId.description": "The ID of the existing account to associate with", "actions.convertLead.properties.accountId.displayName": "Account ID", "actions.convertLead.properties.convertedStatus.description": "The status to set when converting the lead", "actions.convertLead.properties.convertedStatus.displayName": "Converted Status", "actions.convertLead.properties.createAccount.description": "Whether to create a new account", "actions.convertLead.properties.createAccount.displayName": "Create Account", "actions.convertLead.properties.createOpportunity.description": "Whether to create a new opportunity", "actions.convertLead.properties.createOpportunity.displayName": "Create Opportunity", "actions.convertLead.properties.leadId.description": "The ID of the lead to convert", "actions.convertLead.properties.leadId.displayName": "Lead ID", "actions.convertLead.properties.opportunityId.description": "The ID of the existing opportunity to associate with", "actions.convertLead.properties.opportunityId.displayName": "Opportunity ID", "actions.convertLead.properties.opportunityName.description": "The name for the new opportunity", "actions.convertLead.properties.opportunityName.displayName": "Opportunity Name", "actions.createAccount.data.account.description": "The created account", "actions.createAccount.data.account.displayName": "Created Account", "actions.createAccount.description": "Create a new account", "actions.createAccount.displayName": "Create Account", "actions.createAccount.properties.accountNumber.description": "The account number", "actions.createAccount.properties.accountNumber.displayName": "Account Number", "actions.createAccount.properties.annualRevenue.description": "The annual revenue of the account", "actions.createAccount.properties.annualRevenue.displayName": "Annual Revenue", "actions.createAccount.properties.billingCity.description": "The billing city", "actions.createAccount.properties.billingCity.displayName": "Billing City", "actions.createAccount.properties.billingCountry.description": "The billing country", "actions.createAccount.properties.billingCountry.displayName": "Billing Country", "actions.createAccount.properties.billingState.description": "The billing state or province", "actions.createAccount.properties.billingState.displayName": "Billing State/Province", "actions.createAccount.properties.billingStreet.description": "The billing street address", "actions.createAccount.properties.billingStreet.displayName": "Billing Street", "actions.createAccount.properties.billingZipPostalCode.description": "The billing zip/postal code", "actions.createAccount.properties.billingZipPostalCode.displayName": "Billing Zip/Postal Code", "actions.createAccount.properties.description.description": "The description of the account", "actions.createAccount.properties.description.displayName": "Description", "actions.createAccount.properties.fax.description": "The fax number of the account", "actions.createAccount.properties.fax.displayName": "Fax", "actions.createAccount.properties.industry.description": "The industry of the account", "actions.createAccount.properties.industry.displayName": "Industry", "actions.createAccount.properties.name.description": "The name of the account", "actions.createAccount.properties.name.displayName": "Account Name", "actions.createAccount.properties.numberOfEmployees.description": "The number of employees", "actions.createAccount.properties.numberOfEmployees.displayName": "Employees", "actions.createAccount.properties.ownership.description": "The ownership of the account", "actions.createAccount.properties.ownership.displayName": "Ownership", "actions.createAccount.properties.phone.description": "The phone number of the account", "actions.createAccount.properties.phone.displayName": "Phone", "actions.createAccount.properties.rating.description": "The rating of the account", "actions.createAccount.properties.rating.displayName": "Rating", "actions.createAccount.properties.shippingCity.description": "The shipping city", "actions.createAccount.properties.shippingCity.displayName": "Shipping City", "actions.createAccount.properties.shippingCountryCode.description": "The shipping country", "actions.createAccount.properties.shippingCountryCode.displayName": "Shipping Country", "actions.createAccount.properties.shippingState.description": "The shipping state or province", "actions.createAccount.properties.shippingState.displayName": "Shipping State/Province", "actions.createAccount.properties.shippingStreet.description": "The shipping street address", "actions.createAccount.properties.shippingStreet.displayName": "Shipping Street", "actions.createAccount.properties.shippingZipPostalCode.description": "The shipping zip/postal code", "actions.createAccount.properties.shippingZipPostalCode.displayName": "Shipping Zip/Postal Code", "actions.createAccount.properties.sic.description": "The SIC code of the account", "actions.createAccount.properties.sic.displayName": "SIC Code", "actions.createAccount.properties.site.description": "The account site", "actions.createAccount.properties.site.displayName": "Account Site", "actions.createAccount.properties.tickerSymbol.description": "The ticker symbol of the account", "actions.createAccount.properties.tickerSymbol.displayName": "Ticker Symbol", "actions.createAccount.properties.type.description": "The type of account", "actions.createAccount.properties.type.displayName": "Type", "actions.createAccount.properties.website.description": "The website of the account", "actions.createAccount.properties.website.displayName": "Website", "actions.createContact.data.contact.description": "The created contact information", "actions.createContact.data.contact.displayName": "Contact", "actions.createContact.description": "Creates a new Salesforce contact", "actions.createContact.displayName": "Create Contact", "actions.createContact.properties.accountId.description": "The ID of the account to associate with the contact", "actions.createContact.properties.accountId.displayName": "Account ID", "actions.createContact.properties.department.description": "The department of the contact", "actions.createContact.properties.department.displayName": "Department", "actions.createContact.properties.email.description": "The email of the contact", "actions.createContact.properties.email.displayName": "Email", "actions.createContact.properties.firstName.description": "The first name of the contact", "actions.createContact.properties.firstName.displayName": "First Name", "actions.createContact.properties.lastName.description": "The last name of the contact", "actions.createContact.properties.lastName.displayName": "Last Name", "actions.createContact.properties.mailingCity.description": "The city for mailing", "actions.createContact.properties.mailingCity.displayName": "Mailing City", "actions.createContact.properties.mailingCountry.description": "The country for mailing", "actions.createContact.properties.mailingCountry.displayName": "Mailing Country", "actions.createContact.properties.mailingPostalCode.description": "The postal code for mailing", "actions.createContact.properties.mailingPostalCode.displayName": "Mailing Postal Code", "actions.createContact.properties.mailingState.description": "The state/province for mailing", "actions.createContact.properties.mailingState.displayName": "Mailing State", "actions.createContact.properties.mailingStreet.description": "The street address for mailing", "actions.createContact.properties.mailingStreet.displayName": "Mailing Street", "actions.createContact.properties.phone.description": "The phone number of the contact", "actions.createContact.properties.phone.displayName": "Phone", "actions.createContact.properties.title.description": "The job title of the contact", "actions.createContact.properties.title.displayName": "Title", "actions.createLead.data.lead.description": "The created lead", "actions.createLead.data.lead.displayName": "Created Lead", "actions.createLead.description": "Create a new lead in Salesforce", "actions.createLead.displayName": "Create Lead", "actions.createLead.properties.city.description": "The city", "actions.createLead.properties.city.displayName": "City", "actions.createLead.properties.company.description": "The company name", "actions.createLead.properties.company.displayName": "Company", "actions.createLead.properties.country.description": "The country", "actions.createLead.properties.country.displayName": "Country", "actions.createLead.properties.description.description": "Additional description of the lead", "actions.createLead.properties.description.displayName": "Description", "actions.createLead.properties.email.description": "The email address of the lead", "actions.createLead.properties.email.displayName": "Email", "actions.createLead.properties.firstName.description": "The first name of the lead", "actions.createLead.properties.firstName.displayName": "First Name", "actions.createLead.properties.industry.description": "The industry of the lead", "actions.createLead.properties.industry.displayName": "Industry", "actions.createLead.properties.lastName.description": "The last name of the lead", "actions.createLead.properties.lastName.displayName": "Last Name", "actions.createLead.properties.leadSource.description": "The source of the lead", "actions.createLead.properties.leadSource.displayName": "Lead Source", "actions.createLead.properties.phone.description": "The phone number of the lead", "actions.createLead.properties.phone.displayName": "Phone", "actions.createLead.properties.postalCode.description": "The postal code", "actions.createLead.properties.postalCode.displayName": "Postal Code", "actions.createLead.properties.rating.description": "The rating of the lead", "actions.createLead.properties.rating.displayName": "Rating", "actions.createLead.properties.state.description": "The state or province", "actions.createLead.properties.state.displayName": "State/Province", "actions.createLead.properties.status.description": "The status of the lead", "actions.createLead.properties.status.displayName": "Status", "actions.createLead.properties.street.description": "The street address", "actions.createLead.properties.street.displayName": "Street", "actions.createLead.properties.title.description": "The job title of the lead", "actions.createLead.properties.title.displayName": "Title", "actions.createTask.data.task.description": "The created task", "actions.createTask.data.task.displayName": "Created Task", "actions.createTask.description": "Create a task", "actions.createTask.displayName": "Create Task", "actions.createTask.properties.activityDate.description": "Format YYYY-MM-DD eg. 2022-11-20.", "actions.createTask.properties.activityDate.displayName": "Due Date", "actions.createTask.properties.description.description": "Additional comments for the task", "actions.createTask.properties.description.displayName": "Comments", "actions.createTask.properties.ownerId.description": "The user to assign the task to", "actions.createTask.properties.ownerId.displayName": "Assignee", "actions.createTask.properties.priority.description": "The priority of the task", "actions.createTask.properties.priority.displayName": "Priority", "actions.createTask.properties.status.description": "The status of the task", "actions.createTask.properties.status.displayName": "Status", "actions.createTask.properties.subject.description": "The subject of the task", "actions.createTask.properties.subject.displayName": "Subject", "actions.createTask.properties.whatId.description": "Related to an Account", "actions.createTask.properties.whatId.displayName": "Related To", "actions.createTask.properties.whoId.description": "The contact related to the task", "actions.createTask.properties.whoId.displayName": "Name", "actions.deleteRecord.data.objectType.description": "The type of object that was deleted", "actions.deleteRecord.data.objectType.displayName": "Object Type", "actions.deleteRecord.data.recordId.description": "The ID of the deleted record", "actions.deleteRecord.data.recordId.displayName": "Record ID", "actions.deleteRecord.data.success.description": "Whether the deletion was successful", "actions.deleteRecord.data.success.displayName": "Success", "actions.deleteRecord.description": "Delete any type of record from Salesforce", "actions.deleteRecord.displayName": "Delete Record", "actions.deleteRecord.properties.customObjectName.description": "The name of the custom object (required when Object Type is 'Custom')", "actions.deleteRecord.properties.customObjectName.displayName": "Custom Object Name", "actions.deleteRecord.properties.objectType.description": "The type of Salesforce object to delete", "actions.deleteRecord.properties.objectType.displayName": "Object Type", "actions.deleteRecord.properties.recordId.description": "The ID of the record to delete", "actions.deleteRecord.properties.recordId.displayName": "Record ID", "actions.findRecords.data.done.description": "Whether all records have been retrieved", "actions.findRecords.data.done.displayName": "Done", "actions.findRecords.data.records.description": "List of found records", "actions.findRecords.data.records.displayName": "Records", "actions.findRecords.data.totalSize.description": "Total number of records found", "actions.findRecords.data.totalSize.displayName": "Total Size", "actions.findRecords.description": "Search for records in Salesforce using SOQL or field-based search", "actions.findRecords.displayName": "Find Records", "actions.findRecords.properties.fields.description": "Comma-separated list of fields to return (leave empty for all fields)", "actions.findRecords.properties.fields.displayName": "Fields", "actions.findRecords.properties.limit.description": "Maximum number of records to return (default: 200)", "actions.findRecords.properties.limit.displayName": "Limit", "actions.findRecords.properties.objectType.description": "The type of object to work with", "actions.findRecords.properties.objectType.displayName": "Object Type", "actions.findRecords.properties.offset.description": "Number of records to skip (for pagination)", "actions.findRecords.properties.offset.displayName": "Offset", "actions.findRecords.properties.orderBy.description": "Field to order results by", "actions.findRecords.properties.orderBy.displayName": "Order By", "actions.findRecords.properties.orderDirection.description": "Direction to order results", "actions.findRecords.properties.orderDirection.displayName": "Order Direction", "actions.findRecords.properties.searchField.description": "The field to search in", "actions.findRecords.properties.searchField.displayName": "Search Field", "actions.findRecords.properties.searchOperator.description": "The operator to use for comparison (=, !=, LIKE, etc.)", "actions.findRecords.properties.searchOperator.displayName": "Search Operator", "actions.findRecords.properties.searchValue.description": "The value to search for", "actions.findRecords.properties.searchValue.displayName": "Search Value", "actions.findRecords.properties.whereClause.description": "Custom SOQL WHERE clause (e.g., 'Email LIKE \\'%example.com\\'')", "actions.findRecords.properties.whereClause.displayName": "Where Clause", "actions.updateAccount.data.account.description": "The updated account", "actions.updateAccount.data.account.displayName": "Updated Account", "actions.updateAccount.description": "Update an account. At least one account field must be provided to update.", "actions.updateAccount.displayName": "Update Account", "actions.updateAccount.properties.accountId.description": "The ID of the account to update", "actions.updateAccount.properties.accountId.displayName": "Account ID", "actions.updateAccount.properties.accountNumber.description": "The account number", "actions.updateAccount.properties.accountNumber.displayName": "Account Number", "actions.updateAccount.properties.annualRevenue.description": "The annual revenue of the account", "actions.updateAccount.properties.annualRevenue.displayName": "Annual Revenue", "actions.updateAccount.properties.billingCity.description": "The billing city", "actions.updateAccount.properties.billingCity.displayName": "Billing City", "actions.updateAccount.properties.billingCountry.description": "The billing country", "actions.updateAccount.properties.billingCountry.displayName": "Billing Country", "actions.updateAccount.properties.billingState.description": "The billing state or province", "actions.updateAccount.properties.billingState.displayName": "Billing State/Province", "actions.updateAccount.properties.billingStreet.description": "The billing street address", "actions.updateAccount.properties.billingStreet.displayName": "Billing Street", "actions.updateAccount.properties.billingZipPostalCode.description": "The billing zip/postal code", "actions.updateAccount.properties.billingZipPostalCode.displayName": "Billing Zip/Postal Code", "actions.updateAccount.properties.description.description": "The description of the account", "actions.updateAccount.properties.description.displayName": "Description", "actions.updateAccount.properties.fax.description": "The fax number of the account", "actions.updateAccount.properties.fax.displayName": "Fax", "actions.updateAccount.properties.industry.description": "The industry of the account", "actions.updateAccount.properties.industry.displayName": "Industry", "actions.updateAccount.properties.name.description": "The name of the account", "actions.updateAccount.properties.name.displayName": "Account Name", "actions.updateAccount.properties.numberOfEmployees.description": "The number of employees", "actions.updateAccount.properties.numberOfEmployees.displayName": "Employees", "actions.updateAccount.properties.ownership.description": "The ownership of the account", "actions.updateAccount.properties.ownership.displayName": "Ownership", "actions.updateAccount.properties.parentId.description": "The parent account", "actions.updateAccount.properties.parentId.displayName": "Parent Account", "actions.updateAccount.properties.phone.description": "The phone number of the account", "actions.updateAccount.properties.phone.displayName": "Phone", "actions.updateAccount.properties.rating.description": "The rating of the account", "actions.updateAccount.properties.rating.displayName": "Rating", "actions.updateAccount.properties.shippingCity.description": "The shipping city", "actions.updateAccount.properties.shippingCity.displayName": "Shipping City", "actions.updateAccount.properties.shippingCountry.description": "The shipping country", "actions.updateAccount.properties.shippingCountry.displayName": "Shipping Country", "actions.updateAccount.properties.shippingState.description": "The shipping state or province", "actions.updateAccount.properties.shippingState.displayName": "Shipping State/Province", "actions.updateAccount.properties.shippingStreet.description": "The shipping street address", "actions.updateAccount.properties.shippingStreet.displayName": "Shipping Street", "actions.updateAccount.properties.shippingZipPostalCode.description": "The shipping zip/postal code", "actions.updateAccount.properties.shippingZipPostalCode.displayName": "Shipping Zip/Postal Code", "actions.updateAccount.properties.sic.description": "The SIC code of the account", "actions.updateAccount.properties.sic.displayName": "SIC Code", "actions.updateAccount.properties.site.description": "The account site", "actions.updateAccount.properties.site.displayName": "Account Site", "actions.updateAccount.properties.tickerSymbol.description": "The ticker symbol of the account", "actions.updateAccount.properties.tickerSymbol.displayName": "Ticker Symbol", "actions.updateAccount.properties.type.description": "The type of account", "actions.updateAccount.properties.type.displayName": "Type", "actions.updateAccount.properties.website.description": "The website of the account", "actions.updateAccount.properties.website.displayName": "Website", "actions.updateContact.data.contact.description": "The updated contact", "actions.updateContact.data.contact.displayName": "Updated Contact", "actions.updateContact.description": "Update a contact", "actions.updateContact.displayName": "Update Contact", "actions.updateContact.properties.accountId.description": "The ID of the account to associate with the contact", "actions.updateContact.properties.accountId.displayName": "Account ID", "actions.updateContact.properties.contactId.description": "The ID of the contact to update", "actions.updateContact.properties.contactId.displayName": "Contact ID", "actions.updateContact.properties.department.description": "The department of the contact", "actions.updateContact.properties.department.displayName": "Department", "actions.updateContact.properties.email.description": "The email of the contact", "actions.updateContact.properties.email.displayName": "Email", "actions.updateContact.properties.firstName.description": "The first name of the contact", "actions.updateContact.properties.firstName.displayName": "First Name", "actions.updateContact.properties.lastName.description": "The last name of the contact", "actions.updateContact.properties.lastName.displayName": "Last Name", "actions.updateContact.properties.mailingCity.description": "The city for mailing", "actions.updateContact.properties.mailingCity.displayName": "Mailing City", "actions.updateContact.properties.mailingCountry.description": "The country for mailing", "actions.updateContact.properties.mailingCountry.displayName": "Mailing Country", "actions.updateContact.properties.mailingPostalCode.description": "The postal code for mailing", "actions.updateContact.properties.mailingPostalCode.displayName": "Mailing Postal Code", "actions.updateContact.properties.mailingState.description": "The state/province for mailing", "actions.updateContact.properties.mailingState.displayName": "Mailing State", "actions.updateContact.properties.mailingStreet.description": "The street address for mailing", "actions.updateContact.properties.mailingStreet.displayName": "Mailing Street", "actions.updateContact.properties.phone.description": "The phone number of the contact", "actions.updateContact.properties.phone.displayName": "Phone", "actions.updateContact.properties.title.description": "The job title of the contact", "actions.updateContact.properties.title.displayName": "Title", "actions.updateLead.data.lead.description": "The updated lead", "actions.updateLead.data.lead.displayName": "Updated Lead", "actions.updateLead.description": "Update an existing lead in Salesforce", "actions.updateLead.displayName": "Update Lead", "actions.updateLead.properties.city.description": "The city", "actions.updateLead.properties.city.displayName": "City", "actions.updateLead.properties.company.description": "The company name", "actions.updateLead.properties.company.displayName": "Company", "actions.updateLead.properties.country.description": "The country", "actions.updateLead.properties.country.displayName": "Country", "actions.updateLead.properties.description.description": "Additional description of the lead", "actions.updateLead.properties.description.displayName": "Description", "actions.updateLead.properties.email.description": "The email address of the lead", "actions.updateLead.properties.email.displayName": "Email", "actions.updateLead.properties.firstName.description": "The first name of the lead", "actions.updateLead.properties.firstName.displayName": "First Name", "actions.updateLead.properties.industry.description": "The industry of the lead", "actions.updateLead.properties.industry.displayName": "Industry", "actions.updateLead.properties.lastName.description": "The last name of the lead", "actions.updateLead.properties.lastName.displayName": "Last Name", "actions.updateLead.properties.leadId.description": "The ID of the lead to update", "actions.updateLead.properties.leadId.displayName": "Lead ID", "actions.updateLead.properties.leadSource.description": "The source of the lead", "actions.updateLead.properties.leadSource.displayName": "Lead Source", "actions.updateLead.properties.phone.description": "The phone number of the lead", "actions.updateLead.properties.phone.displayName": "Phone", "actions.updateLead.properties.postalCode.description": "The postal code", "actions.updateLead.properties.postalCode.displayName": "Postal Code", "actions.updateLead.properties.rating.description": "The rating of the lead", "actions.updateLead.properties.rating.displayName": "Rating", "actions.updateLead.properties.state.description": "The state or province", "actions.updateLead.properties.state.displayName": "State/Province", "actions.updateLead.properties.status.description": "The status of the lead", "actions.updateLead.properties.status.displayName": "Status", "actions.updateLead.properties.street.description": "The street address", "actions.updateLead.properties.street.displayName": "Street", "actions.updateLead.properties.title.description": "The job title of the lead", "actions.updateLead.properties.title.displayName": "Title", "actions.updateTask.data.task.description": "The updated task", "actions.updateTask.data.task.displayName": "Updated Task", "actions.updateTask.description": "Update a task", "actions.updateTask.displayName": "Update Task", "actions.updateTask.properties.activityDate.description": "Format YYYY-MM-DD eg. 2022-11-20.", "actions.updateTask.properties.activityDate.displayName": "Due Date", "actions.updateTask.properties.ownerId.description": "The user ID to assign the task to", "actions.updateTask.properties.ownerId.displayName": "Owner ID", "actions.updateTask.properties.priority.description": "The priority of the task", "actions.updateTask.properties.priority.displayName": "Priority", "actions.updateTask.properties.status.description": "The status of the task", "actions.updateTask.properties.status.displayName": "Status", "actions.updateTask.properties.subject.description": "The subject of the task", "actions.updateTask.properties.subject.displayName": "Subject", "actions.updateTask.properties.taskId.description": "The ID of the task to update", "actions.updateTask.properties.taskId.displayName": "Task ID", "actions.updateTask.properties.whatId.description": "Related to an Account", "actions.updateTask.properties.whatId.displayName": "Related To", "actions.updateTask.properties.whoId.description": "The contact related to the task", "actions.updateTask.properties.whoId.displayName": "Name", "credentials.oauth2.description": "OAuth 2.0 authentication for Salesforce API", "credentials.oauth2.displayName": "OAuth 2.0", "credentials.oauth2.properties.apiVersion.description": "The API version to use for the Salesforce API", "credentials.oauth2.properties.apiVersion.displayName": "API Version", "credentials.oauth2.properties.clientId.description": "The client ID from your Salesforce OAuth app", "credentials.oauth2.properties.clientId.displayName": "Client ID", "credentials.oauth2.properties.clientSecret.description": "The client secret from your Salesforce OAuth app", "credentials.oauth2.properties.clientSecret.displayName": "Client Secret", "credentials.oauth2.properties.instanceUrl.description": "The Salesforce instance URL (e.g., https://test.salesforce.com for sandbox, https://login.salesforce.com for production, or 'enter your instance URL manually'). Leave empty to auto-detect from isSandbox setting.", "credentials.oauth2.properties.instanceUrl.displayName": "Instance URL", "credentials.oauth2.properties.isSandbox.description": "Whether this is a sandbox environment", "credentials.oauth2.properties.isSandbox.displayName": "Is Sandbox", "description": "Interact with Salesforce", "displayName": "Salesforce", "schemas.salesforceContact.properties.id.description": "The ID of the created contact", "schemas.salesforceContact.properties.id.displayName": "Contact ID", "schemas.salesforceContact.properties.success.description": "Whether the contact was created successfully", "schemas.salesforceContact.properties.success.displayName": "Success", "schemas.salesforceRecord.properties.id.description": "The ID of the record", "schemas.salesforceRecord.properties.id.displayName": "Record ID", "schemas.salesforceRecord.properties.name.description": "The name of the record", "schemas.salesforceRecord.properties.name.displayName": "Name", "settings.credential.description": "The credential to use for authentication", "settings.credential.displayName": "Credential"}, "schedule": {"description": "Trigger a workflow on a scheduled basis such as hourly, daily, weekly, or monthly", "displayName": "Schedule", "schemas.scheduleOutputData.properties.dateDay.description": "Day part of the date", "schemas.scheduleOutputData.properties.dateDay.displayName": "Date Day", "schemas.scheduleOutputData.properties.dateMonth.description": "Month part of the date (1-12)", "schemas.scheduleOutputData.properties.dateMonth.displayName": "Date Month", "schemas.scheduleOutputData.properties.dateYear.description": "Year part of the date", "schemas.scheduleOutputData.properties.dateYear.displayName": "Date Year", "schemas.scheduleOutputData.properties.dayOfWeek.description": "Numeric day of week (0-6, where 0 is Sunday)", "schemas.scheduleOutputData.properties.dayOfWeek.displayName": "Day Of Week", "schemas.scheduleOutputData.properties.id.description": "The ISO timestamp for the trigger (e.g., 2025-05-09T09:18:37Z)", "schemas.scheduleOutputData.properties.id.displayName": "ID", "schemas.scheduleOutputData.properties.nextOccurrence.description": "ISO timestamp of the next scheduled occurrence", "schemas.scheduleOutputData.properties.nextOccurrence.displayName": "Next Occurrence", "schemas.scheduleOutputData.properties.prettyDate.description": "Human-readable date format (e.g., May 9, 2025)", "schemas.scheduleOutputData.properties.prettyDate.displayName": "Pretty Date", "schemas.scheduleOutputData.properties.prettyDayOfWeek.description": "Name of day of week (e.g., Friday)", "schemas.scheduleOutputData.properties.prettyDayOfWeek.displayName": "Pretty Day Of Week", "schemas.scheduleOutputData.properties.prettyNextOccurrence.description": "Human-readable format of next occurrence", "schemas.scheduleOutputData.properties.prettyNextOccurrence.displayName": "Pretty Next Occurrence", "schemas.scheduleOutputData.properties.prettyTime.description": "Human-readable time format (e.g., 09:18:37 AM)", "schemas.scheduleOutputData.properties.prettyTime.displayName": "Pretty Time", "schemas.scheduleOutputData.properties.timeHour.description": "Hour part of the time (0-23)", "schemas.scheduleOutputData.properties.timeHour.displayName": "Time Hour", "schemas.scheduleOutputData.properties.timeMinute.description": "Minute part of the time", "schemas.scheduleOutputData.properties.timeMinute.displayName": "Time Minute", "schemas.scheduleOutputData.properties.timeSecond.description": "Second part of the time", "schemas.scheduleOutputData.properties.timeSecond.displayName": "Time Second", "schemas.scheduleOutputData.properties.weekOfYear.description": "The week number within the year (1-52)", "schemas.scheduleOutputData.properties.weekOfYear.displayName": "Week Of Year", "settings.timezone.description": "The timezone to use for scheduling", "settings.timezone.displayName": "Timezone", "triggers.scheduleCron.description": "Create a schedule using cron expression syntax", "triggers.scheduleCron.displayName": "<PERSON><PERSON> (Advanced)", "triggers.scheduleCron.properties.cronExpression.description": "Standard cron expression (e.g., '0 0 * * *' for daily at midnight)", "triggers.scheduleCron.properties.cronExpression.displayName": "Cron Expression", "triggers.scheduleCustom.description": "Create a custom schedule with flexible frequency", "triggers.scheduleCustom.displayName": "Custom", "triggers.scheduleCustom.properties.frequencyType.description": "How often the schedule should repeat", "triggers.scheduleCustom.properties.frequencyType.displayName": "Frequency Type", "triggers.scheduleCustom.properties.interval.description": "Repeat every X days/weeks/months (e.g., every 2 weeks)", "triggers.scheduleCustom.properties.interval.displayName": "Interval", "triggers.scheduleCustom.properties.startDate.description": "The date to start the schedule (format: YYYY-MM-DD)", "triggers.scheduleCustom.properties.startDate.displayName": "Start Date", "triggers.scheduleCustom.properties.timeOfDay.description": "The time of day to trigger (format: HH:MM, 24-hour)", "triggers.scheduleCustom.properties.timeOfDay.displayName": "Time of Day", "triggers.scheduleEveryDay.description": "Trigger on a daily schedule", "triggers.scheduleEveryDay.displayName": "Every Day", "triggers.scheduleEveryDay.properties.timeOfDay.description": "The time of day to trigger (format: HH:MM, 24-hour)", "triggers.scheduleEveryDay.properties.timeOfDay.displayName": "Time of Day", "triggers.scheduleEveryHour.description": "Trigger on an hourly schedule", "triggers.scheduleEveryHour.displayName": "Every Hour", "triggers.scheduleEveryHour.properties.minute.description": "The minute within the hour to trigger (0, 15, 30, 45)", "triggers.scheduleEveryHour.properties.minute.displayName": "Minute of Hour", "triggers.scheduleEveryMonth.description": "Trigger on a monthly schedule", "triggers.scheduleEveryMonth.displayName": "Every Month", "triggers.scheduleEveryMonth.properties.daysOfMonth.description": "The days of month to trigger", "triggers.scheduleEveryMonth.properties.daysOfMonth.displayName": "Days of Month", "triggers.scheduleEveryMonth.properties.timeOfDay.description": "The time of day to trigger (format: HH:MM, 24-hour)", "triggers.scheduleEveryMonth.properties.timeOfDay.displayName": "Time of Day", "triggers.scheduleEveryWeek.description": "Trigger on a weekly schedule", "triggers.scheduleEveryWeek.displayName": "Every Week", "triggers.scheduleEveryWeek.properties.daysOfWeek.description": "The days of week to trigger", "triggers.scheduleEveryWeek.properties.daysOfWeek.displayName": "Days of Week", "triggers.scheduleEveryWeek.properties.timeOfDay.description": "The time of day to trigger (format: HH:MM, 24-hour)", "triggers.scheduleEveryWeek.properties.timeOfDay.displayName": "Time of Day"}, "slack": {"actions.get_channel.description": "Get information about a Slack channel", "actions.get_channel.displayName": "Get Channel Info", "actions.get_channel.properties.channel_id.description": "The ID of the channel to get information about", "actions.get_channel.properties.channel_id.displayName": "Channel ID", "actions.get_user.description": "Get information about a Slack user", "actions.get_user.displayName": "Get User Info", "actions.get_user.properties.user_id.description": "The ID of the user to get information about", "actions.get_user.properties.user_id.displayName": "User ID", "actions.list_channels.description": "List channels in the workspace", "actions.list_channels.displayName": "List Channels", "actions.list_channels.properties.exclude_archived.description": "Whether to exclude archived channels", "actions.list_channels.properties.exclude_archived.displayName": "Exclude Archived", "actions.list_channels.properties.types.description": "Types of channels to include", "actions.list_channels.properties.types.displayName": "Channel Types", "actions.send_message.description": "Send a message to a Slack channel or user", "actions.send_message.displayName": "Send Message", "actions.send_message.properties.attachments.description": "JSON string of message attachments (for legacy formatting)", "actions.send_message.properties.attachments.displayName": "Attachments", "actions.send_message.properties.blocks.description": "JSON string of Block Kit blocks", "actions.send_message.properties.blocks.displayName": "Blocks", "actions.send_message.properties.channel_id.description": "The ID of the channel or user to send the message to", "actions.send_message.properties.channel_id.displayName": "Channel ID", "actions.send_message.properties.message_type.description": "The type of message to send", "actions.send_message.properties.message_type.displayName": "Message Type", "actions.send_message.properties.text.description": "The text content of the message", "actions.send_message.properties.text.displayName": "Message Text", "credentials.slack.api_token.description": "", "credentials.slack.api_token.displayName": "", "api_token": "", "description": "Slack messaging platform integration", "displayName": "<PERSON><PERSON>ck", "schemas.channel_created_event.properties.channel_id.description": "The ID of the created channel", "schemas.channel_created_event.properties.channel_name.description": "The name of the created channel", "schemas.channel_created_event.properties.created.description": "The timestamp when the channel was created", "schemas.channel_created_event.properties.creator_id.description": "The ID of the user who created the channel", "schemas.channel_created_event.properties.event_type.description": "The type of event (channel_created)", "schemas.channel_created_event.properties.is_private.description": "Whether the channel is private", "schemas.message_event.properties.channel_id.description": "The ID of the channel where the message was posted", "schemas.message_event.properties.event_type.description": "The type of event (message)", "schemas.message_event.properties.team.description": "The team ID (workspace)", "schemas.message_event.properties.text.description": "The text content of the message", "schemas.message_event.properties.ts.description": "The timestamp of the message", "schemas.message_event.properties.user_id.description": "The ID of the user who posted the message", "schemas.slackEvents.urlGroup.data.properties.apiKey.description": "", "schemas.slackEvents.urlGroup.data.properties.url.description": "", "schemas.user_joined_channel_event.properties.channel_id.description": "The ID of the channel the user joined", "schemas.user_joined_channel_event.properties.event_type.description": "The type of event (member_joined_channel)", "schemas.user_joined_channel_event.properties.team.description": "The team ID (workspace)", "schemas.user_joined_channel_event.properties.user_id.description": "The ID of the user who joined the channel", "settings.credential.description": "Credential to use", "settings.credential.displayName": "Credential", "triggers.channel_created.description": "Triggers when a new channel is created", "triggers.channel_created.displayName": "New Channel Created", "triggers.channel_created.properties.urlGroup.description": "The URL and API key for Slack webhook events", "triggers.channel_created.properties.urlGroup.displayName": "URL Group", "triggers.channel_created.properties.urlGroup.properties.callbackUrl.description": "Copy this URL and paste it in your Slack app's Event Subscriptions settings", "triggers.channel_created.properties.urlGroup.properties.callbackUrl.displayName": "Callback URL", "triggers.message_posted.description": "Triggers when a new message is posted to a channel", "triggers.message_posted.displayName": "New Message Posted", "triggers.message_posted.properties.urlGroup.description": "The URL and API key for Slack webhook events", "triggers.message_posted.properties.urlGroup.displayName": "URL Group", "triggers.message_posted.properties.urlGroup.properties.callbackUrl.description": "Copy this URL and paste it in your Slack app's Event Subscriptions settings", "triggers.message_posted.properties.urlGroup.properties.callbackUrl.displayName": "Callback URL", "triggers.user_joined_channel.description": "Triggers when a user joins a channel", "triggers.user_joined_channel.displayName": "User Joined Channel", "triggers.user_joined_channel.properties.urlGroup.description": "The URL and API key for Slack webhook events", "triggers.user_joined_channel.properties.urlGroup.displayName": "URL Group", "triggers.user_joined_channel.properties.urlGroup.properties.callbackUrl.description": "Copy this URL and paste it in your Slack app's Event Subscriptions settings", "triggers.user_joined_channel.properties.urlGroup.properties.callbackUrl.displayName": "Callback URL"}, "storage": {"actions.get_multiple_values.data.properties.success.description": "Whether the operation was successful", "actions.get_multiple_values.data.properties.values.description": "Object with keys mapped to their values", "actions.get_multiple_values.description": "Get multiple values stored at defined keys", "actions.get_multiple_values.displayName": "Get Multiple Values", "actions.get_multiple_values.properties.keys.description": "Array of keys to retrieve values for", "actions.get_multiple_values.properties.keys.displayName": "Keys", "actions.get_multiple_values.properties.success_if_not_exist.description": "Whether to return success and continue flow if keys don't exist (false will stop the flow)", "actions.get_multiple_values.properties.success_if_not_exist.displayName": "Success If Not Exist", "actions.get_value.data.properties.exists.description": "Whether the key existed", "actions.get_value.data.properties.success.description": "Whether the operation was successful", "actions.get_value.data.properties.value.description": "The retrieved value", "actions.get_value.description": "Get a value stored at a defined key", "actions.get_value.displayName": "Get Value", "actions.get_value.properties.key.description": "The key to retrieve the value from", "actions.get_value.properties.key.displayName": "Key", "actions.get_value.properties.success_if_not_exist.description": "Whether to return success and continue flow if the key doesn't exist (false will stop the flow)", "actions.get_value.properties.success_if_not_exist.displayName": "Success If Not Exist", "actions.pop_value_from_list.data.properties.list_length.description": "The new length of the list after popping", "actions.pop_value_from_list.data.properties.success.description": "Whether the operation was successful", "actions.pop_value_from_list.data.properties.value.description": "The popped value or default value", "actions.pop_value_from_list.description": "Pop a value off a list of values, removing the value from the list. Optionally, return a default value if the list is empty", "actions.pop_value_from_list.displayName": "Pop Value From List", "actions.pop_value_from_list.properties.default_value.description": "Value to return if list is empty", "actions.pop_value_from_list.properties.default_value.displayName": "Default Value", "actions.pop_value_from_list.properties.key.description": "The key for the list", "actions.pop_value_from_list.properties.key.displayName": "Key", "actions.pop_value_from_list.properties.location.description": "Location to pop from - start (FIFO) or end (LIFO)", "actions.pop_value_from_list.properties.location.displayName": "Location", "actions.push_value_onto_list.data.properties.key.description": "The key for the list", "actions.push_value_onto_list.data.properties.list_length.description": "The new length of the list", "actions.push_value_onto_list.data.properties.success.description": "Whether the operation was successful", "actions.push_value_onto_list.description": "Push a value onto a list of values", "actions.push_value_onto_list.displayName": "Push Value Onto List", "actions.push_value_onto_list.properties.key.description": "The key for the list", "actions.push_value_onto_list.properties.key.displayName": "Key", "actions.push_value_onto_list.properties.value.description": "The value to push onto the list", "actions.push_value_onto_list.properties.value.displayName": "Value", "actions.remove_all_values.data.properties.deleted_count.description": "Number of keys that were deleted", "actions.remove_all_values.data.properties.success.description": "Whether the operation was successful", "actions.remove_all_values.description": "Removes all values for the connected account", "actions.remove_all_values.displayName": "Remove All Values", "actions.remove_all_values.properties.confirm.description": "Must be true to confirm deletion of all matching keys", "actions.remove_all_values.properties.confirm.displayName": "Confirm Deletion", "actions.remove_all_values.properties.pattern.description": "Optional pattern to filter keys to remove (e.g., 'temp:*')", "actions.remove_all_values.properties.pattern.displayName": "Key Pattern", "actions.remove_value.data.properties.existed.description": "Whether the key existed before removal", "actions.remove_value.data.properties.success.description": "Whether the operation was successful", "actions.remove_value.description": "Remove a value stored at a defined key. Also works if the value is a list", "actions.remove_value.displayName": "Remove Value", "actions.remove_value.properties.key.description": "The key to remove", "actions.remove_value.properties.key.displayName": "Key", "actions.set_multiple_values.data.properties.keys_set.description": "Array of keys that were successfully set", "actions.set_multiple_values.data.properties.success.description": "Whether the operation was successful", "actions.set_multiple_values.description": "Set multiple values stored at defined keys", "actions.set_multiple_values.displayName": "Set Multiple Values", "actions.set_multiple_values.properties.values.description": "Object with key-value pairs to store", "actions.set_multiple_values.properties.values.displayName": "Values", "actions.set_value.data.properties.key.description": "The key that was set", "actions.set_value.data.properties.success.description": "Whether the operation was successful", "actions.set_value.description": "Set a value stored at a defined key", "actions.set_value.displayName": "Set Value", "actions.set_value.properties.key.description": "The key to store the value under", "actions.set_value.properties.key.displayName": "Key", "actions.set_value.properties.value.description": "The value to store (will be JSON-encoded)", "actions.set_value.properties.value.displayName": "Value", "description": "Redis-based storage for temporary and persistent data with organizational scoping", "displayName": "Storage", "settings.scope_type.description": "Data lifecycle scope - 'flow' for auto-cleanup on flow completion, 'global' for persistent storage", "settings.scope_type.displayName": "Scope Type"}, "virtualstore": {"actions.direct_session_create.description": "Create a new direct session", "actions.direct_session_create.displayName": "Create Direct Session", "actions.direct_session_create.properties.guest.description": "Guest information", "actions.direct_session_create.properties.guest.displayName": "Guest", "actions.direct_session_create.properties.guest.properties.email.description": "Guest email", "actions.direct_session_create.properties.guest.properties.email.displayName": "Email", "actions.direct_session_create.properties.guest.properties.externalId.description": "Guest external id", "actions.direct_session_create.properties.guest.properties.externalId.displayName": "External ID", "actions.direct_session_create.properties.guest.properties.familyFurigana.description": "The family name of furigana format", "actions.direct_session_create.properties.guest.properties.familyFurigana.displayName": "Family Furigana", "actions.direct_session_create.properties.guest.properties.familyKatakana.description": "The family name of katakana format", "actions.direct_session_create.properties.guest.properties.familyKatakana.displayName": "Family Katakana", "actions.direct_session_create.properties.guest.properties.familyName.description": "The family name of guest", "actions.direct_session_create.properties.guest.properties.familyName.displayName": "Family Name", "actions.direct_session_create.properties.guest.properties.firstFurigana.description": "The first name of furigana format", "actions.direct_session_create.properties.guest.properties.firstFurigana.displayName": "First Furigana", "actions.direct_session_create.properties.guest.properties.firstKatakana.description": "The first name of katakana format", "actions.direct_session_create.properties.guest.properties.firstKatakana.displayName": "First Katakana", "actions.direct_session_create.properties.guest.properties.firstName.description": "The first name of guest", "actions.direct_session_create.properties.guest.properties.firstName.displayName": "First Name", "actions.direct_session_create.properties.guest.properties.phoneNumber.description": "Guest's phone number", "actions.direct_session_create.properties.guest.properties.phoneNumber.displayName": "Phone Number", "actions.direct_session_create.properties.guest.properties.webId.description": "Guest web id", "actions.direct_session_create.properties.guest.properties.webId.displayName": "Web ID", "actions.direct_session_create.properties.hostId.description": "Host ID", "actions.direct_session_create.properties.hostId.displayName": "Host ID", "actions.direct_session_create.properties.isTest.description": "Flag for test mode", "actions.direct_session_create.properties.isTest.displayName": "Is Test", "actions.direct_session_create.properties.key.description": "Private key for the personal direct session", "actions.direct_session_create.properties.key.displayName": "Key", "actions.direct_session_create.properties.organizationId.description": "VS ID of the organization", "actions.direct_session_create.properties.organizationId.displayName": "VS Organization ID", "actions.direct_session_create.properties.storeId.description": "ID of the store", "actions.direct_session_create.properties.storeId.displayName": "Store ID", "actions.direct_session_create.properties.urlType.description": "URL source that user creates direct session from", "actions.direct_session_create.properties.urlType.displayName": "URL Type", "actions.direct_session_create.properties.utmSource.description": "UTM source", "actions.direct_session_create.properties.utmSource.displayName": "UTM Source", "actions.direct_session_list.description": "Get a list of all direct sessions in the specific store", "actions.direct_session_list.displayName": "List Direct Sessions", "actions.direct_session_list.properties.page.description": "Page number for pagination", "actions.direct_session_list.properties.page.displayName": "Page", "actions.direct_session_list.properties.per_page.description": "Number of items per page", "actions.direct_session_list.properties.per_page.displayName": "Per Page", "actions.direct_session_list.properties.sort.description": "Sort option for direct session list", "actions.direct_session_list.properties.sort.displayName": "Sort", "actions.direct_session_list.properties.storeId.description": "ID of the store", "actions.direct_session_list.properties.storeId.displayName": "Store ID", "actions.direct_session_retrieve.description": "Retrieve a direct session by ID", "actions.direct_session_retrieve.displayName": "Retrieve Direct Session", "actions.direct_session_retrieve.properties.directSessionId.description": "ID of the direct session", "actions.direct_session_retrieve.properties.directSessionId.displayName": "Direct Session ID", "actions.host_list.description": "Get a list of all hosts in the specific store", "actions.host_list.displayName": "List Hosts", "actions.host_list.properties.page.description": "Page number for pagination", "actions.host_list.properties.page.displayName": "Page", "actions.host_list.properties.per_page.description": "Number of items per page", "actions.host_list.properties.per_page.displayName": "Per Page", "actions.host_list.properties.role.description": "Filter by host role", "actions.host_list.properties.role.displayName": "Role", "actions.reservation_cancel.description": "Cancel a reservation by ID", "actions.reservation_cancel.displayName": "Cancel Reservation", "actions.reservation_cancel.properties.reservationId.description": "ID of the reservation", "actions.reservation_cancel.properties.reservationId.displayName": "Reservation ID", "actions.reservation_create.description": "Create a new reservation", "actions.reservation_create.displayName": "Create Reservation", "actions.reservation_create.properties.creatorId.description": "Creator id, if the reservation is created by host", "actions.reservation_create.properties.creatorId.displayName": "Creator ID", "actions.reservation_create.properties.durationId.description": "The duration setting ID", "actions.reservation_create.properties.durationId.displayName": "Duration ID", "actions.reservation_create.properties.equipmentId.description": "Equipment id, required for the offline reservation", "actions.reservation_create.properties.equipmentId.displayName": "Equipment ID", "actions.reservation_create.properties.guest.description": "Guest information", "actions.reservation_create.properties.guest.displayName": "Guest", "actions.reservation_create.properties.guest.properties.email.description": "Guest email", "actions.reservation_create.properties.guest.properties.email.displayName": "Email", "actions.reservation_create.properties.guest.properties.externalId.description": "External id of the resource", "actions.reservation_create.properties.guest.properties.externalId.displayName": "External ID", "actions.reservation_create.properties.guest.properties.familyFurigana.description": "The family name of furigana format", "actions.reservation_create.properties.guest.properties.familyFurigana.displayName": "Family Furigana", "actions.reservation_create.properties.guest.properties.familyKatakana.description": "The family name of katakana format", "actions.reservation_create.properties.guest.properties.familyKatakana.displayName": "Family Katakana", "actions.reservation_create.properties.guest.properties.familyName.description": "The family name of guest", "actions.reservation_create.properties.guest.properties.familyName.displayName": "Family Name", "actions.reservation_create.properties.guest.properties.firstFurigana.description": "The first name of furigana format", "actions.reservation_create.properties.guest.properties.firstFurigana.displayName": "First Furigana", "actions.reservation_create.properties.guest.properties.firstKatakana.description": "The first name of katakana format", "actions.reservation_create.properties.guest.properties.firstKatakana.displayName": "First Katakana", "actions.reservation_create.properties.guest.properties.firstName.description": "The first name of guest", "actions.reservation_create.properties.guest.properties.firstName.displayName": "First Name", "actions.reservation_create.properties.guest.properties.note.description": "Guest note", "actions.reservation_create.properties.guest.properties.note.displayName": "Note", "actions.reservation_create.properties.guest.properties.phoneNumber.description": "Guest's phone number", "actions.reservation_create.properties.guest.properties.phoneNumber.displayName": "Phone Number", "actions.reservation_create.properties.guest.properties.webId.description": "Web id", "actions.reservation_create.properties.guest.properties.webId.displayName": "Web ID", "actions.reservation_create.properties.hostId.description": "Host id, if the reservation is being created with a specific host", "actions.reservation_create.properties.hostId.displayName": "Host ID", "actions.reservation_create.properties.isTest.description": "Flag for test mode", "actions.reservation_create.properties.isTest.displayName": "Is Test", "actions.reservation_create.properties.organizationId.description": "VS ID of the organization", "actions.reservation_create.properties.organizationId.displayName": "VS Organization ID", "actions.reservation_create.properties.response.description": "The hearing form response", "actions.reservation_create.properties.response.displayName": "Response", "actions.reservation_create.properties.response.properties.answers.description": "The answer list", "actions.reservation_create.properties.response.properties.answers.displayName": "Answers", "actions.reservation_create.properties.response.properties.answers.items.properties.answer.description": "The question's response", "actions.reservation_create.properties.response.properties.answers.items.properties.answer.displayName": "Answer", "actions.reservation_create.properties.response.properties.answers.items.properties.questionId.description": "The question ID", "actions.reservation_create.properties.response.properties.answers.items.properties.questionId.displayName": "Question ID", "actions.reservation_create.properties.response.properties.surveyId.description": "Survey id", "actions.reservation_create.properties.response.properties.surveyId.displayName": "Survey ID", "actions.reservation_create.properties.startAt.description": "Start at datetime", "actions.reservation_create.properties.startAt.displayName": "Start At", "actions.reservation_create.properties.storeId.description": "ID of the store", "actions.reservation_create.properties.storeId.displayName": "Store ID", "actions.reservation_create.properties.type.description": "Type of the reservation", "actions.reservation_create.properties.type.displayName": "Type", "actions.reservation_create.properties.urlType.description": "URL source that user creates reservation from", "actions.reservation_create.properties.urlType.displayName": "URL Type", "actions.reservation_list.description": "Get a list of all reservations in the specific store", "actions.reservation_list.displayName": "List Reservations", "actions.reservation_list.properties.host_id.description": "Filter by host ID", "actions.reservation_list.properties.host_id.displayName": "Host ID", "actions.reservation_list.properties.page.description": "Page number for pagination", "actions.reservation_list.properties.page.displayName": "Page", "actions.reservation_list.properties.per_page.description": "Number of items per page", "actions.reservation_list.properties.per_page.displayName": "Per Page", "actions.reservation_list.properties.status.description": "Filter by reservation status", "actions.reservation_list.properties.status.displayName": "Status", "actions.reservation_list_day_slots.description": "Get a list of day slots that available for creating a new reservation", "actions.reservation_list_day_slots.displayName": "List Day Slots", "actions.reservation_list_day_slots.properties.duration.description": "Duration in minutes (15, 30, 45, 60)", "actions.reservation_list_day_slots.properties.duration.displayName": "Duration", "actions.reservation_list_day_slots.properties.durationId.description": "ID of the duration", "actions.reservation_list_day_slots.properties.durationId.displayName": "Duration ID", "actions.reservation_list_day_slots.properties.equipmentId.description": "ID of the equipment, This is for the offline reservation", "actions.reservation_list_day_slots.properties.equipmentId.displayName": "Equipment ID", "actions.reservation_list_day_slots.properties.fromDate.description": "From date", "actions.reservation_list_day_slots.properties.fromDate.displayName": "From Date", "actions.reservation_list_day_slots.properties.hostId.description": "ID of the host", "actions.reservation_list_day_slots.properties.hostId.displayName": "Host ID", "actions.reservation_list_day_slots.properties.reservationId.description": "Reservation ID in case changing the date time", "actions.reservation_list_day_slots.properties.reservationId.displayName": "Reservation ID", "actions.reservation_list_day_slots.properties.storeId.description": "ID of the store", "actions.reservation_list_day_slots.properties.storeId.displayName": "Store ID", "actions.reservation_list_day_slots.properties.toDate.description": "To date", "actions.reservation_list_day_slots.properties.toDate.displayName": "To Date", "actions.reservation_list_day_slots.properties.type.description": "Type of the reservation", "actions.reservation_list_day_slots.properties.type.displayName": "Type", "actions.reservation_list_time_slots.description": "Get a list of time slots that available for creating a new reservation", "actions.reservation_list_time_slots.displayName": "List Time Slots", "actions.reservation_list_time_slots.properties.duration.description": "Duration in minutes (15, 30, 45, 60)", "actions.reservation_list_time_slots.properties.duration.displayName": "Duration", "actions.reservation_list_time_slots.properties.durationId.description": "ID of the duration", "actions.reservation_list_time_slots.properties.durationId.displayName": "Duration ID", "actions.reservation_list_time_slots.properties.equipmentId.description": "ID of the equipment, This is for the offline reservation", "actions.reservation_list_time_slots.properties.equipmentId.displayName": "Equipment ID", "actions.reservation_list_time_slots.properties.hostId.description": "ID of the host, if the reservation is being created with a specific host", "actions.reservation_list_time_slots.properties.hostId.displayName": "Host ID", "actions.reservation_list_time_slots.properties.reservationId.description": "ID of the reservation in case updating the reservation", "actions.reservation_list_time_slots.properties.reservationId.displayName": "Reservation ID", "actions.reservation_list_time_slots.properties.selectedDay.description": "Selected Day (for example '2025-09-08')", "actions.reservation_list_time_slots.properties.selectedDay.displayName": "Selected Day", "actions.reservation_list_time_slots.properties.storeId.description": "ID of the store", "actions.reservation_list_time_slots.properties.storeId.displayName": "Store ID", "actions.reservation_list_time_slots.properties.type.description": "Type of the reservation", "actions.reservation_list_time_slots.properties.type.displayName": "Type", "actions.reservation_retrieve.description": "Retrieve a reservation by ID", "actions.reservation_retrieve.displayName": "Retrieve Reservation", "actions.reservation_retrieve.properties.reservationId.description": "ID of the reservation", "actions.reservation_retrieve.properties.reservationId.displayName": "Reservation ID", "actions.store_create.description": "Create a new store", "actions.store_create.displayName": "Create Store", "actions.store_create.properties.brand.description": "Brand of the store, only use for the KAO organization", "actions.store_create.properties.brand.displayName": "Brand", "actions.store_create.properties.name.description": "Name of the store", "actions.store_create.properties.name.displayName": "Name", "actions.store_list.description": "Get a list of all stores", "actions.store_list.displayName": "List Stores", "actions.store_list.properties.page.description": "Page number for pagination", "actions.store_list.properties.page.displayName": "Page", "actions.store_list.properties.per_page.description": "Number of items per page", "actions.store_list.properties.per_page.displayName": "Per Page", "actions.store_list_duration.description": "Get a list of all durations in the specific store", "actions.store_list_duration.displayName": "List Store Durations", "actions.store_list_duration.properties.storeId.description": "ID of the store", "actions.store_list_duration.properties.storeId.displayName": "Store ID", "actions.store_list_duration.properties.type.description": "Type of duration", "actions.store_list_duration.properties.type.displayName": "Type", "actions.store_retrieve.description": "Retrieve a store by ID", "actions.store_retrieve.displayName": "Retrieve Store", "actions.store_retrieve.properties.resourceId.description": "ID of the store", "actions.store_retrieve.properties.resourceId.displayName": "Resource ID", "actions.store_retrieve_host.description": "Retrieve a host by ID in the specific store", "actions.store_retrieve_host.displayName": "Retrieve Host", "actions.store_retrieve_host.properties.storeHostId.description": "ID of the host in the specific store", "actions.store_retrieve_host.properties.storeHostId.displayName": "Store Host ID", "actions.store_update_duration.description": "Update a duration in the specific store", "actions.store_update_duration.displayName": "Update Store Duration", "actions.store_update_duration.properties.durations.description": "List of durations to update", "actions.store_update_duration.properties.durations.displayName": "Durations", "actions.store_update_duration.properties.durations.items.properties.byHostSurveyId.description": "The survey ID (by host)", "actions.store_update_duration.properties.durations.items.properties.byHostSurveyId.displayName": "By Host Survey ID", "actions.store_update_duration.properties.durations.items.properties.description.description": "Duration description", "actions.store_update_duration.properties.durations.items.properties.description.displayName": "Description", "actions.store_update_duration.properties.durations.items.properties.duration.description": "The duration in minutes", "actions.store_update_duration.properties.durations.items.properties.duration.displayName": "Duration", "actions.store_update_duration.properties.durations.items.properties.imageId.description": "The duration image ID", "actions.store_update_duration.properties.durations.items.properties.imageId.displayName": "Image ID", "actions.store_update_duration.properties.durations.items.properties.index.description": "The index of duration item", "actions.store_update_duration.properties.durations.items.properties.index.displayName": "Index", "actions.store_update_duration.properties.durations.items.properties.name.description": "The duration name", "actions.store_update_duration.properties.durations.items.properties.name.displayName": "Name", "actions.store_update_duration.properties.durations.items.properties.reception.description": "Reception time setting", "actions.store_update_duration.properties.durations.items.properties.reception.displayName": "Reception", "actions.store_update_duration.properties.durations.items.properties.reception.properties.enabled.description": "Enable/Disable reception time for duration", "actions.store_update_duration.properties.durations.items.properties.reception.properties.enabled.displayName": "Enabled", "actions.store_update_duration.properties.durations.items.properties.reception.properties.fromDate.description": "The from-date for booking a reservation", "actions.store_update_duration.properties.durations.items.properties.reception.properties.fromDate.displayName": "From Date", "actions.store_update_duration.properties.durations.items.properties.reception.properties.limitDay.description": "Reservation limit in day", "actions.store_update_duration.properties.durations.items.properties.reception.properties.limitDay.displayName": "Limit Day", "actions.store_update_duration.properties.durations.items.properties.reception.properties.limitHour.description": "Reservation limit in hour", "actions.store_update_duration.properties.durations.items.properties.reception.properties.limitHour.displayName": "Limit Hour", "actions.store_update_duration.properties.durations.items.properties.reception.properties.offsetDay.description": "Reservation offset in day", "actions.store_update_duration.properties.durations.items.properties.reception.properties.offsetDay.displayName": "Offset Day", "actions.store_update_duration.properties.durations.items.properties.reception.properties.offsetHour.description": "Reservation offset in hour", "actions.store_update_duration.properties.durations.items.properties.reception.properties.offsetHour.displayName": "Offset Hour", "actions.store_update_duration.properties.durations.items.properties.reception.properties.toDate.description": "The to-date for booking a reservation", "actions.store_update_duration.properties.durations.items.properties.reception.properties.toDate.displayName": "To Date", "actions.store_update_duration.properties.durations.items.properties.surveyId.description": "The survey ID", "actions.store_update_duration.properties.durations.items.properties.surveyId.displayName": "Survey ID", "actions.store_update_duration.properties.durations.items.properties.type.description": "The duration type", "actions.store_update_duration.properties.durations.items.properties.type.displayName": "Type", "actions.store_update_duration.properties.storeId.description": "ID of the store", "actions.store_update_duration.properties.storeId.displayName": "Store ID", "actions.store_update_host.description": "Update a host by ID in the specific store", "actions.store_update_host.displayName": "Update Store Host", "actions.store_update_host.properties.directSessionIndex.description": "Index for direct session ordering", "actions.store_update_host.properties.directSessionIndex.displayName": "Direct Session Index", "actions.store_update_host.properties.directSessionIsVisible.description": "Whether the host is visible for direct sessions", "actions.store_update_host.properties.directSessionIsVisible.displayName": "Direct Session Is Visible", "actions.store_update_host.properties.directSessionListSort.description": "Sort option for direct session list", "actions.store_update_host.properties.directSessionListSort.displayName": "Direct Session List Sort", "actions.store_update_host.properties.directSessionSettings.description": "Direct session settings for the host", "actions.store_update_host.properties.directSessionSettings.displayName": "Direct Session Settings", "actions.store_update_host.properties.directSessionSettings.properties.allowInternationalPhone.description": "Allow international phone number", "actions.store_update_host.properties.directSessionSettings.properties.allowInternationalPhone.displayName": "Allow International Phone", "actions.store_update_host.properties.directSessionSettings.properties.emailEnabled.description": "Enable/Disable Guest Email in Direct Session Form", "actions.store_update_host.properties.directSessionSettings.properties.emailEnabled.displayName": "Email Enabled", "actions.store_update_host.properties.directSessionSettings.properties.externalIdEnabled.description": "For collect some sort of external ID", "actions.store_update_host.properties.directSessionSettings.properties.externalIdEnabled.displayName": "External ID Enabled", "actions.store_update_host.properties.directSessionSettings.properties.externalIdRegexEnabled.description": "Regex for external ID enabled", "actions.store_update_host.properties.directSessionSettings.properties.externalIdRegexEnabled.displayName": "External ID Regex Enabled", "actions.store_update_host.properties.directSessionSettings.properties.externalIdRegexErrorMsg.description": "The error message for guest when input invalid external ID", "actions.store_update_host.properties.directSessionSettings.properties.externalIdRegexErrorMsg.displayName": "External ID Regex Error Message", "actions.store_update_host.properties.directSessionSettings.properties.externalIdRegexValue.description": "External ID regex value", "actions.store_update_host.properties.directSessionSettings.properties.externalIdRegexValue.displayName": "External ID Regex Value", "actions.store_update_host.properties.directSessionSettings.properties.externalIdRequired.description": "External ID mandatory flag", "actions.store_update_host.properties.directSessionSettings.properties.externalIdRequired.displayName": "External ID Required", "actions.store_update_host.properties.directSessionSettings.properties.externalIdText.description": "External ID display text", "actions.store_update_host.properties.directSessionSettings.properties.externalIdText.displayName": "External ID Text", "actions.store_update_host.properties.directSessionSettings.properties.furiganaEnabled.description": "Enable/Disable Guest <PERSON><PERSON><PERSON> in Direct Session Form", "actions.store_update_host.properties.directSessionSettings.properties.furiganaEnabled.displayName": "<PERSON><PERSON><PERSON> Enabled", "actions.store_update_host.properties.directSessionSettings.properties.guestNameEnabled.description": "Enable/Disable Guest Name in Direct Session Form", "actions.store_update_host.properties.directSessionSettings.properties.guestNameEnabled.displayName": "Guest Name Enabled", "actions.store_update_host.properties.directSessionSettings.properties.katakanaEnabled.description": "Enable/Disable Guest <PERSON><PERSON><PERSON> in Direct Session Form", "actions.store_update_host.properties.directSessionSettings.properties.katakanaEnabled.displayName": "<PERSON><PERSON>na Enabled", "actions.store_update_host.properties.directSessionSettings.properties.phoneEnabled.description": "Enable/Disable Phone number in Direct Session Form", "actions.store_update_host.properties.directSessionSettings.properties.phoneEnabled.displayName": "Phone Enabled", "actions.store_update_host.properties.directSessionSettings.properties.surveyId.description": "Survey ID", "actions.store_update_host.properties.directSessionSettings.properties.surveyId.displayName": "Survey ID", "actions.store_update_host.properties.isAvailable.description": "Can be assigned", "actions.store_update_host.properties.isAvailable.displayName": "Is Available", "actions.store_update_host.properties.isAvailableDirectSession.description": "Available for direct session", "actions.store_update_host.properties.isAvailableDirectSession.displayName": "Is Available Direct Session", "actions.store_update_host.properties.isAvailableOffline.description": "Available for offline session", "actions.store_update_host.properties.isAvailableOffline.displayName": "Is Available Offline", "actions.store_update_host.properties.isPublishNameCardInPrivateUrl.description": "Show hide name card in private URL", "actions.store_update_host.properties.isPublishNameCardInPrivateUrl.displayName": "Is Publish Name Card In Private URL", "actions.store_update_host.properties.isPublishNameCardInSession.description": "Show hide name card in session room", "actions.store_update_host.properties.isPublishNameCardInSession.displayName": "Is Publish Name Card In Session", "actions.store_update_host.properties.liffId.description": "LIFF ID for the host", "actions.store_update_host.properties.liffId.displayName": "LIFF ID", "actions.store_update_host.properties.liffOfflineId.description": "LIFF offline ID for the host", "actions.store_update_host.properties.liffOfflineId.displayName": "LIFF Offline ID", "actions.store_update_host.properties.offlineIndex.description": "Index for offline session ordering", "actions.store_update_host.properties.offlineIndex.displayName": "Offline Index", "actions.store_update_host.properties.offlineIsVisible.description": "Whether the host is visible for offline sessions", "actions.store_update_host.properties.offlineIsVisible.displayName": "Offline Is Visible", "actions.store_update_host.properties.onlineIndex.description": "Index for online session ordering", "actions.store_update_host.properties.onlineIndex.displayName": "Online Index", "actions.store_update_host.properties.onlineIsVisible.description": "Whether the host is visible for online sessions", "actions.store_update_host.properties.onlineIsVisible.displayName": "Online Is Visible", "actions.store_update_host.properties.storeHostId.description": "ID of the host in the specific store", "actions.store_update_host.properties.storeHostId.displayName": "Store Host ID", "actions.survey_list.description": "Get a list of all surveys in the specific store", "actions.survey_list.displayName": "List Surveys", "actions.survey_list.properties.storeId.description": "ID of the store", "actions.survey_list.properties.storeId.displayName": "Store ID", "actions.survey_retrieve.description": "Retrieve a survey by ID", "actions.survey_retrieve.displayName": "Retrieve Survey", "actions.survey_retrieve.properties.surveyId.description": "ID of the survey", "actions.survey_retrieve.properties.surveyId.displayName": "Survey ID", "description": "Interact with VirtualStore API to manage reservations, stores, and hosts", "displayName": "VirtualStore", "schemas.answer.description": "Survey answer", "schemas.answer.displayName": "Answer", "schemas.answer.properties.answer.description": "The question's response", "schemas.answer.properties.answer.displayName": "Answer", "schemas.answer.properties.assets.description": "Assets", "schemas.answer.properties.assets.displayName": "Assets", "schemas.answer.properties.question_id.description": "The question ID", "schemas.answer.properties.question_id.displayName": "Question ID", "schemas.answer.properties.response_id.description": "Response ID", "schemas.answer.properties.response_id.displayName": "Response ID", "schemas.answer.properties.response_resource_id.description": "Response resource id", "schemas.answer.properties.response_resource_id.displayName": "Response Resource ID", "schemas.answer.properties.response_resource_type.description": "Response resource type", "schemas.answer.properties.response_resource_type.displayName": "Response Resource Type", "schemas.answer.properties.survey_id.description": "Survey ID", "schemas.answer.properties.survey_id.displayName": "Survey ID", "schemas.asset.properties.created_at.description": "Created at datetime", "schemas.asset.properties.created_at.displayName": "Created At", "schemas.asset.properties.id.description": "Asset id", "schemas.asset.properties.id.displayName": "ID", "schemas.asset.properties.index.description": "Index of asset in the list", "schemas.asset.properties.index.displayName": "Index", "schemas.asset.properties.mime_type.description": "MIME Type", "schemas.asset.properties.mime_type.displayName": "MIME Type", "schemas.asset.properties.name.description": "File name", "schemas.asset.properties.name.displayName": "Name", "schemas.asset.properties.path.description": "URL of file", "schemas.asset.properties.path.displayName": "Path", "schemas.asset.properties.resource_id.description": "Resource Id", "schemas.asset.properties.resource_id.displayName": "Resource ID", "schemas.asset.properties.resource_type.description": "One of resource type defined in settings SUPPORTED_ASSET_RESOURCE_TYPE", "schemas.asset.properties.resource_type.displayName": "Resource Type", "schemas.asset.properties.size.description": "Size of file", "schemas.asset.properties.size.displayName": "Size", "schemas.attendee.description": "Attendee information", "schemas.attendee.displayName": "<PERSON><PERSON><PERSON>", "schemas.attendee.properties.external_user_id.description": "External user ID", "schemas.attendee.properties.external_user_id.displayName": "External User ID", "schemas.attendee.properties.id.description": "Attendee ID", "schemas.attendee.properties.id.displayName": "ID", "schemas.attendee.properties.join_token.description": "Join token", "schemas.attendee.properties.join_token.displayName": "Join <PERSON>", "schemas.common.properties.created_at.description": "Timestamp when the item was created", "schemas.common.properties.created_at.displayName": "Created At", "schemas.common.properties.id.description": "Unique identifier", "schemas.common.properties.id.displayName": "ID", "schemas.common.properties.updated_at.description": "Timestamp when the item was last updated", "schemas.common.properties.updated_at.displayName": "Updated At", "schemas.day.description": "Day", "schemas.day.displayName": "Day", "schemas.day.properties.date.description": "Date", "schemas.day.properties.date.displayName": "Date", "schemas.day.properties.hosts.description": "Hosts", "schemas.day.properties.hosts.displayName": "Hosts", "schemas.day.properties.hosts.items.properties.id.description": "Host ID", "schemas.day.properties.hosts.items.properties.id.displayName": "ID", "schemas.day.properties.hosts.items.properties.name.description": "Host name", "schemas.day.properties.hosts.items.properties.name.displayName": "Name", "schemas.day.properties.hosts.items.properties.slots.description": "Slots", "schemas.day.properties.hosts.items.properties.slots.displayName": "Slots", "schemas.day.properties.hosts.items.properties.slots.items.properties.duration.description": "Duration", "schemas.day.properties.hosts.items.properties.slots.items.properties.duration.displayName": "Duration", "schemas.day.properties.hosts.items.properties.slots.items.properties.start_time.description": "Start time", "schemas.day.properties.hosts.items.properties.slots.items.properties.start_time.displayName": "Start Time", "schemas.day.properties.hosts.items.properties.status.description": "Host status", "schemas.day.properties.hosts.items.properties.status.displayName": "Status", "schemas.day.properties.is_holiday.description": "Is holiday", "schemas.day.properties.is_holiday.displayName": "Is Holiday", "schemas.day.properties.status.description": "Status", "schemas.day.properties.status.displayName": "Status", "schemas.day_slot.description": "Day slot", "schemas.day_slot.displayName": "Day Slot", "schemas.day_slot.properties.days.description": "Days", "schemas.day_slot.properties.days.displayName": "Days", "schemas.day_slot.properties.today.description": "Today", "schemas.day_slot.properties.today.displayName": "Today", "schemas.direct_session.description": "Direct session", "schemas.direct_session.displayName": "Direct Session", "schemas.direct_session.properties.created_at.description": "Created at datetime", "schemas.direct_session.properties.created_at.displayName": "Created At", "schemas.direct_session.properties.end_at.description": "End at datetime", "schemas.direct_session.properties.end_at.displayName": "End At", "schemas.direct_session.properties.guests.description": "List of guests", "schemas.direct_session.properties.guests.displayName": "Guests", "schemas.direct_session.properties.hash_id.description": "Hashed of id", "schemas.direct_session.properties.hash_id.displayName": "Hash ID", "schemas.direct_session.properties.host.description": "Host information", "schemas.direct_session.properties.host.displayName": "Host", "schemas.direct_session.properties.host_id.description": "The host ID", "schemas.direct_session.properties.host_id.displayName": "Host ID", "schemas.direct_session.properties.id.description": "The direct session ID", "schemas.direct_session.properties.id.displayName": "ID", "schemas.direct_session.properties.is_blocked_by.description": "This direct session is blocked by the host", "schemas.direct_session.properties.is_blocked_by.displayName": "Is Blocked By", "schemas.direct_session.properties.is_private.description": "This direct session is booked from private host URL", "schemas.direct_session.properties.is_private.displayName": "Is Private", "schemas.direct_session.properties.is_recorded.description": "Determine the direct session is recorder or not", "schemas.direct_session.properties.is_recorded.displayName": "Is Recorded", "schemas.direct_session.properties.is_test.description": "Flag this direct session is a test", "schemas.direct_session.properties.is_test.displayName": "Is Test", "schemas.direct_session.properties.num_by_guest.description": "Show the number of direct session via the guest email", "schemas.direct_session.properties.num_by_guest.displayName": "Number By Guest", "schemas.direct_session.properties.responses.description": "List of responses", "schemas.direct_session.properties.responses.displayName": "Responses", "schemas.direct_session.properties.start_at.description": "Start at datetime", "schemas.direct_session.properties.start_at.displayName": "Start At", "schemas.direct_session.properties.status.description": "The direct session status", "schemas.direct_session.properties.status.displayName": "Status", "schemas.direct_session.properties.updated_at.description": "Updated at datetime", "schemas.direct_session.properties.updated_at.displayName": "Updated At", "schemas.direct_session.properties.url_type.description": "URL source that user creates direct session from", "schemas.direct_session.properties.url_type.displayName": "URL Type", "schemas.direct_session.properties.utm_source.description": "UTM source", "schemas.direct_session.properties.utm_source.displayName": "UTM Source", "schemas.duration.properties.by_host_survey_id.description": "The survey ID (by host)", "schemas.duration.properties.by_host_survey_id.displayName": "By Host Survey ID", "schemas.duration.properties.created_at.description": "Created at datetime", "schemas.duration.properties.created_at.displayName": "Created At", "schemas.duration.properties.description.description": "Duration description", "schemas.duration.properties.description.displayName": "Description", "schemas.duration.properties.duration.description": "The duration in minutes", "schemas.duration.properties.duration.displayName": "Duration", "schemas.duration.properties.id.description": "Unique identifier for the duration", "schemas.duration.properties.id.displayName": "ID", "schemas.duration.properties.image.description": "Duration image", "schemas.duration.properties.image.displayName": "Image", "schemas.duration.properties.image_id.description": "The duration image ID", "schemas.duration.properties.image_id.displayName": "Image ID", "schemas.duration.properties.name.description": "The duration name", "schemas.duration.properties.name.displayName": "Name", "schemas.duration.properties.reception.description": "Reception time setting", "schemas.duration.properties.reception.displayName": "Reception", "schemas.duration.properties.reception.properties.enabled.description": "Enable/Disable reception time for duration", "schemas.duration.properties.reception.properties.enabled.displayName": "Enabled", "schemas.duration.properties.reception.properties.from_date.description": "The from-date for booking a reservation", "schemas.duration.properties.reception.properties.from_date.displayName": "From Date", "schemas.duration.properties.reception.properties.limit_day.description": "Reservation limit in day", "schemas.duration.properties.reception.properties.limit_day.displayName": "Limit Day", "schemas.duration.properties.reception.properties.limit_hour.description": "Reservation limit in hour", "schemas.duration.properties.reception.properties.limit_hour.displayName": "Limit Hour", "schemas.duration.properties.reception.properties.offset_day.description": "Reservation offset in day", "schemas.duration.properties.reception.properties.offset_day.displayName": "Offset Day", "schemas.duration.properties.reception.properties.offset_hour.description": "Reservation offset in hour", "schemas.duration.properties.reception.properties.offset_hour.displayName": "Offset Hour", "schemas.duration.properties.reception.properties.to_date.description": "The to-date for booking a reservation", "schemas.duration.properties.reception.properties.to_date.displayName": "To Date", "schemas.duration.properties.survey_id.description": "The survey ID", "schemas.duration.properties.survey_id.displayName": "Survey ID", "schemas.duration.properties.type.description": "The duration type", "schemas.duration.properties.type.displayName": "Type", "schemas.duration.properties.updated_at.description": "Updated at datetime", "schemas.duration.properties.updated_at.displayName": "Updated At", "schemas.guest.properties.email.description": "Guest email", "schemas.guest.properties.email.displayName": "Email", "schemas.guest.properties.external_id.description": "Guest external id", "schemas.guest.properties.external_id.displayName": "External ID", "schemas.guest.properties.family_furigana.description": "Family furigana format", "schemas.guest.properties.family_furigana.displayName": "Family Furigana", "schemas.guest.properties.family_katakana.description": "Family katakana format", "schemas.guest.properties.family_katakana.displayName": "Family Katakana", "schemas.guest.properties.family_name.description": "Family name of guest", "schemas.guest.properties.family_name.displayName": "Family Name", "schemas.guest.properties.first_furigana.description": "First furigana format", "schemas.guest.properties.first_furigana.displayName": "First Furigana", "schemas.guest.properties.first_katakana.description": "First katakana format", "schemas.guest.properties.first_katakana.displayName": "First Katakana", "schemas.guest.properties.first_name.description": "First name of guest", "schemas.guest.properties.first_name.displayName": "First Name", "schemas.guest.properties.hash_id.description": "Guest hashed id", "schemas.guest.properties.hash_id.displayName": "Hash ID", "schemas.guest.properties.id.description": "Unique identifier for the guest", "schemas.guest.properties.id.displayName": "ID", "schemas.guest.properties.is_survey_enabled.description": "Flag to enable survey", "schemas.guest.properties.is_survey_enabled.displayName": "Is Survey Enabled", "schemas.guest.properties.line.description": "LINE profile", "schemas.guest.properties.line.displayName": "LINE Profile", "schemas.guest.properties.line.properties.avatar.description": "The LINE avatar", "schemas.guest.properties.line.properties.avatar.displayName": "Avatar", "schemas.guest.properties.line.properties.id.description": "Internal ID for LINE profile", "schemas.guest.properties.line.properties.id.displayName": "ID", "schemas.guest.properties.line.properties.name.description": "The LINE name of guest", "schemas.guest.properties.line.properties.name.displayName": "Name", "schemas.guest.properties.line.properties.user_id.description": "External ID for LINE profile (LINE UID)", "schemas.guest.properties.line.properties.user_id.displayName": "User ID", "schemas.guest.properties.note.description": "Guest note", "schemas.guest.properties.note.displayName": "Note", "schemas.guest.properties.phone_number.description": "Guest's phone number", "schemas.guest.properties.phone_number.displayName": "Phone Number", "schemas.guest.properties.resource_id.description": "The resource id", "schemas.guest.properties.resource_id.displayName": "Resource ID", "schemas.guest.properties.resource_type.description": "The resource type", "schemas.guest.properties.resource_type.displayName": "Resource Type", "schemas.guest.properties.status.description": "The reservation status", "schemas.guest.properties.status.displayName": "Status", "schemas.guest_with_hash.description": "Guest with hash information", "schemas.guest_with_hash.displayName": "Guest With <PERSON><PERSON>", "schemas.guest_with_hash.properties.attendee.description": "Attendee information", "schemas.guest_with_hash.properties.attendee.displayName": "<PERSON><PERSON><PERSON>", "schemas.guest_with_hash.properties.email.description": "Guest email", "schemas.guest_with_hash.properties.email.displayName": "Email", "schemas.guest_with_hash.properties.external_id.description": "Guest external id", "schemas.guest_with_hash.properties.external_id.displayName": "External ID", "schemas.guest_with_hash.properties.family_furigana.description": "The family name of furigana format", "schemas.guest_with_hash.properties.family_furigana.displayName": "Family Furigana", "schemas.guest_with_hash.properties.family_katakana.description": "The family name of katakana format", "schemas.guest_with_hash.properties.family_katakana.displayName": "Family Katakana", "schemas.guest_with_hash.properties.family_name.description": "The family name of guest", "schemas.guest_with_hash.properties.family_name.displayName": "Family Name", "schemas.guest_with_hash.properties.first_furigana.description": "The first name of furigana format", "schemas.guest_with_hash.properties.first_furigana.displayName": "First Furigana", "schemas.guest_with_hash.properties.first_katakana.description": "The first name of katakana format", "schemas.guest_with_hash.properties.first_katakana.displayName": "First Katakana", "schemas.guest_with_hash.properties.first_name.description": "The first name of guest", "schemas.guest_with_hash.properties.first_name.displayName": "First Name", "schemas.guest_with_hash.properties.full_name.description": "Full name of guest", "schemas.guest_with_hash.properties.full_name.displayName": "Full Name", "schemas.guest_with_hash.properties.hash_id.description": "Hashed of id", "schemas.guest_with_hash.properties.hash_id.displayName": "Hash ID", "schemas.guest_with_hash.properties.id.description": "Guest id", "schemas.guest_with_hash.properties.id.displayName": "ID", "schemas.guest_with_hash.properties.identity.description": "Guest identity", "schemas.guest_with_hash.properties.identity.displayName": "Identity", "schemas.guest_with_hash.properties.is_booker.description": "Guest is booker", "schemas.guest_with_hash.properties.is_booker.displayName": "<PERSON>", "schemas.guest_with_hash.properties.is_survey_enabled.description": "Flag to enable survey", "schemas.guest_with_hash.properties.is_survey_enabled.displayName": "Is Survey Enabled", "schemas.guest_with_hash.properties.phone_number.description": "Guest's phone number", "schemas.guest_with_hash.properties.phone_number.displayName": "Phone Number", "schemas.guest_with_hash.properties.visitor_id.description": "Visitor id", "schemas.guest_with_hash.properties.visitor_id.displayName": "Visitor ID", "schemas.guest_with_hash.properties.web_id.description": "Guest web id", "schemas.guest_with_hash.properties.web_id.displayName": "Web ID", "schemas.host.properties.display_name.description": "Display name of the host", "schemas.host.properties.display_name.displayName": "Display Name", "schemas.host.properties.email.description": "Email address of the host", "schemas.host.properties.email.displayName": "Email", "schemas.host.properties.full_name.description": "Full name of the host", "schemas.host.properties.full_name.displayName": "Full Name", "schemas.host.properties.id.description": "Unique identifier for the host", "schemas.host.properties.id.displayName": "ID", "schemas.host.properties.number_of_onhold_sessions.description": "Number of sessions on hold", "schemas.host.properties.number_of_onhold_sessions.displayName": "Number of On-hold Sessions", "schemas.host.properties.number_of_scheduled_sessions.description": "Number of scheduled sessions", "schemas.host.properties.number_of_scheduled_sessions.displayName": "Number of Scheduled Sessions", "schemas.host.properties.profile.description": "Host profile information", "schemas.host.properties.profile.displayName": "Profile", "schemas.host.properties.profile.properties.introduction.description": "Host introduction", "schemas.host.properties.profile.properties.introduction.displayName": "Introduction", "schemas.host.properties.profile.properties.nickname.description": "Host nickname", "schemas.host.properties.profile.properties.nickname.displayName": "Nickname", "schemas.host.properties.profile.properties.position.description": "Host position", "schemas.host.properties.profile.properties.position.displayName": "Position", "schemas.host.properties.role.description": "Role of the host", "schemas.host.properties.role.displayName": "Role", "schemas.option.description": "Question option", "schemas.option.displayName": "Option", "schemas.option.properties.asset_id.description": "Asset for image option", "schemas.option.properties.asset_id.displayName": "Asset ID", "schemas.option.properties.order.description": "Order for the satisfaction question", "schemas.option.properties.order.displayName": "Order", "schemas.option.properties.value.description": "Option value", "schemas.option.properties.value.displayName": "Value", "schemas.pagination.properties.count.description": "Total number of items", "schemas.pagination.properties.count.displayName": "Count", "schemas.pagination.properties.num_pages.description": "Total number of pages", "schemas.pagination.properties.num_pages.displayName": "Number of Pages", "schemas.pagination.properties.page.description": "Current page number", "schemas.pagination.properties.page.displayName": "Page", "schemas.pagination.properties.per_page.description": "Number of items per page", "schemas.pagination.properties.per_page.displayName": "Per Page", "schemas.question.description": "Survey question", "schemas.question.displayName": "Question", "schemas.question.properties.columns.description": "The column's number for checkbox|radio", "schemas.question.properties.columns.displayName": "Columns", "schemas.question.properties.created_at.description": "Created at datetime", "schemas.question.properties.created_at.displayName": "Created At", "schemas.question.properties.explanation.description": "Question explanation", "schemas.question.properties.explanation.displayName": "Explanation", "schemas.question.properties.has_other.description": "The other option flag for options", "schemas.question.properties.has_other.displayName": "Has Other", "schemas.question.properties.id.description": "The question ID", "schemas.question.properties.id.displayName": "ID", "schemas.question.properties.is_image_option.description": "Flag this question contains image in options", "schemas.question.properties.is_image_option.displayName": "Is Image Option", "schemas.question.properties.is_satisfaction.description": "The question is static satisfaction question or not", "schemas.question.properties.is_satisfaction.displayName": "Is Satisfaction", "schemas.question.properties.options.description": "The options for radio|checkbox|dropdown|linear_scale", "schemas.question.properties.options.displayName": "Options", "schemas.question.properties.order.description": "The question order", "schemas.question.properties.order.displayName": "Order", "schemas.question.properties.placeholder.description": "The placeholder for input|textarea", "schemas.question.properties.placeholder.displayName": "Placeholder", "schemas.question.properties.question.description": "The question content", "schemas.question.properties.question.displayName": "Question", "schemas.question.properties.type.description": "The question type", "schemas.question.properties.type.displayName": "Type", "schemas.question.properties.updated_at.description": "Updated at datetime", "schemas.question.properties.updated_at.displayName": "Updated At", "schemas.question.properties.validations.description": "The input validation", "schemas.question.properties.validations.displayName": "Validations", "schemas.reservation.properties.duration.description": "Duration information", "schemas.reservation.properties.duration.displayName": "Duration", "schemas.reservation.properties.end_at.description": "End time of the reservation", "schemas.reservation.properties.end_at.displayName": "End At", "schemas.reservation.properties.equipment.description": "Equipment information", "schemas.reservation.properties.equipment.displayName": "Equipment", "schemas.reservation.properties.equipment_id.description": "ID of the equipment", "schemas.reservation.properties.equipment_id.displayName": "Equipment ID", "schemas.reservation.properties.guests.description": "List of guests in the reservation", "schemas.reservation.properties.guests.displayName": "Guests", "schemas.reservation.properties.host.description": "Host information", "schemas.reservation.properties.host.displayName": "Host", "schemas.reservation.properties.host_id.description": "ID of the host", "schemas.reservation.properties.host_id.displayName": "Host ID", "schemas.reservation.properties.id.description": "Unique identifier for the reservation", "schemas.reservation.properties.id.displayName": "ID", "schemas.reservation.properties.is_private.description": "Whether the reservation is private", "schemas.reservation.properties.is_private.displayName": "Is Private", "schemas.reservation.properties.num_by_guest.description": "Number of guests in the reservation", "schemas.reservation.properties.num_by_guest.displayName": "Number of Guests", "schemas.reservation.properties.start_at.description": "Start time of the reservation", "schemas.reservation.properties.start_at.displayName": "Start At", "schemas.reservation.properties.status.description": "Status of the reservation", "schemas.reservation.properties.status.displayName": "Status", "schemas.reservation.properties.type.description": "Type of the reservation", "schemas.reservation.properties.type.displayName": "Type", "schemas.response.description": "Survey response", "schemas.response.displayName": "Response", "schemas.response.properties.answers.description": "The answer list", "schemas.response.properties.answers.displayName": "Answers", "schemas.response.properties.created_at.description": "Created at datetime", "schemas.response.properties.created_at.displayName": "Created At", "schemas.response.properties.resource_id.description": "Resource id", "schemas.response.properties.resource_id.displayName": "Resource ID", "schemas.response.properties.resource_type.description": "Resource type", "schemas.response.properties.resource_type.displayName": "Resource Type", "schemas.response.properties.survey_id.description": "Survey id", "schemas.response.properties.survey_id.displayName": "Survey ID", "schemas.response.properties.updated_at.description": "Updated at datetime", "schemas.response.properties.updated_at.displayName": "Updated At", "schemas.store.properties.address.description": "Store address information", "schemas.store.properties.address.displayName": "Address", "schemas.store.properties.address.properties.address.description": "Full address", "schemas.store.properties.address.properties.address.displayName": "Address", "schemas.store.properties.address.properties.latitude.description": "Latitude coordinate", "schemas.store.properties.address.properties.latitude.displayName": "Latitude", "schemas.store.properties.address.properties.longitude.description": "Longitude coordinate", "schemas.store.properties.address.properties.longitude.displayName": "Longitude", "schemas.store.properties.hash_id.description": "Hash identifier for the store", "schemas.store.properties.hash_id.displayName": "Hash ID", "schemas.store.properties.id.description": "Unique identifier for the store", "schemas.store.properties.id.displayName": "ID", "schemas.store.properties.image.description": "Store image information", "schemas.store.properties.image.displayName": "Image", "schemas.store.properties.logo.description": "Store logo information", "schemas.store.properties.logo.displayName": "Logo", "schemas.store.properties.name.description": "Name of the store", "schemas.store.properties.name.displayName": "Name", "schemas.store.properties.reservation_url.description": "URL for making reservations", "schemas.store.properties.reservation_url.displayName": "Reservation URL", "schemas.store.properties.settings.description": "Store settings", "schemas.store.properties.settings.displayName": "Settings", "schemas.store.properties.time_zone.description": "Store time zone", "schemas.store.properties.time_zone.displayName": "Time Zone", "schemas.store.properties.working_days.description": "Working days configuration", "schemas.store.properties.working_days.displayName": "Working Days", "schemas.survey.description": "Survey", "schemas.survey.displayName": "Survey", "schemas.survey.properties.created_at.description": "Created at datetime", "schemas.survey.properties.created_at.displayName": "Created At", "schemas.survey.properties.description.description": "The survey description", "schemas.survey.properties.description.displayName": "Description", "schemas.survey.properties.enabled_for.description": "The flag to enable or disable", "schemas.survey.properties.enabled_for.displayName": "Enabled For", "schemas.survey.properties.id.description": "Survey ID", "schemas.survey.properties.id.displayName": "ID", "schemas.survey.properties.is_editable.description": "The survey is editable", "schemas.survey.properties.is_editable.displayName": "Is Editable", "schemas.survey.properties.is_survey.description": "The survey flag", "schemas.survey.properties.is_survey.displayName": "Is Survey", "schemas.survey.properties.questions.description": "The survey question list", "schemas.survey.properties.questions.displayName": "Questions", "schemas.survey.properties.resource_type.description": "The survey resource type", "schemas.survey.properties.resource_type.displayName": "Resource Type", "schemas.survey.properties.title.description": "The survey title", "schemas.survey.properties.title.displayName": "Title", "schemas.survey.properties.updated_at.description": "Updated at datetime", "schemas.survey.properties.updated_at.displayName": "Updated At", "schemas.time_slot.description": "Time slot", "schemas.time_slot.displayName": "Time Slot", "schemas.time_slot.properties.description": "Array of time slots with start time and duration", "schemas.time_slot.properties.items.properties.duration.description": "Duration", "schemas.time_slot.properties.items.properties.duration.displayName": "Duration", "schemas.time_slot.properties.items.properties.start_time.description": "Start time", "schemas.time_slot.properties.items.properties.start_time.displayName": "Start Time"}, "wait": {"actions.waitFor.description": "Pause execution for a specified duration", "actions.waitFor.displayName": "Wait For", "actions.waitFor.properties.duration.description": "How long to wait", "actions.waitFor.properties.duration.displayName": "Duration", "actions.waitFor.properties.unit.description": "The unit of time for the duration", "actions.waitFor.properties.unit.displayName": "Time Unit", "actions.waitUntil.description": "Pause execution until a specific date and time", "actions.waitUntil.displayName": "Wait Until", "actions.waitUntil.properties.dateTime.description": "The date and time to wait until (ISO format)", "actions.waitUntil.properties.dateTime.displayName": "Date and Time", "actions.waitUntil.properties.pastDateHandling.description": "What to do if the target date/time is in the past", "actions.waitUntil.properties.pastDateHandling.displayName": "How should we handle dates in the past?", "actions.waitUntil.properties.relativeTime.description": "Time from now (only used if Use Relative Time is checked)", "actions.waitUntil.properties.relativeTime.displayName": "Relative Time", "actions.waitUntil.properties.relativeUnit.description": "The unit for relative time", "actions.waitUntil.properties.relativeUnit.displayName": "Relative Time Unit", "actions.waitUntil.properties.useRelative.description": "Use a relative time instead of absolute date/time", "actions.waitUntil.properties.useRelative.displayName": "Use Relative Time", "description": "Pause workflow execution for a specified duration, until a specific time, or until an event occurs", "displayName": "Wait", "schemas.waitForData.properties.endTime.description": "The ISO timestamp when the wait ended", "schemas.waitForData.properties.endTime.displayName": "End Time", "schemas.waitForData.properties.startTime.description": "The ISO timestamp when the wait started", "schemas.waitForData.properties.startTime.displayName": "Start Time", "schemas.waitForData.properties.waitDuration.description": "The actual duration waited in milliseconds", "schemas.waitForData.properties.waitDuration.displayName": "Wait Duration", "schemas.waitUntilData.properties.endTime.description": "The ISO timestamp when the wait ended", "schemas.waitUntilData.properties.endTime.displayName": "End Time", "schemas.waitUntilData.properties.startTime.description": "The ISO timestamp when the wait started", "schemas.waitUntilData.properties.startTime.displayName": "Start Time", "schemas.waitUntilData.properties.targetTime.description": "The ISO timestamp of the target time waited for", "schemas.waitUntilData.properties.targetTime.displayName": "Target Time", "schemas.waitUntilData.properties.waitDuration.description": "The actual duration waited in milliseconds", "schemas.waitUntilData.properties.waitDuration.displayName": "Wait Duration", "schemas.waitUntilData.properties.wasInPast.description": "Whether the target time was in the past when executed", "schemas.waitUntilData.properties.wasInPast.displayName": "Was In Past", "settings.timezone.description": "The timezone to use for the wait", "settings.timezone.displayName": "Timezone"}, "webhook": {"description": "Trigger from a request POST to a DECA webhook url", "displayName": "Webhook", "schemas.webhook.trigger.hookUrl.data.properties.apiKey.description": "", "schemas.webhook.trigger.hookUrl.data.properties.url.description": "", "triggers.webhook.trigger.description": "", "triggers.webhook.trigger.displayName": "", "trigger": "", "triggers.webhook.trigger.properties.urlGroup.description": "", "triggers.webhook.trigger.properties.urlGroup.displayName": "", "triggers.webhook.trigger.properties.urlGroup.properties.hookApiKey.description": "", "triggers.webhook.trigger.properties.urlGroup.properties.hookApiKey.displayName": "", "triggers.webhook.trigger.properties.urlGroup.properties.hookUrl.description": "", "triggers.webhook.trigger.properties.urlGroup.properties.hookUrl.displayName": ""}, "zendesk-ticket": {"actions.createTicket.description": "Create a new Zendesk ticket", "actions.createTicket.displayName": "Create Ticket", "actions.createTicket.properties.ticket.properties.description.description": "The ticket description", "actions.createTicket.properties.ticket.properties.description.displayName": "Description", "actions.createTicket.properties.ticket.properties.priority.description": "The ticket priority", "actions.createTicket.properties.ticket.properties.priority.displayName": "Priority", "actions.createTicket.properties.ticket.properties.requester.description": "The email of the recipient, if not provided, the requester will be the user who is logged in", "actions.createTicket.properties.ticket.properties.requester.displayName": "Requester", "actions.createTicket.properties.ticket.properties.subject.description": "The ticket subject", "actions.createTicket.properties.ticket.properties.subject.displayName": "Subject", "actions.createTicket.properties.ticket.properties.tags.description": "Tags to associate with the ticket", "actions.createTicket.properties.ticket.properties.tags.displayName": "Tags", "actions.createTicket.properties.ticket.properties.ticketType.description": "The type of ticket", "actions.createTicket.properties.ticket.properties.ticketType.displayName": "Type", "credentials.api_key.description": "API Key authentication for Zendesk API", "credentials.api_key.displayName": "API Key", "credentials.api_key.properties.apiKey.description": "Your Zendesk API key", "credentials.api_key.properties.apiKey.displayName": "API Key", "credentials.api_key.properties.email.description": "Your Zendesk account email", "credentials.api_key.properties.email.displayName": "Email", "credentials.api_key.properties.subdomain.description": "Your Zendesk subdomain", "credentials.api_key.properties.subdomain.displayName": "Subdomain", "description": "Interact with Zendesk tickets", "displayName": "Zendesk Ticket", "schemas.zendeskTicket.properties.createdAt.description": "When the ticket was created", "schemas.zendeskTicket.properties.createdAt.displayName": "Created At", "schemas.zendeskTicket.properties.description.description": "The ticket description", "schemas.zendeskTicket.properties.description.displayName": "Description", "schemas.zendeskTicket.properties.id.description": "The unique identifier for the ticket", "schemas.zendeskTicket.properties.id.displayName": "Ticket ID", "schemas.zendeskTicket.properties.priority.description": "The ticket priority level", "schemas.zendeskTicket.properties.priority.displayName": "Priority", "schemas.zendeskTicket.properties.status.description": "The current status of the ticket", "schemas.zendeskTicket.properties.status.displayName": "Status", "schemas.zendeskTicket.properties.subject.description": "The ticket subject", "schemas.zendeskTicket.properties.subject.displayName": "Subject", "schemas.zendeskTicket.properties.type.description": "The type of ticket", "schemas.zendeskTicket.properties.type.displayName": "Type", "schemas.zendeskTicket.properties.updatedAt.description": "When the ticket was last updated", "schemas.zendeskTicket.properties.updatedAt.displayName": "Updated At"}}