{"addNew": "Add new", "table": "Table", "object": "Object", "rename": "<PERSON><PERSON>", "duplicate": "Duplicate", "clearData": "Clear data", "clearView": "Clear view", "deleteView": "Delete View", "lock": "Lock", "locked": "Locked", "unlocked": "Unlocked", "unLock": "Unlock", "addAbove": "Insert <input /> above", "addBelow": "Insert <input /> below", "selectRow": "Select row", "deselectRow": "Deselect row", "duplicateRow": "Duplicate", "editProperty": "Edit property", "duplicateField": "Duplicate field", "hideField": "Hide field", "insertLeft": "<PERSON><PERSON><PERSON> left", "insertRight": "Insert right", "freezeField": "Freeze up to this field", "sortFirst": "Sort first to last", "sortLast": "Sort last to first", "groupBy": "Group by", "filterBy": "Filter by", "createKanban": "Create <PERSON><PERSON><PERSON> with", "defaultView": "<PERSON><PERSON><PERSON>", "deleteColumn": "Delete column", "delete": "Delete", "deleteRow": "Delete row", "deleteRowWarning": "Are you sure you want to delete {{rows}} {{objectName}} records?", "deleteColumnWarning": "Are you sure you want to delete this \"{{column}}\" column?", "clearViewWarning": "Are you sure you want to clear this \"{{view}}\" view?", "deleteViewWarning": "Are you sure you want to delete \"{{view}}\" view?", "add": "Add Row", "addView": "Add View", "noColumnHasBeenAddedYet": "No column has been added yet.", "rowHeight": "Row Height", "short": "Short", "medium": "Medium", "filter": "Filter", "sort": "Sort", "customizeFields": "Customize Fields", "view": "View", "createNewView": "Create as a view", "resultsFound": "results found", "fieldName": "Field name", "fieldType": "Field type", "cancel": "Cancel", "apply": "Apply", "save": "Save", "create": "Create", "automaticSorting": "Automatic Sorting", "typeToSearch": "Type to search", "search": "Search", "and": "and", "fields": {"meta": {"singleLineText": "Single Line Text", "longText": "Long Text", "phone": "Phone", "email": "Email", "url": "URL", "createdBy": "Created By", "modifiedBy": "Modified By", "createdTime": "Created Time", "modifiedTime": "Modified Time", "dateTime": "Date and Time", "image": "Image", "singleSelect": "Single Select", "multipleSelect": "Multiple Select", "checkbox": "Checkbox", "number": "Number", "autonumber": "Autonumber", "currency": "<PERSON><PERSON><PERSON><PERSON>", "percent": "Percent", "array": "Array", "relationship": "Link to another record", "lookup": "Lookup"}, "description": {"longText": "Enter multiple lines of text", "phone": "Enter a telephone number", "email": "Enter an email address", "url": "Enter a URL", "createdBy": "See which user created the record.", "modifiedBy": "See which user made the most recent edit to some or all fields in a record.", "createdTime": "See the date and time each record was created.", "modifiedTime": "See the date and time each record was created.", "dateTime": "Enter a date or choose one from a calendar.", "image": "Add images to be viewed or downloaded.", "singleSelect": "Select one predefined option from a list, or prefill each new cell with a default option.", "multipleSelect": "Select one or more predefined options in a list.", "checkbox": "Check or uncheck to indicate status.", "number": "Enter a number, or prefill each new cell with a default value.", "autonumber": "Automatically generate unique incremental numbers for each record.", "currency": "Enter a monetary amount, or prefill each new cell with a default value.", "percent": "Enter a percentage, or prefill each new cell with a default value.", "array": "A field for arrays", "lookup": "A field for lookups", "relationship": "A field for relationships"}, "label": {"name": "Name"}, "dateFormat": "Date Format", "jaFormat": "Japan : 2024/06/13", "usFormat": "United States: 06/13/2024", "longDate": "Long Date (June 13, 2024)", "euFormat": "European: 13/06/2024", "isoFormat": "ISO: YYYY-MM-DD", "includeTime": "Include time", "sameTimezone": "Use the same time zone for all collaborators", "displayTimezone": "Display timezone", "defaultCurrentDate": "Default to current date", "timeZone": "Time Zone", "format": "Format", "japan": "Japan", "timeZoneSearch": "Find a locale or timezone", "international": "International", "northUS": "North America", "general": "General", "suggestTimeZone": "SUGGESTED TIME ZONES", "allTimeZones": "ALL TIME ZONES", "numberFormat": "Number Format", "decimal": "Decimal", "integer": "Integer", "decimalPlaces": "Decimal Places", "usd": "USD - $ (US Dollar)", "yen": "YEN - ¥ (Japan Yen)", "currency": "<PERSON><PERSON><PERSON><PERSON>", "showThousandsSeparator": "Show thousands separator", "thousandAndDecimalSeparator": "Thousands separators", "numberWithCommas": "Number with commas", "space": "Space", "ring": "Ring", "bar": "Bar", "displayAsAProgressBar": "Display as progress bar", "linkTo": "Link to", "select": "Select", "search": "Search", "showAs": "Show as", "addOption": "Add option", "colorCode": "Color-code", "numberValidateErr": "Please enter a number", "defaultOption": "Default Option", "manual": "Manual", "searchOrAddAnOpt": "Search or add an option..", "selectOrAddAnOpt": "Select or add an option", "done": "Done", "upload": "Upload", "chooseImage": "Choose an image", "maximumSize": "The maximum size is 5MB", "image": "Image", "changePicture": "Change Picture", "view": "View Original", "download": "Download", "delete": "Delete", "loading": "Loading", "uploadHere": "Upload here", "previewImage": "Preview Image", "timeFormat": "Time format", "24h": "24 hours (15:32)", "12h": "12 hours (3:32pm)"}, "errors": {"validations": {"invalidPhonePattern": "Input data is not following phone number pattern", "number": "Please enter a number", "integer": "Please enter an integer number", "decimalPlaces": "Please enter a valid number with {{places}} decimal places", "percentage": "Please enter a valid number between 0 and 100", "email": "Please enter a valid email address", "url": "Please enter a valid URL"}, "fields": {"duplicateOptionName": "This name already exists.", "nameRequired": "Please enter a name.", "maxLengthError": "Please enter less than 40 characters."}}, "unlockView": "Unlock View", "unlockViewDesc": "Are you sure to unlock the view?", "viewLocked": "\"{{name}}\" view locked", "viewUnlocked": "\"{{name}}\" view unlocked", "viewDuplicated": "\"{{name}}\" view duplicated", "tags": "Tags", "selectValue": "Select a value", "find": "Find", "noFieldsToCustomize": "There is no fields to customize", "exportData": "Export", "goToTrash": "Go to Trash", "deleteCustomFieldTitle": "Delete Field", "deleteCustomFieldDescription": "Are you sure you want to delete \"{{field}}\" field ?", "exportCSV": "Export CSV", "exportInfo": "The processed CSV file will be sent to your email shortly.", "downloadCSV": "Download CSV File", "downloadAWhile": "This process could take a while.", "confirm": "Confirm", "findView": "Find View", "manageView": "Manage View", "addNewView": "Add New View", "moveTo": "Move to", "markAsDefault": "Mark as default", "manageViewDesc": "Customize your views: Create folders, reorder to organize your view.", "addGroupView": "Add Group View", "searchView": "Search View", "noViews": "No view inside this folder.", "ungroup": "Ungrouping will remove this folder. \nAll views will be ungrouped and returned to the default list.", "requiredField": "Group name cannot be empty", "duplicateGroupName": "A folder with this name already exists. Please change to another name. ", "noResult": "No result found", "noResultDesc": "We can’t find any item matching your search", "automaticSortingTooltip": "Sorting will be applied after closing the modal", "lastUpdated": "Last updated", "recordsInView": "records in this view", "enableRecordCounter": "Show record count for each view", "enableRecordCounterDesc": "Counts are updated hourly or when the view is selected.", "noViewPermission": "Setting Access Denied", "viewsAndGroups": "Customize views and groups", "addRecord": "Add Record", "searchResult": "{{count}} results", "group": "Group", "properties": "Properties", "kanban": "Ka<PERSON><PERSON>"}