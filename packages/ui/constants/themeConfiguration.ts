import {
  InputBase,
  type MantineThemeOverride,
  MultiSelect,
  NumberInput,
  Select,
  TextInput,
  rem,
} from '@mantine/core';
import { defaultFontName } from '@resola-ai/utils';

export const Colors = {
  silverFox: [
    '#FFFFFF',
    '#FEFEFE',
    '#FAFAFA',
    '#F3F3F4',
    '#EAEAEC',
    '#DEDEE2',
    '#CFCFD5',
    '#BDBDC5',
    '#87878D',
    '#47474A',
  ],
  decaMono: ['#000000', '#FFFFFF'],
  decaGray: ['', '', '', '#CED4DA', '', '#495057', '', '', '', ''],
  decaGrey: [
    '#CCCCCC',
    '#B8B8B8',
    '#A3A3A3',
    '#8F8F8F',
    '#7A7A7A',
    '#666666',
    '#5C5C5C',
    '#525252',
    '#474747',
    '#333333',
  ],
  decaLight: [
    '#F9F9FA',
    '#EEEEF1',
    '#E3E3E8',
    '#D7D7DE',
    '#CFCFD7',
    '#C1C1CC',
    '#ABABBA',
    '#9F9FB0',
    '#9494A7',
    '#89899E',
  ],
  decaDark: [
    '#7E7E93',
    '#5E606C',
    '#454650',
    '#353740',
    '#2D2E35',
    '#27282D',
    '#202125',
    '#1B1D20',
    '#141418',
    '#060707',
  ],
  decaNavy: [
    '#F0F2FA',
    '#D7DAF0',
    '#B2B7E2',
    '#848BD3',
    '#4247A6',
    '#1D2088',
    '#0C0B62',
    '#050046',
    '#050032',
    '#050024',
  ],
  decaBlue: [
    '#E1EFFE',
    '#BBD9FC',
    '#94C4FA',
    '#6DAFF8',
    '#4699F6',
    '#1F84F4',
    '#0C79F3',
    '#0B70DF',
    '#0A66CC',
    '#0852A5',
  ],
  decaViolet: [
    '#F0E8FF',
    '#E3CEF4',
    '#D6B4E9',
    '#C99BDF',
    '#BC81D4',
    '#AE67C9',
    '#A14DBE',
    '#9434B4',
    '#871AA9',
    '#7A009E',
  ],
  decaPurple: [
    '#F8F2F6',
    '#F0E4ED',
    '#E0C6D9',
    '#CFA3C3',
    '#B763A2',
    '#A81B8D',
    '#96187E',
    '#82156D',
    '#6A1159',
    '#4B0C3F',
  ],
  decaPink: [
    '#FCF2F5',
    '#FAE4EB',
    '#F5C6D5',
    '#EFA1BD',
    '#E85F98',
    '#E4007F',
    '#CC0072',
    '#B10062',
    '#900050',
    '#660039',
  ],
  decaRed: [
    '#FEF2F3',
    '#FEE5E6',
    '#FDC8CB',
    '#FB9BA0',
    '#FA6B75',
    '#F93549',
    '#DF2F41',
    '#CF1607',
    '#BC1407',
    '#A81206',
  ],
  decaTeal: [
    '#F2F5F3',
    '#E4EBE6',
    '#C6D6C9',
    '#99B9A1',
    '#5F9A6F',
    '#00833E',
    '#007537',
    '#006530',
    '#005327',
    '#003B1C',
  ],
  decaGreen: [
    '#DCF7CE',
    '#C4F2AA',
    '#ABED87',
    '#92E763',
    '#79E240',
    '#62D821',
    '#58C11E',
    '#4CA71A',
    '#3E8915',
    '#3A7F13',
  ],
  decaYellow: [
    '#FFF1CC',
    '#FFE6A3',
    '#FFE5A1',
    '#FFDB7A',
    '#FFDB72',
    '#FFD100',
    '#E4BB00',
    '#C6A200',
    '#C28D00',
    '#996F00',
  ],
} as const;

// TODO: Remove this once we upgrade Mantine to the latest v7 as it has been fixed
const pointerEventsProps = {
  leftSectionPointerEvents: 'none',
  rightSectionPointerEvents: 'none',
} as const;

export const componentOverrides: MantineThemeOverride['components'] = {
  InputBase: InputBase.extend({
    defaultProps: pointerEventsProps,
  }),
  TextInput: TextInput.extend({
    defaultProps: {
      leftSectionPointerEvents: 'all',
      rightSectionPointerEvents: 'all',
    },
  }),
  NumberInput: NumberInput.extend({
    defaultProps: {
      leftSectionPointerEvents: 'all',
      rightSectionPointerEvents: 'all',
    },
  }),
  MultiSelect: MultiSelect.extend({
    defaultProps: pointerEventsProps,
  }),
  Select: Select.extend({
    defaultProps: pointerEventsProps,
  }),
};

export const themeConfigurations: any = {
  components: componentOverrides,
  fontFamily: defaultFontName,
  headings: {
    fontFamily: defaultFontName,
    sizes: {
      h1: { fontSize: rem(40), lineHeight: rem(60) },
      h2: { fontSize: rem(32), lineHeight: rem(48) },
      h3: { fontSize: rem(24), lineHeight: rem(36) },
      h4: { fontSize: rem(20), lineHeight: rem(30) },
      h5: { fontSize: rem(18), lineHeight: rem(28) },
      h6: { fontSize: rem(16), lineHeight: rem(24) },
    },
  },
  primaryColor: 'decaNavy',
  colors: Colors as any,
  spacing: {
    xxs: rem(4),
    xs: rem(8),
    sm: rem(12),
    md: rem(16),
    lg: rem(20),
    xl: rem(24),
    xxl: rem(32),
  },
  fontSizes: {
    xxs: rem(10),
    xs: rem(11),
    sm: rem(12),
    md: rem(14),
    lg: rem(16),
    xl: rem(18),
  },
  shadows: {
    xs: `${rem(0)} ${rem(2)} ${rem(8)} ${rem(0)} rgba(13,17,54,0.12)`,
    sm: `${rem(0)} ${rem(2)} ${rem(8)} ${rem(0)} rgba(13,17,54,0.1)`,
    md: `${rem(0)} ${rem(3)} ${rem(12)} ${rem(0)} rgba(13,17,54,0.06)`,
    lg: `${rem(0)} ${rem(2)} ${rem(12)} ${rem(0)} rgba(13,17,54,0.09)`,
  },
  other: {
    lineHeights: {
      xxs: rem(16),
      xs: rem(18),
      sm: rem(18),
      md: rem(22),
      lg: rem(24),
    },
  },
};
