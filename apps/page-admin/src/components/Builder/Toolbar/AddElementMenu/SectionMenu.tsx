import NoResultImage from '@/assets/images/no-result.svg';
import { useUserInfor } from '@/hooks';
import useSearchSection from '@/hooks/useSearchSection';
import { CategoryAPI, SectionsAPI } from '@/services/api';
import type { SectionCategory } from '@/types';
import { showNotificationToast } from '@/utils/notification';
import { useEditor } from '@craftjs/core';
import { Box, Flex, Image, Stack, Text, Tooltip, rem, useMantineTheme } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { DecaButton } from '@resola-ai/ui';
import { IconHelpCircle } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { nanoid } from 'nanoid';
import { useEffect, useState } from 'react';
import Section from '../../Canvas/Section';
import classes from '../Toolbar.module.css';
import accordionClasses from './Accordion.module.css';
import ActionMenu from './ActionMenu';
import DeleteCategoryModal from './Modals/DeleteCategoryModal';
import EditCategoryModal from './Modals/EditCategoryModal';

interface SectionMenuProps {
  category?: SectionCategory;
  keyword?: string;
  onCategoryUpdate?: (updatedCategory?: SectionCategory) => void;
}

const SectionMenu = ({ category, keyword, onCategoryUpdate }: SectionMenuProps) => {
  const { t } = useTranslate('builder');
  const theme = useMantineTheme();
  const { connectors, actions, query } = useEditor();
  const { isStudioUser } = useUserInfor();
  const [editModalOpen, { close: closeEditModal, open: openEditModal }] = useDisclosure(false);
  const [deleteModalOpen, { close: closeDeleteModal, open: openDeleteModal }] =
    useDisclosure(false);

  // Local state to hold the most up-to-date category data
  const [displayCategory, setDisplayCategory] = useState<SectionCategory | undefined>(category);

  // Update display category when prop changes
  useEffect(() => {
    setDisplayCategory(category);
  }, [category]);

  const { data: sectionList, mutate } = useSearchSection({
    filter: JSON.stringify({
      cat_id: category?.id ?? undefined,
    }),
    search: keyword ?? undefined,
    perPage: 50,
    sort: 'cat_id,cat_name',
  });

  const handleSectionCreate = (ref: HTMLDivElement | null, item: any) => {
    if (!ref) return;
    connectors.create(ref, <Section />, {
      onCreate: ({ rootNodeId }) => {
        const { newContent, sectionWrapperId } = createSectionContent(item, rootNodeId);

        if (!sectionWrapperId) return;

        const existingNodes = query.getSerializedNodes();
        const sectionData = {
          ...existingNodes,
          [rootNodeId]: {
            ...existingNodes[rootNodeId],
            displayName: item.cat_name,
            nodes: [...(existingNodes[rootNodeId].nodes || []), sectionWrapperId],
          },
          ...newContent,
        };

        actions.deserialize(sectionData);
      },
    });
  };

  const handleEditCategory = async (category: SectionCategory) => {
    try {
      const updatedCategory = await SectionsAPI.updateSectionCategory(category?.id, {
        name: category?.name,
        description: category?.description,
      });

      // Update local display immediately with the response data
      setDisplayCategory(updatedCategory);

      // Pass the updated category data to the parent
      onCategoryUpdate?.(updatedCategory);

      // Still need to refetch sections since they might reference the updated category name
      mutate();

      showNotificationToast({
        message: t('categoryUpdatedSuccessfully'),
        isError: false,
      });
    } catch (error) {
      console.error('Error edit category:', error);
      showNotificationToast({
        message: t('categoryUpdateFailed'),
        isError: true,
      });
    }
  };

  const handleDeleteCategory = async (categoryId: string) => {
    try {
      await CategoryAPI.deleteCategory(categoryId);
      showNotificationToast({
        message: t('categoryDeletedSuccessfully'),
        isError: false,
      });
      mutate();
    } catch (error) {
      console.error('Error delete category:', error);
      showNotificationToast({
        message: t('categoryDeleteFailed'),
        isError: true,
      });
    }
  };

  const handleDeleteSection = async (sectionId: string) => {
    try {
      await SectionsAPI.deleteSection(sectionId);
      mutate();
      showNotificationToast({
        message: t('sectionDeletedSuccessfully'),
        isError: false,
      });
    } catch (error) {
      console.error('Error delete section:', error);
      showNotificationToast({
        message: t('sectionDeleteFailed'),
        isError: true,
      });
    }
  };

  const createSectionContent = (item: any, targetParentId: string) => {
    // Create ID mapping
    const idMap = {};
    const nodeIds = Object.keys(item.content);
    Object.keys(item.content).forEach((oldId) => {
      idMap[oldId] = nanoid(10);
    });

    // Create new content by deep cloning and only updating IDs
    const newContent = {};
    let sectionWrapperId = '';
    Object.entries(item.content).forEach(([oldId, node]) => {
      const newId = idMap[oldId];
      const clonedNode = structuredClone(node) as Record<string, any>;

      // Update only the necessary references
      if (
        clonedNode.type.resolvedName === 'ContainerWrapper' &&
        (!nodeIds.includes(clonedNode.parent) || clonedNode.parent === 'ROOT')
      ) {
        clonedNode.parent = targetParentId;
        sectionWrapperId = newId;
      } else if (idMap[clonedNode.parent]) {
        clonedNode.parent = idMap[clonedNode.parent];
      }

      if (Array.isArray(clonedNode.nodes)) {
        clonedNode.nodes = clonedNode.nodes.map((nodeId) => idMap[nodeId] || nodeId);
      }

      if (clonedNode.linkedNodes) {
        const newLinkedNodes = {};
        Object.entries(clonedNode.linkedNodes).forEach(([key, linkedNodeId]) => {
          newLinkedNodes[key] = idMap[linkedNodeId as string] || linkedNodeId;
        });
        clonedNode.linkedNodes = newLinkedNodes;
      }

      newContent[newId] = clonedNode;
    });

    return { newContent, sectionWrapperId };
  };

  const handlePlaceOnCanvas = (item: any) => {
    const sectionId = nanoid(10);
    const { newContent, sectionWrapperId } = createSectionContent(item, sectionId);

    if (!sectionWrapperId) return;

    const existingNodes = query.getSerializedNodes();

    // Create a new Section node and add it to the bottom
    const sectionData = {
      ...existingNodes,
      [sectionId]: {
        type: { resolvedName: 'Section' },
        isCanvas: false,
        props: {},
        displayName: item.cat_name,
        custom: {},
        parent: 'ROOT',
        hidden: false,
        nodes: [sectionWrapperId],
        linkedNodes: {},
      },
      ROOT: {
        ...existingNodes.ROOT,
        nodes: [...(existingNodes.ROOT.nodes || []), sectionId],
      },
      ...newContent,
    };

    actions.deserialize(sectionData);
  };

  const renderSection = (item, index) => {
    return (
      <Flex
        className={accordionClasses.section}
        key={index}
        ref={(ref) => handleSectionCreate(ref, item)}
        pos='relative'
        sx={{
          '&:hover .actionMenuContainer': {
            opacity: 1,
            zIndex: 10,
          },
          '&:hover .placeOnCanvasOverlay': {
            opacity: 1,
          },
          '.actionMenuContainer': {
            opacity: 0,
            transition: 'opacity 0.2s',
          },
          '.placeOnCanvasOverlay': {
            opacity: 0,
            transition: 'opacity 0.2s',
          },
        }}
      >
        {item?.screenshot?.thumbnail ? (
          <Flex pos='relative' w='100%'>
            <Image src={item.screenshot.thumbnail} />
            <Box
              className='placeOnCanvasOverlay'
              pos='absolute'
              top={0}
              left={0}
              right={0}
              bottom={0}
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.2)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 10,
                borderRadius: '8px',
              }}
            >
              <DecaButton
                size='sm'
                variant='primary'
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  handlePlaceOnCanvas(item);
                }}
                style={{
                  backgroundColor: 'white',
                  color: 'black',
                  border: 'none',
                }}
              >
                {t('placeOnCanvas')}
              </DecaButton>
            </Box>

            <Box pos='absolute' top={0} right={0} className='actionMenuContainer'>
              {isStudioUser && (
                <ActionMenu isEditable={false} onRemove={() => handleDeleteSection(item?.id)} />
              )}
            </Box>
          </Flex>
        ) : (
          <Flex justify='space-between' align='center' w='100%'>
            <Text variant='h3'>{item.name}</Text>
            {isStudioUser && (
              <ActionMenu isEditable={false} onRemove={() => handleDeleteSection(item?.id)} />
            )}
          </Flex>
        )}
      </Flex>
    );
  };

  const renderSectionLayout = () => {
    if (!keyword) {
      return sectionList?.map((item, index) => {
        return renderSection(item, index);
      });
    }

    if (sectionList && sectionList.length > 0) {
      const sectionCategoryList = sectionList.reduce((list, section) => {
        const key = section.cat_id;
        if (key !== null) {
          if (!list[key]) {
            list[key] = [];
          }
          list[key].push(section);
        }

        return list;
      }, {});

      return Object.keys(sectionCategoryList).map((cat_id, cat_index) => {
        const sectionCategoryInfo = sectionCategoryList[cat_id][0];
        return (
          <Flex direction={'column'} key={cat_index} gap={rem(16)}>
            <Text variant='h3'>{sectionCategoryInfo.cat_name}</Text>
            {sectionCategoryList[cat_id]?.map((item, index) => {
              return renderSection(item, index);
            })}
          </Flex>
        );
      });
    }

    return (
      <Flex direction={'column'} justify={'center'} className={classes.toolbarSubMenu}>
        <Flex m={'sm'} direction={'column'} align={'center'} justify={'center'}>
          <Image maw={300} src={NoResultImage} alt={t('noResult1')} />
          <Text ta='center' mt={rem(20)} c='decaGrey.7' fw={500}>
            {t('noResult1')}
          </Text>
        </Flex>
      </Flex>
    );
  };

  return (
    <Stack>
      <Flex align='center' gap='xs'>
        {displayCategory?.id ? (
          <Flex align='center' justify='space-between' w='100%'>
            <Flex align='center' gap='xs'>
              <Text fw={500} maw={170} truncate>
                {displayCategory?.name}
              </Text>
              <Tooltip label={displayCategory?.description}>
                <IconHelpCircle size={18} color={theme.colors.decaGrey[6]} />
              </Tooltip>
            </Flex>
            {isStudioUser && (
              <ActionMenu
                onEdit={openEditModal}
                onRemove={openDeleteModal}
                isEditable={!!displayCategory}
              />
            )}

            {editModalOpen && (
              <EditCategoryModal
                opened={editModalOpen}
                onClose={closeEditModal}
                category={displayCategory}
                onSave={handleEditCategory}
              />
            )}

            {deleteModalOpen && (
              <DeleteCategoryModal
                opened={deleteModalOpen}
                onClose={closeDeleteModal}
                onDelete={() => handleDeleteCategory(displayCategory?.id)}
              />
            )}
          </Flex>
        ) : (
          <Text fw={500}>{t('searchResults')}</Text>
        )}
      </Flex>

      <Flex direction='column' gap={rem(16)}>
        {renderSectionLayout()}
      </Flex>
    </Stack>
  );
};

export default SectionMenu;
