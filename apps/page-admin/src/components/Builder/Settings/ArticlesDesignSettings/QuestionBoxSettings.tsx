import { ColorSettings, SettingTitle } from '@/components/Builder/Settings';
import { useResponsiveNode } from '@/hooks';
import type { PaddingValue } from '@/types';
import type { PaddingType } from '@/types/enum';
import { Box, Flex, Stack, Switch, Text } from '@mantine/core';
import { rem } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import PaddingSettings from '../PaddingSettings';

const QuestionBoxSettings = () => {
  const { t } = useTranslate('builder');
  const {
    actions: { setProp },
    questionBox,
    builderPreviewMode,
  } = useResponsiveNode((node) => ({
    questionBox: node.data.props.questionBox,
  }));

  const handlePaddingChange = (padding: PaddingValue) => {
    setProp((props) => {
      if (!props.questionBox.padding) {
        props.questionBox.padding = {};
      }
      props.questionBox.padding[builderPreviewMode] = padding;
    });
  };

  const handlePaddingTypeChange = (paddingType: PaddingType) => {
    setProp((props) => {
      if (!props.questionBox.paddingType) {
        props.questionBox.paddingType = {};
      }
      props.questionBox.paddingType[builderPreviewMode] = paddingType;
    });
  };

  return (
    <Stack gap={rem(16)} p='md'>
      <SettingTitle title={t('questionBox')} />
      <ColorSettings
        label={t('backgroundColor', { ns: 'builder' })}
        onChange={(color) => {
          setProp((props) => {
            props.questionBox.backgroundColor = color;
          });
        }}
        defaultValue={questionBox.backgroundColor}
      />

      <ColorSettings
        label={t('textColor', { ns: 'builder' })}
        onChange={(color) => {
          setProp((props) => {
            props.questionBox.textColor = color;
          });
        }}
        defaultValue={questionBox.textColor}
      />

      <Flex align='center' justify='space-between'>
        <Text fw={500}>{t('showDivider')}</Text>
        <Switch
          checked={questionBox.showDivider}
          onChange={(event) =>
            setProp((props) => {
              props.questionBox.showDivider = event.currentTarget.checked;
            })
          }
          color='green'
        />
      </Flex>

      <Box mx={'-md'}>
        <PaddingSettings
          padding={questionBox.padding?.[builderPreviewMode]}
          onChange={handlePaddingChange}
          paddingType={questionBox.paddingType?.[builderPreviewMode]}
          onPaddingTypeChange={handlePaddingTypeChange}
        />
      </Box>
    </Stack>
  );
};

export default QuestionBoxSettings;
