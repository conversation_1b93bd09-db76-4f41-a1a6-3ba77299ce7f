import { ColorSettings, SettingTitle } from '@/components/Builder/Settings';
import { useResponsiveNode } from '@/hooks';
import type { PaddingValue } from '@/types';
import type { PaddingType } from '@/types/enum';
import { Box, Flex, Stack, Switch, Text } from '@mantine/core';
import { rem } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import PaddingSettings from '../PaddingSettings';
const AnswerBoxSettings = () => {
  const { t } = useTranslate('builder');
  const {
    actions: { setProp },
    answerBox,
    builderPreviewMode,
  } = useResponsiveNode((node) => ({
    answerBox: node.data.props.answerBox,
  }));

  const handlePaddingChange = (padding: PaddingValue) => {
    setProp((props) => {
      if (!props.answerBox.padding) {
        props.answerBox.padding = {};
      }
      props.answerBox.padding[builderPreviewMode] = padding;
    });
  };

  const handlePaddingTypeChange = (paddingType: PaddingType) => {
    setProp((props) => {
      if (!props.answerBox.paddingType) {
        props.answerBox.paddingType = {};
      }
      props.answerBox.paddingType[builderPreviewMode] = paddingType;
    });
  };

  return (
    <Stack gap={rem(16)} p='md'>
      <SettingTitle title={t('answerBox')} />
      <ColorSettings
        label={t('backgroundColor', { ns: 'builder' })}
        onChange={(color) => {
          setProp((props) => {
            props.answerBox.backgroundColor = color;
          });
        }}
        defaultValue={answerBox.backgroundColor}
      />

      <ColorSettings
        label={t('textColor', { ns: 'builder' })}
        onChange={(color) => {
          setProp((props) => {
            props.answerBox.textColor = color;
          });
        }}
        defaultValue={answerBox.textColor}
      />

      <Flex align='center' justify='space-between'>
        <Text fw={500}>{t('showDivider')}</Text>
        <Switch
          checked={answerBox.showDivider}
          onChange={(event) =>
            setProp((props) => {
              props.answerBox.showDivider = event.currentTarget.checked;
            })
          }
          color='green'
        />
      </Flex>

      <Box mx={'-md'}>
        <PaddingSettings
          padding={answerBox.padding?.[builderPreviewMode]}
          onChange={handlePaddingChange}
          paddingType={answerBox.paddingType?.[builderPreviewMode]}
          onPaddingTypeChange={handlePaddingTypeChange}
        />
      </Box>
    </Stack>
  );
};

export default AnswerBoxSettings;
