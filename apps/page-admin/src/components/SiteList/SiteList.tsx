import type { Site } from '@/types';
import { Grid, rem } from '@mantine/core';
import SiteItem from './SiteItem';

const SiteList = ({ sites, mutate }: { sites: any; mutate: () => void }) => {
  return (
    <Grid gutter='xl' mt={rem(10)}>
      {sites?.map((site: Site) => (
        <Grid.Col key={site.id} span={{ base: 12, sm: 6 }} miw={450}>
          <SiteItem site={site} mutate={mutate} />
        </Grid.Col>
      ))}
    </Grid>
  );
};

export default SiteList;
