import { SiteStatus } from '@/types';
import { render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import SiteList from './SiteList';

// Mock dependencies
vi.mock('@mantine/core', () => {
  const GridComponent = ({ children, gutter, mt }: any) => (
    <div data-testid='grid-container' data-gutter={gutter} data-mt={mt}>
      {children}
    </div>
  );

  GridComponent.Col = ({ children, span, miw }: any) => (
    <div data-testid='grid-col' data-span={JSON.stringify(span)} data-miw={miw}>
      {children}
    </div>
  );

  return {
    Grid: GridComponent,
    rem: (value: number) => value * 4, // Mock rem function - typically 1rem = 4px in Mantine
  };
});

vi.mock('./SiteItem', () => ({
  default: ({ site, mutate }: any) => (
    <div data-testid={`site-item-${site.id}`}>
      Site: {site.name} - {mutate === mockMutate ? 'with mutate' : 'without mutate'}
    </div>
  ),
}));

const mockSites = [
  {
    id: '1',
    name: 'Site 1',
    status: SiteStatus.Published,
    domains: [{ domain: 'site1.com', is_enabled: true }],
  },
  {
    id: '2',
    name: 'Site 2',
    status: SiteStatus.Draft,
    domains: [{ domain: 'site2.com', is_enabled: false }],
  },
  {
    id: '3',
    name: 'Site 3',
    status: SiteStatus.Published,
    domains: [{ domain: 'site3.com', is_enabled: true }],
  },
];

const mockMutate = vi.fn();

describe('SiteList', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render without crashing', () => {
    render(
      <MemoryRouter>
        <SiteList sites={mockSites} mutate={mockMutate} />
      </MemoryRouter>
    );

    expect(screen.getByTestId('site-item-1')).toBeInTheDocument();
    expect(screen.getByTestId('site-item-2')).toBeInTheDocument();
    expect(screen.getByTestId('site-item-3')).toBeInTheDocument();
  });

  it('should render all sites in the list', () => {
    render(
      <MemoryRouter>
        <SiteList sites={mockSites} mutate={mockMutate} />
      </MemoryRouter>
    );

    expect(screen.getByText('Site: Site 1 - with mutate')).toBeInTheDocument();
    expect(screen.getByText('Site: Site 2 - with mutate')).toBeInTheDocument();
    expect(screen.getByText('Site: Site 3 - with mutate')).toBeInTheDocument();
  });

  it('should handle empty sites array', () => {
    render(
      <MemoryRouter>
        <SiteList sites={[]} mutate={mockMutate} />
      </MemoryRouter>
    );

    expect(screen.queryByTestId(/site-item-/)).not.toBeInTheDocument();
  });

  it('should handle single site', () => {
    const singleSite = [mockSites[0]];

    render(
      <MemoryRouter>
        <SiteList sites={singleSite} mutate={mockMutate} />
      </MemoryRouter>
    );

    expect(screen.getByTestId('site-item-1')).toBeInTheDocument();
    expect(screen.queryByTestId('site-item-2')).not.toBeInTheDocument();
    expect(screen.queryByTestId('site-item-3')).not.toBeInTheDocument();
  });

  it('should pass mutate function to all SiteItem components', () => {
    render(
      <MemoryRouter>
        <SiteList sites={mockSites} mutate={mockMutate} />
      </MemoryRouter>
    );

    // All sites should show "with mutate" indicating the mutate function was passed
    expect(screen.getByText('Site: Site 1 - with mutate')).toBeInTheDocument();
    expect(screen.getByText('Site: Site 2 - with mutate')).toBeInTheDocument();
    expect(screen.getByText('Site: Site 3 - with mutate')).toBeInTheDocument();
  });

  it('should handle sites without mutate function', () => {
    const differentMutate = vi.fn();
    render(
      <MemoryRouter>
        <SiteList sites={mockSites} mutate={differentMutate} />
      </MemoryRouter>
    );

    // All sites should show "without mutate" indicating a different mutate function was passed
    expect(screen.getByText('Site: Site 1 - without mutate')).toBeInTheDocument();
    expect(screen.getByText('Site: Site 2 - without mutate')).toBeInTheDocument();
    expect(screen.getByText('Site: Site 3 - without mutate')).toBeInTheDocument();
  });

  it('should render with proper structure', () => {
    const { container } = render(
      <MemoryRouter>
        <SiteList sites={mockSites} mutate={mockMutate} />
      </MemoryRouter>
    );

    // Check that the component renders without errors
    expect(container.firstChild).toBeInTheDocument();
  });

  it('should maintain component structure on re-render', () => {
    const { rerender } = render(
      <MemoryRouter>
        <SiteList sites={mockSites} mutate={mockMutate} />
      </MemoryRouter>
    );

    expect(screen.getByTestId('site-item-1')).toBeInTheDocument();
    expect(screen.getByTestId('site-item-2')).toBeInTheDocument();
    expect(screen.getByTestId('site-item-3')).toBeInTheDocument();

    // Re-render and check structure is maintained
    rerender(
      <MemoryRouter>
        <SiteList sites={mockSites} mutate={mockMutate} />
      </MemoryRouter>
    );

    expect(screen.getByTestId('site-item-1')).toBeInTheDocument();
    expect(screen.getByTestId('site-item-2')).toBeInTheDocument();
    expect(screen.getByTestId('site-item-3')).toBeInTheDocument();
  });

  it('should handle sites with different statuses', () => {
    const sitesWithDifferentStatuses = [
      { ...mockSites[0], status: SiteStatus.Published },
      { ...mockSites[1], status: SiteStatus.Draft },
      { ...mockSites[2], status: SiteStatus.Draft }, // Changed from Archived to Draft
    ];

    render(
      <MemoryRouter>
        <SiteList sites={sitesWithDifferentStatuses} mutate={mockMutate} />
      </MemoryRouter>
    );

    expect(screen.getByTestId('site-item-1')).toBeInTheDocument();
    expect(screen.getByTestId('site-item-2')).toBeInTheDocument();
    expect(screen.getByTestId('site-item-3')).toBeInTheDocument();
  });

  it('should handle sites with missing properties', () => {
    const sitesWithMissingProps = [
      { id: '1', name: 'Site 1' }, // Missing status and domains
      { id: '2', name: 'Site 2', status: SiteStatus.Draft }, // Missing domains
      { id: '3', name: 'Site 3', domains: [] }, // Missing status
    ];

    render(
      <MemoryRouter>
        <SiteList sites={sitesWithMissingProps} mutate={mockMutate} />
      </MemoryRouter>
    );

    expect(screen.getByTestId('site-item-1')).toBeInTheDocument();
    expect(screen.getByTestId('site-item-2')).toBeInTheDocument();
    expect(screen.getByTestId('site-item-3')).toBeInTheDocument();
  });

  it('should handle undefined sites prop', () => {
    render(
      <MemoryRouter>
        <SiteList sites={undefined} mutate={mockMutate} />
      </MemoryRouter>
    );

    expect(screen.queryByTestId(/site-item-/)).not.toBeInTheDocument();
  });

  it('should handle null sites prop', () => {
    render(
      <MemoryRouter>
        <SiteList sites={null} mutate={mockMutate} />
      </MemoryRouter>
    );

    expect(screen.queryByTestId(/site-item-/)).not.toBeInTheDocument();
  });

  it('should render Grid container with correct props', () => {
    render(
      <MemoryRouter>
        <SiteList sites={mockSites} mutate={mockMutate} />
      </MemoryRouter>
    );

    const gridContainer = screen.getByTestId('grid-container');
    expect(gridContainer).toHaveAttribute('data-gutter', 'xl');
    expect(gridContainer).toHaveAttribute('data-mt', '40'); // rem(10) = 10 * 4 = 40
  });

  it('should render Grid.Col components with correct responsive props', () => {
    render(
      <MemoryRouter>
        <SiteList sites={mockSites} mutate={mockMutate} />
      </MemoryRouter>
    );

    const gridCols = screen.getAllByTestId('grid-col');
    expect(gridCols).toHaveLength(3); // Should have 3 sites

    gridCols.forEach((gridCol) => {
      expect(gridCol).toHaveAttribute('data-span', JSON.stringify({ base: 12, sm: 6 }));
      expect(gridCol).toHaveAttribute('data-miw', '450');
    });
  });

  it('should handle sites with duplicate IDs', () => {
    const sitesWithDuplicateIds = [
      { id: '1', name: 'Site 1', status: SiteStatus.Published },
      { id: '1', name: 'Site 1 Duplicate', status: SiteStatus.Draft }, // Same ID
      { id: '2', name: 'Site 2', status: SiteStatus.Published },
    ];

    render(
      <MemoryRouter>
        <SiteList sites={sitesWithDuplicateIds} mutate={mockMutate} />
      </MemoryRouter>
    );

    // Should render all sites even with duplicate IDs (React will handle this)
    const siteItemsWithId1 = screen.getAllByTestId('site-item-1');
    expect(siteItemsWithId1).toHaveLength(2);
    expect(screen.getByTestId('site-item-2')).toBeInTheDocument();
  });
});
