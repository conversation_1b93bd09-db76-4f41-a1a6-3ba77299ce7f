import NewSiteImage from '@/assets/images/new-site-icon.svg';
import { Flex, Image, Text, rem, useMantineTheme } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import { useState } from 'react';
import CreateNewSiteModal from '../SiteModal/CreateNewSiteModal';

const CreateNewSiteButton = () => {
  const { t } = useTranslate('workspace');
  const theme = useMantineTheme();
  const [opened, setOpened] = useState(false);

  return (
    <>
      <Flex
        gap={rem(15)}
        sx={{
          border: `1px solid ${theme.colors.decaLight[2]}`,
          borderRadius: rem(12),
          cursor: 'pointer',
          padding: `${rem(8)} ${rem(20)} ${rem(8)} ${rem(8)}}`,
        }}
        onClick={() => setOpened(true)}
      >
        <Image src={NewSiteImage} alt='new-site-icon' height={48} />
        <Flex direction='column' align='flex-start' justify='center'>
          <Text size='md' color={theme.colors.decaDark[4]} fw={700}>
            {t('newSite')}
          </Text>
          <Text size='sm' color={theme.colors.decaDark[0]}>
            {t('createNewSite')}
          </Text>
        </Flex>
      </Flex>
      <CreateNewSiteModal isOpen={opened} onClose={() => setOpened(false)} />
    </>
  );
};

export default CreateNewSiteButton;
