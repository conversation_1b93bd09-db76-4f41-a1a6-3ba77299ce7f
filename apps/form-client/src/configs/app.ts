/* eslint-disable turbo/no-undeclared-env-vars */
import type { IBaseAppConfig } from '@resola-ai/models';

interface AppConfig extends IBaseAppConfig {
  FORM_ADMIN_URL?: string;
}

export const AppConfig: AppConfig = {
  IS_PRODUCTION: process.env.NEXT_PUBLIC_NODE_ENV === 'production',
  AUTH0: {
    DOMAIN: process.env.NEXT_PUBLIC_AUTH0_DOMAIN ?? '',
    CLIENT_ID: process.env.NEXT_PUBLIC_AUTH0_CLIENT_ID ?? '',
    AUDIENCE: process.env.NEXT_PUBLIC_AUTH0_AUDIENCE ?? '',
    SCOPE: process.env.NEXT_PUBLIC_AUTH0_SCOPE ?? '',
  },
  DEFAULT_ORGANIZATION_NAME: process.env.NEXT_PUBLIC_DEFAULT_ORGANIZATION_NAME ?? 'resola',
  PU<PERSON>IC_URL: process.env.NEXT_PUBLIC_PUBLIC_URL ?? '',
  CDN_URL: process.env.NEXT_PUBLIC_CDN_PREFIX ?? '',
  EXCLUDED_BASE_PATH_DOMAINS: process.env.NEXT_PUBLIC_EXCLUDED_BASE_PATH_DOMAINS
    ? process.env.NEXT_PUBLIC_EXCLUDED_BASE_PATH_DOMAINS.split(',')
    : [],
  INCLUDED_BASE_PATH_DOMAINS: process.env.NEXT_PUBLIC_INCLUDED_BASE_PATH_DOMAINS
    ? process.env.NEXT_PUBLIC_INCLUDED_BASE_PATH_DOMAINS.split(',')
    : [],
  WEBSOCKET_URL: process.env.NEXT_PUBLIC_WEBSOCKET_URL ?? '',
  API_SERVER_URL: process.env.NEXT_PUBLIC_API_SERVER_URL ?? '',
  NO_PERMISSION_REDIRECT_URL:
    process.env.NEXT_PUBLIC_NO_PERMISSION_REDIRECT_URL ?? 'https://deca-dev.com',
  SKIP_ORG_NAME_DOMAINS: process.env.NEXT_PUBLIC_SKIP_ORG_NAME_DOMAINS
    ? process.env.NEXT_PUBLIC_SKIP_ORG_NAME_DOMAINS.split(',')
    : [],
  SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN ?? '',
  BASE_PATH: process.env.NEXT_PUBLIC_BASE_PATH ?? '/',
  FORM_ADMIN_URL: process.env.NEXT_PUBLIC_FORM_ADMIN_URL ?? '/',
  TOLGEE_URL: process.env.NEXT_PUBLIC_TOLGEE_URL ?? 'https://app.tolgee.io',
  TOLGEE_KEY: process.env.NEXT_PUBLIC_TOLGEE_KEY ?? '',
  TOLGEE_TOOLS_ENABLED: process.env.NEXT_PUBLIC_TOLGEE_TOOLS_ENABLED === 'true',
};
