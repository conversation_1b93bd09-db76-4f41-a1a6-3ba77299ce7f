export const fakePagesData = [
  {
    id: '01jfcjrm4zaqkxs711ftsgqkbr',
    name: '無題のページ',
    layout: {
      type: 'image-top-with-space',
      image_url:
        'https://cdn.deca-dev.com/form/assets/01haxd218s50f6yy4jf2f92fzf/01haxd218s50f6yy4jf2f92fzf/01jfcjrm39d6krv9t8qn4tsq4d/asset_01jhhtetng4xbt8npmz5vxf0rx.jpg',
      field_width: '70%',
      show_progress_bar: false,
    },
    type: 'section',
    index: 0,
    created_at: '2024-12-18T09:49:40.383000Z',
    updated_at: '2025-09-08T09:27:09.990000Z',
    content: [
      {
        id: '01jfcjtrxg7dye6ybkzps85ec6',
        index: 0,
        type: 'website',
        label: '<p>Website</p>',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: ' Type your description‌‌‍‍‌‍‍‌‌‌‌‍‍‌‍‍‌‌‌‌‌‌‍‌‍‌‌',
        icon: 'IconLink',
        right_icon: false,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: false,
        hide_label: false,
        placeholder: 'Enter your URL link‌‌‍‍‌‍‍‌‌‌‌‍‍‌‍‍‍‌‌‌‌‌‍‌‍‌‌',
        validators: [
          {
            type: 'pattern',
            value: '^(http|https):\\/\\/[^ "]+$',
            message: '',
          },
        ],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [],
        fields: [],
      },
      {
        id: '01jfcjtv3qg98v8t31h7aa6gke',
        index: 0,
        type: 'postcode',
        label: '<p>Location Postcode</p>',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: ' Type your description.‌‌‍‍‌‍‍‌‌‌‌‍‍‍‌‌‌‌‌‌‌‌‍‌‍‌‌',
        icon: null,
        right_icon: false,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: false,
        hide_label: false,
        placeholder: 'Enter your postcode‌‌‍‍‌‍‌‍‌‌‌‍‍‌‍‌‌‌‌‌‌‌‍‌‍‌‌',
        validators: [],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [],
        fields: [],
      },
      {
        id: '01jfcjwmj4ysmf3ac1agnn5r6z',
        index: 0,
        type: 'heading',
        label: '<p>Heading</p>',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: 'Please fill in the following fields‌‌‍‍‌‍‍‍‌‌‌‍‍‌‌‍‍‌‌‌‌‌‍‌‍‌‌',
        icon: null,
        right_icon: false,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: false,
        hide_label: false,
        placeholder: 'This is a text field‌‌‍‍‌‍‍‍‌‌‌‍‍‌‌‍‌‌‌‌‌‌‍‌‍‌‌',
        validators: [],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [],
        fields: [],
      },
      {
        id: '01jfck48xxfwdt8f61kjftry8b',
        index: 0,
        type: 'paragraph',
        label: '<p>Paragraph</p>',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: null,
        icon: null,
        right_icon: false,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: false,
        hide_label: false,
        placeholder:
          'It will take approximately 30 minutes to complete the application online. You will be asked to provide personal details such as your name, date of birth, tax file number (TFN) and passport details.‌‌‍‍‌‍‍‍‌‌‌‍‍‌‍‌‍‌‌‌‌‌‍‌‍‌‌',
        validators: [],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [],
        fields: [],
      },
      {
        id: '01jfck4c2kcjpdy5y785vddgzw',
        index: 0,
        type: 'short_qa',
        label: '<p>Type your question 1 here</p>',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: '<p>Type your description</p>',
        icon: null,
        right_icon: false,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: true,
        hide_label: false,
        placeholder: 'Type your answer here',
        validators: [
          {
            type: 'required',
            value: true,
            message: '',
          },
        ],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [],
        fields: [],
      },
      {
        id: '01jfck4dz43cqhpv1d8jtvs37c',
        index: 0,
        type: 'long_qa',
        label: '<p>Type your question 2 here</p>',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: '<p>Type your description</p>',
        icon: null,
        right_icon: false,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: true,
        hide_label: false,
        placeholder: 'Type your answer here‌‌‍‍‍‌‌‌‌‌‌‍‍‌‍‌‌‌‌‌‌‌‍‌‍‌‌',
        validators: [
          {
            type: 'required',
            value: false,
            message: '',
          },
        ],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [],
        fields: [],
      },
      {
        id: '01jfck4gdvv6p13b7t04cwkwxy',
        index: 0,
        type: 'opinion_scale',
        label: '<p>Type your question 3 here</p>',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: '<p>Type your description</p>',
        icon: null,
        right_icon: false,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 10,
        show_number: true,
        footer_label: {
          left: 'Please rate your level of agreement with the statement, with 1 representing “Strongly disagree.”‌‌‍‍‍‌‌‌‌‌‌‍‍‍‌‌‌‌‌‌‌‌‍‌‍‌‌',
          middle: '',
          right: '',
        },
        shape: null,
        description_enabled: true,
        hide_label: false,
        placeholder: null,
        validators: [
          {
            type: 'required',
            value: true,
            message: '',
          },
        ],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [],
        fields: [],
      },
      {
        id: '01jfck4s1kxkdjr9vamc7jzzch',
        index: 0,
        type: 'file_uploader',
        label: '<p>File uploader 4</p>',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: '<p>Type your description</p>',
        icon: null,
        right_icon: false,
        supported_types: ['image', 'document', 'media'],
        theme: 'large',
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: true,
        hide_label: false,
        placeholder: null,
        validators: [
          {
            type: 'required',
            value: false,
            message: '',
          },
        ],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [],
        fields: [],
      },
      {
        id: '01jfck4xjgjtbnhc4thw2d9857',
        index: 0,
        type: 'rating',
        label: '<p>Type your question 5 here</p>',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: '<p>Type your description</p>',
        icon: null,
        right_icon: false,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 10,
        show_number: true,
        footer_label: null,
        shape: 'star',
        description_enabled: true,
        hide_label: false,
        placeholder: null,
        validators: [
          {
            type: 'required',
            value: true,
            message: '',
          },
        ],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [],
        fields: [],
      },
      {
        id: '01jfck5mn8ydcyb2sfd4jfg3pe',
        index: 0,
        type: 'multiple_choice',
        label: '<p>Type your question 6 here</p>',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: 'Type your description‌‌‍‍‍‌‌‍‌‌‌‍‍‍‌‌‌‌‌‌‌‌‍‌‍‌‌',
        icon: null,
        right_icon: false,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: false,
        hide_label: false,
        placeholder: 'Enter your text',
        validators: [],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [
          {
            label: 'オプション1',
            default_check: false,
            image_url: null,
          },
        ],
        fields: [],
      },
      {
        id: '01jfck5p7hz8rwznkggeftz2tf',
        index: 0,
        type: 'checkboxes',
        label: '<p>Type your question 7 here</p>',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: 'Type your description‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‌‌‌‌‌‍‍‌‌‌‍‌‌‌‌‌‍‌‍‌‌',
        icon: null,
        right_icon: false,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: false,
        hide_label: false,
        placeholder: 'Enter your text',
        validators: [],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [
          {
            label: 'オプション223',
            default_check: false,
            image_url: null,
          },
          {
            label: 'オプション2sasdawwqqwewqsswewe',
            default_check: false,
            image_url: null,
          },
          {
            label: 'オプション321111111111222',
            default_check: false,
            image_url: null,
          },
        ],
        fields: [],
      },
      {
        id: '01jfck5ra7aa922d0ydcpx4va6',
        index: 0,
        type: 'dropdown',
        label: '<p>Type your question 8 here</p>',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: 'Type your description‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‌‌‌‌‌‍‍‌‍‌‌‌‌‌‌‌‍‌‍‌‌',
        icon: null,
        right_icon: false,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: false,
        hide_label: false,
        placeholder: 'Enter your text',
        validators: [],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [
          {
            label: 'オプション1',
            default_check: true,
            image_url: null,
          },
          {
            label: 'オプション2ssw',
            default_check: false,
            image_url: null,
          },
          {
            label: 'オプション3sasasdaswa',
            default_check: false,
            image_url: null,
          },
        ],
        fields: [],
      },
      {
        id: '01jfck5tcrfzj4gh89yd7rtn77',
        index: 0,
        type: 'yes_no',
        label: '<p>Type your question 9 here</p>',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: 'Type your description‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‌‌‌‌‌‍‍‌‍‍‍‌‌‌‌‌‍‌‍‌‌',
        icon: null,
        right_icon: false,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: false,
        hide_label: false,
        placeholder: 'Enter your text',
        validators: [],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [
          {
            label: 'Yes‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‌‌‌‌‌‍‍‍‌‌‌‌‌‌‌‌‍‌‍‌‌',
            default_check: false,
            image_url: null,
          },
          {
            label: 'No‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‌‌‌‌‌‍‍‍‌‌‍‌‌‌‌‌‍‌‍‌‌',
            default_check: false,
            image_url: null,
          },
        ],
        fields: [],
      },
      {
        id: '01jfck5xdnb3sk45t8vvg67gft',
        index: 0,
        type: 'legal',
        label: '<p>Type your question 10 here</p>',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: 'Type your description‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‌‌‌‌‌‌‍‌‍‌‌',
        icon: null,
        right_icon: false,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: false,
        hide_label: false,
        placeholder: 'Enter your text',
        validators: [],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [
          {
            label: 'I accept‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‍‌‌‌‌‌‍‌‍‌‌',
            default_check: false,
            image_url: null,
          },
        ],
        fields: [],
      },
      {
        id: '01jfck5z2gttawbr1ad2aa0sp7',
        index: 0,
        type: 'checkbox',
        label: '<p>Type your description</p>',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: null,
        icon: null,
        right_icon: false,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: false,
        hide_label: false,
        placeholder: 'Enter your text',
        validators: [],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [
          {
            label: '<p>noooo1112</p>',
            default_check: false,
            image_url: null,
          },
        ],
        fields: [],
      },
      {
        id: '01jfck6pxaq43es63fn8c7zebz',
        index: 0,
        type: 'date',
        label: '<p>Date</p>',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: 'YYYY/MM/DD',
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: 'Type your description‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‌‌‌‌‍‍‌‌‌‌‌‌‌‌‌‍‌‍‌‌',
        icon: null,
        right_icon: true,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: false,
        hide_label: false,
        placeholder: 'YYYY/MM/DD‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‌‍‌‌‌‍‍‍‌‌‍‌‌‌‌‌‍‌‍‌‌',
        validators: [],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [],
        fields: [],
      },
      {
        id: '01jfck6t1qmene5hbx7cwn9xv6',
        index: 0,
        type: 'time',
        label: '<p>Time</p>',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: 'hh:mm A',
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: 'Type your description‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‌‌‌‌‍‍‍‌‌‌‌‌‌‌‌‍‌‍‌‌',
        icon: null,
        right_icon: true,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: false,
        hide_label: false,
        placeholder: 'HH:MM AM‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‌‌‌‌‍‍‌‍‍‍‌‌‌‌‌‍‌‍‌‌',
        validators: [],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [],
        fields: [],
      },
      {
        id: '01jkses3cxxamhwn8pd8fn0hx4',
        index: 0,
        type: 'paragraph',
        label: '<p>以下の項目を記入してください。</p>',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: null,
        icon: null,
        right_icon: false,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: false,
        hide_label: false,
        placeholder:
          'オンライン申請には約30分かかります。氏名、生年月日、タックス・ファイル・ナンバー（TFN）、パスポート情報などの個人情報の入力が求められます。‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‌‌‌‌‍‍‌‍‌‌‌‌‌‌‌‍‌‍‌‌',
        validators: [],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [],
        fields: [],
      },
      {
        id: '01jmbk0m3j3812n3ck1x53ekpy',
        index: 0,
        type: 'name',
        label: null,
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: '',
        icon: null,
        right_icon: false,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: false,
        hide_label: true,
        placeholder: null,
        validators: [],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [],
        fields: [
          {
            id: '01jmbk0m3jspk4ra5sknhjt3k4',
            index: 0,
            type: 'short_qa',
            label: '<p>姓</p>',
            name: null,
            is_other: false,
            is_hide: false,
            time_format: null,
            date_format: null,
            dob_enabled: false,
            input_directly_enabled: false,
            fixed_date_title_enabled: false,
            description: '',
            icon: null,
            right_icon: false,
            supported_types: null,
            theme: null,
            footer_hide: false,
            max_scale: 0,
            show_number: true,
            footer_label: null,
            shape: null,
            description_enabled: false,
            hide_label: false,
            placeholder: '姓を入力‌‌‍‍‍‌‌‌‌‌‌‍‍‍‌‌‌‌‌‌‌‌‍‌‍‌‌',
            validators: [
              {
                type: 'required',
                value: true,
                message: '',
              },
              {
                type: 'alphabet',
                value: true,
                message: '',
              },
            ],
            logics: [],
            style: {
              width: '50%',
            },
            layout: 'one_column',
            options: [],
          },
          {
            id: '01jmbk0m3jb8g89js7e7vvjm5q',
            index: 0,
            type: 'short_qa',
            label: '<p>名</p>',
            name: null,
            is_other: false,
            is_hide: false,
            time_format: null,
            date_format: null,
            dob_enabled: false,
            input_directly_enabled: false,
            fixed_date_title_enabled: false,
            description: null,
            icon: null,
            right_icon: false,
            supported_types: null,
            theme: null,
            footer_hide: false,
            max_scale: 0,
            show_number: true,
            footer_label: null,
            shape: null,
            description_enabled: false,
            hide_label: false,
            placeholder: '名を入力‌‌‍‍‍‌‌‍‌‌‌‍‍‌‌‌‌‌‌‌‌‌‍‌‍‌‌',
            validators: [
              {
                type: 'required',
                value: true,
                message: '',
              },
              {
                type: 'alphabet',
                value: true,
                message: '',
              },
            ],
            logics: [],
            style: {
              width: '50%',
            },
            layout: 'one_column',
            options: [],
          },
        ],
      },
      {
        id: '01k49xq0phkrthc190mt708cjc',
        index: 0,
        type: 'short_qa',
        label: 'Type your question here‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‌‌‌‌‍‍‌‍‍‌‌‌‌‌‌‍‌‍‌‌',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: 'Type your description‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‌‌‌‌‍‍‌‍‍‍‌‌‌‌‌‍‌‍‌‌',
        icon: null,
        right_icon: false,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: true,
        hide_label: false,
        placeholder: 'Type your answer here‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‌‌‌‌‍‍‍‌‌‌‌‌‌‌‌‍‌‍‌‌',
        validators: [
          {
            type: 'required',
            value: true,
            message: '',
          },
          {
            type: 'max_length',
            value: 100,
            message: '',
          },
        ],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [],
        fields: [],
      },
      {
        id: '01k4m416s73x45rr4gtt1bazaj',
        index: 0,
        type: 'phone_number',
        label: '<p>my phone</p>',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: 'formField.phoneNumber.description',
        icon: null,
        right_icon: false,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: false,
        hide_label: false,
        placeholder: 'formField.phoneNumber.placeholder',
        validators: [
          {
            type: 'required',
            value: true,
            message: '',
          },
        ],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [],
        fields: [],
      },
    ],
  },
  {
    id: '01jh7rh7zr1vnep042xvkxy4et',
    name: '無題のページ‌‌‍‍‌‌‍‌‌‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‌‌‌‌‌‌‍‌‍‌‌',
    layout: {
      type: 'image-top-with-space',
      image_url: null,
      field_width: '55%',
      show_progress_bar: false,
    },
    type: 'section',
    index: 1,
    created_at: '2025-01-10T09:25:44.546000Z',
    updated_at: '2025-09-08T09:27:10.011000Z',
    content: [
      {
        id: '01jh7spjk3rcdbp7qyr191v7kb',
        index: 0,
        type: 'short_qa',
        label: '<p>ここに質問を入力してください</p>',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: '<p>説明を入力してください</p>',
        icon: null,
        right_icon: false,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: true,
        hide_label: false,
        placeholder: 'ここに回答を入力してください‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‌‌‌‌‍‍‌‌‌‌‌‌‌‌‌‍‌‍‌‌',
        validators: [
          {
            type: 'required',
            value: false,
            message: '',
          },
        ],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [],
        fields: [],
      },
      {
        id: '01jh7sprehgtdf4szaw182pps5',
        index: 0,
        type: 'long_qa',
        label: '<p>ここに質問を入力してください used</p>',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: '<p>説明を入力してください</p>',
        icon: null,
        right_icon: false,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: true,
        hide_label: false,
        placeholder: 'ここに回答を入力してください‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‌‌‌‌‍‍‌‍‌‌‌‌‌‌‌‍‌‍‌‌',
        validators: [
          {
            type: 'required',
            value: false,
            message: '',
          },
        ],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [],
        fields: [],
      },
      {
        id: '01jvs0zdc4fzz3q7f71ffy9m72',
        index: 0,
        type: 'checkbox',
        label: 'Text Field',
        name: null,
        is_other: false,
        is_hide: false,
        time_format: null,
        date_format: null,
        dob_enabled: false,
        input_directly_enabled: false,
        fixed_date_title_enabled: false,
        description: null,
        icon: null,
        right_icon: false,
        supported_types: null,
        theme: null,
        footer_hide: false,
        max_scale: 0,
        show_number: true,
        footer_label: null,
        shape: null,
        description_enabled: false,
        hide_label: false,
        placeholder: 'Enter your text',
        validators: [],
        logics: [],
        style: {},
        layout: 'one_column',
        options: [
          {
            label: '<p>123</p>',
            default_check: false,
            image_url: null,
          },
        ],
        fields: [],
      },
    ],
  },
  {
    id: '01jh7v8fndw4m5697y90rzrjhj',
    name: 'Untitled Page‌‌‍‍‌‌‍‌‌‌‌‍‍‍‌‌‌‌‌‌‍‍‌‌‍‌‌‌‌‌‌‍‌‍‌‌',
    layout: {
      type: 'image-top-with-space',
      image_url: null,
      field_width: '55%',
      show_progress_bar: false,
    },
    type: 'section',
    index: 2,
    created_at: '2025-01-10T10:13:26.361000Z',
    updated_at: '2025-09-08T09:27:10.031000Z',
    content: [],
  },
];
