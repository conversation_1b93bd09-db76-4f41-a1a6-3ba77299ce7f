export const pageFakeData = {
  createdAt: '2024-12-18T09:49:40.383000Z',
  id: '01jfcjrm4zaqkxs711ftsgqkbr',
  index: 0,
  layout: {
    type: 'image-top-with-space',
    imageUrl:
      'https://cdn.deca-dev.com/form/assets/01haxd218s50f6yy4jf2f92fzf/01haxd218s50f6yy4jf2f92fzf/01jfcjrm39d6krv9t8qn4tsq4d/asset_01jhhtetng4xbt8npmz5vxf0rx.jpg',
    fieldWidth: '70%',
    showProgressBar: false,
  },
  name: '無題のページ',
  type: 'section',
  updatedAt: '2025-09-05T07:06:31.028000Z',
  content: [
    {
      id: '01jfcjtrxg7dye6ybkzps85ec6',
      index: 0,
      type: 'website',
      label: '<p>Website</p>',
      name: null,
      isOther: false,
      isHide: false,
      timeFormat: null,
      dateFormat: null,
      dobEnabled: false,
      inputDirectlyEnabled: false,
      fixedDateTitleEnabled: false,
      description: ' Type your description‌‌‍‍‌‍‍‌‌‌‌‍‍‌‍‍‌‌‌‌‌‌‍‌‍‌‌',
      icon: 'IconLink',
      rightIcon: false,
      supportedTypes: null,
      theme: null,
      footerHide: false,
      maxScale: 0,
      showNumber: true,
      footerLabel: null,
      shape: null,
      descriptionEnabled: false,
      hideLabel: false,
      placeholder: 'Enter your URL link‌‌‍‍‌‍‍‌‌‌‌‍‍‌‍‍‍‌‌‌‌‌‍‌‍‌‌',
      validators: [
        {
          type: 'pattern',
          value: '^(http|https):\\/\\/[^ "]+$',
          message: '',
        },
      ],
      logics: [
        {
          id: 'logic_20241225_website_001',
          action: 'show',
          condition: {
            $and: [
              {
                '01jfcjtv3qg98v8t31h7aa6gke': {
                  $regex: '^SW|^N[0-9]|^E[0-9]',
                },
              },
            ],
          },
        },
      ],
      style: {},
      layout: 'one_column',
      options: [],
      fields: [],
    },
    {
      id: '01jfcjtv3qg98v8t31h7aa6gke',
      index: 0,
      type: 'postcode',
      label: '<p>Location Postcode</p>',
      name: null,
      isOther: false,
      isHide: false,
      timeFormat: null,
      dateFormat: null,
      dobEnabled: false,
      inputDirectlyEnabled: false,
      fixedDateTitleEnabled: false,
      description: ' Type your description.‌‌‍‍‌‍‍‌‌‌‌‍‍‍‌‌‌‌‌‌‌‌‍‌‍‌‌',
      icon: null,
      rightIcon: false,
      supportedTypes: null,
      theme: null,
      footerHide: false,
      maxScale: 0,
      showNumber: true,
      footerLabel: null,
      shape: null,
      descriptionEnabled: false,
      hideLabel: false,
      placeholder: 'Enter your postcode‌‌‍‍‌‍‌‍‌‌‌‍‍‌‍‌‌‌‌‌‌‌‍‌‍‌‌',
      validators: [],
      logics: [],
      style: {},
      layout: 'one_column',
      options: [],
      fields: [],
    },
    {
      id: '01jfck4c2kcjpdy5y785vddgzw',
      index: 0,
      type: 'short_qa',
      label: '<p>Type your question 1 here</p>',
      name: null,
      is_other: false,
      is_hide: false,
      time_format: null,
      date_format: null,
      dob_enabled: false,
      input_directly_enabled: false,
      fixed_date_title_enabled: false,
      description: '<p>Type your description</p>',
      icon: null,
      right_icon: false,
      supported_types: null,
      theme: null,
      footer_hide: false,
      max_scale: 0,
      show_number: true,
      footer_label: null,
      shape: null,
      description_enabled: true,
      hide_label: false,
      placeholder: 'Type your answer here',
      validators: [
        {
          type: 'required',
          value: true,
          message: '',
        },
        {
          type: 'max_length',
          value: 10,
        },
      ],
      logics: [
        {
          id: 'logic_20241225_shortqa_001',
          action: 'show',
          condition: {
            $and: [
              {
                '01jfcjtrxg7dye6ybkzps85ec6': {
                  $ne: '',
                },
              },
              {
                '01jfcjtv3qg98v8t31h7aa6gke': {
                  $regex: '^[A-Z]{1,2}[0-9]',
                },
              },
            ],
          },
        },
      ],
      style: {},
      layout: 'one_column',
      options: [],
      fields: [],
    },
    {
      id: '01jfck4dz43cqhpv1d8jtvs37c',
      index: 0,
      type: 'long_qa',
      label: '<p>Type your question 2 here</p>',
      name: null,
      isOther: false,
      isHide: false,
      timeFormat: null,
      dateFormat: null,
      dobEnabled: false,
      inputDirectlyEnabled: false,
      fixedDateTitleEnabled: false,
      description: '<p>Type your description</p>',
      icon: null,
      rightIcon: false,
      supportedTypes: null,
      theme: null,
      footerHide: false,
      maxScale: 0,
      showNumber: true,
      footerLabel: null,
      shape: null,
      descriptionEnabled: true,
      hideLabel: false,
      placeholder: 'Type your answer here‌‌‍‍‍‌‌‌‌‌‌‍‍‌‍‌‌‌‌‌‌‌‍‌‍‌‌',
      validators: [
        {
          type: 'required',
          value: false,
          message: '',
        },
      ],
      logics: [
        {
          id: 'logic_20241225_longqa_001',
          action: 'show',
          condition: {
            $or: [
              {
                '01jfck4c2kcjpdy5y785vddgzw': {
                  $regex: 'interested|yes|positive',
                },
              },
              {
                '01jfcjtrxg7dye6ybkzps85ec6': {
                  $regex: '^https://',
                },
              },
            ],
          },
        },
      ],
      style: {},
      layout: 'one_column',
      options: [],
      fields: [],
    },
    {
      id: '01jfck4gdvv6p13b7t04cwkwxy',
      index: 0,
      type: 'opinion_scale',
      label: '<p>Type your question 3 here</p>',
      name: null,
      isOther: false,
      isHide: false,
      timeFormat: null,
      dateFormat: null,
      dobEnabled: false,
      inputDirectlyEnabled: false,
      fixedDateTitleEnabled: false,
      description: '<p>Type your description</p>',
      icon: null,
      rightIcon: false,
      supportedTypes: null,
      theme: null,
      footerHide: false,
      maxScale: 10,
      showNumber: true,
      footerLabel: {
        left: "Please rate your level of agreement with the statement, with 1 representing 'Strongly disagree.'‌‌‍‍‍‌‌‌‌‌‌‍‍‍‌‌‌‌‌‌‌‌‍‌‍‌‌",
        middle: '',
        right: '',
      },
      shape: null,
      descriptionEnabled: true,
      hideLabel: false,
      placeholder: null,
      validators: [
        {
          type: 'required',
          value: true,
          message: '',
        },
      ],
      logics: [],
      style: {},
      layout: 'one_column',
      options: [],
      fields: [],
    },
    {
      id: '01jfck4xjgjtbnhc4thw2d9857',
      index: 0,
      type: 'rating',
      label: '<p>Type your question 5 here</p>',
      name: null,
      isOther: false,
      isHide: false,
      timeFormat: null,
      dateFormat: null,
      dobEnabled: false,
      inputDirectlyEnabled: false,
      fixedDateTitleEnabled: false,
      description: '<p>Type your description</p>',
      icon: null,
      rightIcon: false,
      supportedTypes: null,
      theme: null,
      footerHide: false,
      maxScale: 10,
      showNumber: true,
      footerLabel: null,
      shape: 'star',
      descriptionEnabled: true,
      hideLabel: false,
      placeholder: null,
      validators: [
        {
          type: 'required',
          value: true,
          message: '',
        },
      ],
      logics: [
        {
          id: 'logic_20241225_rating_001',
          action: 'show',
          condition: {
            $and: [
              {
                '01jfck4gdvv6p13b7t04cwkwxy': {
                  $in: ['7', '8', '9', '10'],
                },
              },
              {
                '01jfck4dz43cqhpv1d8jtvs37c': {
                  $ne: '',
                },
              },
              {
                '01jfcjtrxg7dye6ybkzps85ec6': {
                  $exists: true,
                },
              },
            ],
          },
        },
      ],
      style: {},
      layout: 'one_column',
      options: [],
      fields: [],
    },
    {
      id: '01jfck5mn8ydcyb2sfd4jfg3pe',
      index: 9,
      type: 'multiple_choice',
      label: '<p>Type your question 6 here</p>',
      name: null,
      is_other: false,
      is_hide: false,
      time_format: null,
      date_format: null,
      dob_enabled: false,
      input_directly_enabled: false,
      fixed_date_title_enabled: false,
      description: 'Type your description‌‌‍‍‍‌‌‍‌‌‌‍‍‍‌‌‌‌‌‌‌‌‍‌‍‌‌',
      icon: null,
      right_icon: false,
      supported_types: null,
      theme: null,
      footer_hide: false,
      max_scale: 0,
      show_number: true,
      footer_label: null,
      shape: null,
      description_enabled: false,
      hide_label: false,
      placeholder: 'Enter your text',
      validators: [],
      logics: [
        {
          id: 'logic_20241225_multichoice_001',
          action: 'hide',
          condition: {
            $and: [
              {
                '01jfck4gdvv6p13b7t04cwkwxy': {
                  $in: ['1', '2', '3'],
                },
              },
              {
                '01jfck4xjgjtbnhc4thw2d9857': {
                  $in: ['1', '2'],
                },
              },
            ],
          },
        },
      ],
      style: {},
      layout: 'one_column',
      options: [
        {
          label: 'option 1',
          default_check: false,
          image_url: null,
          id: '5izyYfhM',
        },
        {
          label: 'option 2',
          id: 'JMYGunq7',
        },
      ],
      fields: [],
    },
    {
      id: '01jfck5p7hz8rwznkggeftz2tf',
      index: 0,
      type: 'checkboxes',
      label: '<p>Type your question 7 here</p>',
      name: null,
      isOther: false,
      isHide: false,
      timeFormat: null,
      dateFormat: null,
      dobEnabled: false,
      inputDirectlyEnabled: false,
      fixedDateTitleEnabled: false,
      description: 'Type your description‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‌‌‌‌‌‍‍‌‌‌‍‌‌‌‌‌‍‌‍‌‌',
      icon: null,
      rightIcon: false,
      supportedTypes: null,
      theme: null,
      footerHide: false,
      maxScale: 0,
      showNumber: true,
      footerLabel: null,
      shape: null,
      descriptionEnabled: false,
      hideLabel: false,
      placeholder: 'Enter your text',
      validators: [],
      logics: [
        {
          id: 'logic_20241225_checkboxes_001',
          action: 'show',
          condition: {
            $or: [
              {
                '01jfck5mn8ydcyb2sfd4jfg3pe': {
                  $eq: 'オプション1',
                },
              },
              {
                '01jfck4xjgjtbnhc4thw2d9857': {
                  $in: ['8', '9', '10'],
                },
              },
              {
                '01jfck4gdvv6p13b7t04cwkwxy': {
                  $in: ['8', '9', '10'],
                },
              },
              {
                '01jfcjtrxg7dye6ybkzps85ec6': {
                  $regex: 'github|portfolio|company',
                },
              },
              {
                '01jfck4c2kcjpdy5y785vddgzw': {
                  $regex: 'professional|business|work',
                },
              },
            ],
          },
        },
      ],
      style: {},
      layout: 'one_column',
      options: [
        {
          label: 'オプション223',
          defaultCheck: false,
          imageUrl: null,
        },
        {
          label: 'オプション2sasdawwqqwewqsswewe',
          defaultCheck: false,
          imageUrl: null,
        },
        {
          label: 'オプション321111111111222',
          defaultCheck: false,
          imageUrl: null,
        },
      ],
      fields: [],
    },
    {
      id: '01jfck5ra7aa922d0ydcpx4va6',
      index: 0,
      type: 'dropdown',
      label: '<p>Type your question 8 here</p>',
      name: null,
      isOther: false,
      isHide: false,
      timeFormat: null,
      dateFormat: null,
      dobEnabled: false,
      inputDirectlyEnabled: false,
      fixedDateTitleEnabled: false,
      description: 'Type your description‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‌‌‌‌‌‍‍‌‍‌‌‌‌‌‌‌‍‌‍‌‌',
      icon: null,
      rightIcon: false,
      supportedTypes: null,
      theme: null,
      footerHide: false,
      maxScale: 0,
      showNumber: true,
      footerLabel: null,
      shape: null,
      descriptionEnabled: false,
      hideLabel: false,
      placeholder: 'Enter your text',
      validators: [],
      logics: [
        {
          id: 'logic_20241225_dropdown_001',
          action: 'show',
          condition: {
            $and: [
              {
                '01jfck5p7hz8rwznkggeftz2tf': {
                  $in: ['オプション223', 'オプション321111111111222'],
                },
              },
              {
                '01jfck4xjgjtbnhc4thw2d9857': {
                  $in: ['6', '7', '8', '9', '10'],
                },
              },
              {
                '01jfck4gdvv6p13b7t04cwkwxy': {
                  $in: ['6', '7', '8', '9', '10'],
                },
              },
            ],
          },
        },
      ],
      style: {},
      layout: 'one_column',
      options: [
        {
          label: 'オプション1',
          defaultCheck: true,
          imageUrl: null,
        },
        {
          label: 'オプション2ssw',
          defaultCheck: false,
          imageUrl: null,
        },
        {
          label: 'オプション3sasasdaswa',
          defaultCheck: false,
          imageUrl: null,
        },
      ],
      fields: [],
    },
    {
      id: '01jfck5tcrfzj4gh89yd7rtn77',
      index: 0,
      type: 'yes_no',
      label: '<p>Type your question 9 here</p>',
      name: null,
      is_other: false,
      is_hide: false,
      time_format: null,
      date_format: null,
      dob_enabled: false,
      input_directly_enabled: false,
      fixed_date_title_enabled: false,
      description: 'Type your description‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‌‌‌‌‌‍‍‌‍‍‍‌‌‌‌‌‍‌‍‌‌',
      icon: null,
      right_icon: false,
      supported_types: null,
      theme: null,
      footer_hide: false,
      max_scale: 0,
      show_number: true,
      footer_label: null,
      shape: null,
      description_enabled: false,
      hide_label: false,
      placeholder: 'Enter your text',
      validators: [],
      logics: [],
      style: {},
      layout: 'one_column',
      options: [
        {
          label: 'Yes‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‌‌‌‌‌‍‍‍‌‌‌‌‌‌‌‌‍‌‍‌‌',
          default_check: false,
          image_url: null,
        },
        {
          label: 'No‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‌‌‌‌‌‍‍‍‌‌‍‌‌‌‌‌‍‌‍‌‌',
          default_check: false,
          image_url: null,
        },
      ],
      fields: [],
    },
    {
      id: '01jfck5xdnb3sk45t8vvg67gft',
      index: 0,
      type: 'legal',
      label: '<p>Type your question 10 here</p>',
      name: null,
      isOther: false,
      isHide: false,
      timeFormat: null,
      dateFormat: null,
      dobEnabled: false,
      inputDirectlyEnabled: false,
      fixedDateTitleEnabled: false,
      description: 'Type your description‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‌‌‌‌‌‌‍‌‍‌‌',
      icon: null,
      rightIcon: false,
      supportedTypes: null,
      theme: null,
      footerHide: false,
      maxScale: 0,
      showNumber: true,
      footerLabel: null,
      shape: null,
      descriptionEnabled: false,
      hideLabel: false,
      placeholder: 'Enter your text',
      validators: [],
      logics: [
        {
          id: 'logic_20241225_legal_001',
          action: 'show',
          condition: {
            $and: [
              {
                '01jfck5tcrfzj4gh89yd7rtn77': {
                  $eq: 'Yes‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‌‌‌‌‌‍‍‍‌‌‌‌‌‌‌‌‍‌‍‌‌',
                },
              },
              {
                '01jfck5ra7aa922d0ydcpx4va6': {
                  $ne: '',
                },
              },
            ],
          },
        },
      ],
      style: {},
      layout: 'one_column',
      options: [
        {
          label: 'I accept‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‍‌‌‌‌‌‍‌‍‌‌',
          defaultCheck: false,
          imageUrl: null,
        },
      ],
      fields: [],
    },
    {
      id: '01jfck5z2gttawbr1ad2aa0sp7',
      index: 0,
      type: 'checkbox',
      label: '<p>Type your description</p>',
      name: null,
      isOther: false,
      isHide: false,
      timeFormat: null,
      dateFormat: null,
      dobEnabled: false,
      inputDirectlyEnabled: false,
      fixedDateTitleEnabled: false,
      description: null,
      icon: null,
      rightIcon: false,
      supportedTypes: null,
      theme: null,
      footerHide: false,
      maxScale: 0,
      showNumber: true,
      footerLabel: null,
      shape: null,
      descriptionEnabled: false,
      hideLabel: false,
      placeholder: 'Enter your text',
      validators: [],
      logics: [],
      style: {},
      layout: 'one_column',
      options: [
        {
          label: '<p>noooo1112</p>',
          defaultCheck: false,
          imageUrl: null,
        },
      ],
      fields: [],
    },
    {
      id: '01jmbk0m3j3812n3ck1x53ekpy',
      index: 0,
      type: 'name',
      label: null,
      name: null,
      isOther: false,
      isHide: false,
      timeFormat: null,
      dateFormat: null,
      dobEnabled: false,
      inputDirectlyEnabled: false,
      fixedDateTitleEnabled: false,
      description: '',
      icon: null,
      rightIcon: false,
      supportedTypes: null,
      theme: null,
      footerHide: false,
      maxScale: 0,
      showNumber: true,
      footerLabel: null,
      shape: null,
      descriptionEnabled: false,
      hideLabel: true,
      placeholder: null,
      validators: [],
      logics: [],
      style: {},
      layout: 'one_column',
      options: [],
      fields: [
        {
          id: '01jmbk0m3jspk4ra5sknhjt3k4',
          index: 0,
          type: 'short_qa',
          label: '<p>姓</p>',
          name: null,
          isOther: false,
          isHide: false,
          timeFormat: null,
          dateFormat: null,
          dobEnabled: false,
          inputDirectlyEnabled: false,
          fixedDateTitleEnabled: false,
          description: '',
          icon: null,
          rightIcon: false,
          supportedTypes: null,
          theme: null,
          footerHide: false,
          maxScale: 0,
          showNumber: true,
          footerLabel: null,
          shape: null,
          descriptionEnabled: false,
          hideLabel: false,
          placeholder: '姓を入力‌‌‍‍‍‌‌‌‌‌‌‍‍‍‌‌‌‌‌‌‌‌‍‌‍‌‌',
          validators: [
            {
              type: 'required',
              value: true,
              message: '',
            },
            {
              type: 'alphabet',
              value: true,
              message: '',
            },
            {
              type: 'min_length',
              value: 1,
              message: 'Name must have at least 1 letter‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‍‌‌‌‌‌‍‌‍‌‌',
            },
            {
              type: 'max_length',
              value: 40,
              message: 'Name must be 2-40 characters long‌‌‍‍‌‌‌‍‌‌‌‍‍‌‍‌‌‌‌‌‌‌‍‌‍‌‌',
            },
          ],
          logics: [],
          style: {
            width: '50%',
          },
          layout: 'one_column',
          options: [],
        },
        {
          id: '01jmbk0m3jb8g89js7e7vvjm5q',
          index: 0,
          type: 'short_qa',
          label: '<p>名</p>',
          name: null,
          isOther: false,
          isHide: false,
          timeFormat: null,
          dateFormat: null,
          dobEnabled: false,
          inputDirectlyEnabled: false,
          fixedDateTitleEnabled: false,
          description: null,
          icon: null,
          rightIcon: false,
          supportedTypes: null,
          theme: null,
          footerHide: false,
          maxScale: 0,
          showNumber: true,
          footerLabel: null,
          shape: null,
          descriptionEnabled: false,
          hideLabel: false,
          placeholder: '名を入力‌‌‍‍‍‌‌‍‌‌‌‍‍‌‌‌‌‌‌‌‌‌‍‌‍‌‌',
          validators: [
            {
              type: 'required',
              value: true,
              message: '',
            },
            {
              type: 'alphabet',
              value: true,
              message: '',
            },
            {
              type: 'min_length',
              value: 1,
              message: 'Name must have at least 1 letter‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‍‌‌‌‌‌‍‌‍‌‌',
            },
            {
              type: 'max_length',
              value: 40,
              message: 'Name must be 2-40 characters long‌‌‍‍‌‌‌‍‌‌‌‍‍‌‍‌‌‌‌‌‌‌‍‌‍‌‌',
            },
          ],
          logics: [],
          style: {
            width: '50%',
          },
          layout: 'one_column',
          options: [],
        },
      ],
    },
    {
      id: '01k49xq0phkrthc190mt708cjc',
      index: 0,
      type: 'short_qa',
      label: 'Type your question here‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‌‌‌‌‍‍‌‍‍‌‌‌‌‌‌‍‌‍‌‌',
      name: null,
      isOther: false,
      isHide: false,
      timeFormat: null,
      dateFormat: null,
      dobEnabled: false,
      inputDirectlyEnabled: false,
      fixedDateTitleEnabled: false,
      description: 'Type your description‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‌‌‌‌‍‍‌‍‍‍‌‌‌‌‌‍‌‍‌‌',
      icon: null,
      rightIcon: false,
      supportedTypes: null,
      theme: null,
      footerHide: false,
      maxScale: 0,
      showNumber: true,
      footerLabel: null,
      shape: null,
      descriptionEnabled: true,
      hideLabel: false,
      placeholder: 'Type your answer here‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‌‌‌‌‍‍‍‌‌‌‌‌‌‌‌‍‌‍‌‌',
      validators: [
        {
          type: 'required',
          value: true,
          message: '',
        },
        {
          type: 'max_length',
          value: 100,
          message: '',
        },
      ],
      logics: [
        {
          id: 'logic_20241225_extraqa_001',
          action: 'show',
          condition: {
            $and: [
              {
                '01jfck5xdnb3sk45t8vvg67gft': {
                  $eq: 'I accept‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‍‌‌‌‌‌‍‌‍‌‌',
                },
              },
              {
                '01jmbk0m3j3812n3ck1x53ekpy': {
                  $ne: '',
                },
              },
            ],
          },
        },
      ],
      style: {},
      layout: 'one_column',
      options: [],
      fields: [],
    },
    {
      id: '01k4m416s73x45rr4gtt1bazaj',
      index: 0,
      type: 'phone_number',
      label: '<p>my phone</p>',
      name: null,
      isOther: false,
      isHide: false,
      timeFormat: null,
      dateFormat: null,
      dobEnabled: false,
      inputDirectlyEnabled: false,
      fixedDateTitleEnabled: false,
      description: 'formField.phoneNumber.description',
      icon: null,
      rightIcon: false,
      supportedTypes: null,
      theme: null,
      footerHide: false,
      maxScale: 0,
      showNumber: true,
      footerLabel: null,
      shape: null,
      descriptionEnabled: false,
      hideLabel: false,
      placeholder: 'formField.phoneNumber.placeholder',
      validators: [
        {
          type: 'required',
          value: true,
          message: '',
        },
      ],
      logics: [
        {
          id: 'logic_20241225_phone_001',
          action: 'show',
          condition: {
            $or: [
              {
                '01jfck5z2gttawbr1ad2aa0sp7': {
                  $eq: '<p>noooo1112</p>',
                },
              },
              {
                '01jfck5xdnb3sk45t8vvg67gft': {
                  $eq: 'I accept‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‍‌‌‌‌‌‍‌‍‌‌',
                },
              },
            ],
          },
        },
      ],
      style: {},
      layout: 'one_column',
      options: [],
      fields: [],
    },
  ],
};
