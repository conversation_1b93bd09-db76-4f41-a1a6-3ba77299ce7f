import type { FormField, FormSection, FormSettings } from '@/types/form-builder';
import { createContext, useContext, useMemo } from 'react';

interface FormSettingsContext extends FormSettings {
  hiddenFieldsMap: Record<string, string>;
  contentPages?: FormSection[];
}

const context = createContext<FormSettingsContext | null>(null);

export const FormSettingsProvider = ({
  children,
  formSettings,
  hiddenFields,
  contentPages,
}: {
  children: React.ReactNode;
  formSettings: FormSettings;
  hiddenFields: FormField[];
  contentPages?: FormSection[];
}) => {
  const hiddenFieldsMap = useMemo(
    () =>
      hiddenFields?.reduce((acc, field) => {
        acc[field.id] = field.name;
        return acc;
      }, {}),
    [hiddenFields]
  );

  return (
    <context.Provider value={{ ...formSettings, hiddenFieldsMap, contentPages }}>
      {children}
    </context.Provider>
  );
};

export const useFormSettings = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useFormSettings must be used inside FormSettingsProvider');
  }

  return value;
};
