/* eslint-disable no-unused-vars */
import type React from 'react';
import { createContext, useContext, useEffect, useRef } from 'react';
import { useFormLogic } from '../hooks/useFormLogic';

type FormLogicContextType = {
  updateFieldVisibility: (fieldId: string, value: any) => void;
  isFieldVisible: (fieldId: string) => boolean;
  initializeVisibility: (formData: Record<string, any>) => void;
  clearCache: () => void;
  getPerformanceStats: () => any;
  visibilityState: Record<string, boolean>;
};

const FormLogicContext = createContext<FormLogicContextType | null>(null);

interface FormLogicProviderProps {
  children: React.ReactNode;
  fields: any[]; // Form field definitions with logic\
  initialValues?: Record<string, any>;
}

export const FormLogicProvider: React.FC<FormLogicProviderProps> = ({
  children,
  fields,
  initialValues = {},
}) => {
  const logicHook = useFormLogic(fields);
  const initializedRef = useRef(false);

  // Initialize visibility state once when component mounts
  useEffect(() => {
    if (!initializedRef.current) {
      logicHook.initializeVisibility(initialValues);
      initializedRef.current = true;
    }
  }, [logicHook, initialValues]);

  return <FormLogicContext.Provider value={logicHook}>{children}</FormLogicContext.Provider>;
};

export const useFormLogicContext = () => {
  const context = useContext(FormLogicContext);
  if (!context) {
    throw new Error('useFormLogicContext must be used within a FormLogicProvider');
  }
  return context;
};

export default FormLogicContext;
