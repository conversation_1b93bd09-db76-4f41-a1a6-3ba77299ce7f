# Form Logic System Documentation

## 🎯 Overview

The Form Logic System enables conditional field visibility based on user input. Fields can be shown or hidden dynamically based on values in other fields using MongoDB-style query conditions.

## 📋 Architecture

```
FormContentWithLogic
├── FormLogicProvider (Context)
│   └── useFormLogic (Hook)
│       └── ConditionEvaluator (Engine)
└── FormFieldsWithLogic
    ├── FormFieldWithLogic (Individual fields)
    └── GroupFieldWithLogic (Group fields)
```

## 🔧 Core Components

### 1. **useFormLogic Hook** (`hooks/useFormLogic.ts`)
**Purpose:** Main logic engine that manages field visibility state

**Key Features:**
- ✅ MongoDB-style condition evaluation ($and, $or, $eq, $ne, $in, $nin, $regex, etc.)
- ✅ Dependency graph optimization (only evaluates affected fields)
- ✅ Memoization with cache limits (500 entries max)
- ✅ Performance tracking for debugging

**API:**
```typescript
const {
  updateFieldVisibility,  // Trigger logic evaluation
  isFieldVisible,         // Check field visibility
  initializeVisibility,   // Set initial state
  clearCache,            // Clear memoization cache
  getPerformanceStats,   // Debug information
  visibilityState        // Current visibility state
} = useFormLogic(fields);
```

### 2. **FormLogicContext** (`contexts/FormLogicContext.tsx`)
**Purpose:** React context that shares logic state across components

**Features:**
- ✅ Integrates with existing FormContext
- ✅ One-time initialization on mount
- ✅ Prevents infinite loop with useRef guard

### 3. **ConditionEvaluator** (internal class)
**Purpose:** Lightweight MongoDB-style query engine

**Supported Operators:**
```javascript
$and     // Logical AND
$or      // Logical OR
$eq      // Equals
$ne      // Not equals
$in      // Value in array
$nin     // Value not in array
$regex   // Regular expression match
$exists  // Field exists and not empty
$gt/$gte // Greater than / equal
$lt/$lte // Less than / equal
```

## 🚀 Integration Components

### 1. **FormFieldWithLogic** (`components/FormFields/FormFieldWithLogic.tsx`)
Wraps `FormFieldBase` with conditional visibility:
```tsx
// Automatically hides/shows based on logic rules
<FormFieldWithLogic field={field} />
```

### 2. **GroupFieldWithLogic** (`components/FormFields/GroupFieldWithLogic.tsx`)
Handles group fields (Name, Address) with logic:
```tsx
// Groups can also have conditional logic
<GroupFieldWithLogic group={groupField} />
```

### 3. **FormFieldsWithLogic** (`components/FormFields/FormFieldsWithLogic.tsx`)
Enhanced version of `FormFields` with logic support

### 4. **FormContentWithLogic** (`components/FormLayout/FormContentWithLogic.tsx`)
Drop-in replacement for `FormContent` that enables logic system

## 📝 Usage Example

### 1. **Basic Setup**
```tsx
// Replace FormContent with FormContentWithLogic
<FormContentWithLogic 
  settings={settings}  // Contains fields with logic
  showNextButton={true}
  // ... other props
/>
```

### 2. **Field Logic Definition**
```typescript
const fieldWithLogic = {
  id: "field_123",
  type: "short_qa",
  label: "Additional Comments",
  logics: [
    {
      id: "logic_20241225_001",
      action: "show",  // or "hide"
      condition: {
        $and: [
          {
            "website_field_id": {
              "$ne": ""  // Website field is not empty
            }
          },
          {
            "rating_field_id": {
              "$in": [8, 9, 10]  // Rating is 8, 9, or 10
            }
          }
        ]
      }
    }
  ]
}
```

### 3. **Complex Logic Example**
```typescript
// Show field if:
// (Website contains "company" OR Rating > 7) AND Postcode starts with "SW"
condition: {
  $and: [
    {
      $or: [
        {
          "website_field": {
            $regex: "company",
            $options: "i"
          }
        },
        {
          "rating_field": {
            $gt: 7
          }
        }
      ]
    },
    {
      "postcode_field": {
        $regex: "^SW",
        $options: "i"
      }
    }
  ]
}
```

## ⚡ Performance Features

### 1. **Dependency Graph Optimization**
- Only evaluates fields that depend on changed values
- Prevents unnecessary calculations across 150+ fields

### 2. **Memoization**
- Caches evaluation results during session
- 500 entry limit prevents memory issues
- Cache hit ratio tracking for debugging

### 3. **On-Blur Evaluation**
- Logic triggers when user leaves field (not on every keystroke)
- Reduces computational overhead

### 4. **React.memo Optimization**
- Components only re-render when visibility actually changes
- Prevents cascade re-renders

## 🛠 Debugging

### Debug Panel (Development)
```tsx
import { useFormLogicContext } from '@/contexts/FormLogicContext';

const { getPerformanceStats, visibilityState } = useFormLogicContext();
const stats = getPerformanceStats();

console.log('Performance:', {
  totalFields: stats.totalFields,
  fieldsWithLogic: stats.fieldsWithLogic,
  cacheHitRatio: stats.cacheStats.hitRatio,
  visibleFields: Object.values(visibilityState).filter(Boolean).length
});
```

## 🔍 Field Condition Reference

| Human Condition | MongoDB Operator | Example |
|-----------------|------------------|---------|
| contains | `$regex` | `{ "field": { "$regex": "text" } }` |
| does not contain | `$not + $regex` | `{ "field": { "$not": { "$regex": "text" } } }` |
| starts with | `$regex` with `^` | `{ "field": { "$regex": "^prefix" } }` |
| ends with | `$regex` with `$` | `{ "field": { "$regex": "suffix$" } }` |
| is empty | `$eq` | `{ "field": { "$eq": "" } }` |
| is not empty | `$ne` | `{ "field": { "$ne": "" } }` |
| is any of | `$in` | `{ "field": { "$in": ["val1", "val2"] } }` |
| is not any of | `$nin` | `{ "field": { "$nin": ["val1", "val2"] } }` |

## 🎯 Best Practices

### 1. **Field Logic Design**
- ✅ Keep conditions simple and readable
- ✅ Use meaningful logic IDs with date format: `logic_YYYYMMDD_description_001`
- ✅ Test logic with various input combinations

### 2. **Performance**
- ✅ Limit logic rules to 5 conditions per field (as specified)
- ✅ Use specific field references, avoid broad dependencies
- ✅ Clear cache when form resets: `clearCache()`

### 3. **Maintenance**
- ✅ Document complex logic conditions
- ✅ Use consistent field naming
- ✅ Test edge cases (empty values, special characters)

## 🐛 Troubleshooting

### Common Issues

1. **Infinite Loop Error**
   - ✅ **Fixed:** useRef guard prevents multiple initializations
   - Check for unstable dependencies in useEffect

2. **Logic Not Triggering**
   - Verify field IDs match exactly
   - Check condition syntax (MongoDB-style)
   - Ensure FormLogicProvider wraps components

3. **Performance Issues**
   - Monitor cache hit ratio
   - Reduce complex nested conditions
   - Check dependency graph size

### Debug Commands
```tsx
// Check current state
console.log('Visibility State:', visibilityState);

// Performance stats
console.log('Logic Stats:', getPerformanceStats());

// Clear cache if needed
clearCache();
```

## 📚 Migration Guide

### From FormContent to FormContentWithLogic

**Before:**
```tsx
<FormContent settings={settings} />
```

**After:**
```tsx
<FormContentWithLogic settings={settings} />
```

That's it! The system is backward compatible and adds logic functionality transparently.

---

**🎉 Result:** Your forms now support sophisticated conditional logic with optimal performance for up to 150 fields!


=========================================================RELATIONSHIPS=======================================================================
# Field Logic Relationships Table

Based on `fakeDataWithLogic.ts`, here are all the conditional field relationships:

## 📋 Field Logic Relationships

| Field ID | Field Name | Field Type | Action | Condition | Human Readable |
|----------|------------|------------|--------|-----------|----------------|
| `01jfcjtrxg7dye6ybkzps85ec6` | Website | website | **SHOW** | `"01jfcjtv3qg98v8t31h7aa6gke": {"$regex": "^SW\|^N[0-9]\|^E[0-9]"}` | Show when Postcode starts with SW, N+number, or E+number |
| `01jfck4c2kcjpdy5y785vddgzw` | Type your question 1 here | short_qa | **SHOW** | `"01jfcjtrxg7dye6ybkzps85ec6": {"$ne": ""} AND "01jfcjtv3qg98v8t31h7aa6gke": {"$regex": "^[A-Z]{1,2}[0-9]"}` | Show when Website is not empty AND Postcode matches UK format |
| `01jfck4dz43cqhpv1d8jtvs37c` | Type your question 2 here | long_qa | **SHOW** | `"01jfck4c2kcjpdy5y785vddgzw": {"$regex": "interested\|yes\|positive"} OR "01jfcjtrxg7dye6ybkzps85ec6": {"$regex": "^https://"}` | Show when Short Q&A contains positive keywords OR Website starts with https |
| `01jfck4xjgjtbnhc4thw2d9857` | Type your question 5 here | rating | **SHOW** | `"01jfck4gdvv6p13b7t04cwkwxy": {"$in": [7,8,9,10]} AND "01jfck4dz43cqhpv1d8jtvs37c": {"$ne": ""} AND "01jfcjtrxg7dye6ybkzps85ec6": {"$exists": true}` | Show when Opinion Scale is 7-10 AND Long Q&A is filled AND Website exists |
| `01jfck5mn8ydcyb2sfd4jfg3pe` | Type your question 6 here | multiple_choice | **HIDE** | `"01jfck4gdvv6p13b7t04cwkwxy": {"$in": [1,2,3]} AND "01jfck4xjgjtbnhc4thw2d9857": {"$in": [1,2]}` | Hide when Opinion Scale is 1-3 AND Rating is 1-2 |
| `01jfck5p7hz8rwznkggeftz2tf` | Type your question 7 here | checkboxes | **SHOW** | Complex OR condition (see details below) | Show when ANY of 5 conditions are met |
| `01jfck5ra7aa922d0ydcpx4va6` | Type your question 8 here | dropdown | **SHOW** | `"01jfck5p7hz8rwznkggeftz2tf": {"$in": ["オプション223", "オプション321111111111222"]} AND "01jfck4xjgjtbnhc4thw2d9857": {"$in": [6,7,8,9,10]} AND "01jfck4gdvv6p13b7t04cwkwxy": {"$in": [6,7,8,9,10]}` | Show when specific Checkboxes selected AND both Rating & Opinion Scale are 6+ |
| `01jfck5xdnb3sk45t8vvg67gft` | Type your question 10 here | legal | **SHOW** | `"01jfck5tcrfzj4gh89yd7rtn77": {"$eq": "Yes"} AND "01jfck5ra7aa922d0ydcpx4va6": {"$ne": ""}` | Show when Yes/No is "Yes" AND Dropdown is selected |
| `01k49xq0phkrthc190mt708cjc` | Type your question here | short_qa | **SHOW** | `"01jfck5xdnb3sk45t8vvg67gft": {"$eq": "I accept"} AND "01jmbk0m3j3812n3ck1x53ekpy": {"$ne": ""}` | Show when Legal terms accepted AND Name is filled |
| `01k4m416s73x45rr4gtt1bazaj` | my phone | phone_number | **SHOW** | `"01jfck5z2gttawbr1ad2aa0sp7": {"$eq": "<p>noooo1112</p>"} OR "01jfck5xdnb3sk45t8vvg67gft": {"$eq": "I accept"}` | Show when Checkbox checked OR Legal terms accepted |

---

## 🔍 Detailed Breakdown

### Complex Conditions

#### Checkboxes Field (`01jfck5p7hz8rwznkggeftz2tf`) - Show when ANY of:
1. **Multiple Choice** = "オプション1" 
2. **Rating** ∈ [8, 9, 10]
3. **Opinion Scale** ∈ [8, 9, 10] 
4. **Website** contains "github|portfolio|company"
5. **Short Q&A** contains "professional|business|work"

---

## 📊 Field Dependencies Summary

### Fields WITHOUT Logic (Always Visible):
- `01jfcjtv3qg98v8t31h7aa6gke` - Location Postcode
- `01jfck4gdvv6p13b7t04cwkwxy` - Type your question 3 here (Opinion Scale)
- `01jfck5tcrfzj4gh89yd7rtn77` - Type your question 9 here (Yes/No)
- `01jfck5z2gttawbr1ad2aa0sp7` - Type your description (Checkbox)
- `01jmbk0m3j3812n3ck1x53ekpy` - Name field

### Most Referenced Fields (Dependencies):
1. **Website** (`01jfcjtrxg7dye6ybkzps85ec6`) - Referenced by 5 other fields
2. **Opinion Scale** (`01jfck4gdvv6p13b7t04cwkwxy`) - Referenced by 3 other fields
3. **Rating** (`01jfck4xjgjtbnhc4thw2d9857`) - Referenced by 2 other fields

---

## 🎯 Logic Flow Visualization

```
Postcode (UK format) ──┐
                       ├─→ Website Field (show)
Website (not empty) ────┘

Website (not empty) ────┐
                        ├─→ Short Q&A (show)
Postcode (UK format) ───┘

Short Q&A (positive) ───┐
                        ├─→ Long Q&A (show)
Website (https) ─────────┘

Opinion Scale (7-10) ────┐
Long Q&A (filled) ───────┼─→ Rating (show)
Website (exists) ────────┘

Opinion Scale (1-3) ─────┐
                         ├─→ Multiple Choice (hide)
Rating (1-2) ────────────┘

[Multiple conditions] ───────→ Checkboxes (show)

Checkboxes (specific) ───┐
Rating (6+) ─────────────┼─→ Dropdown (show)
Opinion Scale (6+) ──────┘

Yes/No (Yes) ────────────┐
                         ├─→ Legal (show)
Dropdown (selected) ─────┘

Legal (accepted) ────────┐
                         ├─→ Extra Q&A (show)
Name (filled) ───────────┘

Checkbox (checked) ──────┐
                         ├─→ Phone (show)
Legal (accepted) ────────┘
```

---

## 📈 Statistics

- **Total Fields**: 15
- **Fields with Logic**: 10 (67%)
- **Always Visible Fields**: 5 (33%)
- **Show Actions**: 9
- **Hide Actions**: 1
- **Simple Conditions**: 6
- **Complex Conditions**: 4
- **Max Dependencies per Field**: 5 (Checkboxes)

---

This table shows the complete conditional logic relationships in your form! 🎯
