# Form Logic System - Data Structures

## 📊 Overview

This document explains the key data structures used in the Form Logic System for managing conditional field visibility.

---

## 1. 🎯 **conditionResult**

**Type:** `boolean`

**Purpose:** Result of evaluating a single logic condition against form data.

### Structure:
```typescript
const conditionResult: boolean = true | false;
```

### Examples:

#### ✅ **True Result:**
```javascript
// Form data
const formData = {
  "website_field": "https://company.com",
  "postcode_field": "SW1 1AA"
};

// Condition: Website starts with https
const condition = {
  "website_field": {
    "$regex": "^https://"
  }
};

const conditionResult = true; // ✅ Condition matches
```

#### ❌ **False Result:**
```javascript
// Form data
const formData = {
  "website_field": "http://company.com", // Not HTTPS
  "postcode_field": "SW1 1AA"
};

// Condition: Website starts with https
const condition = {
  "website_field": {
    "$regex": "^https://"
  }
};

const conditionResult = false; // ❌ Condition doesn't match
```

---

## 2. 👁️ **initialVisibility**

**Type:** `VisibilityState` (Record<string, boolean>)

**Purpose:** Initial visibility state for all fields with logic when form first loads.

### Structure:
```typescript
interface VisibilityState {
  [fieldId: string]: boolean;
}

const initialVisibility: VisibilityState = {
  "field_id_1": true,
  "field_id_2": false,
  "field_id_3": true,
  // ... more fields
};
```

### Real Example:
```javascript
const initialVisibility = {
  // Short Q&A field - depends on website + postcode
  "01jfck4c2kcjpdy5y785vddgzw": false,  // Hidden initially
  
  // Long Q&A field - depends on keywords OR https
  "01jfck4dz43cqhpv1d8jtvs37c": false,  // Hidden initially
  
  // Rating field - depends on opinion scale + other fields
  "01jfck4xjgjtbnhc4thw2d9857": false,  // Hidden initially
  
  // Legal field - depends on yes/no + dropdown
  "01jfck5xdnb3sk45t8vvg67gft": true,   // Visible initially
};
```

### Usage Flow:
```typescript
// 1. Form loads
const fieldsWithLogic = fields.filter(field => field.logics?.length > 0);

// 2. Evaluate each field's logic against empty form data
fieldsWithLogic.forEach(field => {
  initialVisibility[field.id] = evaluateFieldLogic(field, {});
});

// 3. Set visibility state
setVisibilityState(initialVisibility);
```

---

## 3. 🕸️ **dependencyGraph**

**Type:** `DependencyGraph` (Record<string, string[]>)

**Purpose:** Maps each field to the list of other fields it depends on for its visibility logic.

### Structure:
```typescript
interface DependencyGraph {
  [fieldId: string]: string[]; // Field ID -> Array of dependency field IDs
}
```

### Real Example:
```javascript
const dependencyGraph = {
  // Short Q&A depends on website AND postcode
  "01jfck4c2kcjpdy5y785vddgzw": [
    "01jfcjtrxg7dye6ybkzps85ec6", // website field
    "01jfcjtv3qg98v8t31h7aa6gke"  // postcode field
  ],
  
  // Long Q&A depends on short Q&A OR website
  "01jfck4dz43cqhpv1d8jtvs37c": [
    "01jfck4c2kcjpdy5y785vddgzw", // short Q&A field
    "01jfcjtrxg7dye6ybkzps85ec6"  // website field
  ],
  
  // Rating depends on opinion scale + long Q&A + website
  "01jfck4xjgjtbnhc4thw2d9857": [
    "01jfck4gdvv6p13b7t04cwkwxy", // opinion scale field
    "01jfck4dz43cqhpv1d8jtvs37c", // long Q&A field
    "01jfcjtrxg7dye6ybkzps85ec6"  // website field
  ]
};
```

### How It's Built:
```typescript
function buildDependencyGraph(fields: any[]): DependencyGraph {
  const graph: DependencyGraph = {};
  
  fields.forEach(field => {
    if (field.logics && field.logics.length > 0) {
      const dependencies = new Set<string>();
      
      field.logics.forEach(logic => {
        // Extract field IDs from condition
        const fieldIds = extractFieldIds(logic.condition);
        fieldIds.forEach(id => dependencies.add(id));
      });
      
      graph[field.id] = Array.from(dependencies);
    }
  });
  
  return graph;
}
```

### Logic Condition → Dependencies:
```javascript
// This logic condition:
{
  "$and": [
    {"website_field": {"$ne": ""}},
    {"postcode_field": {"$regex": "^SW"}}
  ]
}

// Creates these dependencies:
dependencyGraph["target_field"] = ["website_field", "postcode_field"];
```

---

## 4. 🔄 **reverseDepGraph** (Reverse Dependency Graph)

**Type:** `DependencyGraph` (Record<string, string[]>)

**Purpose:** Maps each field to the list of fields that depend on it (opposite of dependencyGraph).

### Structure:
```typescript
interface DependencyGraph {
  [fieldId: string]: string[]; // Field ID -> Array of fields that depend on it
}
```

### Real Example:
```javascript
const reverseDepGraph = {
  // When website field changes, these fields need re-evaluation
  "01jfcjtrxg7dye6ybkzps85ec6": [
    "01jfck4c2kcjpdy5y785vddgzw", // short Q&A
    "01jfck4dz43cqhpv1d8jtvs37c", // long Q&A
    "01jfck4xjgjtbnhc4thw2d9857", // rating
    "01jfck5p7hz8rwznkggeftz2tf"  // checkboxes
  ],
  
  // When postcode changes, only short Q&A needs re-evaluation
  "01jfcjtv3qg98v8t31h7aa6gke": [
    "01jfck4c2kcjpdy5y785vddgzw"  // short Q&A
  ],
  
  // When opinion scale changes, rating field needs re-evaluation
  "01jfck4gdvv6p13b7t04cwkwxy": [
    "01jfck4xjgjtbnhc4thw2d9857"  // rating
  ]
};
```

### How It's Built:
```typescript
function buildReverseDependencyGraph(dependencyGraph: DependencyGraph): DependencyGraph {
  const reverseGraph: DependencyGraph = {};
  
  Object.entries(dependencyGraph).forEach(([fieldId, dependencies]) => {
    dependencies.forEach(depId => {
      if (!reverseGraph[depId]) {
        reverseGraph[depId] = [];
      }
      reverseGraph[depId].push(fieldId);
    });
  });
  
  return reverseGraph;
}
```

### Performance Optimization:
```typescript
// When user changes website field:
const changedFieldId = "01jfcjtrxg7dye6ybkzps85ec6";

// Find affected fields using reverse dependency graph
const affectedFields = reverseDepGraph[changedFieldId] || [];
// Result: ["01jfck4c2kcjpdy5y785vddgzw", "01jfck4dz43cqhpv1d8jtvs37c", ...]

// Only re-evaluate these 4 fields instead of all 150 fields!
affectedFields.forEach(fieldId => {
  const field = fields.find(f => f.id === fieldId);
  const newVisibility = evaluateFieldLogic(field, formData);
  // Update visibility...
});
```

---

## 🔄 **Complete Data Flow Example**

### Scenario: User enters website URL

```javascript
// 1. USER ACTION
// User types "https://company.com" in website field

// 2. FIND AFFECTED FIELDS
const changedFieldId = "01jfcjtrxg7dye6ybkzps85ec6"; // website
const affectedFields = reverseDepGraph[changedFieldId];
// ["01jfck4c2kcjpdy5y785vddgzw", "01jfck4dz43cqhpv1d8jtvs37c", ...]

// 3. RE-EVALUATE AFFECTED FIELDS
const newVisibility = {};

affectedFields.forEach(fieldId => {
  const field = fields.find(f => f.id === fieldId);
  
  // Evaluate each logic condition
  field.logics.forEach(logic => {
    const conditionResult = evaluateCondition(logic.condition, formData);
    // conditionResult: true or false
    
    if (logic.action === 'show') {
      newVisibility[fieldId] = conditionResult;
    } else if (logic.action === 'hide') {
      newVisibility[fieldId] = !conditionResult;
    }
  });
});

// 4. UPDATE VISIBILITY STATE
setVisibilityState(prev => ({ ...prev, ...newVisibility }));

// 5. REACT RE-RENDERS
// Only affected fields re-render based on new visibility
```

---

## 📈 **Performance Metrics**

### Example with 150 fields:

```javascript
// Total fields in form
const totalFields = 150;

// Fields with logic conditions
const fieldsWithLogic = 25;

// Dependency connections
const dependencyConnections = Object.values(dependencyGraph).flat().length;
// Example: 45 total dependencies

// When website field changes:
const directlyAffected = reverseDepGraph["website_field"].length;
// Example: 8 fields need re-evaluation

// Performance gain:
// Without dependency graph: 150 evaluations
// With dependency graph: 8 evaluations
// Improvement: 94% reduction in computations!
```

---

## 🛠️ **Debugging Data Structures**

### Console Commands:
```javascript
// Check current visibility state
console.log('Current Visibility:', visibilityState);

// Check which fields depend on website
console.log('Website Dependencies:', reverseDepGraph["01jfcjtrxg7dye6ybkzps85ec6"]);

// Check what short Q&A depends on
console.log('Short Q&A Dependencies:', dependencyGraph["01jfck4c2kcjpdy5y785vddgzw"]);

// Test condition evaluation
const testCondition = { "website_field": { "$ne": "" } };
const result = evaluateCondition(testCondition, formData);
console.log('Condition Result:', result);
```

---

## 🎯 **Key Takeaways**

1. **`conditionResult`**: Simple boolean result of evaluating one condition
2. **`initialVisibility`**: Starting visibility state for all fields with logic  
3. **`dependencyGraph`**: "Field X depends on fields Y, Z" (forward mapping)
4. **`reverseDepGraph`**: "Field Y affects fields A, B, C" (reverse mapping)

These structures work together to provide **efficient, real-time conditional logic** with **optimal performance** for large forms! 🚀
