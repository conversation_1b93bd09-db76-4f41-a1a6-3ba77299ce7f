import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import {
  FieldType,
  type FormField,
  FormLayoutType,
  type FormSettings,
  GroupFieldType,
  ValidatorType,
} from '@/types/form-builder';
import {
  getAcceptTypes,
  getAddressFromPostalCode,
  getDefaultValues,
  getGridColsByLayout,
  getGridColsByWidth,
  getOtherAnswerValue,
  getPostalCodeData,
  getPrefecturesOptions,
  getValidationSchema,
  hasReachedSubmissionLimit,
  hasRequiredFields,
  hasRequiredRule,
  isExpiredForm,
  isNotOpenForm,
  isValidDateSelector,
  showErrorToast,
} from '@/utils';
import { notifications } from '@mantine/notifications';
import { themeConfigurations } from '@resola-ai/ui/constants';
// utils.test.ts
import { type Mock, beforeAll, describe, expect, it, vi } from 'vitest';

vi.mock('next-i18next', () => ({
  i18n: { t: (key: string) => key },
}));

const exampleFormSettings: FormSettings = {
  id: 'form-123',
  name: 'Example Form',
  description: 'This is an example form',
  setting: {
    submission: {
      mode: 'redirect',
      message: 'Thank you for submission',
      caption: 'Form submitted successfully',
      button: 'Submit',
      redirectUrl: 'https://example.com',
      enableBranding: true,
      limitResponse: false,
      limitNumber: 0,
    },
    notification: {
      isAutoresponse: true,
    },
    behavior: {
      isMultipleResponse: false,
    },
    systemMessage: [
      {
        type: 'success',
        body: { en: 'Success message' },
        heading: { en: 'Success' },
      },
    ],
  },
  metadata: {
    organizationId: 'org-123',
    workspaceId: 'ws-123',
  },
  status: 'active',
  permissions: ['read', 'write'],
  urls: {
    public: 'https://forms.example.com/public/form-123',
    embed: 'https://forms.example.com/embed/form-123',
    private: 'https://forms.example.com/private/form-123',
  },
  tags: ['example', 'demo'],
  expiredAt: '2025-12-31T23:59:59Z',
  createdAt: '2025-01-01T00:00:00Z',
  updatedAt: '2025-01-01T00:00:00Z',
  isFavorited: false,
  isPinned: false,
  appearance: {
    headingStyle: {
      fontFamily: 'Arial',
      fontSize: 24,
      color: '#000000',
    },
    paragraphStyle: {
      fontFamily: 'Arial',
      fontSize: 16,
      color: '#000000',
    },
    buttonStyle: {
      type: 'filled',
      fullWidth: false,
      backgroundColor: '#0000FF',
      textColor: '#FFFFFF',
      fontFamily: 'Arial',
      fontSize: 16,
    },
    formFieldStyle: {
      color: {
        placeholder: '#666666',
        question: '#000000',
        answer: '#000000',
        icon: '#000000',
        description: '#666666',
        fieldStroke: '#CCCCCC',
        fieldBackGround: '#FFFFFF',
      },
      fontFamily: {
        placeholder: 'Arial',
        question: 'Arial',
        text: 'Arial',
        answer: 'Arial',
      },
      fontSize: {
        placeholder: '14px',
        question: 16,
        text: 14,
        answer: 14,
      },
    },
    defaultSettings: {
      color: '#0000FF',
      font: 'Arial',
      inputStyle: InputStyle.Classic,
    },
    customize: false,
    headerStyle: {
      position: 'top',
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Large,
      logoAlign: AppearanceSettingsLogoAlign.Center,
      isUsingText: true,
      text: 'Form Header',
    },
    footerStyle: {
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Large,
      logoAlign: AppearanceSettingsLogoAlign.Center,
      isUsingText: true,
      text: 'Form Footer',
    },
  },
  responses: 0,
  screenshot: {
    original: 'https://example.com/screenshots/original.jpg',
    thumbnail: 'https://example.com/screenshots/thumbnail.jpg',
    preview: null,
  },
  startAt: '2025-01-01T00:00:00Z',
};

const ymd = (d: Date) => {
  const yyyy = d.getFullYear();
  const mm = String(d.getMonth() + 1).padStart(2, '0');
  const dd = String(d.getDate()).padStart(2, '0');
  return `${yyyy}-${mm}-${dd}`;
};

describe('isValidDateSelector', () => {
  it('returns true for a valid date', () => {
    expect(isValidDateSelector('2023-03-15', false)).toBe(true);
  });

  it('handles leap years correctly', () => {
    expect(isValidDateSelector('2024-02-29', false)).toBe(true);
    expect(isValidDateSelector('2023-02-29', false)).toBe(false);
  });

  it('rejects invalid month/day combinations', () => {
    expect(isValidDateSelector('2023-13-01', false)).toBe(false);
    expect(isValidDateSelector('2023-04-31', false)).toBe(false);
  });

  it('disallows future dates when isDob is true', () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    expect(isValidDateSelector(ymd(today), true)).toBe(true);
    expect(isValidDateSelector(ymd(tomorrow), true)).toBe(false);
  });
});

describe('getGridColsByLayout', () => {
  it('maps known layouts correctly', () => {
    expect(getGridColsByLayout('one_column')).toBe(12);
    expect(getGridColsByLayout('two_column')).toBe(6);
  });

  it('returns undefined for unknown layouts', () => {
    expect(getGridColsByLayout('three_column')).toBeUndefined();
  });
});

describe('getGridColsByWidth', () => {
  it('calculates columns from percent width', () => {
    expect(getGridColsByWidth('100%')).toBe(12);
    expect(getGridColsByWidth('50%')).toBe(6);
    expect(getGridColsByWidth('25%')).toBe(3);
  });

  it('uses parseInt behavior for fractional percentages', () => {
    // parseInt('33.3%') -> 33, 33 * 12 / 100 = 3.96
    expect(getGridColsByWidth('33.3%')).toBeCloseTo(3.96, 2);
    // parseInt('12.5%') -> 12, 12 * 12 / 100 = 1.44
    expect(getGridColsByWidth('12.5%')).toBeCloseTo(1.44, 2);
  });

  it('defaults to 12 when width is falsy', () => {
    expect(getGridColsByWidth(undefined)).toBe(12);
    expect(getGridColsByWidth('')).toBe(12);
    expect(getGridColsByWidth(null as unknown as string)).toBe(12);
  });
});

describe('getValidationSchema', () => {
  beforeAll(() => {
    vi.clearAllMocks();
  });
  it('returns an empty schema when there are no visible validated questions', () => {
    const questions: any[] = [
      { id: 'hidden', type: FieldType.ShortQA, isHide: true, validators: [{ type: 'required' }] },
      { id: 'noValidators', type: FieldType.ShortQA, isHide: false, validators: [] },
    ];

    const schema = getValidationSchema(questions);
    const result = schema.safeParse({ any: 'value' });
    // z.object({}) should not fail; it ignores unknown keys by default
    expect(result.success).toBe(true);
    // Parsed data should be an empty object
    expect(result.success && result.data).toEqual({});
  });

  it('includes nested fields from group questions and enforces Legal field requirements', () => {
    const questions: any[] = [
      {
        id: 'group1',
        type: GroupFieldType.Name,
        isHide: false,
        fields: [
          // A visible Legal field should always be included
          { id: 'terms', type: FieldType.Legal, isHide: false, validators: [] },
        ],
      },
    ];

    const schema = getValidationSchema(questions);

    // Missing required Legal field should fail
    const missing = schema.safeParse({});
    expect(missing.success).toBe(false);

    // Providing a valid Legal value (array with at least one item with value) should pass
    const ok = schema.safeParse({
      terms: [{ value: 'accepted' }],
    });
    expect(ok.success).toBe(true);

    // Providing an empty array for Legal should fail .min(1)
    const empty = schema.safeParse({ terms: [] });
    expect(empty.success).toBe(false);
  });

  it('validates multiple choice fields correctly', () => {
    const questions = [
      {
        id: 'choice',
        type: FieldType.MultipleChoice,
        isHide: false,
        validators: [{ type: 'required' }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(schema.safeParse({ value: ['option1'] }).success).toBe(true);
  });

  it('validates yes/no fields correctly', () => {
    const questions = [
      {
        id: 'yesno',
        type: FieldType.YesNo,
        isHide: false,
        validators: [{ type: 'required' }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(schema.safeParse({ value: true }).success).toBe(true);
    expect(schema.safeParse({ value: false }).success).toBe(true);
  });

  it('validates checkboxes fields correctly', () => {
    const questions = [
      {
        id: 'checks',
        type: FieldType.Checkboxes,
        isHide: false,
        validators: [{ type: 'required', value: true }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(
      schema.safeParse({
        checks: {
          value: 'option1',
        },
      }).success
    ).toBe(false);
  });

  it('validates checkboxes fields correctly, no require', () => {
    const questions = [
      {
        id: 'checks',
        type: FieldType.Checkboxes,
        isHide: false,
        validators: [{ type: 'required', value: false }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(
      schema.safeParse({
        checks: [
          {
            value: 'option1',
          },
        ],
      }).success
    ).toBe(true);
  });

  it('validates single checkbox fields correctly', () => {
    const questions = [
      {
        id: 'check',
        type: FieldType.Checkbox,
        isHide: false,
        validators: [{ type: 'required', value: true }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(schema.safeParse({ check: true }).success).toBe(true);
  });

  it('validates single checkbox fields correctly, no-required', () => {
    const questions = [
      {
        id: 'check',
        type: FieldType.Checkbox,
        isHide: false,
        validators: [{ type: 'required', value: false }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(schema.safeParse({ check: true }).success).toBe(true);
  });

  it('validates dropdown fields correctly', () => {
    const questions = [
      {
        id: 'dropdown',
        type: FieldType.Dropdown,
        isHide: false,
        validators: [{ type: 'required', value: true }],
      },
      {
        id: 'dropdown2',
        type: FieldType.Dropdown,
        isHide: false,
        validators: [{ type: 'required', value: false }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(schema.safeParse({ dropdown: 'option1' }).success).toBe(true);
  });

  it('validates date selector fields correctly', () => {
    const questions = [
      {
        id: 'date',
        type: FieldType.DateSelector,
        isHide: false,
        validators: [{ type: 'required' }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(schema.safeParse({ date: '2023-01-01' }).success).toBe(true);
  });

  it('validates date time fields correctly', () => {
    const questions = [
      {
        id: 'datetime',
        type: FieldType.DateTime,
        isHide: false,
        validators: [{ type: 'required' }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(schema.safeParse({ value: '2023-01-01T12:00' }).success).toBe(true);
  });

  it('validates date range fields correctly', () => {
    const questions = [
      {
        id: 'daterange',
        type: FieldType.DateRange,
        isHide: false,
        validators: [{ type: 'required' }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(schema.safeParse({ value: ['2023-01-01', '2023-01-02'] }).success).toBe(true);
  });

  it('validates phone number fields correctly', () => {
    const questions = [
      {
        id: 'phone',
        type: FieldType.PhoneNumber,
        isHide: false,
        validators: [{ type: 'required' }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(schema.safeParse({ value: '+1234567890' }).success).toBe(true);
  });

  it('validates opinion scale fields correctly', () => {
    const questions = [
      {
        id: 'opinion',
        type: FieldType.OpinionScale,
        isHide: false,
        validators: [{ type: 'required' }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(schema.safeParse({ value: 5 }).success).toBe(true);
  });

  it('validates file uploader fields correctly', () => {
    const questions = [
      {
        id: 'file',
        type: FieldType.FileUploader,
        isHide: false,
        validators: [{ type: 'required', value: true }],
      },
      {
        id: 'file2',
        type: FieldType.FileUploader,
        isHide: false,
        validators: [{ type: 'required', value: false }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(schema.safeParse({ file: [{ name: 'test.pdf', size: 1024 }] }).success).toBe(true);
  });

  it('validates rating fields correctly', () => {
    const questions = [
      {
        id: 'rating',
        type: FieldType.Rating,
        isHide: false,
        validators: [{ type: 'required' }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(schema.safeParse({ value: 4 }).success).toBe(true);
  });

  it('validates fields with email validator correctly', () => {
    const questions = [
      {
        id: 'email',
        type: FieldType.ShortQA,
        isHide: false,
        validators: [{ type: 'email' }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(schema.safeParse({ email: '<EMAIL>' }).success).toBe(true);
    expect(schema.safeParse({ email: 'invalid-email' }).success).toBe(false);
  });

  it('validates fields with required validator correctly', () => {
    const questions = [
      {
        id: 'required',
        type: FieldType.ShortQA,
        isHide: false,
        validators: [{ type: 'required', value: true }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(schema.safeParse('').success).toBe(false);
  });

  it('validates fields with minlength validator correctly', () => {
    const questions = [
      {
        id: 'minlength',
        type: FieldType.ShortQA,
        isHide: false,
        validators: [{ type: 'minLength', value: 3 }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(schema.safeParse({ value: 'abc' }).success).toBe(true);
  });

  it('validates fields with minValue validator correctly', () => {
    const questions = [
      {
        id: 'minvalue',
        type: FieldType.ShortQA,
        isHide: false,
        validators: [{ type: 'minValue', value: 5 }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(schema.safeParse({ value: 6 }).success).toBe(true);
  });

  it('validates fields with maxLength validator correctly', () => {
    const questions = [
      {
        id: 'maxlength',
        type: FieldType.ShortQA,
        isHide: false,
        validators: [{ type: 'maxLength', value: 5 }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(schema.safeParse({ value: 'abcd' }).success).toBe(true);
  });

  it('validates fields with maxValue validator correctly', () => {
    const questions = [
      {
        id: 'maxvalue',
        type: FieldType.ShortQA,
        isHide: false,
        validators: [{ type: 'maxValue', value: 10 }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(schema.safeParse({ value: 9 }).success).toBe(true);
  });

  it('validates fields with pattern validator correctly', () => {
    const questions = [
      {
        id: 'pattern',
        type: FieldType.ShortQA,
        isHide: false,
        validators: [{ type: 'pattern', value: '^[0-9]+$' }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(schema.safeParse({ value: '123' }).success).toBe(true);
  });

  it('validates fields with hiragana validator correctly', () => {
    const questions = [
      {
        id: 'hiragana',
        type: FieldType.ShortQA,
        isHide: false,
        validators: [{ type: 'hiragana' }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(schema.safeParse({ value: 'ひらがな' }).success).toBe(true);
  });

  it('validates fields with katakana validator correctly', () => {
    const questions = [
      {
        id: 'katakana',
        type: FieldType.ShortQA,
        isHide: false,
        validators: [{ type: 'katakana' }],
      },
    ];

    const schema = getValidationSchema(questions);
    expect(schema.safeParse({ value: 'カタカナ' }).success).toBe(true);
  });

  describe('hasRequiredRule', () => {
    it('returns true when required validator is present', () => {
      const validators = [{ type: ValidatorType.Required, value: true }];
      const formField: FormField = {
        id: 'id',
        type: FieldType.ShortQA,
        label: 'label',
        name: 'name',
        validators,
      };
      expect(hasRequiredRule(formField)).toBe(true);
    });

    it('returns false when required validator is not present', () => {
      const validators = [{ type: ValidatorType.Email }];
      const formField: FormField = {
        id: 'id',
        type: FieldType.ShortQA,
        label: 'label',
        name: 'name',
        validators,
      };
      expect(hasRequiredRule(formField)).toBe(false);
    });
  });

  describe('getPrefecturesOptions', () => {
    it('returns array of prefecture options', () => {
      const options = getPrefecturesOptions();
      expect(Array.isArray(options)).toBe(true);
      expect(options.length).toBeGreaterThan(0);
      expect(options[0]).toHaveProperty('label');
    });
  });

  describe('getPostalCodeData', () => {
    it('handles postal code without hyphen', async () => {
      ((global as any).fetch as Mock).mockResolvedValueOnce({
        json: vi.fn().mockResolvedValueOnce({
          results: ['1234567'],
        }),
      });
      const result = await getPostalCodeData('1234567');
      expect(result).toBe('1234567');
    });
    it('handles postal code failed', async () => {
      ((global as any).fetch as Mock).mockResolvedValueOnce({
        json: vi.fn().mockResolvedValueOnce({}),
      });
      const result = await getPostalCodeData('1234567');
      expect(result).toBe(null);
    });
  });

  describe('getAddressFromPostalCode', () => {
    it('fetches address data from postal code API', async () => {
      ((global as any).fetch as Mock).mockResolvedValueOnce({
        json: vi.fn().mockResolvedValueOnce({
          results: [{ address1: 'Tokyo', address2: 'Shibuya', address3: 'Some Location' }],
        }),
      });

      const result = await getAddressFromPostalCode('1234567');
      expect(result).toEqual({
        city: 'Shibuya',
        street: 'Some Location',
      });
    });
    it('fetches address data from postal code API failed', async () => {
      ((global as any).fetch as Mock).mockResolvedValueOnce({
        json: vi.fn().mockRejectedValue({}),
      });

      const result = await getAddressFromPostalCode('1234567');
      expect(result).toEqual({ prefecture: '', address: '' });
    });
  });

  describe('hasRequiredFields', () => {
    it('returns true when form has required fields', () => {
      const fields = [{ validators: [{ type: 'required', value: true }] }];
      expect(hasRequiredFields(fields)).toBe(true);
    });

    it('returns false when form has no required fields', () => {
      const fields = [{ fields: [{ validators: [{ type: 'email' }] }] }];
      expect(hasRequiredFields(fields)).toBe(false);
    });
  });

  describe('isExpiredForm', () => {
    it('returns true for expired forms', () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);
      expect(isExpiredForm(pastDate.toISOString())).toBe(true);
    });

    it('returns false for active forms', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);
      expect(isExpiredForm(futureDate.toISOString())).toBe(false);
    });
  });

  describe('hasReachedSubmissionLimit', () => {
    it('returns false when submissions not reach limit', () => {
      expect(hasReachedSubmissionLimit(exampleFormSettings)).toBe(false);
    });

    it('returns true when submissions reach limit', () => {
      const submission = {
        limitResponse: true,
        limitNumber: 0,
      };
      expect(
        hasReachedSubmissionLimit({
          ...exampleFormSettings,
          setting: {
            ...exampleFormSettings.setting,
            submission: { ...exampleFormSettings.setting.submission, ...submission },
          },
        })
      ).toBe(true);
    });
  });

  describe('isNotOpenForm', () => {
    it('returns true when form is not yet open', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);
      expect(isNotOpenForm(futureDate.toISOString())).toBe(true);
    });

    it('returns false when form is open', () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);
      expect(isNotOpenForm(pastDate.toISOString())).toBe(false);
    });
  });

  describe('showErrorToast', () => {
    it('displays error toast message', () => {
      vi.spyOn(notifications, 'show').mockImplementation((params: any): any => {
        params.styles(themeConfigurations);
      });
      showErrorToast('Error message');

      vi.restoreAllMocks();
    });
  });

  describe('getOtherAnswerValue', () => {
    it('returns other answer value when present', () => {
      expect(getOtherAnswerValue('')).toBe('otherOptionLabel');
    });

    it('returns empty string when no other answer', () => {
      const answer = { value: 'option' };
      expect(getOtherAnswerValue(answer.value)).toBe('otherOptionLabel: option');
    });
  });

  describe('getAcceptTypes', () => {
    it('returns correct mime types for images', () => {
      expect(getAcceptTypes(['image'])).toEqual(['image/png', 'image/gif', 'image/jpeg']);
    });
  });

  describe('getDefaultValues', () => {
    it('returns empty object when no fields provided', () => {
      expect(getDefaultValues([])).toEqual({});
    });

    it('returns object with default values for fields', () => {
      const fields: any = [
        {
          id: 'text',
          name: 'text',
          type: FieldType.Section,
          layout: {
            type: FormLayoutType.ImageLeft,
            imageUrl: null,
            fieldWidth: '100px',
          },
          content: [
            {
              id: 'id',
              type: FieldType.ShortQA,
              label: 'label',
              name: 'name',
              validators: [],
            },
            {
              id: 'id2',
              type: FieldType.ShortQA,
              label: 'label',
              name: 'name',
              validators: [],
              options: [
                {
                  defaultCheck: true,
                  label: 'hello',
                },
              ],
            },
            {
              id: 'id3',
              type: FieldType.ShortQA,
              label: 'label',
              name: 'name',
              validators: [],
              fields: [
                {
                  id: 'id4',
                  type: FieldType.ShortQA,
                  label: 'label',
                  name: 'name',
                  validators: [],
                },
              ],
            },
          ],
        },
      ];
      expect(getDefaultValues(fields)).toEqual({
        id: '', // ShortQA field gets empty string default
        id2: 'hello', // ShortQA field with defaultCheck option gets that value
        id3: '', // ShortQA field gets empty string default
        id4: '', // Nested ShortQA field gets empty string default
      });
    });
  });
});
