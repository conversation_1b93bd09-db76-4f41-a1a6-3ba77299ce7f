import type {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
/* eslint-disable no-unused-vars */
import type { InputStyle } from '@/types/enum/inputStyle';

export interface FormAppearance {
  headingStyle: {
    fontFamily: string;
    fontSize: number;
    color: string;
  };
  paragraphStyle: {
    fontFamily: string;
    fontSize: number;
    color: string;
  };
  buttonStyle: {
    type: string;
    fullWidth: boolean;
    backgroundColor: string;
    textColor: string;
    fontFamily: string;
    fontSize: number;
  };
  formFieldStyle: {
    color: {
      placeholder: string;
      question: string;
      answer: string;
      icon: string;
      description: string;
      fieldStroke: string;
      fieldBackGround: string;
    };
    fontFamily: {
      placeholder: string;
      question: string;
      text: string;
      answer: string;
    };
    fontSize: {
      placeholder: string;
      question: number;
      text: number;
      answer: number;
    };
  };
  defaultSettings: {
    color: string;
    font: string;
    inputStyle: InputStyle;
  };
  customize: boolean;
  headerStyle: {
    position: string;
    logoImage: File | string;
    logoSize: AppearanceSettingsLogoSize;
    logoAlign: AppearanceSettingsLogoAlign;
    isUsingText: boolean;
    text: string;
  };
  footerStyle: {
    logoImage: File | string;
    logoSize: AppearanceSettingsLogoSize;
    logoAlign: AppearanceSettingsLogoAlign;
    isUsingText: boolean;
    text: string;
  };
}

export interface FormStyle {
  fontFamily?: string;
  fontSize?: string;
  borderColor?: string;
  backgroundColor?: string;
}

export interface SectionStyle extends FormStyle {}

export interface FormFieldStyle extends FormStyle {
  width?: number | string;
}

export interface FormSectionLayout {
  type: FormLayoutType;
  imageUrl: string | null;
  fieldWidth: string;
  backgroundTransparency?: string;
}

export enum FormLayoutType {
  ImageTopWithSpace = 'image-top-with-space',
  ImageLeft = 'image-left',
  ImageRight = 'image-right',
  ImageTop = 'image-top',
  ImageBottom = 'image-bottom',
  ImageBackground = 'image-background',
}

// Form Section mostly used for page of form, layout purpose
export interface FormSection {
  id: string;
  type: FieldType.Section | FieldType.Hidden;
  name: string;
  layout: FormSectionLayout;
  content: FormDataContent[];
}

export enum FieldType {
  Email = 'email',
  Checkbox = 'checkbox',
  Checkboxes = 'checkboxes',
  MultipleChoice = 'multiple_choice',
  Radio = 'radio',
  Date = 'date',
  Time = 'time',
  DateTime = 'datetime',
  DateRange = 'date_range',
  DateSelector = 'date_selector',
  Number = 'number',
  Section = 'section',
  Heading = 'heading',
  Paragraph = 'paragraph',
  Dropdown = 'dropdown',
  Name = 'name',
  LongQA = 'long_qa',
  ShortQA = 'short_qa',
  Legal = 'legal',
  Website = 'website',
  PostCode = 'postcode',
  YesNo = 'yes_no',
  PhoneNumber = 'phone_number',
  OpinionScale = 'opinion_scale',
  FileUploader = 'file_uploader',
  Rating = 'rating',
  Hidden = 'hidden',
}

export enum GroupFieldType {
  Name = 'name',
  Address = 'address',
  DateTimeRange = 'datetime_range',
}

export enum ValidatorType {
  Required = 'required',
  Email = 'email',
  MinLength = 'min_length',
  MaxLength = 'max_length',
  MinValue = 'min_value',
  MaxValue = 'max_value',
  Pattern = 'pattern',
  Hiragana = 'hiragana',
  Katakana = 'katakana',
  Kanji = 'kanji',
  Alphabet = 'alphabet',
}

export interface Validator {
  type: ValidatorType;
  value?: any;
  message?: string;
}

export interface FieldOption {
  label: string;
  defaultCheck?: boolean;
  imageUrl?: string;
}

export interface FooterLabel {
  left: string;
  middle: string;
  right: string;
}

export interface FormField {
  id: string;
  type: FieldType | GroupFieldType;
  label: string;
  name: string;
  placeholder?: string;
  icon?: IconType;
  description?: string;
  descriptionEnabled?: boolean;
  fixedDateTitleEnabled?: boolean;
  inputDirectlyEnabled?: boolean;
  dobEnabled?: boolean;
  required?: boolean;
  style?: FormFieldStyle;
  validators: Validator[];
  options?: FieldOption[]; // For radio, checkbox, select
  defaultOption?: FieldOption; // For radio, checkbox, select
  isHide?: boolean; // hide show feature in group field
  isOther?: boolean; // For radio, checkbox, select
  layout?: string; // Radio, checkboxes layout
  dateFormat?: string;
  timeFormat?: string;
  searchable?: boolean;
  maxScale?: number;
  footerHide?: boolean;
  footerLabel?: FooterLabel;
  theme?: string;
  supportedTypes?: string[];
  shape?: string;
  showNumber?: boolean;
}

// Support for group of fields  => remove, drag and drop group
export interface GroupFormField {
  id: string;
  type: GroupFieldType;
  groupType: GroupFieldType;
  description?: string;
  descriptionEnabled?: boolean;
  label: string;
  style?: FormFieldStyle;
  fields: FormField[];
}

export type FormDataContent = FormField | GroupFormField | FormSection;

export interface FormBuilderData {
  title: string;
  description?: string;
  backgroundImageUrl?: string;
  appearance: FormAppearance;
  // One form could contain multiple sections or fields in case in future we need to support nested forms or multiple steps, section
  content: FormDataContent[]; // Array of fields or sections
}

export enum IconType {
  IconEmail = 'IconEmail',
}

export type FormSettings = {
  id: string;
  name: string;
  description: string;
  setting: {
    submission: FormSubmission;
    notification: {
      isAutoresponse: boolean;
    };
    behavior: {
      isMultipleResponse: boolean;
    };
    systemMessage: SystemMessage[];
  };
  metadata: {
    organizationId: string;
    workspaceId: string;
  };
  status: string;
  permissions: string[];
  urls: {
    public: string;
    embed: string;
    private: string;
  };
  tags: string[];
  expiredAt: string;
  createdAt: string;
  updatedAt: string;
  isFavorited: boolean;
  isPinned: boolean;
  appearance: FormAppearance;
  responses: number;
  screenshot: {
    original: string;
    thumbnail: string;
    preview: string | null;
  };
  startAt: string;
};

export interface SystemMessage {
  type: string;
  body: Record<string, string>;
  heading: Record<string, string>;
}

interface FormSubmission {
  mode: string;
  message: string;
  caption: string;
  button: string;
  redirectUrl: string;
  enableBranding: boolean;
  limitResponse: boolean;
  limitNumber: number;
  enableConfirmationScreen: boolean;
}

export interface CreateResponsePayload {
  form_id: string;
  answers: {
    question_id: string;
    page_id: string;
    values: {
      value: string;
      is_other: boolean;
    }[];
  }[];
}

export interface AssetApiResponse {
  response: {
    id: string;
    name: string;
    mime_type: string;
    size: number;
    path: string;
    url: string;
    resource_id: string;
    resource_type: string;
  };
}

export interface Integration {
  id: string;
  type: string;
  form_id: string;
  form_name: string;
  settings: {
    liff_id?: string;
  };
  is_enabled: boolean;
  created_by?: string;
  created_at?: string;
  updated_at?: string;
  allForm?: boolean;
  form_ids?: string[];
}
