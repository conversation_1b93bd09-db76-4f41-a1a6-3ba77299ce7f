export interface FieldLogic {
  id: string;
  action: 'show' | 'hide';
  condition: LogicCondition;
}

export interface LogicCondition {
  $and?: LogicCondition[];
  $or?: LogicCondition[];
  [fieldId: string]: any; // MongoDB-style field conditions
}

export interface DependencyGraph {
  [fieldId: string]: string[]; // Maps field ID to array of field IDs it depends on
}

export interface VisibilityState {
  [fieldId: string]: boolean; // Maps field ID to visibility state
}
