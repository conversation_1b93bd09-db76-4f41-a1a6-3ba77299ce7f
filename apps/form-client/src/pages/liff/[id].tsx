import FormContainerWithLogic from '@/components/FormContainer/FormContainerWithLogic';
import withEdgeCaseHandler from '@/hoc/withEdgeCaseHandler';
import { getForm, getIntegration, getPages } from '@/services/api/form';
import { getLanguageLocale } from '@/utils';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { LiffProvider } from '../../contexts/LiffContext';

export const getServerSideProps = async ({ params, req, query }) => {
  try {
    const id = params?.id;
    const formSettings = await getForm(id);
    const integation = await getIntegration(query.integration_id);
    const liffId = integation.settings.liffId;
    const isValid = !!formSettings && !formSettings.isTemplate && integation.isEnabled && liffId;
    if (!isValid) {
      return { notFound: true };
    }
    const pages = await getPages(id);
    return {
      props: {
        ...(await serverSideTranslations(getLanguageLocale(req.headers), ['common'])),
        id,
        formSettings,
        pages,
        liffId,
      },
    };
  } catch (error) {
    return { notFound: true };
  }
};

const FormPage = ({ id, pages, formSettings, liffId }) => {
  return (
    <LiffProvider liffId={liffId}>
      <FormContainerWithLogic id={id} formSettings={formSettings} pages={pages} />
    </LiffProvider>
  );
};

export default withEdgeCaseHandler(FormPage);
