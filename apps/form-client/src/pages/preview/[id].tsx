import FormContainerWithLogic from '@/components/FormContainer/FormContainerWithLogic';
import { getForm, getPages } from '@/services/api/form';
import { getLanguageLocale, postMessage } from '@/utils';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useEffect, useState } from 'react';

export const getServerSideProps = async ({ params, req }) => {
  try {
    const id = params?.id;
    const formSettings = await getForm(id);
    if (!formSettings) {
      return { notFound: true };
    }
    const pages = await getPages(id);
    return {
      props: {
        ...(await serverSideTranslations(getLanguageLocale(req.headers), ['common'])),
        id,
        pages,
        formSettings,
      },
    };
  } catch (error) {
    return { notFound: true };
  }
};

const FormPage = ({ id, pages, formSettings }) => {
  const [activePageIndex, setActivePageIndex] = useState(0);

  const handleChangePage = (page: number) => {
    setActivePageIndex(page);
    postMessage('pageChange', { page, formId: id });
  };

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data.eventType === 'pageChange') {
        const pageId = event.data.payload;
        const pageIndex = pages.findIndex((page) => page.id === pageId);
        setActivePageIndex(pageIndex);
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [pages]);

  return (
    <FormContainerWithLogic
      id={id}
      preview={true}
      pages={pages}
      activePageIndex={activePageIndex}
      formSettings={formSettings}
      onChangePage={handleChangePage}
    />
  );
};

export default FormPage;
