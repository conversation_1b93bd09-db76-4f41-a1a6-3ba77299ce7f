import FormContainerWithLogic from '@/components/FormContainer/FormContainerWithLogic';
import withEdgeCaseHandler from '@/hoc/withEdgeCaseHandler';
import { getForm, getPages } from '@/services/api/form';
import { getLanguageLocale, postMessage } from '@/utils';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useRouter } from 'next/router';

export const getServerSideProps = async ({ params, req, query }) => {
  try {
    const id = params?.id;
    const formSettings = await getForm(id);
    const isValid = (!!formSettings && !formSettings.isTemplate) || query.preview;
    if (!isValid) {
      return { notFound: true };
    }
    const pages = await getPages(id);
    return {
      props: {
        ...(await serverSideTranslations(getLanguageLocale(req.headers), ['common'])),
        id,
        pages,
        formSettings,
      },
    };
  } catch (error) {
    return { notFound: true };
  }
};

const FormPage = ({ id, pages, formSettings }) => {
  const router = useRouter();
  const { preview, inputWidth, backgroundTransparency } = router.query;

  if (inputWidth) {
    pages = pages.map((page) => {
      if (page.layout?.fieldWidth) {
        page.layout.fieldWidth = inputWidth;
      }
      return page;
    });
  }

  const handleOnSizeChange = (size) => {
    postMessage('sizeChange', { ...size, formId: id });
  };

  const handleOnSubmit = () => {
    postMessage('submit', { formId: id });
  };

  return (
    <FormContainerWithLogic
      id={id}
      pages={pages}
      preview={!!preview}
      backgroundTransparency={backgroundTransparency as string}
      enableHeader={false}
      enableFooter={false}
      formSettings={formSettings}
      onSizeChange={handleOnSizeChange}
      onSubmit={handleOnSubmit}
    />
  );
};

export default withEdgeCaseHandler(FormPage);
