import FormContainerWithLogic from '@/components/FormContainer/FormContainerWithLogic';
import withEdgeCaseHandler from '@/hoc/withEdgeCaseHandler';
import { getForm, getPages } from '@/services/api/form';
import { getLanguageLocale } from '@/utils';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

export const getServerSideProps = async ({ params, req }) => {
  try {
    const id = params?.id;
    const formSettings = await getForm(id);
    const isValid = !!formSettings && !formSettings.isTemplate;
    if (!isValid) {
      return { notFound: true };
    }
    const pages = await getPages(id);
    return {
      props: {
        ...(await serverSideTranslations(getLanguageLocale(req.headers), ['common'])),
        id,
        formSettings,
        pages,
      },
    };
  } catch (error) {
    return { notFound: true };
  }
};

const FormPage = ({ id, pages, formSettings }) => {
  return <FormContainerWithLogic id={id} formSettings={formSettings} pages={pages} />;
};

export default withEdgeCaseHandler(FormPage);
