import { useFormLogicContext } from '@/contexts/FormLogicContext';
import { getValidationSchema } from '@/utils/form';
import { useMemo } from 'react';

/**
 * Hook that creates a dynamic validation schema that respects field visibility
 * Only visible fields will be validated
 */
export function useFormValidation(questions: any[]) {
  // Use context directly to avoid error when not within FormLogicProvider
  const { visibilityState } = useFormLogicContext();

  // Generate validation schema that excludes hidden fields
  const validationSchema = useMemo(() => {
    return getValidationSchema(questions, visibilityState);
  }, [questions, visibilityState]);

  return validationSchema;
}

export default useFormValidation;
