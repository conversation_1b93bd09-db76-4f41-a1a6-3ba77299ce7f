import { FieldType } from '@/types/form-builder';
import type { VisibilityState } from '@/types/logic-condtion';
import { getValidationSchema } from '@/utils/form';
import { renderHook } from '@testing-library/react';
import React, { type ReactNode } from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { z } from 'zod';
import { useFormValidation } from '../useFormValidation';

// Mock the form utility
vi.mock('@/utils/form', () => ({
  getValidationSchema: vi.fn(),
}));

// Mock the FormLogicContext
const mockFormLogicContext = {
  updateFieldVisibility: vi.fn(),
  isFieldVisible: vi.fn(),
  initializeVisibility: vi.fn(),
  clearCache: vi.fn(),
  getPerformanceStats: vi.fn(),
  visibilityState: {} as VisibilityState,
};

vi.mock('@/contexts/FormLogicContext', () => ({
  useFormLogicContext: () => mockFormLogicContext,
  FormLogicProvider: ({ children }: { children: ReactNode }) =>
    React.createElement('div', {}, children),
}));

describe('useFormValidation', () => {
  const createMockQuestion = (id: string, required = false, type = FieldType.ShortQA) => ({
    id,
    type,
    title: `Question ${id}`,
    required,
  });

  beforeEach(() => {
    vi.clearAllMocks();
    mockFormLogicContext.visibilityState = {};
    // Default mock returns new Zod schema instance each time
    vi.mocked(getValidationSchema).mockImplementation(() => z.object({}));
  });

  describe('Basic functionality', () => {
    it('should return validation schema and call getValidationSchema with correct parameters', () => {
      const questions = [
        createMockQuestion('q1', true),
        createMockQuestion('q2', false),
        createMockQuestion('q3', true),
      ];

      const { result } = renderHook(() => useFormValidation(questions));

      // Should return a Zod schema
      expect(result.current).toBeInstanceOf(z.ZodObject);

      // Should call getValidationSchema with questions and visibility state
      expect(getValidationSchema).toHaveBeenCalledWith(questions, {});
    });

    it('should pass visibility state to getValidationSchema', () => {
      const questions = [
        createMockQuestion('q1', true),
        createMockQuestion('q2', false),
        createMockQuestion('q3', true),
      ];

      mockFormLogicContext.visibilityState = {
        q1: true,
        q2: false,
        q3: true,
      };

      renderHook(() => useFormValidation(questions));

      expect(getValidationSchema).toHaveBeenCalledWith(questions, {
        q1: true,
        q2: false,
        q3: true,
      });
    });

    it('should handle empty visibility state', () => {
      const questions = [createMockQuestion('q1', true), createMockQuestion('q2', false)];

      mockFormLogicContext.visibilityState = {};

      renderHook(() => useFormValidation(questions));

      expect(getValidationSchema).toHaveBeenCalledWith(questions, {});
    });
  });

  describe('Reactivity to visibility changes', () => {
    it('should call getValidationSchema again when visibility state changes', () => {
      const questions = [createMockQuestion('q1', true), createMockQuestion('q2', true)];

      mockFormLogicContext.visibilityState = {
        q1: true,
        q2: true,
      };

      const { rerender } = renderHook(() => useFormValidation(questions));

      expect(getValidationSchema).toHaveBeenCalledTimes(1);

      // Change visibility state
      mockFormLogicContext.visibilityState = {
        q1: true,
        q2: false,
      };

      rerender();

      expect(getValidationSchema).toHaveBeenCalledTimes(2);
      expect(getValidationSchema).toHaveBeenLastCalledWith(questions, {
        q1: true,
        q2: false,
      });
    });

    it('should call getValidationSchema again when questions change', () => {
      let questions = [createMockQuestion('q1', true), createMockQuestion('q2', true)];

      const { rerender } = renderHook(({ questions }) => useFormValidation(questions), {
        initialProps: { questions },
      });

      expect(getValidationSchema).toHaveBeenCalledTimes(1);

      // Add more questions
      questions = [...questions, createMockQuestion('q3', true), createMockQuestion('q4', false)];

      rerender({ questions });

      expect(getValidationSchema).toHaveBeenCalledTimes(2);
      expect(getValidationSchema).toHaveBeenLastCalledWith(questions, {});
    });
  });

  describe('Different field types', () => {
    it('should pass all field types to getValidationSchema', () => {
      const questions = [
        createMockQuestion('email', true, FieldType.Email),
        createMockQuestion('text', true, FieldType.ShortQA),
        createMockQuestion('number', false, FieldType.Number),
        createMockQuestion('date', true, FieldType.Date),
      ];

      renderHook(() => useFormValidation(questions));

      expect(getValidationSchema).toHaveBeenCalledWith(questions, {});
    });
  });

  describe('Edge cases', () => {
    it('should handle empty questions array', () => {
      const { result } = renderHook(() => useFormValidation([]));

      expect(result.current).toBeInstanceOf(z.ZodObject);
      expect(getValidationSchema).toHaveBeenCalledWith([], {});
    });

    it('should handle questions with null/undefined values', () => {
      const questions = [
        createMockQuestion('q1', true),
        null,
        undefined,
        createMockQuestion('q2', false),
      ].filter(Boolean) as any[];

      renderHook(() => useFormValidation(questions));

      expect(getValidationSchema).toHaveBeenCalledWith(questions, {});
    });

    it('should handle complex visibility state with mixed values', () => {
      const questions = [
        createMockQuestion('visible', true),
        createMockQuestion('hidden', true),
        createMockQuestion('undefined', true),
        createMockQuestion('null', true),
      ];

      mockFormLogicContext.visibilityState = {
        visible: true,
        hidden: false,
        undefined: undefined as any,
        null: null as any,
      };

      renderHook(() => useFormValidation(questions));

      expect(getValidationSchema).toHaveBeenCalledWith(questions, {
        visible: true,
        hidden: false,
        undefined: undefined,
        null: null,
      });
    });
  });

  describe('Performance considerations', () => {
    it('should memoize validation schema when inputs do not change', () => {
      const questions = [createMockQuestion('q1', true), createMockQuestion('q2', true)];

      const { result, rerender } = renderHook(() => useFormValidation(questions));

      const firstResult = result.current;

      // Rerender with same inputs
      rerender();

      const secondResult = result.current;

      // Should return the same object reference (memoized)
      expect(firstResult).toBe(secondResult);
      expect(getValidationSchema).toHaveBeenCalledTimes(1);
    });

    it('should create new validation schema when visibility state changes', () => {
      const questions = [createMockQuestion('q1', true), createMockQuestion('q2', true)];

      mockFormLogicContext.visibilityState = { q1: true, q2: true };

      const { result, rerender } = renderHook(() => useFormValidation(questions));

      const firstResult = result.current;

      // Change visibility state
      mockFormLogicContext.visibilityState = { q1: true, q2: false };

      rerender();

      const secondResult = result.current;

      // Should return different object reference (new schema)
      expect(firstResult).not.toBe(secondResult);
      expect(getValidationSchema).toHaveBeenCalledTimes(2);
    });
  });

  describe('Integration with FormLogicContext', () => {
    it('should use FormLogicContext visibility state', () => {
      const questions = [createMockQuestion('q1', true)];

      mockFormLogicContext.visibilityState = { q1: false };

      renderHook(() => useFormValidation(questions));

      expect(getValidationSchema).toHaveBeenCalledWith(questions, { q1: false });
    });

    it('should handle context updates correctly', () => {
      const questions = [createMockQuestion('q1', true), createMockQuestion('q2', true)];

      // Initial state
      mockFormLogicContext.visibilityState = { q1: true, q2: true };

      const { rerender } = renderHook(() => useFormValidation(questions));

      expect(getValidationSchema).toHaveBeenCalledWith(questions, { q1: true, q2: true });

      // Simulate context update
      mockFormLogicContext.visibilityState = { q1: false, q2: true };

      rerender();

      expect(getValidationSchema).toHaveBeenLastCalledWith(questions, { q1: false, q2: true });
    });
  });

  describe('Zod schema validation', () => {
    it('should return a valid Zod schema that can parse data', () => {
      const questions = [createMockQuestion('q1', true)];

      // Mock a schema with actual validation
      const mockSchema = z.object({
        q1: z.string().min(1, 'Required'),
      });
      vi.mocked(getValidationSchema).mockReturnValue(mockSchema as any);

      const { result } = renderHook(() => useFormValidation(questions));

      // Should be able to use Zod methods
      expect(result.current.parse).toBeDefined();
      expect(result.current.safeParse).toBeDefined();

      // Should validate correctly
      const validData = { q1: 'test' };
      const invalidData = { q1: '' };

      expect(() => result.current.parse(validData)).not.toThrow();
      expect(() => result.current.parse(invalidData)).toThrow();
    });
  });
});
