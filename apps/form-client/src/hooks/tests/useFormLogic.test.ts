import { FieldType } from '@/types/form-builder';
import type { FieldLogic } from '@/types/logic-condtion';
import { act, renderHook } from '@testing-library/react';
import { beforeEach, describe, expect, it } from 'vitest';
import { useFormLogic } from '../useFormLogic';

describe('useFormLogic', () => {
  const createMockField = (id: string, logics: FieldLogic[] = [], fields: any[] = []) => ({
    id,
    type: 'shortQA',
    label: `Field ${id}`,
    logics,
    fields, // For nested field testing
  });

  const createMockLogic = (conditions: any, action: 'show' | 'hide'): FieldLogic => ({
    id: 'mock-logic',
    conditions,
    action,
  });

  beforeEach(() => {
    // Clear any cached state between tests
  });

  describe('🚀 Basic Functionality', () => {
    it('should initialize with empty visibility state for fields without logic', () => {
      const fields = [createMockField('field1'), createMockField('field2')];

      const { result } = renderHook(() => useFormLogic(fields));

      expect(result.current.visibilityState).toEqual({});
      expect(result.current.isFieldVisible('field1')).toBe(true);
      expect(result.current.isFieldVisible('field2')).toBe(true);
    });

    it('should handle fields with null or undefined values safely', () => {
      const fields = [
        null,
        undefined,
        createMockField('field1'),
        { id: 'field2', logics: null },
        { id: 'field3', logics: undefined },
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      expect(result.current.isFieldVisible('field1')).toBe(true);
      expect(result.current.isFieldVisible('field2')).toBe(true);
      expect(result.current.isFieldVisible('field3')).toBe(true);
    });

    it('should return default visibility (true) for unknown fields', () => {
      const fields = [createMockField('field1')];
      const { result } = renderHook(() => useFormLogic(fields));

      expect(result.current.isFieldVisible('unknown-field')).toBe(true);
    });
  });

  describe('🎯 Simple Visibility Logic', () => {
    it('should handle show logic correctly', () => {
      const fields = [
        createMockField('trigger', []),
        createMockField('target', [createMockLogic({ trigger: { $eq: 'show-me' } }, 'show')]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      expect(result.current.isFieldVisible('target')).toBe(false);

      act(() => {
        result.current.updateFieldVisibility('trigger', 'show-me');
      });

      expect(result.current.isFieldVisible('target')).toBe(true);
    });

    it('should handle hide logic correctly', () => {
      const fields = [
        createMockField('trigger', []),
        createMockField('target', [createMockLogic({ trigger: { $eq: 'hide-me' } }, 'hide')]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      expect(result.current.isFieldVisible('target')).toBe(true);

      act(() => {
        result.current.updateFieldVisibility('trigger', 'hide-me');
      });

      expect(result.current.isFieldVisible('target')).toBe(false);
    });
  });

  describe('🔄 Recursive Nested Field Visibility', () => {
    it('should handle nested field visibility propagation', () => {
      const nestedChild = createMockField('nested-child');
      const parentField = createMockField(
        'parent',
        [createMockLogic({ trigger: { $eq: 'show-nested' } }, 'show')],
        [nestedChild]
      );

      const fields = [createMockField('trigger', []), parentField];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      // Both parent and child should be hidden initially
      expect(result.current.isFieldVisible('parent')).toBe(false);
      expect(result.current.isFieldVisible('nested-child')).toBe(false);

      act(() => {
        result.current.updateFieldVisibility('trigger', 'show-nested');
      });

      // Both parent and child should be visible after trigger
      expect(result.current.isFieldVisible('parent')).toBe(true);
      expect(result.current.isFieldVisible('nested-child')).toBe(true);
    });

    it('should handle deeply nested field structures', () => {
      const deepChild = createMockField('deep-child');
      const midChild = createMockField('mid-child', [], [deepChild]);
      const parentField = createMockField(
        'parent',
        [createMockLogic({ trigger: { $eq: 'show-all' } }, 'show')],
        [midChild]
      );

      const fields = [createMockField('trigger', []), parentField];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      expect(result.current.isFieldVisible('parent')).toBe(false);
      expect(result.current.isFieldVisible('mid-child')).toBe(false);
      expect(result.current.isFieldVisible('deep-child')).toBe(false);

      act(() => {
        result.current.updateFieldVisibility('trigger', 'show-all');
      });

      expect(result.current.isFieldVisible('parent')).toBe(true);
      expect(result.current.isFieldVisible('mid-child')).toBe(true);
      expect(result.current.isFieldVisible('deep-child')).toBe(true);
    });
  });

  describe('🧮 MongoDB-Style Operators', () => {
    describe('Logical Operators', () => {
      it('should handle $and conditions', () => {
        const fields = [
          createMockField('field1', []),
          createMockField('field2', []),
          createMockField('target', [
            createMockLogic(
              {
                $and: [{ field1: { $eq: 'value1' } }, { field2: { $eq: 'value2' } }],
              },
              'show'
            ),
          ]),
        ];

        const { result } = renderHook(() => useFormLogic(fields));

        act(() => {
          result.current.initializeVisibility({});
        });

        expect(result.current.isFieldVisible('target')).toBe(false);

        // Set only first condition
        act(() => {
          result.current.updateFieldVisibility('field1', 'value1');
        });

        expect(result.current.isFieldVisible('target')).toBe(false);

        // Set both conditions
        act(() => {
          result.current.updateFieldVisibility('field2', 'value2');
        });

        expect(result.current.isFieldVisible('target')).toBe(true);
      });

      it('should handle $or conditions', () => {
        const fields = [
          createMockField('field1', []),
          createMockField('field2', []),
          createMockField('target', [
            createMockLogic(
              {
                $or: [{ field1: { $eq: 'value1' } }, { field2: { $eq: 'value2' } }],
              },
              'show'
            ),
          ]),
        ];

        const { result } = renderHook(() => useFormLogic(fields));

        act(() => {
          result.current.initializeVisibility({});
        });

        expect(result.current.isFieldVisible('target')).toBe(false);

        // Set first condition only
        act(() => {
          result.current.updateFieldVisibility('field1', 'value1');
        });

        expect(result.current.isFieldVisible('target')).toBe(true);
      });
    });

    describe('Comparison Operators', () => {
      it('should handle $eq operator', () => {
        const fields = [
          createMockField('trigger', []),
          createMockField('target', [createMockLogic({ trigger: { $eq: 'exact-match' } }, 'show')]),
        ];

        const { result } = renderHook(() => useFormLogic(fields));

        act(() => {
          result.current.initializeVisibility({});
        });

        act(() => {
          result.current.updateFieldVisibility('trigger', 'exact-match');
        });

        expect(result.current.isFieldVisible('target')).toBe(true);

        act(() => {
          result.current.updateFieldVisibility('trigger', 'different');
        });

        expect(result.current.isFieldVisible('target')).toBe(false);
      });

      it('should handle $ne operator', () => {
        const fields = [
          createMockField('trigger', []),
          createMockField('target', [createMockLogic({ trigger: { $ne: 'forbidden' } }, 'show')]),
        ];

        const { result } = renderHook(() => useFormLogic(fields));

        act(() => {
          result.current.initializeVisibility({});
        });

        act(() => {
          result.current.updateFieldVisibility('trigger', 'allowed');
        });

        expect(result.current.isFieldVisible('target')).toBe(true);

        act(() => {
          result.current.updateFieldVisibility('trigger', 'forbidden');
        });

        expect(result.current.isFieldVisible('target')).toBe(false);
      });

      it('should handle $ne with null values properly', () => {
        const fields = [
          createMockField('target', [
            createMockLogic(
              {
                $and: [{ phone: { $regex: '1212' } }, { email: { $ne: null } }],
              },
              'show'
            ),
          ]),
        ];

        const { result } = renderHook(() => useFormLogic(fields));

        act(() => {
          result.current.initializeVisibility({});
        });

        // Set phone to match regex first
        act(() => {
          result.current.updateFieldVisibility('phone', '1212345');
        });

        // Test with email having a value - should show
        act(() => {
          result.current.updateFieldVisibility('email', '<EMAIL>');
        });
        expect(result.current.isFieldVisible('target')).toBe(true);

        // Test with email being null - should hide
        act(() => {
          result.current.updateFieldVisibility('email', null);
        });
        expect(result.current.isFieldVisible('target')).toBe(false);

        // Test with email being empty string - should hide
        act(() => {
          result.current.updateFieldVisibility('email', '');
        });
        expect(result.current.isFieldVisible('target')).toBe(false);
      });

      it('should handle $gt operator', () => {
        const fields = [
          createMockField('trigger', []),
          createMockField('target', [createMockLogic({ trigger: { $gt: 18 } }, 'show')]),
        ];

        const { result } = renderHook(() => useFormLogic(fields));

        act(() => {
          result.current.initializeVisibility({});
        });

        act(() => {
          result.current.updateFieldVisibility('trigger', 20);
        });
        expect(result.current.isFieldVisible('target')).toBe(true);

        act(() => {
          result.current.updateFieldVisibility('trigger', 18);
        });
        expect(result.current.isFieldVisible('target')).toBe(false);
      });

      it('should handle $gte operator', () => {
        const fields = [
          createMockField('trigger', []),
          createMockField('target', [createMockLogic({ trigger: { $gte: 70 } }, 'show')]),
        ];

        const { result } = renderHook(() => useFormLogic(fields));

        act(() => {
          result.current.initializeVisibility({});
        });

        act(() => {
          result.current.updateFieldVisibility('trigger', 70);
        });
        expect(result.current.isFieldVisible('target')).toBe(true);

        act(() => {
          result.current.updateFieldVisibility('trigger', 69);
        });
        expect(result.current.isFieldVisible('target')).toBe(false);
      });

      it('should handle $lt operator', () => {
        const fields = [
          createMockField('trigger', []),
          createMockField('target', [createMockLogic({ trigger: { $lt: 100 } }, 'show')]),
        ];

        const { result } = renderHook(() => useFormLogic(fields));

        act(() => {
          result.current.initializeVisibility({});
        });

        act(() => {
          result.current.updateFieldVisibility('trigger', 50);
        });
        expect(result.current.isFieldVisible('target')).toBe(true);

        act(() => {
          result.current.updateFieldVisibility('trigger', 100);
        });
        expect(result.current.isFieldVisible('target')).toBe(false);
      });

      it('should handle $lte operator', () => {
        const fields = [
          createMockField('trigger', []),
          createMockField('target', [createMockLogic({ trigger: { $lte: 10 } }, 'show')]),
        ];

        const { result } = renderHook(() => useFormLogic(fields));

        act(() => {
          result.current.initializeVisibility({});
        });

        act(() => {
          result.current.updateFieldVisibility('trigger', 10);
        });
        expect(result.current.isFieldVisible('target')).toBe(true);

        act(() => {
          result.current.updateFieldVisibility('trigger', 11);
        });
        expect(result.current.isFieldVisible('target')).toBe(false);
      });
    });

    describe('Array Operators', () => {
      it('should handle $in operator', () => {
        const fields = [
          createMockField('trigger', []),
          createMockField('target', [
            createMockLogic({ trigger: { $in: ['option1', 'option2', 'option3'] } }, 'show'),
          ]),
        ];

        const { result } = renderHook(() => useFormLogic(fields));

        act(() => {
          result.current.initializeVisibility({});
        });

        expect(result.current.isFieldVisible('target')).toBe(false);

        act(() => {
          result.current.updateFieldVisibility('trigger', 'option2');
        });

        expect(result.current.isFieldVisible('target')).toBe(true);

        act(() => {
          result.current.updateFieldVisibility('trigger', 'invalid-option');
        });

        expect(result.current.isFieldVisible('target')).toBe(false);
      });

      it('should handle $nin operator', () => {
        const fields = [
          createMockField('trigger', []),
          createMockField('target', [
            createMockLogic({ trigger: { $nin: ['forbidden1', 'forbidden2'] } }, 'show'),
          ]),
        ];

        const { result } = renderHook(() => useFormLogic(fields));

        act(() => {
          result.current.initializeVisibility({});
        });

        act(() => {
          result.current.updateFieldVisibility('trigger', 'allowed');
        });

        expect(result.current.isFieldVisible('target')).toBe(true);

        act(() => {
          result.current.updateFieldVisibility('trigger', 'forbidden1');
        });

        expect(result.current.isFieldVisible('target')).toBe(false);
      });
    });

    describe('String Operators', () => {
      it('should handle $regex operator', () => {
        const fields = [
          createMockField('trigger', []),
          createMockField('target', [createMockLogic({ trigger: { $regex: '^test.*' } }, 'show')]),
        ];

        const { result } = renderHook(() => useFormLogic(fields));

        act(() => {
          result.current.initializeVisibility({});
        });

        expect(result.current.isFieldVisible('target')).toBe(false);

        act(() => {
          result.current.updateFieldVisibility('trigger', 'test123');
        });

        expect(result.current.isFieldVisible('target')).toBe(true);

        act(() => {
          result.current.updateFieldVisibility('trigger', 'nottest');
        });

        expect(result.current.isFieldVisible('target')).toBe(false);
      });

      it('should handle $exists operator', () => {
        const fields = [
          createMockField('trigger', []),
          createMockField('target', [createMockLogic({ trigger: { $exists: true } }, 'show')]),
        ];

        const { result } = renderHook(() => useFormLogic(fields));

        act(() => {
          result.current.initializeVisibility({});
        });

        expect(result.current.isFieldVisible('target')).toBe(false);

        act(() => {
          result.current.updateFieldVisibility('trigger', 'any-value');
        });

        expect(result.current.isFieldVisible('target')).toBe(true);

        act(() => {
          result.current.updateFieldVisibility('trigger', '');
        });

        expect(result.current.isFieldVisible('target')).toBe(false);
      });
    });

    describe('Negation Operator', () => {
      it('should handle $not operator', () => {
        const fields = [
          createMockField('trigger', []),
          createMockField('target', [
            createMockLogic({ trigger: { $not: { $regex: '03' } } }, 'show'),
          ]),
        ];

        const { result } = renderHook(() => useFormLogic(fields));

        act(() => {
          result.current.initializeVisibility({});
        });

        // Should show when trigger does NOT contain '03'
        act(() => {
          result.current.updateFieldVisibility('trigger', 'SW1A 1AA');
        });
        expect(result.current.isFieldVisible('target')).toBe(true);

        // Should hide when trigger DOES contain '03'
        act(() => {
          result.current.updateFieldVisibility('trigger', '03123');
        });
        expect(result.current.isFieldVisible('target')).toBe(false);
      });
    });
  });

  describe('🔥 Complex Nested Conditions', () => {
    it('should handle the example complex condition from documentation', () => {
      const fields = [
        createMockField('01k62j6z284738465j0rbx7zv7', []),
        createMockField('01k62j6z28ddsa1wmrg14fekbk', []),
        createMockField('target', [
          createMockLogic(
            {
              $or: [
                {
                  $and: [
                    {
                      '01k62j6z284738465j0rbx7zv7': {
                        $regex: 'test',
                      },
                    },
                    {
                      '01k62j6z28ddsa1wmrg14fekbk': {
                        $not: {
                          $regex: 'az',
                        },
                      },
                    },
                  ],
                },
              ],
            },
            'show'
          ),
        ]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      // Initially hidden (first condition not met)
      expect(result.current.isFieldVisible('target')).toBe(false);

      // Input "test" in first field, second field empty (doesn't contain "az")
      act(() => {
        result.current.updateFieldVisibility('01k62j6z284738465j0rbx7zv7', 'test');
      });

      // Should be visible (both AND conditions met)
      expect(result.current.isFieldVisible('target')).toBe(true);

      // Now input "az" in second field
      act(() => {
        result.current.updateFieldVisibility('01k62j6z28ddsa1wmrg14fekbk', 'contains-az-here');
      });

      // Should be hidden ($not condition fails)
      expect(result.current.isFieldVisible('target')).toBe(false);
    });

    it('should handle multiple nested $and and $or conditions', () => {
      const fields = [
        createMockField('field1', []),
        createMockField('field2', []),
        createMockField('field3', []),
        createMockField('target', [
          createMockLogic(
            {
              $or: [
                {
                  $and: [{ field1: { $eq: 'value1' } }, { field2: { $regex: 'pattern' } }],
                },
                {
                  $and: [{ field3: { $gt: 100 } }, { field1: { $ne: 'forbidden' } }],
                },
              ],
            },
            'show'
          ),
        ]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      expect(result.current.isFieldVisible('target')).toBe(false);

      // Meet first OR branch - need both conditions of the AND
      act(() => {
        result.current.updateFieldVisibility('field1', 'value1');
      });
      act(() => {
        result.current.updateFieldVisibility('field2', 'pattern123');
      });

      expect(result.current.isFieldVisible('target')).toBe(true);

      // Clear first branch, meet second OR branch
      act(() => {
        result.current.updateFieldVisibility('field1', 'other');
      });
      act(() => {
        result.current.updateFieldVisibility('field3', 150);
      });

      expect(result.current.isFieldVisible('target')).toBe(true);
    });
  });

  describe('🎭 Multiple Logic Rules', () => {
    it('should handle multiple show/hide rules correctly', () => {
      const fields = [
        createMockField('trigger1', []),
        createMockField('trigger2', []),
        createMockField('target', [
          createMockLogic({ trigger1: { $eq: 'show' } }, 'show'),
          createMockLogic({ trigger2: { $eq: 'hide' } }, 'hide'),
        ]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      // Initially hidden (show condition not met)
      expect(result.current.isFieldVisible('target')).toBe(false);

      // Meet show condition
      act(() => {
        result.current.updateFieldVisibility('trigger1', 'show');
      });

      expect(result.current.isFieldVisible('target')).toBe(true);

      // Meet hide condition (should override show)
      act(() => {
        result.current.updateFieldVisibility('trigger2', 'hide');
      });

      expect(result.current.isFieldVisible('target')).toBe(false);
    });
  });

  describe('⚡ Performance and Caching', () => {
    it('should provide performance stats', () => {
      const fields = [
        createMockField('field1', [createMockLogic({ field2: { $eq: 'test' } }, 'show')]),
        createMockField('field2', []),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      const stats = result.current.getPerformanceStats();

      expect(stats).toHaveProperty('totalFields', 2);
      expect(stats).toHaveProperty('fieldsWithLogic', 1);
      expect(stats).toHaveProperty('dependencyConnections');
      expect(stats).toHaveProperty('cacheStats');
    });

    it('should clear cache when requested', () => {
      const fields = [createMockField('field1')];
      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.clearCache();
      });

      expect(result.current.getPerformanceStats()).toHaveProperty('cacheStats');
    });
  });

  describe('🕸️ Dependency Graph', () => {
    it('should only update affected fields when dependencies change', () => {
      const fields = [
        createMockField('independent', []),
        createMockField('trigger', []),
        createMockField('dependent', [createMockLogic({ trigger: { $eq: 'show' } }, 'show')]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      const initialVisibilityState = { ...result.current.visibilityState };

      // Update independent field - should not affect dependent field
      act(() => {
        result.current.updateFieldVisibility('independent', 'any-value');
      });

      // Visibility state should remain the same for dependent field
      expect(result.current.visibilityState).toEqual(initialVisibilityState);

      // Update trigger field - should affect dependent field
      act(() => {
        result.current.updateFieldVisibility('trigger', 'show');
      });

      expect(result.current.isFieldVisible('dependent')).toBe(true);
    });
  });

  describe('🔧 Option ID Resolution', () => {
    it('should resolve option IDs from string values', () => {
      const fieldWithOptions = {
        id: 'select-field',
        type: FieldType.Dropdown,
        options: [
          { id: 'opt1', label: 'Option 1' },
          { id: 'opt2', label: 'Option 2' },
        ],
      };

      const fields = [
        fieldWithOptions,
        createMockField('target', [createMockLogic({ 'select-field': 'opt1' }, 'show')]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      act(() => {
        result.current.updateFieldVisibility('select-field', 'Option 1');
      });

      expect(result.current.isFieldVisible('target')).toBe(true);
    });

    it('should resolve option IDs from object values', () => {
      const fieldWithOptions = {
        id: 'radio-field',
        type: FieldType.Radio,
        options: [
          { id: 'yes-id', label: 'Yes' },
          { id: 'no-id', label: 'No' },
        ],
      };

      const fields = [
        fieldWithOptions,
        createMockField('target', [createMockLogic({ 'radio-field': 'yes-id' }, 'show')]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      act(() => {
        result.current.updateFieldVisibility('radio-field', { value: 'Yes', is_other: false });
      });

      expect(result.current.isFieldVisible('target')).toBe(true);
    });

    it('should resolve option IDs from array values', () => {
      const fieldWithOptions = {
        id: 'checkbox-field',
        type: FieldType.Checkboxes,
        options: [
          { id: 'opt1', label: 'Option 1' },
          { id: 'opt2', label: 'Option 2' },
          { id: 'opt3', label: 'Option 3' },
        ],
      };

      const fields = [
        fieldWithOptions,
        createMockField('target', [
          createMockLogic({ 'checkbox-field': { $in: ['opt1', 'opt2'] } }, 'show'),
        ]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      act(() => {
        result.current.updateFieldVisibility('checkbox-field', [
          { value: 'Option 1', is_other: false },
          { value: 'Option 2', is_other: false },
        ]);
      });

      expect(result.current.isFieldVisible('target')).toBe(true);
    });

    it('should handle "other" option values', () => {
      const fieldWithOptions = {
        id: 'select-field',
        type: FieldType.Dropdown,
        options: [
          { id: 'opt1', label: 'Option 1' },
          { id: 'other', label: 'Other' },
        ],
      };

      const fields = [
        fieldWithOptions,
        createMockField('target', [createMockLogic({ 'select-field': 'other' }, 'show')]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      act(() => {
        result.current.updateFieldVisibility('select-field', {
          value: 'Custom Value',
          is_other: true,
          id: 'other',
        });
      });

      expect(result.current.isFieldVisible('target')).toBe(true);
    });

    it('should handle Legal field type correctly', () => {
      const legalField = {
        id: 'legal-field',
        type: FieldType.Legal,
        options: [{ id: 'agree', label: 'I agree to terms' }],
      };

      const fields = [
        legalField,
        createMockField('target', [createMockLogic({ 'legal-field': true }, 'show')]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      // Legal field with non-empty value should resolve to true
      act(() => {
        result.current.updateFieldVisibility('legal-field', [
          { value: 'I agree to terms', is_other: false },
        ]);
      });

      expect(result.current.isFieldVisible('target')).toBe(true);

      // Legal field with empty value should resolve to false
      act(() => {
        result.current.updateFieldVisibility('legal-field', []);
      });

      expect(result.current.isFieldVisible('target')).toBe(false);
    });
  });

  describe('🔍 Invisible Character Handling', () => {
    it('should match options with invisible characters in labels', () => {
      const fieldWithInvisibleChars = {
        id: 'field-with-invisible',
        type: FieldType.Radio,
        options: [
          { id: 'yes-id', label: 'Yes‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‌‌‌‌‍‍‌‍‌‌‌‌‌‌‌‍‌‍‌‌' },
          { id: 'no-id', label: 'No‌‌‍‍‌‌‌‍‌‌‌‍‍‌‌‍‌‌‌‌‍‍‌‍‌‍‌‌‌‌‌‍‌‍‌‌' },
        ],
      };

      const fields = [
        fieldWithInvisibleChars,
        createMockField('target', [createMockLogic({ 'field-with-invisible': 'no-id' }, 'show')]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      // Should match "No" even though the option label has invisible characters
      act(() => {
        result.current.updateFieldVisibility('field-with-invisible', 'No');
      });

      expect(result.current.isFieldVisible('target')).toBe(true);
    });

    it('should handle various invisible Unicode characters', () => {
      const fieldWithVariousChars = {
        id: 'unicode-field',
        type: FieldType.Dropdown,
        options: [
          { id: 'test1', label: 'TestValue' }, // Clean label for comparison
          { id: 'test2', label: 'Test\u200CValue' }, // Zero Width Non-Joiner
          { id: 'test3', label: 'Test\u200DValue' }, // Zero Width Joiner
        ],
      };

      const fields = [
        fieldWithVariousChars,
        createMockField('target', [createMockLogic({ 'unicode-field': { $eq: 'test1' } }, 'show')]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      // Should match clean value against clean label
      act(() => {
        result.current.updateFieldVisibility('unicode-field', 'TestValue');
      });

      expect(result.current.isFieldVisible('target')).toBe(true);
    });

    it('should match invisible characters in option labels with clean input', () => {
      const fieldWithInvisibleChars = {
        id: 'invisible-test',
        type: FieldType.Radio,
        options: [
          { id: 'clean-option', label: 'CleanValue' },
          { id: 'invisible-option', label: 'Test\u200DValue' }, // Zero Width Joiner
        ],
      };

      const fields = [
        fieldWithInvisibleChars,
        createMockField('target', [
          createMockLogic({ 'invisible-test': { $eq: 'invisible-option' } }, 'show'),
        ]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      // Should match 'TestValue' against 'Test\u200DValue' (with invisible character)
      act(() => {
        result.current.updateFieldVisibility('invisible-test', 'TestValue');
      });

      expect(result.current.isFieldVisible('target')).toBe(true);
    });
  });

  describe('📊 Array Comparison Logic', () => {
    it('should handle mixed type arrays in $in operator', () => {
      const fields = [
        createMockField('trigger', []),
        createMockField('target', [createMockLogic({ trigger: { $in: ['1', '2', '3'] } }, 'show')]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      // Test with number that should match string in array
      act(() => {
        result.current.updateFieldVisibility('trigger', 2);
      });

      expect(result.current.isFieldVisible('target')).toBe(true);

      // Test with string
      act(() => {
        result.current.updateFieldVisibility('trigger', '3');
      });

      expect(result.current.isFieldVisible('target')).toBe(true);
    });

    it('should handle array field values with $in operator', () => {
      const fields = [
        createMockField('trigger', []),
        createMockField('target', [
          createMockLogic({ trigger: { $in: ['option1', 'option2'] } }, 'show'),
        ]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      // Test with array field value
      act(() => {
        result.current.updateFieldVisibility('trigger', ['option1', 'option3']);
      });

      expect(result.current.isFieldVisible('target')).toBe(true);
    });

    it('should handle $nin operator with array field values', () => {
      const fields = [
        createMockField('trigger', []),
        createMockField('target', [
          createMockLogic({ trigger: { $nin: ['forbidden1', 'forbidden2'] } }, 'show'),
        ]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      // Should be visible when array doesn't contain forbidden values
      act(() => {
        result.current.updateFieldVisibility('trigger', ['allowed1', 'allowed2']);
      });

      expect(result.current.isFieldVisible('target')).toBe(true);

      // Should be hidden when array contains forbidden value
      act(() => {
        result.current.updateFieldVisibility('trigger', ['allowed1', 'forbidden1']);
      });

      expect(result.current.isFieldVisible('target')).toBe(false);
    });
  });

  describe('🚨 Edge Cases', () => {
    it('should handle empty fields array', () => {
      const { result } = renderHook(() => useFormLogic([]));

      expect(result.current.visibilityState).toEqual({});
      expect(result.current.isFieldVisible('any-field')).toBe(true);

      act(() => {
        result.current.initializeVisibility({});
      });

      expect(result.current.visibilityState).toEqual({});
    });

    it('should handle fields with empty logics array', () => {
      const fields = [createMockField('field1', [])];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      expect(result.current.isFieldVisible('field1')).toBe(true);
    });

    it('should handle circular dependencies gracefully', () => {
      const fields = [
        createMockField('field1', [createMockLogic({ field2: { $eq: 'show1' } }, 'show')]),
        createMockField('field2', [createMockLogic({ field1: { $eq: 'show2' } }, 'show')]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      // Should not throw an error
      act(() => {
        result.current.initializeVisibility({});
      });

      expect(result.current.isFieldVisible('field1')).toBe(false);
      expect(result.current.isFieldVisible('field2')).toBe(false);
    });

    it('should handle direct value comparisons', () => {
      const fields = [
        createMockField('stringField', [createMockLogic({ trigger: 'exact-match' }, 'show')]),
        createMockField('numberField', [createMockLogic({ trigger: 42 }, 'show')]),
        createMockField('booleanField', [createMockLogic({ trigger: true }, 'show')]),
        createMockField('nullField', [createMockLogic({ trigger: null }, 'show')]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      // Test string comparison
      act(() => {
        result.current.updateFieldVisibility('trigger', 'exact-match');
      });
      expect(result.current.isFieldVisible('stringField')).toBe(true);

      // Test number comparison
      act(() => {
        result.current.updateFieldVisibility('trigger', 42);
      });
      expect(result.current.isFieldVisible('numberField')).toBe(true);

      // Test boolean comparison
      act(() => {
        result.current.updateFieldVisibility('trigger', true);
      });
      expect(result.current.isFieldVisible('booleanField')).toBe(true);

      // Test null comparison
      act(() => {
        result.current.updateFieldVisibility('trigger', null);
      });
      expect(result.current.isFieldVisible('nullField')).toBe(true);
    });

    it('should handle fields with options correctly', () => {
      const fieldWithOptions = {
        id: 'select-field',
        type: 'select',
        label: 'Select Field',
        logics: [],
        options: [
          { id: 'opt1', label: 'Option 1' },
          { id: 'opt2', label: 'Option 2' },
        ],
      };

      const fields = [
        fieldWithOptions,
        createMockField('target', [createMockLogic({ 'select-field': 'opt1' }, 'show')]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      // Test with simple string value first
      act(() => {
        result.current.updateFieldVisibility('select-field', 'opt1');
      });

      // Should show when field value matches condition
      expect(result.current.isFieldVisible('target')).toBe(true);
    });
  });
});
