import type { FieldLogic } from '@/types/logic-condtion';
import { act, renderHook } from '@testing-library/react';
import { beforeEach, describe, expect, it } from 'vitest';
import { useFormLogic } from '../useFormLogic';

describe('useFormLogic', () => {
  const createMockField = (id: string, logics: FieldLogic[] = []) => ({
    id,
    type: 'shortQA',
    title: `Field ${id}`,
    logics,
  });

  const createMockLogic = (condition: any, action: 'show' | 'hide'): FieldLogic => ({
    id: 'mock-logic',
    condition,
    action,
  });

  beforeEach(() => {
    // Clear any cached state between tests
  });

  describe('Basic functionality', () => {
    it('should initialize with empty visibility state for fields without logic', () => {
      const fields = [createMockField('field1'), createMockField('field2')];

      const { result } = renderHook(() => useFormLogic(fields));

      expect(result.current.visibilityState).toEqual({});
      expect(result.current.isFieldVisible('field1')).toBe(true);
      expect(result.current.isFieldVisible('field2')).toBe(true);
    });

    it('should handle fields with null or undefined values safely', () => {
      const fields = [
        null,
        undefined,
        createMockField('field1'),
        { id: 'field2', logics: null },
        { id: 'field3', logics: undefined },
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      expect(result.current.isFieldVisible('field1')).toBe(true);
      expect(result.current.isFieldVisible('field2')).toBe(true);
      expect(result.current.isFieldVisible('field3')).toBe(true);
    });

    it('should return default visibility (true) for unknown fields', () => {
      const fields = [createMockField('field1')];
      const { result } = renderHook(() => useFormLogic(fields));

      expect(result.current.isFieldVisible('unknown-field')).toBe(true);
    });
  });

  describe('Simple visibility logic', () => {
    it('should handle show logic correctly', () => {
      const fields = [
        createMockField('trigger', []),
        createMockField('target', [createMockLogic({ trigger: { $eq: 'show-me' } }, 'show')]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      // Initialize with empty form data
      act(() => {
        result.current.initializeVisibility({});
      });

      // Target should be hidden initially (condition not met)
      expect(result.current.isFieldVisible('target')).toBe(false);

      // Update trigger field to meet condition
      act(() => {
        result.current.updateFieldVisibility('trigger', 'show-me', { trigger: 'show-me' });
      });

      // Target should now be visible
      expect(result.current.isFieldVisible('target')).toBe(true);
    });

    it('should handle hide logic correctly', () => {
      const fields = [
        createMockField('trigger', []),
        createMockField('target', [createMockLogic({ trigger: { $eq: 'hide-me' } }, 'hide')]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      // Initialize with empty form data
      act(() => {
        result.current.initializeVisibility({});
      });

      // Target should be visible initially (hide condition not met)
      expect(result.current.isFieldVisible('target')).toBe(true);

      // Update trigger field to meet hide condition
      act(() => {
        result.current.updateFieldVisibility('trigger', 'hide-me', { trigger: 'hide-me' });
      });

      // Target should now be hidden
      expect(result.current.isFieldVisible('target')).toBe(false);
    });
  });

  describe('Complex condition evaluation', () => {
    it('should handle $and conditions', () => {
      const fields = [
        createMockField('field1', []),
        createMockField('field2', []),
        createMockField('target', [
          createMockLogic(
            {
              $and: [{ field1: { $eq: 'value1' } }, { field2: { $eq: 'value2' } }],
            },
            'show'
          ),
        ]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      expect(result.current.isFieldVisible('target')).toBe(false);

      // Set only first condition
      act(() => {
        result.current.updateFieldVisibility('field1', 'value1', { field1: 'value1' });
      });

      expect(result.current.isFieldVisible('target')).toBe(false);

      // Set both conditions
      act(() => {
        result.current.updateFieldVisibility('field2', 'value2', {
          field1: 'value1',
          field2: 'value2',
        });
      });

      expect(result.current.isFieldVisible('target')).toBe(true);
    });

    it('should handle $or conditions', () => {
      const fields = [
        createMockField('field1', []),
        createMockField('field2', []),
        createMockField('target', [
          createMockLogic(
            {
              $or: [{ field1: { $eq: 'value1' } }, { field2: { $eq: 'value2' } }],
            },
            'show'
          ),
        ]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      expect(result.current.isFieldVisible('target')).toBe(false);

      // Set first condition only
      act(() => {
        result.current.updateFieldVisibility('field1', 'value1', { field1: 'value1' });
      });

      expect(result.current.isFieldVisible('target')).toBe(true);
    });

    it('should handle $in conditions', () => {
      const fields = [
        createMockField('trigger', []),
        createMockField('target', [
          createMockLogic({ trigger: { $in: ['option1', 'option2', 'option3'] } }, 'show'),
        ]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      expect(result.current.isFieldVisible('target')).toBe(false);

      act(() => {
        result.current.updateFieldVisibility('trigger', 'option2', { trigger: 'option2' });
      });

      expect(result.current.isFieldVisible('target')).toBe(true);

      act(() => {
        result.current.updateFieldVisibility('trigger', 'invalid-option', {
          trigger: 'invalid-option',
        });
      });

      expect(result.current.isFieldVisible('target')).toBe(false);
    });

    it('should handle $exists conditions', () => {
      const fields = [
        createMockField('trigger', []),
        createMockField('target', [createMockLogic({ trigger: { $exists: true } }, 'show')]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      expect(result.current.isFieldVisible('target')).toBe(false);

      act(() => {
        result.current.updateFieldVisibility('trigger', 'any-value', { trigger: 'any-value' });
      });

      expect(result.current.isFieldVisible('target')).toBe(true);

      act(() => {
        result.current.updateFieldVisibility('trigger', '', { trigger: '' });
      });

      expect(result.current.isFieldVisible('target')).toBe(false);
    });

    it('should handle $regex conditions', () => {
      const fields = [
        createMockField('trigger', []),
        createMockField('target', [createMockLogic({ trigger: { $regex: '^test.*' } }, 'show')]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      expect(result.current.isFieldVisible('target')).toBe(false);

      act(() => {
        result.current.updateFieldVisibility('trigger', 'test123', { trigger: 'test123' });
      });

      expect(result.current.isFieldVisible('target')).toBe(true);

      act(() => {
        result.current.updateFieldVisibility('trigger', 'nottest', { trigger: 'nottest' });
      });

      expect(result.current.isFieldVisible('target')).toBe(false);
    });
  });

  describe('Multiple logic rules', () => {
    it('should handle multiple show/hide rules correctly', () => {
      const fields = [
        createMockField('trigger1', []),
        createMockField('trigger2', []),
        createMockField('target', [
          createMockLogic({ trigger1: { $eq: 'show' } }, 'show'),
          createMockLogic({ trigger2: { $eq: 'hide' } }, 'hide'),
        ]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      // Initially hidden (show condition not met)
      expect(result.current.isFieldVisible('target')).toBe(false);

      // Meet show condition
      act(() => {
        result.current.updateFieldVisibility('trigger1', 'show', { trigger1: 'show' });
      });

      expect(result.current.isFieldVisible('target')).toBe(true);

      // Meet hide condition (should override show)
      act(() => {
        result.current.updateFieldVisibility('trigger2', 'hide', {
          trigger1: 'show',
          trigger2: 'hide',
        });
      });

      expect(result.current.isFieldVisible('target')).toBe(false);
    });
  });

  describe('Performance and caching', () => {
    it('should provide performance stats', () => {
      const fields = [
        createMockField('field1', [createMockLogic({ field2: { $eq: 'test' } }, 'show')]),
        createMockField('field2', []),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      const stats = result.current.getPerformanceStats();

      expect(stats).toHaveProperty('totalFields', 2);
      expect(stats).toHaveProperty('fieldsWithLogic', 1);
      expect(stats).toHaveProperty('dependencyConnections');
      expect(stats).toHaveProperty('cacheStats');
    });

    it('should clear cache when requested', () => {
      const fields = [createMockField('field1')];
      const { result } = renderHook(() => useFormLogic(fields));

      // This should not throw an error
      act(() => {
        result.current.clearCache();
      });

      expect(result.current.getPerformanceStats()).toHaveProperty('cacheStats');
    });
  });

  describe('Dependency graph', () => {
    it('should only update affected fields when dependencies change', () => {
      const fields = [
        createMockField('independent', []),
        createMockField('trigger', []),
        createMockField('dependent', [createMockLogic({ trigger: { $eq: 'show' } }, 'show')]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      const initialVisibilityState = { ...result.current.visibilityState };

      // Update independent field - should not affect dependent field
      act(() => {
        result.current.updateFieldVisibility('independent', 'any-value', {
          independent: 'any-value',
        });
      });

      // Visibility state should remain the same for dependent field
      expect(result.current.visibilityState).toEqual(initialVisibilityState);

      // Update trigger field - should affect dependent field
      act(() => {
        result.current.updateFieldVisibility('trigger', 'show', { trigger: 'show' });
      });

      expect(result.current.isFieldVisible('dependent')).toBe(true);
    });
  });

  describe('Edge cases', () => {
    it('should handle empty fields array', () => {
      const { result } = renderHook(() => useFormLogic([]));

      expect(result.current.visibilityState).toEqual({});
      expect(result.current.isFieldVisible('any-field')).toBe(true);

      act(() => {
        result.current.initializeVisibility({});
      });

      expect(result.current.visibilityState).toEqual({});
    });

    it('should handle fields with empty logics array', () => {
      const fields = [createMockField('field1', [])];

      const { result } = renderHook(() => useFormLogic(fields));

      act(() => {
        result.current.initializeVisibility({});
      });

      expect(result.current.isFieldVisible('field1')).toBe(true);
    });

    it('should handle circular dependencies gracefully', () => {
      const fields = [
        createMockField('field1', [createMockLogic({ field2: { $eq: 'show1' } }, 'show')]),
        createMockField('field2', [createMockLogic({ field1: { $eq: 'show2' } }, 'show')]),
      ];

      const { result } = renderHook(() => useFormLogic(fields));

      // Should not throw an error
      act(() => {
        result.current.initializeVisibility({});
      });

      expect(result.current.isFieldVisible('field1')).toBe(false);
      expect(result.current.isFieldVisible('field2')).toBe(false);
    });
  });
});
