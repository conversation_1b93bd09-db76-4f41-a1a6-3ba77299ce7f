import type { FormAppearance } from '@/types/form-builder';
import { useMantineTheme } from '@mantine/core';
import { type CSSObject, createStyles } from '@mantine/emotion';

const useFieldStyles = ({
  customize,
  inputStyle,
  formFieldStyle,
  defaultSettings,
}: {
  customize: boolean;
  inputStyle: string;
  formFieldStyle: FormAppearance['formFieldStyle'];
  defaultSettings: FormAppearance['defaultSettings'];
}) => {
  const theme = useMantineTheme();
  const inputBorderColor = customize
    ? formFieldStyle?.color.fieldStroke
    : theme.colors.decaLight[5];
  const focusBorderColor = defaultSettings.color;

  const getBorderStyles = () => {
    return inputStyle === 'line'
      ? {
          borderBottomStyle: 'solid',
          borderBottomWidth: 1,
          borderBottomColor: inputBorderColor,
          '&:focus': {
            borderBottomColor: focusBorderColor,
          },
        }
      : {
          borderColor: inputStyle?.includes('fill') ? 'none' : inputBorderColor,
          '&:focus': {
            borderColor: focusBorderColor,
          },
        };
  };

  const textSize = customize ? formFieldStyle?.fontSize.text : 12;
  const getBackgroundColor = () => {
    if (customize) {
      return formFieldStyle?.color.fieldBackGround;
    }
    if (inputStyle === 'line') {
      return 'transparent';
    }
    return inputStyle?.includes('fill') ? theme.colors.decaLight[0] : 'white';
  };

  const { classes } = createStyles(() => ({
    root: {
      '&:focus-within .mantine-Input-icon svg': {
        stroke: focusBorderColor,
      },
    },
    input: {
      color: formFieldStyle?.color.answer,
      fontSize: customize ? formFieldStyle?.fontSize.answer : 14,
      backgroundColor: getBackgroundColor(),
      caretColor: focusBorderColor,
      ...(getBorderStyles() as CSSObject),
      '&[type="checkbox"]': {
        borderRadius: '4px',
        background: 'white',
        borderColor: inputBorderColor,
      },
      '&:checked': {
        background: focusBorderColor,
        borderColor: focusBorderColor,
      },
      '::placeholder': {
        color: formFieldStyle?.color.placeholder,
      },
      '&[aria-expanded="true"]': {
        borderColor: focusBorderColor,
      },
    },
    label: {
      color: formFieldStyle?.color.question,
      fontSize: customize ? formFieldStyle?.fontSize.question : 14,
      paddingBottom: '8px',
      display: 'inline-flex !important',
      gap: '2px',
      p: {
        margin: 0,
      },
    },
    description: {
      color: formFieldStyle?.color.description,
      fontSize: textSize,
      paddingBottom: '8px',
      p: {
        margin: 0,
      },
    },
    radio: {
      '&:checked': {
        background: focusBorderColor,
        borderColor: focusBorderColor,
      },
    },
    error: {
      fontSize: textSize,
    },
    icon: {
      color: theme.colors.decaGrey[6],
    },
  }))();

  return {
    radius: inputStyle?.includes('rounded') ? theme.radius.xl : theme.radius.sm,
    variant:
      inputStyle === 'line' ? 'unstyled' : inputStyle?.includes('fill') ? 'filled' : 'default',
    classNames: {
      root: classes.root,
      input: classes.input,
      label: classes.label,
      description: classes.description,
      radio: classes.radio,
      error: classes.error,
      icon: classes.icon,
    },
  };
};

export default useFieldStyles;
