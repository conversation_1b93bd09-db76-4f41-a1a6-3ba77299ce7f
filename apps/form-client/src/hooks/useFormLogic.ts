import { FieldType } from '@/types/form-builder';
import type {
  DependencyGraph,
  FieldLogic,
  LogicCondition,
  VisibilityState,
} from '@/types/logic-condtion';
import dayjs from 'dayjs';
import { isArray, isDate, isEmpty, isObject } from 'lodash';
import { useCallback, useMemo, useRef, useState } from 'react';

const SPECIAL_CHARACTERS = /\u200B|\u200C|\u200D|\u2060|\uFEFF/g;

/**
 * Lightweight MongoDB-style condition evaluator optimized for form logic
 * Supports: $and, $or, $eq, $ne, $in, $nin, $regex, $exists, $gt, $gte, $lt, $lte
 */
class ConditionEvaluator {
  private memoCache = new Map<string, boolean>();
  private cacheHits = 0;
  private cacheSize = 0;

  // Clear cache when form data changes significantly
  clearCache() {
    this.memoCache.clear();
    this.cacheHits = 0;
    this.cacheSize = 0;
  }

  // Generate cache key from condition and relevant form data
  private getCacheKey(conditions: LogicCondition, formData: Record<string, any>): string {
    const relevantFields = this.extractFieldIds(conditions);
    const relevantData = relevantFields.reduce(
      (acc, fieldId) => {
        acc[fieldId] = formData[fieldId];
        return acc;
      },
      {} as Record<string, any>
    );

    return JSON.stringify({ conditions, data: relevantData });
  }

  // Extract all field IDs referenced in a condition
  extractFieldIds(conditions: LogicCondition): string[] {
    const fieldIds: string[] = [];

    if (conditions.$and) {
      conditions.$and.forEach((cond) => {
        fieldIds.push(...this.extractFieldIds(cond));
      });
    } else if (conditions.$or) {
      conditions.$or.forEach((cond) => {
        fieldIds.push(...this.extractFieldIds(cond));
      });
    } else {
      // Direct field conditions
      Object.keys(conditions).forEach((key) => {
        if (key !== '$and' && key !== '$or') {
          fieldIds.push(key);
        }
      });
    }

    return Array.from(new Set(fieldIds)); // Remove duplicates
  }

  // Main evaluation function with memoization
  evaluate(conditions: LogicCondition, formData: Record<string, any>): boolean {
    const cacheKey = this.getCacheKey(conditions, formData);

    if (this.memoCache.has(cacheKey)) {
      this.cacheHits++;
      return this.memoCache.get(cacheKey)!;
    }

    const result = this.evaluateCondition(conditions, formData);

    // Limit cache size to prevent memory issues with 150 fields
    if (this.cacheSize < 500) {
      this.memoCache.set(cacheKey, result);
      this.cacheSize++;
    }

    return result;
  }

  private evaluateCondition(conditions: LogicCondition, formData: Record<string, any>): boolean {
    // Handle logical operators
    if (conditions.$and) {
      return conditions.$and.every((cond) => this.evaluateCondition(cond, formData));
    }

    if (conditions.$or) {
      return conditions.$or.some((cond) => this.evaluateCondition(cond, formData));
    }

    // Handle field-specific conditions
    for (const [fieldId, fieldCondition] of Object.entries(conditions)) {
      if (fieldId === '$and' || fieldId === '$or') continue;

      const fieldValue = formData[fieldId];
      if (!this.evaluateFieldCondition(fieldValue, fieldCondition)) {
        return false;
      }
    }

    return true;
  }

  private checkFieldValueInArray(fieldValue: any, targetArray: any, shouldInclude = true): boolean {
    if (!Array.isArray(targetArray)) return false;

    // Handle type conversion for mixed string/number comparisons
    const normalizedArray = targetArray.map((v) => String(v));

    if (isArray(fieldValue)) {
      const normalizedFieldValue = fieldValue.map((v) => String(v));
      const hasMatch = normalizedFieldValue.some((v) => normalizedArray.includes(v));
      return shouldInclude ? hasMatch : !hasMatch;
    }

    const normalizedFieldValue = String(fieldValue?.value ?? fieldValue);
    const isIncluded = normalizedArray.includes(normalizedFieldValue);
    return shouldInclude ? isIncluded : !isIncluded;
  }

  private evaluateFieldCondition(fieldValue: any, condition: any): boolean {
    // Handle different MongoDB-style operators
    if (typeof condition === 'object' && condition !== null) {
      for (const [operator, value] of Object.entries(condition)) {
        switch (operator) {
          case '$eq':
            if (fieldValue !== value) return false;
            break;
          case '$ne': {
            // Handle null/undefined/empty string/object checks properly
            if (value === null) {
              // $ne: null should pass if field has any value (not null, undefined, or empty)
              const hasValue = isObject(fieldValue)
                ? !isEmpty(fieldValue)
                : fieldValue !== null && fieldValue !== undefined && fieldValue !== '';
              if (!hasValue) return false;
            } else {
              // Regular not-equals comparison
              // if value is a Date, convert it to a string
              if (isDate(fieldValue)) {
                return !dayjs(fieldValue).isSame(dayjs(value as string));
              }
              if (fieldValue === value) return false;
            }
            break;
          }
          case '$in': {
            if (!this.checkFieldValueInArray(fieldValue, value, true)) return false;
            break;
          }
          case '$nin': {
            if (!this.checkFieldValueInArray(fieldValue, value, false)) return false;
            break;
          }
          case '$regex': {
            const regex = new RegExp(value as string, condition.$options || '');
            if (!regex.test(String(fieldValue || ''))) return false;
            break;
          }
          case '$exists': {
            const exists = fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
            if (exists !== value) return false;
            break;
          }
          case '$gt': {
            const numField = Number(fieldValue);
            const numValue = Number(value);
            if (Number.isNaN(numField) || Number.isNaN(numValue) || numField <= numValue)
              return false;
            break;
          }
          case '$gte': {
            const numField = Number(fieldValue);
            const numValue = Number(value);
            if (Number.isNaN(numField) || Number.isNaN(numValue) || numField < numValue)
              return false;
            break;
          }
          case '$lt': {
            const numField = Number(fieldValue);
            const numValue = Number(value);
            if (Number.isNaN(numField) || Number.isNaN(numValue) || numField >= numValue)
              return false;
            break;
          }
          case '$lte': {
            const numField = Number(fieldValue);
            const numValue = Number(value);
            if (Number.isNaN(numField) || Number.isNaN(numValue) || numField > numValue)
              return false;
            break;
          }
          case '$not': {
            // $not negates the result of the nested condition
            if (this.evaluateFieldCondition(fieldValue, value)) return false;
            break;
          }
        }
      }
    } else {
      // Direct value comparison
      if (isDate(fieldValue)) {
        return dayjs(fieldValue).isSame(dayjs(condition));
      }
      // in some cases, condition is null or undefined or empty object or empty array then empty value is passed
      if (condition === null || condition === false) {
        return isObject(fieldValue)
          ? isEmpty(fieldValue)
          : fieldValue === '' ||
              fieldValue === null ||
              fieldValue === undefined ||
              fieldValue === false;
      }
      return fieldValue === condition;
    }

    return true;
  }

  // Get cache statistics for debugging
  getCacheStats() {
    return {
      size: this.cacheSize,
      hits: this.cacheHits,
      hitRatio: this.cacheSize > 0 ? this.cacheHits / this.cacheSize : 0,
    };
  }
}

// Build dependency graph from form fields
function buildDependencyGraph(fields: any[]): DependencyGraph {
  const graph: DependencyGraph = {};
  const evaluator = new ConditionEvaluator();

  fields.forEach((field) => {
    if (field && field.logics && field.logics.length > 0) {
      const dependencies = new Set<string>();

      field.logics.forEach((logic: FieldLogic) => {
        const fieldIds = evaluator.extractFieldIds(logic.conditions);
        fieldIds.forEach((id) => dependencies.add(id));
      });

      graph[field.id] = Array.from(dependencies);
    }
  });

  return graph;
}

// Reverse dependency graph - which fields are affected when a field changes
function buildReverseDependencyGraph(dependencyGraph: DependencyGraph): DependencyGraph {
  const reverseGraph: DependencyGraph = {};

  Object.entries(dependencyGraph).forEach(([fieldId, dependencies]) => {
    dependencies.forEach((depId) => {
      if (!reverseGraph[depId]) {
        reverseGraph[depId] = [];
      }
      reverseGraph[depId].push(fieldId);
    });
  });

  return reverseGraph;
}

/**
 * Recursively sets visibility for a field and all its nested children
 * @param field - The field to set visibility for
 * @param isVisible - The visibility state to apply
 * @param visibilityMap - The object to store visibility states in
 */
const setNestedFieldVisibility = (
  field: any,
  isVisible: boolean,
  visibilityMap: VisibilityState
): void => {
  visibilityMap[field.id] = isVisible;
  if (field.fields && field.fields.length > 0) {
    field.fields.forEach((child: any) => {
      setNestedFieldVisibility(child, isVisible, visibilityMap);
    });
  }
};

/**
 * Main hook for form logic system
 * Manages field visibility based on conditional logic rules
 *
 * @param fields - Array of form fields with logic definitions
 * @returns Object with visibility management functions
 */
export function useFormLogic(fields: any[]) {
  const [visibilityState, setVisibilityState] = useState<VisibilityState>({});
  const transformedFormDataRef = useRef<Record<string, any>>({});

  // Initialize evaluator and dependency graphs
  const evaluatorRef = useRef(new ConditionEvaluator());
  const dependencyGraph = useMemo(() => buildDependencyGraph(fields), [fields]);
  const reverseDepGraph = useMemo(
    () => buildReverseDependencyGraph(dependencyGraph),
    [dependencyGraph]
  );

  // Cache fields with logic for performance
  const fieldsWithLogic = useMemo(
    () => fields.filter((field) => field && field.logics && field.logics.length > 0),
    [fields]
  );

  // Evaluate visibility for a specific field
  const evaluateFieldVisibility = useCallback(
    (field: any, formData: Record<string, any>): boolean => {
      if (!field.logics || field.logics.length === 0) {
        return true; // Default to visible if no logic
      }

      // Evaluate all logic conditions for this field
      let isVisible = true; // Default visibility

      for (const logic of field.logics) {
        const conditionResult = evaluatorRef.current.evaluate(logic.conditions, formData);

        if (logic.action === 'show') {
          isVisible = conditionResult;
        } else if (logic.action === 'hide') {
          isVisible = !conditionResult;
        }

        // If any condition makes field invisible, stop evaluating
        if (!isVisible) break;
      }

      return isVisible;
    },
    []
  );

  const resolveOptionIds = (
    value: string | Record<string, any> | Record<string, any>[],
    field: any
  ) => {
    // For legal field, if value is not empty array, return true
    if (field.type === FieldType.Legal) {
      return !isEmpty(value);
    }

    const _compareValues = (value: string, target: string) =>
      value.replace(SPECIAL_CHARACTERS, '').trim() ===
      target.replace(SPECIAL_CHARACTERS, '').trim();

    if (isArray(value)) {
      const _ids = value.map((v) => {
        if (v.is_other) {
          return 'other';
        }

        const _option = field?.options?.find((o) => _compareValues(o.label, v.value));
        return _option?.id;
      });
      return _ids;
    }

    if (isObject(value)) {
      if (value.is_other) {
        return value.id;
      }

      // Remove all zero-width spaces and trim the value
      const _option = field?.options?.find((o) => _compareValues(o.label, value.value));
      return _option?.id;
    }

    const _option = field?.options?.find((o) => _compareValues(o.label, value));
    return _option?.id;
  };

  // Update visibility based on form data change, if any field is affected by the change, re-evaluate the visibility of the affected fields
  const updateFieldVisibility = useCallback(
    (fieldId: string, value: any) => {
      // If an updated field has options, update the value to the id of the active option
      const field = fields.find((f) => f.id === fieldId);
      if (
        [
          FieldType.Checkboxes,
          FieldType.MultipleChoice,
          FieldType.Legal,
          FieldType.Radio,
          FieldType.YesNo,
          FieldType.Dropdown,
        ].includes(field?.type)
      ) {
        value = resolveOptionIds(value, field);
      }
      transformedFormDataRef.current = { ...transformedFormDataRef.current, [fieldId]: value };

      // Find fields that might be affected by this change
      const affectedFieldIds = reverseDepGraph[fieldId] || [];

      if (affectedFieldIds.length > 0) {
        // Re-evaluate visibility for affected fields
        const newVisibility: VisibilityState = {};

        affectedFieldIds.forEach((affectedFieldId) => {
          const field = fields.find((f) => f.id === affectedFieldId);
          if (field) {
            const _isVisible = evaluateFieldVisibility(field, transformedFormDataRef.current);
            setNestedFieldVisibility(field, _isVisible, newVisibility);
          }
        });

        // Update visibility state if anything changed
        setVisibilityState((prevVisibility) => {
          const hasChanges = Object.entries(newVisibility).some(
            ([id, visible]) => prevVisibility[id] !== visible
          );

          return hasChanges ? { ...prevVisibility, ...newVisibility } : prevVisibility;
        });
      }
    },
    [reverseDepGraph, fields, evaluateFieldVisibility]
  );

  // Initialize visibility state for all fields with logic
  const initializeVisibility = useCallback(
    (formData: Record<string, any>) => {
      // Initialize the transformed form data ref with the provided form data
      transformedFormDataRef.current = { ...formData };

      const initialVisibility: VisibilityState = {};
      const currentFieldsWithLogic = fields.filter(
        (field) => field && field.logics && field.logics.length > 0
      );

      currentFieldsWithLogic.forEach((field) => {
        const _isVisible = evaluateFieldVisibility(field, formData);
        setNestedFieldVisibility(field, _isVisible, initialVisibility);
      });

      setVisibilityState(initialVisibility);
    },
    [fields, evaluateFieldVisibility]
  );

  // Get visibility for a specific field
  const isFieldVisible = useCallback(
    (fieldId: string): boolean => {
      return visibilityState[fieldId] ?? true; // Default to visible
    },
    [visibilityState]
  );

  // Clear cache when form is reset or major changes occur
  const clearCache = useCallback(() => {
    evaluatorRef.current.clearCache();
  }, []);

  // Debug function to get performance stats
  const getPerformanceStats = useCallback(() => {
    return {
      totalFields: fields.length,
      fieldsWithLogic: fieldsWithLogic.length,
      dependencyConnections: Object.values(dependencyGraph).flat().length,
      cacheStats: evaluatorRef.current.getCacheStats(),
    };
  }, [fields.length, fieldsWithLogic.length, dependencyGraph]);

  return {
    updateFieldVisibility,
    isFieldVisible,
    initializeVisibility,
    clearCache,
    getPerformanceStats,
    visibilityState,
  };
}
