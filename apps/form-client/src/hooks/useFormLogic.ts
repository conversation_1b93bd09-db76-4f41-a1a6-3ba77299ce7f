import type {
  DependencyG<PERSON>h,
  FieldLogic,
  LogicCondition,
  VisibilityState,
} from '@/types/logic-condtion';
import { useCallback, useMemo, useRef, useState } from 'react';

/**
 * Lightweight MongoDB-style condition evaluator optimized for form logic
 * Supports: $and, $or, $eq, $ne, $in, $nin, $regex, $exists, $gt, $gte, $lt, $lte
 */
class ConditionEvaluator {
  private memoCache = new Map<string, boolean>();
  private cacheHits = 0;
  private cacheSize = 0;

  // Clear cache when form data changes significantly
  clearCache() {
    this.memoCache.clear();
    this.cacheHits = 0;
    this.cacheSize = 0;
  }

  // Generate cache key from condition and relevant form data
  private getCacheKey(condition: LogicCondition, formData: Record<string, any>): string {
    const relevantFields = this.extractFieldIds(condition);
    const relevantData = relevantFields.reduce(
      (acc, fieldId) => {
        acc[fieldId] = formData[fieldId];
        return acc;
      },
      {} as Record<string, any>
    );

    return JSON.stringify({ condition, data: relevantData });
  }

  // Extract all field IDs referenced in a condition
  extractFieldIds(condition: LogicCondition): string[] {
    const fieldIds: string[] = [];

    if (condition.$and) {
      condition.$and.forEach((cond) => {
        fieldIds.push(...this.extractFieldIds(cond));
      });
    } else if (condition.$or) {
      condition.$or.forEach((cond) => {
        fieldIds.push(...this.extractFieldIds(cond));
      });
    } else {
      // Direct field conditions
      Object.keys(condition).forEach((key) => {
        if (key !== '$and' && key !== '$or') {
          fieldIds.push(key);
        }
      });
    }

    return Array.from(new Set(fieldIds)); // Remove duplicates
  }

  // Main evaluation function with memoization
  evaluate(condition: LogicCondition, formData: Record<string, any>): boolean {
    const cacheKey = this.getCacheKey(condition, formData);

    if (this.memoCache.has(cacheKey)) {
      this.cacheHits++;
      return this.memoCache.get(cacheKey)!;
    }

    const result = this.evaluateCondition(condition, formData);

    // Limit cache size to prevent memory issues with 150 fields
    if (this.cacheSize < 500) {
      this.memoCache.set(cacheKey, result);
      this.cacheSize++;
    }

    return result;
  }

  private evaluateCondition(condition: LogicCondition, formData: Record<string, any>): boolean {
    // Handle logical operators
    if (condition.$and) {
      return condition.$and.every((cond) => this.evaluateCondition(cond, formData));
    }

    if (condition.$or) {
      return condition.$or.some((cond) => this.evaluateCondition(cond, formData));
    }

    // Handle field-specific conditions
    for (const [fieldId, fieldCondition] of Object.entries(condition)) {
      if (fieldId === '$and' || fieldId === '$or') continue;

      const fieldValue = formData[fieldId];

      if (!this.evaluateFieldCondition(fieldValue, fieldCondition)) {
        return false;
      }
    }

    return true;
  }

  private evaluateFieldCondition(fieldValue: any, condition: any): boolean {
    // Handle different MongoDB-style operators
    if (typeof condition === 'object' && condition !== null) {
      for (const [operator, value] of Object.entries(condition)) {
        switch (operator) {
          case '$eq':
            if (fieldValue !== value) return false;
            break;
          case '$ne':
            if (fieldValue === value) return false;
            break;
          case '$in':
            if (!Array.isArray(value) || !value.includes(fieldValue)) return false;
            break;
          case '$nin':
            if (!Array.isArray(value) || value.includes(fieldValue)) return false;
            break;
          case '$regex': {
            const regex = new RegExp(value as string, condition.$options || '');
            if (!regex.test(String(fieldValue || ''))) return false;
            break;
          }
          case '$exists': {
            const exists = fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
            if (exists !== value) return false;
            break;
          }
          // add all the other operators here
        }
      }
    } else {
      // Direct value comparison
      return fieldValue === condition;
    }

    return true;
  }

  // Get cache statistics for debugging
  getCacheStats() {
    return {
      size: this.cacheSize,
      hits: this.cacheHits,
      hitRatio: this.cacheSize > 0 ? this.cacheHits / this.cacheSize : 0,
    };
  }
}

// Build dependency graph from form fields
function buildDependencyGraph(fields: any[]): DependencyGraph {
  const graph: DependencyGraph = {};
  const evaluator = new ConditionEvaluator();

  fields.forEach((field) => {
    if (field && field.logics && field.logics.length > 0) {
      const dependencies = new Set<string>();

      field.logics.forEach((logic: FieldLogic) => {
        const fieldIds = evaluator.extractFieldIds(logic.condition);
        fieldIds.forEach((id) => dependencies.add(id));
      });

      graph[field.id] = Array.from(dependencies);
    }
  });

  return graph;
}

// Reverse dependency graph - which fields are affected when a field changes
function buildReverseDependencyGraph(dependencyGraph: DependencyGraph): DependencyGraph {
  const reverseGraph: DependencyGraph = {};

  Object.entries(dependencyGraph).forEach(([fieldId, dependencies]) => {
    dependencies.forEach((depId) => {
      if (!reverseGraph[depId]) {
        reverseGraph[depId] = [];
      }
      reverseGraph[depId].push(fieldId);
    });
  });

  return reverseGraph;
}

/**
 * Main hook for form logic system
 * Manages field visibility based on conditional logic rules
 *
 * @param fields - Array of form fields with logic definitions
 * @returns Object with visibility management functions
 */
export function useFormLogic(fields: any[]) {
  const [visibilityState, setVisibilityState] = useState<VisibilityState>({});

  // Initialize evaluator and dependency graphs
  const evaluatorRef = useRef(new ConditionEvaluator());
  const dependencyGraph = useMemo(() => buildDependencyGraph(fields), [fields]);
  const reverseDepGraph = useMemo(
    () => buildReverseDependencyGraph(dependencyGraph),
    [dependencyGraph]
  );

  // Cache fields with logic for performance
  const fieldsWithLogic = useMemo(
    () => fields.filter((field) => field && field.logics && field.logics.length > 0),
    [fields]
  );

  // Evaluate visibility for a specific field
  const evaluateFieldVisibility = useCallback(
    (field: any, formData: Record<string, any>): boolean => {
      if (!field.logics || field.logics.length === 0) {
        return true; // Default to visible if no logic
      }

      // Evaluate all logic conditions for this field
      let isVisible = true; // Default visibility

      for (const logic of field.logics) {
        const conditionResult = evaluatorRef.current.evaluate(logic.condition, formData);

        if (logic.action === 'show') {
          isVisible = conditionResult;
        } else if (logic.action === 'hide') {
          isVisible = !conditionResult;
        }

        // If any condition makes field invisible, stop evaluating
        if (!isVisible) break;
      }

      return isVisible;
    },
    []
  );

  // Update visibility based on form data change
  const updateFieldVisibility = useCallback(
    (fieldId: string, value: any, formData: Record<string, any>) => {
      const newData = { ...formData, [fieldId]: value };

      // Find fields that might be affected by this change
      const affectedFieldIds = reverseDepGraph[fieldId] || [];

      if (affectedFieldIds.length > 0) {
        // Re-evaluate visibility for affected fields
        const newVisibility: VisibilityState = {};

        affectedFieldIds.forEach((affectedFieldId) => {
          const field = fields.find((f) => f.id === affectedFieldId);
          if (field) {
            newVisibility[affectedFieldId] = evaluateFieldVisibility(field, newData);
          }
        });

        // Update visibility state if anything changed
        setVisibilityState((prevVisibility) => {
          const hasChanges = Object.entries(newVisibility).some(
            ([id, visible]) => prevVisibility[id] !== visible
          );

          return hasChanges ? { ...prevVisibility, ...newVisibility } : prevVisibility;
        });
      }
    },
    [reverseDepGraph, fields, evaluateFieldVisibility]
  );

  // Initialize visibility state for all fields with logic
  const initializeVisibility = useCallback(
    (formData: Record<string, any>) => {
      const initialVisibility: VisibilityState = {};
      const currentFieldsWithLogic = fields.filter(
        (field) => field && field.logics && field.logics.length > 0
      );

      currentFieldsWithLogic.forEach((field) => {
        initialVisibility[field.id] = evaluateFieldVisibility(field, formData);
      });

      setVisibilityState(initialVisibility);
    },
    [fields, evaluateFieldVisibility]
  );

  // Get visibility for a specific field
  const isFieldVisible = useCallback(
    (fieldId: string): boolean => {
      return visibilityState[fieldId] ?? true; // Default to visible
    },
    [visibilityState]
  );

  // Clear cache when form is reset or major changes occur
  const clearCache = useCallback(() => {
    evaluatorRef.current.clearCache();
  }, []);

  // Debug function to get performance stats
  const getPerformanceStats = useCallback(() => {
    return {
      totalFields: fields.length,
      fieldsWithLogic: fieldsWithLogic.length,
      dependencyConnections: Object.values(dependencyGraph).flat().length,
      cacheStats: evaluatorRef.current.getCacheStats(),
    };
  }, [fields.length, fieldsWithLogic.length, dependencyGraph]);

  return {
    updateFieldVisibility,
    isFieldVisible,
    initializeVisibility,
    clearCache,
    getPerformanceStats,
    visibilityState,
  };
}
