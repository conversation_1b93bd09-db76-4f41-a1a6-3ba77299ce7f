import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import type { FormSection } from '@/types/form-builder';
import { FieldType, FormLayoutType } from '@/types/form-builder';
import { render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import FormContainerWithLogic from './FormContainerWithLogic';

// Mock the FormContainer component
vi.mock('./FormContainer', () => ({
  default: vi.fn(
    ({
      id,
      formSettings,
      pages,
      preview,
      inputWidth,
      backgroundTransparency,
      enableHeader,
      enableFooter,
      activePageIndex,
      onChangePage,
      onSizeChange,
      onSubmit,
    }) => (
      <div data-testid='form-container'>
        <div data-testid='form-id'>{id}</div>
        <div data-testid='form-title'>{formSettings.name || formSettings.title}</div>
        <div data-testid='pages-count'>{pages.length}</div>
        <div data-testid='preview'>{preview ? 'true' : 'false'}</div>
        <div data-testid='input-width'>{inputWidth}</div>
        <div data-testid='background-transparency'>{backgroundTransparency}</div>
        <div data-testid='enable-header'>{enableHeader ? 'true' : 'false'}</div>
        <div data-testid='enable-footer'>{enableFooter ? 'true' : 'false'}</div>
        <div data-testid='active-page-index'>{activePageIndex}</div>
      </div>
    )
  ),
}));

// Mock the FormLogicProvider
vi.mock('@/contexts/FormLogicContext', () => ({
  FormLogicProvider: vi.fn(({ children, fields, initialValues }) => (
    <div data-testid='form-logic-provider'>
      <div data-testid='fields-count'>{fields.length}</div>
      <div data-testid='initial-values'>{JSON.stringify(initialValues)}</div>
      {children}
    </div>
  )),
}));

// Mock the getDefaultValues utility
vi.mock('@/utils/form', () => ({
  getDefaultValues: vi.fn((pages) => {
    const defaultValues: Record<string, any> = {};
    pages.forEach((page: FormSection) => {
      page.content.forEach((field: any) => {
        if (field.type === FieldType.ShortQA || field.type === FieldType.Email) {
          defaultValues[field.id] = '';
        }
      });
    });
    return defaultValues;
  }),
}));

describe('FormContainerWithLogic', () => {
  const mockFormSettings = {
    id: 'test-form',
    name: 'Test Form',
    description: 'Test Description',
    startAt: '2024-01-01T00:00:00Z',
    expiredAt: '2024-12-31T23:59:59Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    status: 'active',
    permissions: ['read', 'write'],
    isFavorited: false,
    isPinned: false,
    responses: 0,
    tags: [],
    urls: {
      public: '/public-url',
      embed: '/embed-url',
      private: '/private-url',
    },
    metadata: {
      organizationId: 'org-id',
      workspaceId: 'workspace-id',
    },
    appearance: {
      customize: false,
      defaultSettings: {
        color: '#000000',
        font: 'Arial',
        inputStyle: InputStyle.Classic,
      },
      formFieldStyle: {
        color: {
          placeholder: '#000000',
          question: '#000000',
          answer: '#000000',
          icon: '#000000',
          description: '#000000',
          fieldStroke: '#000000',
          fieldBackGround: '#ffffff',
        },
        fontFamily: {
          placeholder: 'Arial',
          question: 'Arial',
          text: 'Arial',
          answer: 'Arial',
        },
        fontSize: {
          placeholder: '14',
          question: 16,
          text: 14,
          answer: 14,
        },
      },
      headingStyle: {
        fontFamily: 'Arial',
        fontSize: 16,
        color: '#000000',
      },
      paragraphStyle: {
        fontFamily: 'Arial',
        fontSize: 14,
        color: '#000000',
      },
      buttonStyle: {
        type: 'primary',
        fullWidth: false,
        backgroundColor: '#000000',
        textColor: '#ffffff',
        fontFamily: 'Arial',
        fontSize: 14,
      },
      headerStyle: {
        position: 'top',
        logoImage: '',
        logoSize: AppearanceSettingsLogoSize.Medium,
        logoAlign: AppearanceSettingsLogoAlign.Left,
        isUsingText: true,
        text: 'Test Form',
      },
      footerStyle: {
        logoImage: 'https://google.com',
        logoSize: AppearanceSettingsLogoSize.Medium,
        logoAlign: AppearanceSettingsLogoAlign.Left,
        isUsingText: true,
        text: 'Test Description',
      },
    },
    setting: {
      submission: {
        mode: 'message',
        message: 'Thank you for your submission',
        caption: '',
        button: '',
        redirectUrl: '',
        enableBranding: true,
        limitResponse: false,
        limitNumber: 0,
      },
      notification: {
        isAutoresponse: false,
      },
      behavior: {
        isMultipleResponse: false,
      },
      systemMessage: [],
    },
    screenshot: {
      preview: '/preview.jpg',
      thumbnail: '/thumbnail.jpg',
      original: '/original.jpg',
    },
  };

  const mockPages = [
    {
      id: 'page-1',
      type: FieldType.Section,
      name: 'Page 1',
      layout: {
        type: FormLayoutType.ImageLeft,
        imageUrl: null,
        fieldWidth: '55%',
      },
      content: [
        {
          id: 'field-1',
          type: FieldType.ShortQA,
          label: 'Name',
          name: 'name',
          placeholder: 'Enter your name',
          required: true,
          validators: [
            {
              type: 'min_length',
              value: 3,
              message: 'Name must be at least 3 characters',
            },
          ],
        },
      ],
    },
    {
      id: 'page-2',
      type: FieldType.Section,
      name: 'Page 2',
      layout: {
        type: 'image-top-with-space',
        imageUrl: null,
        fieldWidth: '55%',
      },
      content: [
        {
          id: 'field-2',
          type: FieldType.ShortQA,
          label: 'Email',
          name: 'email',
          placeholder: 'Enter your email',
          required: true,
          validators: [
            {
              type: 'email',
              message: 'Please enter a valid email',
            },
          ],
        },
      ],
    },
  ];

  const defaultProps = {
    id: 'test-form-123',
    formSettings: mockFormSettings as any,
    pages: mockPages as any,
  };

  it('renders FormContainer wrapped with FormLogicProvider', () => {
    render(<FormContainerWithLogic {...defaultProps} />);

    expect(screen.getByTestId('form-logic-provider')).toBeInTheDocument();
    expect(screen.getByTestId('form-container')).toBeInTheDocument();
  });

  it('passes all props correctly to FormContainer', () => {
    const props = {
      ...defaultProps,
      preview: true,
      inputWidth: '400px',
      backgroundTransparency: '0.8',
      enableHeader: false,
      enableFooter: true,
      activePageIndex: 1,
      onChangePage: vi.fn(),
      onSizeChange: vi.fn(),
      onSubmit: vi.fn(),
    };

    render(<FormContainerWithLogic {...props} />);

    expect(screen.getByTestId('form-id')).toHaveTextContent('test-form-123');
    expect(screen.getByTestId('form-title')).toHaveTextContent('Test Form');
    expect(screen.getByTestId('pages-count')).toHaveTextContent('2');
    expect(screen.getByTestId('preview')).toHaveTextContent('true');
    expect(screen.getByTestId('input-width')).toHaveTextContent('400px');
    expect(screen.getByTestId('background-transparency')).toHaveTextContent('0.8');
    expect(screen.getByTestId('enable-header')).toHaveTextContent('false');
    expect(screen.getByTestId('enable-footer')).toHaveTextContent('true');
    expect(screen.getByTestId('active-page-index')).toHaveTextContent('1');
  });

  it('filters out hidden pages and extracts all fields for FormLogicProvider', () => {
    render(<FormContainerWithLogic {...defaultProps} />);

    // Should have 2 fields from 2 content pages (hidden page filtered out)
    expect(screen.getByTestId('fields-count')).toHaveTextContent('2');
  });

  it('generates correct default values for FormLogicProvider', () => {
    render(<FormContainerWithLogic {...defaultProps} />);

    const initialValuesText = screen.getByTestId('initial-values').textContent;
    const initialValues = JSON.parse(initialValuesText || '{}');

    // Should have default values for ShortQA and Email fields
    expect(initialValues).toHaveProperty('field-1', '');
    expect(initialValues).toHaveProperty('field-2', '');
  });

  it('handles empty pages array', () => {
    const propsWithEmptyPages = {
      ...defaultProps,
      pages: [],
    };

    render(<FormContainerWithLogic {...propsWithEmptyPages} />);

    expect(screen.getByTestId('fields-count')).toHaveTextContent('0');
    expect(screen.getByTestId('pages-count')).toHaveTextContent('0');
    expect(screen.getByTestId('initial-values')).toHaveTextContent('{}');
  });

  it('handles pages with only hidden type', () => {
    const propsWithOnlyHiddenPages = {
      ...defaultProps,
      pages: [
        {
          id: 'hidden-page-1',
          type: 'hidden' as const,
          content: [
            {
              id: 'hidden-field-1',
              type: FieldType.ShortQA,
              title: 'Hidden Field 1',
            },
          ],
        },
      ],
    };

    render(<FormContainerWithLogic {...(propsWithOnlyHiddenPages as any)} />);

    // Should filter out all hidden pages, resulting in 0 fields
    expect(screen.getByTestId('fields-count')).toHaveTextContent('0');
    expect(screen.getByTestId('pages-count')).toHaveTextContent('1');
  });

  it('handles pages with empty content arrays', () => {
    const propsWithEmptyContent = {
      ...defaultProps,
      pages: [
        {
          id: 'empty-page',
          type: 'content' as const,
          content: [],
        },
      ],
    };

    render(<FormContainerWithLogic {...(propsWithEmptyContent as any)} />);

    expect(screen.getByTestId('fields-count')).toHaveTextContent('0');
    expect(screen.getByTestId('pages-count')).toHaveTextContent('1');
    expect(screen.getByTestId('initial-values')).toHaveTextContent('{}');
  });

  it('preserves field logic definitions when extracting fields', () => {
    render(<FormContainerWithLogic {...defaultProps} />);

    // The FormLogicProvider should receive fields with their logic intact
    // This is tested indirectly by ensuring the fields count is correct
    // and that the component renders without errors
    expect(screen.getByTestId('form-logic-provider')).toBeInTheDocument();
    expect(screen.getByTestId('fields-count')).toHaveTextContent('2');
  });

  it('handles callback props correctly', () => {
    const mockCallbacks = {
      onChangePage: vi.fn(),
      onSizeChange: vi.fn(),
      onSubmit: vi.fn(),
    };

    const propsWithCallbacks = {
      ...defaultProps,
      ...mockCallbacks,
    };

    render(<FormContainerWithLogic {...propsWithCallbacks} />);

    // Verify the component renders successfully with callback props
    expect(screen.getByTestId('form-container')).toBeInTheDocument();
    expect(screen.getByTestId('form-logic-provider')).toBeInTheDocument();
  });
});
