/* eslint-disable no-unused-vars */
import { AppContextProvider } from '@/contexts/AppContext';
import { FormSettingsProvider } from '@/contexts/FormSettingsContext';
import { useLiff } from '@/contexts/LiffContext';
import { useSubmitForm } from '@/hooks/form/useSubmitForm';
import useFormValidation from '@/hooks/useFormValidation';
import {
  FieldType,
  type FormField,
  type FormSection,
  type FormSettings,
  type GroupFormField,
} from '@/types/form-builder';
import {
  camelToSnake,
  getDefaultValues,
  hasRequiredFields,
  shouldShowHeader,
  showErrorToast,
} from '@/utils';
import { Box, Flex, ScrollArea } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { type FormErrors, zodResolver } from '@mantine/form';
import { Notifications } from '@mantine/notifications';
import { useTranslate as useTranslation } from '@tolgee/react';
import { useRouter } from 'next/router';
import { useEffect, useRef, useState } from 'react';
import { FormProvider, useForm } from '../../contexts/FormContext';
import HiddenField from '../FormFields/HiddenField';
import Footer from '../FormHeader/Footer';
import Header from '../FormHeader/Header';
import FormLayout from '../FormLayout/FormLayout';
import Meta from '../Meta/Meta';

const useStyles = createStyles(
  (
    theme,
    {
      is2ColumnsLayout,
      backgroundTransparency = '1',
    }: { is2ColumnsLayout: boolean; backgroundTransparency?: string }
  ) => ({
    root: {
      backgroundColor: `rgba(255, 255, 255, ${backgroundTransparency});`,
      height: '100%',
      '@media (min-width: 1000px)': {
        height: is2ColumnsLayout ? '100vh' : '100%',
      },
    },
    formLayout: {
      flex: 1,
      [`@media (min-width: ${theme.breakpoints.sm})`]: {
        overflow: is2ColumnsLayout ? 'hidden' : 'initial',
      },
    },
    fullHeight: {
      height: '100%',
    },
    scrollArea: {
      '.mantine-ScrollArea-viewport > div': {
        height: '100%',
        width: '100%',
        tableLayout: 'fixed',
      },
    },
  })
);

interface FormContainerProps {
  id: string;
  formSettings: FormSettings;
  pages: FormSection[];
  preview?: boolean;
  inputWidth?: string;
  backgroundTransparency?: string;
  enableHeader?: boolean;
  enableFooter?: boolean;
  activePageIndex?: number;
  onChangePage?: (page: number) => void;
  onSizeChange?: (size: { height: number }) => void;
  onSubmit?: () => void;
}

const FormContainer = ({
  id,
  formSettings,
  pages,
  preview,
  backgroundTransparency,
  activePageIndex = 0,
  enableHeader = true,
  enableFooter = true,
  onChangePage,
  onSizeChange,
  onSubmit,
}: FormContainerProps) => {
  const { t } = useTranslation('common');
  const router = useRouter();
  const [activeIndex, setActiveIndex] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { submitForm } = useSubmitForm();
  const { liff } = useLiff();
  const scrollRef = useRef<HTMLDivElement>(null);

  const headerSettings = formSettings.appearance?.headerStyle;
  const footerSettings = formSettings.appearance?.footerStyle;
  const submissionSettings = formSettings?.setting?.submission;
  const hiddenFields = pages.find((page) => page.type === FieldType.Hidden)?.content as FormField[];
  const contentPages = pages.filter((page) => page.type !== FieldType.Hidden);
  const currentLayout = contentPages?.[activeIndex]?.layout.type;
  const is2ColumnsLayout = ['image-left', 'image-right'].includes(currentLayout);
  const { classes } = useStyles({
    is2ColumnsLayout,
    backgroundTransparency: backgroundTransparency,
  });
  const showSkipButton =
    !hasRequiredFields(contentPages[activeIndex]?.content) && activeIndex < contentPages.length - 1;
  const showHeader = enableHeader && shouldShowHeader(headerSettings);
  const showFooter = enableFooter && shouldShowHeader(footerSettings);
  const showOutsideFooter = showFooter && !is2ColumnsLayout;
  const showInsideFooter = showFooter && is2ColumnsLayout;

  const questions = contentPages[activeIndex].content || [];
  const validationSchema = useFormValidation(questions);
  const form = useForm({
    validateInputOnChange: true,
    initialValues: getDefaultValues(pages),
    validate: (values: Record<string, any>) => {
      const schema = validationSchema;
      return zodResolver(schema)(values);
    },
    transformValues: (values) => transformToAPIPayload(values),
  });

  useEffect(() => {
    setActiveIndex(activePageIndex);
  }, [activePageIndex]);

  useEffect(() => {
    const contentContainer = document.getElementById(`content_container_${activeIndex}`);
    if (contentContainer) {
      const headerHeight = document.getElementById('header')?.offsetHeight || 0;
      const footerHeight = document.getElementById('footer')?.offsetHeight || 0;
      const pageHeight = contentContainer.scrollHeight + headerHeight + footerHeight;
      onSizeChange?.({
        height: pageHeight,
      });
    }
  }, [activeIndex, onSizeChange]);

  useEffect(() => {
    scrollRef.current!.scrollTo({ top: 0, behavior: 'smooth' });
  }, [activeIndex]);

  const prevStep = () => {
    const newIndex = activeIndex > 0 ? activeIndex - 1 : activeIndex;
    setActiveIndex(newIndex);
    onChangePage?.(newIndex);
  };

  const nextStep = () => {
    const { errors, hasErrors } = form.validate();
    if (hasErrors) {
      handleValidationFailure(errors);
      return;
    }
    const newIndex = activeIndex < contentPages.length - 1 ? activeIndex + 1 : activeIndex;
    setActiveIndex(newIndex);
    onChangePage?.(newIndex);
  };

  const handleSubmit = async (formValues) => {
    if (preview) return;
    setIsSubmitting(true);
    try {
      if (liff) {
        formValues.integration = {
          id: router.query.integration_id,
          data: {
            access_token: liff.getAccessToken(),
          },
        };
      }
      const response = await submitForm(formValues);
      if (response) {
        handleSubmitSuccess(response);
      }
    } catch (error) {
      setIsSubmitting(false);
      handleSubmitError(error);
    }
  };

  const transformToAPIPayload = (formValues) => {
    const questionPageIdMap = generateQuestionPageIdMap();
    const answers: Record<string, any>[] = [];
    Object.keys(formValues).forEach((key) => {
      if ([null, ''].includes(formValues[key])) return;
      answers.push({
        question_id: key,
        page_id: questionPageIdMap[key],
        values: (Array.isArray(formValues[key]) ? formValues[key] : [formValues[key]]).map(
          (rawValue) => transformValue(rawValue, key)
        ),
      });
    });

    return {
      form_id: id,
      answers: answers,
    };
  };

  const generateQuestionPageIdMap = () => {
    const questionPageIdMap = {};
    const setQuestionValidation = (questions, pageId) => {
      questions.forEach((question) => {
        if ((question as GroupFormField).fields?.length) {
          setQuestionValidation(question.fields, pageId);
        } else {
          questionPageIdMap[question.id] = pageId;
        }
      });
    };

    pages.forEach((section) => {
      setQuestionValidation(section.content, section.id);
    });

    return questionPageIdMap;
  };

  const transformValue = (rawValue, questionId: string) => {
    const isOther = rawValue?.is_other ?? false;
    let value = rawValue?.value ?? rawValue;
    if (value instanceof Date) {
      value = value.toISOString();
    }
    const question = contentPages[activeIndex].content.find(
      (question) => question.id === questionId
    );
    if (question && question.type === FieldType.DateSelector) {
      const parts = value.split('-').map(Number);
      const year = parts[0];
      const month = parts[1];
      const day = parts[2];
      const date = new Date(year, month - 1, day);
      value = new Date(date).toISOString();
    }
    if (value.asset) {
      return {
        value: value.asset.name,
        asset: camelToSnake(value.asset),
      };
    }
    const resultValue: any = {
      value: value,
      is_other: isOther,
    };
    if (rawValue.maxScale) {
      resultValue.maxScale = rawValue.maxScale;
    }
    return resultValue;
  };

  const handleSubmitSuccess = (response) => {
    if (submissionSettings.mode === 'redirect') {
      window.location.href = submissionSettings.redirectUrl;
      return;
    }
    if (['message', 'message_redirect'].includes(submissionSettings.mode)) {
      localStorage.setItem('response_id', response.id);
      router.push(`/${id}/success`);
      router.events.on('routeChangeComplete', () => {
        onSubmit?.();
      });
    } else {
      onSubmit?.();
    }
  };

  const handleSubmitError = (error: Error) => {
    const isLimitError = error.toString()?.includes('limit reached');
    const errorMessage = isLimitError ? t('limitErrorMessage') : t('submitErrorMessage');
    showErrorToast(errorMessage);
  };

  const handleValidationFailure = (errors: FormErrors) => {
    const firstErrorPath = Object.keys(errors)[0];
    const inputElm = form.getInputNode(firstErrorPath);
    if (inputElm) {
      inputElm.scrollIntoView();
      inputElm.focus();
    }
  };

  return (
    <>
      <Meta
        url=''
        title={formSettings.name}
        description={formSettings.description}
        image={formSettings.screenshot.preview || formSettings.screenshot.thumbnail}
      />
      <Notifications position='top-right' />
      <ScrollArea h={'100%'} type='scroll' viewportRef={scrollRef} className={classes.scrollArea}>
        <AppContextProvider>
          <FormSettingsProvider
            contentPages={contentPages}
            formSettings={formSettings}
            hiddenFields={hiddenFields}
          >
            <Flex direction='column' className={classes.root}>
              {showHeader && <Header {...headerSettings} />}
              <Box className={classes.formLayout}>
                <FormProvider form={form}>
                  <form
                    className={classes.fullHeight}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !(e.target as HTMLElement).closest('textarea')) {
                        e.preventDefault();
                      }
                    }}
                  >
                    {hiddenFields?.map((field) => (
                      <HiddenField key={field.id} field={field as FormField} />
                    ))}
                    {contentPages.map((page, index) => (
                      <Box hidden={index !== activeIndex} key={index} h='100%'>
                        <FormLayout
                          index={index}
                          settings={page}
                          isSaving={isSubmitting}
                          backgroundTransparency={backgroundTransparency}
                          onNext={nextStep}
                          onBack={prevStep}
                          onSubmit={form.onSubmit(handleSubmit, handleValidationFailure)}
                          showBackButton={index > 0}
                          showNextButton={index < contentPages.length - 1}
                          showSubmitButton={index === contentPages.length - 1}
                          showSkipButton={showSkipButton}
                          renderFooter={() => showInsideFooter && <Footer {...footerSettings} />}
                        />
                      </Box>
                    ))}
                  </form>
                </FormProvider>
              </Box>
              {showOutsideFooter && <Footer {...footerSettings} />}
            </Flex>
          </FormSettingsProvider>
        </AppContextProvider>
      </ScrollArea>
    </>
  );
};

export default FormContainer;
