import { AppContextProvider } from '@/contexts/AppContext';
import { FormProvider, useForm } from '@/contexts/FormContext';
import { FormLogicProvider } from '@/contexts/FormLogicContext';
import { FormSettingsProvider } from '@/contexts/FormSettingsContext';
import { useSubmitForm } from '@/hooks/form/useSubmitForm';
import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import { FieldType, FormLayoutType } from '@/types/form-builder';
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { TolgeeProvider } from '@tolgee/react';
import { type Mock, afterAll, beforeEach, describe, expect, it, vi } from 'vitest';
import FormContainer from './FormContainer';

// Mock dependencies
vi.mock('@/contexts/LiffContext', () => ({
  useLiff: () => ({
    isLoggedIn: false,
    profile: null,
  }),
}));

vi.mock('@/hooks/form/useSubmitForm', () => ({
  useSubmitForm: vi.fn(() => ({
    submitForm: vi.fn(),
    isSubmitting: false,
  })),
}));

vi.mock('next/router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    query: {},
  }),
}));

vi.mock('@/utils/form', async (importOriginal) => ({
  ...(await importOriginal()),
  showErrorToast: vi.fn(),
}));

// Mock Tolgee useTranslate hook
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
    i18n: {
      language: 'en',
      changeLanguage: vi.fn(),
    },
  }),
  TolgeeProvider: ({ children }: { children: React.ReactNode }) => children,
}));

vi.mock('@/contexts/LiffContext', async (importOriginal) => ({
  ...(await importOriginal()),
  useLiff: () => ({
    liff: {
      getAccessToken: vi.fn().mockReturnValue('abc'),
    },
  }),
}));

// Mock tolgee instance
const mockTolgee = {
  getLanguage: vi.fn(() => 'en'),
  changeLanguage: vi.fn(),
  isLoaded: vi.fn(() => true),
  getRequiredRecords: vi.fn(() => []),
  on: vi.fn(),
  off: vi.fn(),
  stop: vi.fn(),
  start: vi.fn(),
  t: vi.fn(() => 'translated'),
  onNsUpdate: vi.fn(),
  subscribeNs: vi.fn(),
  addPlugin: vi.fn(),
  updateOptions: vi.fn(),
  getCurrentLanguage: vi.fn(() => 'en'),
  getCurrentNamespace: vi.fn(() => 'common'),
  getNamespaces: vi.fn(() => ['common']),
  getDefaultLanguage: vi.fn(() => 'en'),
  getDefaultNamespace: vi.fn(() => 'common'),
  getFallbackLanguage: vi.fn(() => 'en'),
  getFallbackNamespace: vi.fn(() => 'common'),
  getAvailableLanguages: vi.fn(() => ['en']),
  getAvailableNamespaces: vi.fn(() => ['common']),
  isLanguageLoaded: vi.fn(() => true),
  isNamespaceLoaded: vi.fn(() => true),
  loadLanguage: vi.fn(),
  loadNamespace: vi.fn(),
  reloadLanguage: vi.fn(),
  reloadNamespace: vi.fn(),
  removeLanguage: vi.fn(),
  removeNamespace: vi.fn(),
  setLanguage: vi.fn(),
  setNamespace: vi.fn(),
  setDefaultLanguage: vi.fn(),
  setDefaultNamespace: vi.fn(),
  setFallbackLanguage: vi.fn(),
  setFallbackNamespace: vi.fn(),
  setAvailableLanguages: vi.fn(),
  setAvailableNamespaces: vi.fn(),
  setOptions: vi.fn(),
  getOptions: vi.fn(() => ({})),
  getInstance: vi.fn(() => mockTolgee),
} as any;

const mockFormSettings = {
  id: 'test-form',
  name: 'Test Form',
  description: 'Test Description',
  startAt: '2024-01-01T00:00:00Z',
  expiredAt: '2024-12-31T23:59:59Z',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  status: 'active',
  permissions: ['read', 'write'],
  isFavorited: false,
  isPinned: false,
  responses: 0,
  tags: [],
  urls: {
    public: '/public-url',
    embed: '/embed-url',
    private: '/private-url',
  },
  metadata: {
    organizationId: 'org-id',
    workspaceId: 'workspace-id',
  },
  appearance: {
    customize: false,
    defaultSettings: {
      color: '#000000',
      font: 'Arial',
      inputStyle: InputStyle.Classic,
    },
    formFieldStyle: {
      color: {
        placeholder: '#000000',
        question: '#000000',
        answer: '#000000',
        icon: '#000000',
        description: '#000000',
        fieldStroke: '#000000',
        fieldBackGround: '#ffffff',
      },
      fontFamily: {
        placeholder: 'Arial',
        question: 'Arial',
        text: 'Arial',
        answer: 'Arial',
      },
      fontSize: {
        placeholder: '14',
        question: 16,
        text: 14,
        answer: 14,
      },
    },
    headingStyle: {
      fontFamily: 'Arial',
      fontSize: 16,
      color: '#000000',
    },
    paragraphStyle: {
      fontFamily: 'Arial',
      fontSize: 14,
      color: '#000000',
    },
    buttonStyle: {
      type: 'primary',
      fullWidth: false,
      backgroundColor: '#000000',
      textColor: '#ffffff',
      fontFamily: 'Arial',
      fontSize: 14,
    },
    headerStyle: {
      position: 'top',
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: true,
      text: 'Test Form',
    },
    footerStyle: {
      logoImage: 'https://google.com',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: true,
      text: 'Test Description',
    },
  },
  setting: {
    submission: {
      mode: 'message',
      message: 'Thank you for your submission',
      caption: '',
      button: '',
      redirectUrl: '',
      enableBranding: true,
      limitResponse: false,
      limitNumber: 0,
    },
    notification: {
      isAutoresponse: false,
    },
    behavior: {
      isMultipleResponse: false,
    },
    systemMessage: [],
  },
  screenshot: {
    preview: '/preview.jpg',
    thumbnail: '/thumbnail.jpg',
    original: '/original.jpg',
  },
};

const mockPages = [
  {
    id: 'page-1',
    type: FieldType.Section,
    name: 'Page 1',
    layout: {
      type: FormLayoutType.ImageLeft,
      imageUrl: null,
      fieldWidth: '55%',
    },
    content: [
      {
        id: 'field-1',
        type: FieldType.ShortQA,
        label: 'Name',
        name: 'name',
        placeholder: 'Enter your name',
        required: true,
        validators: [
          {
            type: 'min_length',
            value: 3,
            message: 'Name must be at least 3 characters',
          },
        ],
      },
    ],
  },
  {
    id: 'page-2',
    type: FieldType.Section,
    name: 'Page 2',
    layout: {
      type: 'image-top-with-space',
      imageUrl: null,
      fieldWidth: '55%',
    },
    content: [
      {
        id: 'field-2',
        type: FieldType.ShortQA,
        label: 'Email',
        name: 'email',
        placeholder: 'Enter your email',
        required: true,
        validators: [
          {
            type: 'email',
            message: 'Please enter a valid email',
          },
        ],
      },
    ],
  },
];

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const mockForm = useForm({
    initialValues: {},
  });

  return (
    <MantineEmotionProvider>
      <MantineProvider
        theme={{
          colors: {
            decaLight: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
            decaNavy: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
            decaDark: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
            decaGrey: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
          },
          breakpoints: {
            sm: '768px',
          },
        }}
      >
        <TolgeeProvider tolgee={mockTolgee} fallback={<div>Loading...</div>}>
          <AppContextProvider>
            <FormLogicProvider fields={[]}>
              <FormSettingsProvider formSettings={mockFormSettings as any} hiddenFields={[]}>
                <FormProvider form={mockForm}>{children}</FormProvider>
              </FormSettingsProvider>
            </FormLogicProvider>
          </AppContextProvider>
        </TolgeeProvider>
      </MantineProvider>
    </MantineEmotionProvider>
  );
};

const renderWithProviders = (component: React.ReactElement) => {
  return render(<TestWrapper>{component}</TestWrapper>);
};

describe('FormContainer', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (useSubmitForm as Mock).mockReturnValue({
      submitForm: vi.fn(),
      isSubmitting: false,
    });
    // Mock scrollTo method
    Element.prototype.scrollTo = vi.fn();

    // Mock getElementById for header and footer
    document.getElementById = vi.fn((id) => {
      if (id === 'header' || id === 'footer') {
        return {
          offsetHeight: 50,
        } as any;
      }
      return null;
    });

    // Mock ResizeObserver
    global.ResizeObserver = vi.fn().mockImplementation(() => ({
      observe: vi.fn(),
      unobserve: vi.fn(),
      disconnect: vi.fn(),
    }));
  });
  afterAll(() => {
    vi.clearAllMocks();
  });

  it('renders form container with header and footer', () => {
    renderWithProviders(
      <FormContainer
        id='test-form'
        formSettings={mockFormSettings as any}
        pages={mockPages as any}
        enableHeader={true}
        enableFooter={true}
      />
    );

    expect(screen.getByText('Test Form')).toBeInTheDocument();
    expect(screen.getByText('Test Description')).toBeInTheDocument();
  });

  it('renders form container without header and footer when disabled', () => {
    renderWithProviders(
      <FormContainer
        id='test-form'
        formSettings={mockFormSettings as any}
        pages={mockPages as any}
        enableHeader={false}
        enableFooter={false}
      />
    );

    expect(screen.queryByText('Test Form')).not.toBeInTheDocument();
  });

  it('renders form fields correctly', () => {
    renderWithProviders(
      <FormContainer
        id='test-form'
        formSettings={mockFormSettings as any}
        pages={mockPages as any}
      />
    );

    expect(screen.getByLabelText('Name')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter your name')).toBeInTheDocument();
  });

  it('applies custom background transparency', () => {
    renderWithProviders(
      <FormContainer
        id='test-form'
        formSettings={mockFormSettings as any}
        pages={mockPages as any}
        backgroundTransparency='0.8'
      />
    );

    const container = screen.getByText('Name');
    expect(container).toBeInTheDocument();
  });

  it('handles size change callback', () => {
    const onSizeChange = vi.fn();

    renderWithProviders(
      <FormContainer
        id='test-form'
        formSettings={mockFormSettings as any}
        pages={mockPages as any}
        onSizeChange={onSizeChange}
      />
    );

    // The onSizeChange should be called when the component mounts
    // Since the mock getElementById returns null for content_container_0, it won't be called
    expect(onSizeChange).not.toHaveBeenCalled();
  });

  it('navigates to next page when nextStep is called', () => {
    const onChangePage = vi.fn();
    renderWithProviders(
      <FormContainer
        id='test-form'
        formSettings={mockFormSettings as any}
        pages={mockPages as any}
        onChangePage={onChangePage}
        activePageIndex={0}
      />
    );

    const nextButton = screen.getByRole('button', { name: /next/i });
    nextButton.click();

    expect(onChangePage).toHaveBeenCalledWith(1);
  });

  it('navigates to previous page when prevStep is called', () => {
    const onChangePage = vi.fn();
    renderWithProviders(
      <FormContainer
        id='test-form'
        formSettings={mockFormSettings as any}
        pages={mockPages as any}
        onChangePage={onChangePage}
        activePageIndex={1}
      />
    );

    const prevButton = screen.getByText(/back/i);
    prevButton.click();

    expect(onChangePage).toHaveBeenCalledWith(0);
  });

  it('handles form submission correctly', async () => {
    (useSubmitForm as Mock).mockReturnValue({
      submitForm: vi.fn().mockResolvedValue({
        id: 'test-form',
      }),
    });
    const onSubmit = vi.fn();
    const { container } = renderWithProviders(
      <FormContainer
        id='test-form'
        formSettings={
          {
            ...mockFormSettings,
            setting: {
              ...mockFormSettings.setting,
              submission: {
                ...mockFormSettings.setting.submission,
                mode: 'test',
              },
            },
          } as any
        }
        pages={[mockPages[0]] as any}
        onSubmit={onSubmit}
        activePageIndex={0}
      />
    );

    const input = container.querySelector('input');
    fireEvent.change(input!, { target: { value: 'valuee' } });
    const submitButton = screen.getByText(/submit/i);

    submitButton.click();
    await waitFor(() => {
      expect(onSubmit).toHaveBeenCalled();
    });
  });

  it('shows validation error for invalid name', async () => {
    renderWithProviders(
      <FormContainer
        id='test-form'
        formSettings={mockFormSettings as any}
        pages={mockPages as any}
        activePageIndex={0}
      />
    );

    const nameInput = screen.getByPlaceholderText('Enter your name');
    fireEvent.change(nameInput, { target: { value: 'ab' } });

    const nextButton = screen.getByRole('button', { name: /next/i });
    nextButton.click();

    await waitFor(() => {
      expect(screen.getByText('Name must be at least 3 characters')).toBeInTheDocument();
    });
  });

  it('shows validation error for invalid email', async () => {
    renderWithProviders(
      <FormContainer
        id='test-form'
        formSettings={mockFormSettings as any}
        pages={mockPages as any}
        activePageIndex={1}
      />
    );

    const emailInput = screen.getByPlaceholderText('Enter your email');
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });

    const nextButton = screen.getByRole('button', { name: /submit/i });
    nextButton.click();

    await waitFor(() => {
      expect(screen.getByText('Please enter a valid email')).toBeInTheDocument();
    });
  });

  it('shows validation error for max length exceeded', async () => {
    // Create a mock page with a field that has max_length validator
    const mockPageWithMaxLength = {
      id: 'page-max-length',
      type: FieldType.Section,
      name: 'Max Length Test',
      layout: {
        type: FormLayoutType.ImageLeft,
        imageUrl: null,
        fieldWidth: '55%',
      },
      content: [
        {
          id: 'field-max-length',
          type: FieldType.ShortQA,
          label: 'Short Description',
          name: 'description',
          placeholder: 'Enter a short description',
          required: true,
          validators: [
            {
              type: 'max_length',
              value: 10,
              message: 'This field must have at most 10 characters',
            },
          ],
        },
      ],
    };

    renderWithProviders(
      <FormContainer
        id='test-form'
        formSettings={mockFormSettings as any}
        pages={[mockPageWithMaxLength] as any}
        activePageIndex={0}
      />
    );

    const descriptionInput = screen.getByPlaceholderText('Enter a short description');
    fireEvent.change(descriptionInput, {
      target: { value: 'This is a very long description that exceeds the limit' },
    });

    const nextButton = screen.getByRole('button', { name: /submit/i });
    nextButton.click();

    await waitFor(() => {
      expect(screen.getByText('This field must have at most 10 characters')).toBeInTheDocument();
    });
  });

  it('show correctly with mockdocument get by Id', async () => {
    vi.spyOn(document, 'getElementById').mockReturnValueOnce({} as any);
    vi.spyOn(document, 'getElementById').mockReturnValueOnce({ offsetHeight: 200 } as any);
    vi.spyOn(document, 'getElementById').mockReturnValueOnce({} as any);

    const { container } = renderWithProviders(
      <FormContainer
        id='test-form'
        formSettings={mockFormSettings as any}
        pages={[mockPages[0]] as any}
        activePageIndex={0}
        onSizeChange={vi.fn()}
        enableFooter={true}
      />
    );

    const input = container.querySelector('input');
    fireEvent.change(input!, { target: { value: 'valuee' } });
    fireEvent.keyDown(input!, { key: 'Enter' });

    const submitButton = screen.getByText(/submit/i);
    submitButton.click();
  });
});
