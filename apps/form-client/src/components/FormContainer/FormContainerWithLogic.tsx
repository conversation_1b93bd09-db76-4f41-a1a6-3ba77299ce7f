/* eslint-disable no-unused-vars */
import { FormLogicProvider } from '@/contexts/FormLogicContext';
import type { FormSection, FormSettings } from '@/types/form-builder';
import { getDefaultValues } from '@/utils/form';
import FormContainer from './FormContainer';

interface FormContainerWithLogicProps {
  id: string;
  formSettings: FormSettings;
  pages: FormSection[];
  preview?: boolean;
  inputWidth?: string;
  backgroundTransparency?: string;
  enableHeader?: boolean;
  enableFooter?: boolean;
  activePageIndex?: number;
  onChangePage?: (page: number) => void;
  onSizeChange?: (size: { height: number }) => void;
  onSubmit?: () => void;
}

/**
 * FormContainer wrapper that provides FormLogic for dynamic validation
 * This ensures that form validation respects field visibility state
 */
const FormContainerWithLogic = (props: FormContainerWithLogicProps) => {
  // Extract all fields from all pages for FormLogic
  const contentPages = props.pages.filter((page) => page.type !== 'hidden');
  const allFields = contentPages.flatMap((page) => page.content);
  const defaultValues = getDefaultValues(contentPages);

  return (
    <FormLogicProvider fields={allFields} initialValues={defaultValues}>
      <FormContainer {...props} />
    </FormLogicProvider>
  );
};

export default FormContainerWithLogic;
