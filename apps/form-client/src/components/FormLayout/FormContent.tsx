import ConfirmationStep from '@/components/ConfirmationStep/ConfirmationStep';
import { useAppContext } from '@/contexts/AppContext';
import { useFormContext } from '@/contexts/FormContext';
import { useFormSettings } from '@/contexts/FormSettingsContext';
import useButtonStyles from '@/hooks/form/useButtonStyles';
import { Box, Flex, Space } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import type { FormErrors } from '@mantine/form';
import { useTranslate as useTranslation } from '@tolgee/react';
import { useState } from 'react';
import { DecaButton } from '../DecaButton';
import FormFields from '../FormFields/FormFields';
import type { FormLayoutProps } from './FormLayout';

const useStyles = createStyles((theme) => ({
  content: {
    flex: 1,
    [`@media (min-width: ${theme.breakpoints.xs})`]: {
      flexGrow: 0,
    },
  },
}));

const FormContent = ({
  settings,
  showNextButton = true,
  showBackButton,
  showSubmitButton,
  showSkipButton,
  isSaving,
  onSubmit,
  onNext,
  onBack,
}: FormLayoutProps) => {
  const { t } = useTranslation('common');
  const { appearance, setting } = useFormSettings();
  const buttonStyles = useButtonStyles(
    appearance?.customize,
    appearance?.buttonStyle,
    appearance?.defaultSettings
  );
  const { classes } = useStyles();
  const { buttonsDisabled } = useAppContext();
  const [showConfirmation, setShowConfirmation] = useState(false);
  const form = useFormContext();

  const handleValidationFailure = (errors: FormErrors) => {
    const firstErrorPath = Object.keys(errors)[0];
    const inputElm = form.getInputNode(firstErrorPath);
    if (inputElm) {
      inputElm.scrollIntoView();
      inputElm.focus();
    }
  };

  const handleConfirm = () => {
    const { errors, hasErrors } = form.validate();
    if (hasErrors) {
      handleValidationFailure(errors);
      return;
    }
    setShowConfirmation(true);
  };

  const renderContent = () => {
    return (
      <>
        <Box className={classes.content}>
          <FormFields fields={settings.content} />
          <Space h='xl' />
        </Box>
        <Flex justify='space-between' gap={'lg'}>
          <Flex gap={'sm'} sx={{ flex: 1 }}>
            {showBackButton && (
              <DecaButton
                {...buttonStyles}
                variant='neutral'
                onClick={onBack}
                disabled={buttonsDisabled}
              >
                {t('back')}
              </DecaButton>
            )}
            {showNextButton && (
              <DecaButton {...buttonStyles} onClick={onNext} disabled={buttonsDisabled}>
                {t('next')}
              </DecaButton>
            )}
            {showSubmitButton && (
              <DecaButton
                {...buttonStyles}
                onClick={setting?.submission?.enableConfirmationScreen ? handleConfirm : onSubmit}
                loading={isSaving}
                disabled={buttonsDisabled}
              >
                {setting?.submission?.enableConfirmationScreen ? t('confirm') : t('submit')}
              </DecaButton>
            )}
          </Flex>
          {showSkipButton && (
            <DecaButton
              {...buttonStyles}
              w={100}
              variant='neutral_text'
              // styles={theme => ({
              //   '&.mantine-Button-root': {
              //     color: theme.colors.decaBlue[5],
              //   },
              // })}
              onClick={onNext}
              disabled={buttonsDisabled}
            >
              {t('skip')}
            </DecaButton>
          )}
        </Flex>
      </>
    );
  };

  const renderConfirmation = () => {
    return (
      <Box className={classes.content}>
        <ConfirmationStep onCancel={() => setShowConfirmation(false)} onSubmit={onSubmit} />
      </Box>
    );
  };

  return (
    <Flex direction={'column'}>{showConfirmation ? renderConfirmation() : renderContent()}</Flex>
  );
};

export default FormContent;
