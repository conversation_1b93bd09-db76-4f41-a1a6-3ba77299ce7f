import {
  IconCrown,
  IconHeartFilled,
  IconMoodSmileBeam,
  IconStarFilled,
  IconThumbUpFilled,
} from '@tabler/icons-react';
import type { ComponentType } from 'react';

const RatingAnswer = ({ shape }: { shape: string }) => {
  const renderIcon = (Icon: ComponentType<any>, color: string, fill?: string) => {
    return <Icon size={28} color={color} fill={fill || color} />;
  };

  const renderFullIcon = (shape: string) => {
    switch (shape) {
      case 'heart':
        return renderIcon(IconHeartFilled, '#F93549');
      case 'like':
        return renderIcon(IconThumbUpFilled, '#4699F6');
      case 'crown':
        return renderIcon(IconCrown, '#FFD100', '#FFD100');
      case 'smiley':
        return renderIcon(IconMoodSmileBeam, '#62D821');
      default:
        return renderIcon(IconStarFilled, '#FFD100');
    }
  };
  return <div>{renderFullIcon(shape)}</div>;
};

export default RatingAnswer;
