import {
  IconCrown,
  IconHeartFilled,
  IconMoodSmileBeam,
  IconStarFilled,
  IconThumbUpFilled,
} from '@tabler/icons-react';
import type { ComponentType } from 'react';

const RatingAnswer = ({ shape, value }: { shape: string; value: number }) => {
  const renderIcon = (Icon: ComponentType<any>, color: string, value: number, fill?: string) => {
    return Array.from(Array(value)).map((_, index) => (
      <Icon size={28} color={color} fill={fill} key={index} />
    ));
  };

  const renderFullIcon = (shape: string, value: number) => {
    switch (shape) {
      case 'heart':
        return renderIcon(IconHeartFilled, '#F93549', value);
      case 'like':
        return renderIcon(IconThumbUpFilled, '#4699F6', value);
      case 'crown':
        return renderIcon(IconCrown, '#FFD100', value, '#FFD100');
      case 'smiley':
        return renderIcon(IconMoodSmileBeam, '#62D821', value);
      default:
        return renderIcon(IconStarFilled, '#FFD100', value);
    }
  };
  return <div>{renderFullIcon(shape, value)}</div>;
};

export default RatingAnswer;
