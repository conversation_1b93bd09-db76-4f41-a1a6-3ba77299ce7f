import { screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { renderWithProviders } from '@/test-utils';
import { describe, expect, it, vi } from 'vitest';
import FilePreview from '../FilePreview';

// <PERSON><PERSON>'s createStyles to avoid theme-related issues in tests
vi.mock('@mantine/emotion', async (importOriginal) => {
  return {
    ...(await importOriginal()),
    createStyles: () => () => ({ classes: { fileResult: 'fileResult' } }),
  };
});

describe('FilePreview', () => {
  it('renders file name and size in KB when size < 1024 bytes', () => {
    renderWithProviders(<FilePreview asset={{ name: 'report.pdf', size: 500 } as any} />);
    expect(screen.getByText('report.pdf')).toBeInTheDocument();
    // 500 bytes -> 0.49KB (500 / 1024 rounded to 2 decimals)
    expect(screen.getByText('0.49KB')).toBeInTheDocument();
  });

  it('renders 1KB when size is exactly 1024 bytes', () => {
    renderWithProviders(<FilePreview asset={{ name: 'image.png', size: 1024 } as any} />);
    expect(screen.getByText('image.png')).toBeInTheDocument();
    expect(screen.getByText('1KB')).toBeInTheDocument();
  });

  it('renders MB for large sizes (e.g., 1MB)', () => {
    renderWithProviders(<FilePreview asset={{ name: 'archive.zip', size: 1024 * 1024 } as any} />);
    expect(screen.getByText('archive.zip')).toBeInTheDocument();
    expect(screen.getByText('1MB')).toBeInTheDocument();
  });
});
