import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import ConfirmationStep from '@/components/ConfirmationStep/ConfirmationStep';
import { renderWithProviders } from '@/test-utils';
import { FieldType } from '@/types/form-builder';

// Mock FieldType enum and type
vi.mock('@/types/form-builder', async (importOriginal) => ({
  ...(await importOriginal()),
  FieldType: {
    Date: 'date',
    DateSelector: 'dateSelector',
    DateTime: 'dateTime',
    DateRange: 'dateRange',
    Rating: 'rating',
    OpinionScale: 'opinionScale',
    Checkbox: 'checkbox',
  },
}));

vi.mock('@mantine/emotion', async (importOriginal) => ({
  ...(await importOriginal()),
  createStyles: () => () => ({
    classes: {
      questionTitle: 'questionTitle',
    },
  }),
}));

// Mock i18n translation
vi.mock('@tolgee/react', async (importOriginal) => ({
  ...(await importOriginal()),
  useTranslate: () => ({
    t: (key: string, opts?: Record<string, any>) =>
      key === 'common:page' || key === 'page' ? `Page ${opts?.page}` : key,
  }),
}));

// Mock DecaButton as a regular button
vi.mock('@/components/DecaButton', () => ({
  DecaButton: ({ children, onClick, ...rest }: any) => (
    <button onClick={onClick} {...rest}>
      {children}
    </button>
  ),
}));

// Mock RatingAnswer so we can assert props easily
vi.mock('@/components/ConfirmationStep/RatingAnswer', () => ({
  default: ({ shape, value }: { shape: string; value: number }) => (
    <span data-testid='rating-answer' data-shape={shape} data-value={value} />
  ),
}));

// Mock FormContext with a controllable values bag
let mockValues: Record<string, any> = {};
vi.mock('@/contexts/FormContext', async (importOriginal) => ({
  ...(await importOriginal()),
  useFormContext: () => ({ values: mockValues }),
}));

// Mock FormSettingsContext with controllable contentPages
let mockContentPages: any[] = [];
vi.mock('@/contexts/FormSettingsContext', async (importOriginal) => ({
  ...(await importOriginal()),
  useFormSettings: () => ({ contentPages: mockContentPages }),
}));

describe('ConfirmationStep', () => {
  beforeEach(() => {
    mockValues = {};
    mockContentPages = [];
  });

  it('renders all fields with proper formatting and handles actions', () => {
    // Arrange: build a page with various field types
    const dateField = {
      id: 'f-date',
      type: FieldType.Date,
      label: '<strong>Date of birth</strong>',
      name: 'dob',
    };
    const dateSelectorField = {
      id: 'f-date-selector',
      type: FieldType.DateSelector,
      label: '<i>Select a date</i>',
      name: 'ds',
    };
    const dateTimeField = {
      id: 'f-datetime',
      type: FieldType.DateTime,
      label: 'Appointment time',
      name: 'dt',
    };
    const dateRangeField = {
      id: 'f-daterange',
      type: FieldType.DateRange,
      label: 'Period',
      name: 'dr',
    };
    const ratingField = {
      id: 'f-rating',
      type: FieldType.Rating,
      label: 'Satisfaction',
      name: 'rate',
      shape: 'star',
    };
    const opinionScaleField = {
      id: 'f-opinion',
      type: FieldType.OpinionScale,
      label: 'How likely?',
      name: 'ops',
    };
    const textField = {
      id: 'f-text',
      type: 'text',
      label: 'Comments',
      name: 'comments',
    };
    const checkboxField = {
      id: 'f-checkbox',
      type: FieldType.Checkbox,
      label: 'I agree to the terms',
      name: 'agree',
    };

    mockContentPages = [
      {
        id: 'page-1',
        name: 'Intro',
        content: [
          dateField,
          dateSelectorField,
          dateTimeField,
          dateRangeField,
          ratingField,
          opinionScaleField,
          textField,
          checkboxField,
        ],
      },
    ];

    mockValues = {
      [dateField.id]: '2025-01-02',
      [dateSelectorField.id]: '2025-01-03',
      [dateTimeField.id]: '2025-01-02T10:30',
      [dateRangeField.id]: ['2025-01-02T09:00', '2025-01-05T17:00'],
      [ratingField.id]: 3,
      [opinionScaleField.id]: { value: 7 },
      [textField.id]: 'Hello world',
      [checkboxField.id]: ['Option A', 'Option B'], // label should not render, answer will render as "Option A,Option B"
    };

    const onCancel = vi.fn();
    const onSubmit = vi.fn();

    // Act
    const { container } = renderWithProviders(
      <ConfirmationStep onCancel={onCancel} onSubmit={onSubmit} />
    );

    // Assert: page title translation
    expect(screen.getByText('Page 1: Intro')).toBeInTheDocument();

    // Assert: labels inserted with dangerouslySetInnerHTML (non-checkbox only)
    const questionTitles = container.querySelectorAll('.questionTitle');
    expect(questionTitles.length).toBe(8);

    // Ensure the HTML label is intact for one example
    expect(questionTitles[0].innerHTML).toBe('<strong>Date of birth</strong>');
    expect(questionTitles[1].innerHTML).toBe('<i>Select a date</i>');

    // Checkbox label should not appear
    expect(screen.queryByText('I agree to the terms')).toBeInTheDocument();

    // Assert: formatted answers
    expect(screen.getByText('2025/01/02')).toBeInTheDocument(); // Date
    expect(screen.getByText('2025/01/03')).toBeInTheDocument(); // DateSelector
    expect(screen.getByText('2025/01/02 10:30')).toBeInTheDocument(); // DateTime
    expect(screen.getByText('2025/01/02 09:00 - 2025/01/05 17:00')).toBeInTheDocument(); // DateRange

    // Rating: shows "value/max" and passes through to RatingAnswer
    const ratingAnswer = screen.getByTestId('rating-answer');
    expect(ratingAnswer).toHaveAttribute('data-shape', 'star');

    // OpinionScale: renders numeric value
    expect(screen.getByText('7')).toBeInTheDocument();

    // Default (text)
    expect(screen.getByText('Hello world')).toBeInTheDocument();

    // Checkbox answer rendered as array (comma-joined by React to string)
    // Buttons call handlers
    fireEvent.click(screen.getByRole('button', { name: /back/i }));
    expect(onCancel).toHaveBeenCalledTimes(1);

    fireEvent.click(screen.getByRole('button', { name: /submit/i }));
    expect(onSubmit).toHaveBeenCalledTimes(1);
  });

  it('renders gracefully with no pages', () => {
    mockContentPages = [];
    mockValues = {};

    const { container } = renderWithProviders(
      <ConfirmationStep onCancel={() => {}} onSubmit={() => {}} />
    );

    // No page titles or content
    expect(container.querySelector('.questionTitle')).toBeNull();
    expect(screen.queryByText(/Page \d+:/)).not.toBeInTheDocument();
  });
});
