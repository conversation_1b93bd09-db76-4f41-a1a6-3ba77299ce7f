import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { describe, expect, it, vi } from 'vitest';
import RatingAnswer from '../RatingAnswer';

// Mock Tabler icons with lightweight components that expose props via data-* attributes
vi.mock('@tabler/icons-react', () => {
  const makeIcon = (name: string) => {
    const component = ({ color, fill, size }: { color?: string; fill?: string; size?: number }) => (
      <svg
        data-testid={name}
        data-color={color}
        data-fill={fill}
        data-size={size}
        aria-label={name}
      />
    );
    component.displayName = name;
    return component;
  };

  return {
    IconCrown: makeIcon('IconCrown'),
    IconHeartFilled: makeIcon('IconHeartFilled'),
    IconMoodSmileBeam: makeIcon('IconMoodSmileBeam'),
    IconStarFilled: makeIcon('IconStarFilled'),
    IconThumbUpFilled: makeIcon('IconThumbUpFilled'),
  };
});

describe('RatingAnswer', () => {
  it('renders the correct number of heart icons with the correct color', () => {
    render(<RatingAnswer shape='heart' value={3} />);
    const icons = screen.getAllByTestId('IconHeartFilled');
    expect(icons).toHaveLength(3);
    icons.forEach((icon) => {
      expect(icon).toHaveAttribute('data-color', '#F93549');
      expect(icon).not.toHaveAttribute('data-fill');
      expect(icon).toHaveAttribute('data-size', '28');
    });
  });

  it('renders the correct number of like icons with the correct color', () => {
    render(<RatingAnswer shape='like' value={2} />);
    const icons = screen.getAllByTestId('IconThumbUpFilled');
    expect(icons).toHaveLength(2);
    icons.forEach((icon) => {
      expect(icon).toHaveAttribute('data-color', '#4699F6');
      expect(icon).not.toHaveAttribute('data-fill');
      expect(icon).toHaveAttribute('data-size', '28');
    });
  });

  it('renders crown icons with both color and fill set', () => {
    render(<RatingAnswer shape='crown' value={4} />);
    const icons = screen.getAllByTestId('IconCrown');
    expect(icons).toHaveLength(4);
    icons.forEach((icon) => {
      expect(icon).toHaveAttribute('data-color', '#FFD100');
      expect(icon).toHaveAttribute('data-fill', '#FFD100');
      expect(icon).toHaveAttribute('data-size', '28');
    });
  });

  it('renders smiley icons with the correct color', () => {
    render(<RatingAnswer shape='smiley' value={1} />);
    const icons = screen.getAllByTestId('IconMoodSmileBeam');
    expect(icons).toHaveLength(1);
    expect(icons[0]).toHaveAttribute('data-color', '#62D821');
    expect(icons[0]).not.toHaveAttribute('data-fill');
    expect(icons[0]).toHaveAttribute('data-size', '28');
  });

  it('falls back to star icons when shape is unknown', () => {
    render(<RatingAnswer shape='unknown' value={5} />);
    const icons = screen.getAllByTestId('IconStarFilled');
    expect(icons).toHaveLength(5);
    icons.forEach((icon) => {
      expect(icon).toHaveAttribute('data-color', '#FFD100');
      expect(icon).not.toHaveAttribute('data-fill');
      expect(icon).toHaveAttribute('data-size', '28');
    });
  });

  it('renders no icons when value is 0', () => {
    render(<RatingAnswer shape='heart' value={0} />);
    expect(screen.queryByTestId('IconHeartFilled')).toBeNull();
  });
});
