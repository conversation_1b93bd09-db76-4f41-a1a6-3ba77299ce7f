// RatingAnswer.test.tsx
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/vitest';
import { describe, expect, it, vi } from 'vitest';
import RatingAnswer from '../RatingAnswer';

// Mock the Tabler icons to simple components that expose props as data-attributes
vi.mock('@tabler/icons-react', () => {
  const makeIcon = (testId: string) => {
    const Icon = ({ size, color, fill }: { size?: number; color?: string; fill?: string }) => (
      // Use an SVG to resemble the real icon element
      // Expose props for assertions via data-attributes
      <svg data-testid={testId} data-size={size} data-color={color} data-fill={fill} />
    );
    Icon.displayName = 'Icon';
    return Icon;
  };

  return {
    IconCrown: makeIcon('icon-crown'),
    IconHeartFilled: makeIcon('icon-heart'),
    IconMoodSmileBeam: makeIcon('icon-smiley'),
    IconStarFilled: makeIcon('icon-star'),
    IconThumbUpFilled: makeIcon('icon-like'),
  };
});

describe('RatingAnswer', () => {
  it('renders heart icon with correct size, color, and fill', () => {
    render(<RatingAnswer shape='heart' />);
    const el = screen.getByTestId('icon-heart');
    expect(el).toBeInTheDocument();
    expect(el).toHaveAttribute('data-size', '28');
    expect(el).toHaveAttribute('data-color', '#F93549');
    expect(el).toHaveAttribute('data-fill', '#F93549'); // fill defaults to color
  });

  it('renders like icon with correct size, color, and fill', () => {
    render(<RatingAnswer shape='like' />);
    const el = screen.getByTestId('icon-like');
    expect(el).toBeInTheDocument();
    expect(el).toHaveAttribute('data-size', '28');
    expect(el).toHaveAttribute('data-color', '#4699F6');
    expect(el).toHaveAttribute('data-fill', '#4699F6');
  });

  it('renders crown icon with correct size, color, and fill', () => {
    render(<RatingAnswer shape='crown' />);
    const el = screen.getByTestId('icon-crown');
    expect(el).toBeInTheDocument();
    expect(el).toHaveAttribute('data-size', '28');
    expect(el).toHaveAttribute('data-color', '#FFD100');
    expect(el).toHaveAttribute('data-fill', '#FFD100'); // explicitly passed
  });

  it('renders smiley icon with correct size, color, and fill', () => {
    render(<RatingAnswer shape='smiley' />);
    const el = screen.getByTestId('icon-smiley');
    expect(el).toBeInTheDocument();
    expect(el).toHaveAttribute('data-size', '28');
    expect(el).toHaveAttribute('data-color', '#62D821');
    expect(el).toHaveAttribute('data-fill', '#62D821');
  });

  it('renders star icon by default with correct size, color, and fill', () => {
    render(<RatingAnswer shape='unknown-shape' />);
    const el = screen.getByTestId('icon-star');
    expect(el).toBeInTheDocument();
    expect(el).toHaveAttribute('data-size', '28');
    expect(el).toHaveAttribute('data-color', '#FFD100');
    expect(el).toHaveAttribute('data-fill', '#FFD100');
  });
});
