import FilePreview from '@/components/ConfirmationStep/FilePreview';
import RatingAnswer from '@/components/ConfirmationStep/RatingAnswer';
import { DecaButton } from '@/components/DecaButton';
import { useFormContext } from '@/contexts/FormContext';
import { useFormSettings } from '@/contexts/FormSettingsContext';
import {
  FieldType,
  type FormDataContent,
  type FormField,
  GroupFieldType,
  type GroupFormField,
} from '@/types/form-builder';
import { Box, Flex, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import dayjs from 'dayjs';
import { Fragment } from 'react';

const useStyles = createStyles(() => ({
  questionTitle: {
    fontWeight: 'bolder',
  },
}));

interface ConfirmationStepProps {
  onCancel: () => void;
  onSubmit: () => void;
}

const ConfirmationStep = ({ onCancel, onSubmit }: ConfirmationStepProps) => {
  const { classes } = useStyles();
  const { values } = useFormContext();
  const { t } = useTranslate('common');
  const { contentPages } = useFormSettings();
  const getAnswerText = (answer: any, question: FormField) => {
    if (!answer) {
      return null;
    }
    switch (question.type) {
      case FieldType.Date:
      case FieldType.DateSelector:
        return answer ? dayjs(answer).format('YYYY/MM/DD') : '';
      case FieldType.DateTime:
        return answer ? dayjs(answer).format('YYYY/MM/DD HH:mm') : '';
      case FieldType.DateRange:
        return answer
          ? `${dayjs(answer[0]).format('YYYY/MM/DD HH:mm')} - ${dayjs(answer[1]).format('YYYY/MM/DD HH:mm')}`
          : '';
      case FieldType.Rating:
        return (
          <Flex align='center' gap={8}>
            <Text>
              {answer}/{question.maxScale || 10}
            </Text>
            <RatingAnswer shape={question.shape || ''} />
          </Flex>
        );
      case FieldType.FileUploader:
        return answer?.map(({ asset }) => <FilePreview asset={asset} key={asset.id} />);
      case FieldType.Legal:
        return answer[0]?.value;
      default:
        if (Array.isArray(answer)) {
          return answer.map(({ value }) => value).join(', ');
        }
        return answer.value ?? String(answer); //this will prevent app to crash in case unhandled answer type;
    }
  };

  const renderField = (field: FormDataContent) => {
    if (
      [GroupFieldType.Name, GroupFieldType.Address, GroupFieldType.DateTimeRange].includes(
        field.type as GroupFieldType
      )
    ) {
      return (field as GroupFormField).fields.map((field) => renderField(field));
    }

    return (
      <Fragment key={field.id}>
        <div
          className={classes.questionTitle}
          dangerouslySetInnerHTML={{ __html: (field as FormField).label }}
        />
        <p>{getAnswerText((values as any)[field.id], field as FormField) || t('blank')}</p>
      </Fragment>
    );
  };

  const renderPages = () => {
    return contentPages?.map((page, index) => {
      return (
        <Box mb={24} key={page.id}>
          <Text fw={500} c='decaNavy.4' mb={12}>
            {`${t('page', { page: index + 1 })}: ${page.name}`}
          </Text>
          <Box mb={12}>{page.content.map((field) => renderField(field))}</Box>
        </Box>
      );
    });
  };

  return (
    <Box p='lg'>
      <section>{renderPages()}</section>
      <Flex gap={rem(12)}>
        <DecaButton variant='neutral' radius='xl' onClick={onCancel}>
          {t('back')}
        </DecaButton>
        <DecaButton radius='xl' onClick={onSubmit}>
          {t('submit')}
        </DecaButton>
      </Flex>
    </Box>
  );
};

export default ConfirmationStep;
