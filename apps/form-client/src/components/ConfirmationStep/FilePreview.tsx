import type { ValueAsset } from '@/types/form-builder';
import { Flex, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';

const useStyle = createStyles((theme) => ({
  fileResult: {
    border: `1px ${theme.colors.decaLight[4]} solid`,
    borderRadius: 4,
    margin: `${rem(8)} 0`,
    fontSize: rem(12),
    padding: `${rem(8)} ${rem(16)}`,
  },
}));

interface FileUploadResultProps {
  asset: ValueAsset;
}

const transformSizeUnit = (size: number) => {
  const SIZE_UNITS = ['KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  let unitIndex = 0;
  if (size < 1024) {
    return `${(size / 1024).toFixed(2)}KB`;
  }

  while (unitIndex < SIZE_UNITS.length) {
    if (size < 1024) {
      return `${size}${SIZE_UNITS[unitIndex - 1]}`;
    }
    unitIndex++;
    size = Math.floor(size / 1024);
  }
  return `${size}${SIZE_UNITS[unitIndex - 1]}`;
};

const FilePreview = ({ asset }: FileUploadResultProps) => {
  const { classes } = useStyle();

  return (
    <Flex align='center' className={classes.fileResult}>
      <Text>{asset.name}</Text>
      <Text c='decaGrey.4' ml='auto' mr={rem(12)}>
        {transformSizeUnit(asset.size)}
      </Text>
    </Flex>
  );
};

export default FilePreview;
