import { useFormContext } from '@/contexts/FormContext';
import { useFormSettings } from '@/contexts/FormSettingsContext';
import useFieldStyles from '@/hooks/form/useFieldStyles';
import { FieldType, type FormField } from '@/types/form-builder';
import { hasRequiredRule } from '@/utils';
import { Checkbox, NumberInput, TextInput, Textarea } from '@mantine/core';
import CheckboxField from './CheckboxField';
import DateField from './DateField';
import DateRangeField from './DateRangeField';
import DateSelectorField from './DateSelectorField';
import DateTimeField from './DateTimeField';
import DropdownField from './DropdownField';
import EmailField from './EmailField';
import FieldLabel from './FieldLabel';
import FileUploadField from './FileUploadField/FileUploadField';
import Heading from './Heading';
import OpinionScaleField from './OpinionScaleField';
import ParagraphField from './ParagraphField';
import PostcodeField from './PostcodeField';
import RadioField from './RadioField';
import RatingField from './RatingField';
import TimeField from './TimeField';
import WebsiteField from './WebsiteField';

export interface FormFieldProps {
  field: FormField;
  // eslint-disable-next-line no-unused-vars
  onChange?: (field: FormField, value: any) => void;
}

const FormFieldBase = ({ field, onChange }: FormFieldProps) => {
  const form = useFormContext();
  const formInputProps = form.getInputProps(field.id, {
    type: field.type === 'checkbox' ? 'checkbox' : 'input',
  });
  const inputProps = {
    label: <FieldLabel label={field.label} />,
    placeholder: field.placeholder,
    description: field.descriptionEnabled ? <FieldLabel label={field.description || ''} /> : '',
    required: hasRequiredRule(field),
    ...formInputProps,
    onChange: (ev) => {
      formInputProps.onChange(ev);
      if (ev?.target) {
        onChange?.(field, ev.target.value);
      }
    },
  };
  const { appearance } = useFormSettings();
  const inputStyle = appearance?.defaultSettings?.inputStyle;
  const fieldStyles = useFieldStyles({
    customize: appearance.customize,
    inputStyle: inputStyle,
    formFieldStyle: appearance?.formFieldStyle,
    defaultSettings: appearance?.defaultSettings,
  });

  const renderField = (field: FormField) => {
    switch (field.type) {
      case FieldType.Heading:
        return <Heading field={field} style={appearance?.headingStyle} />;
      case FieldType.Paragraph:
        return <ParagraphField field={field} style={appearance?.paragraphStyle} />;
      case FieldType.ShortQA:
        return <TextInput {...inputProps} {...fieldStyles} />;
      case FieldType.Number:
        return <NumberInput {...inputProps} {...fieldStyles} />;
      case FieldType.LongQA:
        return (
          <Textarea
            {...inputProps}
            {...fieldStyles}
            radius={inputStyle?.includes('rounded') ? 'lg' : ''}
          />
        );
      case FieldType.Checkbox:
        return (
          <Checkbox
            {...inputProps}
            {...fieldStyles}
            description=''
            label={<FieldLabel label={field.options?.[0]?.label || ''} />}
            sx={{
              '.mantine-Checkbox-error': {
                transform: 'translateX(-30px)',
              },
              '.mantine-Checkbox-inner': {
                svg: {
                  color: 'white',
                },
              },
            }}
          />
        );
      case FieldType.Checkboxes:
        return <CheckboxField {...inputProps} {...fieldStyles} field={field} />;
      case FieldType.Legal:
        return (
          <CheckboxField {...inputProps} {...fieldStyles} showHelperText={false} field={field} />
        );
      case FieldType.YesNo:
      case FieldType.MultipleChoice:
        return <RadioField {...fieldStyles} field={field} />;
      case FieldType.Dropdown:
        return <DropdownField {...inputProps} {...fieldStyles} field={field} />;
      case FieldType.Email:
        return <EmailField {...inputProps} {...fieldStyles} />;
      case FieldType.Date:
        return <DateField {...inputProps} {...fieldStyles} field={field} />;
      case FieldType.DateSelector:
        return <DateSelectorField {...inputProps} {...fieldStyles} field={field} />;
      case FieldType.Time:
        return <TimeField {...inputProps} {...fieldStyles} />;
      case FieldType.DateTime:
        return <DateTimeField {...inputProps} {...fieldStyles} field={field} />;
      case FieldType.DateRange:
        return <DateRangeField {...inputProps} {...fieldStyles} field={field} />;
      case FieldType.Website:
        return <WebsiteField {...inputProps} {...fieldStyles} />;
      case FieldType.PostCode:
        return <PostcodeField {...inputProps} {...fieldStyles} />;
      case FieldType.PhoneNumber:
        return <TextInput {...inputProps} {...fieldStyles} />;
      case FieldType.OpinionScale:
        return <OpinionScaleField {...inputProps} {...fieldStyles} field={field} />;
      case FieldType.FileUploader:
        return <FileUploadField {...inputProps} {...fieldStyles} field={field} />;
      case FieldType.Rating:
        return <RatingField {...inputProps} {...fieldStyles} field={field} />;
      default:
        return null;
    }
  };

  return <>{renderField(field)}</>;
};

export default FormFieldBase;
