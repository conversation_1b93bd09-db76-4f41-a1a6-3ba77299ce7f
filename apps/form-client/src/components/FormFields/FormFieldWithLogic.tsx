import { useFormContext } from '@/contexts/FormContext';
import type { FormField } from '@/types/form-builder';
import { useCallback } from 'react';
import { useFormLogicContext } from '../../contexts/FormLogicContext';
import FormFieldBase from './FormFieldBase';

interface FormFieldWithLogicProps {
  field: FormField;
  // eslint-disable-next-line no-unused-vars
  onChange?: (field: FormField, value: any) => void;
}

const FormFieldWithLogic = ({ field, onChange }: FormFieldWithLogicProps) => {
  const form = useFormContext();
  const { isFieldVisible, updateFieldVisibility } = useFormLogicContext();

  // Enhanced onChange that triggers logic evaluation
  const handleChange = useCallback(
    (field: FormField, value: any) => {
      const formValues = form.getValues() as Record<string, any>;
      updateFieldVisibility(field.id, value, formValues);
      onChange?.(field, value);
    },
    [form, updateFieldVisibility, onChange]
  );

  // Don't render if field is hidden by logic
  if (!isFieldVisible(field.id)) {
    return null;
  }

  return <FormFieldBase field={field} onChange={handleChange} />;
};

export default FormFieldWithLogic;
