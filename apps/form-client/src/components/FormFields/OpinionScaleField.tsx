import { useFormContext } from '@/contexts/FormContext';
import { useFormSettings } from '@/contexts/FormSettingsContext';
import type { FormField } from '@/types/form-builder';
import { Chip, Flex, Input, type InputWrapperProps, rem } from '@mantine/core';
import { useEffect, useState } from 'react';

interface OpinionScaleFieldProps extends InputWrapperProps {
  field: FormField;
}

const OpinionScaleField = ({ field, ...restProps }: OpinionScaleFieldProps) => {
  const form = useFormContext();
  const [value, setValue] = useState(() => (form as any).getValues()[field.id] || '');
  const { appearance } = useFormSettings();

  const chipStyles = {
    iconWrapper: {
      display: 'none',
    },
    label: {
      width: rem(48),
      height: rem(42),
      borderRadius: 4,
      justifyContent: 'center',
      '&[data-checked]:not([data-disabled])': {
        borderColor: appearance.defaultSettings.color,
      },
    },
  };

  const renderOptionChip = () => {
    return [...new Array(field.maxScale)].map((_, index) => (
      <Chip
        key={index}
        styles={chipStyles}
        value={(index + 1).toString()}
        color={appearance.defaultSettings.color}
      >
        {index + 1}
      </Chip>
    ));
  };

  useEffect(() => {
    if (value) {
      form.setFieldValue(field.id, {
        value: Number.parseInt(value),
        maxScale: field.maxScale,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  return (
    <Input.Wrapper {...restProps}>
      <Chip.Group multiple={false} value={value} onChange={setValue}>
        <Flex mb={8} gap={6} wrap='wrap'>
          {renderOptionChip()}
        </Flex>
      </Chip.Group>
      {!field.footerHide && field.footerLabel?.left && (
        <div
          style={{ color: appearance.defaultSettings.color }}
          dangerouslySetInnerHTML={{ __html: field.footerLabel.left }}
        />
      )}
    </Input.Wrapper>
  );
};

export default OpinionScaleField;
