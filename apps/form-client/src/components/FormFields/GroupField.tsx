import { GroupFieldType, type GroupFormField } from '@/types/form-builder';
import AddressField from './AddressFields';
import GroupFormFieldBase from './GroupFormFieldBase';
import NameField from './NameField';

interface GroupFieldProps {
  group: GroupFormField;
}

const GroupField = ({ group }: GroupFieldProps) => {
  const renderGroupField = () => {
    switch (group.type) {
      case GroupFieldType.Address:
        return <AddressField group={group} />;
      case GroupFieldType.Name:
        return <NameField group={group} />;
      // Section
      default:
        return <GroupFormFieldBase group={group} />;
    }
  };

  return <>{renderGroupField()}</>;
};

export default GroupField;
