import type { GroupFormField } from '@/types/form-builder';
import { useFormLogicContext } from '../../contexts/FormLogicContext';
import GroupField from './GroupField';

interface GroupFieldWithLogicProps {
  group: GroupFormField;
}

const GroupFieldWithLogic = ({ group }: GroupFieldWithLogicProps) => {
  const { isFieldVisible } = useFormLogicContext();

  // Don't render if group is hidden by logic
  if (!isFieldVisible(group.id)) {
    return null;
  }

  return <GroupField group={group} />;
};

export default GroupFieldWithLogic;
