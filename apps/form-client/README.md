# Tables

## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type aware lint rules:

- Configure the top-level `parserOptions` property like this:

```js
export default {
  // other rules...
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
    project: ['./tsconfig.json', './tsconfig.node.json'],
    tsconfigRootDir: __dirname,
  },
};
```

## Development

### Setup

```bash
# install dependencies
pnpm install

# pull tolgee
tolgee pull  -ak <NEXT_PUBLIC_TOLGEE_KEY> --path ./public/locales --delimiter=

# run development server
pnpm dev
```

### Linting

```bash
# run next lint
pnpm run lint

# run eslint
pnpm run lint:eslint

# run eslint with fix
pnpm run lint:eslint:fix

# run lint-staged
pnpm run lint-staged-check
```
