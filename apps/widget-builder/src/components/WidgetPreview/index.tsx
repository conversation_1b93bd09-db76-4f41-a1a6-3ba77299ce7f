import { <PERSON>Icon, Button, type <PERSON>ton<PERSON>rops, Flex, Stack, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconDownload, IconExternalLink, IconRefresh } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';

import ConsoleSection from './ConsoleSection';
import PreviewSection from './PreviewSection';

interface WidgetPreviewProps {
  saveButtonProps?: Omit<ButtonProps, 'className' | 'leftSection'>;
  onSave?: () => void;
  onPublish?: () => void;
}

const useStyles = createStyles((theme) => ({
  container: {
    height: 'auto',
    flex: `0 0 ${rem(400)}`,
    minWidth: 0,
    justifyContent: 'space-between',
  },
  actionWrapper: {
    alignItems: 'center',
    backgroundColor: theme.colors.decaDark[9],
    borderBottom: `1px solid ${theme.colors.decaDark[4]}`,
    width: '100%',
  },
  actionButton: {
    backgroundColor: theme.colors.decaDark[9],
    borderRadius: rem(3.2),
    border: 0,
    color: theme.white,
    fontSize: theme.fontSizes.md,
    fontWeight: 500,
    height: rem(30),
    lineHeight: rem(22),
    padding: `${rem(4)} ${rem(8)}`,

    '&:hover': {
      backgroundColor: theme.colors.decaDark[4],
    },
  },
  previewWrapper: {
    backgroundColor: theme.white,
  },
}));

const WidgetPreview: React.FC<WidgetPreviewProps> = (props) => {
  const { saveButtonProps, onSave, onPublish } = props;
  const { classes } = useStyles();
  const { t } = useTranslate('editor');

  return (
    <Flex className={classes.container} direction='column'>
      <Stack gap={0} h='100%'>
        <Flex
          className={classes.actionWrapper}
          justify='flex-end'
          gap={rem(8)}
          h={rem(53)}
          pr={rem(10)}
        >
          <Button
            className={classes.actionButton}
            leftSection={<IconDownload size={16} />}
            onClick={onSave}
            {...saveButtonProps}
          >
            {t('actions.save')}
          </Button>
          <Button
            className={classes.actionButton}
            leftSection={<IconExternalLink size={16} />}
            onClick={onPublish}
          >
            {t('actions.publish')}
          </Button>
        </Flex>
        <Flex
          className={classes.actionWrapper}
          justify='space-between'
          h={rem(36)}
          pl={rem(16)}
          pr={rem(10)}
        >
          <Text c='white' fz={14} lh={rem(22)} fw={500}>
            {t('preview.title')}
          </Text>
          <Flex>
            <ActionIcon variant='transparent' c='white'>
              <IconRefresh size={16} />
            </ActionIcon>
          </Flex>
        </Flex>
        <PreviewSection />
      </Stack>
      <ConsoleSection />
    </Flex>
  );
};

export default WidgetPreview;
