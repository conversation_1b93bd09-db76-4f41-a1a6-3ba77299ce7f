import { ActionIcon, Box, Flex, Group, ScrollArea, Stack, Text, rem, rgba } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCancel, IconChevronDown, IconChevronUp } from '@tabler/icons-react';
import { useEffect, useState } from 'react';

import { useCustomEventContext } from '@/contexts/CustomEventContext';
import { useWidgetContext } from '@/contexts/WidgetContext';
import type { ILog } from '@/types/common';
import type { BuiltAssetLog } from '@/types/editor';

const useStyles = createStyles((theme) => ({
  container: {
    background: theme.colors.decaDark[9],
    width: '100%',
    height: rem(246),
    maxHeight: rem(246),
    transition: 'height 0.3s ease',
  },
  collapsed: {
    height: rem(46),
  },
  header: {
    borderTop: `1px solid ${theme.colors.decaDark[4]}`,
    borderBottom: `1px solid ${theme.colors.decaDark[4]}`,
    padding: `${rem(8)} ${rem(16)}`,
  },
  console: {
    background: theme.colors.decaDark[4],
    width: '100%',
    height: '100%',
    minHeight: rem(200),
    gap: rem(2),
    paddingBottom: rem(4),
  },
  wrapper: {
    padding: `${rem(4)} ${rem(8)}`,
  },
  error: {
    background: rgba(theme.colors.decaRed[9], 0.5),
  },
  warning: {
    background: rgba(theme.colors.decaYellow[9], 0.5),
  },
  log: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.decaMono[1],
    whiteSpace: 'pre-wrap',
  },
}));

const getLogKey = (log: BuiltAssetLog) => {
  if (log instanceof Error) {
    return `${log.name}-${log.stack}-${log.message}`;
  }

  return JSON.stringify(log);
};

const ConsoleSection = () => {
  const { classes, cx } = useStyles();
  const { builtAssetLogsRef } = useWidgetContext();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { addListener, removeListener } = useCustomEventContext();
  const [logs, setLogs] = useState<BuiltAssetLog[]>([]);

  const renderLog = (log: BuiltAssetLog) => {
    if (log instanceof Error || (typeof log === 'object' && 'message' in log)) {
      return <Text className={classes.log}>{log.message}</Text>;
    }

    return <Text className={classes.log}>{log}</Text>;
  };

  useEffect(() => {
    addListener('onEditorValidate', (logs: BuiltAssetLog[]) => {
      setLogs(logs);
    });

    return () => {
      removeListener('onEditorValidate');
    };
  }, [addListener, removeListener]);

  return (
    <Flex
      align='center'
      direction='column'
      className={cx(classes.container, { [classes.collapsed]: isCollapsed })}
    >
      <Flex align='center' justify='space-between' w='100%' className={classes.header}>
        <Text c='white'>Console</Text>
        <Group>
          <ActionIcon
            variant='transparent'
            c='white'
            onClick={() => {
              builtAssetLogsRef.current = [];
              setLogs([]);
            }}
          >
            <IconCancel size={16} />
          </ActionIcon>
          <ActionIcon variant='transparent' c='white' onClick={() => setIsCollapsed(!isCollapsed)}>
            {isCollapsed ? <IconChevronUp size={16} /> : <IconChevronDown size={16} />}
          </ActionIcon>
        </Group>
      </Flex>
      <ScrollArea h={rem(208)} w='100%'>
        <Stack className={classes.console}>
          {logs.map((log, index) => (
            <Box
              className={cx(classes.wrapper, {
                [classes.error]: log instanceof Error || (log as ILog).type === 'error',
                [classes.warning]: (log as ILog).type === 'warning',
              })}
              key={`${getLogKey(log)}-${logs.length - index}`}
            >
              {renderLog(log)}
            </Box>
          ))}
        </Stack>
      </ScrollArea>
    </Flex>
  );
};

export default ConsoleSection;
