import { <PERSON><PERSON>con, Flex, Stack, Text, Tooltip, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconChevronsLeft, IconChevronsRight } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { type ChangeEventHandler, useCallback, useEffect, useRef, useState } from 'react';

import {
  ALLOWED_CSS_EXTENSIONS,
  ALLOWED_IMG_EXTENSIONS,
  ALLOWED_JS_EXTENSIONS,
} from '@/constants/asset';
import { HEADER_HEIGHT } from '@/constants/common';
import { useWidgetContext } from '@/contexts/WidgetContext';
import type { Trees } from '@/types/editor';
import { addNewInputToTree, removeNewInputFromTree } from '@/utils/asset';
import { logger } from '@resola-ai/services-shared';
import { IconNewFile, IconUpload } from '../Icons';
import IconNewFolder from '../Icons/NewFolder';
import TreeList from '../TreeList';

const ACCEPTED_FILE_TYPES = [
  ...ALLOWED_CSS_EXTENSIONS,
  ...ALLOWED_IMG_EXTENSIONS,
  ...ALLOWED_JS_EXTENSIONS,
].join(',');

interface WidgetExplorerProps {
  widgetName: string;
  tree: Trees;
  collapsed: boolean;
  onToggleCollapse: () => void;
}

const useStyles = createStyles((theme) => ({
  container: {
    height: `calc(100vh - ${rem(HEADER_HEIGHT)})`,
    width: rem(232),
    borderRight: `1px solid ${theme.colors.decaLight[2]}`,
    backgroundColor: theme.colors.decaLight[0],
    flexDirection: 'column',
    justifyContent: 'space-between',
    transition: 'all 0.2s ease-in-out',
  },
  collapsed: {
    width: 0,
  },
  widgetName: {
    borderBottom: `1px solid ${theme.colors.decaLight[2]}`,
  },
  actionIcon: {
    backgroundColor: theme.white,
    border: `1px solid ${theme.colors.decaLight[4]}`,
    borderRadius: rem(3),
    width: '100%',
    height: rem(30),
    '&:hover': {
      backgroundColor: theme.colors.decaLight[1],
    },
    '&:disabled': {
      backgroundColor: theme.colors.decaLight[0],
      border: `1px solid ${theme.colors.decaLight[2]}`,
      '& svg': {
        color: theme.colors.decaGrey[0],
      },
    },
  },
  collapseContainer: {
    padding: `${rem(10)} ${rem(16)}`,
    gap: rem(12),
    borderTop: `1px solid ${theme.colors.decaLight[2]}`,
    cursor: 'pointer',
  },
  unCollapsedIcon: {
    position: 'fixed',
    left: rem(8),
    bottom: rem(8),
    cursor: 'pointer',
    backgroundColor: theme.white,

    '&:hover': {
      backgroundColor: theme.colors.decaLight[1],
      borderRadius: '50%',
    },
  },
}));

const WidgetExplorer: React.FC<WidgetExplorerProps> = ({
  collapsed,
  onToggleCollapse,
  tree,
  widgetName,
}) => {
  const { classes, cx, theme } = useStyles();
  const { t } = useTranslate('editor');
  const fileUploadInputRef = useRef<HTMLInputElement | null>(null);
  const {
    createAssets,
    createFolder,
    openedWidgetId,
    selectedPath,
    openedFiles,
    setOpenedFiles,
    selectedFile,
    setSelectedFile,
    widgetAssets,
    relocateAsset,
    fileContentsRef,
  } = useWidgetContext();
  const [explorerTree, setExplorerTree] = useState<Trees>([...tree]);
  const [createdAssetId, setCreatedAssetId] = useState<string | null>(null);

  const uploadFiles = async (files: File[]) => {
    const filesByPath = files.reduce<Record<string, File>>(
      (previous, current) =>
        Object.assign(previous, {
          [selectedPath ? `${selectedPath}/${current.name}` : current.name]: current,
        }),
      {}
    );

    await createAssets({ filesByPath, widgetId: openedWidgetId });
  };

  const onFileUploadInputChange: ChangeEventHandler<HTMLInputElement> = async (event) => {
    if (!event.target.files) return;

    const files = [...event.target.files];

    try {
      await uploadFiles(files);
    } catch (error) {
      logger.error(error);
    } finally {
      event.target.value = '';
    }
  };

  const onFileUpload = async () => {
    if (typeof window.showOpenFilePicker === 'undefined' || !('showOpenFilePicker' in window)) {
      fileUploadInputRef.current?.click();
      return;
    }

    try {
      const fileHandles = await window.showOpenFilePicker({
        excludeAcceptAllOption: true,
        multiple: true,
        types: [
          {
            accept: {
              'image/jpeg': ['.jpg', '.jpeg'],
              'image/png': '.png',
              'image/gif': '.gif',
              'image/svg': '.svg',
              'image/webp': '.webp',
              'text/css': ALLOWED_CSS_EXTENSIONS as `.${string}`[],
              'text/javascript': ALLOWED_JS_EXTENSIONS as `.${string}`[],
            },
          },
        ],
      });

      const files = await Promise.all(fileHandles.map((file) => file.getFile()));

      await uploadFiles(files);
    } catch (error) {
      if (error instanceof DOMException && error.name === 'AbortError') {
        return;
      }
    }
  };

  const onCreateNewFile = useCallback(() => {
    setExplorerTree((prev) => {
      const newTree = [...prev];
      return addNewInputToTree(newTree, selectedPath);
    });
  }, [selectedPath]);

  const onCreateNewFolder = useCallback(() => {
    setExplorerTree((prev) => {
      const newTree = [...prev];
      return addNewInputToTree(newTree, selectedPath, true);
    });
  }, [selectedPath]);

  const onCancelCreateNewFile = useCallback(() => {
    setExplorerTree((prev) => {
      const newTree = [...prev];
      return removeNewInputFromTree(newTree, selectedPath);
    });
  }, [selectedPath]);

  const onHandleCreateNewFile = useCallback(
    async (name: string) => {
      const contentByPath = {
        [selectedPath ? `${selectedPath}/${name}` : name]: '',
      };

      try {
        const response = await createAssets({
          filesByPath: contentByPath,
          widgetId: openedWidgetId,
        });

        setCreatedAssetId(response[0].id);
      } catch (error) {
        logger.error(error);
      }
    },
    [selectedPath, createAssets, openedWidgetId, setCreatedAssetId]
  );

  const onHandleCreateNewFolder = useCallback(
    async (name: string) => {
      try {
        await createFolder({
          folderPath: selectedPath ? `${selectedPath}/${name}/` : `${name}/`,
          widgetId: openedWidgetId,
        });
      } catch (error) {
        logger.error(error);
      }
    },
    [createFolder, openedWidgetId, selectedPath]
  );

  const onRelocate = useCallback(
    async (currentFilePath: string, newFilePath: string) => {
      try {
        await relocateAsset({ currentFilePath, newFilePath });

        fileContentsRef.current[newFilePath] = fileContentsRef.current[currentFilePath];
        delete fileContentsRef.current[currentFilePath];

        const editingFile = openedFiles.find((file) => file.filePath === currentFilePath);

        if (!editingFile) return;

        const fileName = newFilePath.split('/').pop() ?? '';
        const newFile = {
          ...editingFile,
          filePath: newFilePath,
          leafName: fileName,
          fields: { ...editingFile?.fields, name: fileName, path: newFilePath },
        };

        setOpenedFiles((prevState) =>
          prevState.map((file) => (file.filePath === currentFilePath ? newFile : file))
        );

        if (selectedFile?.filePath === currentFilePath) {
          setSelectedFile(newFile);
        }
      } catch (err) {
        logger.error(err);
      }
    },
    [relocateAsset, openedFiles, setOpenedFiles, selectedFile, setSelectedFile, fileContentsRef]
  );

  useEffect(() => {
    if (createdAssetId) {
      const asset = widgetAssets?.find((asset) => asset.id === createdAssetId);
      if (asset) {
        setSelectedFile(asset);
        setOpenedFiles((prev) => {
          const isExist = prev.some((file) => file.id === asset.id);
          return isExist
            ? prev
            : [
                ...prev,
                {
                  ...asset,
                  fields: {
                    ...asset.fields,
                    name: asset.filePath.split('/').pop() ?? '',
                  },
                },
              ];
        });
        setCreatedAssetId(null);
      }
    }
  }, [createdAssetId, widgetAssets, setOpenedFiles, setSelectedFile]);

  useEffect(() => {
    setExplorerTree([...tree]);
  }, [tree]);

  return (
    <Flex className={cx(classes.container, { [classes.collapsed]: collapsed })}>
      {!collapsed ? (
        <>
          <Stack px={rem(16)}>
            <Text size='md' fw={500} c='decaGrey.8' py={rem(14)} className={classes.widgetName}>
              {widgetName}
            </Text>
            <Flex justify='space-between' align='center' pb={rem(10)} gap={rem(8)}>
              <Tooltip label={t('tooltip.upload')} withArrow>
                <ActionIcon
                  data-testid='upload-button'
                  variant='default'
                  className={classes.actionIcon}
                  onClick={onFileUpload}
                >
                  <IconUpload />
                </ActionIcon>
              </Tooltip>
              <input
                accept={ACCEPTED_FILE_TYPES}
                data-testid='file-upload-input'
                multiple
                ref={fileUploadInputRef}
                style={{ display: 'none' }}
                type='file'
                onChange={onFileUploadInputChange}
              />
              <Tooltip label={t('tooltip.newFile')} withArrow>
                <ActionIcon
                  data-testid='new-file-button'
                  variant='default'
                  className={classes.actionIcon}
                  onClick={onCreateNewFile}
                >
                  <IconNewFile />
                </ActionIcon>
              </Tooltip>
              <Tooltip label={t('tooltip.newFolder')} withArrow>
                <ActionIcon
                  data-testid='new-folder-button'
                  variant='default'
                  className={classes.actionIcon}
                  onClick={onCreateNewFolder}
                >
                  <IconNewFolder />
                </ActionIcon>
              </Tooltip>
            </Flex>
            <TreeList
              tree={explorerTree}
              selection={selectedFile?.id}
              onCreateNewFile={onHandleCreateNewFile}
              onCreateNewFolder={onHandleCreateNewFolder}
              onCancelCreateNewFile={onCancelCreateNewFile}
              onRelocate={onRelocate}
            />
          </Stack>
          <Flex
            data-testid='collapse-button'
            className={classes.collapseContainer}
            onClick={onToggleCollapse}
          >
            <IconChevronsLeft color={theme.colors.decaGrey[4]} />
            <Text size='md' fw={500} c='decaGrey.8' lh={rem(22)}>
              {t('actions.collapse')}
            </Text>
          </Flex>
        </>
      ) : (
        <ActionIcon
          data-testid='expand-button'
          className={classes.unCollapsedIcon}
          size={40}
          onClick={onToggleCollapse}
        >
          <IconChevronsRight color={theme.colors.decaGrey[4]} />
        </ActionIcon>
      )}
    </Flex>
  );
};

export default WidgetExplorer;
