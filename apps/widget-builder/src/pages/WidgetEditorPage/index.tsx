import { <PERSON>, Center, Flex, Loader, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>size, Tabs, lighten, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconFile, IconX } from '@tabler/icons-react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';

import PublishWidgetModal from '@/components/Modals/PublishWidget';
import WidgetEditor from '@/components/WidgetEditor';
import WidgetExplorer from '@/components/WidgetExplorer';
import WidgetPreview from '@/components/WidgetPreview';
import { MESSAGE_STATUS } from '@/constants/common';
import { MESSAGE_TYPES, type MessageType } from '@/constants/messaging';
import { useCustomEventContext } from '@/contexts/CustomEventContext';
import { useWidgetContext } from '@/contexts/WidgetContext';
import { queryElements } from '@/helpers/dom';
import useEditorWidget from '@/hooks/useEditorWidget';
import AssetsAPI from '@/services/asset';
import { shareAppConfigService } from '@resola-ai/services-shared';
import { useAuthentication, usePathName } from '@resola-ai/ui/hooks';
import { getOrganizationName } from '@resola-ai/utils';

const AUTO_SAVE_INTERVAL = 5 * 60 * 1000;
const NAV_BAR_WIDTH = rem(56);

const useStyles = createStyles((theme) => ({
  container: {
    marginLeft: NAV_BAR_WIDTH,
    position: 'fixed',
    width: `calc(100% - ${NAV_BAR_WIDTH})`,
  },
  fileContainer: {
    height: `calc(100vh - ${rem(56)})`,
    flex: 1,
    minWidth: 0,
  },
  tabContainer: {
    width: '100%',
  },
  tabPanel: {
    height: 0,
  },
  tabList: {
    height: rem(46),
    width: '100%',
    backgroundColor: theme.colors.decaDark[9],
    borderBottom: `1px solid ${theme.colors.decaDark[4]}`,
    flexWrap: 'nowrap',
  },
  tab: {
    alignItems: 'center',
    backgroundColor: theme.colors.decaDark[9],
    border: 0,
    borderRadius: 0,
    color: theme.colors.decaGrey[4],

    '&:hover': {
      backgroundColor: lighten(theme.colors.decaDark[9], 0.05),
    },

    '&[data-active]': {
      color: theme.white,
      borderBottom: 0,
    },
  },
  tabListScrollArea: {
    backgroundColor: theme.colors.decaDark[9],
  },
}));

const WidgetEditorPage = () => {
  const { classes, cx } = useStyles();
  const { widgetId } = useParams();
  const {
    isLoadingWidgetAssets,
    openedFiles,
    openWidget,
    selectedFile,
    setSelectedFile,
    setOpenedFiles,
    widgetAssets,
    widgets,
  } = useWidgetContext();
  const [collapsed, setCollapsed] = useState(false);
  const [hoveredTab, setHoveredTab] = useState<string | null>(null);
  const autoSaveIntervalIdsRef = useRef<Record<string, NodeJS.Timeout>>({});
  const currentWidget = useMemo(
    () => widgets?.data?.find((widget) => widget.id === widgetId),
    [widgetId, widgets]
  );
  const { addListener, removeListener } = useCustomEventContext();
  const pathname = usePathName();
  const acceptedOrigins = useMemo(() => {
    const organizationName = getOrganizationName(shareAppConfigService.getAppConfig());

    return [
      'http://localhost:5173',
      `https://${organizationName}.deca-dev.com`,
      `https://${organizationName}.deca.cloud`,
    ];
  }, []);

  const {
    closePublishModal,
    fileSaveState,
    isLoadingS3WidgetAssets,
    isPublishing,
    isPublishModalOpened,
    onPublish,
    openPublishModal,
    s3WidgetAssets,
    saveFile,
    setIsLoadingS3WidgetAssets,
    setS3WidgetAssets,
    tree,
    widgetName,
  } = useEditorWidget(widgetId ?? '');
  const { accessToken } = useAuthentication();

  useEffect(() => {
    const onMessage = (
      event: MessageEvent<
        | { type: Exclude<MessageType, 'deca-query-elements'> }
        | { type: 'deca-query-elements'; selector: string }
      >
    ) => {
      if (!acceptedOrigins.includes(event.origin)) return;

      switch (event.data.type) {
        case 'deca-request-token':
          event.source?.postMessage(
            { type: MESSAGE_TYPES.SEND_TOKEN, data: accessToken, token: accessToken },
            { targetOrigin: event.origin }
          );
          break;
        case 'deca-query-elements': {
          const elements = queryElements(event.data.selector);

          event.source?.postMessage(
            {
              type: MESSAGE_TYPES.SEND_ELEMENTS,
              data: elements.length > 0 ? elements : undefined,
              status: elements.length > 0 ? MESSAGE_STATUS.SUCCESS : MESSAGE_STATUS.ERROR,
            },
            { targetOrigin: event.origin }
          );
          break;
        }
      }
    };

    window.addEventListener('message', onMessage);

    return () => {
      window.removeEventListener('message', onMessage);
    };
  }, [acceptedOrigins, accessToken]);

  useEffect(() => {
    addListener('onRouteChange', (destinationPathname: string) => {
      if (destinationPathname === pathname) return;

      const autoSaveIntervalIds = Object.values(autoSaveIntervalIdsRef.current);

      for (const intervalId of autoSaveIntervalIds) {
        clearInterval(intervalId);
      }

      for (const file of openedFiles) {
        saveFile(file.id);
      }
    });

    return () => {
      removeListener('onRouteChange');
    };
  }, [addListener, removeListener, openedFiles, pathname, saveFile]);

  useEffect(() => {
    if (widgetId) {
      openWidget(widgetId);
    }
  }, [widgetId, openWidget]);

  useEffect(() => {
    if (!selectedFile || !widgetId) return;

    const s3Asset = s3WidgetAssets.find((asset) => asset.filePath === selectedFile?.filePath);

    if (s3Asset) return;

    (async () => {
      setIsLoadingS3WidgetAssets(true);

      try {
        const s3Asset = await AssetsAPI.getPresignedUrl({
          filePath: selectedFile.filePath,
          widgetId,
        });

        setS3WidgetAssets((prevState) => [...prevState, s3Asset]);
      } finally {
        setIsLoadingS3WidgetAssets(false);
      }
    })();
  }, [s3WidgetAssets, selectedFile, widgetId, setS3WidgetAssets, setIsLoadingS3WidgetAssets]);

  useEffect(() => {
    for (const file of openedFiles) {
      if (autoSaveIntervalIdsRef.current[file.id]) continue;

      clearInterval(autoSaveIntervalIdsRef.current[file.id]);

      autoSaveIntervalIdsRef.current[file.id] = setInterval(() => {
        saveFile(file.id);
      }, AUTO_SAVE_INTERVAL);
    }
  }, [openedFiles, saveFile]);

  useEffect(() => {
    const autoSaveIntervalIds = Object.values(autoSaveIntervalIdsRef.current);

    const onVisibilityChange = () => {
      const entries = Object.entries(autoSaveIntervalIdsRef.current);

      if (document.hidden) {
        for (const [fileId, intervalId] of entries) {
          clearInterval(intervalId);
          saveFile(fileId);
        }
      } else {
        for (const [fileId] of entries) {
          autoSaveIntervalIdsRef.current[fileId] = setInterval(() => {
            saveFile(fileId);
          }, AUTO_SAVE_INTERVAL);
        }
      }
    };

    document.addEventListener('visibilitychange', onVisibilityChange);

    return () => {
      for (const intervalId of autoSaveIntervalIds) {
        clearInterval(intervalId);
      }

      document.removeEventListener('visibilitychange', onVisibilityChange);
    };
  }, [saveFile]);

  useEffect(() => {
    const onBeforeUnload = () => {
      for (const file of openedFiles) {
        saveFile(file.id);
      }
    };

    window.addEventListener('beforeunload', onBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', onBeforeUnload);
    };
  }, [openedFiles, saveFile]);

  const onToggleCollapse = useCallback(() => {
    setCollapsed((prevState) => !prevState);
  }, []);

  const onFileChange = useCallback(
    (value: string | null) => {
      setSelectedFile(openedFiles.find((file) => file.id === value) ?? null);
    },
    [openedFiles, setSelectedFile]
  );

  const onFileClose = async (fileId: string) => {
    const fileIndex = openedFiles.findIndex((file) => file.id === fileId);
    const file = openedFiles[fileIndex];
    const newSelectedFile = openedFiles[fileIndex - 1] ?? openedFiles[fileIndex + 1];

    if (!file) return;

    saveFile(file.id);
    setSelectedFile(newSelectedFile ?? null);
    clearInterval(autoSaveIntervalIdsRef.current[file.id]);
    delete autoSaveIntervalIdsRef.current[file.id];
    setOpenedFiles((prevState) => prevState.filter((file) => file.id !== fileId));
  };

  if (isLoadingWidgetAssets) {
    return (
      <Flex className={classes.container} h='100%' w='100%'>
        <Center w='100%'>
          <Loader data-testid='loader' />
        </Center>
      </Flex>
    );
  }

  if (!widgetAssets) return null;

  return (
    <Flex className={classes.container}>
      <WidgetExplorer
        widgetName={widgetName}
        tree={tree}
        collapsed={collapsed}
        onToggleCollapse={onToggleCollapse}
      />
      {openedFiles.length > 0 ? (
        <Flex direction='column' className={classes.fileContainer}>
          <Tabs
            variant='none'
            classNames={{
              root: classes.tabContainer,
              panel: classes.tabPanel,
              list: classes.tabList,
              tab: classes.tab,
            }}
            onChange={onFileChange}
            defaultValue={selectedFile?.id ?? ''}
          >
            <ScrollAreaAutosize className={classes.tabListScrollArea} scrollbarSize={4}>
              <Tabs.List>
                {openedFiles.map((file) => (
                  <Tabs.Tab
                    key={`${file.id}-${file.fields?.name}-${file.filePath}`}
                    value={file.id}
                    leftSection={<IconFile size={16} />}
                    c={selectedFile?.id === file.id ? 'white' : 'decaGrey.4'}
                    onMouseEnter={() => setHoveredTab(file.id)}
                    onMouseLeave={() => setHoveredTab(null)}
                    rightSection={
                      hoveredTab === file.id ? (
                        <IconX
                          data-testid='close-tab-button'
                          size={16}
                          onClick={(e) => {
                            e.stopPropagation();
                            onFileClose(file.id);
                          }}
                        />
                      ) : null
                    }
                  >
                    {file.fields?.name}
                  </Tabs.Tab>
                ))}
              </Tabs.List>
            </ScrollAreaAutosize>
          </Tabs>
          <WidgetEditor file={selectedFile} />
        </Flex>
      ) : (
        <Box className={cx(classes.tabContainer, classes.fileContainer)}>
          <Box h={rem(46)} bg='decaDark.9' />
          <WidgetEditor file={null} />
        </Box>
      )}
      <WidgetPreview
        onSave={() => saveFile(selectedFile?.id ?? '')}
        onPublish={openPublishModal}
        saveButtonProps={{
          disabled: !selectedFile || isLoadingS3WidgetAssets,
          loading: !!selectedFile && fileSaveState[selectedFile.filePath],
        }}
      />
      {currentWidget && (
        <PublishWidgetModal
          isPublishing={isPublishing}
          isOpen={isPublishModalOpened}
          widget={currentWidget}
          onClose={closePublishModal}
          onPublish={onPublish}
        />
      )}
    </Flex>
  );
};

export default WidgetEditorPage;
