import type { MessageType } from '@/constants/workspace';
import { Pill, PillsInput, useCombobox } from '@mantine/core';
import { Combobox } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { groupBy } from 'lodash';
import { useState } from 'react';
import type { ReceiverType } from './EditorConfig';
import OptionItem from './OptionItem';
import SelectedItem from './SelectedItem';

const useStyles = createStyles(() => ({
  pillsInput: {
    '& .mantine-PillsInput-input': {
      border: 'none',
    },
  },
}));
export type UserProfile = {
  fieldId: string;
  label: string;
  value: string;
  avatar?: string;
  group?: string;
  channelId?: string;
};
interface MailListProps {
  type: 'to' | 'cc' | 'bcc';
  receiver: ReceiverType;
  setReceiver: (receiver: ReceiverType) => void;
  profileList: UserProfile[];
  setProfileList: (profileList: UserProfile[]) => void;
  editorType: MessageType;
  receiverError?: boolean;
  setReceiverError?: (error: boolean) => void;
}
const MailList = ({
  type,
  receiver,
  setReceiver,
  profileList,
  setProfileList,
  editorType,
  receiverError,
  setReceiverError,
}: MailListProps) => {
  const { t } = useTranslate('workspace');
  const { classes } = useStyles();
  const [search, setSearch] = useState('');
  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
    onDropdownOpen: () => combobox.updateSelectedOptionIndex('active'),
  });
  const exactOptionMatch = profileList.some((item) => item.value === search);
  const handleValueSelect = (val: string) => {
    setSearch('');
    setReceiverError?.(false);
    if (val === '$create') {
      const tempFieldId = `temp_${Date.now()}`;
      setProfileList([
        ...profileList,
        {
          fieldId: tempFieldId,
          label: search,
          value: search,
          avatar: '',
          group: '',
          channelId: '',
        },
      ]);
      setReceiver({ ...receiver, [type]: [...(receiver[type] || []), tempFieldId] });
    } else {
      // Find the profile item by value to get its fieldId
      const selectedProfile = profileList.find((item) => item.value === val);
      if (selectedProfile) {
        if (editorType === 'line') {
          setReceiver({ ...receiver, [type]: [selectedProfile.fieldId] });
        } else {
          setReceiver({
            ...receiver,
            [type]: [...(receiver[type] || []), selectedProfile.fieldId],
          });
        }
      }
    }
  };
  const handleValueRemove = (fieldId: string) => {
    setReceiver({
      ...receiver,
      [type]: (receiver[type] || []).filter((v) => v !== fieldId),
    });
  };

  const values = () =>
    receiver[type]?.map((fieldId) => {
      const item = profileList.find((item) => item.fieldId === fieldId);
      if (!item) return null;
      return (
        <SelectedItem
          key={item.fieldId}
          avatar={item.avatar}
          label={item.label}
          value={item.value}
          onRemove={() => handleValueRemove(item.fieldId)}
        />
      );
    });
  const options = () => {
    const grouped = groupBy(profileList, 'group');
    return Object.entries(grouped).map(([group, items]) => (
      <Combobox.Group key={group} label={group !== 'undefined' ? group : <></>}>
        {items
          .filter((item) => !receiver[type]?.includes(item.fieldId))
          .filter(
            (item) =>
              search.trim() === '' ||
              item.label.toLowerCase().includes(search.toLowerCase()) ||
              item.value.toLowerCase().includes(search.toLowerCase())
          )
          .map((item) =>
            item.value ? (
              <Combobox.Option key={item.fieldId} value={item.value}>
                <OptionItem {...item} />
              </Combobox.Option>
            ) : null
          )}
      </Combobox.Group>
    ));
  };

  return (
    <Combobox
      offset={0}
      key={type}
      store={combobox}
      onOptionSubmit={handleValueSelect}
      withinPortal={false}
    >
      <Combobox.DropdownTarget>
        <PillsInput
          error={receiverError && type === 'to' ? t('receiverRequired') : undefined}
          w='100%'
          pointer
          className={classes.pillsInput}
        >
          <Pill.Group>
            {values()}
            <Combobox.EventsTarget>
              <PillsInput.Field
                bd='none'
                onFocus={() => combobox.openDropdown()}
                onBlur={() => combobox.closeDropdown()}
                value={search}
                onChange={(event) => {
                  combobox.updateSelectedOptionIndex();
                  setSearch(event.currentTarget.value);
                }}
                onKeyDown={(event) => {
                  if (event.key === 'Backspace' && search.length === 0) {
                    event.preventDefault();
                    handleValueRemove((receiver[type] || []).at(-1) || '');
                  }
                }}
              />
            </Combobox.EventsTarget>
          </Pill.Group>
        </PillsInput>
      </Combobox.DropdownTarget>

      <Combobox.Dropdown>
        <Combobox.Options>
          {options()}
          {editorType === 'email' && !exactOptionMatch && search.trim().length > 0 && (
            <Combobox.Option value='$create'>
              + {t('add')} {search}
            </Combobox.Option>
          )}
        </Combobox.Options>
      </Combobox.Dropdown>
    </Combobox>
  );
};

export default MailList;
