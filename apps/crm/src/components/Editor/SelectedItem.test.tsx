import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import type { UserProfile } from './MailList';
import SelectedItem from './SelectedItem';

describe('SelectedItem', () => {
  const mockOnRemove = vi.fn();

  const mockUserProfile: UserProfile & { onRemove: () => void } = {
    fieldId: 'john-id',
    label: '<PERSON>',
    value: '<EMAIL>',
    avatar: 'https://example.com/avatar.jpg',
    group: 'Primary',
    channelId: 'channel-123',
    onRemove: mockOnRemove,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders user profile information correctly', () => {
    renderWithMantine(<SelectedItem {...mockUserProfile} />);

    expect(screen.getByText('<PERSON>')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('renders avatar with correct src and alt attributes', () => {
    renderWithMantine(<SelectedItem {...mockUserProfile} />);

    const avatar = screen.getByRole('img');
    expect(avatar).toHaveAttribute('src', 'https://example.com/avatar.jpg');
    expect(avatar).toHaveAttribute('alt', 'John Doe');
  });

  it('renders close button and calls onRemove when clicked', () => {
    renderWithMantine(<SelectedItem {...mockUserProfile} />);

    const closeButton = screen.getByRole('button');
    expect(closeButton).toBeInTheDocument();

    fireEvent.mouseDown(closeButton);
    expect(mockOnRemove).toHaveBeenCalledTimes(1);
  });

  it('renders without avatar when not provided', () => {
    const profileWithoutAvatar = { ...mockUserProfile, avatar: undefined };
    renderWithMantine(<SelectedItem {...profileWithoutAvatar} />);

    // When no avatar is provided, Mantine shows a placeholder icon
    const avatarContainer = document.querySelector('.mantine-Avatar-root');
    expect(avatarContainer).toBeInTheDocument();

    // Should show placeholder instead of img
    const placeholder = document.querySelector('.mantine-Avatar-placeholder');
    expect(placeholder).toBeInTheDocument();
  });

  it('renders with empty avatar string', () => {
    const profileWithEmptyAvatar = { ...mockUserProfile, avatar: '' };
    renderWithMantine(<SelectedItem {...profileWithEmptyAvatar} />);

    // With empty string, Mantine still shows placeholder
    const avatarContainer = document.querySelector('.mantine-Avatar-root');
    expect(avatarContainer).toBeInTheDocument();

    const placeholder = document.querySelector('.mantine-Avatar-placeholder');
    expect(placeholder).toBeInTheDocument();
  });

  it('handles long label and value names', () => {
    const longProfile = {
      ...mockUserProfile,
      label: 'Very Long Name That Might Cause Layout Issues',
      value: '<EMAIL>',
    };
    renderWithMantine(<SelectedItem {...longProfile} />);

    expect(screen.getByText('Very Long Name That Might Cause Layout Issues')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('handles special characters in label and value', () => {
    const specialCharProfile = {
      ...mockUserProfile,
      label: 'John & Jane Doe',
      value: '<EMAIL>',
    };
    renderWithMantine(<SelectedItem {...specialCharProfile} />);

    expect(screen.getByText('John & Jane Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('renders with minimal required props', () => {
    const minimalProfile = {
      label: 'Test User',
      value: '<EMAIL>',
      onRemove: mockOnRemove,
    };
    renderWithMantine(<SelectedItem {...minimalProfile} />);

    expect(screen.getByText('Test User')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  it('forwards additional props to the root Box component', () => {
    const { container } = renderWithMantine(
      <SelectedItem {...mockUserProfile} data-testid='selected-item-test' />
    );

    // Check that the component structure is rendered correctly
    expect(container.firstChild).toBeInTheDocument();

    // The component may wrap props differently, just verify it renders with the prop
    const element = container.querySelector('[data-testid="selected-item-test"]');
    expect(element).toBeInTheDocument();
  });

  it('handles empty label and value', () => {
    const emptyProfile = {
      label: '',
      value: '',
      onRemove: mockOnRemove,
    };
    renderWithMantine(<SelectedItem {...emptyProfile} />);

    // Should still render the structure even with empty values
    const avatarContainer = document.querySelector('.mantine-Avatar-root');
    expect(avatarContainer).toBeInTheDocument();

    // With empty values, should show placeholder
    const placeholder = document.querySelector('.mantine-Avatar-placeholder');
    expect(placeholder).toBeInTheDocument();

    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  it('close button has correct tabIndex to prevent focus', () => {
    renderWithMantine(<SelectedItem {...mockUserProfile} />);

    const closeButton = screen.getByRole('button');
    expect(closeButton).toHaveAttribute('tabIndex', '-1');
  });

  it('uses mouseDown event instead of onClick for close button', () => {
    renderWithMantine(<SelectedItem {...mockUserProfile} />);

    const closeButton = screen.getByRole('button');

    // Test that mouseDown triggers the callback
    fireEvent.mouseDown(closeButton);
    expect(mockOnRemove).toHaveBeenCalledTimes(1);

    // Reset mock
    mockOnRemove.mockClear();

    // Test that regular click doesn't trigger the callback (since it uses onMouseDown)
    fireEvent.click(closeButton);
    expect(mockOnRemove).toHaveBeenCalledTimes(0);
  });

  it('renders with bordered container styling', () => {
    const { container } = renderWithMantine(<SelectedItem {...mockUserProfile} />);

    // The component should render with the bordered container style
    expect(container.firstChild).toBeInTheDocument();
  });

  it('handles group and channelId props correctly', () => {
    const profileWithGroupAndChannel = {
      ...mockUserProfile,
      group: 'Secondary',
      channelId: 'channel-456',
    };

    renderWithMantine(<SelectedItem {...profileWithGroupAndChannel} />);

    // The component should render successfully with these additional props
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  it('displays label with correct font weight', () => {
    renderWithMantine(<SelectedItem {...mockUserProfile} />);

    const labelText = screen.getByText('John Doe');
    expect(labelText).toBeInTheDocument();
  });

  it('displays value with correct styling', () => {
    renderWithMantine(<SelectedItem {...mockUserProfile} />);

    const valueText = screen.getByText('<EMAIL>');
    expect(valueText).toBeInTheDocument();
  });

  it('close button has correct icon size', () => {
    renderWithMantine(<SelectedItem {...mockUserProfile} />);

    const closeButton = screen.getByRole('button');
    expect(closeButton).toBeInTheDocument();
    // The close button should be rendered with the specified size (22) and iconSize (14)
  });

  it('handles multiple rapid remove clicks correctly', () => {
    renderWithMantine(<SelectedItem {...mockUserProfile} />);

    const closeButton = screen.getByRole('button');

    // Simulate rapid clicks
    fireEvent.mouseDown(closeButton);
    fireEvent.mouseDown(closeButton);
    fireEvent.mouseDown(closeButton);

    expect(mockOnRemove).toHaveBeenCalledTimes(3);
  });

  it('maintains component structure with Group layout', () => {
    const { container } = renderWithMantine(<SelectedItem {...mockUserProfile} />);

    // Should contain avatar, texts, and close button
    expect(screen.getByRole('img')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByRole('button')).toBeInTheDocument();
    expect(container.firstChild).toBeInTheDocument();
  });
});
