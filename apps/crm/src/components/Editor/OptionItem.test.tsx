import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import type { UserProfile } from './MailList';
import OptionItem from './OptionItem';

describe('OptionItem', () => {
  const mockUserProfile: UserProfile = {
    fieldId: 'john-id',
    label: '<PERSON>',
    value: '<EMAIL>',
    avatar: 'https://example.com/avatar.jpg',
    group: 'Primary',
    channelId: 'channel-123',
  };

  it('renders user profile information correctly', () => {
    renderWithMantine(<OptionItem {...mockUserProfile} />);

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('renders avatar with correct src and alt attributes', () => {
    renderWithMantine(<OptionItem {...mockUserProfile} />);

    const avatar = screen.getByRole('img');
    expect(avatar).toHaveAttribute('src', 'https://example.com/avatar.jpg');
    expect(avatar).toHaveAttribute('alt', 'John Doe');
  });

  it('renders without avatar when not provided', () => {
    const profileWithoutAvatar = { ...mockUserProfile, avatar: undefined };
    renderWithMantine(<OptionItem {...profileWithoutAvatar} />);

    // When no avatar is provided, Mantine shows a placeholder icon
    const avatarContainer = document.querySelector('.mantine-Avatar-root');
    expect(avatarContainer).toBeInTheDocument();

    // Should show placeholder instead of img
    const placeholder = document.querySelector('.mantine-Avatar-placeholder');
    expect(placeholder).toBeInTheDocument();
  });

  it('renders with empty avatar string', () => {
    const profileWithEmptyAvatar = { ...mockUserProfile, avatar: '' };
    renderWithMantine(<OptionItem {...profileWithEmptyAvatar} />);

    // With empty string, Mantine still shows placeholder
    const avatarContainer = document.querySelector('.mantine-Avatar-root');
    expect(avatarContainer).toBeInTheDocument();

    const placeholder = document.querySelector('.mantine-Avatar-placeholder');
    expect(placeholder).toBeInTheDocument();
  });

  it('handles long label names gracefully', () => {
    const longProfile = {
      ...mockUserProfile,
      label: 'Very Long Name That Might Cause Layout Issues In The Component',
      value: '<EMAIL>',
    };
    renderWithMantine(<OptionItem {...longProfile} />);

    expect(
      screen.getByText('Very Long Name That Might Cause Layout Issues In The Component')
    ).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('handles special characters in label and value', () => {
    const specialCharProfile = {
      ...mockUserProfile,
      label: 'John & Jane Doe',
      value: '<EMAIL>',
    };
    renderWithMantine(<OptionItem {...specialCharProfile} />);

    expect(screen.getByText('John & Jane Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('renders with minimal required props', () => {
    const minimalProfile: UserProfile = {
      fieldId: 'john-id',
      label: 'Test User',
      value: '<EMAIL>',
    };
    renderWithMantine(<OptionItem {...minimalProfile} />);

    expect(screen.getByText('Test User')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('forwards additional props to the root Box component', () => {
    const { container } = renderWithMantine(
      <OptionItem {...mockUserProfile} data-testid='option-item-test' />
    );

    // Check that the component structure is rendered correctly
    expect(container.firstChild).toBeInTheDocument();

    // The component may wrap props differently, just verify it renders with the prop
    const element = container.querySelector('[data-testid="option-item-test"]');
    expect(element).toBeInTheDocument();
  });

  it('renders with different avatar shapes (circular)', () => {
    renderWithMantine(<OptionItem {...mockUserProfile} />);

    // Check that avatar has the expected radius class for circular shape
    const avatar = screen.getByRole('img');
    expect(avatar).toBeInTheDocument();
  });

  it('handles empty label and value', () => {
    const emptyProfile: UserProfile = {
      fieldId: 'john-id',
      label: '',
      value: '',
    };
    renderWithMantine(<OptionItem {...emptyProfile} />);

    // Should still render the structure even with empty values
    const avatarContainer = document.querySelector('.mantine-Avatar-root');
    expect(avatarContainer).toBeInTheDocument();

    // With empty values, should show placeholder
    const placeholder = document.querySelector('.mantine-Avatar-placeholder');
    expect(placeholder).toBeInTheDocument();
  });

  it('uses correct text sizing and weight', () => {
    renderWithMantine(<OptionItem {...mockUserProfile} />);

    const labelText = screen.getByText('John Doe');
    const valueText = screen.getByText('<EMAIL>');

    // Both should be visible and rendered as text elements
    expect(labelText).toBeInTheDocument();
    expect(valueText).toBeInTheDocument();
  });

  it('maintains proper component structure with Group and Avatar', () => {
    const { container } = renderWithMantine(<OptionItem {...mockUserProfile} />);

    // Should contain the avatar
    const avatar = screen.getByRole('img');
    expect(avatar).toBeInTheDocument();

    // Should have the proper structure
    expect(container.firstChild).toBeInTheDocument();
  });

  it('handles group and channelId props (even though not directly rendered)', () => {
    const profileWithGroupAndChannel: UserProfile = {
      fieldId: 'john-id',
      label: 'Test User',
      value: '<EMAIL>',
      group: 'Secondary',
      channelId: 'channel-456',
    };

    renderWithMantine(<OptionItem {...profileWithGroupAndChannel} />);

    // The component should render successfully even with these additional props
    expect(screen.getByText('Test User')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });
});
