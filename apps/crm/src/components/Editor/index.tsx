import type { MessageType } from '@/constants/workspace';
import { useProfileContext } from '@/contexts/ProfileContext';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { useTemplates } from '@/hooks';
import { useChannels } from '@/hooks/useChannels';
import type { Attachment, MessagePayload } from '@/models';
import { RecordAPI } from '@/services/api';
import {
  useShowMessageFailedNotification,
  useShowMessageSentNotification,
  useShowMessageUndoNotification,
} from '@/utils';
import { Box, Divider, Flex, Group, ScrollArea, Text, TextInput, rem } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { RichTextEditor } from '@mantine/tiptap';
import { DecaButton } from '@resola-ai/ui';
import { useEditor } from '@tiptap/react';
import { useTranslate } from '@tolgee/react';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { AttachmentProvider, useAttachmentContext } from './EditorAttachments';
import {
  type EditorInstance,
  EditorToolbar,
  type ReceiverType,
  type Template,
  availableFields,
  characterLimit,
  extensions,
} from './EditorConfig';
import MailList, { type UserProfile } from './MailList';
import EditorMenu from './Menu';
import { useEditorStyles } from './useEditorStyles';

interface IEditor {
  editorType: MessageType;
  closeModal: () => void;
  openModal: () => void;
  setMounted: (mounted: boolean) => void;
}

// Main editor component with attachment provider wrapper
const Editor = (props: IEditor) => {
  return (
    <AttachmentProvider>
      <EditorContent {...props} />
    </AttachmentProvider>
  );
};

// Separate component that uses the attachment context
const EditorContent = ({ editorType, closeModal, openModal, setMounted }: IEditor) => {
  const { t } = useTranslate('workspace');
  const { wsId, id: objId, recordId } = useParams();
  const { profile } = useProfileContext();
  const { object } = useWorkspaceContext();
  const { channels } = useChannels();
  const { classes, cx } = useEditorStyles();
  const { selectedAttachmentObjects, setSelectedAttachments } = useAttachmentContext();

  // Initialize notification hooks
  const showMessageSentNotification = useShowMessageSentNotification();
  const showMessageFailedNotification = useShowMessageFailedNotification();
  const showMessageUndoNotification = useShowMessageUndoNotification();

  // Initialize editor with profile fields for mention functionality
  const editor: EditorInstance = useEditor({
    extensions: extensions(
      editorType,
      undefined,
      object?.fields,
      editorType === 'email' && object ? availableFields(object, profile) : undefined
    ) as any,
  });

  // State management
  const [formState, setFormState] = useState({
    showCC: false,
    showBCC: false,
    subject: '',
    showMenu: false,
    subjectError: false,
    receiverError: false,
  });
  const [profileList, setProfileList] = useState<UserProfile[]>([]);
  const [receiver, setReceiver] = useState<ReceiverType>({ to: [] });

  const lastEditorContentRef = useRef('');

  // Destructure form state for easier access
  const { showCC, showBCC, subject, showMenu, subjectError, receiverError } = formState;

  // Simplify form state updates
  const updateFormState = (updates: Partial<typeof formState>) => {
    setFormState((prev) => ({ ...prev, ...updates }));
  };

  // Fetch templates
  const { templates, mutateTemplates } = useTemplates(
    wsId,
    `{"type":"${editorType === 'email' ? 'email' : 'sms'}", "objectId": "${objId}"}`
  );

  // Process profile data to get mail fields
  useEffect(() => {
    if (object?.fields && profile) {
      const fields = object.fields;
      const mailFields = fields.filter(
        (f) => f.type === (editorType === 'sms' ? 'phone' : editorType)
      );

      const mapProfile = mailFields.map((field) => {
        const label =
          editorType === 'line'
            ? channels?.find((c) => c.id === field.options?.channelId)
            : fields.find((f) => f.type === 'text' && f.options?.isPrimary === true);

        const avatar = fields.find((f) => f.type === 'image' && f.options?.isAvatar === true);

        return {
          fieldId: field.id,
          label:
            label?.id && profile[label.id] && label.id !== field.id
              ? profile[label.id]
              : label?.name || '',
          avatar: avatar?.id ? profile[avatar.id] : '',
          value: field.id && profile[field.id] ? profile[field.id] : '',
          group:
            editorType === 'line'
              ? ''
              : field.options?.isPrimary
                ? editorType === 'email'
                  ? t('emailPrimary')
                  : t('smsPrimary')
                : editorType === 'email'
                  ? t('emailSecondary')
                  : t('smsSecondary'),
          channelId: editorType === 'line' ? label?.ocsChannelId : '',
        };
      });

      setProfileList(mapProfile);

      // Auto-select receiver: prioritize primary email, then first available email
      const validReceivers = mapProfile.filter((item) => item.value);
      if (validReceivers.length > 0) {
        // First, try to find primary email
        const primaryReceiver = validReceivers.find(
          (item) => item.group === (editorType === 'email' ? t('emailPrimary') : t('smsPrimary'))
        );

        if (primaryReceiver) {
          // Auto-select primary email
          setReceiver({ to: [primaryReceiver.fieldId] });
        } else {
          // If no primary email, select the first available email
          setReceiver({ to: [validReceivers[0].fieldId] });
        }
      }
    }

    return () => {
      setProfileList([]);
    };
  }, [object, profile, channels, editorType, t]);

  // Handle template selection
  const handleTemplateSelect = (template: Template) => {
    if (editorType === 'email') {
      updateFormState({ subject: template.description });
    }

    if (template.attachments && template.attachments.length > 0) {
      setSelectedAttachments(template.attachments);
    } else {
      setSelectedAttachments([]);
    }

    // When inserting a template, we need to set isTemplate to true to properly handle deleted fields
    if (editor) {
      // Store the current content
      const originalContent = editor.getHTML();

      // Create a temporary editor configuration with isTemplate=true to check for missing fields
      editor.setOptions({
        extensions: extensions(
          editorType,
          t(editorType === 'email' ? 'inputEmailContent' : 'inputMessageContent'),
          object?.fields,
          editorType === 'email' && object ? availableFields(object, profile) : undefined,
          true // Set isTemplate to true
        ) as any,
      });

      // Insert the template content
      editor.commands.setContent(template.content);

      // Get the content with potential error indicators for deleted fields
      const processedContent = editor.getHTML();

      // Reset the editor extensions to normal mode (isTemplate=false)
      editor.setOptions({
        extensions: extensions(
          editorType,
          t(editorType === 'email' ? 'inputEmailContent' : 'inputMessageContent'),
          object?.fields,
          editorType === 'email' && object ? availableFields(object, profile) : undefined,
          false // Reset isTemplate to false
        ) as any,
      });

      // Set the content back with the processed content (with error indicators)
      editor.commands.setContent(originalContent);
      editor.commands.insertContent(processedContent);

      // After inserting the template content, update all mention nodes to display profile values on hover
      if (editorType === 'email' && profile && object?.fields) {
        // Need to wait for DOM to update with new content
        setTimeout(() => {
          const editorElement = document.querySelector('.ProseMirror');
          if (editorElement) {
            const mentionNodes = editorElement.querySelectorAll('.mention:not(.mention-deleted)');
            mentionNodes.forEach((node) => {
              const fieldId = node.getAttribute('data-field-id');
              if (fieldId && profile[fieldId]) {
                // Set the actual profile value to data-value for hover tooltip
                node.setAttribute('data-value', profile[fieldId] || '');
              }
            });
          }
        }, 100);
      }
    }
  };

  // Handle emoji selection for line messages
  const handleEmojiSelect = (emoji: string) => {
    if (editor && editorType === 'line') {
      editor.commands.insertContent(emoji);
    }
  };

  // Prepare and send message payload
  const prepareMessagePayload = (): MessagePayload => {
    // Format attachments for the payload
    const attachments =
      editorType === 'email' && selectedAttachmentObjects.length > 0
        ? selectedAttachmentObjects.map((attachment: Attachment) => ({
            path: attachment.path,
            type: attachment.mimeType,
            filename: attachment.title,
            url: attachment.url,
            disposition: 'attachment',
          }))
        : undefined;

    // Get editor content, process mentions if needed
    let emailContent = editorType === 'email' ? editor?.getHTML() : undefined;
    const smsContent = editorType !== 'email' ? editor?.getText() : undefined;

    // Process email content to convert mention nodes to {{fieldId}} format
    if (emailContent && editorType === 'email') {
      // Create a temporary DOM element to parse the HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = emailContent;

      // Find all mention nodes
      const mentionNodes = tempDiv.querySelectorAll('.mention');

      // Replace each mention with its {{fieldId}} format
      mentionNodes.forEach((node) => {
        const fieldId = node.getAttribute('data-field-id') || node.getAttribute('data-id');
        if (fieldId) {
          const replacementText = `{{${fieldId}}}`;
          const textNode = document.createTextNode(replacementText);
          node.replaceWith(textNode);
        }
      });

      // Get the processed HTML
      emailContent = tempDiv.innerHTML;
    }

    // Convert fieldIds back to values for the payload
    const getValueFromFieldId = (fieldId: string) => {
      const profile = profileList.find((p) => p.fieldId === fieldId);
      return profile?.value || '';
    };

    return {
      to:
        editorType === 'line'
          ? receiver.to.map((fieldId) => {
              const profile = profileList.find((p) => p.fieldId === fieldId);
              return {
                lineId: profile?.value || '',
                channelId: profile?.channelId,
              };
            })
          : receiver.to.map(getValueFromFieldId),
      cc: receiver.cc?.map(getValueFromFieldId),
      bcc: receiver.bcc?.map(getValueFromFieldId),
      subject: subject || undefined,
      html: emailContent,
      body: smsContent,
      channel: editorType === 'email' ? 'mail' : editorType,
      attachments,
    };
  };

  const handleOpenLiveChat = () => {
    const profile = profileList.find((p) => p.fieldId === receiver.to[0]);
    const channelId = profile?.channelId;
    const integrationId = channels?.find((c) => c.ocsChannelId === channelId)?.id;
    if (integrationId && profile) {
      RecordAPI.getLiveChatUrl({
        enduserId: profile.value,
        integrationId: integrationId,
      }).then((res) => {
        window.open(res.conversationUrl, '_blank');
      });
    }
  };

  // Send message function
  const sendMessage = async (payload: MessagePayload) => {
    if (wsId && objId && recordId) {
      try {
        const res = await RecordAPI.sendMessage(wsId, objId, recordId, payload);
        if (res.status !== false || res.result.status !== 'error') {
          showMessageSentNotification({
            editorType,
            onOpenLiveChat: editorType === 'line' ? handleOpenLiveChat : undefined,
          });
        } else {
          showMessageFailedNotification({
            editorType,
          });
        }
      } catch (error) {
        console.error(error);
      } finally {
        setMounted(false);
      }
    }
  };

  // Handle send click
  const handleSend = async () => {
    setMounted(true);

    // Validate form
    if (!subject && editorType === 'email') {
      updateFormState({ subjectError: true });
      return;
    }

    if (
      !receiver.to.length ||
      receiver.to.every((fieldId) => {
        const profile = profileList.find((p) => p.fieldId === fieldId);
        return !profile?.value;
      })
    ) {
      updateFormState({ receiverError: true });
      return;
    }

    lastEditorContentRef.current = editor?.getHTML() || '';

    const payload = prepareMessagePayload();

    editor?.commands.setContent('');
    // Show sending notification with undo option
    showMessageUndoNotification({
      editorType,
      onUndo: () => {
        openModal();
        editor?.commands.setContent(lastEditorContentRef.current);
        notifications.update({
          id: 'sendingNotification',
          message: '',
          onClose: undefined,
          autoClose: 0,
        });
      },
      onSend: () => sendMessage(payload),
    });

    closeModal();
  };

  // Render receiver inputs (to, cc, bcc)
  const renderReceivers = useMemo(() => {
    // eslint-disable-next-line react/display-name
    return () => (
      <Box w='100%'>
        <Flex
          align='center'
          justify='space-between'
          gap='xs'
          w='100%'
          className={cx(classes.profileInput, {
            [classes.receiverBorder]: ['sms', 'line'].includes(editorType),
          })}
        >
          <Flex align='center' w='100%' direction='row'>
            <Text w={rem(40)} mr={rem(20)} fw={500}>
              {t('to')}
            </Text>
            <MailList
              receiverError={receiverError}
              key={`to-${editorType}`}
              type='to'
              receiver={receiver}
              setReceiver={setReceiver}
              profileList={profileList}
              setProfileList={setProfileList}
              editorType={editorType}
              setReceiverError={(value: boolean) => updateFormState({ receiverError: value })}
            />
          </Flex>
          {editorType === 'email' && (
            <Flex align='flex-end' direction='row' gap='xs' fz={rem(14)}>
              <DecaButton
                size='sm'
                variant='neutral_text'
                onClick={() => updateFormState({ showCC: !showCC })}
              >
                Cc
              </DecaButton>
              <DecaButton
                size='sm'
                variant='neutral_text'
                onClick={() => updateFormState({ showBCC: !showBCC })}
              >
                Bcc
              </DecaButton>
            </Flex>
          )}
        </Flex>
        {showCC && (
          <Flex
            align='center'
            justify='space-between'
            gap='xs'
            w='100%'
            className={classes.profileInput}
          >
            <Flex align='center' w='100%' direction='row'>
              <Text w={rem(40)} mr={rem(20)} fw={500}>
                Cc
              </Text>
              <MailList
                receiverError={receiverError}
                key={`cc-${editorType}`}
                type='cc'
                receiver={receiver}
                setReceiver={setReceiver}
                profileList={profileList}
                setProfileList={setProfileList}
                editorType={editorType}
              />
            </Flex>
          </Flex>
        )}
        {showBCC && (
          <Flex
            align='center'
            justify='space-between'
            gap='xs'
            w='100%'
            className={classes.profileInput}
          >
            <Flex align='center' w='100%' direction='row'>
              <Text w={rem(40)} mr={rem(20)} fw={500}>
                Bcc
              </Text>
              <MailList
                receiverError={receiverError}
                key={`bcc-${editorType}`}
                type='bcc'
                receiver={receiver}
                setReceiver={setReceiver}
                profileList={profileList}
                setProfileList={setProfileList}
                editorType={editorType}
              />
            </Flex>
          </Flex>
        )}
        {editorType === 'email' && (
          <Flex
            align='center'
            justify='flex-start'
            gap='xs'
            w='100%'
            className={classes.profileSubject}
          >
            <Flex align='center' w='100%' direction='row'>
              <Text w={rem(40)} mr={rem(20)} fw={500}>
                {t('subject')}
              </Text>
              <TextInput
                w='100%'
                onChange={(e) => {
                  updateFormState({
                    subject: e.target.value,
                    subjectError: false,
                  });
                }}
                value={subject}
                error={subjectError ? t('subjectRequired') : undefined}
              />
            </Flex>
          </Flex>
        )}
      </Box>
    );
  }, [
    editorType,
    showCC,
    showBCC,
    subject,
    t,
    subjectError,
    receiver,
    profileList,
    receiverError,
    classes.profileInput,
    classes.profileSubject,
    updateFormState,
  ]);

  return (
    <Box>
      {renderReceivers()}
      <RichTextEditor editor={editor} className={classes.editor}>
        <ScrollArea>
          <RichTextEditor.Content autoFocus mih={rem(300)} mah={rem(600)} />
        </ScrollArea>
        {editorType === 'email' && editor && <EditorToolbar editor={editor} />}
        {editor && editorType === 'sms' && (
          <Text size='xs' c='decaGrey.6' mb={rem(10)}>
            {editor.storage.characterCount.characters()} / {characterLimit} {t('characters')}
          </Text>
        )}
      </RichTextEditor>
      <Divider mr={rem(-24)} ml={rem(-24)} />
      <Group justify='space-between' mt='md'>
        <EditorMenu
          showMenu={showMenu}
          setShowMenu={(show) => updateFormState({ showMenu: show })}
          templates={templates || []}
          onTemplateSelect={handleTemplateSelect}
          editorType={editorType}
          mutateTemplates={mutateTemplates}
          onEmojiSelect={handleEmojiSelect}
        />
        <Group>
          <DecaButton
            variant='neutral'
            onClick={() => {
              closeModal();
              setMounted(false);
            }}
            fw={500}
          >
            {t('cancel')}
          </DecaButton>
          <DecaButton fw={500} onClick={handleSend}>
            {editorType === 'email'
              ? t('sendEmail')
              : editorType === 'line'
                ? t('sendLine', 'Send Line')
                : t('sendSMS')}
          </DecaButton>
        </Group>
      </Group>
    </Box>
  );
};

export default Editor;
