import type { MessageType } from '@/constants/workspace';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { WorkspaceAPI } from '@/services/api';
import { useShowTemplateSavedNotification } from '@/utils';
import { Box, Divider, Flex, Paper, ScrollArea, Text, TextInput, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDebouncedState } from '@mantine/hooks';
import { RichTextEditor } from '@mantine/tiptap';
import { DecaButton, Modal as DecaModal } from '@resola-ai/ui';
import { Modal } from '@resola-ai/ui';
import { IconPlus } from '@tabler/icons-react';
import { useEditor } from '@tiptap/react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useAttachmentContext } from './EditorAttachments';
import {
  type EditorInstance,
  EditorToolbar,
  type Template,
  availableFields,
  extensions,
} from './EditorConfig';
import { useEditorStyles } from './useEditorStyles';

const useStyles = createStyles((theme) => ({
  modal: {
    '.mantine-Modal-content': {
      height: '90vh',
      overflow: 'hidden',
      borderRadius: rem(8),
    },
    '.mantine-RichTextEditor-content': {
      border: `${rem(1)} solid ${theme.colors.decaGrey[0]}`,
    },
    '.mantine-Modal-body': {
      height: `calc(100% - ${rem(60)})`,

      '& > div:first-child': {
        height: '100%',
        padding: '0 !important',
      },
    },
  },
}));
const TemplateModal: React.FC<{
  opened: boolean;
  onClose: () => void;
  editorType: MessageType;
  mutateTemplates: () => void;
  templates: Template[];
}> = ({ opened, onClose, editorType, mutateTemplates, templates }) => {
  const { wsId, objId } = useParams();
  const { classes } = useStyles();
  const { classes: editorClasses } = useEditorStyles();
  const { t } = useTranslate('workspace');
  const showTemplateSavedNotification = useShowTemplateSavedNotification();
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [newTemplate, setNewTemplate] = useDebouncedState<Template>(
    {
      id: '',
      name: '',
      description: '',
      content: '',
      attachments: [],
    },
    200
  );
  const [nameError, setNameError] = useState(false);
  const [subjectError, setSubjectError] = useState(false);
  const [saving, setSaving] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [contentError, setContentError] = useState(false);
  const [confirmModalOpened, setConfirmModalOpened] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const nameRef = useRef<HTMLInputElement>(null);
  const desRef = useRef<HTMLInputElement>(null);
  const { object } = useWorkspaceContext();
  const { selectedAttachments, setSelectedAttachments } = useAttachmentContext();

  const editor: EditorInstance = useEditor({
    extensions: extensions(
      editorType,
      t(editorType === 'email' ? 'inputEmailContent' : 'inputMessageContent'),
      object?.fields,
      editorType === 'email' && object ? availableFields(object) : undefined,
      true
    ) as any,
    onUpdate: () => {
      setContentError(false);
    },
  });

  useEffect(() => {
    if (selectedTemplate && !isInitializing) {
      setIsInitializing(true);

      // Use template data directly - templates list already has all details
      if (nameRef.current) nameRef.current.value = selectedTemplate.name;
      if (desRef.current) desRef.current.value = selectedTemplate.description;
      editor?.commands.setContent(selectedTemplate.content);

      // Load template attachments if they exist
      if (selectedTemplate.attachments && selectedTemplate.attachments.length > 0) {
        setSelectedAttachments(selectedTemplate.attachments);
      } else {
        setSelectedAttachments([]);
      }

      setIsInitializing(false);
    } else if (!selectedTemplate) {
      if (nameRef.current) nameRef.current.value = '';
      if (desRef.current) desRef.current.value = '';
      editor?.commands.setContent('');
      setSelectedAttachments([]);
    }
    setContentError(false);
  }, [selectedTemplate?.id, editor, isInitializing, setSelectedAttachments]);

  const handleSave = async () => {
    setSaving(true);
    setNameError(false);
    setSubjectError(false);
    setContentError(false);

    if (!newTemplate.name && !selectedTemplate?.name) {
      setNameError(true);
      setSaving(false);
      return;
    }

    if (editorType === 'email' && !newTemplate.description && !selectedTemplate?.description) {
      setSubjectError(true);
      setSaving(false);
      return;
    }

    const content = editor?.getText();
    if (!content || content.trim() === '') {
      setContentError(true);
      setSaving(false);
      return;
    }

    let res: any;
    if (selectedTemplate && wsId) {
      res = await WorkspaceAPI.updateTemplate(wsId, selectedTemplate.id, {
        ...selectedTemplate,
        content: editor?.getHTML(),
        type: editorType === 'email' ? 'email' : 'sms',
        objectId: objId || object?.id,
        attachments: selectedAttachments,
      });
    } else if (newTemplate.name && wsId) {
      res = await WorkspaceAPI.createTemplate(wsId, {
        ...newTemplate,
        content: editor?.getHTML(),
        type: editorType === 'email' ? 'email' : 'sms',
        objectId: objId || object?.id,
        attachments: selectedAttachments,
      });
    }
    if (res && 'id' in res) {
      showTemplateSavedNotification({ editorType });
      mutateTemplates();
    }
    setSaving(false);
  };

  const handleDelete = async () => {
    if (selectedTemplate && wsId) {
      setDeleting(true);
      const res = await WorkspaceAPI.deleteTemplate(wsId, selectedTemplate.id);

      if (res?.message) {
        mutateTemplates();
        addNewTemplate();
        setConfirmModalOpened(false);
      }
      setDeleting(false);
    }
  };

  const addNewTemplate = useCallback(() => {
    setSelectedTemplate(null);
    setNewTemplate({ id: '', name: '', description: '', content: '', attachments: [] });
    setNameError(false);
    setSubjectError(false);
    setContentError(false);
    setIsInitializing(false);
    editor?.commands.setContent('');
  }, [editor, setNewTemplate]);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNameError(false);
    const newValue = e.target.value;
    if (selectedTemplate) {
      setSelectedTemplate({ ...selectedTemplate, name: newValue });
    } else {
      setNewTemplate({ ...newTemplate, name: newValue });
    }
  };

  const handleSubjectChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSubjectError(false);
    const newValue = e.target.value;
    if (selectedTemplate) {
      setSelectedTemplate({ ...selectedTemplate, description: newValue });
    } else {
      setNewTemplate({ ...newTemplate, description: newValue });
    }
  };

  // Reset to new template mode when modal opens
  useEffect(() => {
    if (opened) {
      // Reset to new template mode
      setSelectedTemplate(null);
      setNewTemplate({ id: '', name: '', description: '', content: '', attachments: [] });
      setNameError(false);
      setSubjectError(false);
      setContentError(false);
      setIsInitializing(false);

      // Clear selected attachments
      setSelectedAttachments([]);

      // Clear form inputs
      if (nameRef.current) nameRef.current.value = '';
      if (desRef.current) desRef.current.value = '';
      editor?.commands.setContent('');
    }
  }, [opened, setNewTemplate, setSelectedAttachments, editor]);

  // Add data-template attribute to ProseMirror element when component is mounted
  useEffect(() => {
    if (editor) {
      const editorElement = document.querySelector('.ProseMirror');
      if (editorElement) {
        editorElement.setAttribute('data-template', 'true');
      }
    }
  }, [editor]);

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      size={'70%'}
      title={editorType === 'email' ? t('manageEmailTemplates') : t('manageSMSTemplates')}
      centered
      bodyPadding={0}
      className={classes.modal}
    >
      <Flex direction='row' h={`calc(100% - ${rem(70)})`}>
        <Paper bg='decaLight.0'>
          <Box px={rem(16)} py={rem(14)} bg={!selectedTemplate ? 'white' : 'transparent'}>
            <DecaButton
              variant='secondary_text'
              leftSection={<IconPlus size={16} />}
              fullWidth
              onClick={addNewTemplate}
            >
              {t('newTemplate')}
            </DecaButton>
          </Box>
          <ScrollArea h={400}>
            {templates.map((template) => (
              <Flex
                key={template.id}
                onClick={() => {
                  setSelectedTemplate(template);
                }}
                bg={selectedTemplate?.id === template.id ? 'white' : 'transparent'}
                p='md'
                direction='column'
              >
                <Text fz={rem(16)} c='decaGrey.9' fw={500}>
                  {template.name}
                </Text>
                <Text fz={rem(14)} c='decaGrey.5' lineClamp={2}>
                  {template.description}
                </Text>
              </Flex>
            ))}
          </ScrollArea>
        </Paper>
        <Divider p={0} orientation='vertical' />
        <Paper p='md' pb={0} w='100%' h={'100%'}>
          <ScrollArea h={`calc(100% - ${rem(70)})`}>
            <TextInput
              ref={nameRef}
              label={
                <Text fw={500} fz={rem(14)} mb={rem(6)}>
                  <span>{t('templateName')}&nbsp;</span>
                  <span style={{ color: '#fa5252' }}>*</span>
                </Text>
              }
              onChange={handleNameChange}
              error={nameError && t('templateNameRequired')}
              mb='md'
              placeholder={t('inputName')}
              size='md'
            />
            {editorType === 'email' && (
              <TextInput
                ref={desRef}
                label={
                  <Text fw={500} fz={rem(14)} mb={rem(6)}>
                    <span>{t('emailSubject')}&nbsp;</span>
                    <span style={{ color: '#fa5252' }}>*</span>
                  </Text>
                }
                onChange={handleSubjectChange}
                error={subjectError && t('emailSubjectRequired')}
                mb='md'
                placeholder={t('inputSubject')}
                size='md'
              />
            )}
            <Text fw={500} fz={rem(14)} mb={rem(6)}>
              <span>
                {editorType === 'email' ? t('emailContent') : t('smsContent')}
                &nbsp;
              </span>
              <span style={{ color: '#fa5252' }}>*</span>
            </Text>
            <RichTextEditor editor={editor} className={editorClasses.editor}>
              <ScrollArea h={'30vh'} mb={rem(20)}>
                <RichTextEditor.Content autoFocus mih={'30vh'} mah={rem(600)} />
              </ScrollArea>
              {editor && editorType === 'email' && (
                <EditorToolbar
                  editor={editor}
                  isTemplate={true}
                  selectedTemplate={selectedTemplate}
                />
              )}
            </RichTextEditor>
            {contentError && (
              <Text c='#fa5252' fz={rem(10)}>
                {t('templateContentRequired')}
              </Text>
            )}
          </ScrollArea>
          <Flex justify='space-between' mt={rem(16)}>
            <DecaButton
              variant='negative_text'
              onClick={() => setConfirmModalOpened(true)}
              loading={deleting}
              disabled={!selectedTemplate || saving}
            >
              {t('deleteTemplate')}
            </DecaButton>
            <DecaButton onClick={handleSave} loading={saving} disabled={deleting}>
              {t('save')}
            </DecaButton>
          </Flex>
        </Paper>
      </Flex>
      <Divider />
      <Box py={rem(16)} h={rem(70)}>
        <DecaButton variant='neutral_text' onClick={onClose} color='decaGrey.6' fw={500}>
          {t('close')}
        </DecaButton>
      </Box>
      <DecaModal
        centered
        opened={confirmModalOpened}
        onClose={() => setConfirmModalOpened(false)}
        onCancel={() => setConfirmModalOpened(false)}
        onOk={handleDelete}
        title={t('deleteTemplate')}
        okText={t('delete')}
        cancelText={t('cancel')}
        okButtonProps={{ variant: 'negative' }}
        footerDivider={false}
      >
        <Text>
          {t('deleteTemplateWarning', {
            template: selectedTemplate?.name || '',
          })}
        </Text>
      </DecaModal>
    </Modal>
  );
};

export default TemplateModal;
