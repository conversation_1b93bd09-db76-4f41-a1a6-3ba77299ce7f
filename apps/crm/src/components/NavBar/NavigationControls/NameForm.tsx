import { IconList } from '@/constants/workspace';
import { useAppContext } from '@/contexts/AppContext';
import { useNavbarContext } from '@/contexts/NavbarContext';
import type { WSObject } from '@/models';
import {
  DndContext,
  type DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  closestCenter,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  SortableContext,
  arrayMove,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  ActionIcon,
  Box,
  Checkbox,
  Collapse,
  Divider,
  Flex,
  ScrollArea,
  Stack,
  Text,
  rem,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton, HFSwitch } from '@resola-ai/ui';
import { IconChevronDown, IconChevronUp, IconGripVertical } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { TextInput } from 'react-hook-form-mantine';
import { z } from 'zod';

type Props = {
  object?: WSObject;
  onSave?: (ws: WSObject) => void;
  onCancel: () => void;
};

const useStyles = createStyles((theme) => ({
  form: {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  icon: {
    width: rem(30),
    height: rem(30),
    padding: rem(4),
    '& svg': {
      color: `${theme.colors.decaGrey[6]} !important`,
    },
    '&:hover': {
      backgroundColor: theme.colors.decaBlue[0],
      svg: {
        color: `${theme.colors.decaBlue[5]} !important`,
      },
    },
  },
  selected: {
    backgroundColor: theme.colors.decaBlue[0],
    '& svg': {
      color: `${theme.colors.decaBlue[5]} !important`,
    },
  },
  component: {
    borderRadius: rem(8),
    padding: rem(8),
  },
  subComp: {
    borderRadius: rem(8),
    border: `1px solid ${theme.colors.decaLight[1]}`,
  },
}));

type SortableItemProps = {
  id: string;
  label: string;
  name: string;
  control: any;
};

const SortableItem = ({ id, label, name, control }: SortableItemProps) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div ref={setNodeRef} style={style}>
      <Flex align='center' justify='space-between'>
        <Flex align='center' gap={rem(8)}>
          <div {...attributes} {...listeners} style={{ cursor: 'grab' }}>
            <IconGripVertical size={16} color='#9CA3AF' />
          </div>
          <Text fz={rem(14)}>{label}</Text>
        </Flex>
        <HFSwitch control={control} name={name} size='sm' />
      </Flex>
    </div>
  );
};

export const NameForm = ({ object, onSave, onCancel }: Props) => {
  const { t } = useTranslate('common');
  const { createObject, updateObject } = useNavbarContext();
  const { mutateObjects, objects } = useAppContext();
  const { classes, cx, theme } = useStyles();

  // State for linked objects collapse
  const [linkedObjectsOpened, setLinkedObjectsOpened] = useState(true);
  const [childObjects, setChildObjects] = useState<
    { id: string; pinned: boolean; isVisible: boolean }[]
  >([]);

  // State for drag and drop ordering
  const [messageItems, setMessageItems] = useState([
    { id: 'email', label: t('email'), name: 'messaging.email' },
    { id: 'sms', label: 'SMS', name: 'messaging.sms' },
    { id: 'line', label: 'LINE', name: 'messaging.line' },
  ]);

  const defaultItems = useMemo(
    () => [
      { id: 'activities', label: t('activities'), name: 'profileSettings.activities' },
      { id: 'identities', label: t('identities'), name: 'profileSettings.identities' },
      { id: 'attachments', label: t('files'), name: 'profileSettings.attachments' },
      { id: 'tasks', label: t('history'), name: 'profileSettings.tasks' },
      { id: 'pinLongText', label: t('longText'), name: 'profileSettings.pinLongText' },
      { id: 'pinCustomObject', label: t('customObject'), name: 'profileSettings.pinCustomObject' },
    ],
    [t]
  );

  const [additionalRecordItems, setAdditionalRecordItems] = useState(defaultItems);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const nameSchema = z.object({
    singular: z.string().min(1, { message: t('requiredField') }),
    plural: z.string().optional(),
  });
  const objSchema = z
    .object({
      name: nameSchema,
      icon: z.string(),
      hasAvatar: z.boolean().optional(),
      hasTags: z.boolean().optional(),
      profileSettings: z
        .object({
          activities: z.boolean().optional(),
          identities: z.boolean().optional(),
          attachments: z.boolean().optional(),
          tasks: z.boolean().optional(),
          pinLongText: z.boolean().optional(),
          pinCustomObject: z.boolean().optional(),
        })
        .optional(),
      messaging: z
        .object({
          sms: z.boolean().optional(),
          email: z.boolean().optional(),
          line: z.boolean().optional(),
        })
        .optional(),
    })
    .passthrough();

  const methods = useForm({
    defaultValues: {
      name: {
        singular: '',
        plural: '',
      },
      icon: 'users',
      hasAvatar: false,
      hasTags: false,
      profileSettings: {
        activities: false,
        identities: false,
        attachments: false,
        tasks: false,
        pinLongText: false,
        pinCustomObject: false,
      },
      messaging: {
        sms: false,
        email: false,
        line: false,
      },
    },
    resolver: zodResolver(objSchema),
  });
  const { handleSubmit, control, setValue, watch, reset } = methods;

  useEffect(() => {
    if (object) {
      // Transform profileSettings array to form format
      const formData: any = { ...object };
      if (object.profileSettings) {
        // Initialize with default false values
        formData.profileSettings = {
          activities: false,
          identities: false,
          attachments: false,
          tasks: false,
          pinLongText: false,
          pinCustomObject: false,
        };

        // Set enabled states from API data
        object.profileSettings.forEach((setting) => {
          if (formData.profileSettings) {
            formData.profileSettings[setting.type] = setting.enabled;
          }
        });

        // Restore the order from API data
        const savedOrder = object.profileSettings.map((setting) => setting.type);

        // Reorder items based on saved order, keeping any new items at the end
        const reorderedItems: typeof additionalRecordItems = [];
        savedOrder.forEach((type) => {
          const item = defaultItems.find((item) => item.id === type);
          if (item) {
            reorderedItems.push(item);
          }
        });
        // Add any items not in saved order to the end
        defaultItems.forEach((item) => {
          if (!savedOrder.includes(item.id as any)) {
            reorderedItems.push(item);
          }
        });

        setAdditionalRecordItems(reorderedItems);
      }

      // Initialize childObjects
      if (object.childObjects) {
        setChildObjects(object.childObjects);
      }

      reset(formData);
    }
  }, [object, reset, defaultItems]);

  const onSumit = async (data: any) => {
    // Transform form data to API format
    const transformedData = { ...data };

    // Convert profileSettings from form format to array format
    if (data.profileSettings) {
      transformedData.profileSettings = additionalRecordItems.map((item) => ({
        type: item.id,
        enabled: data.profileSettings[item.id] || false,
      }));
    }

    // Add childObjects to the transformed data
    transformedData.childObjects = childObjects;

    let obj: WSObject | undefined;
    if (object?.id) {
      obj = await updateObject(object.id, { ...object, ...transformedData });
    } else {
      obj = await createObject(transformedData);
    }
    mutateObjects();
    obj && onSave?.(obj as WSObject);
  };

  const currIcon = watch('icon');

  const handleMessageDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setMessageItems((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over.id);

        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  const handleAdditionalRecordDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setAdditionalRecordItems((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over.id);

        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  return (
    <form onSubmit={(e) => e.preventDefault()} className={classes.form} data-testid='name-form'>
      <ScrollArea scrollbarSize={rem(6)} type='hover' h={`calc(100% - ${rem(50)})`} px={rem(8)}>
        {/* Object Icon Section */}
        <Text fw={500}>{t('objectIcon')}</Text>
        <Flex gap={rem(12)} mt={rem(10)} mb={rem(20)}>
          {IconList.map((icon) => (
            <ActionIcon
              variant='subtle'
              key={icon.value}
              className={cx(classes.icon, {
                [classes.selected]: icon.value === currIcon,
              })}
              onClick={() => setValue('icon', icon.value)}
            >
              <icon.Icon />
            </ActionIcon>
          ))}
        </Flex>

        {/* Object Name Section */}
        <Text fw={500}>{t('objectName')}</Text>
        <TextInput
          control={control}
          name='name.singular'
          mt={5}
          maw={rem(364)}
          data-testid='name-input'
        />

        <Divider my={rem(20)} />

        {/* Custom Object Section */}
        <Text fw={500} fz={rem(16)}>
          {t('customObject')}
        </Text>

        {/* Record Detail Group */}
        <Text my={rem(20)} fw={500}>
          {t('recordDetail')}
        </Text>
        <Box className={classes.component} data-testid='record-detail-switches'>
          <Stack gap={rem(16)}>
            <Flex align='center' justify='space-between'>
              <Text fz={rem(14)}>{t('avatar')}</Text>
              <HFSwitch control={control} name='hasAvatar' size='sm' />
            </Flex>
            <Flex align='center' justify='space-between'>
              <Text fz={rem(14)}>{t('tags')}</Text>
              <HFSwitch control={control} name='hasTags' size='sm' />
            </Flex>
          </Stack>
        </Box>

        {/* Show Linked Object Section */}
        <Box my={rem(10)}>
          <Flex
            align='center'
            justify='left'
            onClick={() => setLinkedObjectsOpened(!linkedObjectsOpened)}
            style={{ cursor: 'pointer' }}
            gap={rem(4)}
          >
            {linkedObjectsOpened ? (
              <IconChevronUp color={theme.colors.decaBlue[5]} size={20} />
            ) : (
              <IconChevronDown color={theme.colors.decaBlue[5]} size={20} />
            )}
            <Text c={theme.colors.decaBlue[5]} fz={rem(16)} fw={500}>
              {t('showLinkedObject')}
            </Text>
          </Flex>
          <Collapse in={linkedObjectsOpened}>
            <Box p={rem(12)}>
              <Stack gap={rem(12)}>
                {childObjects.map((childObj) => {
                  const obj = objects?.find((o) => o.id === childObj.id);
                  if (!obj) return null;

                  return (
                    <Checkbox
                      key={childObj.id}
                      label={obj.name.singular}
                      checked={childObj.isVisible}
                      color={theme.colors.decaNavy[4]}
                      onChange={(event) => {
                        const newChecked = event.currentTarget.checked;
                        setChildObjects((prev) =>
                          prev.map((child) =>
                            child.id === childObj.id ? { ...child, isVisible: newChecked } : child
                          )
                        );
                      }}
                      sx={{
                        label: {
                          fontSize: rem(14),
                        },
                      }}
                    />
                  );
                })}
              </Stack>
            </Box>
          </Collapse>
        </Box>

        {/* Record Panel Group with Drag & Drop */}
        <Text my={rem(20)} fw={500}>
          {t('recordPanel')}
        </Text>
        <Box className={classes.component} data-testid='additional-record-switches'>
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleAdditionalRecordDragEnd}
          >
            <SortableContext
              items={additionalRecordItems.map((item) => item.id)}
              strategy={verticalListSortingStrategy}
            >
              <Stack gap={rem(16)}>
                {additionalRecordItems.map((item) => (
                  <SortableItem
                    key={item.id}
                    id={item.id}
                    label={item.label}
                    name={item.name}
                    control={control}
                  />
                ))}
              </Stack>
            </SortableContext>
          </DndContext>
        </Box>

        {/* Message Group with Drag & Drop */}
        <Text my={rem(20)} fw={500}>
          {t('message')}
        </Text>
        <Box className={classes.component} data-testid='message-switches'>
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleMessageDragEnd}
          >
            <SortableContext
              items={messageItems.map((item) => item.id)}
              strategy={verticalListSortingStrategy}
            >
              <Stack gap={rem(16)}>
                {messageItems.map((item) => (
                  <SortableItem
                    key={item.id}
                    id={item.id}
                    label={item.label}
                    name={item.name}
                    control={control}
                  />
                ))}
              </Stack>
            </SortableContext>
          </DndContext>
        </Box>
      </ScrollArea>

      {object && <Divider ml={rem(-24)} mr={rem(-24)} my={rem(16)} />}

      <Flex
        justify={'flex-end'}
        gap={rem(16)}
        w={'100%'}
        data-testid='save-cancel-buttons'
        pr={rem(8)}
      >
        <DecaButton variant='neutral' onClick={onCancel} data-testid='cancel-button'>
          {t('cancel')}
        </DecaButton>
        <DecaButton onClick={handleSubmit(onSumit)} data-testid='save-button'>
          {t(object ? 'update' : 'save')}
        </DecaButton>
      </Flex>
    </form>
  );
};
