import { EmptyState } from '@/components/EmptyState/EmptyState';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import {
  useAttachmentDelete,
  useAttachments,
  useClickOutside,
  useDateFilter,
  useInfiniteScroll,
} from '@/hooks';
import type { Attachment } from '@/models/attachment';
import { FileEnhanced } from '@/models/file';
import type { AttachmentCategory } from '@/models/workspace';
import { AssetAPI, AttachmentAPI, ObjectAPI, UploadAPI } from '@/services/api';
import {
  ActionIcon,
  Badge,
  Box,
  Button,
  Center,
  Checkbox,
  Flex,
  Group,
  Loader,
  Menu,
  Popover,
  ScrollArea,
  Stack,
  Text,
  TextInput,
  Title,
  rem,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { type DecaFileUploadProps, DecaTooltip } from '@resola-ai/ui/components';
import { DecaButton } from '@resola-ai/ui/components/DecaButton';
import { showCustomNotification } from '@resola-ai/ui/components/DecaTable/utils';
import {
  IconAlertCircle,
  IconArrowsSort,
  IconFile,
  IconList,
  IconSearch,
  IconX,
} from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import omitBy from 'lodash/omitBy';
import { memo, useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import DateFilterMenu from '../../Common/DateFilterMenu';
import { CategoryManager } from './CategoryManager';
import { FileItem } from './FileItem';
import { FileRemovalModal } from './FileRemovalModal';
import { FileUploader } from './FileUploader';
import { FilesCategoryGroup } from './FilesCategoryGroup';
import { downloadFile, fileEquals, getFileKey } from './utils';

const useStyles = createStyles((theme) => ({
  emptyState: {
    '& img': {
      height: rem(100),
      width: rem(100),
    },
  },
  filterButton: {
    backgroundColor: 'white',
    color: theme.colors.decaGrey[6],
    '&[data-active="true"]': {
      backgroundColor: theme.colors.decaBlue[0],
      borderColor: theme.colors.decaBlue[3],
      color: theme.colors.decaBlue[7],
    },
  },
}));

type SortOption = {
  value: string;
  label: string;
  field: keyof Attachment;
  direction: 'asc' | 'desc';
};

const fileTypeOptions = [
  { value: 'application/pdf', label: 'PDF' },
  {
    value: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    label: 'DOCX',
  },
  { value: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', label: 'XLSX' },
  { value: 'text/plain', label: 'TXT' },
  { value: 'image/jpeg', label: 'JPG' },
  { value: 'image/png', label: 'PNG' },
];

const Files = () => {
  const { wsId: workspaceId = '', id: objectId = '', recordId = '' } = useParams();
  const { classes, theme } = useStyles();
  const { t } = useTranslate('workspace');
  const sortOptions: SortOption[] = useMemo(
    () => [
      { value: 'name-asc', label: t('a-z'), field: 'title', direction: 'asc' },
      { value: 'name-desc', label: t('z-a'), field: 'title', direction: 'desc' },
      { value: 'date-desc', label: t('newest'), field: 'createdAt', direction: 'desc' },
      { value: 'date-asc', label: t('oldest'), field: 'createdAt', direction: 'asc' },
    ],
    [t]
  );
  // Search state with manual debouncing
  const [searchValue, setSearchValue] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [searchOpened, { close: closeSearch, toggle: toggleSearch }] = useDisclosure(false);

  // Refs for custom click-outside detection
  const searchPopoverRef = useRef<HTMLDivElement>(null);
  const searchTargetRef = useRef<HTMLDivElement>(null);

  // Manual debouncing with useEffect
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchValue);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchValue]);

  // Custom click-outside detection for search popover
  useClickOutside({
    targetRef: searchTargetRef,
    dropdownRef: searchPopoverRef,
    onClickOutside: closeSearch,
    enabled: searchOpened,
  });

  // Filter state
  const [selectedFileTypes, setSelectedFileTypes] = useState<string[]>([]);
  const [tempFileTypes, setTempFileTypes] = useState<string[]>([]);
  const [fileTypeMenuOpened, setFileTypeMenuOpened] = useState(false);
  const [selectedSort, setSelectedSort] = useState<SortOption>(sortOptions[2]); // Default to Newest

  // Use shared date filter hook
  const {
    datePresets,
    dateRangeForApi,
    getDateRangeLabel,
    handleDatePresetChange,
    selectedDatePreset,
    tempDate,
    tempDateRange,
    setTempDate,
    setTempDateRange,
    setSelectedDatePreset,
    setSingleDate,
    setCustomDateRange,
    resetDateFilter,
  } = useDateFilter();

  // Build sort object for API
  const sortConfig = useMemo(
    () => ({
      [selectedSort.field]: selectedSort.direction,
    }),
    [selectedSort]
  );

  const {
    data = [],
    totalRecordsCount,
    error,
    isLoading: isLoadingAttachments,
    isLoadingMore,
    hasMore,
    loadMore,
    mutate: refetchAttachments,
  } = useAttachments({
    objectId,
    recordId,
    workspaceId,
    sort: sortConfig,
    searchTerm: debouncedSearchTerm.trim() || undefined,
    fileTypes: selectedFileTypes.length > 0 ? selectedFileTypes : undefined,
    dateRange: dateRangeForApi,
  });

  // Infinite scroll hook
  const { containerRef, sentinelRef } = useInfiniteScroll({
    hasMore,
    isLoading: isLoadingMore || false,
    loadMore,
    threshold: 200,
  });

  const { object, mutateObject } = useWorkspaceContext();

  // File deletion state
  const [modalOpen, { close: closeModal, open: openModal }] = useDisclosure(false);
  const [removingAttachment, setRemovingAttachment] = useState({ id: '', name: '' });
  const { isMutating: isDeletingAttachment, trigger: triggerDeleteAttachment } =
    useAttachmentDelete({
      attachmentId: removingAttachment.id,
      objectId,
      recordId,
      workspaceId,
    });

  // File upload state
  const [uploadingFiles, setUploadingFiles] = useState<FileEnhanced[]>([]);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});

  // Category management state
  const [categoryModalOpened, { close: closeCategoryModal, open: openCategoryModal }] =
    useDisclosure(false);
  const [isSavingCategories, setIsSavingCategories] = useState(false);

  // Helper functions
  const handleFileTypeChange = (fileType: string, checked: boolean) => {
    if (checked) {
      setTempFileTypes([...tempFileTypes, fileType]);
    } else {
      setTempFileTypes(tempFileTypes.filter((type) => type !== fileType));
    }
  };

  const handleFileTypeMenuOpen = () => {
    setTempFileTypes([...selectedFileTypes]); // Initialize temp state with current selection
    setFileTypeMenuOpened(true);
  };

  const handleFileTypeMenuClose = () => {
    setSelectedFileTypes([...tempFileTypes]); // Apply the temporary selection
    setFileTypeMenuOpened(false);
  };

  // Clear all filters and search
  const clearAllFilters = () => {
    setSearchValue('');
    setDebouncedSearchTerm('');
    setSelectedFileTypes([]);
    setTempFileTypes([]);
    resetDateFilter();
    setSelectedSort(sortOptions[2]); // Reset to default (Newest)
    closeSearch();
  };

  // File deletion handlers
  const handleDeleteClick = (id: string, name: string) => {
    setRemovingAttachment({ id, name });
    openModal();
  };

  const deleteAttachment = async () => {
    await triggerDeleteAttachment();
    setRemovingAttachment({ id: '', name: '' });
    closeModal();
    showCustomNotification(t('fileDeleted'));
    await refetchAttachments();
  };

  const handleDownload = async (id: string) => {
    const attachment = await AttachmentAPI.get({
      workspaceId,
      objectId,
      recordId,
      attachmentId: id,
    });
    if (attachment) {
      downloadFile(attachment.url);
    }
  };

  // Category change handler
  const handleChangeCategoryClick = async (attachmentId: string, categoryId: string) => {
    try {
      await AttachmentAPI.update({
        workspaceId,
        objectId,
        recordId,
        attachmentId,
        categoryId: categoryId ?? ' ', // Using a space instead of empty string
      });
      await refetchAttachments();
    } catch (err) {
      console.error(err);
    }
  };

  // Save category changes
  const handleSaveCategories = async (categories: AttachmentCategory[]) => {
    if (!object) return;

    setIsSavingCategories(true);
    try {
      // Create updated object with new categories
      const updatedObject = {
        ...object,
        attachmentCategories: categories,
      };

      // Use ObjectAPI.update instead of mutateObject
      await ObjectAPI.update(workspaceId, updatedObject);

      // Refresh the object data after update
      if (mutateObject) {
        await mutateObject({ ...updatedObject }, false);
      }

      closeCategoryModal();
    } catch (error) {
      console.error('Failed to save categories:', error);
    } finally {
      setIsSavingCategories(false);
    }
  };

  // File upload handlers
  const handleDrop: DecaFileUploadProps['onDrop'] = async ([file]) => {
    // Check file size limit (25MB)
    const maxFileSize = 25 * 1024 * 1024; // 25MB in bytes
    if (file.size > maxFileSize) {
      showCustomNotification(
        t('fileSizeExceeded'),
        <IconAlertCircle size={16} />,
        theme.colors.decaRed[0],
        theme.colors.decaRed[6]
      );
      return;
    }

    const fileExists = uploadingFiles.find((f) => fileEquals(f, file));

    if (fileExists && fileExists.status === (file as FileEnhanced).status) return;

    const controller = new AbortController();
    const uploadingFile = new FileEnhanced([file], file.name, {
      controller,
      lastModified: file.lastModified,
      type: file.type,
    });

    setUploadingFiles((prevState) => [uploadingFile, ...prevState]);

    try {
      const { file: asset, uploadUrl } = await AssetAPI.save(file, 'attachment');

      await UploadAPI.update({
        file,
        url: uploadUrl,
        config: {
          signal: controller.signal,
          onUploadProgress: (event) => {
            setUploadProgress((prevState) => ({
              ...prevState,
              [getFileKey(file)]: event.lengthComputable ? event.progress! * 100 : 0,
            }));
          },
        },
      });

      await AttachmentAPI.save(
        { workspaceId, objectId, recordId },
        {
          assetId: asset.externalId,
          mimeType: asset.mimeType,
          path: asset.path,
          size: asset.size,
          title: file.name,
          url: asset.url,
          assetType: 'attachment',
        }
      );

      setUploadingFiles((prevState) => prevState.filter((f) => !fileEquals(f, file)));
      await refetchAttachments();
    } catch (err) {
      console.error('Error while marking file as upload failed:', err);

      setUploadingFiles((prevState) =>
        prevState.map((file) =>
          file === uploadingFile
            ? new FileEnhanced([uploadingFile], uploadingFile.name, {
                controller: uploadingFile.controller,
                lastModified: uploadingFile.lastModified,
                status: 'fileStatus.uploadFailed',
                type: uploadingFile.type,
              })
            : file
        )
      );
    } finally {
      setUploadProgress((prevState) => omitBy(prevState, (_, key) => key === getFileKey(file)));
    }
  };

  const handleRetryUploadingFile = (file: FileEnhanced) => {
    setUploadingFiles((prevState) => prevState.filter((f) => !fileEquals(f, file)));
    setUploadProgress((prevState) => omitBy(prevState, (_, key) => key === getFileKey(file)));
    handleDrop([
      new FileEnhanced([file], file.name, {
        controller: file.controller,
        lastModified: file.lastModified,
        type: file.type,
      }),
    ]);
  };

  // Show loading or error state
  if (error) return <Box>{error.toString()}</Box>;
  if (isLoadingAttachments)
    return (
      <Center maw='100%' h='100%' mx='auto' data-testid='loading-indicator'>
        <Loader />
      </Center>
    );

  // Organize files by category and subcategory
  const categorizedFiles: Record<string, Attachment[]> = {};
  const subcategorizedFiles: Record<string, Record<string, Attachment[]>> = {};
  const uncategorizedFiles: Attachment[] = [];
  const categories = object?.attachmentCategories ?? [];

  if (categories.length > 0) {
    categories.forEach((category) => {
      categorizedFiles[category.id] = [];
      subcategorizedFiles[category.id] = {};

      if (category.subcategories) {
        category.subcategories.forEach((subcategory) => {
          subcategorizedFiles[category.id][subcategory.id] = [];
        });
      }
    });
  }

  data.forEach((file) => {
    if (file.categoryId) {
      let isSubcategory = false;
      let parentCategoryId = '';

      for (const category of categories) {
        if (category.subcategories) {
          const subcategory = category.subcategories.find((sub) => sub.id === file.categoryId);
          if (subcategory) {
            isSubcategory = true;
            parentCategoryId = category.id;
            break;
          }
        }
      }

      if (
        isSubcategory &&
        subcategorizedFiles[parentCategoryId] &&
        subcategorizedFiles[parentCategoryId][file.categoryId]
      ) {
        subcategorizedFiles[parentCategoryId][file.categoryId].push(file);
      } else if (categorizedFiles[file.categoryId]) {
        categorizedFiles[file.categoryId].push(file);
      } else {
        uncategorizedFiles.push(file);
      }
    } else {
      uncategorizedFiles.push(file);
    }
  });

  return (
    <>
      <Flex direction='column' gap={rem(18)} data-testid='files-container'>
        {/* Header */}
        <Flex justify='space-between' align='center'>
          <Title order={5} fw={500}>
            {t('files')}
          </Title>
          <DecaTooltip label={t('customCategories')}>
            <ActionIcon variant='subtle' radius='xl' onClick={openCategoryModal} color='decaGrey.4'>
              <IconList size={20} />
            </ActionIcon>
          </DecaTooltip>
        </Flex>

        {/* Filter Bar */}
        <Group gap={rem(16)} align='flex-start' justify='space-between'>
          <Group gap={rem(16)} align='flex-start'>
            {/* File Type Filter */}
            <Menu
              position='bottom-start'
              closeOnClickOutside={true}
              opened={fileTypeMenuOpened}
              onOpen={handleFileTypeMenuOpen}
              onClose={handleFileTypeMenuClose}
            >
              <Menu.Target>
                <Button
                  variant='subtle'
                  className={classes.filterButton}
                  data-active={selectedFileTypes.length > 0}
                  leftSection={<IconFile size={16} />}
                  rightSection={
                    selectedFileTypes.length > 0 ? (
                      <ActionIcon
                        variant='transparent'
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedFileTypes([]);
                          setTempFileTypes([]);
                        }}
                      >
                        <IconX size={16} />
                      </ActionIcon>
                    ) : undefined
                  }
                >
                  {t('fileType')}
                  {selectedFileTypes.length > 0 && (
                    <Badge variant='light' color='decaBlue.8' circle ml={rem(4)}>
                      {selectedFileTypes.length}
                    </Badge>
                  )}
                </Button>
              </Menu.Target>
              <Menu.Dropdown>
                <Stack gap={rem(8)} p={rem(8)}>
                  {fileTypeOptions.map((option) => (
                    <Checkbox
                      key={option.value}
                      label={option.label}
                      checked={tempFileTypes.includes(option.value)}
                      onChange={(event) =>
                        handleFileTypeChange(option.value, event.currentTarget.checked)
                      }
                    />
                  ))}
                </Stack>
              </Menu.Dropdown>
            </Menu>

            {/* Upload Date Filter */}
            <DateFilterMenu
              datePresets={datePresets}
              selectedDatePreset={selectedDatePreset}
              getDateRangeLabel={getDateRangeLabel}
              handleDatePresetChange={handleDatePresetChange}
              tempDate={tempDate}
              tempDateRange={tempDateRange}
              setTempDate={setTempDate}
              setTempDateRange={setTempDateRange}
              setSingleDate={setSingleDate}
              setCustomDateRange={setCustomDateRange}
              setSelectedDatePreset={setSelectedDatePreset}
              className={classes.filterButton}
              dateLabel='uploadDate'
            />

            {/* Sort Filter */}
            <Menu position='bottom-start'>
              <Menu.Target>
                <Button
                  variant='subtle'
                  className={classes.filterButton}
                  leftSection={<IconArrowsSort size={16} />}
                  data-active={selectedSort.value !== sortOptions[2].value}
                  rightSection={
                    selectedSort.value !== sortOptions[2].value ? (
                      <ActionIcon
                        variant='transparent'
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedSort(sortOptions[2]);
                        }}
                      >
                        <IconX size={16} />
                      </ActionIcon>
                    ) : undefined
                  }
                >
                  {selectedSort.label || t('sort')}
                </Button>
              </Menu.Target>
              <Menu.Dropdown>
                <Stack gap={rem(4)} p={rem(4)}>
                  {sortOptions.map((option) => (
                    <Menu.Item
                      key={option.value}
                      onClick={() => setSelectedSort(option)}
                      bg={selectedSort.value === option.value ? 'blue.0' : undefined}
                    >
                      {option.label}
                    </Menu.Item>
                  ))}
                </Stack>
              </Menu.Dropdown>
            </Menu>
          </Group>

          {/* Search Popover */}
          <Popover
            opened={searchOpened}
            onClose={closeSearch}
            position='bottom-start'
            width={rem(300)}
            shadow='md'
            radius={rem(8)}
            styles={{
              dropdown: {
                padding: rem(10),
              },
            }}
            withinPortal
          >
            <Popover.Target ref={searchTargetRef}>
              <ActionIcon
                variant='subtle'
                size='md'
                radius='md'
                className={classes.filterButton}
                data-active={searchValue.trim() !== ''}
                onClick={toggleSearch}
              >
                <IconSearch size={16} />
              </ActionIcon>
            </Popover.Target>
            <Popover.Dropdown ref={searchPopoverRef}>
              <Flex direction='column' gap={rem(8)}>
                <Text c='decaGrey.3' fw={400} fz={rem(12)}>
                  {t('search')}
                </Text>
                <TextInput
                  placeholder={t('typeToSearch')}
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.currentTarget.value)}
                  leftSection={<IconSearch size={16} />}
                  autoFocus
                />
              </Flex>
            </Popover.Dropdown>
          </Popover>
        </Group>

        {/* Results Count - Show when filters or search are active */}
        {(debouncedSearchTerm.trim() !== '' ||
          selectedFileTypes.length > 0 ||
          dateRangeForApi?.start ||
          selectedSort.value !== sortOptions[2].value) && (
          <Flex justify='space-between' align='center'>
            <Text fz={rem(14)} c='decaGrey.6' fw={500}>
              {`${totalRecordsCount} ${t('resultsFound')}`}
            </Text>
            <Button
              variant='subtle'
              size='sm'
              fz={rem(14)}
              c='decaGrey.6'
              onClick={clearAllFilters}
              radius={rem(4)}
              px={rem(8)}
              py={rem(4)}
              bd={`1px solid ${theme.colors.decaLight[5]}`}
            >
              {t('clear')}
            </Button>
          </Flex>
        )}

        {/* File Uploader */}
        <FileUploader
          onDrop={handleDrop}
          uploadingFiles={uploadingFiles}
          uploadProgress={uploadProgress}
          onRetryUploadingFile={handleRetryUploadingFile}
        />

        {/* Files Container with Infinite Scroll */}
        <ScrollArea.Autosize ref={containerRef} mah='65vh'>
          {/* Overall empty state when no files at all */}
          {data.length === 0 &&
          !isLoadingAttachments &&
          (debouncedSearchTerm.trim() !== '' ||
            selectedFileTypes.length > 0 ||
            dateRangeForApi?.start) ? (
            <Box mt={rem(32)} bg='white' pb={rem(16)}>
              <EmptyState
                imageUrl='images/no_search_result.png'
                message={t('pleaseAdjustSearchFilter')}
                title={t('noResultFound')}
              />
              <Center mt={rem(20)}>
                <DecaButton variant='neutral' onClick={clearAllFilters} color='decaGrey.6'>
                  <Text c='decaGrey.6' fw={500} fz={rem(14)}>
                    {t('clear')}
                  </Text>
                </DecaButton>
              </Center>
            </Box>
          ) : (
            <Box pr={rem(12)}>
              {/* Uncategorized Files */}
              {uncategorizedFiles.length > 0 && (
                <Flex direction='column' gap={rem(16)} data-testid='uncategorized-files'>
                  {uncategorizedFiles.map((file) => (
                    <FileItem
                      key={file.id}
                      file={file}
                      onDelete={handleDeleteClick}
                      onDownload={handleDownload}
                      categories={categories}
                      onChangeCategory={handleChangeCategoryClick}
                    />
                  ))}
                </Flex>
              )}

              {/* Categorized Files */}
              {Object.entries(categorizedFiles).map(([categoryId, files]) => {
                const category = categories.find((cat) => cat.id === categoryId);
                if (!category) return null;

                return (
                  <FilesCategoryGroup
                    key={categoryId}
                    category={category}
                    files={files}
                    subcategorizedFiles={subcategorizedFiles[categoryId]}
                    onDeleteFile={handleDeleteClick}
                    onDownloadFile={handleDownload}
                    categories={categories}
                    onChangeCategory={handleChangeCategoryClick}
                    className={classes.emptyState}
                  />
                );
              })}

              {/* Loading indicator at bottom */}
              {isLoadingMore && (
                <Center py={rem(20)}>
                  <Loader size='sm' />
                </Center>
              )}

              {/* Invisible sentinel element for intersection observer */}
              <div ref={sentinelRef} style={{ height: '1px' }} />
            </Box>
          )}
        </ScrollArea.Autosize>
      </Flex>

      {/* File Removal Modal */}
      <FileRemovalModal
        opened={modalOpen}
        onClose={closeModal}
        fileName={removingAttachment.name}
        isDeleting={isDeletingAttachment}
        onDelete={deleteAttachment}
      />

      {/* Category Manager Modal */}
      <CategoryManager
        opened={categoryModalOpened}
        onClose={closeCategoryModal}
        categories={categories}
        onSave={handleSaveCategories}
        isSaving={isSavingCategories}
      />
    </>
  );
};

export default memo(Files);
