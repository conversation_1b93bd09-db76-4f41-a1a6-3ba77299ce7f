import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { useBreadcrumbNavigation } from '@/hooks/useBreadcrumbNavigation';
import type { ITag } from '@/models';
import { ObjectAPI } from '@/services/api';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { FieldTypes } from '@resola-ai/ui/components';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import Forms from './index';

// Mock dependencies
vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: vi.fn(),
}));

vi.mock('@/hooks/useBreadcrumbNavigation', () => ({
  useBreadcrumbNavigation: vi.fn(),
}));

vi.mock('@/contexts/ProfileContext', () => ({
  useProfileContext: vi.fn(() => ({
    setCustomObjectFields: vi.fn(),
  })),
  ProfileContextProvider: ({ children }: any) => children,
}));

vi.mock('@/contexts/AppContext', () => ({
  useAppContext: vi.fn(() => ({
    isManager: true,
  })),
  AppContextProvider: ({ children }: any) => children,
}));

vi.mock('@/services/api', () => ({
  ObjectAPI: {
    update: vi.fn(),
  },
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: (key: string) => key }),
  Tolgee: vi.fn(() => ({
    use: vi.fn().mockReturnThis(),
    init: vi.fn().mockReturnThis(),
    run: vi.fn(),
  })),
  FormatSimple: vi.fn(() => ({
    use: vi.fn().mockReturnThis(),
  })),
  InContextTools: vi.fn(() => ({
    use: vi.fn().mockReturnThis(),
  })),
}));

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: () => ({ wsId: 'test-workspace' }),
  };
});

describe('Forms Component', () => {
  const mockNavigateToLinkedRecord = vi.fn();
  const mockRefetchObject = vi.fn();
  const mockObject = {
    id: 'test-object',
    childObjects: [
      { id: 'linked-obj-1', pinned: false, isVisible: true }, // This should match the linkedObj.id
      { id: 'child-2', pinned: true, isVisible: true },
    ],
    profileSettings: [{ type: 'pinCustomObject', enabled: true }],
  };

  const mockLinkedObj = {
    id: 'linked-obj-1',
    name: { singular: 'Contact', plural: 'Contacts' },
    fields: [
      { id: 'field-1', name: 'firstName', type: FieldTypes.SINGLE_LINE_TEXT, header: 'First Name' },
      { id: 'field-2', name: 'email', type: FieldTypes.EMAIL, header: 'Email' },
      { id: 'field-3', name: 'createdAt', type: FieldTypes.CREATED_TIME, header: 'Created At' },
      {
        id: 'field-4',
        name: 'status',
        type: FieldTypes.SINGLE_SELECT,
        header: 'Status',
        options: {
          choices: [
            { id: 'active', label: 'Active' },
            { id: 'inactive', label: 'Inactive' },
          ],
        },
      },
    ],
    userconfig: { viewId: 'default-view' },
    permission: { 'crm:object:read': 'allow', 'crm:object:update': 'allow' } as const,
  };

  const mockForm = {
    linkedObj: mockLinkedObj,
    records: [
      {
        id: 'record-1',
        objectId: 'linked-obj-1',
        firstName: 'John',
        email: '<EMAIL>',
        status: 'active',
        createdAt: '2023-01-01T10:00:00Z',
      },
    ],
  };

  beforeEach(() => {
    vi.clearAllMocks();

    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      object: mockObject,
      refetchObject: mockRefetchObject,
    });

    (useBreadcrumbNavigation as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      navigateToLinkedRecord: mockNavigateToLinkedRecord,
    });

    (ObjectAPI.update as ReturnType<typeof vi.fn>).mockResolvedValue({});
  });

  it('should be defined', () => {
    expect(Forms).toBeDefined();
    // Forms component is wrapped with React.memo so it's an object
    expect(typeof Forms).toBe('object');
  });

  it('should render loader when no form is provided', () => {
    const { container } = renderWithMantine(
      <MemoryRouter>
        <Forms form={null} />
      </MemoryRouter>
    );

    // Check for the Loader component by class name since it doesn't have progressbar role
    const loader = container.querySelector('.mantine-Loader-root');
    expect(loader).toBeInTheDocument();
  });

  it('should render nothing when form has no records', () => {
    const formWithoutRecords = { ...mockForm, records: [] };

    const { container } = renderWithMantine(
      <MemoryRouter>
        <Forms form={formWithoutRecords} />
      </MemoryRouter>
    );

    // The component renders a fragment with conditional content
    expect(container.firstChild).toBeInTheDocument();
  });

  it('should render form title correctly', () => {
    renderWithMantine(
      <MemoryRouter>
        <Forms form={mockForm} />
      </MemoryRouter>
    );

    expect(screen.getByText('Contact')).toBeInTheDocument();
  });

  it('should render pin button when pin is enabled and not pinned', () => {
    const { container } = renderWithMantine(
      <MemoryRouter>
        <Forms form={mockForm} />
      </MemoryRouter>
    );

    // Look for pin button by the icon class
    const pinIcon = container.querySelector('.tabler-icon-pin');
    expect(pinIcon).toBeInTheDocument();
  });

  it('should not render pin button when already pinned', () => {
    const pinnedForm = {
      ...mockForm,
      linkedObj: { ...mockLinkedObj, id: 'child-2' },
    };

    const { container } = renderWithMantine(
      <MemoryRouter>
        <Forms form={pinnedForm} />
      </MemoryRouter>
    );

    // Should not have pin button since this object is already pinned
    const pinIcon = container.querySelector('.tabler-icon-pin');
    expect(pinIcon).not.toBeInTheDocument();
  });

  it('should handle pin toggle correctly', async () => {
    const user = userEvent.setup();

    const { container } = renderWithMantine(
      <MemoryRouter>
        <Forms form={mockForm} />
      </MemoryRouter>
    );

    const pinIcon = container.querySelector('.tabler-icon-pin');
    expect(pinIcon).toBeInTheDocument();

    const pinButton = pinIcon?.closest('button');
    expect(pinButton).toBeInTheDocument();

    await user.click(pinButton!);

    // The component should update the matching child object to be pinned
    expect(ObjectAPI.update).toHaveBeenCalledWith('test-workspace', {
      ...mockObject,
      childObjects: [
        { id: 'linked-obj-1', pinned: true, isVisible: true }, // This should be updated to pinned: true
        { id: 'child-2', pinned: true, isVisible: true },
      ],
    });
    expect(mockRefetchObject).toHaveBeenCalled();
  });

  it('should render accordion with FieldRender', () => {
    renderWithMantine(
      <MemoryRouter>
        <Forms form={mockForm} />
      </MemoryRouter>
    );

    expect(screen.getByText('Contact - record-1')).toBeInTheDocument();
    expect(screen.getByText('editObject')).toBeInTheDocument();
  });

  it('should render field values correctly with FieldRender', () => {
    renderWithMantine(
      <MemoryRouter>
        <Forms form={mockForm} />
      </MemoryRouter>
    );

    // Check that the field values are rendered by FieldRender component
    expect(screen.getByText('John')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('should render datetime fields through FieldRender', () => {
    renderWithMantine(
      <MemoryRouter>
        <Forms form={mockForm} />
      </MemoryRouter>
    );

    // The date formatting is now handled by FieldRender component
    // Since FieldRender uses specific date formatting, let's check for field presence instead
    // The date should be rendered by FieldRender as part of the accordion
    expect(screen.getByText('Contact')).toBeInTheDocument();
  });

  it('should handle field rendering through FieldRender component', () => {
    renderWithMantine(
      <MemoryRouter>
        <Forms form={mockForm} />
      </MemoryRouter>
    );

    // Field values are now rendered by FieldRender component
    expect(screen.getByText('Contact - record-1')).toBeInTheDocument();
    expect(screen.getByText('editObject')).toBeInTheDocument();
  });

  it('should use FieldRender for all field types', () => {
    const formWithMultipleFields = {
      ...mockForm,
      linkedObj: {
        ...mockLinkedObj,
        fields: [
          ...mockLinkedObj.fields,
          {
            id: 'field-5',
            name: 'tags',
            type: FieldTypes.MULTI_SELECT,
            header: 'Tags',
            options: {
              choices: [
                { id: 'tag1', label: 'Tag 1' },
                { id: 'tag2', label: 'Tag 2' },
              ],
            },
          },
        ],
      },
      records: [
        {
          ...mockForm.records[0],
          tags: [
            { id: 'tag1', name: 'Tag 1', value: 'tag1', color: '#blue' },
            { id: 'tag2', name: 'Tag 2', value: 'tag2', color: '#green' }
          ] as ITag[],
        },
      ],
    };

    renderWithMantine(
      <MemoryRouter>
        <Forms form={formWithMultipleFields} />
      </MemoryRouter>
    );

    // FieldRender component handles all field type rendering
    expect(screen.getByText('Contact - record-1')).toBeInTheDocument();
  });

  it('should handle edit object click', async () => {
    const user = userEvent.setup();

    renderWithMantine(
      <MemoryRouter>
        <Forms form={mockForm} />
      </MemoryRouter>
    );

    const editButton = screen.getByText('editObject');
    await user.click(editButton);

    expect(mockNavigateToLinkedRecord).toHaveBeenCalledWith(
      'linked-obj-1',
      'record-1',
      mockForm.records[0]
    );
  });

  it('should integrate with FieldRender for complex field types', () => {
    const formWithComplexFields = {
      ...mockForm,
      linkedObj: {
        ...mockLinkedObj,
        fields: [
          ...mockLinkedObj.fields,
          { id: 'field-6', name: 'company', type: FieldTypes.RELATIONSHIP, header: 'Company' },
        ],
      },
      records: [
        {
          ...mockForm.records[0],
          company: { name: 'Test Company' },
        },
      ],
    };

    renderWithMantine(
      <MemoryRouter>
        <Forms form={formWithComplexFields} />
      </MemoryRouter>
    );

    // FieldRender handles relationship field rendering
    expect(screen.getByText('Contact - record-1')).toBeInTheDocument();
  });

  it('should display ViewAllModal functionality', () => {
    renderWithMantine(
      <MemoryRouter>
        <Forms form={mockForm} />
      </MemoryRouter>
    );

    // Check that the ViewAllModal component is rendered in the DOM
    // Note: Since ViewAllModal is now integrated into FieldRender, the test ID has changed
    const modal = screen.getByTestId('field-render-view-all-modal-test-id');
    expect(modal).toBeInTheDocument();
  });

  it('should handle pin functionality when pin is disabled', () => {
    const mockObjectWithoutPin = {
      ...mockObject,
      profileSettings: [{ type: 'pinCustomObject', enabled: false }],
    };

    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      object: mockObjectWithoutPin,
      refetchObject: mockRefetchObject,
    });

    const { container } = renderWithMantine(
      <MemoryRouter>
        <Forms form={mockForm} />
      </MemoryRouter>
    );

    const pinIcon = container.querySelector('.tabler-icon-pin');
    expect(pinIcon).not.toBeInTheDocument();
  });

  it('should handle error in pin toggle', async () => {
    const user = userEvent.setup();
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    (ObjectAPI.update as ReturnType<typeof vi.fn>).mockRejectedValue(new Error('Update failed'));

    const { container } = renderWithMantine(
      <MemoryRouter>
        <Forms form={mockForm} />
      </MemoryRouter>
    );

    const pinIcon = container.querySelector('.tabler-icon-pin');
    const pinButton = pinIcon?.closest('button');

    await user.click(pinButton!);

    expect(consoleSpy).toHaveBeenCalledWith('Failed to update pin status:', expect.any(Error));
    consoleSpy.mockRestore();
  });
});
