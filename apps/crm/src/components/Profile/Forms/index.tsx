import { ColumnIcon } from '@/constants/workspace';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { useBreadcrumbNavigation } from '@/hooks/useBreadcrumbNavigation';
import type { Form, Record } from '@/models';
import { ObjectAPI } from '@/services/api';
import {
  Accordion,
  ActionIcon,
  Box,
  Flex,
  Loader,
  ScrollArea,
  Stack,
  Text,
  rem,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import type { FieldType } from '@resola-ai/ui/components';
import { FieldTypes } from '@resola-ai/ui/components';
import { IconPin } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import React, { useCallback } from 'react';
import { useParams } from 'react-router-dom';
import FieldRender from '../ObjectFields/FieldRender';
import type { ProfileData } from '../ObjectFields/RenderProfileTypes';

const useStyles = createStyles((theme) => ({
  root: {
    border: 'none',
  },

  item: {
    border: 'none',
    padding: `${rem(10)} ${rem(0)}`,
    '&[data-active]': {
      zIndex: 1,
    },
  },
  control: {
    width: 'fit-content',
    '.mantine-Accordion-label': {
      padding: `${rem(4)} ${rem(12)}`,
      backgroundColor: theme.colors.decaYellow[1],
      borderRadius: rem(4),
      fontSize: rem(16),
    },
    '&:hover': {
      backgroundColor: 'unset',
    },
  },
  panel: {
    padding: `0 ${rem(5)}`,
  },
  content: {
    padding: `${rem(5)} ${rem(8)} `,
  },
  chevron: {
    transform: 'rotate(-90deg)',
    '&[data-rotate]': {
      transform: 'rotate(0deg)',
    },
  },
  header: {
    wordBreak: 'break-word',
    '.go-to-object': {
      visibility: 'hidden',
      borderRadius: 4,
      whiteSpace: 'nowrap',
      cursor: 'pointer',
    },
    '&:hover': {
      '.go-to-object': {
        visibility: 'visible',
      },
    },
  },
}));
const Forms = ({ form }: { form: Form | null }) => {
  const { classes } = useStyles();
  const { t } = useTranslate('workspace');
  const linkedObj = form?.linkedObj;
  const { navigateToLinkedRecord } = useBreadcrumbNavigation();
  const { object, refetchObject } = useWorkspaceContext();
  const { wsId } = useParams<{ wsId: string }>();

  // Check if this form's linked object is visible and pinned
  const childObject = object?.childObjects?.find((childObj) => childObj.id === linkedObj?.id);
  const isVisible = childObject?.isVisible || false;
  const isPinned = childObject?.pinned || false;

  // Check if pinCustomObject is enabled in profile settings
  const isPinCustomObjectEnabled = object?.profileSettings?.find(
    (setting) => setting.type === 'pinCustomObject' && setting.enabled
  );

  const handleTogglePin = useCallback(async () => {
    if (!object || !linkedObj?.id || !wsId) return;

    try {
      // Update the childObjects array
      const updatedChildObjects =
        object.childObjects?.map((childObj) => {
          if (childObj.id === linkedObj.id) {
            return { ...childObj, pinned: true };
          }
          return childObj;
        }) || [];

      // Update the object with the modified childObjects
      await ObjectAPI.update(wsId, {
        ...object,
        childObjects: updatedChildObjects,
      });

      // Refetch the object to get the updated data
      refetchObject();
    } catch (error) {
      console.error('Failed to update pin status:', error);
    }
  }, [object, linkedObj?.id, refetchObject, wsId]);

  const goToObjectRecord = useCallback(
    (objectId: string, recordId: string, record?: any) => {
      navigateToLinkedRecord(objectId, recordId, record);
    },
    [navigateToLinkedRecord]
  );

  // Transform form record into ProfileData format for FieldRender
  const createProfileDataForRecord = useCallback(
    (record: Record, recordIndex: number): ProfileData[] => {
      const fields: ProfileData[] = [];

      if (linkedObj?.fields) {
        linkedObj.fields.forEach((fieldDef) => {
          // Skip system fields
          if (
            fieldDef.id === 'id' ||
            fieldDef.id === 'objectId' ||
            fieldDef.id === 'createdAt' ||
            fieldDef.id === 'updatedAt'
          ) {
            return;
          }

          // Create a unique field ID that includes the record index
          const uniqueFieldId = `${linkedObj.id}-${fieldDef.id}-${recordIndex}`;

          // Get the value from the record data
          let fieldValue = record[fieldDef.id!] || record[fieldDef.name!] || '';

          // Handle date range fields - construct array with start and end values
          if (
            fieldDef.options?.dateRange &&
            [FieldTypes.DATETIME, FieldTypes.CREATED_TIME, FieldTypes.MODIFIED_TIME].includes(
              fieldDef.type as any
            )
          ) {
            const startValue = record[fieldDef.id!] || record[fieldDef.name!];
            const endValue = record[`${fieldDef.id!}_end`] || record[`${fieldDef.name!}_end`];
            fieldValue = [startValue, endValue] as any;
          }

          fields.push({
            id: uniqueFieldId,
            name: fieldDef.name,
            type: fieldDef.type,
            options: fieldDef.options,
            mapValue: fieldValue,
            icon: ColumnIcon[fieldDef.type as FieldType] || ColumnIcon[FieldTypes.SINGLE_LINE_TEXT],
            isDetailVisible: true,
          } as ProfileData);
        });
      }

      return fields;
    },
    [linkedObj?.fields, linkedObj?.id]
  );

  const renderRecordWithFieldRender = useCallback(
    (record: Record, recordIndex: number) => {
      if (!form?.records) return <></>;

      // Create profile data for this specific record
      const profileData = createProfileDataForRecord(record, recordIndex);

      return (
        <Accordion.Item key={record.id} value={`${form.linkedObj?.name.singular} - ${record.id}`}>
          <Flex fz={rem(14)} fw={500} className={classes.header}>
            <Accordion.Control>{`${form.linkedObj?.name.singular} - ${record.id}`}</Accordion.Control>
            <Box
              px={rem(8)}
              py={rem(2)}
              c='decaBlue.5'
              bg={'decaBlue.0'}
              h={'fit-content'}
              className='go-to-object'
              onClick={() => {
                goToObjectRecord(record.objectId, record.id, record);
              }}
            >
              {t('editObject')}
            </Box>
          </Flex>
          <Accordion.Panel>
            <Box pl={rem(16)}>
            <FieldRender
              fields={profileData}
              hideShowFieldToggle={false}
              allowEdit={false}
            />
            </Box>
          </Accordion.Panel>
        </Accordion.Item>
      );
    },
    [form?.records, createProfileDataForRecord, form?.linkedObj?.name?.singular]
  );

  if (!form) return <Loader />;
  
  // Don't render if not visible
  if (!isVisible) {
    return null;
  }

  return (
    <>
      {(form.records?.length ?? 0) > 0 && (
        <Box>
          <Stack gap='xs' h='100%'>
            <ScrollArea type='hover'>
              <Box>
                <Box>
                  <Flex align='center' gap={rem(8)} mb={rem(8)}>
                    <Text fz={rem(18)} fw={500}>
                      {form?.linkedObj?.name.singular ?? form?.linkedObj?.name.plural ?? ''}
                    </Text>
                    {isPinCustomObjectEnabled && !isPinned && (
                      <ActionIcon variant='subtle' size='sm' onClick={handleTogglePin} color='gray'>
                        <IconPin size={16} />
                      </ActionIcon>
                    )}
                  </Flex>
                  <Accordion classNames={classes} className={classes.root} chevronPosition='left'>
                    {form.records?.map((record, recordIndex) =>
                      renderRecordWithFieldRender(record, recordIndex)
                    )}
                  </Accordion>
                </Box>
              </Box>
            </ScrollArea>
          </Stack>
        </Box>
      )}
    </>
  );
};

export default React.memo(Forms);
