import { IgnoreActivityActorType, type MessageType } from '@/constants/workspace';
import { useActivities, useClickOutside, useDateFilter, useInfiniteScroll } from '@/hooks';
import ActivityEmailSms from '@/templates/activitiesEmail';
import { syncContextData } from '@/utils';
import {
  ActionIcon,
  Badge,
  Box,
  Button,
  Center,
  Checkbox,
  Flex,
  Group,
  Loader,
  Menu,
  Popover,
  ScrollArea,
  Stack,
  Text,
  TextInput,
  Timeline,
  rem,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { DecaButton } from '@resola-ai/ui/components/DecaButton';
import {
  IconArrowsSort,
  IconFileDescription,
  IconPlus,
  IconSearch,
  IconX,
} from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import DOMPurify from 'dompurify';
import React, { useState, useEffect, useMemo, useRef } from 'react';
import DateFilterMenu from '../../Common/DateFilterMenu';
import { EmptyState } from '../../EmptyState/EmptyState';
import { ActivityModal } from './ActivityModal';

const useStyles = createStyles((theme) => ({
  filterButton: {
    backgroundColor: 'white',
    color: theme.colors.decaGrey[6],
    '&[data-active="true"]': {
      backgroundColor: theme.colors.decaBlue[0],
      borderColor: theme.colors.decaBlue[3],
      color: theme.colors.decaBlue[7],
    },
  },
}));

type SortOption = {
  value: string;
  label: string;
  field: 'sentAt';
  direction: 'asc' | 'desc';
};

const Activities = () => {
  const { t } = useTranslate('workspace');
  const { classes, theme } = useStyles();
  const [opened, setOpened] = useState(false);

  const sortOptions: SortOption[] = useMemo(
    () => [
      { value: 'date-desc', label: t('newest'), field: 'sentAt', direction: 'desc' },
      { value: 'date-asc', label: t('oldest'), field: 'sentAt', direction: 'asc' },
    ],
    [t]
  );

  const sourceOptions = useMemo(
    () => [
      { value: 'virtualstore', label: t('virtualStore') },
      { value: 'forms', label: t('forms') },
      { value: 'mail', label: t('email') },
      { value: 'sms', label: t('sms') },
      { value: 'line', label: t('line') },
      { value: 'website', label: t('websiteVisit') },
      { value: 'livechat', label: t('liveChat') },
      { value: 'admin', label: t('offline') },
    ],
    [t]
  );

  // Search state with manual debouncing
  const [searchValue, setSearchValue] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [searchOpened, { close: closeSearch, toggle: toggleSearch }] = useDisclosure(false);

  // Refs for custom click-outside detection
  const searchPopoverRef = useRef<HTMLDivElement>(null);
  const searchTargetRef = useRef<HTMLDivElement>(null);

  // Manual debouncing with useEffect
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchValue);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchValue]);

  // Custom click-outside detection for search popover
  useClickOutside({
    targetRef: searchTargetRef,
    dropdownRef: searchPopoverRef,
    onClickOutside: closeSearch,
    enabled: searchOpened,
  });

  // Filter state
  const [selectedSources, setSelectedSources] = useState<string[]>([]);
  const [tempSources, setTempSources] = useState<string[]>([]);
  const [sourceMenuOpened, setSourceMenuOpened] = useState(false);
  const [selectedSort, setSelectedSort] = useState<SortOption>(sortOptions[0]); // Default to Newest

  // Use shared date filter hook
  const {
    datePresets,
    dateRangeForApi,
    getDateRangeLabel,
    handleDatePresetChange,
    selectedDatePreset,
    tempDate,
    tempDateRange,
    setTempDate,
    setTempDateRange,
    setSingleDate,
    setCustomDateRange,
    setSelectedDatePreset,
    resetDateFilter,
  } = useDateFilter();

  const { activities, isLoading, isLoadingMore, hasMore, loadMore, error, totalRecordsCount } =
    useActivities({
      searchTerm: debouncedSearchTerm.trim() || undefined,
      sourceTypes: selectedSources.length > 0 ? selectedSources : undefined,
      dateRange: dateRangeForApi,
      sort: { [selectedSort.field]: selectedSort.direction },
    });

  // Infinite scroll hook
  const { containerRef, sentinelRef } = useInfiniteScroll({
    hasMore,
    isLoading: isLoadingMore || false,
    loadMore,
    threshold: 200,
  });

  // Helper functions
  const handleSourceChange = (source: string, checked: boolean) => {
    if (checked) {
      setTempSources([...tempSources, source]);
    } else {
      setTempSources(tempSources.filter((type) => type !== source));
    }
  };

  const handleSourceMenuOpen = () => {
    setTempSources([...selectedSources]); // Initialize temp state with current selection
    setSourceMenuOpened(true);
  };

  const handleSourceMenuClose = () => {
    setSelectedSources([...tempSources]); // Apply the temporary selection
    setSourceMenuOpened(false);
  };

  // Clear all filters and search
  const clearAllFilters = () => {
    setSearchValue('');
    setDebouncedSearchTerm('');
    setSelectedSources([]);
    setTempSources([]);
    resetDateFilter();
    setSelectedSort(sortOptions[0]); // Reset to default (Newest)
    closeSearch();
  };

  if (error) {
    return (
      <Center h='100%' w='100%'>
        <Text c='red'>{t('errorLoadingActivities')}</Text>
      </Center>
    );
  }

  if (isLoading)
    return (
      <Center h='100%' w='100%' data-testid='activities-loading'>
        <Loader />
      </Center>
    );

  return (
    <>
      <Flex direction='column' gap={rem(18)}>
        {/* Header */}
        <Flex justify='space-between' align='center'>
          <Text fz='xl' fw={500}>
            {t('activities')}
          </Text>
          <DecaButton
            onClick={() => setOpened(true)}
            leftSection={<IconPlus size={14} />}
            variant='neutral'
            color='decaLight.5'
            size='sm'
          >
            {t('create')}
          </DecaButton>
        </Flex>

        {/* Filter Bar */}
        <Group gap={rem(16)} align='flex-start' justify='space-between'>
          <Group gap={rem(16)} align='flex-start'>
            {/* Source Filter */}
            <Menu
              position='bottom-start'
              closeOnClickOutside={true}
              opened={sourceMenuOpened}
              onOpen={handleSourceMenuOpen}
              onClose={handleSourceMenuClose}
            >
              <Menu.Target>
                <Button
                  variant='subtle'
                  className={classes.filterButton}
                  data-active={selectedSources.length > 0}
                  leftSection={<IconFileDescription size={16} />}
                  rightSection={
                    selectedSources.length > 0 ? (
                      <ActionIcon
                        variant='transparent'
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedSources([]);
                          setTempSources([]);
                        }}
                      >
                        <IconX size={16} />
                      </ActionIcon>
                    ) : undefined
                  }
                >
                  {t('activityType')}
                  {selectedSources.length > 0 && (
                    <Badge variant='light' color='decaBlue.8' circle ml={rem(4)}>
                      {selectedSources.length}
                    </Badge>
                  )}
                </Button>
              </Menu.Target>
              <Menu.Dropdown>
                <Stack gap={rem(8)} p={rem(8)}>
                  {sourceOptions.map((option) => (
                    <Checkbox
                      key={option.value}
                      label={option.label}
                      checked={tempSources.includes(option.value)}
                      onChange={(event) =>
                        handleSourceChange(option.value, event.currentTarget.checked)
                      }
                    />
                  ))}
                </Stack>
              </Menu.Dropdown>
            </Menu>

            {/* Activity Date Filter */}
            <DateFilterMenu
              datePresets={datePresets}
              selectedDatePreset={selectedDatePreset}
              getDateRangeLabel={getDateRangeLabel}
              handleDatePresetChange={handleDatePresetChange}
              tempDate={tempDate}
              tempDateRange={tempDateRange}
              setTempDate={setTempDate}
              setTempDateRange={setTempDateRange}
              setSingleDate={setSingleDate}
              setCustomDateRange={setCustomDateRange}
              setSelectedDatePreset={setSelectedDatePreset}
              className={classes.filterButton}
              dateLabel='activityDate'
            />

            {/* Sort Filter */}
            <Menu position='bottom-start'>
              <Menu.Target>
                <Button
                  variant='subtle'
                  className={classes.filterButton}
                  leftSection={<IconArrowsSort size={16} />}
                  data-active={selectedSort.value !== sortOptions[0].value}
                  rightSection={
                    selectedSort.value !== sortOptions[0].value ? (
                      <ActionIcon
                        variant='transparent'
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedSort(sortOptions[0]);
                        }}
                      >
                        <IconX size={16} />
                      </ActionIcon>
                    ) : undefined
                  }
                >
                  {selectedSort.label || t('sort')}
                </Button>
              </Menu.Target>
              <Menu.Dropdown>
                <Stack gap={rem(4)} p={rem(4)}>
                  {sortOptions.map((option) => (
                    <Menu.Item
                      key={option.value}
                      onClick={() => setSelectedSort(option)}
                      bg={selectedSort.value === option.value ? 'blue.0' : undefined}
                    >
                      {option.label}
                    </Menu.Item>
                  ))}
                </Stack>
              </Menu.Dropdown>
            </Menu>
          </Group>

          {/* Search Popover */}
          <Popover
            opened={searchOpened}
            onClose={closeSearch}
            position='bottom-start'
            width={300}
            shadow='md'
            radius={rem(8)}
            styles={{
              dropdown: {
                padding: rem(10),
              },
            }}
            withinPortal
          >
            <Popover.Target ref={searchTargetRef}>
              <ActionIcon
                variant='subtle'
                size='md'
                radius='md'
                className={classes.filterButton}
                data-active={searchValue.trim() !== ''}
                onClick={toggleSearch}
              >
                <IconSearch size={16} />
              </ActionIcon>
            </Popover.Target>
            <Popover.Dropdown ref={searchPopoverRef}>
              <Flex direction='column' gap={rem(8)}>
                <Text c='decaGrey.3' fw={400} fz={rem(12)}>
                  {t('search')}
                </Text>
                <TextInput
                  placeholder={t('typeToSearch')}
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.currentTarget.value)}
                  leftSection={<IconSearch size={16} />}
                  autoFocus
                />
              </Flex>
            </Popover.Dropdown>
          </Popover>
        </Group>

        {/* Results Count - Show when filters or search are active */}
        {(debouncedSearchTerm.trim() !== '' ||
          selectedSources.length > 0 ||
          dateRangeForApi?.start ||
          selectedSort.value !== sortOptions[0].value) && (
          <Flex justify='space-between' align='center'>
            <Text fz={rem(14)} c='decaGrey.6' fw={500}>
              {`${totalRecordsCount} ${t('resultsFound')}`}
            </Text>
            <Button
              variant='subtle'
              size='sm'
              fz={rem(14)}
              c='decaGrey.6'
              onClick={clearAllFilters}
              radius={rem(4)}
              px={rem(8)}
              py={rem(4)}
              bd={`1px solid ${theme.colors.decaLight[5]}`}
            >
              {t('clear')}
            </Button>
          </Flex>
        )}

        {/* Activities Container with Infinite Scroll */}
        <ScrollArea.Autosize ref={containerRef} mah='73vh'>
          {/* Activities Timeline */}
          {activities
            ?.filter((activity) => activity && activity.id !== undefined)
            ?.map((activity, i) => (
              <Timeline
                key={`${activity.id || i}`}
                active={1}
                bulletSize={6}
                lineWidth={1}
                py={rem(18)}
                color='decaGrey.2'
              >
                <Timeline.Item
                  pl={rem(16)}
                  pt={rem(20)}
                  lineVariant='dashed'
                  bullet={
                    <Box w={6} h={6} bg='decaGrey.2' sx={{ borderRadius: '50%' }} mr={rem(10)} />
                  }
                  sx={{
                    '.mantine-Timeline-itemContent': {
                      marginTop: rem(-40),
                    },
                  }}
                >
                  {IgnoreActivityActorType.includes(activity.actor?.type || '') ? (
                    <ActivityEmailSms
                      type={
                        activity.actor?.type === 'mail'
                          ? 'email'
                          : (activity.actor?.type as MessageType)
                      }
                      payload={activity}
                    />
                  ) : (
                    <div
                      // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
                      dangerouslySetInnerHTML={{
                        __html: DOMPurify.sanitize(
                          syncContextData(activity.template, { ...activity })
                        ),
                      }}
                    />
                  )}
                </Timeline.Item>
                <Timeline.Item display='none' m={0} />
              </Timeline>
            ))}

          {/* Loading indicator at bottom */}
          {isLoadingMore && (
            <Center py={rem(20)}>
              <Loader size='sm' />
            </Center>
          )}

          {/* Empty state */}
          {activities &&
            activities.length === 0 &&
            !isLoading &&
            (selectedSources.length > 0 ||
              debouncedSearchTerm.trim() !== '' ||
              dateRangeForApi?.start) && (
              <Box mt={rem(32)} bg='white' pb={rem(16)}>
                <EmptyState
                  imageUrl='images/no_search_result.png'
                  message={t('pleaseAdjustSearchFilter')}
                  title={t('noResultFound')}
                />
                <Center mt={rem(20)}>
                  <DecaButton variant='neutral' onClick={clearAllFilters} color='decaGrey.6'>
                    <Text c='decaGrey.6' fw={500} fz={rem(14)}>
                      {t('clear')}
                    </Text>
                  </DecaButton>
                </Center>
              </Box>
            )}

          {/* Invisible sentinel element for intersection observer */}
          <div ref={sentinelRef} style={{ height: '1px' }} />
        </ScrollArea.Autosize>
      </Flex>
      <ActivityModal opened={opened} setOpened={setOpened} />
    </>
  );
};

export default React.memo(Activities);
