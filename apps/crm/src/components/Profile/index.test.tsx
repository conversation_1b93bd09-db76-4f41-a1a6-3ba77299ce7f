import { ProfileContextProvider } from '@/contexts/ProfileContext';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { Profile } from './index';

// Mock react-router-dom first
vi.mock('react-router-dom', () => ({
  useParams: () => ({
    wsId: 'workspace-123',
    id: 'object-456',
    recordId: 'record-789',
  }),
  useNavigate: () => vi.fn(),
  BrowserRouter: ({ children }: any) => <div>{children}</div>,
  Link: ({ children, to, className, target }: any) => (
    <a href={to} className={className} target={target}>
      {children}
    </a>
  ),
}));

// Mock the useDrawerResize hook
vi.mock('../../hooks/useDrawerResize', () => ({
  useDrawerResize: vi.fn(() => ({
    drawerSize: '75vw',
    isResizing: false,
    resizeRef: { current: null },
    handleMouseDown: vi.fn(),
    drawerWidth: 75,
    setDrawerWidth: vi.fn(),
    isFullWidth: false,
    toggleFullWidth: vi.fn(),
  })),
}));

// Mock the workspace context
vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: vi.fn(() => ({
    opened: true,
    data: [
      { id: 'record-1', name: 'Record 1' },
      { id: 'record-2', name: 'Record 2' },
      { id: 'record-3', name: 'Record 3' },
    ],
    currRecordIndex: 1,
    object: {
      name: {
        singular: 'Test Object',
        plural: 'Test Objects',
      },
      profileSettings: [
        { type: 'activities', enabled: true },
        { type: 'identities', enabled: true },
        { type: 'attachments', enabled: true },
        { type: 'tasks', enabled: true },
        { type: 'pinCustomObject', enabled: true },
      ],
      hasTags: true,
      childObjects: [
        { id: 'form-1', name: 'Form 1', pinned: false, isVisible: true },
        { id: 'form-2', name: 'Form 2', pinned: true, isVisible: true },
      ],
    },
    activeView: {
      displayLongText: [],
      permission: { canRead: true, canList: true, canView: true },
    },
    openProfile: vi.fn(),
    viewLoading: false,
  })),
}));

// Mock BreadcrumbContext
vi.mock('@/contexts/BreadcrumbContext', () => ({
  BreadcrumbProvider: ({ children }: any) => children,
  useBreadcrumbContext: () => ({
    breadcrumbs: [],
    addBreadcrumb: vi.fn(),
    navigateToBreadcrumb: vi.fn(),
    clearBreadcrumbs: vi.fn(),
  }),
}));

// Mock useBreadcrumbNavigation
vi.mock('@/hooks/useBreadcrumbNavigation', () => ({
  useBreadcrumbNavigation: () => ({
    navigateToLinkedRecord: vi.fn(),
  }),
}));

// Mock @resola-ai/ui/hooks
vi.mock('@resola-ai/ui/hooks', () => ({
  usePathParams: () => ({
    createPathWithLngParam: (path: string) => path,
  }),
}));

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
  }),
}));

vi.mock('@/tolgee', () => ({
  tolgee: {
    changeLanguage: vi.fn(),
  },
}));

// Mock ProfileContext
vi.mock('@/contexts/ProfileContext', async () => {
  const actual = await vi.importActual('@/contexts/ProfileContext');
  return {
    ...actual,
    ProfileContextProvider: ({ children }) => children,
    useProfileContext: vi.fn(() => ({
      profile: {},
      mutateProfile: vi.fn(),
      tags: [],
      handleAddTag: vi.fn(),
      handleRemoveTag: vi.fn(),
      dragging: false,
      onSetDragging: vi.fn(),
      sortItems: [
        { id: 'objectFields', height: 'auto' },
        { id: 'tags', height: 'auto' },
      ],
      handleDragEnd: vi.fn(),
      handleChangeSize: vi.fn(),
      forms: [],
      getForm: vi.fn(),
      editFields: {},
      onEditField: vi.fn(),
      resetEditFields: vi.fn(),
      customObjectFields: [],
      setCustomObjectFields: vi.fn(),
      closeProfile: vi.fn(),
      handleSaveRecord: vi.fn(),
    })),
  };
});

// Mock @resola-ai/ui
vi.mock('@resola-ai/ui', () => ({
  If: ({ children, condition }: { children: React.ReactNode; condition: boolean }) =>
    condition ? <div>{children}</div> : null,
  Modal: ({ children, opened, onClose, ...props }: any) =>
    opened ? (
      <div data-testid='modal' {...props}>
        {children}
      </div>
    ) : null,
  DecaSwitch: ({ children, ...props }: any) => (
    <div data-testid='deca-switch' {...props}>
      {children}
    </div>
  ),
  DecaTag: ({ children, ...props }: any) => (
    <div data-testid='deca-tag' {...props}>
      {children}
    </div>
  ),
}));

// Mock permission utils
vi.mock('@resola-ai/ui/components/DecaTable/utils', () => ({
  isPermissionAllowed: () => true,
  PERMISSION_KEYS: {
    VIEW_READ: 'view:read',
    OBJECT_READ: 'object:read',
    VIEW_LIST: 'view:list',
  },
}));

// Mock components that are lazy loaded
vi.mock('./Activities', () => ({
  default: () => <div data-testid='activities'>Activities Component</div>,
}));

vi.mock('./Identities', () => ({
  default: () => <div data-testid='identities'>Identities Component</div>,
}));

vi.mock('./Files', () => ({
  default: () => <div data-testid='files'>Files Component</div>,
}));

vi.mock('./History', () => ({
  default: () => <div data-testid='history'>History Component</div>,
}));

// Mock Breadcrumbs component
vi.mock('./Breadcrumbs', () => ({
  default: () => <div data-testid='breadcrumbs'>Breadcrumbs</div>,
}));

// Mock NoPermissionAccess
vi.mock('../NoPermissionAccess', () => ({
  default: () => <div data-testid='no-view-access'>No Permission Access</div>,
}));

const renderWithProviders = (component: React.ReactNode) => {
  return renderWithMantine(
    <BrowserRouter>
      <ProfileContextProvider>{component}</ProfileContextProvider>
    </BrowserRouter>
  );
};

describe('Profile Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders profile component with drawer', () => {
    renderWithProviders(<Profile />);
    expect(screen.getByTestId('profile-drawer')).toBeInTheDocument();
    expect(screen.getByTestId('profile-header')).toBeInTheDocument();
    expect(screen.getByTestId('fullview-toggle')).toBeInTheDocument();
    expect(screen.getByTestId('drawer-resize-handle')).toBeInTheDocument();
  });

  it('renders profile contact section', () => {
    renderWithProviders(<Profile />);
    expect(screen.getByTestId('profile-contact-test-id')).toBeInTheDocument();
  });

  it('renders object fields section', () => {
    renderWithProviders(<Profile />);
    expect(screen.getByTestId('object-fields-test-id')).toBeInTheDocument();
  });

  it('renders tags section when object has tags', () => {
    renderWithProviders(<Profile />);
    expect(screen.getByTestId('tags-test-id')).toBeInTheDocument();
  });

  it('renders breadcrumbs', () => {
    renderWithProviders(<Profile />);
    expect(screen.getByTestId('breadcrumbs')).toBeInTheDocument();
  });

  it('renders tabs when profile settings are enabled', () => {
    renderWithProviders(<Profile />);
    // Since hasTabs is true based on our mock data, tabs should render
    expect(screen.getByTestId('tab-activities')).toBeInTheDocument();
    expect(screen.getByTestId('tab-identities')).toBeInTheDocument();
    expect(screen.getByTestId('tab-files')).toBeInTheDocument();
    expect(screen.getByTestId('tab-history')).toBeInTheDocument();
  });

  it('switches between tabs correctly', async () => {
    renderWithProviders(<Profile />);

    // Click activities tab
    fireEvent.click(screen.getByTestId('tab-activities'));
    await waitFor(() => {
      expect(screen.getByTestId('activities')).toBeInTheDocument();
    });

    // Click identities tab
    fireEvent.click(screen.getByTestId('tab-identities'));
    await waitFor(() => {
      expect(screen.getByTestId('identities')).toBeInTheDocument();
    });
  });

  it('renders fullview toggle button', () => {
    renderWithProviders(<Profile />);
    const fullviewButton = screen.getByTestId('fullview-toggle');
    expect(fullviewButton).toBeInTheDocument();
  });

  describe('Navigation Controls', () => {
    it('renders navigation controls when currRecordIndex is valid', () => {
      renderWithProviders(<Profile />);

      // Find navigation buttons by their icons
      const buttons = screen.getAllByRole('button');
      const prevButton = buttons.find((button) =>
        button.querySelector('svg.tabler-icon-chevron-up')
      );
      const nextButton = buttons.find((button) =>
        button.querySelector('svg.tabler-icon-chevron-down')
      );

      expect(prevButton).toBeInTheDocument();
      expect(nextButton).toBeInTheDocument();
    });

    it('has clickable navigation buttons', () => {
      renderWithProviders(<Profile />);

      const buttons = screen.getAllByRole('button');
      const prevButton = buttons.find((button) =>
        button.querySelector('svg.tabler-icon-chevron-up')
      );
      const nextButton = buttons.find((button) =>
        button.querySelector('svg.tabler-icon-chevron-down')
      );

      expect(prevButton).toBeInTheDocument();
      expect(nextButton).toBeInTheDocument();

      // Test that buttons are clickable
      fireEvent.click(prevButton!);
      fireEvent.click(nextButton!);
    });
  });

  describe('Permission Handling', () => {
    it('renders main content when user has view permission', () => {
      renderWithProviders(<Profile />);

      expect(screen.getByTestId('profile-contact-test-id')).toBeInTheDocument();
      expect(screen.getByTestId('object-fields-test-id')).toBeInTheDocument();
      expect(screen.queryByTestId('no-view-access')).not.toBeInTheDocument();
    });
  });

  describe('Conditional Rendering', () => {
    it('renders tabs section when hasTabs is true', () => {
      renderWithProviders(<Profile />);

      // Check that tabs are rendered when profile settings are enabled
      expect(screen.getByTestId('tab-activities')).toBeInTheDocument();
      expect(screen.getByTestId('tab-identities')).toBeInTheDocument();
    });

    it('filters out pinned forms from left panel when pinCustomObject is enabled', () => {
      // This test verifies that pinned forms appear as tabs instead of in the left panel
      renderWithProviders(<Profile />);

      // Since form-2 is pinned and pinCustomObject is enabled, it should not render in the left panel
      // This is tested indirectly through the tab rendering
      expect(screen.getByTestId('tab-activities')).toBeInTheDocument();
    });
  });

  describe('Resize Functionality', () => {
    it('renders resize handle', () => {
      renderWithProviders(<Profile />);

      const resizeHandle = screen.getByTestId('drawer-resize-handle');
      expect(resizeHandle).toBeInTheDocument();

      // Test that resize handle is clickable
      fireEvent.mouseDown(resizeHandle);
    });

    it('renders drawer with correct test id', () => {
      renderWithProviders(<Profile />);

      const drawer = screen.getByTestId('profile-drawer');
      expect(drawer).toBeInTheDocument();
    });
  });
});
