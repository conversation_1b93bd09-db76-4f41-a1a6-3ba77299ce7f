import { HEIGHT_OF_HEADER } from '@/constants';
import { BOX_CARDS, ProfileContextProvider, useProfileContext } from '@/contexts/ProfileContext';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { DndContext, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { SortableContext } from '@dnd-kit/sortable';
import {
  ActionIcon,
  Box,
  Divider,
  Drawer,
  Flex,
  LoadingOverlay,
  ScrollArea,
  rem,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { If } from '@resola-ai/ui';
import { PERMISSION_KEYS, isPermissionAllowed } from '@resola-ai/ui/components/DecaTable/utils';
import {
  IconArrowsMaximize,
  IconArrowsMinimize,
  IconChevronDown,
  IconChevronUp,
  IconChevronsRight,
} from '@tabler/icons-react';
import React, { Suspense, useCallback, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useDrawerResize } from '../../hooks/useDrawerResize';
import NoPermissionAccess from '../NoPermissionAccess';
import BoxCardAdvance from './BoxCardAdvance';
import Breadcrumbs from './Breadcrumbs';
import Forms from './Forms';
import ObjectFields from './ObjectFields';
import ProfileContact from './ProfileContact';
import ProfileTabs from './Tabs';
import Tags from './Tags';

const useStyles = createStyles((theme) => ({
  drawer: {
    position: 'relative',
    '& .mantine-Drawer-content': {
      borderLeft: `${rem(1)} solid ${theme.colors.decaBlue[5]}`,
      borderTop: `${rem(1)} solid ${theme.colors.decaBlue[5]}`,
      borderTopLeftRadius: rem(6),
      boxShadow: `0 0 ${rem(4)} 0 ${theme.colors.decaBlue[5]}`,
      willChange: 'unset !important',
    },
    '& .mantine-Drawer-inner': {
      top: HEIGHT_OF_HEADER,
      height: `calc(100vh - ${HEIGHT_OF_HEADER}px)`,
    },
    '& .mantine-Drawer-body': {
      padding: 0,
      height: 'calc(100% - 22px)',
    },
  },
  resizeHandle: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: rem(1),
    cursor: 'col-resize',
    zIndex: 1000,
    '&:hover': {
      backgroundColor: theme.colors.decaBlue[2],
    },
    '&:active': {
      backgroundColor: theme.colors.decaBlue[4],
    },
  },
  header: {
    svg: {
      width: rem(18),
      color: theme.colors.decaDark[1],
    },
  },
  leftBoxCardAdvance: {
    position: 'relative',
    height: '100%',
    flexGrow: 1,
    background: theme.colors.decaLight[1],
  },
  disabled: {
    pointerEvents: 'none',
  },
}));

// Loading component for Suspense fallback
const TabLoading = () => (
  <Box pos='relative' h='100%'>
    <LoadingOverlay visible={true} loaderProps={{ size: 'sm' }} />
  </Box>
);

const ProfileSettings = () => {
  const { opened, data, currRecordIndex, object, activeView, openProfile, viewLoading } =
    useWorkspaceContext();
  const { classes } = useStyles();
  const {
    onSetDragging: setDragging,
    handleDragEnd,
    handleChangeSize,
    sortItems,
    getForm,
    closeProfile,
  } = useProfileContext();
  const { recordId } = useParams();
  const canReadView = isPermissionAllowed(activeView?.permission || {}, PERMISSION_KEYS.VIEW_READ);
  const [showHiddenFields, setShowHiddenFields] = useState(false);

  const { drawerSize, resizeRef, handleMouseDown, isFullWidth, toggleFullWidth } = useDrawerResize({
    defaultWidth: 75,
    maxWidth: 100,
    storageKey: 'profile-drawer-width',
  });

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const objectFieldsCard = sortItems.find((item) => item.id === BOX_CARDS.objectFields) || {
    id: BOX_CARDS.objectFields,
    height: 'auto',
  };

  const allowOrderCards = sortItems.filter((item) => {
    if (item.id === 'tags') {
      return object?.hasTags;
    }
    return item.id !== BOX_CARDS.objectFields;
  });

  const moveProfile = useCallback(
    (direction: 'prev' | 'next') => {
      const moveToIndex = direction === 'prev' ? currRecordIndex - 1 : currRecordIndex + 1;
      if (moveToIndex >= 0 && moveToIndex < data.length) {
        openProfile(data[moveToIndex].id, data[moveToIndex]);
      }
    },
    [currRecordIndex, data, openProfile]
  );

  // Check if there are any tab settings enabled
  const hasTabs = object?.profileSettings?.some(
    (setting) =>
      setting.enabled &&
      (setting.type === 'pinCustomObject' ||
        setting.type === 'tasks' ||
        setting.type === 'attachments' ||
        setting.type === 'activities' ||
        setting.type === 'identities' ||
        (setting.type === 'pinLongText' && activeView?.displayLongText?.length))
  );

  const renderBoxCardAdvance = (boxCard) => {
    switch (boxCard.id) {
      case 'tags':
        return <Tags />;
      default: {
        // First check if this child object is visible
        const childObj = object?.childObjects?.find((child) => child.id === boxCard.id);
        if (!childObj?.isVisible) {
          return null;
        }

        // Check if this form is pinned and pinCustomObject is enabled
        const isPinCustomObjectEnabled = object?.profileSettings?.some(
          (setting) => setting.type === 'pinCustomObject' && setting.enabled
        );

        // If form is pinned and pinCustomObject is enabled, don't render it here (it will appear as a tab)
        if (isPinCustomObjectEnabled && childObj.pinned) {
          return null;
        }

        const form = getForm(boxCard.id);
        if (!form) return null;
        return (
          <Suspense fallback={<TabLoading />}>
            <Forms form={form} />
          </Suspense>
        );
      }
    }
  };

  const recordIdIndex = useMemo(() => {
    return `${recordId}_${currRecordIndex}`;
  }, [recordId, currRecordIndex]);

  return (
    <Drawer
      data-testid='profile-drawer'
      opened={opened}
      onClose={closeProfile}
      position='right'
      size={drawerSize}
      withCloseButton={false}
      className={classes.drawer}
      transitionProps={{
        transition: 'slide-left',
        duration: 500,
        timingFunction: 'ease-in-out',
      }}
      overlayProps={{ opacity: 0.1, blur: 0 }}
    >
      <>
        <Box
          ref={resizeRef}
          className={classes.resizeHandle}
          onMouseDown={handleMouseDown}
          data-testid='drawer-resize-handle'
        />
        <If condition={viewLoading}>
          <LoadingOverlay visible={true} />
        </If>
        <If condition={!viewLoading && !!activeView && !canReadView}>
          <NoPermissionAccess />
        </If>
        <If condition={!viewLoading && canReadView}>
          <>
            <Flex className={classes.header} px={rem(16)} py={rem(6)} data-testid='profile-header'>
              <Flex align='center' gap={rem(4)}>
                <ActionIcon onClick={closeProfile} variant='subtle' c={'decaGrey.5'}>
                  <IconChevronsRight />
                </ActionIcon>
                <ActionIcon
                  data-testid='fullview-toggle'
                  onClick={toggleFullWidth}
                  variant='subtle'
                  c={'decaGrey.5'}
                >
                  {isFullWidth ? <IconArrowsMinimize /> : <IconArrowsMaximize />}
                </ActionIcon>
                {currRecordIndex >= 0 && (
                  <>
                    <ActionIcon
                      variant='subtle'
                      c={'decaGrey.5'}
                      onClick={() => moveProfile('prev')}
                      className={currRecordIndex <= 0 ? classes.disabled : ''}
                    >
                      <IconChevronUp />
                    </ActionIcon>
                    <ActionIcon
                      variant='subtle'
                      c={'decaGrey.5'}
                      onClick={() => moveProfile('next')}
                      className={currRecordIndex >= data.length - 1 ? classes.disabled : ''}
                    >
                      <IconChevronDown />
                    </ActionIcon>
                  </>
                )}
              </Flex>
              <Flex align='center' style={{ flex: 1 }}>
                <Breadcrumbs />
              </Flex>
            </Flex>
            <Divider />
            <Flex w={'inherit'} h={'inherit'} sx={{ overflow: 'hidden' }}>
              <ScrollArea
                type='hover'
                w={hasTabs ? '50%' : '100%'}
                h={'100%'}
                p={rem(16)}
                className={classes.leftBoxCardAdvance}
              >
                <Flex gap={rem(16)} direction='column'>
                  <ProfileContact />
                  <BoxCardAdvance
                    recordId={recordIdIndex}
                    boxCard={objectFieldsCard}
                    onChangeSize={handleChangeSize}
                    allowDrag={false}
                    expandToFit={showHiddenFields}
                  >
                    <ObjectFields onShowHiddenFieldsChange={setShowHiddenFields} />
                  </BoxCardAdvance>
                  <DndContext
                    modifiers={[restrictToVerticalAxis]}
                    sensors={sensors}
                    onDragStart={() => {
                      setDragging(true);
                    }}
                    onDragEnd={({ active, over }) => {
                      setDragging(false);
                      if (over && active.id !== over?.id) {
                        handleDragEnd(active, over);
                      }
                    }}
                  >
                    <SortableContext items={allowOrderCards}>
                      {allowOrderCards.map((boxCard) => {
                        // Skip rendering if no childObjects exist
                        if (boxCard.id === 'forms' && (object?.childObjects?.length ?? 0) === 0) {
                          return null;
                        }

                        // First check if this child object is visible
                        const childObj = object?.childObjects?.find((child) => child.id === boxCard.id);
                        if (childObj && !childObj.isVisible) {
                          return null;
                        }

                        // Check if this specific form is pinned and should be rendered as a tab instead
                        const isPinCustomObjectEnabled = object?.profileSettings?.some(
                          (setting) => setting.type === 'pinCustomObject' && setting.enabled
                        );

                        // If form is pinned and pinCustomObject is enabled, don't render it here
                        if (isPinCustomObjectEnabled && childObj?.pinned) {
                          return null;
                        }

                        return (
                          <BoxCardAdvance
                            key={boxCard?.id}
                            recordId={currRecordIndex?.toString() || ''}
                            boxCard={boxCard}
                            onChangeSize={handleChangeSize}
                          >
                            {renderBoxCardAdvance(boxCard)}
                          </BoxCardAdvance>
                        );
                      })}
                    </SortableContext>
                  </DndContext>
                </Flex>
              </ScrollArea>
              {(hasTabs || !!activeView?.displayLongText?.length) && (
                <ProfileTabs isFullview={isFullWidth} />
              )}
            </Flex>
          </>
        </If>
      </>
    </Drawer>
  );
};

export const Profile = () => {
  return (
    <ProfileContextProvider>
      <ProfileSettings />
    </ProfileContextProvider>
  );
};

export default React.memo(Profile);
