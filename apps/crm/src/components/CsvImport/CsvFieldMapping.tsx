import { ColumnIcon } from '@/constants';
import { Box, type ComboboxItem, Text, ThemeIcon, rem } from '@mantine/core';
import { FieldTypes, InfoAlert } from '@resola-ai/ui/components';
import { IconPlus, IconX } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import React, { useCallback, useMemo, useRef } from 'react';
import type { ObjectColumn } from '../../models';
import {
  type FieldMapping,
  calculateTotalRecords,
  filterAutoCreatedFields,
  getAutoMatchedFieldType,
  getCsvFields,
  updateFieldMapping,
} from '../../utils';
import { useOptions } from '../FieldSettings/AddFieldForm/useOptions';
import FieldMappingList from './FieldMappingList';
import { useImportStyles } from './useImportStyle';

interface CsvFieldMappingProps {
  csvData: string[][];
  onFieldMappingChange: (mappings: FieldMapping[]) => void;
  fileName?: string;
  objectName?: string;
  objectFields?: ObjectColumn[];
  fieldMappings: FieldMapping[]; // Add fieldMappings as a prop
}

const iconMap = {
  plus: (
    <ThemeIcon variant='transparent' c='decaBlue.5' size={16}>
      <IconPlus />
    </ThemeIcon>
  ),
  x: (
    <ThemeIcon variant='transparent' c='decaRed.5' size={16}>
      <IconX />
    </ThemeIcon>
  ),
};

const CsvFieldMapping: React.FC<CsvFieldMappingProps> = ({
  csvData,
  onFieldMappingChange,
  fileName,
  objectName,
  objectFields = [],
  fieldMappings,
}) => {
  const { t } = useTranslate('workspace');
  const { classes } = useImportStyles();
  const { options: allFieldTypeOptions } = useOptions();
  const onFieldMappingChangeRef = useRef(onFieldMappingChange);

  // Update ref when callback changes
  React.useEffect(() => {
    onFieldMappingChangeRef.current = onFieldMappingChange;
  }, [onFieldMappingChange]);

  // Memoize filtered field type options with stable dependency
  const fieldTypeOptions = useMemo(() => {
    if (!allFieldTypeOptions || allFieldTypeOptions.length === 0) {
      return [];
    }
    return filterAutoCreatedFields(allFieldTypeOptions);
  }, [allFieldTypeOptions]);

  // Ensure field type options are loaded before proceeding
  const hasFieldTypeOptions = fieldTypeOptions.length > 0;

  // Memoize CSV fields
  const csvFields = useMemo(() => getCsvFields(csvData), [csvData]);

  // Check if importing into new object (no existing fields)
  const isNewObject = useMemo(() => {
    return !Array.isArray(objectFields) || objectFields.length === 0;
  }, [objectFields]);

  const initializeFieldMappings = useCallback(() => {
    const usedFieldIds = new Set<string>();

    return csvFields.map((csvField) => {
      const lowerCsvField = csvField.toLowerCase();

      let matchedField: ObjectColumn | null = null;

      if (Array.isArray(objectFields) && objectFields.length > 0) {
        const availableFields = objectFields.filter((field) => !usedFieldIds.has(field.id));

        // Exact name match
        matchedField =
          availableFields.find((field) => (field.name?.toLowerCase() ?? '') === lowerCsvField) ||
          null;

        // Partial includes match
        if (!matchedField) {
          matchedField =
            availableFields.find((field) => {
              const fieldName = field.name?.toLowerCase() ?? '';
              return fieldName.includes(lowerCsvField) || lowerCsvField.includes(fieldName);
            }) || null;
        }
      }

      if (matchedField) {
        usedFieldIds.add(matchedField.id);
        return {
          csvField,
          systemFieldType: matchedField.type,
          systemFieldId: matchedField.id,
          systemFieldName: matchedField.name || null,
          isExistingField: true,
          skipped: false,
        };
      }

      // For new objects or when no existing fields, use field name-based type detection
      const suggestedFieldType = getAutoMatchedFieldType(csvField);

      // Ensure the suggested field type is available in the field type options
      const availableFieldType =
        hasFieldTypeOptions && fieldTypeOptions.length > 0
          ? fieldTypeOptions.find((option) => option.value === suggestedFieldType)?.value ||
            fieldTypeOptions[0].value
          : suggestedFieldType;

      return {
        csvField,
        systemFieldType: availableFieldType,
        systemFieldId: null,
        systemFieldName: null,
        isExistingField: false,
        skipped: false,
      };
    });
  }, [csvFields, objectFields, isNewObject, hasFieldTypeOptions, fieldTypeOptions]);

  // Initialize field mappings if they are empty (only on first load)
  React.useEffect(() => {
    // Only initialize if fieldMappings is empty and we have CSV fields
    if (fieldMappings.length === 0 && csvFields.length > 0) {
      // Skip if field type options are not loaded yet for new objects
      if (isNewObject && !hasFieldTypeOptions) {
        return;
      }

      const newMappings = initializeFieldMappings();
      onFieldMappingChangeRef.current(newMappings);
    }
  }, [
    fieldMappings.length,
    csvFields.length,
    isNewObject,
    hasFieldTypeOptions,
    initializeFieldMappings,
  ]);

  // Determine if field should show new field type selector
  const shouldShowNewFieldType = useCallback(
    (mapping: FieldMapping) => {
      if (isNewObject) return false; // For new objects, always show single select
      return !mapping.skipped && !mapping.isExistingField && !mapping.systemFieldId;
    },
    [isNewObject]
  );

  const getOption = (value: string, labelKey: string, icon: React.ReactNode) => ({
    value,
    label: t(labelKey),
    icon,
  });

  // Create a function to get main field options for a specific mapping
  const getMainFieldOptionsForMapping = useCallback(
    (currentMapping: FieldMapping) => {
      if (isNewObject) {
        return [getOption('skip', 'skipThisField', iconMap.x), ...fieldTypeOptions];
      }

      const actionOptions = [
        getOption('create-new', 'createNewField', iconMap.plus),
        getOption('skip', 'skipThisField', iconMap.x),
      ];

      // Get field IDs that are already selected by other mappings (excluding current mapping)
      const selectedFieldIds = new Set(
        fieldMappings
          .filter(
            (mapping) =>
              mapping.csvField !== currentMapping.csvField &&
              mapping.isExistingField &&
              mapping.systemFieldId
          )
          .map((mapping) => mapping.systemFieldId)
      );

      const existingFieldOptions = Array.isArray(objectFields)
        ? objectFields
            .filter(
              (field) =>
                !selectedFieldIds.has(field.id) || // Field is not selected by others
                field.id === currentMapping.systemFieldId // OR it's the current mapping's selected field
            )
            .map((field) => {
              const IconComponent = ColumnIcon[field.type];
              return {
                value: field.id,
                label: field.name || field.id,
                icon: IconComponent ? <IconComponent /> : null,
              };
            })
        : [];

      return [...actionOptions, ...existingFieldOptions];
    },
    [isNewObject, fieldTypeOptions, objectFields, t, fieldMappings]
  );

  // Memoize main field options (keeping for backward compatibility, but now uses the function above)
  const mainFieldOptions = useMemo(() => {
    if (isNewObject) {
      return [getOption('skip', 'skipThisField', iconMap.x), ...fieldTypeOptions];
    }
    const actionOptions = [
      getOption('create-new', 'createNewField', iconMap.plus),
      getOption('skip', 'skipThisField', iconMap.x),
    ];

    const existingFieldOptions = Array.isArray(objectFields)
      ? objectFields.map((field) => {
          const IconComponent = ColumnIcon[field.type];
          return {
            value: field.id,
            label: field.name || field.id,
            icon: IconComponent ? <IconComponent /> : null,
          };
        })
      : [];

    return [...actionOptions, ...existingFieldOptions];
  }, [isNewObject, fieldTypeOptions, objectFields, t]);

  // Memoize render function (shared for both field and field type options)
  const renderOption = useCallback(
    ({ option }: { option: ComboboxItem & { icon: React.ReactNode } }) => (
      <>
        {option.icon && (
          <ThemeIcon variant='transparent' c='decaGrey.5' size={18}>
            {option.icon}
          </ThemeIcon>
        )}
        <Text truncate fz={rem(14)}>
          {option.label}
        </Text>
      </>
    ),
    []
  );

  // Get main selected value for a mapping
  const getMainSelectedValue = useCallback(
    (mapping: FieldMapping) => {
      if (mapping.skipped) return 'skip';
      if (mapping.isExistingField && mapping.systemFieldId) return mapping.systemFieldId;
      if (isNewObject) {
        // For new objects, ensure the value exists in the options
        const fieldType = mapping.systemFieldType || FieldTypes.SINGLE_LINE_TEXT;
        const hasOption = mainFieldOptions.some((option) => option.value === fieldType);
        return hasOption
          ? fieldType
          : fieldTypeOptions.length > 0
            ? fieldTypeOptions[0].value
            : FieldTypes.SINGLE_LINE_TEXT;
      }
      return 'create-new';
    },
    [isNewObject, mainFieldOptions, fieldTypeOptions]
  );

  // Handle main field selection change
  const handleMainFieldSelectionChange = useCallback(
    (csvField: string, value: string | null) => {
      let updatedMappings: FieldMapping[];

      if (value === 'skip') {
        updatedMappings = updateFieldMapping(fieldMappings, csvField, {
          systemFieldType: null,
          systemFieldId: null,
          systemFieldName: null,
          isExistingField: false,
          skipped: true,
        });
      } else if (value === 'create-new') {
        updatedMappings = updateFieldMapping(fieldMappings, csvField, {
          systemFieldType: FieldTypes.SINGLE_LINE_TEXT, // Default to single line text
          systemFieldId: null,
          systemFieldName: null,
          isExistingField: false,
          skipped: false,
        });
      } else if (value && isNewObject) {
        // For new objects, value is directly a field type
        updatedMappings = updateFieldMapping(fieldMappings, csvField, {
          systemFieldType: value,
          systemFieldId: null,
          systemFieldName: null,
          isExistingField: false,
          skipped: false,
        });
      } else if (value) {
        // Existing field selected (for existing objects)
        const existingField = Array.isArray(objectFields)
          ? objectFields.find((field) => field.id === value)
          : null;

        if (existingField) {
          updatedMappings = updateFieldMapping(fieldMappings, csvField, {
            systemFieldType: existingField.type,
            systemFieldId: existingField.id,
            systemFieldName: existingField.name || null,
            isExistingField: true,
            skipped: false,
          });
        } else {
          return;
        }
      } else {
        return;
      }

      onFieldMappingChangeRef.current(updatedMappings);
    },
    [isNewObject, objectFields, fieldMappings]
  );

  // Handle new field type change
  const handleNewFieldTypeChange = useCallback(
    (csvField: string, fieldType: string | null) => {
      const updatedMappings = updateFieldMapping(fieldMappings, csvField, {
        systemFieldType: fieldType,
        systemFieldId: null,
        systemFieldName: null,
        isExistingField: false,
        skipped: false,
      });
      onFieldMappingChangeRef.current(updatedMappings);
    },
    [fieldMappings]
  );

  // Memoize summary data
  const summaryData = useMemo(() => {
    const totalRecords = calculateTotalRecords(csvData);
    return { totalRecords };
  }, [csvData]);
  const { totalRecords } = summaryData;

  return (
    <Box>
      <Box className={classes.container}>
        <InfoAlert description={t('csvFieldMappingInfo')} />
        <FieldMappingList
          fieldMappings={fieldMappings}
          objectFields={objectFields}
          mainFieldOptions={mainFieldOptions}
          fieldTypeOptions={fieldTypeOptions}
          shouldShowNewFieldType={shouldShowNewFieldType}
          getMainSelectedValue={getMainSelectedValue}
          handleMainFieldSelectionChange={handleMainFieldSelectionChange}
          handleNewFieldTypeChange={handleNewFieldTypeChange}
          renderOption={renderOption}
          getMainFieldOptionsForMapping={getMainFieldOptionsForMapping}
        />
      </Box>
      {fileName && objectName && (
        <Box>
          <Text
            mt={rem(24)}
            // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
            dangerouslySetInnerHTML={{
              __html: t('importSummary', {
                count: totalRecords - 1,
                fileName: fileName,
                objectName: objectName,
              }),
            }}
          />
        </Box>
      )}
    </Box>
  );
};

export default CsvFieldMapping;
