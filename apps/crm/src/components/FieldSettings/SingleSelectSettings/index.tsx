import { OptionsName, OptionsOrder } from '@/constants/workspace';
import type { FieldOptions } from '@/models';
import { handleDragOption, sortManual, sortOptionsAsc, sortOptionsDesc } from '@/utils';
import {
  type Active,
  DndContext,
  type Over,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { SortableContext } from '@dnd-kit/sortable';
import { Box, Flex, Select, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { CustomSelect, DecaStatus, HFSwitch } from '@resola-ai/ui';
import { IconPlus } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { nanoid } from 'nanoid';
import { useCallback, useMemo } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { useOptions } from '../AddFieldForm/useOptions';
import OptionItem from './OptionItem';
import { OptionSelect } from './OptionSelect';

const useStyles = createStyles((theme) => ({
  addOption: {
    borderRadius: rem(4),
    backgroundColor: theme.colors.decaLight[0],
    padding: rem(10),
    cursor: 'pointer',
  },
  sortable: {
    overflowY: 'auto',
  },
}));

export const choicesName = `${OptionsName}.choices`;
const notSelectedValue = 'notSelected';

export const SingleSelectSettings = ({
  options,
  isMulti = false,
}: {
  options: FieldOptions;
  isMulti?: boolean;
}) => {
  const { t } = useTranslate('workspace');
  const { control, watch, setValue } = useFormContext();
  const { classes } = useStyles();
  const { fields, append, remove } = useFieldArray({
    control,
    name: choicesName,
  });
  const watchFieldArray = watch(choicesName) || [];
  const controlledFields = fields.map((field, index) => ({
    ...field,
    ...watchFieldArray[index],
  }));

  const { choiceOrderOptions } = useOptions();
  const defaultOption = watch(`${OptionsName}.defaultValue`);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const onDragEnd = useCallback(
    (active: Active, over: Over) => {
      const arrayMoved = handleDragOption(active, over, controlledFields);
      setValue(choicesName, arrayMoved);
    },
    [controlledFields, setValue]
  );

  const selectOptions = useMemo(() => {
    const notSelectedOption = {
      value: notSelectedValue,
      label: <Text c='decaGrey.4'>{t('notSelected')}</Text>,
    };
    const optionChoices =
      controlledFields?.map((option) => ({
        value: option.id,
        label: <DecaStatus size='small' variant={option.color} text={option.label} />,
      })) || [];

    return [notSelectedOption, ...optionChoices];
  }, [controlledFields, t]);

  const onAddOption = () => {
    const id = nanoid(10);
    append({
      id,
      label: t('name'),
      color: 'grey',
      time: new Date().getTime(),
    });
    !isMulti && !defaultOption && setValue(`${OptionsName}.defaultValue`, notSelectedValue);
  };

  const handleChangeOrder = useCallback(
    (val: string) => {
      setValue(`${OptionsName}.order`, val);
      if (val === OptionsOrder.ASC) {
        setValue(choicesName, sortOptionsAsc(controlledFields));
      } else if (val === OptionsOrder.DESC) {
        setValue(choicesName, sortOptionsDesc(controlledFields));
      } else {
        setValue(choicesName, sortManual(controlledFields));
      }
    },
    [controlledFields]
  );

  const hasOption = !!controlledFields?.length;

  return (
    <>
      <Flex justify={'space-between'} align={'center'}>
        <HFSwitch
          control={control}
          name={`${OptionsName}.enableColor`}
          label={t('colorCode')}
          size='xs'
          my={rem(20)}
        />
        {hasOption && (
          <Flex gap={rem(5)} align={'center'}>
            <Text>{`${t('sort')}:`}</Text>
            <Select
              w={rem(100)}
              defaultValue={options?.order || OptionsOrder.MANUAL}
              data={choiceOrderOptions}
              onChange={(val) => handleChangeOrder(val as string)}
            />
          </Flex>
        )}
      </Flex>
      <Box my={rem(16)} className={classes.addOption}>
        <Flex c='decaNavy.4' align={'center'} gap={rem(8)} onClick={onAddOption}>
          <IconPlus size={16} />
          <Text>{t('addOption')}</Text>
        </Flex>
        <DndContext
          sensors={sensors}
          onDragStart={() => {}}
          onDragEnd={({ active, over }) => {
            if (over && active.id !== over?.id) {
              onDragEnd(active, over);
            }
          }}
          onDragCancel={() => {}}
        >
          <SortableContext items={controlledFields || []}>
            <Flex
              mah={rem(280)}
              direction={'column'}
              mt={controlledFields?.length ? rem(10) : 0}
              className={classes.sortable}
            >
              {controlledFields.map((field, index) => (
                <OptionItem
                  key={field.id}
                  name={`${choicesName}.${index}`}
                  item={field}
                  remove={() => remove(index)}
                />
              ))}
            </Flex>
          </SortableContext>
        </DndContext>
      </Box>
      {hasOption && (
        <Box my={rem(10)}>
          <Text mb={rem(5)}>{t('defaultOption')}</Text>
          {isMulti ? (
            <OptionSelect
              defaultValue={defaultOption || []}
              data={controlledFields}
              onChange={(val) => {
                setValue(`${OptionsName}.defaultValue`, val);
              }}
              placeholder={t('notSelected')}
            />
          ) : (
            <CustomSelect
              defaultValue={defaultOption || selectOptions?.[0].value}
              options={selectOptions}
              onChange={(val) => {
                setValue(`${OptionsName}.defaultValue`, val === notSelectedValue ? '' : val);
              }}
              search={false}
              notSelectedLabel={t('notSelected')}
            />
          )}
        </Box>
      )}
    </>
  );
};
