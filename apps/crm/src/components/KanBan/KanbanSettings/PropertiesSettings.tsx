import { MAX_VISIBLE_KANBAN_CARD_FIELDS } from '@/constants';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { ViewAPI } from '@/services/api';
import { customNotificationStyles } from '@/utils/workspace';
import {
  DndContext,
  type DragEndEvent,
  PointerSensor,
  closestCenter,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { arrayMove } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  ActionIcon,
  Box,
  Button,
  Group,
  ScrollArea,
  Stack,
  Text,
  TextInput,
  rem,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { notifications } from '@mantine/notifications';
import { DecaTooltip, FieldTypes, type View as TableView } from '@resola-ai/ui/components';
import {
  IconAlertCircle,
  IconCircleCheck,
  IconEye,
  IconEyeOff,
  IconGripVertical,
  IconSearch,
} from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useOptions } from '../../FieldSettings/AddFieldForm/useOptions';

const useStyles = createStyles((theme) => ({
  container: {
    padding: rem(16),
    minWidth: rem(400),
    maxWidth: rem(500),
  },
  section: {
    marginBottom: rem(24),
  },
  sectionTitle: {
    fontSize: rem(14),
    fontWeight: 600,
    marginBottom: rem(12),
    color: theme.colors.decaNavy[6],
  },
  searchSection: {
    marginBottom: rem(20),
  },
  fieldItem: {
    padding: rem(8),
    marginBottom: rem(6),
    display: 'flex',
    alignItems: 'center',
    gap: rem(8),
    borderRadius: theme.radius.sm,
  },
  dragHandle: {
    color: theme.colors.decaGrey[4],
    cursor: 'grab',
    '&:hover': {
      color: theme.colors.decaGrey[6],
    },
    '&:active': {
      cursor: 'grabbing',
    },
  },
  fieldIcon: {
    color: theme.colors.decaGrey[6],
  },
  fieldName: {
    flex: 1,
  },
  eyeButton: {
    color: theme.colors.decaGrey[4],
    '&:hover': {
      color: theme.colors.decaBlue[6],
    },
  },
  hiddenFieldItem: {
    opacity: 0.6,
  },
  sectionGroups: {
    backgroundColor: theme.colors.decaLight[0],
    borderRadius: theme.radius.md,
    padding: theme.spacing.sm,
    minHeight: rem(60),
  },
  linkButton: {
    color: theme.colors.decaBlue[6],
    fontSize: rem(14),
    textDecoration: 'none',
    cursor: 'pointer',
    '&:hover': {
      textDecoration: 'underline',
    },
  },
  footer: {
    display: 'flex',
    justifyContent: 'flex-end',
    gap: rem(12),
    marginTop: rem(24),
    paddingTop: rem(16),
    borderTop: `1px solid ${theme.colors.decaLight[3]}`,
  },
}));

interface FieldItem {
  id: string;
  name: string;
  type: string;
  icon?: React.ReactElement;
  visible: boolean;
}

const EMPTY_ARRAY: string[] = [];

const SortableFieldItem: React.FC<{
  field: FieldItem;
  onToggleVisibility: (fieldId: string) => void;
  isHidden?: boolean;
  disableDragDrop?: boolean;
  disableToggle?: boolean;
}> = ({
  field,
  onToggleVisibility,
  isHidden = false,
  disableDragDrop = false,
  disableToggle = false,
}) => {
  const { classes } = useStyles();
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: field.id,
    disabled: disableDragDrop,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <Box
      ref={disableDragDrop ? undefined : setNodeRef}
      style={disableDragDrop ? undefined : style}
      className={`${classes.fieldItem} ${isHidden ? classes.hiddenFieldItem : ''}`}
    >
      {!disableDragDrop && (
        <ActionIcon
          className={classes.dragHandle}
          variant='transparent'
          size='sm'
          {...attributes}
          {...listeners}
        >
          <IconGripVertical size={16} />
        </ActionIcon>
      )}

      {disableDragDrop && <Box w={rem(32)} h={rem(32)} />}

      <Box className={classes.fieldIcon}>
        {field.icon ? React.cloneElement(field.icon, { size: 16 }) : null}
      </Box>

      <DecaTooltip label={field.name}>
        <Text className={classes.fieldName} size='sm' truncate>
          {field.name}
        </Text>
      </DecaTooltip>

      <ActionIcon
        className={classes.eyeButton}
        variant='transparent'
        size='sm'
        onClick={() => !disableToggle && onToggleVisibility(field.id)}
        disabled={disableToggle}
        style={{
          opacity: disableToggle ? 0.4 : 1,
          cursor: disableToggle ? 'not-allowed' : 'pointer',
        }}
      >
        {field.visible ? <IconEye size={16} /> : <IconEyeOff size={16} />}
      </ActionIcon>
    </Box>
  );
};

interface PropertiesSettingsProps {
  onSave?: (fieldOrder: string[]) => void;
  onCancel?: () => void;
  initialFieldOrder?: string[];
}

const PropertiesSettings: React.FC<PropertiesSettingsProps> = ({
  onSave,
  onCancel,
  initialFieldOrder = EMPTY_ARRAY,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate('workspace');
  const { object, activeView, mutateView } = useWorkspaceContext();
  const { options: fieldTypeOptions } = useOptions();
  const { wsId, id: objId } = useParams();

  // State
  const [searchQuery, setSearchQuery] = useState('');

  // LocalStorage key for hidden fields order
  const hiddenFieldsStorageKey = useMemo(() => {
    if (!wsId || !objId || !activeView?.id) return null;
    return `kanban-hidden-fields-${wsId}-${objId}-${activeView.id}`;
  }, [wsId, objId, activeView?.id]);

  // Save hidden fields order to localStorage
  const saveHiddenFieldsOrder = useCallback(
    (hiddenFields: FieldItem[]) => {
      if (!hiddenFieldsStorageKey) return;

      try {
        const hiddenFieldsOrder = hiddenFields.map((f) => f.id);
        localStorage.setItem(hiddenFieldsStorageKey, JSON.stringify(hiddenFieldsOrder));
      } catch (error) {
        console.warn('Failed to save hidden fields order to localStorage:', error);
      }
    },
    [hiddenFieldsStorageKey]
  );

  // Load hidden fields order from localStorage
  const loadHiddenFieldsOrder = useCallback((): string[] => {
    if (!hiddenFieldsStorageKey) return [];

    try {
      const stored = localStorage.getItem(hiddenFieldsStorageKey);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.warn('Failed to load hidden fields order from localStorage:', error);
      return [];
    }
  }, [hiddenFieldsStorageKey]);

  // Set up drag sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    })
  );

  // Derive initial fields from object using useMemo
  const initialFields = useMemo(() => {
    if (!object?.fields) return [];

    // Get available fields (excluding system fields like in KanbanConfigModal)
    const availableFields = object.fields.filter(
      (field) =>
        field.type !== FieldTypes.CREATED_TIME &&
        field.type !== FieldTypes.MODIFIED_TIME &&
        field.type !== FieldTypes.CREATED_BY &&
        field.type !== FieldTypes.MODIFIED_BY &&
        activeView?.kanban?.swimlaneFieldId !== field.id
    );

    // Create field items with icons
    const fieldItems = availableFields.map((field, index) => {
      const fieldTypeOption = fieldTypeOptions.find((option) => option.value === field.type);
      const isVisible = index < MAX_VISIBLE_KANBAN_CARD_FIELDS; // First 5 are visible by default

      // If we have initial field order, respect it
      let visible = isVisible;
      if (initialFieldOrder.length > 0) {
        visible = initialFieldOrder.includes(field.id!);
      }

      return {
        id: field.id!,
        name: field.name || field.id!,
        type: field.type,
        icon: fieldTypeOption?.icon,
        visible,
      };
    });

    // Sort fields: visible first (in order), then hidden (respecting localStorage order)
    const visibleFields = fieldItems.filter((f) => f.visible);
    const hiddenFields = fieldItems.filter((f) => !f.visible);

    // Apply localStorage order to hidden fields
    const storedHiddenOrder = loadHiddenFieldsOrder();
    if (storedHiddenOrder.length > 0) {
      // Sort hidden fields according to stored order
      const orderedHiddenFields: FieldItem[] = [];
      const unorderedHiddenFields: FieldItem[] = [];

      // First, add fields in the stored order
      storedHiddenOrder.forEach((fieldId) => {
        const field = hiddenFields.find((f) => f.id === fieldId);
        if (field) {
          orderedHiddenFields.push(field);
        }
      });

      // Then add any new hidden fields that weren't in the stored order
      hiddenFields.forEach((field) => {
        if (!storedHiddenOrder.includes(field.id)) {
          unorderedHiddenFields.push(field);
        }
      });

      return [...visibleFields, ...orderedHiddenFields, ...unorderedHiddenFields];
    }

    return [...visibleFields, ...hiddenFields];
  }, [object?.fields, fieldTypeOptions, initialFieldOrder, loadHiddenFieldsOrder]);

  // Local fields state (initialized from derived fields)
  const [fields, setFields] = useState<FieldItem[]>(() => initialFields);

  // Update local state when initial fields change
  useEffect(() => {
    setFields(initialFields);
  }, [initialFields]);

  // Save card properties order to API
  const saveFieldOrderToAPI = useCallback(
    async (fieldOrder: string[]) => {
      if (!activeView || !wsId || !objId) {
        console.warn('Missing required parameters for saving card properties order');
        return;
      }

      try {
        // Create view payload with updated field order
        const viewPayload: TableView = {
          ...activeView,
          fieldOrder: fieldOrder,
        };

        // Save the view configuration using ViewAPI
        const updatedView = await ViewAPI.update(wsId, objId, activeView.id, viewPayload);

        // Soft update the view data using the response (avoid additional API call)
        await mutateView(updatedView, false);

        notifications.show({
          message: t('cardPropertiesOrderSaved', 'Card properties order saved successfully'),
          icon: <IconCircleCheck size={24} />,
          autoClose: 1500,
          styles: (theme: any) => customNotificationStyles(theme) as any,
        });
      } catch (error) {
        console.error('Failed to save card properties order:', error);
        notifications.show({
          message: t('cardPropertiesOrderSaveFailed', 'Failed to save card properties order'),
          icon: <IconAlertCircle size={24} />,
          autoClose: 3000,
          styles: (theme: any) =>
            customNotificationStyles(
              theme,
              theme.colors.decaRed[0],
              theme.colors.decaRed[9]
            ) as any,
        });
      }
    },
    [activeView, wsId, objId, t, mutateView]
  );

  // Handle toggle field visibility
  const handleToggleField = useCallback(
    (fieldId: string) => {
      setFields((prevFields) => {
        const fieldIndex = prevFields.findIndex((f) => f.id === fieldId);
        if (fieldIndex === -1) return prevFields;

        const field = prevFields[fieldIndex];
        const updatedField = { ...field, visible: !field.visible };

        // If making visible, check if we're at the limit
        if (updatedField.visible) {
          const visibleCount = prevFields.filter((f) => f.visible && f.id !== fieldId).length;
          if (visibleCount >= MAX_VISIBLE_KANBAN_CARD_FIELDS) {
            return prevFields; // Don't allow more than 5 visible
          }
        }

        const updatedFields = [...prevFields];
        updatedFields[fieldIndex] = updatedField;

        // Re-sort: visible first, then hidden
        const visible = updatedFields.filter((f) => f.visible);
        const hidden = updatedFields.filter((f) => !f.visible);
        const newFields = [...visible, ...hidden];

        // Save to API when visibility changes affect the visible section
        const visibleFieldOrder = visible.map((f) => f.id);
        saveFieldOrderToAPI(visibleFieldOrder);

        // Save hidden fields order to localStorage when field visibility changes
        saveHiddenFieldsOrder(hidden);

        return newFields;
      });
    },
    [saveFieldOrderToAPI, saveHiddenFieldsOrder]
  );

  // Handle drag end for visible fields
  const handleVisibleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      if (active.id !== over?.id) {
        setFields((prevFields) => {
          const visibleFields = prevFields.filter((f) => f.visible);
          const oldIndex = visibleFields.findIndex((item) => item.id === active.id);
          const newIndex = visibleFields.findIndex((item) => item.id === over?.id);

          if (oldIndex !== -1 && newIndex !== -1) {
            const reorderedVisible = arrayMove(visibleFields, oldIndex, newIndex);
            const hiddenFields = prevFields.filter((f) => !f.visible);
            const newFields = [...reorderedVisible, ...hiddenFields];

            // Save the new field order to API
            const visibleFieldOrder = reorderedVisible.map((f) => f.id);
            saveFieldOrderToAPI(visibleFieldOrder);

            return newFields;
          }
          return prevFields;
        });
      }
    },
    [saveFieldOrderToAPI]
  );

  // Handle drag end for hidden fields (local reordering only, no API call)
  const handleHiddenDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      if (active.id !== over?.id) {
        setFields((prevFields) => {
          const visibleFields = prevFields.filter((f) => f.visible);
          const hiddenFields = prevFields.filter((f) => !f.visible);
          const oldIndex = hiddenFields.findIndex((item) => item.id === active.id);
          const newIndex = hiddenFields.findIndex((item) => item.id === over?.id);

          if (oldIndex !== -1 && newIndex !== -1) {
            const reorderedHidden = arrayMove(hiddenFields, oldIndex, newIndex);

            // Save the new hidden fields order to localStorage
            saveHiddenFieldsOrder(reorderedHidden);

            return [...visibleFields, ...reorderedHidden];
          }
          return prevFields;
        });
      }
    },
    [saveHiddenFieldsOrder]
  );

  // Handle save
  const handleSave = useCallback(() => {
    const visibleFieldOrder = fields.filter((f) => f.visible).map((f) => f.id);
    onSave?.(visibleFieldOrder);
  }, [fields, onSave]);

  // Filter fields based on search
  const filteredFields = fields.filter((field) =>
    field.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const visibleFields = filteredFields.filter((f) => f.visible);
  const hiddenFields = filteredFields.filter((f) => !f.visible);

  return (
    <Box className={classes.container}>
      {/* Search Section */}
      <Box className={classes.searchSection}>
        <TextInput
          placeholder={t('searchForProperty', 'Search for a property')}
          leftSection={<IconSearch size={16} />}
          value={searchQuery}
          onChange={(event) => setSearchQuery(event.currentTarget.value)}
        />
      </Box>

      {/* Visible Fields Section */}
      <Box className={classes.section}>
        <Group justify='space-between' mb={rem(12)}>
          <Text className={classes.sectionTitle}>
            {t('shownInKanbanView', 'Shown in Kanban view')}
          </Text>
        </Group>

        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleVisibleDragEnd}
        >
          <SortableContext
            items={visibleFields.map((f) => f.id)}
            strategy={verticalListSortingStrategy}
          >
            <Stack gap={rem(4)} className={classes.sectionGroups}>
              {visibleFields.map((field) => (
                <SortableFieldItem
                  key={field.id}
                  field={field}
                  onToggleVisibility={handleToggleField}
                />
              ))}
              {visibleFields.length === 0 && (
                <Text size='sm' c='dimmed' ta='center' py={rem(20)}>
                  {t('noVisibleFields', 'No visible fields')}
                </Text>
              )}
            </Stack>
          </SortableContext>
        </DndContext>
      </Box>

      {/* Hidden Fields Section */}
      {hiddenFields.length > 0 && (
        <Box className={classes.section}>
          <Group justify='space-between' mb={rem(12)}>
            <Text className={classes.sectionTitle}>
              {t('hiddenInKanbanView', 'Hidden in Kanban view')}
            </Text>
          </Group>

          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleHiddenDragEnd}
          >
            <SortableContext
              items={hiddenFields.map((f) => f.id)}
              strategy={verticalListSortingStrategy}
            >
              <ScrollArea.Autosize mah={rem(300)}>
                <Stack gap={rem(4)} className={classes.sectionGroups}>
                  {hiddenFields.map((field) => (
                    <SortableFieldItem
                      key={field.id}
                      field={field}
                      onToggleVisibility={handleToggleField}
                      isHidden
                      disableDragDrop={false}
                      disableToggle={visibleFields.length >= MAX_VISIBLE_KANBAN_CARD_FIELDS}
                    />
                  ))}
                </Stack>
              </ScrollArea.Autosize>
            </SortableContext>
          </DndContext>
        </Box>
      )}

      {/* Footer */}
      {(onSave || onCancel) && (
        <Box className={classes.footer}>
          <Button variant='subtle' onClick={onCancel}>
            {t('cancel', 'Cancel')}
          </Button>
          <Button onClick={handleSave}>{t('save', 'Save')}</Button>
        </Box>
      )}
    </Box>
  );
};

export default memo(PropertiesSettings);
