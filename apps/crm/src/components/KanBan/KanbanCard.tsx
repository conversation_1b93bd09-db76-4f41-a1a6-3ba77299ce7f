import { MAX_VISIBLE_KANBAN_CARD_FIELDS } from '@/constants';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { Record as CRMRecord } from '@/models';
import type { ObjectColumn } from '@/models/workspace';
import { RecordAPI } from '@/services/api';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { ActionIcon, Box, Flex, Menu, Paper, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import ConfirmModal from '@resola-ai/ui/components/ConfirmModal';
import {
  IconArrowsRightDown,
  IconChevronRight,
  IconCopy,
  IconDots,
  IconTrash,
} from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { memo, useCallback, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import Cell from '../TableCellRendering/Cell';

const useStyles = createStyles((theme) => ({
  card: {
    backgroundColor: theme.white,
    borderRadius: rem(8),
    padding: rem(16),
    cursor: 'pointer',
    border: `1px solid ${theme.colors.decaLight[2]}`,
    transition: 'all 0.2s ease',
    marginBottom: 0,
    position: 'relative',
    '&:hover': {
      borderColor: theme.colors.decaBlue[3],
      '& .card-menu': {
        opacity: 1,
        visibility: 'visible',
      },
    },
  },
  dragging: {
    opacity: 0.8,
    boxShadow: theme.shadows.lg,
    borderColor: theme.colors.decaBlue[5],
  },
  dragHandle: {
    color: theme.colors.decaGrey[3],
    cursor: 'grab',
    padding: rem(4),
    borderRadius: rem(4),
    transition: 'all 0.2s ease',
    '&:hover': {
      color: theme.colors.decaGrey[5],
      backgroundColor: theme.colors.decaLight[1],
    },
    '&:active': {
      cursor: 'grabbing',
    },
  },
  fieldContainer: {
    marginBottom: rem(12),
    '&:last-child': {
      marginBottom: 0,
    },
  },
  fieldValue: {
    fontSize: rem(13),
    color: theme.colors.decaNavy[7],
    '& .mantine-Text-root': {
      fontSize: rem(13),
    },
  },
  menuButton: {
    position: 'absolute',
    top: rem(8),
    right: rem(8),
    opacity: 0,
    visibility: 'hidden',
    transition: 'all 0.2s ease',
    backgroundColor: theme.white,
    '&:hover': {
      backgroundColor: theme.colors.decaLight[0],
    },
  },
  menuItem: {
    width: rem(180),
    padding: `${rem(8)} ${rem(12)}`,
    fontSize: 14,
    color: theme.colors.decaGrey[9],
    fontWeight: 400,
  },
}));

interface RecordStage {
  id: string;
  label: string;
  color: string;
}

interface KanbanCardProps {
  record: CRMRecord;
  isDragging?: boolean;
  recordStages?: RecordStage[];
  stageId?: string;
  onDuplicate?: (recordId: string) => void;
  onMove?: (recordId: string, targetStageId: string) => void;
}

const KanbanCard: React.FC<KanbanCardProps> = ({
  record,
  isDragging = false,
  recordStages = [],
  stageId,
  onDuplicate,
  onMove,
}) => {
  const { classes, cx } = useStyles();
  const { object, activeView, openProfile, mutateRecord } = useWorkspaceContext();
  const [menuOpened, setMenuOpened] = useState(false);
  const [deleteModalOpened, setDeleteModalOpened] = useState(false);
  const { t } = useTranslate('workspace');
  const { wsId, id: objId } = useParams();
  // Set up sortable functionality
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({ id: record.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };
  // Get card fields from view fieldOrder (first 5 fields)
  const cardFieldIds = useMemo(() => {
    const fieldOrder = activeView?.fieldOrder || [];
    return fieldOrder.slice(0, MAX_VISIBLE_KANBAN_CARD_FIELDS);
  }, [activeView?.fieldOrder]);

  // Get fields for card display - optimized for virtual rendering
  const displayFields = useMemo(() => {
    if (!object?.fields || !cardFieldIds || !activeView?.fieldOrder) return [];

    return cardFieldIds
      .map((fieldId) => object.fields?.find((field) => field.id === fieldId))
      .filter(Boolean) as ObjectColumn[];
  }, [object?.fields, cardFieldIds, activeView?.fieldOrder]);

  // Create cell value object for rendering - optimized for virtual rendering
  const createCellValue = useCallback(
    (field: ObjectColumn, value: any) => {
      return {
        getValue: () => value,
        column: { id: field.id },
        row: { original: record },
      };
    },
    [record]
  );

  // Handle card click - optimized for virtual rendering
  const handleCardClick = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      openProfile(record.id, record);
    },
    [record.id, openProfile, record]
  );

  // Handle menu actions
  const handleDuplicate = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      setMenuOpened(false);
      onDuplicate?.(record.id);
    },
    [record.id, onDuplicate]
  );

  const handleMove = useCallback(
    (targetStageId: string) => {
      setMenuOpened(false);
      onMove?.(record.id, targetStageId);
    },
    [record.id, onMove]
  );

  const handleDelete = useCallback(() => {
    setMenuOpened(false);
    setDeleteModalOpened(true);
  }, []);

  const handleConfirmDelete = useCallback(async () => {
    setDeleteModalOpened(false);

    if (!wsId || !objId) {
      return;
    }

    try {
      await RecordAPI.delete(wsId, objId, record.id);

      // Soft mutate to remove the record from the UI
      mutateRecord((currData: any) => {
        if (!currData || !currData.length) return currData;

        // Clone the current data to avoid mutations
        const updatedData = [...currData];

        // Update the first page by removing the deleted record
        const firstPage = { ...updatedData[0] };
        if (firstPage.kanbanRecords) {
          const updatedKanbanRecords = { ...firstPage.kanbanRecords };

          // Find and remove the record from its stage
          for (const stageId in updatedKanbanRecords) {
            updatedKanbanRecords[stageId] = updatedKanbanRecords[stageId].filter(
              (recordItem) => recordItem.id !== record.id
            );
          }

          firstPage.kanbanRecords = updatedKanbanRecords;
        }

        updatedData[0] = firstPage;
        return updatedData;
      }, false); // false prevents revalidation
    } catch (error) {
      console.error('Failed to delete record:', error);
    }
  }, [record.id, wsId, objId, mutateRecord]);

  return (
    <>
      <Paper
        ref={setNodeRef}
        style={style}
        className={cx(classes.card, {
          [classes.dragging]: isDragging || isSortableDragging,
        })}
        onClick={handleCardClick}
        data-testid='kanban-card'
        data-record-id={record.id}
        {...attributes}
        {...listeners}
      >
        {displayFields.map((field) => {
          const value = record[field.id || ''];
          const cellValue = createCellValue(field, value);

          return (
            <Box
              key={field.id}
              className={classes.fieldContainer}
              data-testid='kanban-card-field'
              data-field-id={field.id}
            >
              <Box className={classes.fieldValue}>
                <Cell cell={cellValue as any} type={field.type} column={field} />
              </Box>
            </Box>
          );
        })}

        <Menu
          opened={menuOpened}
          onChange={setMenuOpened}
          position='bottom-start'
          closeOnClickOutside
          withinPortal
        >
          <Menu.Target>
            <ActionIcon
              className={`${classes.menuButton} card-menu`}
              variant='subtle'
              size='sm'
              onClick={(e) => e.stopPropagation()}
              data-testid='kanban-card-menu-button'
            >
              <IconDots size={16} />
            </ActionIcon>
          </Menu.Target>

          <Menu.Dropdown onClick={(e) => e.stopPropagation()}>
            <Menu.Item
              className={classes.menuItem}
              leftSection={<IconCopy size={16} />}
              onClick={handleDuplicate}
              data-testid='kanban-card-duplicate'
            >
              {t('duplicate')}
            </Menu.Item>

            <Menu.Item
              className={classes.menuItem}
              leftSection={<IconArrowsRightDown size={16} />}
              data-testid='kanban-card-move'
              rightSection={<IconChevronRight size={16} />}
              onClick={(e) => e.stopPropagation()}
            >
              <Menu trigger='hover' position='right-start' offset={5} closeOnClickOutside={false}>
                <Menu.Target>
                  <Flex align='center' onClick={(e) => e.stopPropagation()}>
                    {t('moveTo')}
                  </Flex>
                </Menu.Target>
                <Menu.Dropdown>
                  {recordStages.map((stage) =>
                    stage.id !== stageId ? (
                      <Menu.Item
                        key={stage.id}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleMove(stage.id);
                        }}
                        data-testid={`kanban-card-move-${stage.id}`}
                      >
                        {stage.label}
                      </Menu.Item>
                    ) : null
                  )}
                </Menu.Dropdown>
              </Menu>
            </Menu.Item>

            <Menu.Item
              className={classes.menuItem}
              leftSection={<IconTrash size={16} />}
              c='red'
              onClick={(e) => {
                e.stopPropagation();
                handleDelete();
              }}
              data-testid='kanban-card-delete'
            >
              {t('delete')}
            </Menu.Item>
          </Menu.Dropdown>
        </Menu>
      </Paper>
      <ConfirmModal
        opened={deleteModalOpened}
        onClose={() => setDeleteModalOpened(false)}
        onConfirm={handleConfirmDelete}
        title={t('deleteCard')}
        message={t('deleteCardConfirm')}
        confirmText={t('confirm')}
        cancelText={t('cancel')}
        isRemoving={true}
        noSeparator={true}
        data-testid='kanban-card-delete-modal'
      />
    </>
  );
};

export default memo(KanbanCard);
