import type { Record as CRMRecord } from '@/models';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { useVirtualizer } from '@tanstack/react-virtual';
import { screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import KanbanCollection from './KanbanCollection';

// Mock dependencies
vi.mock('@dnd-kit/sortable');
vi.mock('@tanstack/react-virtual');
vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: () => ({
    mutateRecord: vi.fn(),
    loadMore: vi.fn(),
    hasMore: false,
    isLoadingMore: false,
  }),
}));
vi.mock('./KanbanCard', () => ({
  default: ({ record, onClick }: any) => (
    <button
      type='button'
      data-testid='kanban-card'
      data-record-id={record.id}
      onClick={onClick}
      style={{ background: 'none', border: 'none', padding: 0, cursor: 'pointer' }}
    >
      {record.title || record.name || record.id}
    </button>
  ),
}));

const mockSortableContext = SortableContext as any;
const mockUseVirtualizer = useVirtualizer as any;

// Mock ResizeObserver for virtualization
global.ResizeObserver = class MockResizeObserver {
  observe = vi.fn();
  unobserve = vi.fn();
  disconnect = vi.fn();
  constructor(public callback: ResizeObserverCallback) {}
};

describe('KanbanCollection', () => {
  const mockRecords: CRMRecord[] = [
    { id: 'record-1', title: 'Record 1', status: 'active' },
    { id: 'record-2', title: 'Record 2', status: 'active' },
    { id: 'record-3', title: 'Record 3', status: 'pending' },
  ] as CRMRecord[];

  const mockRecordStages = [
    {
      id: 'active',
      label: 'Active',
      color: 'blue',
      records: [mockRecords[0], mockRecords[1]],
    },
    {
      id: 'pending',
      label: 'Pending',
      color: 'yellow',
      records: [mockRecords[2]],
    },
    {
      id: 'completed',
      label: 'Completed',
      color: 'green',
      records: [],
    },
  ];

  const defaultProps = {
    recordStages: mockRecordStages,
    cardFieldIds: ['title', 'description'],
    statusField: { id: 'status' },
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock SortableContext
    mockSortableContext.mockImplementation(({ children }: any) => children);

    // Mock useVirtualizer with safe defaults
    mockUseVirtualizer.mockReturnValue({
      getVirtualItems: vi.fn(() => []),
      getTotalSize: vi.fn(() => 0),
      measureElement: vi.fn(),
    });

    // Mock DOM methods
    Object.defineProperty(HTMLElement.prototype, 'getBoundingClientRect', {
      configurable: true,
      value: () => ({
        width: 320,
        height: 600,
        top: 0,
        left: 0,
        right: 320,
        bottom: 600,
      }),
    });
  });

  describe('Component Rendering', () => {
    it('should render the main kanban collection container', () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      expect(screen.getByTestId('kanban-collection')).toBeInTheDocument();
      expect(screen.getByTestId('kanban-scroll-container')).toBeInTheDocument();
    });

    it('should render all kanban columns', () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      const columns = screen.getAllByTestId('kanban-column');
      expect(columns).toHaveLength(3);

      expect(columns[0]).toHaveAttribute('data-stage-id', 'active');
      expect(columns[1]).toHaveAttribute('data-stage-id', 'pending');
      expect(columns[2]).toHaveAttribute('data-stage-id', 'completed');
    });

    it('should render column headers with correct information', () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      const headers = screen.getAllByTestId('kanban-column-header');
      expect(headers).toHaveLength(3);

      const stageLabels = screen.getAllByTestId('stage-label');
      expect(stageLabels[0]).toHaveTextContent('Active');
      expect(stageLabels[1]).toHaveTextContent('Pending');
      expect(stageLabels[2]).toHaveTextContent('Completed');
    });

    it('should render stage indicators with correct colors', () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      const indicators = screen.getAllByTestId('stage-indicator');
      expect(indicators).toHaveLength(3);
    });


    it('should render column menu buttons', () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      const menuButtons = screen.getAllByTestId('column-menu-button');
      expect(menuButtons).toHaveLength(3);
    });
  });

  describe('Virtualization', () => {
    it('should initialize virtualizer for each column', async () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      await waitFor(() => {
        expect(mockUseVirtualizer).toHaveBeenCalled();
      });
    });

    it('should render virtual containers when virtualizer is ready', async () => {
      // Set up virtualizer mock for this test
      mockUseVirtualizer.mockReturnValue({
        getVirtualItems: vi.fn(() => [{ index: 0, start: 0, size: 150, key: '0' }]),
        getTotalSize: vi.fn(() => 150),
        measureElement: vi.fn(),
      });

      renderWithMantine(<KanbanCollection {...defaultProps} />);

      await waitFor(() => {
        const virtualContainers = screen.getAllByTestId('virtual-container');
        expect(virtualContainers.length).toBeGreaterThan(0);
      });
    });

    it('should handle virtualizer setup with correct parameters', () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      expect(mockUseVirtualizer).toHaveBeenCalledWith(
        expect.objectContaining({
          count: expect.any(Number),
          getScrollElement: expect.any(Function),
          estimateSize: expect.any(Function),
          overscan: 5,
        })
      );
    });

    it('should handle ResizeObserver for container detection', () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      // ResizeObserver is available in the test environment
      expect(window.ResizeObserver).toBeDefined();
    });
  });

  describe('Records and Cards Rendering', () => {
    it('should verify virtualizer is set up for rendering', () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      // Verify that virtualizer is initialized for each column
      expect(mockUseVirtualizer).toHaveBeenCalled();
    });

    it('should have record data available for rendering', () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      // Verify that record stages contain the expected data
      expect(defaultProps.recordStages).toHaveLength(3);
      expect(defaultProps.recordStages[0].records).toHaveLength(2);
      expect(defaultProps.recordStages[1].records).toHaveLength(1);
      expect(defaultProps.recordStages[2].records).toHaveLength(0);
    });

    it('should pass correct card field IDs to components', () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      // Verify that card field IDs are properly passed
      expect(defaultProps.cardFieldIds).toEqual(['title', 'description']);
    });
  });

  describe('Add Buttons', () => {
    it('should have add button functionality configured', () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      // Verify that the statusField prop is passed correctly
      expect(defaultProps.statusField).toBeDefined();
      expect(typeof defaultProps.statusField).toBe('object');
    });

    it('should configure add buttons for each stage', () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      // Each stage should have the potential for an add button
      expect(defaultProps.recordStages).toHaveLength(3);
      defaultProps.recordStages.forEach((stage) => {
        expect(stage.id).toBeDefined();
        expect(stage.label).toBeDefined();
      });
    });

    it('should handle create record functionality', () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      // Test that we can call the create record function
      expect(defaultProps.statusField.id).toBeDefined();
    });
  });

  describe('Scroll Handling', () => {
    it('should have proper scroll container structure', () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      const scrollContainer = screen.getByTestId('kanban-scroll-container');
      expect(scrollContainer).toBeInTheDocument();
    });

    it('should handle unified scroll for all columns', () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      const scrollContainer = screen.getByTestId('kanban-scroll-container');
      expect(scrollContainer).toBeInTheDocument();

      // The scroll container should be passed to virtualizers
      expect(mockUseVirtualizer).toHaveBeenCalledWith(
        expect.objectContaining({
          getScrollElement: expect.any(Function),
        })
      );
    });
  });

  describe('Drag and Drop Integration', () => {
    it('should initialize SortableContext', () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      // SortableContext should be available for use (imported)
      expect(mockSortableContext).toBeDefined();
    });

    it('should use vertical list sorting strategy', () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      // Verify that the strategy is imported and available
      expect(verticalListSortingStrategy).toBeDefined();
    });

    it('should handle drag and drop for record management', () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      // Component should have drag and drop infrastructure in place
      expect(verticalListSortingStrategy).toBeDefined();
      expect(mockSortableContext).toBeDefined();
    });
  });

  describe('Empty Stages', () => {
    it('should render empty stages with only add button', () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      // Completed stage should be empty
      const columns = screen.getAllByTestId('kanban-column');
      const completedColumn = columns.find(
        (col) => col.getAttribute('data-stage-id') === 'completed'
      );
      expect(completedColumn).toBeInTheDocument();
    });

  });

  describe('Component Performance', () => {
    it('should handle large numbers of records efficiently', () => {
      const largeRecordStages = mockRecordStages.map((stage) => ({
        ...stage,
        records: Array.from({ length: 100 }, (_, i) => ({
          id: `${stage.id}-record-${i}`,
          title: `Record ${i}`,
          status: stage.id,
        })) as CRMRecord[],
      }));

      expect(() => {
        renderWithMantine(<KanbanCollection {...defaultProps} recordStages={largeRecordStages} />);
      }).not.toThrow();
    });

    it('should handle stage reordering', () => {
      const reorderedStages = [...mockRecordStages].reverse();

      renderWithMantine(<KanbanCollection {...defaultProps} recordStages={reorderedStages} />);

      const stageLabels = screen.getAllByTestId('stage-label');
      expect(stageLabels[0]).toHaveTextContent('Completed');
      expect(stageLabels[1]).toHaveTextContent('Pending');
      expect(stageLabels[2]).toHaveTextContent('Active');
    });
  });

  describe('Error Handling', () => {
    it('should handle missing recordStages gracefully', () => {
      expect(() => {
        renderWithMantine(<KanbanCollection {...defaultProps} recordStages={[]} />);
      }).not.toThrow();
    });

    it('should handle missing card field IDs', () => {
      expect(() => {
        renderWithMantine(<KanbanCollection {...defaultProps} />);
      }).not.toThrow();
    });

    it('should handle null theme gracefully', () => {
      // Component should handle missing theme by having fallbacks
      const propsWithNullTheme = { ...defaultProps, theme: null as any };

      // The component should gracefully handle null theme
      expect(propsWithNullTheme.theme).toBeNull();
    });

    it('should handle virtualizer initialization failures', () => {
      mockUseVirtualizer.mockReturnValue({
        getVirtualItems: () => [],
        getTotalSize: () => 0,
        measureElement: vi.fn(),
      });

      expect(() => {
        renderWithMantine(<KanbanCollection {...defaultProps} />);
      }).not.toThrow();
    });
  });

  describe('Accessibility', () => {
    it('should have proper test IDs for all elements', () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      expect(screen.getByTestId('kanban-collection')).toBeInTheDocument();
      expect(screen.getByTestId('kanban-scroll-container')).toBeInTheDocument();
      expect(screen.getAllByTestId('kanban-column')).toHaveLength(3);
      expect(screen.getAllByTestId('kanban-column-header')).toHaveLength(3);
      expect(screen.getAllByTestId('kanban-column-content')).toHaveLength(3);
    });

    it('should provide stage-specific data attributes', () => {
      renderWithMantine(<KanbanCollection {...defaultProps} />);

      const columns = screen.getAllByTestId('kanban-column');
      expect(columns[0]).toHaveAttribute('data-stage-id', 'active');
      expect(columns[1]).toHaveAttribute('data-stage-id', 'pending');
      expect(columns[2]).toHaveAttribute('data-stage-id', 'completed');
    });
  });
});
