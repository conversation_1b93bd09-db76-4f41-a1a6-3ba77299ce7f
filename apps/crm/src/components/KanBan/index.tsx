import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { Record as CRMRecord } from '@/models';
import ComponentUtils from '@/utils/component';
import { DndContext, PointerSensor, closestCorners, useSensor, useSensors } from '@dnd-kit/core';
import { SortableContext, horizontalListSortingStrategy } from '@dnd-kit/sortable';
import { Box, Flex, Group, LoadingOverlay, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { Modal } from '@resola-ai/ui';
import { MenuViewToolbarItem } from '@resola-ai/ui/components/DecaTable/components/Toolbar/MenuView';
import { SearchBoxToolbarItem } from '@resola-ai/ui/components/DecaTable/components/Toolbar/SearchBox';
import { SelectViewToolbarItem } from '@resola-ai/ui/components/DecaTable/components/Toolbar/SelectView';
import { TableFilterToolbarItem } from '@resola-ai/ui/components/DecaTable/components/Toolbar/TableFilter';
import { TableSortToolbarItem } from '@resola-ai/ui/components/DecaTable/components/Toolbar/TableSort';
import { PERMISSION_KEYS, isPermissionAllowed } from '@resola-ai/ui/components/DecaTable/utils';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback, useMemo, useState } from 'react';
import KanbanCollection from './KanbanCollection';
import GroupSettings from './KanbanSettings/GroupSettings';
import PropertiesSettings from './KanbanSettings/PropertiesSettings';

const KanbanFirstSetup = ComponentUtils.lazy(() => import('./KanbanFirstSetup'));

const useStyles = createStyles((theme) => ({
  kanbanBoard: {
    height: '100%',
    width: '100%',
    backgroundColor: theme.colors.decaLight[0],
    overflow: 'auto',
    paddingLeft: rem(8),
  },
  emptyState: {
    height: '60vh',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    gap: rem(16),
  },
  readOnlyBanner: {
    backgroundColor: theme.colors.decaYellow[0],
    color: theme.colors.decaYellow[9],
    padding: rem(12),
    borderRadius: theme.radius.md,
    marginBottom: rem(16),
    textAlign: 'center',
    border: `1px solid ${theme.colors.decaYellow[3]}`,
  },
}));

interface RecordStage {
  id: string;
  label: string;
  color: string;
  records: CRMRecord[];
}

const KanbanBoard: React.FC = () => {
  const { classes, theme } = useStyles();
  const { t } = useTranslate('workspace');
  const {
    object,
    activeView,
    views,
    handleViewChange,
    handleApplyManageView,
    textSearch,
    handleSearch,
    viewLoading,
    viewGroups,
    // Kanban-specific data from workspace context
    kanbanRecords,
    swimlaneFieldId,
    recordsLoading,
    loading,
  } = useWorkspaceContext();

  const [openedFilterMenu, { close: closeFilterMenu, open: openFilterMenu }] = useDisclosure(false);
  const [openedSortMenu, { close: closeSortMenu, open: openSortMenu }] = useDisclosure(false);

  // Modal state for SelectView components
  const [modalOpened, { open: openModal, close: closeModal }] = useDisclosure(false);
  const [modalProps, setModalProps] = useState<{
    title: string;
    message: string;
    onConfirm: () => void;
    isRemoving?: boolean;
  } | null>(null);

  // State for toolbar items
  const [selectedItem, setSelectedItem] = useState<string>('');

  // Modal handler for SelectView components
  const handleOpenModal = useCallback(
    (props: {
      title: string;
      message: string | React.ReactNode;
      onConfirm: () => void;
      onCancel?: () => void;
      isRemoving?: boolean;
    }) => {
      setModalProps({
        title: props.title,
        message: typeof props.message === 'string' ? props.message : String(props.message),
        onConfirm: props.onConfirm,
        isRemoving: props.isRemoving,
      });
      openModal();
    },
    [openModal]
  );

  // Wrap handleViewChange to return a Promise
  const handleViewChangeAsync = useCallback(
    async (id: string, view: any, type?: string) => {
      if (handleViewChange) {
        return Promise.resolve(handleViewChange(id, view, type));
      }
      return Promise.resolve();
    },
    [handleViewChange]
  );

  // Set up drag sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // Check permissions
  const canEdit = useMemo(() => {
    if (!object?.permission) return false;
    return isPermissionAllowed(object.permission, PERMISSION_KEYS.OBJECT_UPDATE);
  }, [object?.permission]);

  const canRead = useMemo(() => {
    if (!object?.permission) return false;
    return isPermissionAllowed(object.permission, PERMISSION_KEYS.OBJECT_READ);
  }, [object?.permission]);

  // Create a tuned-for-toolbar view to add proper field names and types for filter/sort functionality
  const toolbarView = useMemo((): any => {
    if (activeView) {
      return {
        ...activeView,
        // Ensure fields have proper structure for filter/sort
        fields: (activeView.fields || []).map((field) => {
          // Cross-reference with object fields to get proper field names and types
          const fieldId = field.fieldMetaId || field.id;
          const objectField = object?.fields?.find((objField) => objField.id === fieldId);

          // Get a proper display name - prioritize object field name over view field properties
          const displayName =
            objectField?.name || // From object field definition
            field.header || // From view field
            (field as any).name ||
            (field as any).label ||
            '';

          // Get the proper field type from object field definition (crucial for filter functionality)
          const fieldType = objectField?.type || field.type || 'text';

          return {
            ...field,
            fieldMetaId: fieldId,
            header: displayName,
            isVisible: field.isVisible !== false,
            type: fieldType,
            // Include additional properties from object field for proper filter/sort functionality
            ...(objectField && {
              name: objectField.name,
              options: objectField.options,
              description: objectField.description,
              isProtected: objectField.isProtected,
            }),
          };
        }),
      };
    }

    return undefined;
  }, [activeView, object?.fields]);

  // Extract Kanban configuration from current activeView only
  const kanbanConfig = useMemo((): {
    swimlaneFieldId: string;
    swimlaneOrder: { id: string; active: boolean }[];
  } | null => {
    // Only use kanban config if the current activeView has proper kanban configuration
    if (activeView?.kanban?.swimlaneFieldId) {
      const config = {
        swimlaneFieldId: activeView.kanban.swimlaneFieldId,
        swimlaneOrder: activeView.kanban.swimlaneOrder || [],
      };
      return config;
    }
    return null;
  }, [activeView?.kanban?.swimlaneFieldId, activeView?.kanban?.swimlaneOrder]);

  // Check if we have a valid Kanban configuration
  const hasKanbanConfig = !!kanbanConfig?.swimlaneFieldId;

  // Find status field to determine deal stages
  const statusField = useMemo(() => {
    if (!hasKanbanConfig || !object?.fields || !activeView?.kanban?.swimlaneFieldId) return null;

    return object.fields.find((field) => field.id === kanbanConfig.swimlaneFieldId);
  }, [object?.fields, kanbanConfig, hasKanbanConfig, activeView?.kanban?.swimlaneFieldId]);

  // Group deals by status/stage with proper ordering
  const recordStages = useMemo((): RecordStage[] => {
    if (
      !statusField?.options?.choices ||
      !kanbanRecords ||
      !hasKanbanConfig ||
      !activeView?.kanban?.swimlaneFieldId
    ) {
      return [];
    }

    // Ensure the workspace kanban data matches the current view's swimlane field
    if (swimlaneFieldId !== activeView.kanban.swimlaneFieldId) {
      return [];
    }

    // Create the "other" stage for unassigned records (always first)
    const otherStage: RecordStage = {
      id: 'other',
      label: t('unassigned', 'Unassigned'),
      color: 'grey', // Gray color for unassigned records - matches ColorCodes
      records: (kanbanRecords as any)?.other || [],
    };

    const stages = statusField.options.choices.map((choice) => ({
      id: choice.id,
      label: choice.label,
      color: choice.color,
      records: kanbanRecords[choice.id] || [],
    }));

    // Apply custom column order if specified in swimlaneOrder
    if (kanbanConfig.swimlaneOrder && kanbanConfig.swimlaneOrder.length > 0) {
      const orderedStages = kanbanConfig.swimlaneOrder
        .filter((swimlaneItem) => swimlaneItem.active) // Only include active (visible) columns
        .map((swimlaneItem) => stages.find((stage) => stage.id === swimlaneItem.id))
        .filter(Boolean) as RecordStage[];

      // Add any remaining stages that weren't in the swimlaneOrder (as active by default)
      const swimlaneIds = kanbanConfig.swimlaneOrder.map((item) => item.id);
      const remainingStages = stages.filter((stage) => !swimlaneIds.includes(stage.id));

      return [...orderedStages, ...remainingStages, otherStage];
    }

    // Always put "other" stage last
    return [...stages, otherStage];
  }, [
    statusField,
    kanbanRecords,
    hasKanbanConfig,
    kanbanConfig,
    swimlaneFieldId,
    activeView?.kanban?.swimlaneFieldId,
  ]);

  // Show loading state
  if (loading) {
    return (
      <Box className={classes.kanbanBoard} data-testid='kanban-loading'>
        <LoadingOverlay visible />
      </Box>
    );
  }

  // Show empty state if no read permission
  if (!canRead) {
    return (
      <Box className={classes.kanbanBoard} data-testid='kanban-no-permission'>
        <Flex className={classes.emptyState}>
          <Text size='lg' fw={500} c='dimmed'>
            {t('noPermissionToView', 'No permission to view')}
          </Text>
          <Text size='sm' c='dimmed' ta='center'>
            {t('contactAdminForAccess', 'Contact your administrator for access to this view')}
          </Text>
        </Flex>
      </Box>
    );
  }

  return (
    <Box
      style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
      data-testid='kanban-board'
    >
      {/* Kanban Toolbar */}
      <Box
        p={`${rem(8)} ${rem(4)}`}
        bg='white'
        style={{
          borderBottom: `1px solid ${theme.colors.decaLight[1]}`,
        }}
        data-testid='kanban-toolbar'
      >
        <Group justify='space-between'>
          {/* Left Toolbar Components */}
          <Group gap='xs'>
            <MenuViewToolbarItem
              useGroup
              onApply={handleApplyManageView}
              views={views}
              onViewChange={handleViewChangeAsync}
              viewGroups={viewGroups as any}
              objectPermissions={object?.permission}
              enablePermissions={true}
              currentView={toolbarView}
              objectSettings={object}
            />

            <SelectViewToolbarItem
              currentView={toolbarView}
              views={views}
              isViewLoading={viewLoading}
              objectPermissions={object?.permission}
              enablePermissions={true}
              onViewChange={handleViewChangeAsync}
              openModal={handleOpenModal}
              closeModal={closeModal}
              GroupComponent={GroupSettings}
              PropertiesComponent={PropertiesSettings}
            />

            <TableFilterToolbarItem
              opened={openedFilterMenu}
              onOpenChange={(opened) => (opened ? openFilterMenu() : closeFilterMenu())}
              currentView={toolbarView}
              onViewChange={handleViewChangeAsync}
              tagsFilter={[]} // TODO: Implement tags if needed for Kanban
              setSelectedItem={setSelectedItem}
              enablePermissions={true}
            />

            <TableSortToolbarItem
              opened={openedSortMenu}
              onOpenChange={(opened) => (opened ? openSortMenu() : closeSortMenu())}
              currentView={toolbarView}
              onViewChange={handleViewChangeAsync}
              setSelectedItem={setSelectedItem}
              selectedItem={selectedItem}
              enablePermissions={true}
            />
          </Group>

          {/* Right Toolbar Components */}
          <Group gap='xs'>
            <SearchBoxToolbarItem
              searchQuery={textSearch}
              onSearch={handleSearch}
              currentView={toolbarView}
              enablePermissions={true}
            />
          </Group>
        </Group>
      </Box>

      <Box className={classes.kanbanBoard} style={{ flex: 1 }} data-testid='kanban-content'>
        {/* Show empty state if no Kanban configuration */}
        {!hasKanbanConfig && !viewLoading && !recordsLoading ? (
          <KanbanFirstSetup />
        ) : (
          <>
            {/* Read-only banner */}
            {!canEdit && (
              <Box className={classes.readOnlyBanner} data-testid='read-only-banner'>
                <Text fw={500}>
                  {t('readOnlyMode', 'Read-only mode')} -{' '}
                  {t('cannotEditRecords', 'You cannot edit or move records')}
                </Text>
              </Box>
            )}

            <DndContext
              sensors={sensors}
              collisionDetection={closestCorners}
              data-testid='kanban-dnd-context'
            >
              <SortableContext
                items={recordStages.map((stage) => stage.id)}
                strategy={horizontalListSortingStrategy}
              >
                {/* Kanban Board with TanStack Dynamic Virtualization and Unified Scroll */}
                <KanbanCollection
                  recordStages={recordStages}
                  statusField={statusField}
                />
              </SortableContext>
            </DndContext>
          </>
        )}
      </Box>

      {/* Modal for SelectView actions */}
      <Modal
        opened={modalOpened}
        onClose={closeModal}
        onCancel={closeModal}
        cancelText={t('cancel', 'Cancel')}
        title={modalProps?.title}
        centered
        data-testid='select-view-modal'
        onOk={modalProps?.onConfirm}
        okText={t('confirm', 'Confirm')}
        okButtonProps={{ variant: modalProps?.isRemoving ? 'negative' : 'primary' }}
        footerDivider={false}
      >
        <Text mb='md'>{modalProps?.message}</Text>
      </Modal>
    </Box>
  );
};

export default KanbanBoard;
