import { ColorCodes } from '@/constants';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { Record as CRMRecord } from '@/models';
import { ObjectAPI, RecordAPI } from '@/services/api';
import {
  DndContext,
  type DragEndEvent,
  type DragOverEvent,
  DragOverlay,
  type DragStartEvent,
  PointerSensor,
  useDroppable,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { ActionIcon, Box, Flex, Menu, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { IconDots, IconEyeOff, IconPlus, IconTrash } from '@tabler/icons-react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import KanbanCard from './KanbanCard';

// Styles
const useStyles = createStyles((theme) => ({
  mainContainer: {
    height: 'calc(100vh - 130px)',
    width: '100%',
    position: 'relative',
    overflow: 'hidden',
  },

  scrollContainer: {
    height: '100%',
    width: '100%',
    overflowY: 'auto',
    overflowX: 'auto',
    display: 'flex',
    alignItems: 'flex-start',
    paddingRight: rem(24),
    scrollbarWidth: 'thin',
    scrollbarColor: `${theme.colors.decaGrey[3]} transparent`,

    '&::-webkit-scrollbar': {
      width: rem(8),
      height: rem(8),
    },
    '&::-webkit-scrollbar-track': {
      background: 'transparent',
    },
    '&::-webkit-scrollbar-thumb': {
      background: theme.colors.decaGrey[3],
      borderRadius: rem(4),
      '&:hover': {
        background: theme.colors.decaGrey[4],
      },
    },
    '&::-webkit-scrollbar-corner': {
      background: 'transparent',
    },
  },

  kanbanColumn: {
    width: rem(320),
    minHeight: '100%',
    display: 'flex',
    flexDirection: 'column',
    marginRight: rem(16),
    backgroundColor: theme.colors.decaLight[0],
    borderRadius: rem(8),
    transition: 'all 0.2s ease',
  },

  kanbanColumnDropping: {
    backgroundColor: theme.colors.decaBlue[0],
    borderColor: theme.colors.decaBlue[3],
    border: `2px dashed ${theme.colors.decaBlue[3]}`,
  },

  columnHeader: {
    padding: rem(16),
    backgroundColor: theme.colors.decaLight[0],
    borderRadius: `${rem(8)} ${rem(8)} 0 0`,
    flexShrink: 0,
    position: 'sticky',
    top: 0,
    zIndex: 100,
  },

  stageIndicator: {
    borderRadius: '50%',
  },

  columnContent: {
    flex: 1,
    position: 'relative',
    paddingTop: rem(8),
    zIndex: 1,
  },

  virtualContainer: {
    width: '100%',
    position: 'relative',
  },

  virtualItem: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    zIndex: 1,
  },

  itemContent: {
    padding: `0 ${rem(12)} ${rem(8)} ${rem(12)}`,
  },

  addButton: {
    height: rem(42),
    border: `1px solid ${theme.colors.decaLight[2]}`,
  },

  dropIndicator: {
    height: rem(1),
    backgroundColor: theme.colors.decaBlue[5],
    borderRadius: rem(2),
    margin: rem(8),
    opacity: 0.8,
    boxShadow: `0 0 ${rem(8)} ${theme.colors.decaBlue[3]}`,
    animation: 'pulse 1.5s ease-in-out infinite',
    zIndex: 1000,
  },
}));

// Types and interfaces
interface RecordStage {
  id: string;
  label: string;
  color: string;
  records: CRMRecord[];
}

interface KanbanCollectionProps {
  recordStages: RecordStage[];
  statusField: any; // The swimlane field definition
}

// Separate KanbanColumn component to prevent recreation
interface KanbanColumnProps {
  stage: RecordStage;
  recordStages: RecordStage[];
  handleCreateRecord: (stageId: string) => void;
  onDuplicate: (recordId: string) => void;
  onMove: (recordId: string, targetStageId: string) => void;
  mainScrollRef: React.RefObject<HTMLDivElement>;
  dropIndicator: {
    stageId: string;
    position: 'before' | 'after' | 'end';
    recordId?: string;
  } | null;
}

const KanbanColumn = memo<KanbanColumnProps>(
  ({
    stage,
    recordStages,
    handleCreateRecord,
    onDuplicate,
    onMove,
    mainScrollRef,
    dropIndicator,
  }) => {
    const { classes, theme } = useStyles();
    const { t } = useTranslate('workspace');
    const stageColor = ColorCodes[stage.color]?.color || theme.colors.decaGrey[6];
    const [isVirtualizerReady, setIsVirtualizerReady] = useState(false);
    const [menuOpened, { open: openMenu, close: closeMenu }] = useDisclosure(false);
    const { wsId } = useParams();
    const { object, activeView, handleViewChange, refetchObject } = useWorkspaceContext();

    // Make column droppable
    const { setNodeRef: setDroppableRef, isOver } = useDroppable({
      id: `column-${stage.id}`,
      data: {
        type: 'column',
        stageId: stage.id,
      },
    });

    // Create items array for this column (cards + add button)
    const items = useMemo(() => {
      const cardItems = stage.records.map((record) => ({ type: 'card' as const, record }));
      return [...cardItems, { type: 'addButton' as const }];
    }, [stage.records]);

    // Handle hiding the group (set active to false)
    const handleHideGroup = useCallback(async () => {
      if (!wsId || !activeView || !activeView.kanban?.swimlaneOrder || stage.id === 'other') {
        return;
      }

      try {
        // Update the swimlaneOrder to set this stage as inactive
        const updatedSwimlaneOrder = activeView.kanban.swimlaneOrder.map((swimlaneItem) =>
          swimlaneItem.id === stage.id ? { ...swimlaneItem, active: false } : swimlaneItem
        );

        const updatedView = {
          ...activeView,
          kanban: {
            ...activeView.kanban,
            swimlaneOrder: updatedSwimlaneOrder,
          },
        };

        await handleViewChange(activeView.id, updatedView);
        closeMenu();
      } catch (error) {
        console.error('Failed to hide group:', error);
      }
    }, [wsId, activeView, stage.id, handleViewChange, closeMenu]);

    // Handle deleting the group (remove from field choices)
    const handleDeleteGroup = useCallback(async () => {
      if (!wsId || !object || !activeView?.kanban?.swimlaneFieldId || stage.id === 'other') {
        return;
      }

      try {
        // Find the swimlane field and remove this choice
        const updatedFields =
          object.fields?.map((field) => {
            if (field.id === activeView.kanban?.swimlaneFieldId) {
              const updatedChoices =
                field.options?.choices?.filter((choice) => choice.id !== stage.id) || [];
              return {
                ...field,
                options: {
                  ...field.options,
                  choices: updatedChoices,
                },
              };
            }
            return field;
          }) || [];

        const updatedObject = {
          ...object,
          fields: updatedFields,
        };

        await ObjectAPI.update(wsId, updatedObject);
        await refetchObject();
        closeMenu();
      } catch (error) {
        console.error('Failed to delete group:', error);
      }
    }, [wsId, object, activeView, stage.id, refetchObject, closeMenu]);

    // Wait for the scroll container to be ready before initializing virtualizer
    useEffect(() => {
      let resizeObserver: ResizeObserver | null = null;

      const checkContainer = () => {
        if (mainScrollRef.current) {
          // Also check if the container has dimensions
          const rect = mainScrollRef.current.getBoundingClientRect();
          if (rect.width > 0 && rect.height > 0) {
            setIsVirtualizerReady(true);
            return true;
          }
        }
        return false;
      };

      // Initial check with timeout fallback
      const timer = setTimeout(() => {
        if (!checkContainer()) {
          // If still not ready, set up a ResizeObserver to detect when it becomes available
          if (mainScrollRef.current && window.ResizeObserver) {
            resizeObserver = new ResizeObserver((entries) => {
              for (const entry of entries) {
                if (entry.contentRect.width > 0 && entry.contentRect.height > 0) {
                  setIsVirtualizerReady(true);
                  resizeObserver?.disconnect();
                  break;
                }
              }
            });
            resizeObserver.observe(mainScrollRef.current);
          } else {
            // Fallback: assume ready after a longer delay
            setTimeout(() => setIsVirtualizerReady(true), 500);
          }
        }
      }, 100);

      return () => {
        clearTimeout(timer);
        resizeObserver?.disconnect();
      };
    }, [mainScrollRef]);

    // Dynamic virtualizer - automatically measures actual heights (TanStack approach)
    const columnVirtualizer = useVirtualizer({
      count: items.length,
      getScrollElement: () => mainScrollRef.current,
      estimateSize: () => 150,
      overscan: 5,
    });

    return (
      <Box
        ref={setDroppableRef}
        className={`${classes.kanbanColumn} ${isOver ? classes.kanbanColumnDropping : ''}`}
        data-testid='kanban-column'
        data-stage-id={stage.id}
      >
        {/* Column Header - Fixed */}
        <Box className={classes.columnHeader} data-testid='kanban-column-header'>
          <Flex align='center' justify='space-between'>
            <Flex align='center' gap='sm'>
              <Box
                w={rem(8)}
                h={rem(8)}
                bg={stageColor}
                className={classes.stageIndicator}
                data-testid='stage-indicator'
              />
              <Text
                fw={500}
                fz={rem(14)}
                data-testid='stage-label'
                truncate='end'
                lineClamp={1}
                w={rem(200)}
              >
                {stage.label}
              </Text>
            </Flex>
            <Flex align='center' gap='sm'>
              <ActionIcon
                color={theme.colors.decaGrey[4]}
                variant='transparent'
                size='sm'
                data-testid='kanban-add-button'
                onClick={() => handleCreateRecord(stage.id)}
              >
                <IconPlus size={16} />
              </ActionIcon>
              {stage.id !== 'other' && (
                <Menu
                  opened={menuOpened}
                  onClose={closeMenu}
                  position='bottom-end'
                  shadow='md'
                  withinPortal
                >
                  <Menu.Target>
                    <ActionIcon
                      color={theme.colors.decaGrey[4]}
                      variant='transparent'
                      size='sm'
                      data-testid='column-menu-button'
                      onClick={openMenu}
                    >
                      <IconDots size={16} />
                    </ActionIcon>
                  </Menu.Target>

                  <Menu.Dropdown>
                    <Menu.Item
                      p={`${rem(8)} ${rem(12)}`}
                      fz={rem(14)}
                      leftSection={<IconEyeOff size={16} />}
                      onClick={handleHideGroup}
                      data-testid='hide-group-menu-item'
                    >
                      {t('hideGroup', 'Hide group')}
                    </Menu.Item>
                    <Menu.Item
                      p={`${rem(8)} ${rem(12)}`}
                      fz={rem(14)}
                      leftSection={<IconTrash size={16} />}
                      onClick={handleDeleteGroup}
                      color='red'
                      data-testid='delete-group-menu-item'
                    >
                      {t('deleteGroup', 'Delete group')}
                    </Menu.Item>
                  </Menu.Dropdown>
                </Menu>
              )}
            </Flex>
          </Flex>
        </Box>

        {/* Virtualized Column Content */}
        <Box className={classes.columnContent} data-testid='kanban-column-content'>
          {isVirtualizerReady &&
            columnVirtualizer?.getVirtualItems &&
            columnVirtualizer.getVirtualItems().length >= 0 && (
              /* Virtualized rendering */
              <div
                className={classes.virtualContainer}
                data-testid='virtual-container'
                style={{
                  height: `${columnVirtualizer.getTotalSize()}px`,
                }}
              >
                {columnVirtualizer.getVirtualItems().map((virtualItem) => {
                  const item = items[virtualItem.index];

                  return (
                    <div
                      key={virtualItem.index}
                      data-index={virtualItem.index}
                      ref={columnVirtualizer.measureElement} // Key for dynamic measurement!
                      className={classes.virtualItem}
                      style={{
                        transform: `translateY(${virtualItem.start}px)`,
                      }}
                    >
                      {/* Content wrapper with consistent spacing */}
                      <div className={classes.itemContent}>
                        {/* Drop indicator before card */}
                        {item.type === 'card' &&
                          dropIndicator?.stageId === stage.id &&
                          dropIndicator?.position === 'before' &&
                          dropIndicator?.recordId === item.record.id && (
                            <div className={classes.dropIndicator} data-testid='drop-indicator' />
                          )}

                        {item.type === 'card' && (
                          <SortableContext
                            items={[item.record.id]}
                            strategy={verticalListSortingStrategy}
                          >
                            <KanbanCard
                              record={item.record}
                              recordStages={recordStages}
                              onDuplicate={onDuplicate}
                              onMove={onMove}
                              stageId={stage.id}
                            />
                          </SortableContext>
                        )}

                        {/* Drop indicator after card */}
                        {item.type === 'card' &&
                          dropIndicator?.stageId === stage.id &&
                          dropIndicator?.position === 'after' &&
                          dropIndicator?.recordId === item.record.id && (
                            <div className={classes.dropIndicator} data-testid='drop-indicator' />
                          )}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

          {/* Drop indicator at end of column */}
          {dropIndicator?.stageId === stage.id && dropIndicator?.position === 'end' && (
            <div
              className={classes.dropIndicator}
              data-testid='drop-indicator-end'
              style={{ margin: `${rem(8)} ${rem(12)}` }}
            />
          )}
        </Box>
      </Box>
    );
  }
);

KanbanColumn.displayName = 'KanbanColumn';

const KanbanCollection: React.FC<KanbanCollectionProps> = ({
  recordStages,
  statusField,
}) => {
  const { mutateRecord, loadMore, hasMore, isLoadingMore } = useWorkspaceContext();
  const { classes } = useStyles();
  const mainScrollRef = useRef<HTMLDivElement>(null);
  const { wsId, id: objId } = useParams();

  // Drag and drop state
  const [activeRecord, setActiveRecord] = useState<CRMRecord | null>(null);
  const [activeStageId, setActiveStageId] = useState<string | null>(null);
  const [dropIndicator, setDropIndicator] = useState<{
    stageId: string;
    position: 'before' | 'after' | 'end';
    recordId?: string;
  } | null>(null);

  // Configure drag sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // Require 8px movement before drag starts
      },
    })
  );

  // Handle drag start
  const handleDragStart = useCallback(
    (event: DragStartEvent) => {
      const { active } = event;
      const recordId = active.id as string;

      // Find the record and its stage
      for (const stage of recordStages) {
        const record = stage.records.find((r) => r.id === recordId);
        if (record) {
          setActiveRecord(record);
          setActiveStageId(stage.id);
          break;
        }
      }
    },
    [recordStages]
  );

  // Handle drag over to show drop indicator
  const handleDragOver = useCallback(
    (event: DragOverEvent) => {
      const { active, over } = event;

      if (!over || !active) {
        setDropIndicator(null);
        return;
      }

      const draggedRecordId = active.id as string;
      const overId = over.id as string;

      // Don't show indicator if hovering over itself
      if (draggedRecordId === overId) {
        setDropIndicator(null);
        return;
      }

      // Check if dropped on a column
      if (overId.startsWith('column-')) {
        const targetStageId = overId.replace('column-', '');
        setDropIndicator({
          stageId: targetStageId,
          position: 'end',
        });
        return;
      }

      // Find which stage the target record belongs to
      for (const stage of recordStages) {
        const targetIndex = stage.records.findIndex((r) => r.id === overId);
        if (targetIndex !== -1) {
          // Find the dragged record's stage for comparison
          let sourceStageId = '';
          for (const sourceStage of recordStages) {
            if (sourceStage.records.find((r) => r.id === draggedRecordId)) {
              sourceStageId = sourceStage.id;
              break;
            }
          }

          // Determine position based on whether it's same stage or cross-stage
          let position: 'before' | 'after' = 'before';

          if (sourceStageId === stage.id) {
            // Same stage - need to consider current positions
            const draggedIndex = stage.records.findIndex((r) => r.id === draggedRecordId);
            if (draggedIndex !== -1 && draggedIndex < targetIndex) {
              position = 'after'; // Moving down, place after target
            }
          }

          setDropIndicator({
            stageId: stage.id,
            position,
            recordId: overId,
          });
          return;
        }
      }

      setDropIndicator(null);
    },
    [recordStages]
  );

  // Handle drag end and reordering
  const handleDragEnd = useCallback(
    async (event: DragEndEvent) => {
      const { active, over } = event;

      setActiveRecord(null);
      setActiveStageId(null);
      setDropIndicator(null);

      if (!over || !wsId || !objId) {
        return;
      }

      const draggedRecordId = active.id as string;
      const overId = over.id as string;

      // Don't do anything if dropped on itself
      if (draggedRecordId === overId) {
        return;
      }

      // Find the dragged record and its current stage
      let draggedRecord: CRMRecord | null = null;
      let sourceStage: RecordStage | null = null;
      let draggedRecordIndex = -1;

      for (const stage of recordStages) {
        const index = stage.records.findIndex((r) => r.id === draggedRecordId);
        if (index !== -1) {
          draggedRecord = stage.records[index];
          sourceStage = stage;
          draggedRecordIndex = index;
          break;
        }
      }

      if (!draggedRecord || !sourceStage) {
        return;
      }

      // Determine if dropped on a record or a column
      const isDroppedOnColumn = overId.startsWith('column-');
      let targetStageId = '';
      let targetPosition: { previousRecordId: string | null; nextRecordId: string | null };

      if (isDroppedOnColumn) {
        // Dropped on a column - place at the end
        targetStageId = overId.replace('column-', '');
        const targetStage = recordStages.find((stage) => stage.id === targetStageId);

        if (!targetStage) {
          return;
        }

        // Place at the end of the column
        const lastRecord = targetStage.records[targetStage.records.length - 1];
        targetPosition = {
          previousRecordId: lastRecord?.id || null,
          nextRecordId: null,
        };
      } else {
        // Dropped on another record - find target stage and position
        let targetStage: RecordStage | null = null;
        let targetRecordIndex = -1;

        for (const stage of recordStages) {
          const index = stage.records.findIndex((r) => r.id === overId);
          if (index !== -1) {
            targetStage = stage;
            targetRecordIndex = index;
            targetStageId = stage.id;
            break;
          }
        }

        if (!targetStage || targetRecordIndex === -1) {
          return;
        }

        // Calculate position relative to target record
        const records = targetStage.records;
        const isSameStage = sourceStage.id === targetStage.id;

        if (isSameStage) {
          // Same column reordering
          const movingDown = draggedRecordIndex < targetRecordIndex;

          if (movingDown) {
            // Moving down: place after the target record
            targetPosition = {
              previousRecordId: overId,
              nextRecordId:
                targetRecordIndex + 1 < records.length ? records[targetRecordIndex + 1].id : null,
            };
          } else {
            // Moving up: place before the target record
            targetPosition = {
              previousRecordId: targetRecordIndex > 0 ? records[targetRecordIndex - 1].id : null,
              nextRecordId: overId,
            };
          }
        } else {
          // Cross-column drop: place before the target record
          targetPosition = {
            previousRecordId: targetRecordIndex > 0 ? records[targetRecordIndex - 1].id : null,
            nextRecordId: overId,
          };
        }
      }

      try {
        // Check if position actually changed before making API calls
        const isStageChange = sourceStage.id !== targetStageId;
        let isPositionChange = false;

        if (!isStageChange) {
          // Same column - check if position actually changes by comparing target position
          // with current position in the list
          const records = sourceStage.records;
          const currentPreviousId =
            draggedRecordIndex > 0 ? records[draggedRecordIndex - 1]?.id : null;
          const currentNextId =
            draggedRecordIndex < records.length - 1 ? records[draggedRecordIndex + 1]?.id : null;

          // Compare with target position (handle null/empty string equivalence)
          const targetPreviousId = targetPosition.previousRecordId || null;
          const targetNextId = targetPosition.nextRecordId || null;

          // Position hasn't changed if both previous and next records are the same
          isPositionChange = !(
            currentPreviousId === targetPreviousId && currentNextId === targetNextId
          );
        } else {
          // Different stage is always a position change
          isPositionChange = true;
        }

        // Early return if no actual change
        if (!isStageChange && !isPositionChange) {
          return;
        }

        // Validate required fields for API call
        if (!statusField?.id) {
          console.error('StatusField is required for kanban reordering');
          return;
        }

        // Optimistically update the UI immediately for better UX
        mutateRecord((currData: any) => {
          if (!currData || !currData.length) return currData;

          const updatedData = [...currData];
          const firstPage = { ...updatedData[0] };

          if (firstPage.kanbanRecords) {
            const updatedKanbanRecords = { ...firstPage.kanbanRecords };

            // Remove record from source stage
            if (updatedKanbanRecords[sourceStage.id]) {
              updatedKanbanRecords[sourceStage.id] = updatedKanbanRecords[sourceStage.id].filter(
                (r) => r.id !== draggedRecordId
              );
            }

            // Update the record with new field values if stage changed
            const updatedRecord = { ...draggedRecord };
            if (sourceStage.id !== targetStageId) {
              if (targetStageId !== 'other') {
                (updatedRecord as any)[statusField.id] = targetStageId;
              } else {
                (updatedRecord as any)[statusField.id] = null;
              }
            }

            // Add record to target stage at the correct position
            if (!updatedKanbanRecords[targetStageId]) {
              updatedKanbanRecords[targetStageId] = [];
            }

            const targetRecords = [...updatedKanbanRecords[targetStageId]];

            if (isDroppedOnColumn) {
              // Add to end of column
              targetRecords.push(updatedRecord);
            } else {
              // Insert at calculated position
              let insertIndex = 0;
              if (targetPosition.nextRecordId) {
                insertIndex = targetRecords.findIndex((r) => r.id === targetPosition.nextRecordId);
                insertIndex = insertIndex === -1 ? targetRecords.length : insertIndex;
              } else if (targetPosition.previousRecordId) {
                insertIndex = targetRecords.findIndex(
                  (r) => r.id === targetPosition.previousRecordId
                );
                insertIndex = insertIndex === -1 ? targetRecords.length : insertIndex + 1;
              }

              targetRecords.splice(insertIndex, 0, updatedRecord);
            }

            updatedKanbanRecords[targetStageId] = targetRecords;
            firstPage.kanbanRecords = updatedKanbanRecords;
          }

          updatedData[0] = firstPage;
          return updatedData;
        }, false);

        // Call the reorder API with swimlane information (handles both positioning and field updates)
        try {
          await RecordAPI.reorder(wsId, objId, draggedRecordId, {
            previousRecordId: targetPosition.previousRecordId || '',
            nextRecordId: targetPosition.nextRecordId || '',
            swimlaneId: statusField.id,
            swimlaneOptId: targetStageId === 'other' ? '' : targetStageId,
          });
          // API success - optimistic update was correct, no action needed
        } catch (error) {
          console.error('Failed to reorder record:', error);

          // Rollback optimistic changes on API failure
          mutateRecord((currData: any) => {
            if (!currData || !currData.length) return currData;

            const revertedData = [...currData];
            const firstPage = { ...revertedData[0] };

            if (firstPage.kanbanRecords) {
              const revertedKanbanRecords = { ...firstPage.kanbanRecords };

              // Remove record from target stage (where we optimistically placed it)
              if (revertedKanbanRecords[targetStageId]) {
                revertedKanbanRecords[targetStageId] = revertedKanbanRecords[targetStageId].filter(
                  (r) => r.id !== draggedRecordId
                );
              }

              // Restore record to original position in source stage
              if (!revertedKanbanRecords[sourceStage.id]) {
                revertedKanbanRecords[sourceStage.id] = [];
              }

              const sourceRecords = [...revertedKanbanRecords[sourceStage.id]];

              // Insert back at original position
              if (draggedRecordIndex >= sourceRecords.length) {
                sourceRecords.push(draggedRecord);
              } else {
                sourceRecords.splice(draggedRecordIndex, 0, draggedRecord);
              }

              revertedKanbanRecords[sourceStage.id] = sourceRecords;
              firstPage.kanbanRecords = revertedKanbanRecords;
            }

            revertedData[0] = firstPage;
            return revertedData;
          }, false);

          // Optionally show error notification to user
          // toast.error('Failed to move record. Please try again.');
        }
      } catch (error) {
        console.error('Failed to reorder record:', error);
      }
    },
    [recordStages, wsId, objId, statusField, mutateRecord]
  );

  // Handle record duplication
  const handleDuplicate = useCallback(
    async (recordId: string) => {
      if (!wsId || !objId) {
        console.error('Missing workspace or object ID');
        return;
      }

      try {
        // Call the duplicate API
        const duplicatedRecord = await RecordAPI.duplicate(wsId, objId, recordId);

        if (duplicatedRecord) {
          // Update the UI by adding the duplicated record to the kanban
          mutateRecord((currData: any) => {
            if (!currData || !currData.length) return currData;

            // Clone the current data to avoid mutations
            const updatedData = [...currData];
            const firstPage = { ...updatedData[0] };

            if (firstPage.kanbanRecords) {
              const updatedKanbanRecords = { ...firstPage.kanbanRecords };

              // Find which stage the original record belongs to
              const originalStageId = Object.keys(updatedKanbanRecords).find((stageId) =>
                updatedKanbanRecords[stageId].some((record) => record.id === recordId)
              );

              if (originalStageId && updatedKanbanRecords[originalStageId]) {
                // Add the duplicated record to the same stage as the original
                updatedKanbanRecords[originalStageId] = [
                  duplicatedRecord,
                  ...updatedKanbanRecords[originalStageId],
                ];
              } else {
                // If we can't find the original stage, add to 'other' as fallback
                if (updatedKanbanRecords.other) {
                  updatedKanbanRecords.other = [duplicatedRecord, ...updatedKanbanRecords.other];
                }
              }

              firstPage.kanbanRecords = updatedKanbanRecords;
            }

            updatedData[0] = firstPage;
            return updatedData;
          }, false);
        }
      } catch (error) {
        console.error('Failed to duplicate record:', error);
      }
    },
    [wsId, objId, mutateRecord]
  );

  // Handle record move to different stage
  const handleMove = useCallback(
    async (recordId: string, targetStageId: string) => {
      if (!wsId || !objId || !statusField?.id) {
        return;
      }

      try {
        // Update record data with new swimlane field value
        const recordData: any = {};

        if (targetStageId !== 'other') {
          recordData[statusField.id] = targetStageId;
        } else {
          // For "other" column, set the field to null/undefined
          recordData[statusField.id] = null;
        }

        await RecordAPI.update(wsId, objId, recordId, recordData);

        // Soft mutate to move the record between stages
        mutateRecord((currData: any) => {
          if (!currData || !currData.length) return currData;

          // Clone the current data to avoid mutations
          const updatedData = [...currData];

          // Update the first page with moved record
          const firstPage = { ...updatedData[0] };
          if (firstPage.kanbanRecords) {
            const updatedKanbanRecords = { ...firstPage.kanbanRecords };

            // Find and remove the record from its current stage
            let movedRecord = null;
            for (const stageId in updatedKanbanRecords) {
              const stageRecords = updatedKanbanRecords[stageId];
              const recordIndex = stageRecords.findIndex((r) => r.id === recordId);
              if (recordIndex !== -1) {
                movedRecord = { ...stageRecords[recordIndex] };
                // Update the record with new field value
                if (movedRecord && statusField?.id) {
                  if (targetStageId !== 'other') {
                    (movedRecord as any)[statusField.id] = targetStageId;
                  } else {
                    (movedRecord as any)[statusField.id] = null;
                  }
                }
                updatedKanbanRecords[stageId] = stageRecords.filter((r) => r.id !== recordId);
                break;
              }
            }

            // Add the record to the target stage
            if (movedRecord) {
              if (updatedKanbanRecords[targetStageId]) {
                updatedKanbanRecords[targetStageId] = [
                  movedRecord,
                  ...updatedKanbanRecords[targetStageId],
                ];
              } else {
                updatedKanbanRecords[targetStageId] = [movedRecord];
              }
            }

            firstPage.kanbanRecords = updatedKanbanRecords;
          }

          updatedData[0] = firstPage;
          return updatedData;
        }, false); // false prevents revalidation
      } catch (error) {
        console.error('Failed to move record:', error);
      }
    },
    [wsId, objId, statusField, mutateRecord]
  );

  // Handle new record creation
  const handleCreateRecord = useCallback(
    async (stageId: string) => {
      if (!wsId || !objId) {
        return;
      }

      try {
        // Create record data with swimlane field if not "other" column
        const recordData: any = {};

        if (stageId !== 'other' && statusField?.id) {
          // Set the swimlane field ID to the stage ID for specific columns
          // Format: { [swimlaneFieldId]: stageId }
          recordData[statusField.id] = stageId;
        }
        // For "other" column, leave the swimlane field unset (null/undefined)

        const newRecord = await RecordAPI.save(wsId, objId, recordData);

        // Soft mutate to add the new record to the appropriate stage
        mutateRecord((currData: any) => {
          if (!currData || !currData.length) return currData;

          // Clone the current data to avoid mutations
          const updatedData = [...currData];

          // Update the first page with the new record
          const firstPage = { ...updatedData[0] };
          if (firstPage.kanbanRecords) {
            const updatedKanbanRecords = { ...firstPage.kanbanRecords };

            // Add the new record to the beginning of the appropriate stage
            if (updatedKanbanRecords[stageId]) {
              updatedKanbanRecords[stageId] = [
                { ...newRecord, id: newRecord?.id },
                ...updatedKanbanRecords[stageId],
              ];
            } else {
              // If stage doesn't exist, create it with the new record
              updatedKanbanRecords[stageId] = [{ ...newRecord, id: newRecord?.id }];
            }

            firstPage.kanbanRecords = updatedKanbanRecords;
          }

          updatedData[0] = firstPage;
          return updatedData;
        }, false); // false prevents revalidation
      } catch (error) {
        console.error('Failed to create record:', error);
      }
    },
    [wsId, objId, statusField, mutateRecord]
  );

  // Debounced scroll handler to prevent rapid firing
  const scrollTimeoutRef = useRef<NodeJS.Timeout>();
  const isLoadingRef = useRef<boolean>(false);

  const handleScroll = useCallback(
    (event: React.UIEvent<HTMLDivElement>) => {
      const container = event.target as HTMLDivElement;

      // Clear existing timeout
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      // Debounce scroll events
      scrollTimeoutRef.current = setTimeout(() => {
        // Debug scroll conditions
        const { scrollHeight, scrollTop, clientHeight } = container;
        const offsetToBottom = scrollHeight - scrollTop - clientHeight;
        const hasRecords = recordStages.some((stage) => stage.records.length > 0);
        const hasScrolledDown = scrollTop > 200;
        const nearBottom = offsetToBottom < 500;

        // Multiple guards to prevent unwanted loading
        if (!hasMore || isLoadingMore || isLoadingRef.current) {
          return;
        }

        if (hasRecords && hasScrolledDown && nearBottom) {
          isLoadingRef.current = true;
          loadMore();
        }
      }, 50);
    },
    [hasMore, isLoadingMore, loadMore, recordStages]
  );

  // Reset loading ref when isLoadingMore changes
  useEffect(() => {
    if (!isLoadingMore) {
      isLoadingRef.current = false;
    }
  }, [isLoadingMore]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      <Box className={classes.mainContainer} data-testid='kanban-collection'>
        {/* Main scrollable content area with unified scroll for all virtualizers */}
        <Box
          ref={mainScrollRef}
          className={classes.scrollContainer}
          data-testid='kanban-scroll-container'
          onScroll={handleScroll}
        >
          {recordStages.map((stage) => (
            <KanbanColumn
              key={stage.id}
              stage={stage}
              recordStages={recordStages}
              handleCreateRecord={handleCreateRecord}
              onDuplicate={handleDuplicate}
              onMove={handleMove}
              mainScrollRef={mainScrollRef}
              dropIndicator={dropIndicator}
            />
          ))}
        </Box>

        {/* Drag overlay for visual feedback */}
        <DragOverlay>
          {activeRecord && activeStageId ? (
            <KanbanCard
              record={activeRecord}
              recordStages={recordStages}
              stageId={activeStageId}
              isDragging={true}
            />
          ) : null}
        </DragOverlay>
      </Box>
    </DndContext>
  );
};

export default memo(KanbanCollection);
