import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { Record as CRMRecord } from '@/models';
import type { ObjectColumn } from '@/models/workspace';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { useSortable } from '@dnd-kit/sortable';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import KanbanCard from './KanbanCard';

// Mock dependencies
vi.mock('@/contexts/WorkspaceContext');
vi.mock('@dnd-kit/sortable');
vi.mock('../TableCellRendering/Cell', () => ({
  default: ({ cell, type, column }: any) => (
    <div data-testid='cell-component' data-type={type} data-column-id={column.id}>
      {cell.getValue()}
    </div>
  ),
}));

const mockUseWorkspaceContext = useWorkspaceContext as any;
const mockUseSortable = useSortable as any;

describe('KanbanCard', () => {
  const mockRecord: CRMRecord = {
    id: 'record-1',
    title: 'Test Record',
    description: 'Test Description',
    status: 'active',
    createdAt: '2023-01-01',
    updatedAt: '2023-01-02',
  };

  const mockFields: ObjectColumn[] = [
    {
      id: 'title',
      name: 'Title',
      header: 'Title',
      type: 'text',
      options: {},
    },
    {
      id: 'description',
      name: 'Description',
      header: 'Description',
      type: 'text',
      options: {},
    },
    {
      id: 'status',
      name: 'Status',
      header: 'Status',
      type: 'single_select',
      options: {
        choices: [
          { id: 'active', label: 'Active', color: 'blue' },
          { id: 'inactive', label: 'Inactive', color: 'grey' },
        ],
      },
    },
  ];

  const mockOpenProfile = vi.fn();
  const mockOnClick = vi.fn();

  const defaultProps = {
    record: mockRecord,
    onClick: mockOnClick,
    isDragging: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock workspace context
    mockUseWorkspaceContext.mockReturnValue({
      object: {
        fields: mockFields,
      },
      activeView: {
        fieldOrder: ['title', 'description', 'status'],
      },
      openProfile: mockOpenProfile,
      mutateRecord: vi.fn(),
    });

    // Mock sortable hook
    mockUseSortable.mockReturnValue({
      attributes: {},
      listeners: {},
      setNodeRef: vi.fn(),
      transform: null,
      transition: null,
      isDragging: false,
    });
  });

  describe('Component Rendering', () => {
    it('should render the KanbanCard component', () => {
      renderWithMantine(<KanbanCard {...defaultProps} />);

      expect(screen.getByTestId('kanban-card')).toBeInTheDocument();
      expect(screen.getByTestId('kanban-card')).toHaveAttribute('data-record-id', 'record-1');
    });

    it('should render with proper styling when not dragging', () => {
      renderWithMantine(<KanbanCard {...defaultProps} />);

      const card = screen.getByTestId('kanban-card');
      expect(card).toBeInTheDocument();
      expect(card).not.toHaveClass('dragging');
    });

    it('should render with dragging styles when isDragging is true', () => {
      renderWithMantine(<KanbanCard {...defaultProps} isDragging={true} />);

      const card = screen.getByTestId('kanban-card');
      expect(card).toBeInTheDocument();
    });

    it('should render with dragging styles when sortable is dragging', () => {
      mockUseSortable.mockReturnValue({
        attributes: {},
        listeners: {},
        setNodeRef: vi.fn(),
        transform: null,
        transition: null,
        isDragging: true,
      });

      renderWithMantine(<KanbanCard {...defaultProps} />);

      const card = screen.getByTestId('kanban-card');
      expect(card).toBeInTheDocument();
    });
  });

  describe('Field Rendering', () => {
    it('should render card fields based on cardFields prop', () => {
      renderWithMantine(<KanbanCard {...defaultProps} />);

      const fieldContainers = screen.getAllByTestId('kanban-card-field');
      expect(fieldContainers).toHaveLength(3);

      expect(fieldContainers[0]).toHaveAttribute('data-field-id', 'title');
      expect(fieldContainers[1]).toHaveAttribute('data-field-id', 'description');
      expect(fieldContainers[2]).toHaveAttribute('data-field-id', 'status');
    });

    it('should render Cell components for each field', () => {
      renderWithMantine(<KanbanCard {...defaultProps} />);

      const cellComponents = screen.getAllByTestId('cell-component');
      expect(cellComponents).toHaveLength(3);

      expect(cellComponents[0]).toHaveAttribute('data-type', 'text');
      expect(cellComponents[0]).toHaveAttribute('data-column-id', 'title');
      expect(cellComponents[0]).toHaveTextContent('Test Record');

      expect(cellComponents[1]).toHaveAttribute('data-type', 'text');
      expect(cellComponents[1]).toHaveAttribute('data-column-id', 'description');
      expect(cellComponents[1]).toHaveTextContent('Test Description');

      expect(cellComponents[2]).toHaveAttribute('data-type', 'single_select');
      expect(cellComponents[2]).toHaveAttribute('data-column-id', 'status');
      expect(cellComponents[2]).toHaveTextContent('active');
    });

    it('should handle empty cardFields array', () => {
      // Mock context with empty fieldOrder
      mockUseWorkspaceContext.mockReturnValue({
        object: {
          fields: mockFields,
        },
        activeView: {
          fieldOrder: [],
        },
        openProfile: mockOpenProfile,
        mutateRecord: vi.fn(),
      });

      renderWithMantine(<KanbanCard {...defaultProps} />);

      expect(screen.getByTestId('kanban-card')).toBeInTheDocument();
      expect(screen.queryByTestId('kanban-card-field')).not.toBeInTheDocument();
    });

    it('should handle fields that do not exist in object fields', () => {
      // Mock context with fieldOrder that references non-existent fields
      mockUseWorkspaceContext.mockReturnValue({
        object: {
          fields: mockFields,
        },
        activeView: {
          fieldOrder: ['nonexistent1', 'nonexistent2'],
        },
        openProfile: mockOpenProfile,
        mutateRecord: vi.fn(),
      });

      renderWithMantine(<KanbanCard {...defaultProps} />);

      expect(screen.getByTestId('kanban-card')).toBeInTheDocument();
      expect(screen.queryByTestId('kanban-card-field')).not.toBeInTheDocument();
    });

    it('should display field values from record data', () => {
      const recordWithData = {
        ...mockRecord,
        title: 'Custom Title',
        description: 'Custom Description',
        status: 'inactive',
      };

      renderWithMantine(<KanbanCard {...defaultProps} record={recordWithData} />);

      const cellComponents = screen.getAllByTestId('cell-component');
      expect(cellComponents[0]).toHaveTextContent('Custom Title');
      expect(cellComponents[1]).toHaveTextContent('Custom Description');
      expect(cellComponents[2]).toHaveTextContent('inactive');
    });
  });

  describe('Click Handling', () => {
    it('should call openProfile when card is clicked', () => {
      renderWithMantine(<KanbanCard {...defaultProps} />);

      const card = screen.getByTestId('kanban-card');
      fireEvent.click(card);

      expect(mockOpenProfile).toHaveBeenCalledWith('record-1', mockRecord);
    });

    it('should stop event propagation on card click', () => {
      const mockStopPropagation = vi.fn();
      renderWithMantine(<KanbanCard {...defaultProps} />);

      const card = screen.getByTestId('kanban-card');
      fireEvent.click(card, { stopPropagation: mockStopPropagation });

      expect(mockOpenProfile).toHaveBeenCalledWith('record-1', mockRecord);
    });
  });

  describe('Drag and Drop Integration', () => {
    it('should initialize sortable with correct id', () => {
      renderWithMantine(<KanbanCard {...defaultProps} />);

      expect(mockUseSortable).toHaveBeenCalledWith({ id: 'record-1' });
    });

    it('should apply sortable attributes and listeners', () => {
      const mockAttributes = { 'aria-describedby': 'sortable-description' };
      const mockListeners = { onPointerDown: vi.fn() };

      mockUseSortable.mockReturnValue({
        attributes: mockAttributes,
        listeners: mockListeners,
        setNodeRef: vi.fn(),
        transform: null,
        transition: null,
        isDragging: false,
      });

      renderWithMantine(<KanbanCard {...defaultProps} />);

      const card = screen.getByTestId('kanban-card');
      expect(card).toHaveAttribute('aria-describedby', 'sortable-description');
    });

    it('should apply transform styles from sortable', () => {
      mockUseSortable.mockReturnValue({
        attributes: {},
        listeners: {},
        setNodeRef: vi.fn(),
        transform: { x: 10, y: 20, scaleX: 1, scaleY: 1 },
        transition: 'transform 200ms ease',
        isDragging: false,
      });

      renderWithMantine(<KanbanCard {...defaultProps} />);

      const card = screen.getByTestId('kanban-card');
      expect(card).toBeInTheDocument();
      // Style assertions would need to be tested differently due to CSS-in-JS
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle missing workspace context gracefully', () => {
      mockUseWorkspaceContext.mockReturnValue({
        object: null,
        activeView: null,
        openProfile: mockOpenProfile,
        mutateRecord: vi.fn(),
      });

      renderWithMantine(<KanbanCard {...defaultProps} />);

      expect(screen.getByTestId('kanban-card')).toBeInTheDocument();
      expect(screen.queryByTestId('kanban-card-field')).not.toBeInTheDocument();
    });

    it('should handle missing object fields gracefully', () => {
      mockUseWorkspaceContext.mockReturnValue({
        object: { fields: null },
        activeView: {
          fieldOrder: ['title', 'description'],
        },
        openProfile: mockOpenProfile,
        mutateRecord: vi.fn(),
      });

      renderWithMantine(<KanbanCard {...defaultProps} />);

      expect(screen.getByTestId('kanban-card')).toBeInTheDocument();
      expect(screen.queryByTestId('kanban-card-field')).not.toBeInTheDocument();
    });

    it('should handle null cardFields gracefully', () => {
      mockUseWorkspaceContext.mockReturnValue({
        object: {
          fields: mockFields,
        },
        activeView: null,
        openProfile: mockOpenProfile,
        mutateRecord: vi.fn(),
      });

      renderWithMantine(<KanbanCard {...defaultProps} />);

      expect(screen.getByTestId('kanban-card')).toBeInTheDocument();
      expect(screen.queryByTestId('kanban-card-field')).not.toBeInTheDocument();
    });

    it('should handle record with missing field values', () => {
      const incompleteRecord = {
        id: 'record-2',
        title: 'Only Title',
      } as CRMRecord;

      renderWithMantine(<KanbanCard {...defaultProps} record={incompleteRecord} />);

      const cellComponents = screen.getAllByTestId('cell-component');
      expect(cellComponents).toHaveLength(3);
      expect(cellComponents[0]).toHaveTextContent('Only Title');
      expect(cellComponents[1]).toHaveTextContent(''); // Missing description should be empty
      expect(cellComponents[2]).toHaveTextContent(''); // Missing status should be empty
    });
  });

  describe('Memoization and Performance', () => {
    it('should render with different record props', () => {
      const newRecord = { ...mockRecord, id: 'record-2', title: 'New Title' };
      renderWithMantine(<KanbanCard {...defaultProps} record={newRecord} />);

      expect(screen.getByTestId('kanban-card')).toHaveAttribute('data-record-id', 'record-2');
      expect(screen.getAllByTestId('cell-component')[0]).toHaveTextContent('New Title');
    });

    it('should render with different cardFields', () => {
      // Mock context with only one field in fieldOrder
      mockUseWorkspaceContext.mockReturnValue({
        object: {
          fields: mockFields,
        },
        activeView: {
          fieldOrder: ['title'],
        },
        openProfile: mockOpenProfile,
        mutateRecord: vi.fn(),
      });

      renderWithMantine(<KanbanCard {...defaultProps} />);

      expect(screen.getAllByTestId('kanban-card-field')).toHaveLength(1);
      expect(screen.getByTestId('kanban-card-field')).toHaveAttribute('data-field-id', 'title');
    });
  });

  describe('Accessibility', () => {
    it('should have proper data attributes for testing', () => {
      renderWithMantine(<KanbanCard {...defaultProps} />);

      const card = screen.getByTestId('kanban-card');
      expect(card).toHaveAttribute('data-record-id', 'record-1');

      const fields = screen.getAllByTestId('kanban-card-field');
      expect(fields[0]).toHaveAttribute('data-field-id', 'title');
      expect(fields[1]).toHaveAttribute('data-field-id', 'description');
      expect(fields[2]).toHaveAttribute('data-field-id', 'status');
    });

    it('should be clickable for keyboard users', () => {
      renderWithMantine(<KanbanCard {...defaultProps} />);

      const card = screen.getByTestId('kanban-card');
      expect(card).toBeInTheDocument();
      // The card should be clickable - this is handled by the onClick event
    });
  });
});
