import { HEIGHT_OF_HEADER, MAX_VISIBLE_KANBAN_CARD_FIELDS } from '@/constants';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { ObjectColumn } from '@/models/workspace';
import {
  DndContext,
  type DragEndEvent,
  PointerSensor,
  closestCenter,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { arrayMove } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  ActionIcon,
  Box,
  Button,
  Divider,
  Drawer,
  Group,
  Menu,
  Paper,
  Radio,
  Select,
  Stack,
  Tabs,
  Text,
  rem,
} from '@mantine/core';
import { ScrollArea } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { FieldTypes } from '@resola-ai/ui/components';
import {
  IconAlertCircle,
  IconCircleCheck,
  IconGripVertical,
  IconPlus,
  IconSpeakerphone,
  IconX,
} from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import React, { useCallback, useEffect, useState } from 'react';
import { AddFieldForm } from '../FieldSettings/AddFieldForm';
import { useOptions } from '../FieldSettings/AddFieldForm/useOptions';

const useStyles = createStyles((theme) => ({
  drawer: {
    '& .mantine-Drawer-content': {
      borderLeft: `${rem(1)} solid ${theme.colors.decaBlue[5]}`,
      borderTop: `${rem(1)} solid ${theme.colors.decaBlue[5]}`,
      borderTopLeftRadius: rem(6),
      boxShadow: `0 0 ${rem(4)} 0 ${theme.colors.decaBlue[5]}`,
      willChange: 'unset !important',
    },
    '& .mantine-Drawer-inner': {
      top: HEIGHT_OF_HEADER,
      height: `calc(100vh - ${HEIGHT_OF_HEADER}px)`,
    },
    '& .mantine-Drawer-body': {
      padding: 0,
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: theme.colors.decaLight[0],
    },
  },
  header: {
    borderBottom: `1px solid ${theme.colors.decaLight[3]}`,
    backgroundColor: theme.colors.decaLight[0],
    flexShrink: 0,
  },
  title: {
    fontSize: rem(18),
    fontWeight: 600,
    color: theme.colors.decaNavy[7],
    margin: 0,
  },
  content: {
    flex: 1,
    minHeight: 0, // Important for flex child to shrink
  },
  scrollableContent: {
    padding: rem(24),
    height: '100%',
  },
  section: {
    marginBottom: rem(32),
  },
  sectionTitle: {
    fontSize: rem(16),
    fontWeight: 600,
    marginBottom: rem(16),
    color: theme.colors.decaNavy[6],
  },
  infoBox: {
    backgroundColor: theme.colors.decaBlue[0],
    padding: rem(12),
    borderRadius: theme.radius.md,
    marginBottom: rem(16),
    border: `1px solid ${theme.colors.decaBlue[2]}`,
  },
  infoText: {
    fontSize: rem(14),
    color: theme.colors.decaBlue[7],
    display: 'flex',
    alignItems: 'center',
    gap: rem(8),
  },
  fieldItem: {
    border: `1px solid ${theme.colors.decaLight[3]}`,
    borderRadius: theme.radius.md,
    padding: rem(12),
    marginBottom: rem(8),
    backgroundColor: theme.white,
    display: 'flex',
    alignItems: 'center',
    gap: rem(12),
  },
  dragHandle: {
    color: theme.colors.decaGrey[4],
    cursor: 'grab',
    '&:hover': {
      color: theme.colors.decaGrey[6],
    },
    '&:active': {
      cursor: 'grabbing',
    },
  },
  fieldSelect: {
    flex: 1,
    '& .mantine-Select-input': {
      border: 'none',
      backgroundColor: 'transparent',
      fontSize: rem(14),
    },
  },
  removeButton: {
    color: theme.colors.decaGrey[4],
    '&:hover': {
      color: theme.colors.decaRed[6],
      backgroundColor: theme.colors.decaRed[0],
    },
  },
  radioGroup: {
    '& .mantine-Radio-root': {
      marginBottom: rem(12),
    },
    '& .mantine-Radio-label': {
      fontSize: rem(14),
      color: theme.colors.decaNavy[6],
    },
  },
  footer: {
    padding: rem(16),
    borderTop: `1px solid ${theme.colors.decaLight[3]}`,
    flexShrink: 0,
  },
  tabsContainer: {
    '& .mantine-Tabs-list': {
      borderBottom: 'none',
      paddingBottom: 0,
    },
    '& .mantine-Tabs-tab': {
      fontSize: rem(16),
      fontWeight: 600,
      color: theme.colors.decaGrey[6],
      borderBottom: '2px solid transparent',
      '&:hover': {
        backgroundColor: 'transparent',
        borderBottom: `2px solid ${theme.colors.decaBlue[3]}`,
      },
      '&[data-active]': {
        color: theme.colors.decaBlue[6],
        borderBottom: `2px solid ${theme.colors.decaBlue[6]}`,
        backgroundColor: 'transparent',
      },
    },
  },
  previewContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
  },
  previewCard: {
    backgroundColor: theme.white,
    marginTop: rem(24),
    borderRadius: theme.radius.md,
    padding: rem(16),
    border: `1px solid ${theme.colors.decaLight[3]}`,
    boxShadow: theme.shadows.sm,
    width: '90%',
  },
  previewFieldContainer: {
    marginBottom: rem(12),
    '&:last-child': {
      marginBottom: 0,
    },
  },
  previewFieldLabel: {
    fontSize: 16,
    fontWeight: 500,
    color: theme.colors.decaGrey[9],
    marginBottom: rem(4),
  },
  previewFieldValue: {
    fontSize: rem(13),
    color: theme.colors.decaNavy[7],
    '& .mantine-Text-root': {
      fontSize: rem(13),
    },
  },
}));

// Updated interface to match API structure
interface KanbanViewConfig {
  swimlaneFieldId: string;
  swimlaneOrder: { id: string; active: boolean }[]; // Array of objects with column IDs and visibility
}

interface KanbanConfigModalProps {
  opened: boolean;
  onClose: () => void;
  onSave: (config: KanbanViewConfig, fieldOrder: string[]) => Promise<void>;
}

interface SelectedField {
  id: string;
  fieldId: string;
  name: string;
  type: string;
}

const SortableFieldItem: React.FC<{
  field: SelectedField;
  onRemove: (id: string) => void;
  onFieldChange: (id: string, fieldId: string) => void;
  availableFields: ObjectColumn[];
  onFieldAdded: (newField: ObjectColumn) => void;
  selectedFields: SelectedField[];
}> = ({ field, onRemove, onFieldChange, availableFields, onFieldAdded, selectedFields }) => {
  const { classes } = useStyles();
  const { t } = useTranslate('workspace');
  const { handleAddColumn } = useWorkspaceContext();
  const { options: fieldTypeOptions } = useOptions();
  const [addFieldOpened, { close: closeAddField, open: openAddField }] = useDisclosure(false);
  const [isLoadingNewField, setIsLoadingNewField] = useState(false);

  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: field.id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const handleAddFieldSave = useCallback(
    async (fieldData: any) => {
      setIsLoadingNewField(true);
      try {
        // Create a temporary field object with the data from the form
        const tempField: ObjectColumn = {
          id: `temp-${Date.now()}`, // Temporary ID
          name: fieldData.name,
          header: fieldData.name, // Required by MRT_ColumnDef
          type: fieldData.type,
          options: fieldData.options,
          isProtected: fieldData.isProtected || false,
        };

        // Add the new field to local state immediately
        onFieldAdded(tempField);

        // Handle the actual field creation in the background
        await handleAddColumn(fieldData);
        closeAddField();
      } catch (error) {
        console.error('Failed to create field:', error);
      } finally {
        setIsLoadingNewField(false);
      }
    },
    [handleAddColumn, closeAddField, onFieldAdded]
  );

  // Filter out fields that are already selected by other field items (excluding current field)
  const selectedFieldIds = selectedFields
    .filter((f) => f.id !== field.id && f.fieldId && f.fieldId !== '')
    .map((f) => f.fieldId);

  const filteredAvailableFields = availableFields.filter((f) => !selectedFieldIds.includes(f.id!));

  const fieldOptions = [
    { value: 'newField', label: t('newField', 'New field'), leftSection: <IconPlus size={16} /> },
    ...filteredAvailableFields.map((f) => {
      const fieldTypeOption = fieldTypeOptions.find((option) => option.value === f.type);
      return {
        value: f.id!,
        label: f.name || f.id!,
        leftSection: fieldTypeOption?.icon ? (
          React.cloneElement(fieldTypeOption.icon, { size: 16 })
        ) : (
          <IconCircleCheck size={16} />
        ),
      };
    }),
  ];

  // Get the icon for the currently selected field
  const getSelectedFieldIcon = useCallback(() => {
    if (field.fieldId === 'newField') {
      return <IconPlus size={16} />;
    }
    if (field.fieldId) {
      const selectedField = availableFields.find((f) => f.id === field.fieldId);
      if (selectedField) {
        const fieldTypeOption = fieldTypeOptions.find(
          (option) => option.value === selectedField.type
        );
        return fieldTypeOption?.icon ? (
          React.cloneElement(fieldTypeOption.icon, { size: 16 })
        ) : (
          <IconCircleCheck size={16} />
        );
      }
    }
    return null;
  }, [field.fieldId, availableFields, fieldTypeOptions]);

  return (
    <Box
      ref={setNodeRef}
      style={style}
      className={classes.fieldItem}
      data-testid='sortable-field-item'
      data-field-id={field.fieldId}
    >
      <ActionIcon
        className={classes.dragHandle}
        variant='transparent'
        size='sm'
        data-testid='field-drag-handle'
        {...attributes}
        {...listeners}
      >
        <IconGripVertical size={16} />
      </ActionIcon>

      <Menu opened={addFieldOpened} onClose={closeAddField} position='bottom-start' withinPortal>
        <Menu.Target>
          <Box style={{ flex: 1 }}>
            <Select
              className={classes.fieldSelect}
              placeholder={t('selectFieldToDisplay', 'Select field to display in card')}
              data={fieldOptions}
              value={field.fieldId === 'newField' ? 'newField' : field.fieldId || null}
              leftSection={getSelectedFieldIcon()}
              renderOption={({ option }) => {
                const fieldOption = fieldOptions.find((fo) => fo.value === option.value);
                return (
                  <Group gap={rem(8)}>
                    {fieldOption?.leftSection}
                    <Text>{option.label}</Text>
                  </Group>
                );
              }}
              onChange={(value) => {
                if (value === 'newField') {
                  setTimeout(() => openAddField(), 150);
                } else if (value) {
                  onFieldChange(field.id, value);
                }
              }}
              searchable
              clearable={false}
            />
          </Box>
        </Menu.Target>
        <Menu.Dropdown p={rem(16)} miw={rem(320)}>
          <AddFieldForm
            onSave={handleAddFieldSave}
            onCancel={closeAddField}
            loading={isLoadingNewField}
          />
        </Menu.Dropdown>
      </Menu>

      <ActionIcon
        className={classes.removeButton}
        variant='transparent'
        size='sm'
        onClick={() => onRemove(field.id)}
        data-testid='field-remove-button'
      >
        <IconX size={16} />
      </ActionIcon>
    </Box>
  );
};

const SortableColumnItem: React.FC<{
  choice: { id: string; label: string; color: string };
  swimlaneItem: { id: string; active: boolean };
}> = ({ choice, swimlaneItem }) => {
  const { classes } = useStyles();
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: choice.id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <Box ref={setNodeRef} style={style} className={classes.fieldItem}>
      <ActionIcon
        className={classes.dragHandle}
        variant='transparent'
        size='sm'
        {...attributes}
        {...listeners}
      >
        <IconGripVertical size={16} />
      </ActionIcon>
      <Text flex={1}>{choice.label}</Text>
      <Text size='xs' c={swimlaneItem.active ? 'green' : 'dimmed'}>
        {swimlaneItem.active ? 'Visible' : 'Hidden'}
      </Text>
    </Box>
  );
};

const KanbanConfigModal: React.FC<KanbanConfigModalProps> = ({ opened, onClose, onSave }) => {
  const { classes, theme } = useStyles();
  const { t } = useTranslate('workspace');
  const [isLoading, setIsLoading] = useState(false);
  const { object } = useWorkspaceContext();
  const [activeTab, setActiveTab] = useState<string | null>('config');
  const workspaceFields = object?.fields || [];

  // Local fields state to prevent re-renders when new fields are added
  const [localFields, setLocalFields] = useState<ObjectColumn[]>(workspaceFields);

  // Status field configuration (swimlane field)
  const [swimlaneFieldId, setSwimlaneFieldId] = useState<string>('');

  // Column ordering (objects with choice IDs and visibility from single select field)
  const [swimlaneOrder, setSwimlaneOrder] = useState<{ id: string; active: boolean }[]>([]);

  // Selected fields for cards (field IDs)
  const [selectedFields, setSelectedFields] = useState<SelectedField[]>([]);

  // Sync local fields with workspace fields when modal opens
  useEffect(() => {
    if (opened && workspaceFields.length > 0) {
      setLocalFields(workspaceFields);
    }
  }, [opened, workspaceFields]);

  // Available fields for selection
  const availableFields = localFields.filter(
    (field) =>
      field.type !== FieldTypes.CREATED_TIME &&
      field.type !== FieldTypes.MODIFIED_TIME &&
      field.type !== FieldTypes.CREATED_BY &&
      field.type !== FieldTypes.MODIFIED_BY &&
      field.id !== swimlaneFieldId
  );

  // Status field options (single select fields only)
  const statusFieldOptions = localFields
    .filter((field) => field.type === FieldTypes.SINGLE_SELECT)
    .map((field) => ({
      value: field.id!,
      label: field.name || field.id!,
    }));

  // Get current status field for column ordering
  const currentStatusField = localFields.find((field) => field.id === swimlaneFieldId);

  // Set up drag sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    })
  );

  // Initialize selected fields from fieldOrder
  useEffect(() => {
    // Initialize with empty fields when no configuration exists
    const emptyFields = Array.from({ length: MAX_VISIBLE_KANBAN_CARD_FIELDS }, (_, index) => ({
      id: `field-${index}`,
      fieldId: '',
      name: '',
      type: '',
    }));
    setSelectedFields(emptyFields);
  }, [localFields]);

  // Auto-select status field if only one exists
  useEffect(() => {
    if (!swimlaneFieldId && statusFieldOptions.length === 1) {
      setSwimlaneFieldId(statusFieldOptions[0].value);
    }
  }, [swimlaneFieldId, statusFieldOptions]);

  // Update swimlaneOrder when swimlane field changes
  useEffect(() => {
    if (swimlaneFieldId && currentStatusField?.options?.choices) {
      // Initialize with all choice IDs in their natural order if not already set
      if (swimlaneOrder.length === 0) {
        setSwimlaneOrder(
          currentStatusField.options.choices.map((choice) => ({ id: choice.id, active: true }))
        );
      }
    }
  }, [swimlaneFieldId, currentStatusField, swimlaneOrder.length]);

  // Handle drag end for field ordering
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      setSelectedFields((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over?.id);

        return arrayMove(items, oldIndex, newIndex);
      });
    }
  }, []);

  // Handle drag end for column ordering
  const handleColumnDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      setSwimlaneOrder((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over?.id);

        return arrayMove(items, oldIndex, newIndex);
      });
    }
  }, []);

  // Handle field change
  const handleFieldChange = useCallback(
    (id: string, fieldId: string) => {
      const field = localFields.find((f) => f.id === fieldId);
      if (field) {
        setSelectedFields((prev) =>
          prev.map((item) =>
            item.id === id
              ? { ...item, fieldId, name: field.name || fieldId, type: field.type }
              : item
          )
        );
      }
    },
    [localFields]
  );

  // Remove field from selection
  const handleRemoveField = useCallback((id: string) => {
    setSelectedFields((prev) =>
      prev.map((item) => (item.id === id ? { ...item, fieldId: '', name: '', type: '' } : item))
    );
  }, []);

  // Handle field added to local state
  const handleFieldAdded = useCallback((newField: ObjectColumn) => {
    setLocalFields((prev) => [...prev, newField]);
  }, []);

  // Handle save
  const handleSave = useCallback(async () => {
    if (!swimlaneFieldId) {
      return;
    }

    const filledFields = selectedFields.filter((field) => field.fieldId && field.fieldId !== '');

    if (filledFields.length === 0) {
      return;
    }

    setIsLoading(true);
    try {
      const config: KanbanViewConfig = {
        swimlaneFieldId,
        swimlaneOrder: swimlaneOrder, // Column ordering (choice IDs)
      };

      const fieldOrder = filledFields.map((field) => field.fieldId); // Field ordering for cards

      await onSave(config, fieldOrder);
      onClose();
    } catch (error) {
      console.error('Failed to save Kanban configuration:', error);
    } finally {
      setIsLoading(false);
    }
  }, [swimlaneFieldId, swimlaneOrder, selectedFields, onSave, onClose]);

  const filledFieldsCount = selectedFields.filter(
    (field) => field.fieldId && field.fieldId !== ''
  ).length;

  // Preview Card Component
  const PreviewCard: React.FC = () => {
    const { options: fieldTypeOptions } = useOptions();
    const previewFields = selectedFields
      .filter((field) => field.fieldId && field.fieldId !== '')
      .map((selectedField) => localFields.find((f) => f.id === selectedField.fieldId))
      .filter(Boolean) as ObjectColumn[];

    if (previewFields.length === 0) {
      return null;
    }

    return (
      <Box className={classes.previewContainer}>
        <Paper className={classes.previewCard}>
          {/* Preview Fields */}
          {previewFields.map((field) => {
            const fieldName = field.name || field.id || '';
            const fieldTypeOption = fieldTypeOptions.find((option) => option.value === field.type);

            return (
              <Box key={field.id} className={classes.previewFieldContainer}>
                {/* Field Label */}
                <Group gap={rem(6)} mb={rem(4)}>
                  {fieldTypeOption?.icon &&
                    React.cloneElement(fieldTypeOption.icon, {
                      size: 14,
                      color: theme.colors.decaGrey[6],
                    })}
                  <Text className={classes.previewFieldLabel}>{fieldName}</Text>
                </Group>
              </Box>
            );
          })}
        </Paper>
      </Box>
    );
  };

  return (
    <>
      <Drawer
        opened={opened}
        onClose={onClose}
        position='right'
        size='lg'
        className={classes.drawer}
        withCloseButton={false}
        trapFocus={false}
        transitionProps={{
          transition: 'slide-left',
          duration: 500,
          timingFunction: 'ease-in-out',
        }}
        overlayProps={{ opacity: 0.1, blur: 0 }}
        data-testid='kanban-config-modal'
        closeOnClickOutside={false}
      >
        {/* Header */}
        <Box className={classes.header} pt={rem(8)} data-testid='modal-header'>
          <Tabs
            value={activeTab}
            onChange={setActiveTab}
            className={classes.tabsContainer}
            data-testid='modal-tabs'
          >
            <Tabs.List>
              <Tabs.Tab value='config' data-testid='config-tab'>
                {t('customizeCardDisplay', 'Customize Card Display')}
              </Tabs.Tab>
              <Tabs.Tab value='preview' data-testid='preview-tab'>
                {t('preview', 'Preview')}
              </Tabs.Tab>
            </Tabs.List>
          </Tabs>
        </Box>

        {/* Content */}
        {activeTab === 'config' ? (
          <ScrollArea className={classes.content}>
            <Box className={classes.scrollableContent}>
              {/* Group by Section */}
              <Box className={classes.section} data-testid='group-by-section'>
                <Text className={classes.sectionTitle}>{t('groupBy', 'Group by')}</Text>
                <Radio.Group
                  value={swimlaneFieldId}
                  onChange={setSwimlaneFieldId}
                  className={classes.radioGroup}
                  data-testid='swimlane-radio-group'
                >
                  {statusFieldOptions.map((option) => (
                    <Radio
                      key={option.value}
                      value={option.value}
                      label={option.label}
                      data-testid={`radio-${option.value}`}
                    />
                  ))}
                </Radio.Group>
              </Box>

              <Divider mb={rem(20)} />

              {/* Column Ordering Section */}
              {currentStatusField?.options?.choices && (
                <>
                  <Box className={classes.section}>
                    <Text className={classes.sectionTitle}>{t('columnOrder', 'Column Order')}</Text>

                    <Box className={classes.infoBox}>
                      <Text className={classes.infoText}>
                        <IconAlertCircle size={16} />
                        {t('dragColumnsToReorder', 'Drag to reorder columns in your kanban board')}
                      </Text>
                    </Box>

                    <DndContext
                      sensors={sensors}
                      collisionDetection={closestCenter}
                      onDragEnd={handleColumnDragEnd}
                    >
                      <SortableContext
                        items={swimlaneOrder.map((item) => item.id)}
                        strategy={verticalListSortingStrategy}
                      >
                        <Stack gap={rem(8)}>
                          {swimlaneOrder.map((swimlaneItem) => {
                            const choice = currentStatusField?.options?.choices?.find(
                              (c) => c.id === swimlaneItem.id
                            );
                            if (!choice) return null;

                            return (
                              <SortableColumnItem
                                key={swimlaneItem.id}
                                choice={choice}
                                swimlaneItem={swimlaneItem}
                              />
                            );
                          })}
                        </Stack>
                      </SortableContext>
                    </DndContext>
                  </Box>

                  <Divider mb={rem(20)} />
                </>
              )}

              {/* Display Fields Section */}
              <Box className={classes.section}>
                <Text className={classes.sectionTitle}>{t('displayFields', 'Display fields')}</Text>

                <Box className={classes.infoBox}>
                  <Text className={classes.infoText}>
                    <IconSpeakerphone size={16} />
                    {t(
                      'chooseCardFields',
                      `Choose up to ${MAX_VISIBLE_KANBAN_CARD_FIELDS} fields to display in a Kanban card`
                    )}
                  </Text>
                </Box>

                {/* Selected Fields with Drag & Drop */}
                <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragEnd={handleDragEnd}
                >
                  <SortableContext
                    items={selectedFields.map((field) => field.id)}
                    strategy={verticalListSortingStrategy}
                  >
                    <Stack gap={rem(8)}>
                      {selectedFields.map((field) => (
                        <SortableFieldItem
                          key={field.id}
                          field={field}
                          onRemove={handleRemoveField}
                          onFieldChange={handleFieldChange}
                          availableFields={availableFields}
                          onFieldAdded={handleFieldAdded}
                          selectedFields={selectedFields}
                        />
                      ))}
                    </Stack>
                  </SortableContext>
                </DndContext>
              </Box>
            </Box>
          </ScrollArea>
        ) : (
          <ScrollArea className={classes.content}>
            <PreviewCard />
          </ScrollArea>
        )}

        {/* Footer */}
        <Box className={classes.footer} data-testid='modal-footer'>
          <Group justify='flex-end'>
            <Button
              variant='subtle'
              size='md'
              fw={500}
              onClick={onClose}
              data-testid='cancel-button'
            >
              {t('cancel', 'Cancel')}
            </Button>
            <Button
              size='md'
              fw={500}
              loading={isLoading}
              disabled={!swimlaneFieldId || filledFieldsCount === 0}
              onClick={handleSave}
              data-testid='save-button'
            >
              {t('add', 'Add')}
            </Button>
          </Group>
        </Box>
      </Drawer>
    </>
  );
};

export default React.memo(KanbanConfigModal);
