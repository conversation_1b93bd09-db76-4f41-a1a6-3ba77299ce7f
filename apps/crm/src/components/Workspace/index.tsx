import { useAppContext } from '@/contexts/AppContext';
import { BreadcrumbProvider } from '@/contexts/BreadcrumbContext';
import { WorkspaceContextProvider, useWorkspaceContext } from '@/contexts/WorkspaceContext';
import ComponentUtils from '@/utils/component';
import { Box, Center, Loader, LoadingOverlay, Text } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { DEFAULT_TABLE_FILTERS } from '@resola-ai/ui/components/DecaTable/constants';
import { PERMISSION_KEYS, isPermissionAllowed } from '@resola-ai/ui/components/DecaTable/utils';
import { useTranslate } from '@tolgee/react';
import { Suspense, useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import MainContainer from '../Common/MainContainer';
import { FilterEmptyState } from '../EmptyState/FilterEmptyState';
import { WorkspaceEmptyState } from '../EmptyState/WorkspaceEmptyState';
import NoPermissionAccess from '../NoPermissionAccess';
import Profile from '../Profile';
import useObjectStyles from './useObjectStyles';

const KanbanView = ComponentUtils.lazy(() => import('../KanBan'));
const GridView = ComponentUtils.lazy(() => import('./GridView'));
const MergeProfile = ComponentUtils.lazy(() => import('../MergeProfile'));

const WorkspaceSettings = () => {
  const {
    columns,
    data,
    activeView,
    loading,
    recordsLoading,
    object,
    rowSelection,
    selectedRowDetails,
    viewLoading,
    pendingViewSwitch,
  } = useWorkspaceContext();
  const { classes } = useObjectStyles();
  const [onAddingCol, setOnAddingCol] = useState(false);
  const { t } = useTranslate('workspace');
  const { recordId } = useParams();
  const { importLoading } = useAppContext();

  const [opened, { open, close }] = useDisclosure(false);

  // permissions check - only check permissions when data is loaded
  const canReadObject = object ? isPermissionAllowed(object.permission || {}, PERMISSION_KEYS.OBJECT_READ) : true;
  const canReadView = activeView ? isPermissionAllowed(activeView.permission || {}, PERMISSION_KEYS.VIEW_READ) : true;
  const canListView = object ? isPermissionAllowed(object.permission || {}, PERMISSION_KEYS.VIEW_LIST) : true;

  const isFiltering =
    activeView?.filters &&
    !recordsLoading &&
    !viewLoading &&
    JSON.stringify(activeView.filters) !== JSON.stringify(DEFAULT_TABLE_FILTERS);

  const emptyObj = useMemo(() => !columns || !columns.length, [columns]);

  useEffect(() => {
    Object.keys(rowSelection).length === 2 ? open() : close();
  }, [rowSelection]);

  if (loading)
    return (
      <Center maw='100%' h='100%' mx='auto'>
        <LoadingOverlay visible overlayProps={{ blur: 2 }} />
      </Center>
    );

  return (
    <MainContainer className={classes.container}>
      {!canReadObject ? (
        <NoPermissionAccess customText={t('errors.crmObjectReadDenied', { ns: 'common' })} />
      ) : (
        <>
          {importLoading && (
            <Center maw='100%' h='100%' mx='auto'>
              <LoadingOverlay
                visible
                overlayProps={{ blur: 0.1 }}
                loaderProps={{
                  children: (
                    <Text ta='center' mx='auto'>
                      <Loader size='md' />
                      <Text mt='md'>{t('importing')}</Text>
                    </Text>
                  ),
                }}
              />
            </Center>
          )}
          {recordId && <Profile />}
          {selectedRowDetails.length === 2 && activeView?.type === 'grid' && (
            <Suspense fallback={null}>
              <MergeProfile opened={opened} close={close} />
            </Suspense>
          )}
          <Box>
            {/* Conditionally render Kanban or Grid view */}
            <Suspense fallback={<Center h={200}><Loader /></Center>}>
              {activeView?.type === 'kanban' ? <KanbanView /> : <GridView />}
            </Suspense>
          </Box>
          {emptyObj && !recordsLoading && !viewLoading && !onAddingCol && (
            <WorkspaceEmptyState clickToAdd={() => setOnAddingCol(true)} />
          )}
          {isFiltering && !recordsLoading && !viewLoading && data.length === 0 && (
            <FilterEmptyState />
          )}
          {(object && !canListView) || (activeView && !canReadView && !viewLoading && !pendingViewSwitch) ? <NoPermissionAccess /> : null}
        </>
      )}
    </MainContainer>
  );
};

export const Workspace = () => {
  return (
    <WorkspaceContextProvider>
      <BreadcrumbProvider>
        <WorkspaceSettings />
      </BreadcrumbProvider>
    </WorkspaceContextProvider>
  );
};

export default Workspace;
