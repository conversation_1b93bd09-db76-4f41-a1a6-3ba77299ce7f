import { describe, expect, it } from 'vitest';
import {
  HEIGHT_OF_HEADER,
  NAVBAR_MAX_WIDTH,
  NAVBAR_MIN_WIDTH,
  NAVBAR_WIDTH,
} from './index';
import { MAX_VISIBLE_KANBAN_CARD_FIELDS } from './kanban';
import {
  CUSTOM_ACTION_COLORS,
  ColorCodes,
  Colors,
  ColumnIcon,
  ColumnWidth,
  Currency,
  CurrencyFormat,
  CurrencyFormatTypes,
  CurrencySymbol,
  DEFAULT_PROFILE_SETTINGS,
  FormatDate,
  FormatPhone,
  FormatTime,
  IconList,
  IgnoreActivityActorType,
  NOT_ALLOW_IMPORT_FIELDS,
  OptionsName,
  OptionsOrder,
  PREFERENCES,
  UNGROUPED_ID,
  UNSUPPORTED_MENTION_TYPES,
  UN_ADDROW_FIELDS,
} from './workspace';

describe('Constants', () => {
  describe('Layout Constants', () => {
    it('should export correct header height', () => {
      expect(HEIGHT_OF_HEADER).toBe(60);
    });

    it('should export correct navbar dimensions', () => {
      expect(NAVBAR_WIDTH).toBe(232);
      expect(NAVBAR_MIN_WIDTH).toBe(64);
      expect(NAVBAR_MAX_WIDTH).toBe(400);
    });

    it('should have logical navbar width constraints', () => {
      expect(NAVBAR_MIN_WIDTH).toBeLessThan(NAVBAR_WIDTH);
      expect(NAVBAR_WIDTH).toBeLessThan(NAVBAR_MAX_WIDTH);
    });
  });

  describe('Kanban Constants', () => {
    it('should export max visible kanban card fields', () => {
      expect(MAX_VISIBLE_KANBAN_CARD_FIELDS).toBe(5);
      expect(typeof MAX_VISIBLE_KANBAN_CARD_FIELDS).toBe('number');
      expect(MAX_VISIBLE_KANBAN_CARD_FIELDS).toBeGreaterThan(0);
    });
  });

  describe('Workspace Constants', () => {
    it('should export IconList with correct structure', () => {
      expect(Array.isArray(IconList)).toBe(true);
      expect(IconList.length).toBeGreaterThan(0);
      
      IconList.forEach((item) => {
        expect(item).toHaveProperty('value');
        expect(item).toHaveProperty('Icon');
        expect(typeof item.value).toBe('string');
        expect(typeof item.Icon).toBe('object'); // Icons are objects in test environment
      });
    });

    it('should export OptionsName constant', () => {
      expect(OptionsName).toBe('options');
    });

    it('should export Colors array', () => {
      expect(Array.isArray(Colors)).toBe(true);
      expect(Colors.length).toBeGreaterThan(0);
      
      const expectedColors = [
        'purple', 'red', 'blue', 'teal', 'yellow', 
        'green', 'violet', 'pink', 'navy', 'grey'
      ];
      expect(Colors).toEqual(expectedColors);
    });

    it('should export ColorCodes with proper structure', () => {
      expect(typeof ColorCodes).toBe('object');
      
      // Test a few key colors
      expect(ColorCodes.blue).toHaveProperty('color');
      expect(ColorCodes.blue).toHaveProperty('backgroundColor');
      expect(ColorCodes.red).toHaveProperty('color');
      expect(ColorCodes.red).toHaveProperty('backgroundColor');
    });

    it('should export OptionsOrder constants', () => {
      expect(OptionsOrder.MANUAL).toBe('manual');
      expect(OptionsOrder.ASC).toBe('asc');
      expect(OptionsOrder.DESC).toBe('desc');
    });

    it('should export FormatDate constants', () => {
      expect(FormatDate.ja).toBe('YYYY/MM/DD');
      expect(FormatDate.en).toBe('MM/DD/YYYY');
      expect(FormatDate.eu).toBe('DD/MM/YYYY');
      expect(FormatDate.long).toBe('MMMM D, YYYY');
      expect(FormatDate.longJa).toBe('YYYY年MM月DD日');
    });

    it('should export FormatTime constants', () => {
      expect(FormatTime['12h']).toBe('hh:mm A');
      expect(FormatTime['24h']).toBe('HH:mm');
    });

    it('should export ColumnWidth as object with numeric values', () => {
      expect(typeof ColumnWidth).toBe('object');
      
      Object.values(ColumnWidth).forEach((width) => {
        expect(typeof width).toBe('number');
        expect(width).toBeGreaterThan(0);
      });
    });

    it('should export ColumnIcon as object with function values', () => {
      expect(typeof ColumnIcon).toBe('object');
      
      Object.values(ColumnIcon).forEach((IconComponent) => {
        expect(typeof IconComponent).toBe('object'); // Icons are objects in test environment
      });
    });

    it('should export Currency constants', () => {
      expect(Currency.usd).toBe('USD');
      expect(Currency.yen).toBe('JPY');
    });

    it('should export CurrencySymbol mappings', () => {
      expect(CurrencySymbol[Currency.usd]).toBe('$');
      expect(CurrencySymbol[Currency.yen]).toBe('¥');
    });

    it('should export CurrencyFormatTypes', () => {
      expect(CurrencyFormatTypes.commaOnly).toBe('commaOnly');
      expect(CurrencyFormatTypes.commaPeriod).toBe('commaPeriod');
      expect(CurrencyFormatTypes.periodComma).toBe('periodComma');
      expect(CurrencyFormatTypes.spaceComma).toBe('spaceComma');
      expect(CurrencyFormatTypes.spacePeriod).toBe('spacePeriod');
      expect(CurrencyFormatTypes.periodOnly).toBe('periodOnly');
    });

    it('should export CurrencyFormat with proper structure', () => {
      expect(typeof CurrencyFormat).toBe('object');
      
      Object.values(CurrencyFormat).forEach((format) => {
        expect(format).toHaveProperty('thousand');
        expect(format).toHaveProperty('decimal');
        expect(typeof format.thousand).toBe('string');
        expect(typeof format.decimal).toBe('string');
      });
    });

    it('should export FormatPhone constants', () => {
      expect(FormatPhone.jp).toBe('xxx-xxxx-xxxx');
      expect(FormatPhone.en).toBe('xx-xxx-xxx-xxx');
      expect(FormatPhone.northUS).toBe('+x-xxx-xxx-xxxx');
      expect(FormatPhone.general).toBe('+x-(xxx)-xxx-xxxx');
    });

    it('should export PREFERENCES constants', () => {
      expect(PREFERENCES.activities).toBe('activities');
      expect(PREFERENCES.identities).toBe('identities');
      expect(PREFERENCES.files).toBe('files');
      expect(PREFERENCES.history).toBe('history');
      expect(PREFERENCES.longText).toBe('longText');
      expect(PREFERENCES.customObject).toBe('customObject');
    });

    it('should export IgnoreActivityActorType array', () => {
      expect(Array.isArray(IgnoreActivityActorType)).toBe(true);
      expect(IgnoreActivityActorType).toContain('mail');
      expect(IgnoreActivityActorType).toContain('sms');
      expect(IgnoreActivityActorType).toContain('line');
    });

    it('should export UN_ADDROW_FIELDS array', () => {
      expect(Array.isArray(UN_ADDROW_FIELDS)).toBe(true);
      expect(UN_ADDROW_FIELDS.length).toBeGreaterThan(0);
    });

    it('should export CUSTOM_ACTION_COLORS array', () => {
      expect(Array.isArray(CUSTOM_ACTION_COLORS)).toBe(true);
      const expectedColors = ['red', 'violet', 'green', 'yellow', 'purple', 'grey'];
      expect(CUSTOM_ACTION_COLORS).toEqual(expectedColors);
    });

    it('should export UNGROUPED_ID constant', () => {
      expect(UNGROUPED_ID).toBe('default');
    });

    it('should export UNSUPPORTED_MENTION_TYPES array', () => {
      expect(Array.isArray(UNSUPPORTED_MENTION_TYPES)).toBe(true);
      expect(UNSUPPORTED_MENTION_TYPES.length).toBeGreaterThan(0);
    });

    it('should export DEFAULT_PROFILE_SETTINGS array', () => {
      expect(Array.isArray(DEFAULT_PROFILE_SETTINGS)).toBe(true);
      
      DEFAULT_PROFILE_SETTINGS.forEach((setting) => {
        expect(setting).toHaveProperty('type');
        expect(setting).toHaveProperty('enabled');
        expect(typeof setting.type).toBe('string');
        expect(typeof setting.enabled).toBe('boolean');
      });
    });

    it('should export NOT_ALLOW_IMPORT_FIELDS array', () => {
      expect(Array.isArray(NOT_ALLOW_IMPORT_FIELDS)).toBe(true);
      expect(NOT_ALLOW_IMPORT_FIELDS.length).toBeGreaterThan(0);
    });
  });

  describe('Constant Relationships', () => {
    it('should have consistent color definitions between Colors and ColorCodes', () => {
      Colors.forEach((color) => {
        expect(ColorCodes).toHaveProperty(color);
      });
    });

    it('should have consistent currency definitions', () => {
      Object.values(Currency).forEach((currency) => {
        expect(CurrencySymbol).toHaveProperty(currency);
      });
    });

    it('should have consistent currency format type definitions', () => {
      Object.values(CurrencyFormatTypes).forEach((formatType) => {
        expect(CurrencyFormat).toHaveProperty(formatType);
      });
    });
  });
});
