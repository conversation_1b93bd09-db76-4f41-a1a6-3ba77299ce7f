import AppConfig from '@/configs';
import { useRecord } from '@/hooks';
import type { BoxCard, Record, WSObject } from '@/models';
import { ObjectAPI, RecordAPI, TagAPI } from '@/services/api';
import type { Active, Over } from '@dnd-kit/core';
import { arrayMove } from '@dnd-kit/sortable';
import { usePathParams } from '@resola-ai/ui/hooks';
import type React from 'react';
import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import useSWR from 'swr';
import type { ProfileData } from '../components/Profile/ObjectFields/RenderProfileTypes';
import { useAppContext } from './AppContext';
import { useWorkspaceContext } from './WorkspaceContext';

export const BOX_CARDS = {
  profile: 'profile',
  objectFields: 'objectFields',
  forms: 'forms',
  tags: 'tags',
};
const BOX_HEIGHT = '250';
type Form = {
  linkedObj?: WSObject;
  records?: Record[];
  template?: string;
};

const INITIAL_BOX = [{ id: BOX_CARDS.tags, height: BOX_HEIGHT }];

const useProfile = () => {
  const { wsId, id: objectId, recordId } = useParams();
  const [dragging, setDragging] = useState(false);
  const {
    object,
    mutateTags,
    refetchObject,
    currRecordIndex,
    profileRecord: initialProfileRecord,
    setProfileRecord,
    activeView,
    data,
    onSaveData,
  } = useWorkspaceContext();
  const { objects } = useAppContext();
  const [sortItems, setSortItems] = useState<BoxCard[]>([]);
  const [forms, setForms] = useState<Form[]>([]);
  const [editFields, setEditFields] = useState<{ [key: string]: boolean }>({});
  const [customObjectFields, setCustomObjectFields] = useState<ProfileData[]>([]);
  const navigate = useNavigate();
  const { createPathWithLngParam } = usePathParams();

  const MAIN_URL = useMemo(
    () => `${AppConfig.BASE_PATH}workspace/${wsId}/objects/${objectId}`,
    [wsId, objectId]
  );
  const validRecordIdx = useMemo(() => currRecordIndex !== -1, [currRecordIndex]);

  const { record: profileRecord, mutateRecord: mutateProfile } = useRecord(
    wsId,
    objectId,
    initialProfileRecord ? null : recordId,
    false
  );

  // Use initialProfileRecord when available, otherwise use the fetched record
  const currentProfileRecord = initialProfileRecord || profileRecord;

  const { data: tags = [], mutate } = useSWR(
    wsId && objectId && recordId
      ? `data/workspaces/${wsId}/tags/${objectId}?recordId=${recordId}`
      : null,
    () => TagAPI.getList(wsId || '', objectId || '', recordId || ''),
    { revalidateOnFocus: false }
  );

  const fetchFormData = useCallback(async () => {
    if (!recordId || !object?.childObjects || !object?.references) {
      setForms([]);
      return;
    }

    const { references } = object;
    const handleForm = object.childObjects.map(async (childObj) => {
      const ref = Object.entries(references)
        .map(([key]) => references[key].find((o) => o.objectId === childObj.id))
        .filter(Boolean);

      if (!ref.length) return;

      const linkedObj = objects?.find((obj) => obj.id === ref[0].objectId);
      if (!linkedObj) return;

      const [template, refObj] = await Promise.all([
        ObjectAPI.findTemplate(wsId || '', linkedObj.id || ''),
        RecordAPI.getRecords(
          wsId || '',
          ref[0].objectId,
          `{"${ref[0].fieldId}.recordId":"${recordId}"}`
        ),
      ]);

      if (!refObj?.length) return undefined;

      return {
        linkedObj,
        records: refObj.map((record) => ({ ...record, objectId: linkedObj.id })),
        template: template?.content ? atob(template.content) : '',
      };
    });

    const formResults = await Promise.all(handleForm);
    setForms(formResults.filter(Boolean) as Form[]);
  }, [object, recordId, wsId, objects]);

  useEffect(() => {
    fetchFormData();
  }, [fetchFormData]);

  const handleAddTag = useCallback(
    async (name: string) => {
      if (!wsId || !objectId || !recordId) return;

      const payload = {
        name,
        value: name,
        color: 'grey',
        objectId,
        recordId,
      };
      await TagAPI.save(wsId, payload);
      mutate();
      mutateTags();
    },
    [wsId, objectId, recordId, mutate, mutateTags]
  );

  const handleRemoveTag = useCallback(
    async (id: string) => {
      if (!wsId || !recordId) return;

      await TagAPI.delete(wsId, id, recordId);
      mutate();
      mutateTags();
    },
    [wsId, recordId, mutate, mutateTags]
  );

  const onSetDragging = useCallback((value: boolean) => {
    setDragging(value);
  }, []);

  const mergeBoxCards = useCallback((curr, append) => {
    const map = new Map(curr.map((item) => [item.id, item]));

    const result = append.map((form) => {
      const id = form?.linkedObj?.id as string;
      return map.has(id) ? map.get(id) : { id, height: BOX_HEIGHT };
    });

    if (map.has(BOX_CARDS.tags)) {
      result.push(map.get(BOX_CARDS.tags));
    }
    if (map.has(BOX_CARDS.objectFields)) {
      result.push(map.get(BOX_CARDS.objectFields));
    }
    return result;
  }, []);

  useEffect(() => {
    const currBoxCards = object?.displaySettings?.boxCards?.length
      ? object?.displaySettings?.boxCards
      : INITIAL_BOX;
    const result = mergeBoxCards(currBoxCards, forms || []);
    setSortItems(result.filter((item) => item.id !== 'forms'));
  }, [object?.displaySettings?.boxCards, forms, mergeBoxCards]);

  const onUpdateSettings = useCallback(
    async (boxCards: BoxCard[]) => {
      if (!wsId || !object) return;

      setSortItems(boxCards);
      try {
        await ObjectAPI.update(wsId, { ...object, displaySettings: { boxCards } });
        await refetchObject();
      } catch (error) {
        console.error('Failed to update box card settings:', error);
      }
    },
    [wsId, object]
  );

  const handleDragEnd = useCallback(
    (active: Active, over: Over) => {
      const oldIndex = sortItems.findIndex((item) => item.id === active.id);
      const newIndex = sortItems.findIndex((item) => item.id === over.id);
      const newItems = arrayMove(sortItems, oldIndex, newIndex);
      onUpdateSettings(newItems);
    },
    [sortItems, onUpdateSettings]
  );

  const handleChangeSize = useCallback(
    (id: string, height: string) => {
      const existingItem = sortItems.find((item) => item.id === id);
      let newItems: BoxCard[];
      if (existingItem) {
        newItems = sortItems.map((item) => (item.id === id ? { ...item, height } : item));
      } else {
        newItems = [...sortItems, { id, height }];
      }
      onUpdateSettings(newItems);
    },
    [sortItems, onUpdateSettings]
  );

  const getForm = useCallback(
    (id: string) => {
      return forms?.find((form) => form.linkedObj?.id === id);
    },
    [forms]
  );

  const resetEditFields = useCallback(() => {
    setEditFields({});
  }, []);

  useEffect(() => {
    if (currRecordIndex) resetEditFields();
  }, [currRecordIndex, resetEditFields]);

  const onEditField = useCallback((id: string, value = true) => {
    setEditFields((prev) => {
      const prevFields = Object.keys(prev).reduce(
        (acc, key) => ({
          ...(acc as any),
          [key]: false,
        }),
        {}
      );
      return { ...prevFields, [id]: value };
    });
  }, []);

  const closeProfile = useCallback(() => {
    navigate(createPathWithLngParam(`${MAIN_URL}/views/${activeView?.id}`));
  }, [navigate, createPathWithLngParam, MAIN_URL, activeView?.id]);

  const handleSaveRecord = useCallback(
    async (value, colId) => {
      if (!wsId || !objectId || !recordId || !onSaveData || !data) return;

      try {
        // Find the record index in the workspace data array
        const recordIndex = data.findIndex((record) => record.id === recordId);

        if (recordIndex === -1) {
          console.warn(`Record with ID ${recordId} not found in workspace data`);
          return;
        }

        // Optimistically update the profile state for immediate UI feedback
        const workspaceRecord = data[recordIndex];
        const optimisticProfileRecord = {
          ...workspaceRecord,
          [colId]: value,
        };
        setProfileRecord(optimisticProfileRecord);
        mutateProfile(optimisticProfileRecord, { revalidate: false });

        // Use the proven onSaveData function from WorkspaceContext
        // This now handles both grid and kanban views correctly
        await onSaveData(value, recordIndex, colId, false);
      } catch (error) {
        console.error('Failed to save record:', error);
        // Revert optimistic update on error
        setProfileRecord(currentProfileRecord);
        mutateProfile(currentProfileRecord, { revalidate: false });
      }
    },
    [
      wsId,
      objectId,
      recordId,
      onSaveData,
      data,
      setProfileRecord,
      mutateProfile,
      currentProfileRecord,
    ]
  );

  const contextValue = useMemo(
    () => ({
      profile: currentProfileRecord,
      mutateProfile,
      tags,
      handleAddTag,
      handleRemoveTag,
      dragging,
      onSetDragging,
      sortItems,
      handleDragEnd,
      handleChangeSize,
      forms,
      getForm,
      editFields,
      onEditField,
      resetEditFields,
      customObjectFields,
      setCustomObjectFields,
      closeProfile,
      handleSaveRecord,
    }),
    [
      validRecordIdx,
      currentProfileRecord,
      mutateProfile,
      tags,
      handleAddTag,
      handleRemoveTag,
      dragging,
      onSetDragging,
      sortItems,
      handleDragEnd,
      handleChangeSize,
      forms,
      getForm,
      editFields,
      onEditField,
      resetEditFields,
      customObjectFields,
      closeProfile,
      handleSaveRecord,
    ]
  );

  return contextValue;
};

export type ProfileContextType = ReturnType<typeof useProfile>;

const context = createContext<ProfileContextType | null>(null);

export const ProfileContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useProfile();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useProfileContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useProfileContext must be used inside ProfileContextProvider');
  }

  return value;
};
