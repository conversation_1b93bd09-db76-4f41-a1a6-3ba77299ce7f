import { ObjectAPI, RecordAPI, TagAPI } from '@/services/api';
import { usePathParams } from '@resola-ai/ui/hooks';
import { act, render, renderHook, screen, waitFor } from '@testing-library/react';
import { fireEvent } from '@testing-library/react';
import { useParams } from 'react-router-dom';
import useSWR from 'swr';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { AppContextProvider, useAppContext } from './AppContext';
import { ProfileContextProvider, useProfileContext } from './ProfileContext';
import { useWorkspaceContext } from './WorkspaceContext';

// Create mockedNavigate
const mockedNavigate = vi.fn();

// Mock react-router-dom
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockedNavigate,
    useParams: vi.fn(),
  };
});

// Mock contexts
vi.mock('./WorkspaceContext', () => ({
  useWorkspaceContext: vi.fn(),
  WorkspaceContextProvider: ({ children }) => children,
}));

vi.mock('./AppContext', () => ({
  useAppContext: vi.fn(),
  AppContextProvider: ({ children }) => children,
}));

// Mock usePathParams
vi.mock('@resola-ai/ui/hooks', () => ({
  usePathParams: vi.fn(),
}));

// Mock useRecord hook
const mockMutateRecord = vi.fn();
vi.mock('@/hooks', () => ({
  useRecord: vi.fn(() => ({
    record: null,
    mutateRecord: mockMutateRecord,
  })),
}));

// Mock SWR
vi.mock('swr', () => ({
  __esModule: true,
  default: vi.fn(),
}));

// Mock Mantine hooks to make debounced callbacks execute immediately
vi.mock('@mantine/hooks', async () => {
  const actual = await vi.importActual('@mantine/hooks');
  return {
    ...actual,
    useDebouncedCallback: (fn: any) => fn, // Execute immediately for tests
  };
});

// Mock API services
vi.mock('@/services/api', () => ({
  ObjectAPI: {
    findTemplate: vi.fn(),
    update: vi.fn(),
  },
  RecordAPI: {
    getRecordById: vi.fn(),
    getRecords: vi.fn(),
    update: vi.fn(),
    save: vi.fn(),
    updateByField: vi.fn(),
  },
  TagAPI: {
    getList: vi.fn(),
    save: vi.fn(),
    delete: vi.fn(),
  },
}));

// Mock configs
vi.mock('@/configs', () => ({
  default: {
    BASE_PATH: '/app/',
  },
}));

const TestComponent = () => {
  const context = useProfileContext();
  return (
    <div>
      <h1>Test Component</h1>
      <div data-testid='tags-count'>{context.tags?.length || 0}</div>
      <div data-testid='dragging'>{context.dragging ? 'true' : 'false'}</div>
      <button type='button' onClick={() => context.onSetDragging(true)}>Set Dragging</button>
      <button type='button' onClick={() => context.handleAddTag('New Tag')}>Add Tag</button>
      <button type='button' onClick={() => context.handleRemoveTag('tag-1')}>Remove Tag</button>
      <button type='button' onClick={context.closeProfile}>Close Profile</button>
    </div>
  );
};

// Create a wrapper with both required contexts
const TestWrapper = ({ children }) => (
  <AppContextProvider>
    <ProfileContextProvider>{children}</ProfileContextProvider>
  </AppContextProvider>
);

describe('ProfileContext', () => {
  const mockCreatePathWithLngParam = vi.fn((path) => `/${path}`);
  const mockProfile = { id: 'profile-1', name: 'Test Profile' };
  const mockTags = [
    { id: 'tag-1', name: 'Tag 1' },
    { id: 'tag-2', name: 'Tag 2' },
  ];
  const mockObject = {
    id: 'object-1',
    fields: [{ id: 'field-1', name: 'Field 1' }],
    childObjects: [{ id: 'child-object-1', pinned: true }],
    references: {
      'child-object-1': [{ objectId: 'child-object-1', fieldId: 'ref-field-1' }],
    },
  };
  const mockObjects = [{ id: 'child-object-1', name: 'Child Object' }];
  const mockActiveView = { id: 'view-1' };

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock useAppContext with proper casting
    (useAppContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      objects: mockObjects,
    });

    // Mock useParams with proper casting
    (useParams as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      wsId: 'workspace-1',
      id: 'object-1',
      recordId: 'record-1',
    });

    // Mock usePathParams with proper casting
    (usePathParams as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      createPathWithLngParam: mockCreatePathWithLngParam,
    });

    // Mock useWorkspaceContext with proper casting
    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      object: mockObject,
      mutateTags: vi.fn(),
      refetchObject: vi.fn(),
      currRecordIndex: -1,
      objects: mockObjects,
      profileRecord: mockProfile,
      activeView: mockActiveView,
      data: [{ id: 'record-1', 'field-1': 'Current Value' }],
      onSaveData: vi.fn(),
    });

    // Mock useSWR with proper casting
    (useSWR as unknown as ReturnType<typeof vi.fn>).mockImplementation((key) => {
      if (key?.includes('tags')) {
        return { data: mockTags, mutate: vi.fn() };
      }
      return { data: mockProfile, mutate: vi.fn() };
    });

    // Mock API calls with proper casting
    (ObjectAPI.findTemplate as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({
      content: btoa('<template></template>'),
    });
    (ObjectAPI.update as unknown as ReturnType<typeof vi.fn>).mockResolvedValue(mockObject);
    (RecordAPI.getRecords as unknown as ReturnType<typeof vi.fn>).mockResolvedValue([
      { id: 'record-1' },
    ]);
    (RecordAPI.getRecordById as unknown as ReturnType<typeof vi.fn>).mockResolvedValue(mockProfile);
    (TagAPI.getList as unknown as ReturnType<typeof vi.fn>).mockResolvedValue(mockTags);
    (TagAPI.save as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({
      id: 'new-tag',
      name: 'New Tag',
    });
    (TagAPI.delete as unknown as ReturnType<typeof vi.fn>).mockResolvedValue(true);
  });

  it('provides profile context to children', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );
    });

    // Wait for async operations to complete
    await waitFor(() => {
      expect(screen.getByText('Test Component')).toBeInTheDocument();
      expect(screen.getByTestId('tags-count').textContent).toBe('2');
      expect(screen.getByTestId('dragging').textContent).toBe('false');
    });
  });

  it('handles setting dragging state', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );
    });

    // Click the button to set dragging to true
    await act(async () => {
      fireEvent.click(screen.getByText('Set Dragging'));
    });

    expect(screen.getByTestId('dragging').textContent).toBe('true');
  });

  it('handles adding a tag', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Add Tag'));
    });

    expect(TagAPI.save).toHaveBeenCalledWith('workspace-1', {
      name: 'New Tag',
      value: 'New Tag',
      color: 'grey',
      objectId: 'object-1',
      recordId: 'record-1',
    });
  });

  it('handles removing a tag', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Remove Tag'));
    });

    expect(TagAPI.delete).toHaveBeenCalledWith('workspace-1', 'tag-1', 'record-1');
  });

  it('handles closing the profile', async () => {
    // No need to mock navigate again since we have it globally
    await act(async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Close Profile'));
    });

    expect(mockCreatePathWithLngParam).toHaveBeenCalledWith(
      '/app/workspace/workspace-1/objects/object-1/views/view-1'
    );
    expect(mockedNavigate).toHaveBeenCalled();
  });

  it('initializes forms from child objects', async () => {
    // Test the form initialization logic
    await act(async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );
    });

    // Give time for the useEffect to trigger fetchFormData
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 100));
    });

    await waitFor(
      () => {
        expect(ObjectAPI.findTemplate).toHaveBeenCalledWith('workspace-1', 'child-object-1');
        expect(RecordAPI.getRecords).toHaveBeenCalledWith(
          'workspace-1',
          'child-object-1',
          '{"ref-field-1.recordId":"record-1"}'
        );
      },
      { timeout: 5000 }
    );
  });

  it('handles saving record data', async () => {
    const mockOnSaveData = vi.fn();
    const mockSetProfileRecord = vi.fn();
    
    // Clear previous calls
    mockMutateRecord.mockClear();
    
    // Mock useWorkspaceContext to include all required functions
    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      object: mockObject,
      mutateTags: vi.fn(),
      refetchObject: vi.fn(),
      currRecordIndex: -1,
      objects: mockObjects,
      profileRecord: mockProfile,
      activeView: mockActiveView,
      data: [{ id: 'record-1', 'field-1': 'Current Value' }],
      onSaveData: mockOnSaveData,
      setProfileRecord: mockSetProfileRecord,
    });

    const wrapper = ({ children }) => <TestWrapper>{children}</TestWrapper>;
    const { result } = renderHook(() => useProfileContext(), { wrapper });

    await result.current.handleSaveRecord('New Value', 'field-1');

    expect(mockOnSaveData).toHaveBeenCalledWith('New Value', 0, 'field-1', false);
    expect(mockSetProfileRecord).toHaveBeenCalledWith({
      id: 'record-1',
      'field-1': 'New Value',
    });
    expect(mockMutateRecord).toHaveBeenCalledWith(
      {
        id: 'record-1',
        'field-1': 'New Value',
      },
      { revalidate: false }
    );
  });

  it('throws error when used outside of ProfileContextProvider', () => {
    // Expect the useProfileContext hook to throw when used outside of the provider
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    expect(() => {
      renderHook(() => useProfileContext());
    }).toThrow('useProfileContext must be used inside ProfileContextProvider');

    consoleSpy.mockRestore();
  });
});
