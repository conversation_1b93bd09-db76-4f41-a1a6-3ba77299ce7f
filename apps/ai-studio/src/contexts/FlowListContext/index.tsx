import { DEFAULT_PER_PAGE } from '@/constants/common';
import { useHandleApiError } from '@/hooks/useHandleApiError';
import { useRestrictLimitParam } from '@/hooks/useRestrictLimitParam';
import { type Flow, type FlowCreatePayload, type FlowNodeData, FlowNodeType } from '@/models/flow';
import { FlowApi } from '@/services/api/flow';
import { LayoutType } from '@/types';
import { showSuccessNotification } from '@/utils/notification';
import { useTranslate } from '@tolgee/react';
import { throttle } from 'lodash';
import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import useSWR from 'swr';
import { ulid } from 'ulid';

export const useListOfFlows = (
  workspaceId: string,
  limit: number,
  cursor?: string,
  searchValue?: string
) => {
  return useSWR(
    workspaceId ? [workspaceId, 'flows/list', limit, cursor, searchValue] : null,
    () => {
      return FlowApi.getList(workspaceId, limit, cursor ?? '', searchValue ?? '');
    },
    {
      revalidateOnFocus: false,
    }
  );
};

export const useFlowList = () => {
  const navigate = useNavigate();
  const { t } = useTranslate('flow');
  const { workspaceId } = useParams();
  const [_, setSearchParams] = useSearchParams();
  const [limit, setLimit] = useState<number>(DEFAULT_PER_PAGE); // Ensure default is correctly set
  const [search, setSearch] = useState('');
  const [cursor, setCursor] = useState('');
  const [filter, setFilter] = useState('model');
  const [layout, setLayout] = useState<LayoutType>(LayoutType.GRID);
  const [openedCreateModal, setOpenedCreateModal] = useState(false);
  const [openedWaitingModal, setOpenedWaitingModal] = useState(false);
  const [openedTemplateModal, setOpenedTemplateModal] = useState(false);
  const [isCreatingFromTemplate, setIsCreateFromTemplate] = useState(false);
  const [openedEditModal, setOpenedEditModal] = useState(false);
  const [flowList, setFlowList] = useState<Flow[]>([]);
  const {
    data: flowlistSuccessResponse,
    error,
    isLoading,
    mutate: refetchList,
  } = useListOfFlows(workspaceId ?? '', limit, cursor, search);
  const { handleApiError } = useHandleApiError();

  const isFullWidth = layout === LayoutType.LIST;

  const cursorObject = useMemo(() => {
    if (!flowlistSuccessResponse)
      return {
        nextCursor: undefined,
        prevCursor: undefined,
        currentPage: 1,
      };
    return {
      nextCursor: flowlistSuccessResponse?.nextCursor,
      prevCursor: flowlistSuccessResponse?.prevCursor,
      currentPage: flowlistSuccessResponse?.currentPage || 1,
    };
  }, [flowlistSuccessResponse]);

  const handleFilter = useCallback((value: string | null) => {
    value && setFilter(value);
  }, []);

  const handleOnChangeSearch = throttle((value) => {
    setCursor('');
    setSearchParams((prev) => {
      prev.set('cursor', '');
      return prev;
    });
    setSearch(value);
  }, 300);

  const handleCreateFromScratch = useCallback(() => {
    console.log('handleCreateFromScratch');
    setIsCreateFromTemplate(false);
    setOpenedCreateModal(true);
  }, []);

  const handleCloseCreateFlowModal = useCallback(() => {
    setOpenedCreateModal(false);
    isCreatingFromTemplate && setIsCreateFromTemplate(false);
  }, [isCreatingFromTemplate]);

  const handleImportFromFile = useCallback(
    async (file: File) => {
      try {
        await FlowApi.import(workspaceId ?? '', file);
        await refetchList();
        showSuccessNotification(t('notifications.importedSuccessfully', { ns: 'common' }));
      } catch (error) {
        handleApiError(error);
      }
    },
    [workspaceId, refetchList, t, handleApiError]
  );

  const handleCreateFromTemplate = useCallback(() => {
    console.log('handleCreateFromTemplate');
    setOpenedTemplateModal(true);
  }, []);

  const handleSetCursor = useCallback((cursor?: string) => {
    typeof cursor !== 'undefined' && setCursor(cursor);
  }, []);

  const handleCreateFlow = useCallback(
    async (data: { title: string; description: string }) => {
      const newId = ulid();
      const newNodeId = ulid();
      const newFlowData: FlowCreatePayload = {
        name: data.title,
        description: data.description,
        triggers: {
          [newId]: {
            id: newId,
            name: FlowNodeType.NewTrigger,
            displayName: t('defaultNameTriggerNode'),
            description: '',
            settings: {},
            next: newNodeId,
          } as FlowNodeData,
        },
        nodes: {
          [newNodeId]: {
            id: newNodeId,
            name: FlowNodeType.EmptyNode,
            displayName: t('defaultNameActionNode'),
            description: '',
            settings: {},
            next: '',
            prev: newId,
          } as FlowNodeData,
        },
        status: 'enabled',
      };
      isCreatingFromTemplate && setOpenedWaitingModal(true);
      try {
        const res = await FlowApi.create(workspaceId ?? '', newFlowData);
        navigate(`/studio/${workspaceId}/flows/${res.id}`);
      } catch (error) {
        handleApiError(error);
        setOpenedCreateModal(false);
        setOpenedWaitingModal(false);
        setIsCreateFromTemplate(false);
      }
    },
    [isCreatingFromTemplate, navigate, workspaceId, t, handleApiError]
  );

  const handleSelectTemplateToCreate = useCallback((flow: Flow) => {
    console.log(flow);
    setOpenedCreateModal(true);
    setOpenedTemplateModal(false);
    setIsCreateFromTemplate(true);
  }, []);

  const handleEditFLow = useCallback(
    async (editedFlow: Flow) => {
      try {
        let savedFlow: Flow = editedFlow;
        setFlowList((prev) => {
          const index = prev.findIndex((f) => f.id === editedFlow.id);
          savedFlow = { ...prev[index], ...editedFlow };
          prev[index] = savedFlow;
          return [...prev];
        });
        setOpenedEditModal(false);
        await FlowApi.update(workspaceId ?? '', savedFlow!);
      } catch (error) {
        handleApiError(error);
      } finally {
        setOpenedEditModal(false);
      }
    },
    [workspaceId, handleApiError]
  );

  const handleDuplicateFlow = useCallback(
    async (flow: Flow) => {
      try {
        const res = await FlowApi.getById(workspaceId ?? '', flow.id);
        const { id, updatedAt, createdAt, status, ...originalData } = res ?? {};
        const newFlowData = {
          ...originalData,
          name: `${flow.name} (Copy)`,
          status: 'enabled' as Flow['status'],
        };
        await FlowApi.create(workspaceId ?? '', newFlowData);
        refetchList();
        showSuccessNotification(t('flow.duplicated', { ns: 'common' }));
      } catch (error) {
        handleApiError(error);
      }
    },
    [workspaceId, refetchList, handleApiError, t]
  );

  const handleDeleteFlow = useCallback(
    async (flow: Flow) => {
      try {
        await FlowApi.delete(workspaceId ?? '', flow.id);

        const flowListAfterDelete = flowList.filter((f) => f.id !== flow.id);
        if (!flowListAfterDelete?.length) {
          setSearchParams((prev) => {
            prev.set('cursor', '');
            return prev;
          });
          handleSetCursor('');
          setTimeout(() => {
            refetchList();
          }, 500);
        } else {
          refetchList();
        }
      } catch (error) {
        handleApiError(error);
      }
    },
    [workspaceId, flowList, handleSetCursor, refetchList, setSearchParams, handleApiError]
  );

  useEffect(() => {
    const flowList = flowlistSuccessResponse?.data;
    flowList && flowList?.length > 0 ? setFlowList(flowList) : setFlowList([]);
  }, [flowlistSuccessResponse]);

  useRestrictLimitParam(limit, setLimit, setSearchParams);

  return useMemo(
    () => ({
      limit,
      error,
      cursor,
      search,
      filter,
      layout,
      flowList,
      setLimit,
      setLayout,
      isLoading,
      setSearch,
      refetchList,
      isFullWidth,
      setFlowList,
      cursorObject,
      handleFilter,
      handleSetCursor,
      handleEditFLow,
      openedEditModal,
      handleCreateFlow,
      handleDeleteFlow,
      openedCreateModal,
      openedWaitingModal,
      setOpenedEditModal,
      handleDuplicateFlow,
      openedTemplateModal,
      handleOnChangeSearch,
      setOpenedCreateModal,
      handleImportFromFile,
      setOpenedWaitingModal,
      setOpenedTemplateModal,
      isCreatingFromTemplate,
      handleCreateFromScratch,
      handleCreateFromTemplate,
      handleCloseCreateFlowModal,
      handleSelectTemplateToCreate,
    }),
    [
      limit,
      error,
      search,
      filter,
      layout,
      cursor,
      flowList,
      isLoading,
      refetchList,
      isFullWidth,
      cursorObject,
      handleFilter,
      handleEditFLow,
      handleSetCursor,
      openedEditModal,
      handleCreateFlow,
      handleDeleteFlow,
      openedCreateModal,
      openedWaitingModal,
      handleDuplicateFlow,
      openedTemplateModal,
      handleOnChangeSearch,
      handleImportFromFile,
      isCreatingFromTemplate,
      handleCreateFromScratch,
      handleCreateFromTemplate,
      handleCloseCreateFlowModal,
      handleSelectTemplateToCreate,
    ]
  );
};

type FlowListContextType = ReturnType<typeof useFlowList>;

const FlowListContext = createContext<FlowListContextType | null>(null);
export const FlowListContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useFlowList();
  if (!value) {
    throw new Error('useFlowList is not defined');
  }
  return <FlowListContext.Provider value={value}>{children}</FlowListContext.Provider>;
};

export const useFlowListContext = () => {
  const context = useContext(FlowListContext);
  if (!context) {
    throw new Error('useFlowListContext must be used within a FlowListContextProvider');
  }
  return context;
};
