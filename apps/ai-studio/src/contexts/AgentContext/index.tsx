import { createContextHook } from '@resola-ai/utils';
import { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import { useAgentDetails } from '@/hooks/useAgentDetails';
import { useHandleApiError } from '@/hooks/useHandleApiError';
import { type UseListAgentProps, useListAgent } from '@/hooks/useListAgent';
import { useListAgentModel } from '@/hooks/useListAgentModel';
import {
  type IAgent,
  type IAgentInput,
  type IAgentSettings,
  type IMessage,
  type IStreamAgentOutput,
  type IStreamAgentToolCall,
  type IStreamAgentToolCallEnd,
  MessageType,
  StreamAgentOutputType,
} from '@/models';
import { AgentAPI } from '@/services/api/agent';
import { TemplateAPI } from '@/services/api/template';
import type { ResourceType } from '@/types';
import { jsonExport } from '@/utils/jsonExport';
import { showErrorNotification, showSuccessNotification } from '@/utils/notification';
import { useTranslate } from '@tolgee/react';

const useAgent = () => {
  const navigate = useNavigate();
  const { agentId, workspaceId } = useParams();
  const { handleApiError } = useHandleApiError();
  const [apiParams, setApiParams] = useState<UseListAgentProps>({});
  const [settings, setSettings] = useState<IAgentSettings | null>(null);
  const [messages, setMessages] = useState<IMessage[]>([]);
  const [messagesLoading, setMessagesLoading] = useState(false);
  const [toolCallEnds, setToolCallEnds] = useState<IStreamAgentToolCallEnd[]>([]);
  const [sessionId, setSessionId] = useState<string>();
  const {
    data: agents,
    isLoading: isLoadingAgents,
    mutate: mutateAgents,
  } = useListAgent(apiParams);
  const { t } = useTranslate(['agent', 'common']);

  const { data: agentDetails } = useAgentDetails({
    agentId,
    workspaceId,
  });

  const { data: models } = useListAgentModel({
    workspaceId,
  });

  useEffect(() => {
    if (agentDetails?.settings) {
      setSettings(agentDetails.settings);
    }
  }, [agentDetails?.settings]);

  useEffect(() => {
    if (agentId) {
      setMessages([]);
      setSessionId(undefined);
    }
  }, [agentId]);

  const onDeleteAgent = useCallback(
    async (agentId?: string) => {
      if (!apiParams.workspaceId || !agentId) {
        return;
      }

      try {
        await AgentAPI.delete(apiParams.workspaceId, agentId);
      } catch (error) {
        handleApiError(error);
      } finally {
        mutateAgents();
      }
    },
    [mutateAgents, apiParams.workspaceId]
  );

  const onCreateAgent = useCallback(
    async (agent: Partial<IAgent>, isDuplicate = false) => {
      if (!apiParams.workspaceId) {
        return;
      }

      try {
        const newAgent = await AgentAPI.create(apiParams.workspaceId, agent);
        isDuplicate && showSuccessNotification(t('duplicated'));

        navigate(`/studio/${apiParams.workspaceId}/agents/${newAgent.id}`);
      } catch (error) {
        isDuplicate && showErrorNotification(t('duplicateFailed'));
        handleApiError(error);
      }
    },
    [apiParams.workspaceId, navigate]
  );

  const onUpdateAgent = useCallback(
    async (agent: Partial<IAgent>) => {
      if (!apiParams.workspaceId || !agent.id) {
        return;
      }

      try {
        await AgentAPI.update(apiParams.workspaceId, agent.id, agent);
      } catch (error) {
        handleApiError(error);
      } finally {
        mutateAgents();
      }
    },
    [mutateAgents, apiParams.workspaceId]
  );

  const onDuplicateAgent = useCallback(
    async (agent: IAgent) => {
      await onCreateAgent(
        {
          ...agent,
          name: `${agent.name} (Copy)`,
        },
        true
      );
    },
    [onCreateAgent]
  );

  const onSendMessage = useCallback(
    async (payload: IAgentInput) => {
      if (!apiParams.workspaceId || !agentId) {
        return;
      }

      setMessages((prev) => [
        ...prev,
        {
          id: crypto.randomUUID(),
          content: payload.message,
          isUser: true,
          createdAt: new Date(),
          type: MessageType.TEXT,
        },
      ]);

      setMessagesLoading(true);

      try {
        const response = await AgentAPI.execute(
          apiParams.workspaceId,
          agentId,
          {
            ...payload,
            stream: false,
            parameters: {},
          },
          sessionId
        );

        const msg = response.data;
        setMessages((prev) => [
          ...prev,
          {
            id: msg.agentId,
            content: msg.response,
            isUser: false,
            createdAt: new Date(),
            type: MessageType.TEXT,
          },
        ]);

        if (response.sessionId && response.sessionId !== sessionId) {
          setSessionId(response.sessionId);
        }
      } catch (error) {
        handleApiError(error);
      } finally {
        setMessagesLoading(false);
      }
    },
    [apiParams.workspaceId, agentId, models, settings?.model, sessionId]
  );

  const onSendMessageStream = useCallback(
    async (payload: IAgentInput) => {
      if (!apiParams.workspaceId || !agentId) {
        return;
      }

      setMessages((prev) => [
        ...prev,
        {
          id: crypto.randomUUID(),
          content: payload.message,
          isUser: true,
          createdAt: new Date(),
          type: MessageType.TEXT,
        },
      ]);

      setMessagesLoading(true);

      try {
        const assistantMessageId = crypto.randomUUID();
        let isFirstChunk = true;

        const handleStream = (chunk: string) => {
          try {
            const parsedChunk = JSON.parse(chunk) as IStreamAgentOutput;

            if (parsedChunk.type === StreamAgentOutputType.TEXT_MESSAGE_CONTENT) {
              setMessages((prev) => {
                const newMessages = [...prev];

                if (isFirstChunk) {
                  // Add the initial assistant message
                  newMessages.push({
                    id: assistantMessageId,
                    content: parsedChunk.delta ?? '',
                    isUser: false,
                    createdAt: new Date(),
                    type: MessageType.TEXT,
                  });
                  isFirstChunk = false;
                } else {
                  // Update the existing assistant message
                  const lastMessageIndex = newMessages.findIndex(
                    (msg) => msg.id === assistantMessageId
                  );
                  if (lastMessageIndex !== -1) {
                    newMessages[lastMessageIndex] = {
                      ...newMessages[lastMessageIndex],
                      content: newMessages[lastMessageIndex].content + parsedChunk.delta,
                    };
                  }
                }

                return newMessages;
              });
            }

            if (parsedChunk.type === StreamAgentOutputType.TOOL_CALL_START) {
              const toolCall = parsedChunk as IStreamAgentToolCall;

              // if (toolCall.toolCallId !== "6de49a5c-aa15-49be-90ea-c5d82e86b473") return;

              setMessages((prev) => [
                ...prev,
                {
                  id: crypto.randomUUID(),
                  content: toolCall.toolCallName,
                  isUser: false,
                  createdAt: new Date(),
                  type:
                    toolCall.toolCallId === toolCall.functionCallId
                      ? MessageType.THINKING
                      : MessageType.TOOL_CALL,
                  metadata: {
                    ...toolCall,
                  },
                },
              ]);
            }

            if (parsedChunk.type === StreamAgentOutputType.TOOL_CALL_END) {
              setToolCallEnds((prev) => [
                ...prev,
                {
                  ...(parsedChunk as IStreamAgentToolCallEnd),
                },
              ]);
            }
          } catch (error) {
            console.error(error);
          }
        };

        const response = await AgentAPI.executeStream(
          apiParams.workspaceId,
          agentId,
          {
            ...payload,
            stream: true,
            parameters: {},
          },
          handleStream,
          sessionId
        );

        if (response.sessionId && response.sessionId !== sessionId) {
          setSessionId(response.sessionId);
        }
      } catch (error) {
        handleApiError(error);
      } finally {
        setMessagesLoading(false);
      }
    },
    [apiParams.workspaceId, agentId, sessionId]
  );

  const onGeneratePrompt = useCallback(
    async (input: string) => {
      const id = apiParams.workspaceId || workspaceId;
      if (!id) return;
      try {
        const response = await AgentAPI.generatePrompt(id, input);
        return response?.instructionMarkdown ?? '';
      } catch (error) {
        handleApiError(error);
      }
    },
    [apiParams.workspaceId, workspaceId]
  );

  const handleSaveAsTemplate = useCallback(
    async (agent: IAgent) => {
      const template = {
        name: agent?.name ?? '',
        description: agent?.description ?? '',
        type: 'agent',
        resource: 'agent' as ResourceType,
        settings: agent?.settings,
      };
      try {
        await TemplateAPI.create(workspaceId ?? '', template);
        showSuccessNotification(t('template.saved', { ns: 'common' }));
      } catch (error) {
        showErrorNotification(t('template.saveFailed', { ns: 'common' }));
        handleApiError(error);
      }
    },
    [workspaceId]
  );

  const onExportAgent = useCallback(
    async (agentId: string) => {
      if (!apiParams.workspaceId || !agentId) {
        return;
      }

      try {
        const response = await AgentAPI.export(apiParams.workspaceId, agentId);
        jsonExport(response, `agent-${agentId}.json`);
      } catch (error) {
        handleApiError(error);
      }
    },
    [apiParams.workspaceId]
  );

  const onImportAgent = useCallback(
    async (file: File) => {
      if (!apiParams.workspaceId) {
        return;
      }

      try {
        await AgentAPI.import(apiParams.workspaceId, file);
        await mutateAgents();
        showSuccessNotification(t('notifications.importedSuccessfully', { ns: 'common' }));
      } catch (error) {
        handleApiError(error);
      }
    },
    [apiParams.workspaceId, mutateAgents, t]
  );

  return {
    agents,
    agentDetails,
    agentModels: models?.data ?? [],
    isLoadingAgents,
    setApiParams,
    onCreateAgent,
    onDeleteAgent,
    onDuplicateAgent,
    onExportAgent,
    onGeneratePrompt,
    onImportAgent,
    onSendMessage,
    onSendMessageStream,
    onUpdateAgent,
    settings,
    setSettings,
    messages,
    setMessages,
    messagesLoading,
    toolCallEnds,
    handleSaveAsTemplate,
  };
};

export type AgentContextType = ReturnType<typeof useAgent>;

export const [AgentContextProvider, useAgentContext] = createContextHook<AgentContextType>(
  useAgent,
  'AgentContext'
);
