import { DEFAULT_PER_PAGE, PER_PAGE_OPTIONS } from '@/constants/common';
import { useEffect } from 'react';

export const useRestrictLimitParam = (
  limit: number | undefined,
  setLimit: any,
  setSearchParams: any,
  callBack?: any
) => {
  useEffect(() => {
    const limitOptions = PER_PAGE_OPTIONS.map((option) => option.value);
    if (!limitOptions.includes(limit || 0)) {
      setLimit(DEFAULT_PER_PAGE);
      setSearchParams((prev) => {
        prev.set('cursor', '');
        prev.set('limit', DEFAULT_PER_PAGE.toString());
        return prev;
      });
      callBack?.();
    }
  }, [limit, setLimit, setSearchParams, callBack]);
};
