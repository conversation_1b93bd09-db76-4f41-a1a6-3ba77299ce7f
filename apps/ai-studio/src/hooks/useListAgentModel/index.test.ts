import { renderHook, waitFor } from '@testing-library/react';
import useSWR from 'swr';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import type { IAgentModel } from '@/models/agent';
import type { ProviderType } from '@/models/credential';
import { AgentAPI } from '@/services/api/agent';
import { useListAgentModel } from './index';

// Mock useSWR
vi.mock('swr', () => ({
  default: vi.fn(),
}));

// Mock the AgentAPI
vi.mock('@/services/api/agent', () => ({
  AgentAPI: {
    getModels: vi.fn(),
  },
}));

const mockModels: IAgentModel[] = [
  {
    _id: '1',
    modelId: 'gpt-4o',
    name: 'GPT-4o',
    provider: 'openai' as ProviderType,
  },
  {
    _id: '2',
    modelId: 'claude-3.5-sonnet',
    name: 'Claude 3.5 Sonnet',
    provider: 'anthropic' as ProviderType,
  },
];

const mockSWRResponse = {
  data: mockModels,
  error: undefined,
  isLoading: false,
  mutate: vi.fn(),
  isValidating: false,
};

describe('useListAgentModel', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should not fetch models when workspaceId is not provided', async () => {
    vi.mocked(useSWR).mockReturnValueOnce({
      ...mockSWRResponse,
      data: undefined,
    });

    const { result } = renderHook(() => useListAgentModel({ workspaceId: undefined }));

    expect(result.current.data).toBeUndefined();
  });

  it('should fetch models when workspaceId is provided', async () => {
    const workspaceId = 'workspace-123';
    vi.mocked(AgentAPI.getModels).mockResolvedValueOnce({
      data: mockModels,
      totalCount: mockModels.length,
    });
    vi.mocked(useSWR).mockReturnValueOnce({
      ...mockSWRResponse,
      data: mockModels,
    });

    const { result } = renderHook(() => useListAgentModel({ workspaceId }));

    await waitFor(() => {
      expect(result.current.data).toEqual(mockModels);
    });
  });

  it('should handle API error correctly', async () => {
    const workspaceId = 'workspace-123';
    const error = new Error('API error');
    vi.mocked(useSWR).mockReturnValueOnce({
      ...mockSWRResponse,
      error,
    });

    const { result } = renderHook(() => useListAgentModel({ workspaceId }));

    await waitFor(() => {
      expect(result.current.error).toBeTruthy();
    });
  });

  it('should return an empty array when workspaceId is provided but API returns nothing', async () => {
    const workspaceId = 'workspace-123';
    vi.mocked(useSWR).mockReturnValueOnce({
      ...mockSWRResponse,
      data: [],
    });

    const { result } = renderHook(() => useListAgentModel({ workspaceId }));

    await waitFor(() => {
      expect(result.current.data).toEqual([]);
    });
  });

  it('should not revalidate on focus', async () => {
    const workspaceId = 'workspace-123';

    // Verify that the hook is configured with revalidateOnFocus: false
    renderHook(() => useListAgentModel({ workspaceId }));

    expect(vi.mocked(useSWR).mock.calls[0][2]).toEqual(
      expect.objectContaining({
        revalidateOnFocus: false,
      })
    );
  });
});
