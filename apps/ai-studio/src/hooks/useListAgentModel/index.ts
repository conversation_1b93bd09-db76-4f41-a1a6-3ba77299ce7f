import useSWR from 'swr';

import { AgentAPI } from '@/services/api/agent';

export interface UseListAgentModelProps {
  workspaceId?: string;
}

export const useListAgentModel = ({ workspaceId }: UseListAgentModelProps) => {
  return useSWR(
    workspaceId ? [`${workspaceId}/models`] : null,
    () => (workspaceId ? AgentAPI.getModels(workspaceId) : { data: [], totalCount: 0 }),
    {
      revalidateOnFocus: false,
    }
  );
};
