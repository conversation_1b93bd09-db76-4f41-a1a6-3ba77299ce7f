import type {
  CatalogModalRef,
  NodeNameTypes,
} from '@/components/FlowBuilder/ModalPlacements/CatalogModal';
import { useAppContext } from '@/contexts/AppContext';
import { mockVersions } from '@/mockdata/version';
import type { ITemplateCreatePayload, IVersion } from '@/models';
import {
  type ConnectionData,
  type Flow,
  type FlowNodeData,
  FlowNodeType,
  type FlowRouteByIntentSetting,
  FlowTypeNode,
  type PathValue,
} from '@/models/flow';
import { FlowApi } from '@/services/api/flow';
import { TemplateAPI } from '@/services/api/template';
import { showErrorNotification, showSuccessNotification } from '@/utils/notification';
import { useDisclosure } from '@mantine/hooks';
import { useNodeSchemas } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import get from 'lodash/get';
import set from 'lodash/set';
import { nanoid } from 'nanoid';
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useBack } from '../useBack';
import { useHandleApiError } from '../useHandleApiError';
import useFlowActions, { type MoveNodePayload } from './useFlowActions';
import useFlowNodeAndEdgeGenerator from './useFlowNodeAndEdgeGenerator';

const DEFAULT_DISABLED_NODES_CATALOG: NodeNameTypes[] = [
  'new-trigger',
  'manual',
  'loop',
  'schedule',
  'webhook',
];

type CurrentSelectNodeData = {
  nodeId?: string;
  orderNumber?: number;
  type?: FlowNodeType;
  parentNodeId?: string; // actual parent node id;
};

export const useFlowBuilder = () => {
  const navigate = useNavigate();
  const { workspaceId, flowId } = useParams();
  const { t } = useTranslate();
  const { handleApiError } = useHandleApiError();
  const { openConfirmModal, closeConfirmModal } = useAppContext();
  const [loading, setLoading] = useState(true);
  const [flow, setFlow] = useState<Flow | undefined>(undefined);
  const [flowOriginal, setFlowOriginal] = useState<Flow | undefined>(undefined);
  const [openedCatalog, { open: openCatalog, close: closeCatalog }] = useDisclosure(false);
  const [openedRightPanel, { open: openRightPanel, close: closeRightPanel }] = useDisclosure(false);
  const [intendChangeNodeId, setIntendChangeNodeId] = useState<string | undefined>(undefined);
  const [currentSelectNodeId, setCurrentSelectNodeId] = useState<string | undefined>(undefined);
  const [currentSelectNodeData, setCurrentSelectNodeData] = useState<
    CurrentSelectNodeData | undefined
  >(undefined);
  const { handleBack } = useBack('flows', workspaceId);
  const { getTriggerNodes } = useNodeSchemas();

  const versionUtils = useVersioning();
  const { nodes, edges, setNodes, nodeIdToOrderNumber } = useFlowNodeAndEdgeGenerator(
    flow?.triggers,
    flow?.nodes
  );
  const flowActionHandlers = useFLowActionHandler(
    setFlow,
    flow,
    closeRightPanel,
    setCurrentSelectNodeId,
    setCurrentSelectNodeData
  );
  const catalogModalRef = useRef<CatalogModalRef>(null);
  const isDebugging = false;

  const handleUpdateVirtualNodeByPath = useCallback(
    (virtualNodeId: string, path: string, value: any) => {
      setFlow((prev) => {
        if (!prev) return prev;
        const virtualNode = nodes.find((node) => node.id === virtualNodeId);
        if (!virtualNode) return prev;
        const parentNodeId = get(virtualNode, 'data.actualParentNodeId', '') as string;
        const parentNode = get(prev?.nodes, parentNodeId);
        if (!parentNode) return prev;

        const paths = get(parentNode, 'settings.paths', []) as any[];
        const pathIndex = paths.findIndex((path) => path.next === virtualNode.data.nextNodeId);

        const updatedNode = { ...parentNode };
        const updatedPath = `settings.paths.${pathIndex}.${path}`;
        set(updatedNode, updatedPath, value);

        return {
          ...prev,
          nodes: { ...prev.nodes, [parentNodeId]: updatedNode },
        };
      });
    },
    [nodes]
  );

  const handleAddNewNodeWhenClickAddStep = useCallback(
    (connectionData: ConnectionData) => {
      closeRightPanel();
      setCurrentSelectNodeId(undefined);
      const newNodeId = flowActionHandlers.handleAddNewNode(connectionData);
      let disabledOptions: NodeNameTypes[] = DEFAULT_DISABLED_NODES_CATALOG;

      if (connectionData.directAddNodeFromTrigger) {
        disabledOptions = disabledOptions.filter(
          (item) => !['new-trigger', 'manual', 'schedule', 'webhook'].includes(item)
        );
      }
      const { isConnectFromLoopSubNodes } =
        flowActionHandlers.flowActions.checkAncestorIsLoopingNode(flow!, connectionData.parentId);

      if (!connectionData.directSubNodeLooping && !isConnectFromLoopSubNodes) {
        disabledOptions = disabledOptions.filter((item) => item !== 'loop');
      }

      if (newNodeId) {
        openCatalog();
        setIntendChangeNodeId(newNodeId);
        catalogModalRef.current?.openWithDisabledOptions(disabledOptions);
      }
    },
    [openCatalog, flowActionHandlers.handleAddNewNode, closeRightPanel, flow]
  );

  const handleOpenCatalogForReplaceEmptyNode = useCallback(
    ({ nodeId, triggerContext = false }: { nodeId: string; triggerContext?: boolean }) => {
      closeRightPanel();
      setCurrentSelectNodeId(undefined);
      setIntendChangeNodeId(nodeId);
      openCatalog();

      if (triggerContext || flow?.triggers?.[nodeId]) {
        catalogModalRef.current?.openWithEnabledOptions(
          getTriggerNodes().filter((item) => item !== 'new-trigger') as NodeNameTypes[],
          triggerContext
        );
        return;
      }

      const { isConnectFromLoopSubNodes } =
        flowActionHandlers.flowActions.checkAncestorIsLoopingNode(flow!, nodeId);

      let disabledOptions: NodeNameTypes[] = DEFAULT_DISABLED_NODES_CATALOG;
      const directNodeFromTriggerNode = !!flow?.triggers?.[flow?.nodes?.[nodeId]?.prev ?? ''];
      if (directNodeFromTriggerNode) {
        disabledOptions = disabledOptions.filter(
          (item) => !['new-trigger', 'manual', 'schedule', 'webhook'].includes(item)
        );
      }

      if (!isConnectFromLoopSubNodes) {
        disabledOptions = disabledOptions.filter((item) => item !== 'loop');
      }
      catalogModalRef.current?.openWithDisabledOptions(disabledOptions);
    },
    [openCatalog, closeRightPanel, flow, getTriggerNodes]
  );

  const handleTitleChange = useCallback(
    (newTitle: string) => {
      if (flow && workspaceId) {
        const updatedFlow = { ...flow, name: newTitle };
        setFlow(updatedFlow);
        setFlowOriginal(updatedFlow);
        FlowApi.update(workspaceId, updatedFlow);
      }
    },
    [flow, workspaceId]
  );

  const handleUpdateDisplayName = useCallback(
    (
      nodeId: string,
      displayName: string,
      nodeData: { isSubPathNode?: boolean; mainPathNodeId?: string }
    ) => {
      if (!workspaceId) return;
      setFlow((prev) => {
        if (!prev) return prev;
        if (
          !prev.nodes?.[nodeId] &&
          !prev.triggers?.[nodeId] &&
          nodeData?.isSubPathNode &&
          !prev.nodes?.[nodeData?.mainPathNodeId ?? '']
        )
          return prev;

        const updatedFlow = { ...prev };
        if (prev.nodes?.[nodeId]) {
          updatedFlow.nodes = {
            ...prev.nodes,
            [nodeId]: { ...prev.nodes[nodeId], displayName },
          };
        }
        if (prev.triggers?.[nodeId]) {
          updatedFlow.triggers = {
            ...prev.triggers,
            [nodeId]: { ...prev.triggers[nodeId], displayName },
          };
        }

        if (
          nodeData?.isSubPathNode &&
          nodeData?.mainPathNodeId &&
          prev.nodes?.[nodeData?.mainPathNodeId]
        ) {
          const paths = (prev.nodes[nodeData?.mainPathNodeId].settings as FlowRouteByIntentSetting)
            .paths;
          const updatedPaths = paths.map((path) => {
            if (path.id === nodeId) {
              return { ...path, displayName: displayName };
            }
            return path;
          });
          updatedFlow.nodes = {
            ...prev.nodes,
            [nodeData?.mainPathNodeId]: {
              ...prev.nodes[nodeData?.mainPathNodeId],
              settings: { ...prev.nodes[nodeData?.mainPathNodeId].settings, paths: updatedPaths },
            },
          };
        }

        FlowApi.update(workspaceId, updatedFlow as Flow);
        setFlowOriginal(updatedFlow);

        return updatedFlow;
      });
    },
    [workspaceId]
  );

  const handleSave = useCallback(
    async (isPublish = false) => {
      if (flow) {
        await FlowApi.update(workspaceId ?? '', { ...flow, isPublish: isPublish });
        setFlowOriginal(flow);
      }
    },
    [flow, workspaceId]
  );

  const handlePublish = useCallback(async () => {
    handleSave(true);
  }, [handleSave]);

  const handleOpenVersions = useCallback(() => {
    // Implement versions functionality
    console.log('Opening versions');
  }, []);

  const handleOpenSeeMore = useCallback(() => {
    // Implement see more functionality
    console.log('Opening see more');
  }, []);

  const handleRun = useCallback(async () => {
    if (!flow || !workspaceId) return;

    const hasManualTrigger = Object.values(flow.triggers).some(
      (trigger) => trigger.name === 'manual'
    );
    if (!hasManualTrigger) {
      showErrorNotification(t('noManualTrigger', { ns: 'flow' }));
      return;
    }
    setFlowOriginal(flow);
    await FlowApi.update(workspaceId ?? '', flow);
    await FlowApi.run(workspaceId, flow);
    showSuccessNotification(t('runFlow.flowRunning', { ns: 'flow' }));
  }, [flow, workspaceId, t]);

  const handleOpenHistoryRuns = useCallback(() => {
    // Implement history runs functionality
    console.log('Opening history runs');
  }, []);

  const handleOpenVariables = useCallback(() => {
    // Implement variables functionality
    console.log('Opening variables');
  }, []);

  const handleOpenTemplate = useCallback(() => {
    // Implement template functionality
    console.log('Opening template');
  }, []);

  const handleCloseRightPanel = useCallback(() => {
    setCurrentSelectNodeId(undefined);
    closeRightPanel();
  }, [closeRightPanel]);

  const handleOpenCatalog = useCallback(() => {
    handleCloseRightPanel();
    openCatalog();
    catalogModalRef.current?.openWithDisabledOptions();
  }, [openCatalog, handleCloseRightPanel]);

  const handleOpenFormPanel = useCallback(
    (data: CurrentSelectNodeData) => {
      setCurrentSelectNodeId(data.nodeId);
      setCurrentSelectNodeData(data);
      closeCatalog();
      openRightPanel();
    },
    [openRightPanel, closeCatalog]
  );

  const handleOnSelectCatalog = useCallback(
    (item: {
      id: string;
      displayName: string;
      icon: string;
      name: FlowNodeType;
      isTriggerContext: boolean;
    }) => {
      closeCatalog();
      if (!intendChangeNodeId) {
        return;
      }
      const intendChangeNode = flowActionHandlers.flowActions.getNodeInfo(
        flow!,
        intendChangeNodeId
      ).node;
      if (!intendChangeNode) return;

      const openNodeData = {
        type: item?.name,
        nodeId: intendChangeNodeId,
        parentNodeId: intendChangeNode?.prev ?? '',
        orderNumber: nodeIdToOrderNumber[intendChangeNodeId] ?? 0,
      };

      const { node: parentNode, isLoopingNode } = flowActionHandlers.flowActions.getNodeInfo(
        flow!,
        intendChangeNode?.prev ?? ''
      );

      const connectionData: ConnectionData = {
        parentId: intendChangeNode?.prev ?? '',
        nextNodeId: intendChangeNode.next,
        directSubNodeLooping:
          isLoopingNode && intendChangeNode.id === parentNode?.settings?.nextChildID,
        directNextNodeLooping: isLoopingNode && intendChangeNode.id === parentNode?.next,
        displayName: item?.displayName,
        icon: item?.icon,
        name: item?.name,
        nodeId: intendChangeNodeId,
      };
      let newFlow: Flow | undefined;
      const NodeTypeWithNeedActions = ['path', 'trigger', 'loop', 'schedule', 'webhook', 'manual'];

      if (
        !NodeTypeWithNeedActions.some((type) => item?.id.includes(type)) ||
        item.isTriggerContext
      ) {
        // just replace the type of intendChangeNode to type of item
        newFlow = flowActionHandlers.flowActions.replaceNodeType(flow!, intendChangeNodeId, {
          name: item?.name,
          icon: item?.icon,
          displayName: item?.displayName,
        });
        newFlow && setFlow(newFlow);
        setIntendChangeNodeId(undefined);

        !openedRightPanel && handleOpenFormPanel(openNodeData);
        return;
      }

      newFlow = flowActionHandlers.flowActions.removeNode(
        flow!,
        connectionData.parentId,
        intendChangeNodeId!
      );

      if (
        item?.id.includes('trigger') ||
        item?.id.includes('schedule') ||
        item?.id.includes('webhook') ||
        item?.id.includes('manual')
      ) {
        newFlow = flowActionHandlers.flowActions.addNewTrigger(newFlow!, {
          name: item?.name,
          icon: item?.icon,
          displayName: item?.displayName,
          id: intendChangeNodeId,
        });
        openNodeData.parentNodeId = '';
      }

      if (item?.id.includes('path')) {
        newFlow = flowActionHandlers.flowActions.addNewPath(newFlow!, connectionData);

        // Find the path node by checking name and prev property
        const pathNode = Object.values(newFlow?.nodes || {}).find(
          (node) => node.name === 'path' && node.prev === connectionData.parentId
        );
        // open the first sub path node
        openNodeData.nodeId = pathNode?.settings.paths[0].id;
        openNodeData.type = FlowNodeType.SubPathNode;
        openNodeData.parentNodeId = pathNode?.id ?? '';
        openNodeData.orderNumber = +(nodeIdToOrderNumber[intendChangeNodeId] ?? 0) + 1;
      }

      if (item?.id.includes('loop')) {
        newFlow = flowActionHandlers.flowActions.addNewLooping(newFlow!, connectionData);
      }

      setIntendChangeNodeId(undefined);
      newFlow && setFlow(newFlow);

      if (item?.id.includes('trigger')) {
        setTimeout(() => {
          handleOpenCatalogForReplaceEmptyNode({
            nodeId: intendChangeNodeId,
            triggerContext: true,
          });
        }, 300);
      } else {
        !openedRightPanel && setTimeout(() => handleOpenFormPanel(openNodeData));
      }
    },
    [
      closeCatalog,
      flow,
      flowActionHandlers.flowActions,
      intendChangeNodeId,
      handleOpenCatalogForReplaceEmptyNode,
      openedRightPanel,
      handleOpenFormPanel,
    ]
  );

  const handleSaveAsTemplate = useCallback(async () => {
    if (flow) {
      try {
        await versionUtils.handleSaveAsTemplate(workspaceId, flow);
        showSuccessNotification(t('template.saved', { ns: 'common' }));
      } catch (error) {
        showErrorNotification(t('template.saveFailed', { ns: 'common' }));
      }
    }
  }, [flow, workspaceId, t, versionUtils]);

  const handleDuplicateFlow = useCallback(
    async (flow: Flow, isNavigate = false) => {
      try {
        const { id, updatedAt, createdAt, status, ...originalData } = flow ?? {};
        const newFlowData = {
          ...originalData,
          name: `${flow.name} (Copy)`,
          status: 'enabled' as Flow['status'],
        };
        const newFlow = await FlowApi.create(workspaceId ?? '', newFlowData);
        showSuccessNotification(t('flow.duplicated', { ns: 'common' }));
        if (isNavigate) {
          navigate(`/studio/${workspaceId}/flows/${newFlow.id}`);
        }
      } catch (error) {
        showErrorNotification(t('flow.duplicateFailed', { ns: 'common' }));
      }
    },
    [workspaceId, navigate]
  );

  const handleDeleteFlow = async (flow: Flow, callback?: () => void) => {
    openConfirmModal({
      name: flow.name,
      title: t('deleteModalTitle', { ns: 'flow' }),
      onConfirm: async () => {
        if (!workspaceId || !flow.id) return;
        try {
          await FlowApi.delete(workspaceId, flow.id);
        } catch (error) {
          handleApiError(error);
        }
        closeConfirmModal();
        callback?.();
      },
      onCancel: () => {
        closeConfirmModal();
      },
    });
  };

  useEffect(() => {
    if (flow && window) {
      (window as any).globalFlow = flow;
    }
  }, [flow]);

  // Fetch flow details
  useEffect(() => {
    const fetchFlowDetails = async () => {
      if (workspaceId && flowId) {
        try {
          setLoading(true);
          const flowData = await FlowApi.getById(workspaceId, flowId);
          // We could handle empty flowData later
          // back to list or show a notification.
          setFlow(flowData);
          setFlowOriginal(flowData);
        } catch (error) {
          console.error('Error fetching flow details:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchFlowDetails();
  }, [workspaceId, flowId]);

  return {
    flow,
    flowOriginal,
    nodes,
    edges,
    loading,
    isDebugging,
    openedCatalog,
    ...versionUtils,
    catalogModalRef,
    openedRightPanel,
    flowActionHandlers,
    intendChangeNodeId,
    nodeIdToOrderNumber,
    currentSelectNodeId,
    currentSelectNodeData,
    setNodes,
    handleRun,
    handleBack,
    closeCatalog,
    handlePublish,
    handleDeleteFlow,
    handleOpenCatalog,
    handleTitleChange,
    handleOpenSeeMore,
    handleOpenTemplate,
    handleOpenVersions,
    handleDuplicateFlow,
    handleOpenVariables,
    handleOpenFormPanel,
    handleSaveAsTemplate,
    handleCloseRightPanel,
    handleOpenHistoryRuns,
    handleOnSelectCatalog,
    handleUpdateDisplayName,
    handleUpdateVirtualNodeByPath,
    handleAddNewNodeWhenClickAddStep,
    handleOpenCatalogForReplaceEmptyNode,
    handleSave,
  };
};

export const useVersioning = () => {
  const [versions, setVersions] = useState<IVersion[]>(mockVersions);

  const handleAddVersion = (version: Omit<IVersion, 'id'>) => {
    setVersions([{ ...version, id: nanoid() }, ...versions]);
  };

  const handleEditVersion = (version: IVersion) => {
    setVersions(versions.map((v) => (v.id === version.id ? version : v)));
  };

  const handleDeleteVersion = (version: IVersion) => {
    setVersions(versions.filter((v) => v.id !== version.id));
  };

  const handleSaveAsTemplate = async (workspaceId?: string, flow?: Flow) => {
    if (!(workspaceId && flow)) return;
    // create template object from flow object
    const template: ITemplateCreatePayload = {
      name: flow.name,
      description: flow.description,
      type: 'flow',
      resource: 'flow',
      settings: flow,
    };
    await TemplateAPI.create(workspaceId ?? '', template);
  };

  return {
    versions,
    handleAddVersion,
    handleEditVersion,
    handleDeleteVersion,
    handleSaveAsTemplate,
  };
};

export const useFLowActionHandler = (
  setFlow: Dispatch<SetStateAction<Flow | undefined>>,
  flow?: Flow,
  closeRightPanel?: () => void,
  setCurrentSelectNodeId?: Dispatch<SetStateAction<string | undefined>>,
  setCurrentSelectNodeData?: Dispatch<SetStateAction<CurrentSelectNodeData | undefined>>
) => {
  const flowActions = useFlowActions();
  const { getTriggerNodes, getActionNodes } = useNodeSchemas();
  const handleAddNewNode = useCallback(
    (connectionData: ConnectionData) => {
      if (!flow) return undefined;
      const actionRes = flowActions.addNewNode(flow, connectionData);
      if (actionRes) {
        setFlow(actionRes.newFlow);
        return actionRes.newNodeId; // return new node id for later use
      }
      return undefined; // this for clarification.
    },
    [flow, flowActions, setFlow]
  );

  const handleAddNewPath = useCallback(
    (connectionData: ConnectionData) => {
      if (!flow) return;
      const newFlow = flowActions.addNewPath(flow, connectionData);
      if (newFlow) setFlow(newFlow);
    },
    [flow, flowActions, setFlow]
  );

  const handleAddNewSubPath = useCallback(
    (connectionData: ConnectionData) => {
      if (!flow) return;
      const newFlow = flowActions.addNewSubPath(flow, connectionData);
      if (newFlow) setFlow(newFlow);
    },
    [flow, flowActions, setFlow]
  );

  const handleRemoveNode = useCallback(
    ({
      typeRemove,
      nodeId,
      parentId = '',
    }: {
      typeRemove: FlowTypeNode;
      nodeId: string;
      parentId?: string;
    }) => {
      if (!flow) return;
      let newFlow: Flow | undefined;
      switch (typeRemove) {
        case FlowTypeNode.TriggerNode:
          newFlow = flowActions.removeTriggerNode(flow, nodeId);
          break;
        case FlowTypeNode.Node:
          newFlow = flowActions.removeNode(flow, parentId, nodeId);
          break;
        case FlowTypeNode.Path:
          newFlow = flowActions.removePathNode(flow, parentId, nodeId);
          break;
        case FlowTypeNode.SubPath:
          newFlow = flowActions.removeSubPathNode(flow, parentId, nodeId);
          break;
        case FlowTypeNode.Looping:
          newFlow = flowActions.removeLoopingNode(flow, parentId, nodeId);
          break;
        default:
          break;
      }
      if (newFlow) setFlow(newFlow);
      closeRightPanel?.();
    },
    [flow, flowActions, setFlow, closeRightPanel]
  );

  const handleDuplicateNode = useCallback(
    ({ nodeId }: { nodeId: string }) => {
      if (!flow) return;
      const newFlow = flowActions.duplicateNode(flow, nodeId);
      if (newFlow) setFlow(newFlow);
    },
    [flow, setFlow, flowActions]
  );

  const handleUpdateTrigger = useCallback(
    (triggerId: string, trigger: Partial<FlowNodeData>) => {
      setFlow((prev) => {
        if (!prev) return prev;
        return {
          ...prev,
          triggers: {
            ...prev.triggers,
            [triggerId]: { ...prev.triggers[triggerId], ...trigger },
          },
        };
      });
    },
    [setFlow]
  );

  const handleUpdateTriggerByPaths = useCallback(
    (triggerId: string, paths: { path: string; value: any }[]) => {
      setFlow((prev) => {
        if (!prev) return prev;
        if (!prev.triggers?.[triggerId]) return prev;
        const updatedTrigger = { ...prev.triggers[triggerId] };
        paths.forEach(({ path, value }) => {
          set(updatedTrigger, path, value);
        });
        return {
          ...prev,
          triggers: {
            ...prev.triggers,
            [triggerId]: updatedTrigger,
          },
        };
      });
    },
    [setFlow]
  );

  const handleUpdateNodeByPaths = useCallback(
    (nodeId: string, paths: PathValue[]) => {
      setFlow((prev) => {
        if (!prev) return prev;

        if (!prev.nodes?.[nodeId]) return prev;
        const updatedNode = { ...prev.nodes[nodeId] };
        paths.forEach(({ path, value }) => {
          set(updatedNode, path, value);
        });
        return {
          ...prev,
          nodes: { ...prev.nodes, [nodeId]: updatedNode },
        };
      });
    },
    [setFlow]
  );

  const handleUpdateNode = useCallback(
    async (nodeId: string, node: Partial<FlowNodeData> & { name: FlowNodeType }) => {
      setFlow((prev) => {
        if (!prev) return prev;
        if (!prev.nodes?.[nodeId] && !prev.triggers?.[nodeId]) return prev;
        // if node is supposed to update and it is the same type, then no need to update it.
        if (prev.nodes?.[nodeId]?.name === node.name || prev.triggers?.[nodeId]?.name === node.name)
          return prev;

        // if node is one of trigger nodes, then only allow trigger node types to be updated.
        if (prev.triggers?.[nodeId]) {
          if (!getTriggerNodes().includes(node.name)) return prev;
          return {
            ...prev,
            triggers: {
              ...prev.triggers,
              [nodeId]: { ...prev.triggers[nodeId], ...node, settings: {} as any },
            },
          };
        }
        // else if node is a looping node, then we can update that node , but need to remove recursively all sub nodes.
        if (prev.nodes?.[nodeId]?.name === FlowNodeType.Looping) {
          const newFlow = flowActions.removeAllSubNodesLoopingNode(prev, nodeId);
          if (!newFlow) return prev;
          let updatedNode = {
            ...newFlow!.nodes?.[nodeId],
            ...node,
            settings: {} as any,
          } as FlowNodeData;

          if (node.name === FlowNodeType.Path) {
            const currentNode = newFlow.nodes?.[nodeId];
            if (!currentNode) return prev;
            const parentId = currentNode?.prev ?? '';
            const nextNodeId = currentNode?.next ?? '';
            const newPathNode = flowActions.getNewBranchNode(nextNodeId, parentId);
            newPathNode.id = currentNode.id;
            updatedNode = { ...updatedNode, ...newPathNode };
            setCurrentSelectNodeId?.(undefined);
            closeRightPanel?.();
          }

          return {
            ...newFlow,
            nodes: { ...newFlow.nodes, [nodeId]: updatedNode },
          };
        }

        if (
          !prev.nodes?.[nodeId] ||
          (getTriggerNodes().includes(node.name) && !getActionNodes().includes(node.name))
        ) {
          return prev;
        }

        // else if node is path node, let create a correct form of path node.
        if (node.name === FlowNodeType.Path) {
          const currentNode = prev.nodes?.[nodeId];
          if (!currentNode) return prev;
          const parentId = currentNode?.prev ?? '';
          const nextNodeId = currentNode?.next ?? '';
          const newPathNode = flowActions.getNewBranchNode(nextNodeId, parentId);
          newPathNode.id = currentNode.id;
          setCurrentSelectNodeId?.(undefined);
          closeRightPanel?.();
          return {
            ...prev,
            nodes: { ...prev.nodes, [nodeId]: newPathNode },
          };
        }

        return {
          ...prev,
          nodes: {
            ...prev.nodes,
            [nodeId]: { ...prev.nodes?.[nodeId], ...node, settings: {} as any },
          },
        };
      });
      setCurrentSelectNodeData?.((pre) => {
        if (!pre) return pre;
        return {
          ...pre,
          type: node.name,
        };
      });
    },
    [
      setFlow,
      closeRightPanel,
      setCurrentSelectNodeId,
      setCurrentSelectNodeData,
      flowActions.getNewBranchNode,
      flowActions.removeAllSubNodesLoopingNode,
      getTriggerNodes,
      getActionNodes,
    ]
  );

  const handleMoveNodeTo = useCallback(
    (payload: MoveNodePayload) => {
      if (!payload?.dataItemDropped?.nodeId || !payload?.dropAtData || !flow) return;
      const newFlow = flowActions.moveNodeTo(flow, payload);
      if (!newFlow) return;
      closeRightPanel?.();
      setCurrentSelectNodeId?.(undefined);
      setFlow(newFlow);
    },
    [flow, flowActions.moveNodeTo, setFlow, closeRightPanel, setCurrentSelectNodeId]
  );

  const validateFlow = useCallback(() => {
    if (!flow) return false;
    return flowValidator(flow);
  }, [flow]);

  const flowValidator = (flow: Flow) => {
    const triggerNodes = Object.values(flow.triggers);
    const actionNodes = Object.values(flow?.nodes || {});
    return [
      triggerNodes.every((trigger) => trigger?.settings?.ui?.step === 1),
      actionNodes.every((node) => node?.settings?.ui?.step === 1),
    ].every(Boolean);
  };

  return {
    flowActions,
    validateFlow,
    handleAddNewNode,
    handleAddNewPath,
    handleRemoveNode,
    handleMoveNodeTo,
    handleUpdateNode,
    handleAddNewSubPath,
    handleDuplicateNode,
    handleUpdateTrigger,
    handleUpdateNodeByPaths,
    handleUpdateTriggerByPaths,
  };
};
