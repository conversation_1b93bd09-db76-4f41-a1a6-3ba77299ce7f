import { Text, rem, useMantineTheme } from '@mantine/core';
import {
  Background,
  BackgroundVariant,
  ConnectionMode,
  Controls,
  MarkerType,
  ReactFlow,
  ReactFlowProvider,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import BuilderLayout from '@/components/BuilderLayout';
import RunWrapper, { type HistoryRunsRef } from '@/components/BuilderLayout/RunWrapper';
import { InnerVersionControl } from '@/components/BuilderLayout/VersionControlWrapper';
import CatalogModal from '@/components/FlowBuilder/ModalPlacements/CatalogModal';
import RightFormPanel from '@/components/FlowBuilder/ModalPlacements/RightFormPanel';
import { DragLayerPreview } from '@/components/FlowBuilder/Nodes/DrapDropLayer/DragLayerPreview';
import UnsavedChangesModal from '@/components/UnsavedChangesModal';
import { AppConfig } from '@/configs';
import { RESOURCE_TYPE } from '@/constants/common';
import { FlowBuilderProvider, useFlowBuilderContext } from '@/contexts/FlowBuilderContext';
import { RunContextProvider, useRunContext } from '@/contexts/RunContext';
import { VersionContextProvider } from '@/contexts/VersionContext';
import { useAutoPan } from '@/hooks/useFlowBuilder/useAutoPan';
import { useFlowRunRealtimeWithNotification } from '@/hooks/useFlowRunRealtimeWithNotification';
import { useUnsavedChanges } from '@/hooks/useUnsavedChanges';
import { showErrorNotification, showSuccessNotification } from '@/utils/notification';
import { Modal, useNodeSchemasDebug } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import nodeTypes from './nodeTypes';

const defaultEdgeOptions = {
  type: 'floating',
  markerEnd: {
    type: MarkerType.ArrowClosed,
  },
};

function FlowBuilderContent() {
  // Add auto span hook to set viewport of the flow
  const navigate = useNavigate();
  const theme = useMantineTheme();
  const { workspaceId } = useParams();
  const { mutateRuns } = useRunContext();
  const historyRunsRef = useRef<HistoryRunsRef>(null);
  const { t } = useTranslate('common');

  useAutoPan();

  const {
    flow,
    flowOriginal,
    flowActionHandlers,
    nodes,
    edges,
    loading,
    handleRun,
    handleBack,
    closeCatalog,
    catalogModalRef,
    openedRightPanel,
    handleDeleteFlow,
    handlePublish,
    handleTitleChange,
    handleDuplicateFlow,
    handleSaveAsTemplate,
    // handleOpenTemplate,
    handleOnSelectCatalog,
    handleCloseRightPanel,
    handleSave,
  } = useFlowBuilderContext();

  useFlowRunRealtimeWithNotification(workspaceId || '', flow?.id || '', (runId) => {
    mutateRuns(runId);
  });

  useNodeSchemasDebug({ enabled: !AppConfig.IS_PRODUCTION });

  const initialFitViewNodes = useMemo(() => {
    // select all trigger nodes's id and 2 first nodes's id after trigger
    if (!flow?.triggers) return [];
    const triggerKeys = Object.keys(flow?.triggers);
    const nextNodeId = flow?.triggers?.[triggerKeys[0]]?.next ?? '';
    const nextNextNodeId = flow?.nodes?.[nextNodeId]?.next ?? '';
    return [
      ...triggerKeys.map((key) => {
        return {
          id: key,
        };
      }),
      {
        id: nextNodeId,
      },
      {
        id: nextNextNodeId,
      },
    ];
  }, [flow?.triggers, flow?.nodes]);

  const [unChanged, setUnChanged] = useState(false);
  const [isBackClick, setIsBackClick] = useState(false);
  const [showActionModal, setShowActionModal] = useState(false);
  const [pendingAction, setPendingAction] = useState<'publish' | 'run' | null>(null);

  const { showPrompt, setShowPrompt, confirmNavigation, cancelNavigation } = useUnsavedChanges({
    when: unChanged,
    onConfirm: () => {
      setUnChanged(false);
    },
  });

  const handleSaveFlow = async () => {
    setUnChanged(false);
    await handleSave();
    showSuccessNotification(t('builderLayout.savedSuccessfully'));
  };

  const handlePublishFlow = () => {
    if (!flowActionHandlers.validateFlow()) {
      showErrorNotification(t('builderLayout.imCompleteFlow'));
      return;
    }

    if (unChanged) {
      setPendingAction('publish');
      setShowActionModal(true);
    } else {
      handlePublish();
      showSuccessNotification(t('builderLayout.published'));
    }
  };

  const handleRunFlow = async () => {
    await handleRun();
    await mutateRuns();
    historyRunsRef.current?.open();
  };

  const handleActionModalConfirm = async () => {
    if (!pendingAction) return;
    setShowActionModal(false);
    setUnChanged(false);
    if (pendingAction === 'publish') {
      handlePublish();
      showSuccessNotification(t('builderLayout.published'));
    } else {
      handleRunFlow();
    }
    setPendingAction(null);
  };

  const handleActionModalCancel = () => {
    setShowActionModal(false);
    setPendingAction(null);
  };

  const handleRunManual = async () => {
    if (!flowActionHandlers.validateFlow()) {
      showErrorNotification(t('builderLayout.imCompleteFlow'));
      return;
    }

    if (unChanged) {
      setPendingAction('run');
      setShowActionModal(true);
      return;
    }
    handleRunFlow();
  };

  const goBack = () => {
    navigate(`/studio/${workspaceId}/flows`);
  };

  useEffect(() => {
    if (!flow) return;
    const hasChanged = JSON.stringify(flow) !== JSON.stringify(flowOriginal);
    setUnChanged(hasChanged);
  }, [flow, flowOriginal]);

  useEffect(() => {
    if (!showPrompt) {
      setIsBackClick(false);
    }
  }, [showPrompt]);

  const handleBackClick = () => {
    if (unChanged) {
      setIsBackClick(true);
      setShowPrompt(true);
      return;
    }
    handleBack();
  };

  const handleConfirmNavigation = () => {
    if (isBackClick) {
      setShowPrompt(false);
      setIsBackClick(false);
      setUnChanged(false);
      handleBack();
      return;
    }
    confirmNavigation();
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <>
      <BuilderLayout
        onBack={handleBackClick}
        maxTitleLength={40}
        onRun={handleRunManual}
        onPublish={handlePublishFlow}
        onTitleChange={handleTitleChange}
        title={flow?.name || 'Untitled Flow'}
        // openTemplate={handleOpenTemplate}
        historyRuns={
          <RunWrapper
            ref={historyRunsRef}
            resourceId={flow?.id || ''}
            onRunClick={handleRunManual}
            resourceType={RESOURCE_TYPE.FLOW}
          />
        }
        versionControl={<InnerVersionControl resourceId={flow?.id || ''} resourceType='flow' />}
        seeMoreActions={{
          onDuplicate: () => {
            flow && handleDuplicateFlow(flow, false);
          },
          onDelete: () => {
            flow && handleDeleteFlow(flow, () => goBack());
          },
          onSaveAsTemplate: handleSaveAsTemplate,
        }}
        onSave={handleSaveFlow}
        disabled={!unChanged}
      >
        <ReactFlow
          fitView
          nodes={nodes}
          edges={edges}
          minZoom={0.4}
          maxZoom={1.5}
          deleteKeyCode={null}
          nodeTypes={nodeTypes}
          fitViewOptions={{
            padding: 0.2,
            minZoom: 1.2,
            maxZoom: 1.2,
            includeHiddenNodes: false,
            nodes: initialFitViewNodes,
          }}
          connectionMode={ConnectionMode.Loose}
          defaultEdgeOptions={defaultEdgeOptions}
          defaultViewport={{ x: 0, y: 0, zoom: 1.2 }}
        >
          <Background
            size={2}
            gap={[18, 18]}
            variant={BackgroundVariant.Dots}
            color={theme.colors.silverFox[6]}
            bgColor={theme.colors.silverFox[2]}
          />
          <Controls showInteractive={false} />
        </ReactFlow>
        <RightFormPanel opened={openedRightPanel} onClose={handleCloseRightPanel} />

        <UnsavedChangesModal
          opened={showPrompt}
          onCancel={cancelNavigation}
          onConfirm={handleConfirmNavigation}
          onSaveAndQuit={handleSaveFlow}
        />
        <Modal
          opened={showActionModal}
          onClose={handleActionModalCancel}
          title={t(
            pendingAction === 'publish'
              ? 'builderLayout.saveAndPublish'
              : 'builderLayout.saveAndRun'
          )}
          centered
          okText={t(
            pendingAction === 'publish'
              ? 'builderLayout.saveAndPublish'
              : 'builderLayout.saveAndRun'
          )}
          cancelText={t('builderLayout.cancel')}
          onOk={handleActionModalConfirm}
          onCancel={handleActionModalCancel}
          footerDivider={false}
          okButtonProps={{
            variant: 'secondary',
          }}
          cancelButtonProps={{
            variant: 'negative',
          }}
        >
          <Text mb={rem(10)}>
            {t(
              pendingAction === 'publish'
                ? 'builderLayout.saveAndPublishDescription'
                : 'builderLayout.saveAndRunDescription'
            )}
          </Text>
        </Modal>
      </BuilderLayout>
      <CatalogModal ref={catalogModalRef} onClose={closeCatalog} onSelect={handleOnSelectCatalog} />
    </>
  );
}

export default function FlowBuilder() {
  const { flowId } = useParams();

  return (
    <DndProvider backend={HTML5Backend}>
      <ReactFlowProvider>
        <FlowBuilderProvider>
          <DragLayerPreview />
          <RunContextProvider resourceType='flow' resourceId={flowId || ''}>
            <VersionContextProvider resourceType='flow' resourceId={flowId || ''}>
              <FlowBuilderContent />
            </VersionContextProvider>
          </RunContextProvider>
        </FlowBuilderProvider>
      </ReactFlowProvider>
    </DndProvider>
  );
}
