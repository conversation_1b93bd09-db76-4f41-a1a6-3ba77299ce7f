import { PageHeaderWithActions } from '@/components';
import AIEmpty from '@/components/AIEmpty';
import AIPagination from '@/components/AIPagination';
import { CredentialList } from '@/components/Credential/CredentialList';
import { ProvidersModal } from '@/components/Credential/ProvidersModal';
import { useAppContext } from '@/contexts/AppContext';
import {
  CredentialContextProvider,
  type CredentialContextType,
  useCredentialContext,
} from '@/contexts/CredentialContext';
import { useRestrictLimitParam } from '@/hooks/useRestrictLimitParam';
import { type CardStatus, LayoutType } from '@/types';
import { Box, Stack, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { CredentialModalTolgee, type ICredential, type ICredentialPayload } from '@resola-ai/ui';
import { useTolgee, useTranslate } from '@tolgee/react';
import { debounce } from 'lodash';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

export const handleWithError = async (
  callback: () => Promise<void>,
  setStatus?: (status: CardStatus) => void
) => {
  try {
    await callback();
  } catch (error) {
    setStatus?.({
      status: 'error',
      message: error instanceof Error ? error.message : 'An unknown error occurred',
    });
  }
};

const useStyles = createStyles(() => ({
  pageHeader: {
    marginBottom: rem(24),
  },
  pagination: {
    marginBottom: rem(24),
  },
}));

const Credentials = () => {
  const { t } = useTranslate('credential');
  const { classes } = useStyles();
  const tolgee = useTolgee();
  const [_, setSearchParams] = useSearchParams();
  const {
    limit,
    setLimit,
    setCursor,
    tools = [],
    credentials,
    searchValue,
    setSearchValue,
    deleteCredential,
    testCredential,
    reconnectCredential,
    createCredential,
    updateCredential,
    selectedCredential,
    setSelectedCredential,
    selectedTool,
    setSelectedTool,
    loadingCredential,
    fetchCredential,
  } = useCredentialContext();

  const [search, setSearch] = useState(searchValue);
  const [layoutType, setLayoutType] = useState<LayoutType>(LayoutType.GRID);
  const [modalOpened, { open: openModal, close: closeModal }] = useDisclosure(false);
  const [ProvidersModalOpened, { open: openProvidersModal, close: closeProvidersModal }] =
    useDisclosure(false);
  const { openConfirmModal, closeConfirmModal } = useAppContext();

  useEffect(() => {
    setSearch(searchValue);
  }, [searchValue]);

  const handleLayoutChange = (value: LayoutType) => {
    setLayoutType(value);
  };

  const handleSelectTool = (tool: any) => {
    setSelectedTool(tool);
    setSelectedCredential(undefined);
    openModal();
    closeProvidersModal();
  };

  const debounceSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchValue(value);
      }, 500),
    [setSearchValue]
  );

  const handleSearchChange = (value: string) => {
    setSearch(value);
    debounceSearch(value);
  };

  useEffect(() => {
    return () => {
      debounceSearch.cancel();
    };
  }, []);

  const handleCreateNewCredential = () => {
    setSelectedCredential(undefined);
    setSelectedTool(undefined);
    openProvidersModal();
  };

  const handleCreateCredential = useCallback(
    async (values: ICredentialPayload, setStatus?: (status: CardStatus) => void) => {
      handleWithError(async () => {
        if (selectedCredential?.id) {
          await updateCredential(values);
        } else {
          await createCredential(values);
        }

        closeProvidersModal();
        closeModal();
      }, setStatus);
    },
    [selectedCredential]
  );

  const handleEditCredential = async (credential: ICredentialPayload) => {
    updateCredential(credential);
  };

  const handleDeleteCredential = async (
    credential: ICredentialPayload,
    setStatus?: (status: CardStatus) => void
  ) => {
    openConfirmModal({
      title: t('modalDelete.title'),
      content: (
        <Text
          // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
          dangerouslySetInnerHTML={{
            __html: t('modalDelete.confirm', {
              provider: credential.provider?.toUpperCase(),
              name: credential.name,
            }),
          }}
        />
      ),
      confirmText: t('modalDelete.confirmText'),
      onConfirm: async () => {
        if (credential.id) {
          await handleWithError(async () => {
            if (credential.id) {
              await deleteCredential(credential.id);
              closeModal();
            }
          }, setStatus);
        }
        closeConfirmModal();
      },
      onCancel: () => {
        closeConfirmModal();
      },
      options: {
        zIndex: 1001,
      },
    });
  };

  const handleReconnectCredential = async (
    credential: ICredential,
    setStatus?: (status: CardStatus) => void
  ) => {
    openConfirmModal({
      title: t('modalReconnect.title'),
      content: (
        <Text
          // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
          dangerouslySetInnerHTML={{
            __html: t('modalReconnect.confirm', { name: credential.name }),
          }}
        />
      ),
      confirmText: t('modalReconnect.confirmText'),
      isRemoving: false,
      onConfirm: async () => {
        await handleWithError(async () => {
          await reconnectCredential(credential.id);
          setStatus?.({
            status: 'success',
            message: t('reconnect.success'),
          });
        }, setStatus);
        closeConfirmModal();
      },
      onCancel: () => {
        closeConfirmModal();
      },
    });
  };

  const handleTestCredential = async (
    credential: ICredentialPayload,
    setStatus?: (status: CardStatus) => void
  ) => {
    handleWithError(async () => {
      await testCredential(credential);
      setStatus?.({
        status: 'success',
        message: t('test.success'),
      });
    }, setStatus);
  };

  const handleSelectCredential = (credential: ICredential) => {
    const tool = tools.find((tool) => tool.name === credential.provider);
    if (!tool) {
      return;
    }

    setSelectedTool(tool);
    fetchCredential(credential.id);
    openModal();
  };

  const handleChangeLimit = (value: number) => {
    setLimit(value);
    setCursor('');
  };

  useRestrictLimitParam(limit, setLimit, setSearchParams, () => setCursor(''));

  return (
    <Box data-testid='credentials-page'>
      <PageHeaderWithActions
        title={t('list.title')}
        description={t('list.description')}
        searchPlaceholder={t('list.search')}
        searchValue={search}
        onSearchChange={handleSearchChange}
        layoutType={layoutType}
        onLayoutChange={handleLayoutChange}
        isUsingButtonMenuActions={false}
        buttonActionLabel={t('action.createCredential')}
        handleButtonActionClick={handleCreateNewCredential}
        className={classes.pageHeader}
        hasData={(credentials?.data || []).length > 0}
      />
      {(credentials?.data || []).length > 0 ? (
        <Stack gap={rem(20)} mb={rem(10)}>
          <CredentialList
            credentials={credentials?.data || []}
            isFullWidth={layoutType === LayoutType.LIST}
            onEdit={handleEditCredential}
            onDelete={handleDeleteCredential}
            onTest={handleTestCredential}
            onReconnect={handleReconnectCredential}
            onSelect={handleSelectCredential}
          />
          <AIPagination
            limit={limit}
            onChangeLimit={handleChangeLimit}
            onCursorChange={setCursor}
            nextCursor={credentials?.nextCursor}
            prevCursor={credentials?.prevCursor}
            onNext={() => setCursor(credentials?.nextCursor ?? '')}
            onPrevious={() => setCursor(credentials?.prevCursor ?? '')}
          />
        </Stack>
      ) : (
        <AIEmpty />
      )}
      <ProvidersModal
        opened={ProvidersModalOpened}
        onClose={closeProvidersModal}
        onSelect={handleSelectTool}
      />
      <CredentialModalTolgee
        tolgee={tolgee}
        opened={modalOpened}
        onClose={closeModal}
        onSubmit={handleCreateCredential}
        onTest={handleTestCredential}
        onDelete={handleDeleteCredential}
        credential={selectedCredential}
        schema={selectedTool}
        loading={loadingCredential}
      />
    </Box>
  );
};

const CredentialsPage = ({ value }: { value?: CredentialContextType }) => {
  return (
    <CredentialContextProvider value={value}>
      <Credentials />
    </CredentialContextProvider>
  );
};

export default CredentialsPage;
