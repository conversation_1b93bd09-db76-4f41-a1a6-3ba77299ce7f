import { Stack, rem } from '@mantine/core';
import { useDebouncedValue } from '@mantine/hooks';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';

import AIPagination from '@/components/AIPagination';
import AgentList from '@/components/Agents/AgentList';
import { DEFAULT_PER_PAGE, PER_PAGE_OPTIONS } from '@/constants/common';
import { AgentContextProvider, useAgentContext } from '@/contexts/AgentContext';
import { useRestrictLimitParam } from '@/hooks/useRestrictLimitParam';
import type { IAgent } from '@/models';
import { LayoutType } from '@/types';

const AgentContainer = () => {
  const navigate = useNavigate();
  const { workspaceId } = useParams();
  const { agents, setApiParams, isLoadingAgents } = useAgentContext();
  const [searchParams, setSearchParams] = useSearchParams();
  const [limit, setLimit] = useState<number>(DEFAULT_PER_PAGE); // Ensure default is correctly set
  const [searchInputValue, setSearchInputValue] = useState('');
  const [debouncedSearchValue] = useDebouncedValue(searchInputValue, 800);
  const [displayLayout, setDisplayLayout] = useState<LayoutType>(
    (searchParams.get('layout') as LayoutType) || LayoutType.GRID
  );
  const [latestAgents, setLatestAgents] = useState<IAgent[]>([]);
  const isFullWidth = useMemo(() => displayLayout === LayoutType.LIST, [displayLayout]);

  useEffect(() => {
    if (agents?.data && !isLoadingAgents) {
      setLatestAgents(agents.data);
    }
  }, [agents?.data, isLoadingAgents]);

  const handleLayoutChange = (layout: LayoutType) => {
    setDisplayLayout(layout);
    setSearchParams((prev) => {
      prev.set('layout', layout);
      return prev;
    });
  };

  const handleLimitChange = useCallback(
    (limit: number) => {
      setLimit(limit);
      setApiParams((prev) => ({
        ...prev,
        cursor: undefined,
        limit,
      }));
    },
    [setApiParams]
  );

  const handlePaginationChange = useCallback(
    (cursor?: string) => {
      setApiParams((prev) => ({
        ...prev,
        cursor,
      }));
    },
    [setApiParams]
  );

  const handleItemClick = useCallback(
    (agent: IAgent) => {
      navigate(`/studio/${workspaceId}/agents/${agent.id}`);
    },
    [navigate, workspaceId]
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (workspaceId) {
      setApiParams((prev) => ({
        ...prev,
        workspaceId,
      }));
    }
  }, [workspaceId]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    setApiParams((prev) => ({
      ...prev,
      cursor: undefined,
      filters: debouncedSearchValue
        ? {
            name: debouncedSearchValue,
          }
        : undefined,
    }));
  }, [debouncedSearchValue]);

  useEffect(() => {
    const limitOptions = PER_PAGE_OPTIONS.map((option) => option.value);
    if (!limitOptions.includes(limit || 0)) {
      setLimit(DEFAULT_PER_PAGE);
      setSearchParams((prev) => {
        prev.set('cursor', '');
        prev.set('limit', DEFAULT_PER_PAGE.toString());
        return prev;
      });
    }
  }, [limit, setSearchParams]);

  useRestrictLimitParam(limit, setLimit, setSearchParams, () =>
    setApiParams((prev) => ({
      ...prev,
      cursor: undefined,
    }))
  );

  return (
    <Stack gap={rem(20)} mb={rem(10)}>
      <AgentList
        agents={latestAgents}
        isFullWidth={isFullWidth}
        searchValue={searchInputValue}
        setSearchValue={setSearchInputValue}
        displayLayout={displayLayout}
        handleLayoutChange={handleLayoutChange}
        onItemClick={handleItemClick}
      />
      {latestAgents.length > 0 && (
        <AIPagination
          limit={limit}
          onCursorChange={handlePaginationChange}
          onChangeLimit={handleLimitChange}
          nextCursor={agents?.nextCursor}
          prevCursor={agents?.prevCursor}
          onNext={() => handlePaginationChange(agents?.nextCursor)}
          onPrevious={() => handlePaginationChange(agents?.prevCursor)}
        />
      )}
    </Stack>
  );
};

const Agents = () => {
  return (
    <AgentContextProvider>
      <AgentContainer />
    </AgentContextProvider>
  );
};

export default Agents;
