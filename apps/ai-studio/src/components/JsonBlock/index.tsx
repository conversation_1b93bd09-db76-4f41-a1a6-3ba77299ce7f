import type { IRunError, IRunInput, IRunLogs, IRunOutput } from '@/models/historyRun';
import {
  type CollapsedState,
  type JsonFormatterClasses,
  type JsonNode,
  JsonNodeRenderer,
  cleanUnicodeFromData,
  formatJSONLine,
  getCodeLines,
  parseJsonToTree,
} from '@/utils/history/jsonFormatter';
import {
  ActionIcon,
  Box,
  Button,
  CopyButton,
  Flex,
  ScrollArea,
  Switch,
  Text,
  rem,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCheck, IconCopy } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useEffect, useMemo, useState } from 'react';

type BlockProps = {
  data?: IRunInput | IRunOutput | IRunLogs[] | IRunError;
  isError?: boolean;
  emptyText?: string;
  isShowLineNumber?: boolean;
  className?: string;
  enableCollapse?: boolean;
  maxInitialLines?: number;
  defaultExpandedLevels?: number;
};

const useStyles = createStyles((theme, isError: boolean) => ({
  codeBlock: {
    maxHeight: '60vh',
    lineHeight: 1.7,
    borderRadius: theme.radius.md,
    border: `1px solid ${isError ? theme.colors.decaRed[3] : theme.colors.decaLight[3]}`,
    backgroundColor: isError ? theme.colors.decaRed[0] : theme.colors.decaLight[0],
    padding: theme.spacing.md,
  },
  lineNumber: {
    width: rem(35),
    color: theme.colors.decaGrey[2],
    fontWeight: 500,
    userSelect: 'none',
  },
  lineContent: {
    padding: `0 ${rem(8)}`,
    fontWeight: 500,
    width: '100%',
    wordBreak: 'break-word',
  },
  codeLine: {
    display: 'flex',
    width: '100%',
  },
  jsonKey: {
    color: theme.colors.decaBlue[7],
  },
  jsonValue: {
    color: theme.colors.decaGreen[7],
    whiteSpace: 'pre-wrap',
  },
  jsonBrace: {
    color: theme.colors.decaGrey[5],
  },
  controlsRow: {
    marginBottom: theme.spacing.sm,
  },
}));

const Block = ({
  data,
  isError = false,
  emptyText,
  isShowLineNumber = true,
  className,
  enableCollapse = true,
  maxInitialLines = 50, // Default to 50 lines
  defaultExpandedLevels = 0,
}: BlockProps) => {
  const { t } = useTranslate('common');
  const { cx, classes } = useStyles(isError);
  const [collapsedState, setCollapsedState] = useState<CollapsedState>({});
  const [useCollapseMode, setUseCollapseMode] = useState(enableCollapse);
  const [showAll, setShowAll] = useState(false); // New state for show all functionality

  const dataCleaned = useMemo(() => {
    return cleanUnicodeFromData(data);
  }, [data]);

  const { lines, jsonTree, totalLines, totalNodes } = useMemo(() => {
    let lines: string[] = [];
    let allLines: string[] = [];
    let jsonTree: JsonNode[] | null = null;

    // Fallback to line view
    allLines = getCodeLines(dataCleaned);
    const totalLines = allLines.length;

    // Limit lines if not showing all
    if (!showAll && totalLines > maxInitialLines) {
      lines = allLines.slice(0, maxInitialLines);
    } else {
      lines = allLines;
    }

    if (useCollapseMode) {
      try {
        jsonTree = parseJsonToTree(dataCleaned);
      } catch {
        jsonTree = null;
      }

      return {
        lines,
        jsonTree,
        totalLines: 0,
        totalNodes: 0,
      };
    }

    return {
      lines,
      jsonTree: null,
      totalLines,
      totalNodes: 0,
    };
  }, [dataCleaned, useCollapseMode, showAll, maxInitialLines]);

  // Initialize collapsed state based on defaultExpandedLevels when jsonTree changes
  useEffect(() => {
    if (jsonTree && defaultExpandedLevels !== undefined) {
      const initialState: CollapsedState = {};
      const initializeCollapsedState = (nodes: typeof jsonTree, level = 0) => {
        nodes.forEach((node) => {
          if (node.isCollapsible && level > defaultExpandedLevels) {
            initialState[node.id] = true; // Collapse nodes deeper than defaultExpandedLevels
          }
          if (node.children) {
            initializeCollapsedState(node.children, level + 1);
          }
        });
      };
      initializeCollapsedState(jsonTree);
      setCollapsedState(initialState);
    }
  }, [jsonTree, defaultExpandedLevels]);

  const jsonFormatterClasses: JsonFormatterClasses = {
    jsonKey: classes.jsonKey,
    jsonValue: classes.jsonValue,
    jsonBrace: classes.jsonBrace,
  };

  const handleToggleCollapsed = (nodeId: string) => {
    setCollapsedState((prev) => ({
      ...prev,
      [nodeId]: !prev[nodeId],
    }));
  };

  const handleCollapseAll = () => {
    if (!jsonTree) return;
    const newState: CollapsedState = {};
    const collectCollapsibleNodes = (nodes: typeof jsonTree) => {
      nodes.forEach((node) => {
        if (node.isCollapsible) {
          newState[node.id] = true;
        }
        if (node.children) {
          collectCollapsibleNodes(node.children);
        }
      });
    };
    collectCollapsibleNodes(jsonTree);
    setCollapsedState(newState);
  };

  const handleExpandAll = () => {
    setCollapsedState({});
  };

  const handleToggleShowAll = () => {
    setShowAll((prev) => !prev);
  };

  const shouldShowToggleButton = useCollapseMode
    ? totalNodes > maxInitialLines
    : totalLines > maxInitialLines;

  return (
    <ScrollArea.Autosize className={cx(classes.codeBlock, className)} data-testid='code-block'>
      {data ? (
        <Box h={'100%'}>
          <Flex justify='space-between' align='center' className={classes.controlsRow}>
            <Flex align='center' gap='md'>
              {enableCollapse && (
                <>
                  <Switch
                    size='sm'
                    label={t('historyRuns.collapsibleView')}
                    checked={useCollapseMode}
                    onChange={(event) => {
                      setUseCollapseMode(event.currentTarget.checked);
                      setShowAll(false);
                    }}
                    styles={{
                      track: {
                        cursor: 'pointer',
                      },
                    }}
                  />
                  {useCollapseMode && jsonTree && (
                    <Flex gap='xs'>
                      <Button variant='subtle' onClick={handleCollapseAll} size='xs'>
                        {t('historyRuns.collapseAll')}
                      </Button>
                      <Button variant='subtle' onClick={handleExpandAll} size='xs'>
                        {t('historyRuns.expandAll')}
                      </Button>
                    </Flex>
                  )}
                </>
              )}

              {/* Show All / Show Less button */}
              {shouldShowToggleButton && (
                <Button variant='subtle' onClick={handleToggleShowAll} size='xs'>
                  {showAll
                    ? t('historyRuns.showLess', {
                        count: maxInitialLines,
                      })
                    : t('historyRuns.showAll', {
                        count: useCollapseMode ? totalNodes : totalLines,
                      })}
                </Button>
              )}
            </Flex>

            <Flex align='center' gap={0}>
              <CopyButton value={lines.join('\n')}>
                {({ copied, copy }) => (
                  <>
                    <ActionIcon
                      color={copied ? 'teal' : 'gray'}
                      onClick={copy}
                      variant='subtle'
                      data-testid='json-block-copy-button'
                    >
                      {copied ? <IconCheck size={16} /> : <IconCopy size={16} />}
                    </ActionIcon>
                    <Text size={rem(12)} c={'decaGrey.6'}>
                      {copied ? t('historyRuns.copied') : t('historyRuns.copy')}
                    </Text>
                  </>
                )}
              </CopyButton>
            </Flex>
          </Flex>

          {useCollapseMode && jsonTree ? (
            // Render collapsible tree view
            <Box>
              {jsonTree.map((node) => (
                <JsonNodeRenderer
                  key={node.id}
                  node={node}
                  classes={jsonFormatterClasses}
                  collapsedState={collapsedState}
                  onToggleCollapsed={handleToggleCollapsed}
                />
              ))}
            </Box>
          ) : (
            // Render traditional line-by-line view
            <Box>
              {lines.map((line, index) => (
                <Flex key={`line-${index}-${line.slice(0, 20)}`} className={classes.codeLine}>
                  {isShowLineNumber && (
                    <Text className={classes.lineNumber}>{(index + 1).toString().padStart(3)}</Text>
                  )}
                  <Box className={classes.lineContent}>
                    {formatJSONLine(line, jsonFormatterClasses)}
                  </Box>
                </Flex>
              ))}
              {/* Show truncation indicator if not showing all */}
              {!showAll && totalLines > maxInitialLines && (
                <Text size='sm' c='dimmed' mt='xs' style={{ fontStyle: 'italic' }}>
                  ... and {totalLines - maxInitialLines} more lines
                </Text>
              )}
            </Box>
          )}
        </Box>
      ) : (
        <Text>{emptyText}</Text>
      )}
    </ScrollArea.Autosize>
  );
};

export default Block;
