import { Stack, rem } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { useTranslate } from '@tolgee/react';
import { useCallback, useState } from 'react';

import AICard from '@/components/AICard';
import AIEmpty from '@/components/AIEmpty';
import GridLayout from '@/components/GridLayout';
import { EditModal } from '@/components/Modals';
import PageHeaderWithActions from '@/components/PageHeaderWithActions';
import { DEFAULT_MODEL_OPTIONS } from '@/constants/agent';
import { useAgentContext } from '@/contexts/AgentContext';
import { useAppContext } from '@/contexts/AppContext';
import type { IAgent } from '@/models/agent';
import type { EditModalData, LayoutType } from '@/types';

interface AgentListProps {
  agents: IAgent[];
  isFullWidth?: boolean;
  searchValue: string;
  setSearchValue: (value: string) => void;
  displayLayout: LayoutType;
  handleLayoutChange: (value: LayoutType) => void;
  onItemClick: (agent: IAgent) => void;
}

const AgentList = ({
  agents,
  isFullWidth = false,
  searchValue,
  setSearchValue,
  displayLayout,
  handleLayoutChange,
  onItemClick,
}: AgentListProps) => {
  const { t } = useTranslate('agent');
  const { openConfirmModal, closeConfirmModal } = useAppContext();
  const {
    agentModels,
    onCreateAgent,
    onUpdateAgent,
    onDeleteAgent,
    onDuplicateAgent,
    onExportAgent,
    onImportAgent,
    isLoadingAgents,
  } = useAgentContext();
  const [isEditModalOpen, { close: closeEditModal, open: openEditModal }] = useDisclosure(false);
  const [selectedAgent, setSelectedAgent] = useState<IAgent | null>(null);
  const [isCreatingAgent, setIsCreatingAgent] = useState(false);

  const handleEditAgent = useCallback(
    (agent: IAgent) => {
      setSelectedAgent(agent);
      openEditModal();
    },
    [openEditModal]
  );

  const handleCreateAgent = useCallback(() => {
    setIsCreatingAgent(true);
    openEditModal();
  }, [openEditModal]);

  const handleConfirmCreateAgent = useCallback(
    async (value: EditModalData) => {
      await onCreateAgent({
        name: value.title,
        description: value.description,
        settings: {
          ...DEFAULT_MODEL_OPTIONS,
          model: agentModels[0]?.modelId ?? DEFAULT_MODEL_OPTIONS.model,
        },
      });
      closeEditModal();
      setIsCreatingAgent(false);
    },
    [closeEditModal, onCreateAgent, agentModels]
  );

  const handleConfirmEditAgent = useCallback(
    async (value: EditModalData) => {
      if (!selectedAgent?.id) {
        return;
      }

      await onUpdateAgent({
        id: selectedAgent?.id,
        name: value.title,
        description: value.description,
      });
      closeEditModal();
      setSelectedAgent(null);
    },
    [closeEditModal, onUpdateAgent, selectedAgent?.id]
  );

  const handleDuplicateAgent = useCallback(
    async (agent: IAgent) => {
      await onDuplicateAgent(agent);
    },
    [onDuplicateAgent]
  );

  const handleDeleteAgent = useCallback(
    (agent: IAgent) => {
      openConfirmModal({
        title: t('actions.delete'),
        name: agent.name,
        onConfirm: async () => {
          await onDeleteAgent(agent.id);
          closeConfirmModal();
        },
        onCancel: closeConfirmModal,
      });
    },
    [openConfirmModal, closeConfirmModal, onDeleteAgent, t]
  );

  const handleExportAgent = useCallback(
    (agent: IAgent) => {
      onExportAgent(agent.id);
    },
    [onExportAgent]
  );

  const handleImportFromFile = useCallback(
    (file: File) => {
      onImportAgent(file);
    },
    [onImportAgent]
  );

  return (
    <Stack gap={rem(20)}>
      <PageHeaderWithActions
        title={t('title')}
        description={t('description')}
        searchPlaceholder={t('search.placeholder')}
        searchValue={searchValue}
        onSearchChange={setSearchValue}
        layoutType={displayLayout}
        onLayoutChange={handleLayoutChange}
        buttonActionLabel={t('actions.create')}
        handleCreateFromScratch={handleCreateAgent}
        isUsingButtonMenuActions
        hasData={agents.length > 0}
        handleImportFromFile={handleImportFromFile}
      />
      <GridLayout isFullWidth={isFullWidth || agents.length === 0}>
        {agents.length > 0 ? (
          agents.map((agent) => (
            <AICard
              key={agent.id}
              title={agent.name}
              description={agent.description}
              actions={{
                editLabel: t('actions.edit'),
                onEdit: () => handleEditAgent(agent),
                onDelete: () => handleDeleteAgent(agent),
                onDuplicate: () => handleDuplicateAgent(agent),
                onExport: () => handleExportAgent(agent),
              }}
              updatedAt={agent.updatedAt}
              aiModel={agent.settings?.model}
              isFullWidth={isFullWidth}
              onClick={() => onItemClick(agent)}
            />
          ))
        ) : !isLoadingAgents ? (
          <AIEmpty />
        ) : null}
        <EditModal
          title={isCreatingAgent ? t('modal.create.title') : t('modal.edit.title')}
          opened={isEditModalOpen}
          onConfirm={isCreatingAgent ? handleConfirmCreateAgent : handleConfirmEditAgent}
          onCancel={closeEditModal}
          options={{
            titleLabel: t('modal.edit.titleLabel'),
          }}
          initialValues={
            selectedAgent
              ? {
                  title: selectedAgent.name,
                  description: selectedAgent.description,
                }
              : undefined
          }
        />
      </GridLayout>
    </Stack>
  );
};

export default AgentList;
