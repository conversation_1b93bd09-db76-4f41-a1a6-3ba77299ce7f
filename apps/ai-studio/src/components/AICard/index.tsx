import { ActionIcon, Box, Card, Flex, Group, Text, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconBrandOpenai } from '@tabler/icons-react';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import { timeAgo } from '@/helpers/timeHelper';
import type { Callback, CardStatus } from '@/types';
import ActionsMenu, { type ActionsMenuProps } from '../ActionsMenu';
import AlertStatus from '../AlertStatus';

dayjs.extend(relativeTime);

export interface AICardActions extends ActionsMenuProps {
  onTest?: (callback?: Callback<CardStatus>) => void;
  onDelete?: (callback?: Callback<CardStatus>) => void;
  onReconnect?: (callback?: Callback<CardStatus>) => void;
}

interface AICardProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  aiModel?: string;
  actions?: AICardActions;
  flows?: Array<React.ReactNode>;
  isFullWidth?: boolean;
  containerHeight?: string;
  modelIcons?: React.ReactNode;
  switchButton?: React.ReactNode;
  updateTime?: React.ReactNode;
  updatedAt?: Date | string;
  onClick?: () => void;
}

const useStyles = createStyles((theme, { isFullWidth }: { isFullWidth: boolean }) => ({
  root: {
    backgroundColor: theme.white,
    borderColor: theme.colors.decaLight[isFullWidth ? 2 : 3],
    borderRadius: isFullWidth ? rem(10) : rem(12),
    height: isFullWidth ? 'auto' : rem(160),
    width: '100%',
    minHeight: 'auto',
    gap: rem(8),
    position: 'relative',
    cursor: 'pointer',
    transition: 'border-color 0.2s ease',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    '&:hover': {
      borderColor: theme.colors.decaBlue[6],
    },
    padding: rem(20),
  },
  modelIcon: {
    pointerEvents: 'none',
    borderColor: theme.colors.decaLight[2],
  },
  cardModel: {
    borderRight: `1px solid ${theme.colors.decaLight[3]}`,
    paddingRight: rem(10),
  },
  longCardInfo: {
    flex: 4,
    maxWidth: '60%',
  },
  longModelInfo: {
    flex: 2,
  },
  longCardModel: {
    borderRight: 'none',
  },
  modelIconsSwitchGroup: {
    width: 'calc(100% - 35px)',
    position: 'absolute',
    bottom: 10,
  },
  modelIconsSwitchGroupFlex: {
    width: '100%',
    justifyContent: 'space-between',
    flex: 2,
    gap: '1rem',
    flexWrap: 'nowrap',
  },
  modelIcons: {
    maxWidth: '50%',
    overflowX: 'hidden',
    overflowY: 'hidden',
    flexWrap: 'nowrap',
  },
  title: {
    whiteSpace: 'nowrap',
    marginBottom: rem(8),
  },
  description: {
    color: theme.colors.decaGrey[5],
    fontSize: rem(14),
    lineHeight: 1.5,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    WebkitLineClamp: 2,
    display: '-webkit-box',
    WebkitBoxOrient: 'vertical',
    whiteSpace: 'pre-line',
    width: '100%',
  },
}));

const AICard: React.FC<AICardProps> = ({
  title,
  description,
  icon,
  aiModel,
  actions,
  isFullWidth = false,
  modelIcons,
  switchButton,
  updateTime,
  updatedAt,
  onClick,
}) => {
  const { classes, cx, theme } = useStyles({ isFullWidth });
  const [searchParams] = useSearchParams();
  const lang = searchParams.get('lang') ?? 'ja';
  const updatedAtString = timeAgo(updatedAt, lang);
  const [status, setStatus] = useState<CardStatus | null>(null);
  const descriptionRef = useRef<HTMLDivElement>(null);

  // Custom 2 line with ellipsis function for NotoSanJP
  useEffect(() => {
    if (!description || !descriptionRef.current) return;

    const element = descriptionRef.current;
    const lineHeightDefault = 24;
    const lineCount = 2;

    // Reset content to original
    element.textContent = description;

    // Get computed line height
    const lineHeight =
      Number.parseFloat(window.getComputedStyle(element).lineHeight) || lineHeightDefault;
    const maxHeight = lineHeight * lineCount;

    // Check if the content overflows its container
    if (element.scrollHeight > maxHeight) {
      // Get the full text content
      const originalText = description;
      let truncatedText = originalText;

      // Truncate character by character from the end
      while (element.scrollHeight > maxHeight && truncatedText.length > 0) {
        // Remove the last character
        truncatedText = truncatedText.slice(0, -1);
        element.textContent = `${truncatedText}...`;
      }
    }
  }, [description, isFullWidth]);

  const newActions = useMemo(() => {
    if (actions?.onTest) {
      return {
        ...actions,
        onTest: () => {
          actions?.onTest?.(setStatus);
        },
        onDelete: () => {
          actions?.onDelete?.(setStatus);
        },
        onReconnect: () => {
          actions?.onReconnect?.(setStatus);
        },
      };
    }
    return actions;
  }, [actions]);

  const titleElement = useMemo(() => {
    return (
      <Title className={classes.title} order={6} c='decaGrey.9' lineClamp={1}>
        {title}
      </Title>
    );
  }, [title]);

  const modelElement = useMemo(() => {
    return (
      <Group
        data-testid='aic-model-group'
        align='center'
        gap='xs'
        className={cx(!!updatedAtString && classes.cardModel, isFullWidth && classes.longCardModel)}
      >
        <ActionIcon size={38} variant='outline' radius='xl' className={classes.modelIcon}>
          <IconBrandOpenai data-testid='aic-model-icon' size={22} color={theme.black} />
        </ActionIcon>
        <Text c='decaGrey.5'>{aiModel}</Text>
      </Group>
    );
  }, [aiModel, isFullWidth, updatedAtString]);

  const updatedAtElement = useMemo(() => {
    return (
      <Text c='decaGrey.5' size='sm' mr={rem(100)}>
        {updatedAtString}
      </Text>
    );
  }, [updatedAtString]);

  return !isFullWidth ? (
    <Card withBorder classNames={classes} data-testid='aic-card' onClick={onClick} shadow='none'>
      <Box>
        <Card.Section inheritPadding py='xs' pb={0}>
          <Group justify='space-between' wrap='nowrap'>
            {icon ?? titleElement}
            {actions && <ActionsMenu {...newActions} />}
          </Group>
        </Card.Section>
        {icon && titleElement}
        {description && (
          <Text ref={descriptionRef} className={classes.description}>
            {description}
          </Text>
        )}
      </Box>
      <Box>
        <Flex justify='space-between' align='center' gap={rem(10)} wrap='wrap'>
          {(aiModel || updatedAt) && (
            <Group align='center' gap={rem(10)}>
              {aiModel && modelElement}
              {updatedAt && updatedAtElement}
            </Group>
          )}
          {status && (
            <Flex justify='flex-end'>
              <AlertStatus message={status.message} status={status.status} />
            </Flex>
          )}
        </Flex>

        {(modelIcons || switchButton) && (
          <Group
            justify='space-between'
            data-testid='aic-model-icons-switch'
            className={classes.modelIconsSwitchGroup}
          >
            {modelIcons && (
              <Group className={classes.modelIcons} data-testid='aic-model-icons'>
                {modelIcons}
              </Group>
            )}
            {switchButton && (
              <Box data-testid='aic-switch-button' onClick={(e) => e.stopPropagation()}>
                {switchButton}
              </Box>
            )}
          </Group>
        )}
      </Box>
    </Card>
  ) : (
    <Card
      withBorder
      classNames={classes}
      data-testid='aic-card-full-width'
      onClick={onClick}
      shadow='none'
    >
      <Flex justify='space-between' align='center' gap={rem(15)} style={{ position: 'relative' }}>
        <Group data-testid='aic-info-group' gap={rem(15)} className={classes.longCardInfo}>
          {icon}
          <Flex direction='column' gap={rem(5)} maw='100%'>
            {titleElement}
            {description && (
              <Text ref={descriptionRef} className={classes.description}>
                {description}
              </Text>
            )}
          </Flex>
        </Group>
        {(aiModel || updatedAt) && (
          <Group
            data-testid='aic-model-updated-group'
            justify={aiModel && updatedAt ? 'space-between' : 'flex-start'}
            align='center'
            gap={rem(100)}
            className={classes.longModelInfo}
          >
            {aiModel && modelElement}
            {updatedAt && updatedAtElement}
          </Group>
        )}
        {status && (
          <AlertStatus
            message={status.message}
            status={status.status}
            dataTestId='aic-status-container'
          />
        )}
        {(modelIcons || switchButton) && (
          <Flex data-testid='aic-model-icons-switch' className={classes.modelIconsSwitchGroupFlex}>
            {modelIcons ? (
              <Group className={classes.modelIcons} data-testid='aic-model-icons' miw={rem(164)}>
                {modelIcons}
              </Group>
            ) : (
              <Box miw={rem(100)} />
            )}
            <Group
              justify='space-between'
              align='center'
              gap={rem(100)}
              style={{ flexWrap: 'nowrap' }}
            >
              {updateTime}
              {switchButton && (
                <Box data-testid='aic-switch-button' onClick={(e) => e.stopPropagation()}>
                  {switchButton}
                </Box>
              )}
            </Group>
          </Flex>
        )}
        {actions && <ActionsMenu {...newActions} />}
      </Flex>
    </Card>
  );
};

export default AICard;
