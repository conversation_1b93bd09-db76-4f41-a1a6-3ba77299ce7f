import { useFlowBuilderContext } from '@/contexts/FlowBuilderContext';
import { FlowDiagram } from '@/helpers/flowDiagram';
import useFlowSchema from '@/hooks/useFlowSchema';
import { useListCredential } from '@/hooks/useListCredential';
import { FlowNodeType } from '@/models/flow';
import { NodeAttributesAPI } from '@/services/api';
import { CredentialAPI } from '@/services/api/credential';
import { ActionIcon, Box, Group, Paper, Transition, rem, useMantineTheme } from '@mantine/core';
import type { ComboboxNode, ICredentialPayload, NodeApiCallCallback } from '@resola-ai/ui';
import { CatalogNodeIcon, useNodeSchemas } from '@resola-ai/ui';
import {
  SchemaEngineWithTolgee,
  type SchemaFormInput,
} from '@resola-ai/ui/components/SchemaEngine';
import { ACTION_FIELD, TRIGGER_FIELD } from '@resola-ai/ui/components/SchemaEngine/SchemaEngine';
import { resolveRefs } from '@resola-ai/ui/utils/schema';
import { IconX } from '@tabler/icons-react';
import get from 'lodash/get';
import set from 'lodash/set';
import { useCallback, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import CatalogModal, { type CatalogModalRef, type NodeNameTypes } from './CatalogModal';
import NodeName from './NodeName';

// Define the Panel Width
const PANEL_WIDTH = rem(440); // Adjust as needed
const PANEL_Z_INDEX = 150;

// --- Side Panel Component ---
interface SidePanelProps {
  opened: boolean;
  onClose: () => void;
}

const getUpdatePath = (fieldName: string) => {
  let updatedPath = '';

  switch (fieldName) {
    case 'action': {
      updatedPath = 'action';
      break;
    }
    case 'trigger': {
      updatedPath = 'trigger';
      break;
    }
    case 'credential': {
      updatedPath = 'settings.credential.id';
      break;
    }
    case 'completedFormStep': {
      updatedPath = 'settings.ui.step';
      break;
    }
    case 'forceCompleted': {
      updatedPath = 'settings.ui.forceCompleted';
      break;
    }
    case 'settings': {
      updatedPath = 'settings';
      break;
    }
    default: {
      updatedPath = `settings.${fieldName}`;
    }
  }
  return updatedPath;
};

const collectOptions = ({
  schemaSection,
  key,
  schemaName,
  group,
  nodeId,
  parentPath = '',
}: {
  schemaSection: any;
  key: string | undefined;
  schemaName: string;
  group: string;
  nodeId: string;
  parentPath?: string;
}): ComboboxNode['options'] => {
  const options: ComboboxNode['options'] = [];

  if (key && schemaSection?.[key]?.data?.properties) {
    const properties = schemaSection[key].data.properties;

    const processProperties = (props: any, currentPath = '') => {
      Object.keys(props).forEach((propKey) => {
        const item = props[propKey];
        if (item) {
          const fullPath = currentPath ? `${currentPath}.${propKey}` : propKey;

          // Add the current property as an option
          options.push({
            value: fullPath,
            label: item.displayName || propKey,
            description: item.description,
            schemaName,
            group,
            nodeId,
          });

          // Handle different property types for nested properties
          if (['array', 'arrayObject'].includes(item.type) && item.items?.properties) {
            // For array types, properties are nested under items.properties
            processProperties(item.items.properties, `${fullPath}[0]`);
          } else if (item.properties && typeof item.properties === 'object') {
            // For object types, properties are directly accessible
            processProperties(item.properties, fullPath);
          }
        }
      });
    };

    processProperties(properties, parentPath);
  }

  return options;
};

function RightFormPanel({ opened, onClose }: SidePanelProps) {
  const theme = useMantineTheme();
  const { workspaceId } = useParams();
  const [activeStep, setActiveStep] = useState<number>(0);
  const catalogModalRef = useRef<CatalogModalRef>(null);

  const {
    flow,
    closeCatalog,
    flowActionHandlers,
    currentSelectNodeId,
    nodeIdToOrderNumber,
    currentSelectNodeData,
    handleUpdateDisplayName,
    handleUpdateVirtualNodeByPath,
  } = useFlowBuilderContext();

  const { schema } = useFlowSchema({ flowNodeType: currentSelectNodeData?.type });
  const { getNodeSchema, getTriggerNodes, getLoopingNodes, getActionNodes } = useNodeSchemas();

  const {
    data,
    isLoading: isCredentialsLoading,
    mutate: mutateCredentials,
  } = useListCredential({
    workspaceId,
    limit: 100,
    provider: schema?.name,
  });
  const credentials = useMemo(() => {
    return data?.data ?? [];
  }, [data, schema?.name]);

  const nodeInfo = useMemo(() => {
    if (!(currentSelectNodeId && flow)) return undefined;
    return {
      ...flowActionHandlers.flowActions.getNodeInfo(flow, currentSelectNodeId),
      isVirtualNode: currentSelectNodeData?.type === FlowNodeType.SubPathNode,
    };
  }, [flow, currentSelectNodeId, flowActionHandlers.flowActions, currentSelectNodeData?.type]);

  const currentNodeData = useMemo(() => {
    if (!currentSelectNodeData) return {};
    const itemObject = nodeInfo?.isTriggerNode ? flow?.triggers : flow?.nodes;

    if (nodeInfo?.isVirtualNode) {
      const parentNodeId = currentSelectNodeData?.parentNodeId ?? '';
      const parentNode = get(itemObject, parentNodeId);
      const paths = get(parentNode, 'settings.paths', []);
      const pathData = paths.find((path) => path.id === currentSelectNodeData?.nodeId);
      return pathData;
    }
    const node = get(itemObject, currentSelectNodeData?.nodeId ?? '');
    return node;
  }, [currentSelectNodeData, flow, nodeInfo]);

  const displayName = useMemo(() => {
    if (currentNodeData?.displayName) {
      return currentNodeData?.displayName;
    }
    return schema?.displayName;
  }, [currentNodeData?.displayName, schema?.displayName]);

  const formValues = useMemo(() => {
    if (!currentNodeData) return {};

    const formData = {};

    const action = get(currentNodeData, 'action');
    const trigger = get(currentNodeData, 'trigger');
    const settings = get(currentNodeData, 'settings', {});
    const credentialId = get(settings, 'credential.id');

    set(formData, 'settings', settings);
    if (credentialId) {
      set(formData, 'settings.credential', credentialId);
    }
    if (action) {
      set(formData, 'action', action);
    }
    if (trigger) {
      set(formData, 'trigger', trigger);
    }
    return formData;
  }, [currentNodeData]) as SchemaFormInput;

  const completedFormStep = useMemo(() => {
    return currentNodeData?.settings?.ui?.step ?? -1;
  }, [currentNodeData?.settings?.ui?.step]);

  const previousNodes = useMemo(() => {
    if (!(currentSelectNodeId && flow)) {
      return [];
    }

    return flowActionHandlers?.flowActions
      .getPreviousNodes(
        flow,
        currentSelectNodeData?.type === FlowNodeType.SubPathNode
          ? (currentSelectNodeData?.parentNodeId ?? '')
          : (currentSelectNodeData?.nodeId ?? '')
      )
      .sort((a, b) => {
        return nodeIdToOrderNumber[a.id] - nodeIdToOrderNumber[b.id];
      })
      .reduce<ComboboxNode[]>((acc, node) => {
        let name = node.name;
        if (name === FlowNodeType.WebhookTrigger) {
          name = FlowNodeType.NewTrigger;
        }

        const nodeSchema = getNodeSchema(name || '');
        if (!nodeSchema) {
          return acc;
        }

        const schema = resolveRefs(nodeSchema, nodeSchema);
        const nodeName = node?.displayName || node?.name || '';
        const orderNumber = nodeIdToOrderNumber?.[node?.id]
          ? `${nodeIdToOrderNumber?.[node?.id]}. `
          : '';

        const trigger = get(node, 'trigger') as string;
        const action = get(node, 'action') as string;
        let options: ComboboxNode['options'] = [];
        const schemaName = schema?.name || '';
        options = [
          ...collectOptions({
            schemaSection: schema.triggers,
            key: trigger,
            schemaName,
            group: 'Triggers',
            nodeId: node?.id,
          }),
          ...collectOptions({
            schemaSection: schema.actions,
            key: action,
            schemaName,
            group: 'Actions',
            nodeId: node?.id,
          }),
        ];

        return acc.concat({
          label: `${orderNumber}${nodeName}`,
          schemaName,
          nodeId: node?.id,
          value: '',
          options,
        });
      }, []);
  }, [
    flow,
    nodeIdToOrderNumber,
    currentSelectNodeId,
    currentSelectNodeData,
    flowActionHandlers?.flowActions,
    getNodeSchema,
  ]);

  const createCredential = useCallback(
    async (credential: ICredentialPayload) => {
      if (!workspaceId) {
        return null;
      }

      try {
        const response = await CredentialAPI.create(workspaceId, credential);
        mutateCredentials();
        return response;
      } catch (error) {
        console.error(error);
        return null;
      }
    },
    [workspaceId, mutateCredentials]
  );

  const handleOpenAppCatalog = useCallback(() => {
    if (!nodeInfo) return;
    if (nodeInfo?.isTriggerNode) {
      catalogModalRef.current?.openWithEnabledOptions(
        getTriggerNodes().filter((item) => item !== 'new-trigger') as NodeNameTypes[]
      );
    } else {
      catalogModalRef.current?.openWithEnabledOptions([
        ...getLoopingNodes(),
        ...getActionNodes(),
      ] as NodeNameTypes[]);
    }
  }, [nodeInfo, getTriggerNodes, getLoopingNodes, getActionNodes]);

  const handleOnSelectCatalog = useCallback(
    (item: { id: string; name: string; icon: string; displayName: string }) => {
      if (!currentSelectNodeId) return;

      setActiveStep(0);
      flowActionHandlers.handleUpdateNode(currentSelectNodeId, {
        name: item.name as FlowNodeType,
        displayName: item.displayName,
        icon: item.icon,
        action: undefined,
        trigger: undefined,
      });
    },
    [currentSelectNodeId, flowActionHandlers.handleUpdateNode]
  );

  // Removed debounce for now: it caused missed updates during rapid multi-field changes and when new field components mounted
  // @TODO: Add field-level debounce for components with frequent user input (e.g., text fields)
  const handleFormChange = useCallback(
    (formValues: SchemaFormInput, fieldName: string) => {
      if (!currentSelectNodeId) return;

      const value = formValues[fieldName];
      const updatedPath = getUpdatePath(fieldName);

      if (nodeInfo?.isVirtualNode) {
        handleUpdateVirtualNodeByPath(currentSelectNodeId, updatedPath, value);
        return;
      }

      const schemaField = `${fieldName}s`;
      const paths = [{ path: updatedPath, value }];

      if (['action', 'trigger'].includes(fieldName)) {
        const currentSettings = currentNodeData?.settings || {};
        const currentActionSchema = schema?.actions?.[value];
        const currentActionPropertyKeys = new Set<string>();
        const schemaSettingsKeys = Object.keys(schema?.settings || {});

        if (
          currentActionSchema &&
          typeof currentActionSchema === 'object' &&
          'properties' in currentActionSchema &&
          currentActionSchema.properties
        ) {
          Object.keys(currentActionSchema.properties).forEach((key) => {
            currentActionPropertyKeys.add(key);
          });
        }
        // Remove properties that don't exist in the current action or schema settings
        Object.keys(currentSettings).forEach((key) => {
          if (
            key !== 'credential' &&
            key !== 'ui' &&
            !schemaSettingsKeys.includes(key) &&
            !currentActionPropertyKeys.has(key)
          ) {
            paths.push({ path: `settings.${key}`, value: undefined });
          }
        });
      }

      if (FlowDiagram.shouldUpdateNodeName(currentNodeData, schema)) {
        const displayName = schema[schemaField]?.[value]?.displayName;
        ['action', 'trigger'].includes(fieldName) &&
          paths.push({ path: 'displayName', value: displayName });
      }

      if (nodeInfo?.isTriggerNode) {
        flowActionHandlers.handleUpdateTriggerByPaths(currentSelectNodeId, paths);
      } else {
        flowActionHandlers.handleUpdateNodeByPaths(currentSelectNodeId, paths);
      }
    },
    [
      schema,
      nodeInfo,
      currentSelectNodeId,
      handleUpdateVirtualNodeByPath,
      flowActionHandlers.handleUpdateNodeByPaths,
      flowActionHandlers.handleUpdateTriggerByPaths,
      currentNodeData,
    ]
  );

  const handleSaveDisplayName = useCallback(
    (displayName: string) => {
      if (!currentSelectNodeId) return;
      handleUpdateDisplayName(currentSelectNodeId, displayName, {
        isSubPathNode: nodeInfo?.isVirtualNode,
        mainPathNodeId: currentSelectNodeData?.parentNodeId ?? '',
      });
    },
    [
      currentSelectNodeId,
      handleUpdateDisplayName,
      nodeInfo?.isVirtualNode,
      currentSelectNodeData?.parentNodeId,
    ]
  );

  // TODO: Implement the actual API call
  const handleNodeApiCall: NodeApiCallCallback = useCallback(
    async (node, attribute, payload) => {
      if (!workspaceId) {
        return {
          data: {},
        };
      }

      const response = await NodeAttributesAPI.getConfig(workspaceId, node, attribute, payload);
      return response.data;
    },
    [workspaceId]
  );

  useLayoutEffect(() => {
    setActiveStep(0);
  }, [currentSelectNodeId]);

  return (
    <>
      <Transition mounted={opened} transition='slide-left' duration={250} timingFunction='ease'>
        {(styles) => (
          <Paper
            shadow='lg' // Add shadow for separation
            radius={'md'} // No radius for edge-to-edge panel
            style={{
              ...styles, // Apply transition styles
              top: `calc(var(--app-shell-header-height) + ${rem(56)})`,
              right: rem(10),
              bottom: rem(10),
              zIndex: PANEL_Z_INDEX,
              display: 'flex',
              position: 'fixed',
              width: PANEL_WIDTH,
              flexDirection: 'column',
              borderLeft: `1px solid ${theme.colors.gray[3]}`,
            }}
          >
            {/* Panel Header */}
            <Group
              justify='space-between'
              align='flex-start'
              p='md'
              style={{
                borderBottom: `1px solid ${theme.colors.gray[3]}`,
              }}
            >
              <Group gap='xs' flex={1} align='flex-start'>
                <ActionIcon size='md' variant='transparent'>
                  <CatalogNodeIcon name={schema?.name} size={24} />
                </ActionIcon>
                <NodeName
                  orderedNumber={currentSelectNodeData?.orderNumber ?? 0}
                  displayName={displayName ?? ''}
                  nodeId={currentSelectNodeId ?? undefined}
                  onSave={handleSaveDisplayName}
                />
              </Group>
              <ActionIcon variant='subtle' color='gray' onClick={onClose} aria-label='Close panel'>
                <IconX size={20} />
              </ActionIcon>
            </Group>

            <Box p='md' sx={{ flexGrow: 1, overflow: 'hidden' }}>
              <SchemaEngineWithTolgee
                schema={schema}
                form={formValues}
                onClose={onClose}
                activeStep={activeStep}
                key={`${currentSelectNodeId}-${schema?.name}`}
                credentials={credentials}
                previousNodes={previousNodes}
                onFormChange={handleFormChange}
                completedStep={completedFormStep}
                onStepChange={setActiveStep}
                createCredential={createCredential}
                onOpenAppCatalog={handleOpenAppCatalog}
                isCredentialsLoading={isCredentialsLoading}
                nodeContext={nodeInfo?.isTriggerNode ? TRIGGER_FIELD : ACTION_FIELD}
                onNodeApiCall={handleNodeApiCall}
              />
            </Box>
          </Paper>
        )}
      </Transition>

      <CatalogModal ref={catalogModalRef} onClose={closeCatalog} onSelect={handleOnSelectCatalog} />
    </>
  );
}

export default RightFormPanel;
