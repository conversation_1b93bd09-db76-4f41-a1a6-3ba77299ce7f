import { Modal, rem } from '@mantine/core';
import { Catalog, useCatalogConfig, useNodeSchemas } from '@resola-ai/ui';
import { forwardRef, useImperativeHandle, useState } from 'react';

type CatalogModalProps = {
  onClose: () => void;
  withCloseButton?: boolean;
  onSelect: (item: any) => void;
};

export type CatalogModalRef = {
  openWithDisabledOptions: (disabledOptions?: NodeNameTypes[]) => void;
  openWithEnabledOptions: (enabledOptions?: NodeNameTypes[], triggerContext?: boolean) => void;
};

const CatalogModal = forwardRef<CatalogModalRef, CatalogModalProps>(
  ({ onClose, onSelect, withCloseButton = false }, ref) => {
    const [opened, setOpened] = useState(false);
    const [isTriggerContext, setIsTriggerContext] = useState(false);
    const [disabledNodes, setDisabledNodes] = useState<NodeNameTypes[]>([]);
    const catalogConfig = useCatalogConfig();
    const { getTriggerNodes, getLoopingNodes, getActionNodes, getControlNodes } = useNodeSchemas();

    useImperativeHandle(ref, () => ({
      openWithDisabledOptions: (disabledOptions?: NodeNameTypes[]) => {
        disabledOptions && setDisabledNodes(disabledOptions);
        setOpened(true);
      },
      openWithEnabledOptions: (enabledOptions?: NodeNameTypes[], triggerContext = false) => {
        enabledOptions &&
          setDisabledNodes(() => {
            const allNodes = Array.from(
              new Set([
                ...getTriggerNodes(),
                ...getLoopingNodes(),
                ...getActionNodes(),
                ...getControlNodes(),
              ] as NodeNameTypes[])
            );
            return allNodes.filter((node) => !enabledOptions.includes(node));
          });
        setIsTriggerContext(triggerContext);
        setOpened(true);
      },
    }));

    const handleClose = () => {
      setOpened(false);
      setDisabledNodes([]);
      setIsTriggerContext(false);
      onClose();
    };

    const handleOnSelectCatalog = (item: any) => {
      onSelect({ ...item, isTriggerContext });
      handleClose();
    };

    return (
      <Modal
        centered
        radius='md'
        opened={opened}
        size={rem(489)}
        padding={'1rem'}
        closeOnClickOutside
        onClose={handleClose}
        withCloseButton={withCloseButton}
      >
        <Catalog
          schema={catalogConfig.schema}
          onSelect={handleOnSelectCatalog}
          disabledNodes={disabledNodes}
        />
      </Modal>
    );
  }
);

CatalogModal.displayName = 'CatalogModal';

export default CatalogModal;

export type NodeNameTypes =
  | 'chatbot'
  | 'code'
  | 'deca-ai-widgets'
  | 'deca-livechat'
  | 'deca-crm'
  | 'deca-kb'
  | 'deca-tables'
  | 'deca-forms'
  | 'deca-ma'
  | 'filter'
  | 'gmail'
  | 'google-calendar'
  | 'google-docs'
  | 'google-drive'
  | 'google-sheets'
  | 'google-slides'
  | 'hubspot'
  | 'http'
  | 'loop'
  | 'manual'
  | 'new-trigger'
  | 'openai'
  | 'deca-pages'
  | 'path'
  | 'schedule'
  | 'slack'
  | 'wait'
  | 'webhook'
  | 'formatter'
  | 'function'
  | 'zendesk-ticket'
  | 'zoom-meetings'
  | 'salesforce'
  | 'line'
  | 'deca-chatwindow';
