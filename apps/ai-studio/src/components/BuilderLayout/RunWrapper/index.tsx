import { useRunContext } from '@/contexts/RunContext';
import { forwardRef } from 'react';
import HistoryRuns, { type HistoryRunsRef } from './HistoryRuns';

interface RunWrapperProps {
  resourceId: string;
  onRunClick?: () => void;
  resourceType?: string;
}

const RunWrapper = forwardRef<HistoryRunsRef, RunWrapperProps>(
  ({ resourceId, onRunClick, resourceType }, ref) => {
    const { runs } = useRunContext();

    if (!resourceId || !runs) return null;

    return (
      <HistoryRuns
        ref={ref}
        data={runs?.data || []}
        onRunClick={onRunClick}
        resourceType={resourceType}
        latestRun={runs?.data?.[0] ?? null}
      />
    );
  }
);

RunWrapper.displayName = 'RunWrapper';

export default RunWrapper;
export type { HistoryRunsRef };
