import RunDetail from '@/components/BuilderLayout/RunWrapper/RunDetail';
import IconEmptySearch from '@/components/Icons/IconEmptySearch';
import { RESOURCE_TYPE } from '@/constants/common';
import { useRunContext } from '@/contexts/RunContext';
import { useRunDetails } from '@/hooks/useRunDetails';
import type { IRun } from '@/models';
import { Box, Flex, ScrollArea, Text, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton } from '@resola-ai/ui';
import { IconArrowLeft, IconClockPlay, IconPlayerPlay } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import ActionPopover from '../../ActionPopover';
import BuilderActionIcon from '../../BuilderActionIcon';
import { RunNodeGroup, RunNodeItem } from '../RunNodeItem';
import RunItem from './RunItem';

type HistoryRunsProps = {
  data: IRun[];
  onRunClick?: () => void;
  resourceType?: string;
  latestRun?: IRun | null;
};

export type HistoryRunsRef = {
  open: () => void;
  close: () => void;
};

const useStyles = createStyles((theme) => ({
  root: {
    marginTop: theme.spacing.sm,
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
  },
  backButton: {
    cursor: 'pointer',
  },
  historyRunsList: {
    borderTop: `1px solid ${theme.colors.decaLight[3]}`,
    paddingTop: rem(14),
  },
}));

const HistoryRuns = forwardRef<HistoryRunsRef, HistoryRunsProps>(
  ({ data, onRunClick, resourceType, latestRun }, ref) => {
    const { t } = useTranslate(['common', 'flow']);
    const { classes } = useStyles();
    const [selectedRun, setSelectedRun] = useState<IRun | null | undefined>(null);
    const [opened, setOpened] = useState(false);
    const [openedViaRef, setOpenedViaRef] = useState(false); // Track if opened via imperative handle

    // Use latestRun when opened via ref and no specific run is selected, otherwise use selectedRun
    const runForDetails = openedViaRef && !selectedRun ? latestRun : selectedRun;

    const { data: runDetails, mutate } = useRunDetails({
      runId: runForDetails?.id,
      workspaceId: runForDetails?.workspaceId,
    });
    const { mutateRuns, registerRunDetailMutator, unregisterRunDetailMutator } = useRunContext();

    useEffect(() => {
      if (runForDetails?.id && mutate) {
        registerRunDetailMutator(runForDetails.id, mutate);
        return () => {
          unregisterRunDetailMutator(runForDetails.id);
        };
      }
    }, [runForDetails?.id, mutate, registerRunDetailMutator, unregisterRunDetailMutator]);

    useImperativeHandle(
      ref,
      () => ({
        open: () => {
          setOpened(true);
          setOpenedViaRef(true);
          setSelectedRun(null); // Don't set selectedRun, let runForDetails use latestRun
        },
        close: () => {
          setOpened(false);
          setOpenedViaRef(false);
          setSelectedRun(null);
        },
      }),
      [latestRun]
    );

    const selectedRunNodes = useMemo(() => {
      return runDetails?.nodes?.filter((node) => node.id && node.id !== '');
    }, [runDetails]);

    const renderList = () => {
      if (!data?.length) {
        return (
          <Flex h='100%' align='center' justify='center' direction='column' flex={1} mt={'-40px'}>
            <IconEmptySearch />
            <Text
              fw={500}
              c='decaGrey.8'
              fz={16}
              mt={rem(16)}
              data-testid='history-runs-empty-title'
            >
              {t('historyRuns.noRuns.title', { ns: 'flow' })}
            </Text>
            <Text
              fw={500}
              c='decaGrey.4'
              fz={14}
              mt={rem(8)}
              mb={rem(13)}
              style={{
                width: '100%',
                maxWidth: '300px',
                textAlign: 'center',
                lineHeight: rem(22),
                whiteSpace: 'pre-line',
              }}
            >
              {t('historyRuns.noRuns.description', { ns: 'flow' })}
            </Text>
            {onRunClick && (
              <DecaButton
                size='sm'
                onClick={onRunClick}
                variant='secondary'
                data-testid='builder-layout-run-button'
                leftSection={<IconPlayerPlay size={16} />}
              >
                {t('builderLayout.run')}
              </DecaButton>
            )}
          </Flex>
        );
      }
      return (
        <ScrollArea h={`calc(100vh - ${rem(240)})`} type='never'>
          <Box data-testid='history-runs-list' className={classes.historyRunsList}>
            {data.map((item) => (
              <RunItem key={item.id} run={item} onSelect={setSelectedRun} />
            ))}
          </Box>
        </ScrollArea>
      );
    };

    const handleClose = (shouldClose?: boolean) => {
      mutateRuns();
      setSelectedRun(null);
      if (shouldClose) {
        setOpened(false);
        setOpenedViaRef(false);
      }
    };

    const handleToggle = () => {
      setOpened((prev) => {
        if (!prev) {
          // Opening via target button - show list, not details
          setOpenedViaRef(false);
          setSelectedRun(null);
        }
        return !prev;
      });
    };

    return (
      <ActionPopover
        opened={opened}
        onToggle={handleToggle}
        width={rem(700)}
        styles={{
          dropdown: {
            padding: `${rem(24)} ${rem(16)} !important`,
          },
        }}
        title={
          selectedRun || (openedViaRef && latestRun) ? (
            <Flex
              gap={rem(8)}
              align='center'
              className={classes.backButton}
              data-testid='history-run-back-button'
              onClick={() => handleClose(false)}
            >
              <IconArrowLeft size={24} />
              <Text fw={700} fz='lg' c='decaGrey.6'>
                {runForDetails?.name || runForDetails?.resourceName}
              </Text>
            </Flex>
          ) : (
            <Title fw={700} fz='lg' c='decaGrey.6' order={6}>
              {t('historyRuns.title')}
            </Title>
          )
        }
        target={
          <BuilderActionIcon>
            <IconClockPlay size={16} />
          </BuilderActionIcon>
        }
        onClose={() => handleClose(true)}
      >
        <Flex className={classes.root} data-testid='history-runs'>
          {(selectedRun || (openedViaRef && latestRun)) &&
            (resourceType === RESOURCE_TYPE.FLOW ? (
              <ScrollArea h={`calc(100vh - ${rem(240)})`} type='never'>
                <RunNodeGroup data-testid='history-runs-node-list'>
                  {selectedRunNodes?.map((node, index) => (
                    <RunNodeItem key={node.id} value={node.id} node={node} order={index + 1} />
                  ))}
                </RunNodeGroup>
              </ScrollArea>
            ) : (
              runForDetails && <RunDetail run={runForDetails} />
            ))}
          {!selectedRun && !openedViaRef && renderList()}
        </Flex>
      </ActionPopover>
    );
  }
);

HistoryRuns.displayName = 'HistoryRuns';

export default HistoryRuns;
