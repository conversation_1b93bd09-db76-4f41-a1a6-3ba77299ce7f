import { screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { createRef } from 'react';

import { HISTORY_RUN_STATUS, RESOURCE_TYPE } from '@/constants/common';
import { FlowBuilderProvider } from '@/contexts/FlowBuilderContext';
import { mockHistoryRuns } from '@/mockdata/historyRun';
import { renderWithRouterAppContext } from '@/utils/test';
import userEvent from '@testing-library/user-event';
import HistoryRuns, { type HistoryRunsRef } from './index';

// Mock the hooks
vi.mock('@/hooks/useRunDetails', () => ({
  useRunDetails: vi.fn(),
}));

vi.mock('@/hooks/useFlowBuilder', () => ({
  useFlowBuilder: vi.fn(),
}));

vi.mock('@/hooks/useFlowSchema', () => ({
  default: vi.fn(),
}));

vi.mock('@/contexts/RunContext', () => ({
  useRunContext: vi.fn(),
}));

describe('HistoryRuns', () => {
  const renderWithFlowBuilder = (ui: React.ReactNode) => {
    return renderWithRouterAppContext(<FlowBuilderProvider>{ui}</FlowBuilderProvider>);
  };

  beforeEach(async () => {
    const { useRunDetails } = await import('@/hooks/useRunDetails');
    const { useFlowBuilder } = await import('@/hooks/useFlowBuilder');
    const useFlowSchema = await import('@/hooks/useFlowSchema');
    const { useRunContext } = await import('@/contexts/RunContext');

    // Default mock for useRunDetails
    vi.mocked(useRunDetails).mockReturnValue({
      data: undefined,
      error: undefined,
      isLoading: false,
      mutate: vi.fn(),
      isValidating: false,
    });

    // Default mock for useFlowBuilder
    vi.mocked(useFlowBuilder).mockReturnValue({
      flow: null,
      flowActionHandlers: {
        flowActions: {
          getNodeInfo: vi.fn(),
        },
      },
    } as any);

    // Default mock for useFlowSchema
    vi.mocked(useFlowSchema.default).mockReturnValue({
      schema: {
        triggers: {},
        actions: {},
      },
    });

    // Default mock for useRunContext
    vi.mocked(useRunContext).mockReturnValue({
      mutateRuns: vi.fn(),
      registerRunDetailMutator: vi.fn(),
      unregisterRunDetailMutator: vi.fn(),
    } as any);
  });

  it('renders history dropdown correctly', async () => {
    const ref = createRef<HistoryRunsRef>();
    renderWithRouterAppContext(
      <HistoryRuns ref={ref} data={mockHistoryRuns} latestRun={mockHistoryRuns[0]} />
    );

    // Use ref.open() to trigger selection behavior
    ref.current?.open();
    await waitFor(() => {
      expect(screen.getByTestId('history-runs')).toBeInTheDocument();
      // Component automatically selects first run via latestRun prop, so we see back button instead of list
      expect(screen.getByTestId('history-run-back-button')).toBeInTheDocument();
    });
  });

  it('show back button when a run is selected', async () => {
    const ref = createRef<HistoryRunsRef>();
    renderWithRouterAppContext(
      <HistoryRuns ref={ref} data={mockHistoryRuns} latestRun={mockHistoryRuns[0]} />
    );

    // Use ref.open() to trigger selection behavior
    ref.current?.open();

    // Component automatically selects first run via latestRun prop, so back button should be visible
    await waitFor(() => {
      expect(screen.getByTestId('history-runs')).toBeInTheDocument();
      expect(screen.getByTestId('history-run-back-button')).toBeInTheDocument();
    });
  });

  it('show history detail when a run is selected', async () => {
    const { useRunDetails } = await import('@/hooks/useRunDetails');

    // Mock useRunDetails to return the expected data structure with valid nodes
    const mockRunDetails = {
      ...mockHistoryRuns[0],
      nodes: [
        {
          name: 'openai',
          id: 'node-1',
          displayName: 'OpenAI Node',
          status: HISTORY_RUN_STATUS.SUCCESS,
          input: { prompt: 'test input' },
          output: { response: 'test output' },
          logs: [],
          error: {},
          startedAt: Date.now() - 5000,
          completedAt: Date.now(),
          duration: 5000,
        },
        {
          name: 'websearch',
          id: 'node-2',
          displayName: 'Web Search Node',
          status: HISTORY_RUN_STATUS.SUCCESS,
          input: { query: 'test query' },
          output: { results: [] },
          logs: [],
          error: {},
          startedAt: Date.now() - 3000,
          completedAt: Date.now() - 2000,
          duration: 1000,
        },
      ],
    };

    vi.mocked(useRunDetails).mockReturnValue({
      data: mockRunDetails,
      error: undefined,
      isLoading: false,
      mutate: vi.fn(),
      isValidating: false,
    });

    const ref = createRef<HistoryRunsRef>();
    renderWithFlowBuilder(
      <HistoryRuns
        ref={ref}
        data={mockHistoryRuns}
        resourceType={RESOURCE_TYPE.FLOW}
        latestRun={mockHistoryRuns[0]}
      />
    );

    // Use ref.open() to trigger selection behavior
    ref.current?.open();

    // Component automatically selects first run via latestRun prop, so back button should be visible
    await waitFor(() => {
      expect(screen.getByTestId('history-runs')).toBeInTheDocument();
      expect(screen.getByTestId('history-run-back-button')).toBeInTheDocument();
    });

    // Wait for the node list to appear
    await waitFor(() => {
      expect(screen.getByTestId('history-runs-node-list')).toBeInTheDocument();
    });

    // Wait for the run node items to appear
    await waitFor(() => {
      const runNodeItems = screen.getAllByTestId('run-node-item');
      expect(runNodeItems.length).toBeGreaterThan(0);
    });
  });

  it('should render empty state when no history runs', async () => {
    renderWithRouterAppContext(<HistoryRuns data={[]} />);
    const user = userEvent.setup();

    const actionPopover = screen.getByTestId('builder-action-popover');
    await user.click(actionPopover);
    await waitFor(() => {
      expect(screen.getByTestId('history-runs')).toBeInTheDocument();
      expect(screen.getByTestId('history-runs-empty-title')).toBeInTheDocument();
    });
  });

  it('should render empty state with onRunClick', async () => {
    const onRunClick = vi.fn();
    renderWithRouterAppContext(<HistoryRuns data={[]} onRunClick={onRunClick} />);

    const user = userEvent.setup();

    const actionPopover = screen.getByTestId('builder-action-popover');
    await user.click(actionPopover);
    await waitFor(() => {
      expect(screen.getByTestId('history-runs-empty-title')).toBeInTheDocument();
      expect(screen.getByTestId('builder-layout-run-button')).toBeInTheDocument();
    });
  });

  it('should open and close via ref methods', async () => {
    const ref = createRef<HistoryRunsRef>();
    renderWithRouterAppContext(
      <HistoryRuns ref={ref} data={mockHistoryRuns} latestRun={mockHistoryRuns[0]} />
    );

    expect(screen.queryByTestId('history-runs')).not.toBeInTheDocument();

    ref.current?.open();
    await waitFor(() => {
      expect(screen.getByTestId('history-runs')).toBeInTheDocument();
    });

    ref.current?.close();
    await waitFor(() => {
      expect(screen.queryByTestId('history-runs')).not.toBeInTheDocument();
    });
  });

  it('should reset selected run when closing via ref', async () => {
    const ref = createRef<HistoryRunsRef>();
    renderWithRouterAppContext(
      <HistoryRuns ref={ref} data={mockHistoryRuns} latestRun={mockHistoryRuns[0]} />
    );

    ref.current?.open();
    await waitFor(() => {
      expect(screen.getByTestId('history-runs')).toBeInTheDocument();
      expect(screen.getByTestId('history-run-back-button')).toBeInTheDocument();
    });

    ref.current?.close();

    // Re-render without latestRun to test the reset behavior
    renderWithRouterAppContext(<HistoryRuns ref={ref} data={mockHistoryRuns} latestRun={null} />);

    ref.current?.open();
    await waitFor(() => {
      expect(screen.getByTestId('history-runs-list')).toBeInTheDocument();
      expect(screen.queryByTestId('history-run-back-button')).not.toBeInTheDocument();
    });
  });

  // Additional comprehensive tests for better coverage
  it('should handle latestRun prop correctly', async () => {
    const ref = createRef<HistoryRunsRef>();
    const latestRun = mockHistoryRuns[1]; // Use second item as latest
    renderWithRouterAppContext(
      <HistoryRuns ref={ref} data={mockHistoryRuns} latestRun={latestRun} />
    );

    ref.current?.open();
    await waitFor(() => {
      expect(screen.getByTestId('history-runs')).toBeInTheDocument();
      expect(screen.getByTestId('history-run-back-button')).toBeInTheDocument();
      // Check that the correct run name is displayed
      expect(
        screen.getByText(latestRun.name || latestRun.resourceName || 'Unnamed Run')
      ).toBeInTheDocument();
    });
  });

  it('should handle manual run selection from list', async () => {
    renderWithRouterAppContext(<HistoryRuns data={mockHistoryRuns} latestRun={null} />);
    const user = userEvent.setup();

    const actionPopover = screen.getByTestId('builder-action-popover');
    await user.click(actionPopover);

    await waitFor(() => {
      expect(screen.getByTestId('history-runs-list')).toBeInTheDocument();
    });

    // Click on a run item
    const runItems = screen.getAllByTestId('run-item');
    await user.click(runItems[1]);

    await waitFor(() => {
      expect(screen.getByTestId('history-run-back-button')).toBeInTheDocument();
    });
  });

  it('should render RunDetail for non-flow resource types', async () => {
    const { useRunDetails } = await import('@/hooks/useRunDetails');

    const mockRunDetails = {
      ...mockHistoryRuns[0],
      nodes: [],
    };

    vi.mocked(useRunDetails).mockReturnValue({
      data: mockRunDetails,
      error: undefined,
      isLoading: false,
      mutate: vi.fn(),
      isValidating: false,
    });

    renderWithRouterAppContext(
      <HistoryRuns
        data={mockHistoryRuns}
        latestRun={mockHistoryRuns[0]}
        resourceType='PROMPT' // Non-flow type
      />
    );
    const user = userEvent.setup();

    const actionPopover = screen.getByTestId('builder-action-popover');
    await user.click(actionPopover);

    await waitFor(() => {
      expect(screen.getByTestId('history-runs')).toBeInTheDocument();
      // Should not render node list for non-flow types
      expect(screen.queryByTestId('history-runs-node-list')).not.toBeInTheDocument();
    });
  });

  it('should handle close button click', async () => {
    renderWithRouterAppContext(
      <HistoryRuns data={mockHistoryRuns} latestRun={mockHistoryRuns[0]} />
    );
    const user = userEvent.setup();

    const actionPopover = screen.getByTestId('builder-action-popover');
    await user.click(actionPopover);

    await waitFor(() => {
      expect(screen.getByTestId('history-runs')).toBeInTheDocument();
    });

    // Find and click the close button (X button)
    const closeButtons = screen.getAllByRole('button');
    const closeButton = closeButtons.find((btn) =>
      btn.querySelector('svg')?.classList.contains('tabler-icon-x')
    );

    if (closeButton) {
      await user.click(closeButton);
      await waitFor(() => {
        expect(screen.queryByTestId('history-runs')).not.toBeInTheDocument();
      });
    }
  });

  it('should handle useImperativeHandle dependency changes', async () => {
    // Test with different latestRun props in separate renders
    const ref1 = createRef<HistoryRunsRef>();
    const { unmount } = renderWithRouterAppContext(
      <HistoryRuns ref={ref1} data={mockHistoryRuns} latestRun={mockHistoryRuns[0]} />
    );

    // Open with first run
    ref1.current?.open();
    await waitFor(() => {
      expect(screen.getByTestId('history-runs')).toBeInTheDocument();
      expect(screen.getByText(mockHistoryRuns[0].name)).toBeInTheDocument();
    });

    unmount();

    // Re-render with different latestRun
    const ref2 = createRef<HistoryRunsRef>();
    renderWithRouterAppContext(
      <HistoryRuns ref={ref2} data={mockHistoryRuns} latestRun={mockHistoryRuns[2]} />
    );

    ref2.current?.open();
    await waitFor(() => {
      expect(screen.getByTestId('history-runs')).toBeInTheDocument();
      expect(screen.getByText(mockHistoryRuns[2].name)).toBeInTheDocument();
    });
  });

  it('should register and unregister run detail mutators correctly', async () => {
    const { useRunContext } = await import('@/contexts/RunContext');
    const mockRegisterRunDetailMutator = vi.fn();
    const mockUnregisterRunDetailMutator = vi.fn();

    vi.mocked(useRunContext).mockReturnValue({
      mutateRuns: vi.fn(),
      registerRunDetailMutator: mockRegisterRunDetailMutator,
      unregisterRunDetailMutator: mockUnregisterRunDetailMutator,
    } as any);

    const ref = createRef<HistoryRunsRef>();
    const { unmount } = renderWithRouterAppContext(
      <HistoryRuns ref={ref} data={mockHistoryRuns} latestRun={mockHistoryRuns[0]} />
    );

    // Open to trigger selectedRun setting
    ref.current?.open();

    // Should register mutator when selectedRun is set
    await waitFor(() => {
      expect(mockRegisterRunDetailMutator).toHaveBeenCalledWith(
        mockHistoryRuns[0].id,
        expect.any(Function)
      );
    });

    // Should unregister when component unmounts
    unmount();
    await waitFor(() => {
      expect(mockUnregisterRunDetailMutator).toHaveBeenCalledWith(mockHistoryRuns[0].id);
    });
  });

  it('should filter selectedRunNodes correctly', async () => {
    const { useRunDetails } = await import('@/hooks/useRunDetails');

    const mockRunDetails = {
      ...mockHistoryRuns[0],
      nodes: [
        {
          name: 'openai',
          id: 'node-1',
          displayName: 'OpenAI Node',
          status: HISTORY_RUN_STATUS.SUCCESS,
          input: {},
          output: {},
          logs: [],
          error: {},
          startedAt: Date.now(),
          completedAt: Date.now(),
          duration: 1000,
        },
        {
          name: 'websearch',
          id: '', // Empty ID should be filtered out
          displayName: 'Web Search Node',
          status: HISTORY_RUN_STATUS.SUCCESS,
          input: {},
          output: {},
          logs: [],
          error: {},
          startedAt: Date.now(),
          completedAt: Date.now(),
          duration: 1000,
        },
        {
          name: 'another',
          id: 'node-3',
          displayName: 'Another Node',
          status: HISTORY_RUN_STATUS.SUCCESS,
          input: {},
          output: {},
          logs: [],
          error: {},
          startedAt: Date.now(),
          completedAt: Date.now(),
          duration: 1000,
        },
      ],
    };

    vi.mocked(useRunDetails).mockReturnValue({
      data: mockRunDetails,
      error: undefined,
      isLoading: false,
      mutate: vi.fn(),
      isValidating: false,
    });

    const ref = createRef<HistoryRunsRef>();
    renderWithFlowBuilder(
      <HistoryRuns
        ref={ref}
        data={mockHistoryRuns}
        latestRun={mockHistoryRuns[0]}
        resourceType={RESOURCE_TYPE.FLOW}
      />
    );

    // Use ref.open() to trigger selection behavior
    ref.current?.open();

    await waitFor(() => {
      expect(screen.getByTestId('history-runs-node-list')).toBeInTheDocument();
      // Should only render nodes with valid IDs (2 out of 3)
      const runNodeItems = screen.getAllByTestId('run-node-item');
      expect(runNodeItems).toHaveLength(2);
    });
  });
});
