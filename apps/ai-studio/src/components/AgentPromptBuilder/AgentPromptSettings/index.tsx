import {
  ActionIcon,
  Box,
  Flex,
  Group,
  Popover,
  Select,
  Stack,
  Text,
  Title,
  rem,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaSelect } from '@resola-ai/ui';
import { IconAdjustmentsHorizontal } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import isUndefined from 'lodash/isUndefined';
import { useEffect, useState } from 'react';

import { type MAPPING_SLIDER_OPTIONS, RESPONSE_FORMAT_OPTIONS } from '@/constants/agent';
import type {
  IAddonAgents,
  IAgent,
  IAgentModel,
  IAgentSettings,
  IPromptModel,
  IPromptSettings,
} from '@/models';
import type { ModelSettings } from '@/types/model';
import { getModelOptions } from '@/utils/agent';
import AgentSlider from './AgentSlider';
import AgentTools from './AgentTools';
import MultiAgents from './MultiAgents';

const useStyles = createStyles((theme) => {
  const shadow = `0px 0px 4px 0px ${theme.colors.decaBlue[5]}`;

  return {
    root: {
      padding: theme.spacing.md,
    },
    settings: {
      width: '100%',
      backgroundColor: theme.colors.decaLight[0],
      border: `1px solid ${theme.colors.decaLight[3]}`,
      borderRadius: rem(8),
    },
    container: {
      padding: `${rem(8)} ${rem(16)}`,
    },
    selectInput: {
      minHeight: rem(30),
      height: rem(30),
    },
    options: {
      borderRadius: rem(6),
      boxShadow: shadow,
    },
    tagsInput: {
      width: '70%',
    },
  };
});

interface AgentPromptSettingsProps {
  models: IAgentModel[] | IPromptModel[];
  settings: IPromptSettings | IAgentSettings | null;
  isAgent?: boolean;
  onSettingsChange?: (settings: ModelSettings) => void;
  agents?: IAgent[];
  readOnly?: boolean;
  className?: string;
}

const AgentPromptSettings: React.FC<AgentPromptSettingsProps> = ({
  models,
  settings,
  isAgent = false,
  onSettingsChange,
  agents,
  readOnly = false,
  className,
}) => {
  const { classes, cx } = useStyles();
  const { t } = useTranslate('common');
  const [currentSettings, setCurrentSettings] = useState<ModelSettings>(
    getModelOptions(settings, isAgent)
  );
  const [currentEndSettings, setCurrentEndSettings] = useState<ModelSettings>(
    getModelOptions(settings, isAgent)
  );

  useEffect(() => {
    setCurrentSettings(getModelOptions(settings, isAgent));
    setCurrentEndSettings(getModelOptions(settings, isAgent));
  }, [settings]);

  const onChangeEndSettings = (newSettings: ModelSettings) => {
    setCurrentEndSettings(newSettings);
    setCurrentSettings((prev) => ({ ...prev, ...newSettings }));
    onSettingsChange?.(newSettings);
  };

  const [collaborations, setCollaborations] = useState<{
    addonAgents: IAddonAgents[];
    mode: string;
  }>({
    addonAgents: [],
    mode: 'supervisor',
  });

  const getGroupText = (label: string, value?: string | number) => {
    return !isUndefined(value) ? (
      <Group gap={rem(4)}>
        <Text size='sm' c='decaGrey.9'>
          {label}
        </Text>
        <Text size='sm' c='decaGreen.6'>
          {value}
        </Text>
      </Group>
    ) : null;
  };

  const getOptionText = (label: string, value?: string | number) => {
    return (
      <Flex justify='space-between' align='center'>
        <Text fw={500} size='md' c='decaGrey.9'>
          {label}
        </Text>
        <Text fw={500} size='md' c='decaGrey.9'>
          {value}
        </Text>
      </Flex>
    );
  };

  const getSliderOption = (name: keyof typeof MAPPING_SLIDER_OPTIONS) => {
    return (
      <>
        {getOptionText(t(`settings.model.settings.${name}`), currentSettings[name])}
        <AgentSlider
          value={currentSettings[name] ?? 0}
          name={name}
          onChange={(value) => setCurrentSettings((prev) => ({ ...prev, [name]: value }))}
          onChangeEnd={(value) => onChangeEndSettings({ ...currentEndSettings, [name]: value })}
        />
      </>
    );
  };

  const handleSetCollaborations = (collaborations: {
    addonAgents: IAddonAgents[];
    mode: string;
  }) => {
    setCollaborations(collaborations);
  };

  return (
    <Box className={cx(classes.root, className)} data-testid='agent-prompt-settings'>
      <Stack gap='md'>
        <Text size='xl' fw={500} c='decaGrey.9'>
          {t('settings.title')}
        </Text>
        <Box className={classes.settings}>
          <Flex className={classes.container} justify='space-between'>
            <Flex gap={rem(24)}>
              <Text size='md' fw={500} c='decaGrey.3' miw={rem(80)}>
                {t('settings.model.label')}
              </Text>
              <Stack gap={rem(10)} miw={rem(240)}>
                {readOnly ? (
                  <Text fz={rem(12)} c='decaGrey.9' fw={500}>
                    {currentSettings.model}
                  </Text>
                ) : (
                  <Select
                    maw={rem(240)}
                    classNames={{ input: classes.selectInput }}
                    data={models?.map((model) => ({
                      label: model.name,
                      value: model.modelId,
                    }))}
                    placeholder={t('settings.model.placeholder')}
                    value={currentSettings.model}
                    onChange={(value) =>
                      value && onChangeEndSettings({ ...currentEndSettings, model: value })
                    }
                    data-testid='model-select'
                  />
                )}
                <Flex gap={rem(16)}>
                  {getGroupText(t('settings.model.info.temperature'), currentSettings.temperature)}
                  {getGroupText(t('settings.model.info.tokens'), currentSettings.maxTokens)}
                </Flex>
                <Flex gap={rem(16)}>
                  {getGroupText(
                    t('settings.model.info.textFormat'),
                    currentSettings?.responseFormat ?? t('settings.model.info.textFormatValue')
                  )}
                  {getGroupText(t('settings.model.info.topP'), currentSettings.topP)}
                </Flex>
                <Flex gap={rem(16)}>
                  {getGroupText(
                    t('settings.model.info.frequencyPenalty'),
                    currentSettings.frequencyPenalty
                  )}
                  {getGroupText(
                    t('settings.model.info.presencePenalty'),
                    currentSettings.presencePenalty
                  )}
                </Flex>
              </Stack>
            </Flex>
            {!readOnly && (
              <Popover position='bottom-end' closeOnClickOutside>
                <Popover.Target>
                  <ActionIcon
                    size={22}
                    variant='transparent'
                    c='decaGrey.6'
                    data-testid='adjustment-icon'
                  >
                    <IconAdjustmentsHorizontal />
                  </ActionIcon>
                </Popover.Target>
                <Popover.Dropdown className={classes.options}>
                  <Stack gap={rem(12)} px={rem(4)} py={rem(8)} w={rem(326)}>
                    <Title order={6} c='decaGrey.9' data-testid='model-name'>
                      {currentSettings.model}
                    </Title>
                    {getSliderOption('temperature')}
                    {getSliderOption('maxTokens')}
                    {getOptionText(t('settings.model.settings.responseFormat'))}
                    <DecaSelect
                      data-testid='response-format-select'
                      data={RESPONSE_FORMAT_OPTIONS}
                      value={currentSettings.responseFormat}
                      onChange={(value) =>
                        value &&
                        onChangeEndSettings({
                          ...currentEndSettings,
                          responseFormat: value,
                        })
                      }
                      defaultValue={RESPONSE_FORMAT_OPTIONS[0].value}
                    />
                    {getSliderOption('topP')}
                    {getSliderOption('frequencyPenalty')}
                    {getSliderOption('presencePenalty')}
                  </Stack>
                </Popover.Dropdown>
              </Popover>
            )}
          </Flex>
          <AgentTools tools={[]} onToolChange={() => {}} readOnly={readOnly} />
          {isAgent && (
            <MultiAgents
              agents={agents ?? []}
              collaborations={collaborations}
              setCollaborations={handleSetCollaborations}
            />
          )}
        </Box>
      </Stack>
    </Box>
  );
};

export default AgentPromptSettings;
