import { rem } from '@mantine/core';

import { SimpleGrid } from '@mantine/core';

interface GridLayoutProps {
  children: React.ReactNode;
  isFullWidth?: boolean;
  className?: string;
}

const GridLayout = ({ children, isFullWidth = false, className }: GridLayoutProps) => {
  return (
    <SimpleGrid
      data-testid='grid-layout'
      w='100%'
      type='container'
      cols={isFullWidth ? 1 : { '576px': 1, '768px': 2, '1200px': 3, '1400px': 4, '1700px': 5 }}
      spacing={{ base: 'sm', xl: rem(24), sm: rem(16) }}
      className={className}
    >
      {children}
    </SimpleGrid>
  );
};

export default GridLayout;
