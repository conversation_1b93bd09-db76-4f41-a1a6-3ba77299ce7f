import { axiosService, executeRequest } from '@resola-ai/services-shared';
import { createParser } from 'eventsource-parser';

import { AGENT_API_PROD_URL, AGENT_API_URL } from '@/constants/agent';
import { DEFAULT_PER_PAGE } from '@/constants/common';
import type {
  IAgent,
  IAgentInput,
  IAgentModel,
  IAgentOutput,
  IStudioSuccessListResponse,
} from '@/models';
import { agentService } from '../agent/service';

export const AgentAPI = {
  getList: async (
    wsId: string,
    filters?: { name?: string },
    cursor = '',
    limit = DEFAULT_PER_PAGE
  ) => {
    const params = new URLSearchParams({
      cursor,
      limit: limit.toString(),
      filter:
        filters && Object.keys(filters).length > 0
          ? JSON.stringify({
              name: { $regex: filters.name?.trim(), $options: 'i' },
            })
          : '',
    });

    return executeRequest<IStudioSuccessListResponse<IAgent>>(() =>
      axiosService.instance.get(`${wsId}/agents?${params.toString()}`)
    );
  },

  getById: async (wsId: string, agentId: string) => {
    return executeRequest<IAgent>(() => axiosService.instance.get(`${wsId}/agents/${agentId}`));
  },

  create: async (wsId: string, agent: Partial<IAgent>) => {
    return executeRequest<IAgent>(() => axiosService.instance.post(`${wsId}/agents`, agent));
  },

  update: async (wsId: string, agentId: string, agent: Partial<IAgent>) => {
    return executeRequest<IAgent>(() =>
      axiosService.instance.put(`${wsId}/agents/${agentId}`, agent)
    );
  },

  delete: async (wsId: string, agentId: string) => {
    return executeRequest<IAgent>(() => axiosService.instance.delete(`${wsId}/agents/${agentId}`));
  },

  execute: async (wsId: string, agentId: string, payload: IAgentInput, sessionId?: string) => {
    const sessionIdHeader = sessionId ? { 'X-DECA-Agent-Session-Id': sessionId } : undefined;

    try {
      const response = await agentService.instance.post<IAgentOutput>(
        `${wsId}/agents/${agentId}/run`,
        payload,
        {
          headers: {
            ...sessionIdHeader,
          },
        }
      );

      return {
        data: response.data,
        sessionId: response.headers['X-DECA-Agent-Session-Id'],
      };
    } catch (error) {
      console.error(error);
      throw error;
    }
  },

  executeStream: async (
    wsId: string,
    agentId: string,
    payload: IAgentInput,
    onChunk: (chunk: string) => void,
    sessionId?: string
  ) => {
    const sessionIdHeader = sessionId ? { 'X-DECA-Agent-Session-Id': sessionId } : undefined;

    try {
      const response = await fetch(
        `${['prod', 'production'].includes(import.meta.env.VITE_ENV) ? AGENT_API_PROD_URL : AGENT_API_URL}${wsId}/agents/${agentId}/run`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Accept: 'text/event-stream',
            Authorization: `Bearer ${axiosService.getAccessToken()}`,
            ...sessionIdHeader,
          },
          body: JSON.stringify(payload),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData?.detail ?? 'Failed to send message');
      }

      const responseSessionId = response.headers.get('X-DECA-Agent-Session-Id');

      const parser = createParser({
        onEvent: (event) => {
          if (event.data === '[DONE]') {
            return;
          }

          try {
            onChunk(event.data);
          } catch (error) {
            console.error('Error parsing streaming response:', error);
          }
        },
      });

      // Process the streaming response
      const reader = response?.body?.getReader();
      const decoder = new TextDecoder();

      let isDone = false;
      while (!isDone && reader) {
        const { value, done } = await reader.read();
        isDone = done;
        const chunk = decoder.decode(value);
        parser.feed(chunk);
      }

      return {
        data: response,
        sessionId: responseSessionId,
      };
    } catch (error) {
      console.error(error);
      throw error;
    }
  },

  getModels: async (wsId: string) => {
    return executeRequest<{ data: IAgentModel[]; totalCount: number }>(() =>
      agentService.instance.get(`${wsId}/models`)
    );
  },

  generatePrompt: async (wsId: string, input: string) => {
    return executeRequest<{ instructionMarkdown: string }>(() =>
      agentService.instance.post(`${wsId}/instructions/generate`, {
        taskDescription: input,
        detaillevel: 'detailed',
        agentType: 'planning',
        outputFormat: 'markdown',
        context: {},
      })
    );
  },

  getListAgentRun: async (wsId: string, agentId: string, page: number, pageSize: number) => {
    return executeRequest<any>(() =>
      agentService.instance.get(`${wsId}/agents/${agentId}/run?page=${page}&pageSize=${pageSize}`)
    );
  },

  export: async (wsId: string, agentId: string) => {
    return executeRequest<IAgent>(() =>
      axiosService.instance.get(`${wsId}/agents/${agentId}/export`)
    );
  },

  import: async (wsId: string, file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    return executeRequest<IAgent>(() =>
      axiosService.instance.post(`${wsId}/agents/import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
    );
  },
};
