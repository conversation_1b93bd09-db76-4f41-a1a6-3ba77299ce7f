{"action": {"add": "追加", "cancel": "キャンセル", "remove": "削除"}, "agent": {"processing": "「リクエストを処理中です、少々お待ちください！」"}, "builderLayout": {"apply": "適用", "cancel": "キャンセル", "imCompleteFlow": "フローが完了していません。トリガーとアクションのすべての必須フィールドを追加してください。", "publish": "公開", "published": "公開成功", "run": "実行", "save": "保存", "saveAndPublish": "保存して公開", "saveAndPublishDescription": "変更を保存して公開してもよろしいですか？", "saveAndRun": "保存して実行", "saveAndRunDescription": "変更を保存して実行してもよろしいですか？", "savedSuccessfully": "保存が完了しました。"}, "button": {"add": "追加", "cancel": "キャンセル", "close": "閉じる", "confirm": "確認", "delete": "削除", "duplicate": "複製", "edit": "編集", "export": "エクスポート", "reconnect": "再接続", "remove": "削除", "save": "保存", "saveAsTemplate": "テンプレートとして保存", "test": "テスト"}, "combobox": {"dataPoints": "データポイント", "functions": "関数", "less": "閉じる", "more": "もっと見る", "placeholder": "入力するか / を使用して変数を挿入"}, "error": {"pattern": "値は{pattern}のパターンに一致する必要があります", "required": "必須項目です"}, "flow": {"duplicated": "フローを複製しました。", "duplicateFailed": "フローの複製に失敗しました。もう一度お試しください。"}, "function": {"all": "すべて", "Applies str to pattern regex and returns array of match objects": "strをパターン正規表現に適用し、マッチオブジェクトの配列を返します", "array": "配列", "boolean": "ブール値", "Casts the arg parameter to a number using JSONata casting rules": "argパラメータをJSONataキャストルールを使用して数値にキャストします", "Casts the arg parameter to a string using JSONata casting rules": "argパラメータをJSONataキャストルールを使用して文字列にキャストします", "Casts the argument to a Boolean using JSONata casting rules": "引数をJSONataキャストルールを使用してブール値にキャストします", "Converts an ASCII string to a base 64 representation": "ASCII文字列をbase64表現に変換します", "Converts a timestamp string to milliseconds since the Unix Epoch": "タイムスタンプ文字列をUnixエポックからのミリ秒に変換します", "Converts base 64 encoded bytes to a string using UTF-8": "base64エンコードされたバイトをUTF-8を使用して文字列に変換します", "Converts milliseconds since Unix Epoch to a formatted string representation": "Unixエポックからのミリ秒をフォーマットされた文字列表現に変換します", "date": "日付", "Decodes a URL component previously created by encodeUrlComponent": "encodeUrlComponentで作成されたURLコンポーネントをデコードします", "Decodes a URL previously created by encodeUrl": "encodeUrlで作成されたURLをデコードします", "docs": "docs.jsonata.org", "Encodes a URL by replacing special characters with escape sequences": "特殊文字をエスケープシーケンスに置き換えてURLをエンコードします", "Encodes a URL component by replacing special characters with escape sequences": "特殊文字をエスケープシーケンスに置き換えてURLコンポーネントをエンコードします", "example": "例", "Finds occurrences of pattern in str and replaces them with replacement": "str内でパターンの出現を検索し、replacementで置換します", "Formats a number to a decimal representation as specified by picture string": "数値をpicture文字列で指定された10進表現にフォーマットします", "Formats a number to an integer in the specified number base": "数値を指定された数値基数の整数にフォーマットします", "Formats a number to an integer representation as specified by picture string": "数値をpicture文字列で指定された整数表現にフォーマットします", "Generates a UTC timestamp in ISO 8601 compatible format": "ISO 8601互換形式のUTCタイムスタンプを生成します", "higher-order": "高階関数", "Joins an array of strings into a single concatenated string": "文字列の配列を単一の連結文字列に結合します", "Merges an array of objects into a single object containing all key/value pairs": "オブジェクトの配列をすべてのキー/値のペアを含む単一のオブジェクトにマージします", "Normalizes and trims all whitespace characters in str": "str内のすべての空白文字を正規化してトリムします", "number": "数値", "object": "オブジェクト", "Parses a string to an integer using the format specified by picture string": "picture文字列で指定されたフォーマットを使用して文字列を整数にパースします", "Returns a convolved (zipped) array containing grouped arrays of values from multiple arrays": "複数の配列から値のグループ化された配列を含む畳み込み（ジップ）配列を返します", "Returns a copy of str with extra padding to reach the specified width": "指定された幅に達するように追加のパディングを施したstrのコピーを返します", "Returns an aggregated value by applying function successively to each value in array": "配列内の各値に関数を順次適用して集約値を返します", "Returns an array containing all values from array parameter, but in reverse order": "配列パラメータのすべての値を含む配列を返しますが、逆順になっています", "Returns an array containing all values from array parameter, but shuffled into random order": "配列パラメータのすべての値を含む配列を返しますが、ランダムな順序にシャッフルされています", "Returns an array containing all values from array parameter, but sorted into order": "配列パラメータのすべての値を含む配列を返しますが、順序にソートされています", "Returns an array containing all values from array parameter, but with duplicates removed": "配列パラメータのすべての値を含む配列を返しますが、重複が削除されています", "Returns an array containing only values that satisfy the function predicate": "関数述語を満たす値のみを含む配列を返します", "Returns an array containing the keys in the object": "オブジェクト内のキーを含む配列を返します", "Returns an array containing the results of applying function to each value in array": "配列内の各値に関数を適用した結果を含む配列を返します", "Returns an array containing values from array1 followed by values from array2": "array1の値の後にarray2の値が続く配列を返します", "Returns an array containing values returned by function when applied to each key/value pair": "各キー/値のペアに適用されたときに関数によって返される値を含む配列を返します", "Returns an object containing only key/value pairs that satisfy the predicate function": "述語関数を満たすキー/値のペアのみを含むオブジェクトを返します", "Returns a pseudo random number between 0 and 1": "0と1の間の疑似乱数を返します", "Returns a string containing characters from str starting at position start": "位置startから始まるstrの文字を含む文字列を返します", "Returns a string with all characters converted to lowercase": "すべての文字を小文字に変換した文字列を返します", "Returns a string with all characters converted to uppercase": "すべての文字を大文字に変換した文字列を返します", "Returns Boolean NOT on the argument (arg is first cast to boolean)": "引数に対してブールNOTを返します（argは最初にブール値にキャストされます）", "Returns the absolute value of the number parameter": "数値パラメータの絶対値を返します", "Returns the arithmetic sum of an array of numbers": "数値の配列の算術和を返します", "Returns the maximum number in an array of numbers": "数値の配列の最大値を返します", "Returns the mean value of an array of numbers": "数値の配列の平均値を返します", "Returns the minimum number in an array of numbers": "数値の配列の最小値を返します", "Returns the number of characters in the string str": "文字列strの文字数を返します", "Returns the number of items in the array parameter": "配列パラメータの項目数を返します", "Returns the number of milliseconds since the Unix Epoch (1 January, 1970 UTC)": "Unixエポック（1970年1月1日UTC）からのミリ秒数を返します", "Returns the one and only one value that satisfies the function predicate": "関数述語を満たす唯一の値を返します", "Returns the square root of the number parameter": "数値パラメータの平方根を返します", "Returns the substring after the first occurrence of chars in str": "str内でcharsが最初に出現した後の部分文字列を返します", "Returns the substring before the first occurrence of chars in str": "str内でcharsが最初に出現する前の部分文字列を返します", "Returns the value associated with key in object": "オブジェクト内のキーに関連付けられた値を返します", "Returns the value of base raised to the power of exponent": "baseをexponent乗した値を返します", "Returns the value of number rounded down to the nearest integer": "数値を最も近い整数に切り下げた値を返します", "Returns the value of number rounded to the specified decimal places": "数値を指定された小数点以下の桁数に丸めた値を返します", "Returns the value of number rounded up to the nearest integer": "数値を最も近い整数に切り上げた値を返します", "Returns true if str is matched by pattern, otherwise false": "strがパターンにマッチする場合はtrue、そうでなければfalseを返します", "Returns true if the arg expression evaluates to a value, false if it does not match anything": "arg式が値に評価される場合はtrue、何にもマッチしない場合はfalseを返します", "Splits an object containing key/value pairs into an array of objects": "キー/値のペアを含むオブジェクトをオブジェクトの配列に分割します", "Splits str into an array of substrings using separator": "separatorを使用してstrを部分文字列の配列に分割します", "string": "文字列", "syntax": "構文"}, "historyRuns": {"collapseAll": "すべて折りたたむ", "collapsibleView": "折りたたみ表示", "copied": "コピーしました", "copy": "コピー", "error": "エラー", "expandAll": "すべて展開", "failed": "失敗", "input": "入力", "logs": "ログ", "noError": "エラーは発生しませんでした", "noInput": "入力が提供されていません。", "noLogs": "現在、ログはありません。", "noOutput": "現在、出力はありません。", "output": "出力", "running": "実行中", "success": "成功", "title": "最近の実行"}, "instructions": {"generate": "生成", "placeholder": "エージェントの指示を入力してください", "title": "指示"}, "key": "キー", "messageBuilder": {"copy": "コピー", "replay": "繰り返し", "warning": "プロンプトを実行する前に、ユーザー、アシスタント、またはAIメッセージを追加してください。"}, "notifications": {"importedSuccessfully": "インポートに成功しました"}, "pageHeader": {"buttonMenuActions": {"fromScratch": "ゼロから作成", "fromTemplate": "テンプレートから作成", "importFromFile": "ファイルからインポート"}}, "settings": {"model": {"info": {"frequencyPenalty": "頻度ペナルティ:", "presencePenalty": "存在ペナルティ:", "temperature": "温度:", "textFormat": "応答形式:", "textFormatValue": "text", "tokens": "トークン数:", "toolChoice": "tool_choice:", "toolChoiceValue": "web_search_preview", "topP": "Top P:"}, "label": "モデル", "multiAgents": {"collaboration": {"addCollaborator": "新しい協力者を追加", "agent": "協力者エージェント", "agentPlaceholder": "協力者エージェントを選択してください", "collaborator": {"description": "マルチエージェント協力により、このエージェントは他のエージェントにタスクを割り当てることができます。監督エージェントとして、それはその協力者エージェントからの応答を収集して整理します。", "title": "協力者"}, "description": "マルチエージェント協力により、このエージェントは他のエージェントにタスクを割り当てることができます。監督エージェントとして、それはその協力者エージェントからの応答を収集して整理します。", "editCollaborator": "協力者を編集", "handoffInstruction": "協力者の手渡し指示", "handoffInstructionDescription": "マルチエージェント協力でのエージェントの役割を説明します。明確で具体的な例を使用することをお勧めします。また、スタイルとトーンを好みに応じて定義することもできます。エージェントを参照する際は、その協力者の名前を使用してください。", "mode": {"description": "このエージェントが応答を管理する方法を選択します:", "options": {"routing": {"description": "エージェントは応答を適切な協力者エージェントにルーティングします。", "title": "ルーティング"}, "supervisor": {"description": "エージェントは協力者エージェントからの応答を収集して、最終的な応答を生成します。", "title": "監督"}}, "title": "協力モード"}, "name": "協力者名", "nameDescription": "協力者の名前を入力してください", "noAgent": "まだ協力者エージェントが存在しません。まず作成してください。", "title": "マルチエージェント協力"}, "mode": {"description": "このエージェントが応答を管理する方法を選択します:", "title": "協力モード"}, "title": "エージェント"}, "placeholder": "モデルを選択", "settings": {"frequencyPenalty": "頻度ペナルティ", "maxTokens": "トークン数", "presencePenalty": "存在ペナルティ", "responseFormat": "応答形式", "stopSequences": "停止シーケンス", "stopSequencesPlaceholder": "シーケンスを入力してTabキーを押します", "temperature": "温度", "topP": "Top P"}, "tools": {"all": "すべて", "builtIn": "組み込みツール", "city": "都市", "configureWebsearch": "Web検索ツールの設定", "country": "国", "function": "関数", "large": "大", "medium": "中", "noBuiltInTools": "組み込みツールはありません", "region": "地域", "searchContextSize": "検索コンテキストサイズ", "searchPlaceholder": "検索ツールと機能...", "selectCountry": "国を選択...", "selectTimezone": "タイムゾーンを選択...", "small": "小", "timezone": "タイムゾーン", "title": "ツール", "tool": "ツール", "websearch": "Web検索"}}, "title": "設定"}, "systemMessageSettings": {"description": "プロンプトを実行する前に、ユーザー、アシスタント、またはAIメッセージを追加してください。", "message": {"ai": "AIメッセージ", "assistant": "アシスタントメッセージ", "placeholder": "システムメッセージを入力してください", "user": "ユーザーメッセージ"}, "title": "メッセージ"}, "template": {"saved": "プロンプトテンプレートを保存しました。", "saveFailed": "テンプレートの保存に失敗しました。もう一度お試しください。"}, "thisFeatureisComingSoon": "この 機能 は 近日中に 利用 可能になります", "unsavedChangesModal": {"cancel": "キャンセル", "confirm": "保存して続行", "description": "続行しますか？未保存の変更は失われます。必ず変更を保存してください", "leaveWithoutSave": "保存せずに続行", "saveAndQuit": "保存して続行", "title": "未保存の変更"}, "value": "値", "versionControl": {"addNewVersion": "新しいバージョンを追加", "cancel": "キャンセル", "createVersion": "バージョン履歴に追加", "descriptionPlaceholder": "変更を説明", "editVersion": "バージョン情報編集", "name": "バージョン名", "noVersionsDescription": "保存されたバージョンはありません。まずはプロンプトを保存してください。", "noVersionsTitle": "バージョンはありません", "rename": "名前変更", "restore": "復元", "restoreSuccess": "“{name}”を復元しました。", "restoreThisVersion": "このバージョンを復元", "restoreVersionDescription": "このバージョンを復元しますか？現在の変更は上書きされます。\nまた、現在の変更を保存してから復元することもできます。", "saveAndRestore": "保存して復元", "saveAndRestoreSuccess": "現在のデータが新しいバージョンとして保存されました。“{name}”を復元しました。", "searchVersion": "バージョン検索", "title": "バージョン", "titlePlaceholder": "タイトル"}}