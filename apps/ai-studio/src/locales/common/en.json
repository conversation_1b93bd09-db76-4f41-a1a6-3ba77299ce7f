{"action": {"add": "Add", "cancel": "Cancel", "remove": "Remove"}, "agent": {"processing": "I'm processing your request, just a moment!"}, "builderLayout": {"apply": "Apply", "cancel": "Cancel", "imCompleteFlow": "Your flow is not completed. Please add all required fields in the triggers and actions.", "publish": "Publish", "published": "Published successfully", "run": "Run", "save": "Save", "saveAndPublish": "Save and publish", "saveAndPublishDescription": "Are you sure you want to save and publish your changes?", "saveAndRun": "Save and run", "saveAndRunDescription": "Are you sure you want to save and run your changes?", "savedSuccessfully": "Saved successfully."}, "button": {"add": "Add", "cancel": "Cancel", "close": "Close", "confirm": "Confirm", "delete": "Delete", "duplicate": "Duplicate", "edit": "Edit", "export": "Export", "reconnect": "Reconnect", "remove": "Remove", "save": "Save", "saveAsTemplate": "Save as template", "test": "Test"}, "combobox": {"dataPoints": "Data Points", "functions": "Functions", "less": "less", "more": "more", "placeholder": "Type or use / to insert variable"}, "error": {"pattern": "The value must match the pattern: {pattern}.", "required": "This field is required."}, "flow": {"duplicated": "Flow duplicated successfully.", "duplicateFailed": "Couldn’t duplicate the Flow. Please try again."}, "function": {"all": "All", "Applies str to pattern regex and returns array of match objects": "Applies str to pattern regex and returns array of match objects", "array": "Array", "boolean": "Boolean", "Casts the arg parameter to a number using JSONata casting rules": "Casts the arg parameter to a number using JSONata casting rules", "Casts the arg parameter to a string using JSONata casting rules": "Casts the arg parameter to a string using JSONata casting rules", "Casts the argument to a Boolean using JSONata casting rules": "Casts the argument to a Boolean using JSONata casting rules", "Converts an ASCII string to a base 64 representation": "Converts an ASCII string to a base 64 representation", "Converts a timestamp string to milliseconds since the Unix Epoch": "Converts a timestamp string to milliseconds since the Unix Epoch", "Converts base 64 encoded bytes to a string using UTF-8": "Converts base 64 encoded bytes to a string using UTF-8", "Converts milliseconds since Unix Epoch to a formatted string representation": "Converts milliseconds since Unix Epoch to a formatted string representation", "date": "Date", "Decodes a URL component previously created by encodeUrlComponent": "Decodes a URL component previously created by encodeUrlComponent", "Decodes a URL previously created by encodeUrl": "Decodes a URL previously created by encodeUrl", "docs": "docs.jsonata.org", "Encodes a URL by replacing special characters with escape sequences": "Encodes a URL by replacing special characters with escape sequences", "Encodes a URL component by replacing special characters with escape sequences": "Encodes a URL component by replacing special characters with escape sequences", "example": "Example", "Finds occurrences of pattern in str and replaces them with replacement": "Finds occurrences of pattern in str and replaces them with replacement", "Formats a number to a decimal representation as specified by picture string": "Formats a number to a decimal representation as specified by picture string", "Formats a number to an integer in the specified number base": "Formats a number to an integer in the specified number base", "Formats a number to an integer representation as specified by picture string": "Formats a number to an integer representation as specified by picture string", "Generates a UTC timestamp in ISO 8601 compatible format": "Generates a UTC timestamp in ISO 8601 compatible format", "higher-order": "Higher-Order", "Joins an array of strings into a single concatenated string": "Joins an array of strings into a single concatenated string", "Merges an array of objects into a single object containing all key/value pairs": "Merges an array of objects into a single object containing all key/value pairs", "Normalizes and trims all whitespace characters in str": "Normalizes and trims all whitespace characters in str", "number": "Number", "object": "Object", "Parses a string to an integer using the format specified by picture string": "Parses a string to an integer using the format specified by picture string", "Returns a convolved (zipped) array containing grouped arrays of values from multiple arrays": "Returns a convolved (zipped) array containing grouped arrays of values from multiple arrays", "Returns a copy of str with extra padding to reach the specified width": "Returns a copy of str with extra padding to reach the specified width", "Returns an aggregated value by applying function successively to each value in array": "Returns an aggregated value by applying function successively to each value in array", "Returns an array containing all values from array parameter, but in reverse order": "Returns an array containing all values from array parameter, but in reverse order", "Returns an array containing all values from array parameter, but shuffled into random order": "Returns an array containing all values from array parameter, but shuffled into random order", "Returns an array containing all values from array parameter, but sorted into order": "Returns an array containing all values from array parameter, but sorted into order", "Returns an array containing all values from array parameter, but with duplicates removed": "Returns an array containing all values from array parameter, but with duplicates removed", "Returns an array containing only values that satisfy the function predicate": "Returns an array containing only values that satisfy the function predicate", "Returns an array containing the keys in the object": "Returns an array containing the keys in the object", "Returns an array containing the results of applying function to each value in array": "Returns an array containing the results of applying function to each value in array", "Returns an array containing values from array1 followed by values from array2": "Returns an array containing values from array1 followed by values from array2", "Returns an array containing values returned by function when applied to each key/value pair": "Returns an array containing values returned by function when applied to each key/value pair", "Returns an object containing only key/value pairs that satisfy the predicate function": "Returns an object containing only key/value pairs that satisfy the predicate function", "Returns a pseudo random number between 0 and 1": "Returns a pseudo random number between 0 and 1", "Returns a string containing characters from str starting at position start": "Returns a string containing characters from str starting at position start", "Returns a string with all characters converted to lowercase": "Returns a string with all characters converted to lowercase", "Returns a string with all characters converted to uppercase": "Returns a string with all characters converted to uppercase", "Returns Boolean NOT on the argument (arg is first cast to boolean)": "Returns Boolean NOT on the argument (arg is first cast to boolean)", "Returns the absolute value of the number parameter": "Returns the absolute value of the number parameter", "Returns the arithmetic sum of an array of numbers": "Returns the arithmetic sum of an array of numbers", "Returns the maximum number in an array of numbers": "Returns the maximum number in an array of numbers", "Returns the mean value of an array of numbers": "Returns the mean value of an array of numbers", "Returns the minimum number in an array of numbers": "Returns the minimum number in an array of numbers", "Returns the number of characters in the string str": "Returns the number of characters in the string str", "Returns the number of items in the array parameter": "Returns the number of items in the array parameter", "Returns the number of milliseconds since the Unix Epoch (1 January, 1970 UTC)": "Returns the number of milliseconds since the Unix Epoch (1 January, 1970 UTC)", "Returns the one and only one value that satisfies the function predicate": "Returns the one and only one value that satisfies the function predicate", "Returns the square root of the number parameter": "Returns the square root of the number parameter", "Returns the substring after the first occurrence of chars in str": "Returns the substring after the first occurrence of chars in str", "Returns the substring before the first occurrence of chars in str": "Returns the substring before the first occurrence of chars in str", "Returns the value associated with key in object": "Returns the value associated with key in object", "Returns the value of base raised to the power of exponent": "Returns the value of base raised to the power of exponent", "Returns the value of number rounded down to the nearest integer": "Returns the value of number rounded down to the nearest integer", "Returns the value of number rounded to the specified decimal places": "Returns the value of number rounded to the specified decimal places", "Returns the value of number rounded up to the nearest integer": "Returns the value of number rounded up to the nearest integer", "Returns true if str is matched by pattern, otherwise false": "Returns true if str is matched by pattern, otherwise false", "Returns true if the arg expression evaluates to a value, false if it does not match anything": "Returns true if the arg expression evaluates to a value, false if it does not match anything", "Splits an object containing key/value pairs into an array of objects": "Splits an object containing key/value pairs into an array of objects", "Splits str into an array of substrings using separator": "Splits str into an array of substrings using separator", "string": "String", "syntax": "Syntax"}, "historyRuns": {"collapseAll": "Collapse All", "collapsibleView": "Collapsible view", "copied": "<PERSON>pied", "copy": "Copy", "error": "Error", "expandAll": "Expand All", "failed": "Failed", "input": "Input", "logs": "Logs", "noError": "No error occurred", "noInput": "No input was provided.", "noLogs": "No logs are available at this time.", "noOutput": "No output was generated.", "output": "Output", "running": "Running", "success": "Success", "title": "Recent Runs"}, "instructions": {"generate": "Generate", "placeholder": "Write your instruction of agent here", "title": "Instructions"}, "key": "Key", "messageBuilder": {"copy": "Copy", "replay": "Replay", "warning": "Please add a user, assistant, or AI message before running the prompt."}, "notifications": {"importedSuccessfully": "Imported successfully"}, "pageHeader": {"buttonMenuActions": {"fromScratch": "From Scratch", "fromTemplate": "From Template", "importFromFile": "Import from file"}}, "settings": {"model": {"info": {"frequencyPenalty": "Frequency Penalty:", "presencePenalty": "Presence Penalty:", "temperature": "Temperature:", "textFormat": "Response Format:", "textFormatValue": "text", "tokens": "Tokens:", "toolChoice": "tool_choice:", "toolChoiceValue": "web_search_preview", "topP": "Top P:"}, "label": "Model", "multiAgents": {"collaboration": {"addCollaborator": "Add a new Collaborator", "agent": "Collaborator agent", "agentPlaceholder": "Please select an agent", "collaborator": {"description": "An agent in multi-agent collaboration that gathers information from other agents and delivers the final response.", "title": "Collaborators"}, "description": "Multi-agent collaboration allows this agent assign tasks to other agents. As a supervisor agent, it gathers and organizes responses from its collaborator agents.", "editCollaborator": "Edit Collaborator", "handoffInstruction": "Collaborator handoff instruction", "handoffInstructionDescription": "Describe the agent’s role in multi-agent collaboration. We recommend using clear and specific examples. You can also define the preferred style and tone. When referring to the agent, use its collaborator name.", "mode": {"description": "Select how this agent should manage responses:", "options": {"routing": {"description": "The agent routes information to the most suitable collaborator agent, which then provides the final response.", "title": "Routing"}, "supervisor": {"description": "The agent gathers inputs from collaborator agents and delivers a final response.", "title": "Supervisor"}}, "title": "Collaboration Mode"}, "name": "Collaborator name", "nameDescription": "A secondary name used only in collaboration instructions.", "noAgent": "There is no collaborator agent yet, please create one first.", "title": "Multi-agent collaboration"}, "mode": {"description": "Select how this agent should manage responses:", "title": "Collaboration Mode"}, "title": "Agents"}, "placeholder": "Select a model", "settings": {"frequencyPenalty": "Frequency Penalty", "maxTokens": "Tokens", "presencePenalty": "Presence Penalty", "responseFormat": "Response Format", "stopSequences": "Stop sequences", "stopSequencesPlaceholder": "Enter sequences and press tab", "temperature": "Temperature", "topP": "Top P"}, "tools": {"all": "All", "builtIn": "Build-in tools", "city": "City", "configureWebsearch": "Configure Web Search tool", "country": "Country", "function": "Functions", "large": "Large", "medium": "Medium", "noBuiltInTools": "No tools available", "region": "Region", "searchContextSize": "Search Context Size", "searchPlaceholder": "Search tools and functions...", "selectCountry": "Select a country...", "selectTimezone": "Select a timezone...", "small": "Small", "timezone": "Timezone", "title": "Tools", "tool": "Tools", "websearch": "Websearch"}}, "title": "Settings"}, "systemMessageSettings": {"description": "Please add a user, assistant, or AI message before running the prompt.", "message": {"ai": "AI message", "assistant": "Assistant message", "placeholder": "Write your system message here", "user": "User message"}, "title": "Messages"}, "template": {"saved": "Template saved successfully.", "saveFailed": "Couldn't save the template. Please try again."}, "thisFeatureisComingSoon": "This feature is coming soon", "unsavedChangesModal": {"cancel": "Cancel", "confirm": "Save and Quit", "description": "Are you sure you want to continue? Your Unsaved changes will be lost. Make sure you save your changes", "leaveWithoutSave": "Leave without saving", "saveAndQuit": "Save and Quit", "title": "Unsaved Changes"}, "value": "Value", "versionControl": {"addNewVersion": "Add new version", "cancel": "Cancel", "createVersion": "Add to version history", "descriptionPlaceholder": "Describe what changed", "editVersion": "Edit version information", "name": "Version Name", "noVersionsDescription": "Once you save a version, it will appear here for reference or restoration.", "noVersionsTitle": "No Versions Yet", "rename": "<PERSON><PERSON>", "restore": "Rest<PERSON>", "restoreSuccess": "Restored “{name}” successfully. ", "restoreThisVersion": "Restore this version", "restoreVersionDescription": "Are you sure you want to restore this version? Your current changes will be replaced. \nYou can also save the current changes before restoring.", "saveAndRestore": "Save and restore", "saveAndRestoreSuccess": "Current data saved as a new version. Restored “{name}” successfully. ", "searchVersion": "Search version", "title": "Version", "titlePlaceholder": "Title"}}