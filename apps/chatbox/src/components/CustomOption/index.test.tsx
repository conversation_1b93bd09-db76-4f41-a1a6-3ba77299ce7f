import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeAll } from 'vitest';
import { MantineProvider } from '@mantine/core';
import CustomOption from './index';

beforeAll(() => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });

  ['ResizeObserver', 'IntersectionObserver'].forEach((name) => {
    Object.defineProperty(window, name, {
      writable: true,
      value: vi.fn().mockImplementation(() => ({
        disconnect: vi.fn(),
        observe: vi.fn(),
        unobserve: vi.fn(),
      })),
    });
  });
});

vi.mock('@mantine/emotion', () => ({
  createStyles: (styleFunction: any) => {
    const mockTheme = {
      colors: {
        silverFox: Array(10).fill('#666666'),
        decaNavy: Array(10).fill('#003366'),
      },
    };

    try {
      const styles = styleFunction(mockTheme);
      (global as any).lastExecutedStyles = styles;
    } catch (e) {
      console.warn('Style function execution failed:', e);
    }

    return () => ({
      classes: {
        root: 'root-class',
        active: 'active-class',
        check: 'check-class',
        title: 'title-class',
        content: 'content-class',
        iconCheck: 'icon-check-class',
      },
      cx: (...args: any[]) => {
        const classNames: string[] = [];
        args.forEach((arg) => {
          if (typeof arg === 'string') {
            classNames.push(arg);
          } else if (typeof arg === 'object' && arg !== null) {
            Object.entries(arg).forEach(([key, value]) => {
              if (value) classNames.push(key);
            });
          }
        });
        return classNames.join(' ').trim();
      },
    });
  },
}));

vi.mock('@tabler/icons-react', () => ({
  IconCheck: ({ className, ...props }: any) => (
    <div data-testid='icon-check' className={className} {...props}>
      ✓
    </div>
  ),
}));

const renderComponent = (props = {}) =>
  render(
    <MantineProvider>
      <CustomOption {...props} />
    </MantineProvider>
  );

describe('CustomOption', () => {
  describe('CSS Styles Execution', () => {
    it('should execute CSS styles creation during component initialization', () => {
      renderComponent({ title: 'Style test' });

      // Verify the style function was executed during component initialization
      expect((global as any).lastExecutedStyles).toBeDefined();
      expect(typeof (global as any).lastExecutedStyles).toBe('object');

      // The styles should contain all expected style objects
      const styles = (global as any).lastExecutedStyles;
      expect(styles).toHaveProperty('root');
      expect(styles).toHaveProperty('active');
      expect(styles).toHaveProperty('check');
      expect(styles).toHaveProperty('title');
      expect(styles).toHaveProperty('content');
      expect(styles).toHaveProperty('iconCheck');
    });

    it('should apply CSS classes from useStyles', () => {
      const { container } = renderComponent({ title: 'CSS test', active: true });

      const rootElement = container.querySelector('.root-class');
      expect(rootElement).toHaveClass('root-class');
      expect(rootElement).toHaveClass('active-class');

      expect(container.querySelector('.check-class')).toBeInTheDocument();
      expect(container.querySelector('.content-class')).toBeInTheDocument();
      expect(container.querySelector('.title-class')).toBeInTheDocument();
      expect(container.querySelector('.icon-check-class')).toBeInTheDocument();
    });
  });

  describe('Basic Rendering and Structure', () => {
    it('should render with default props and correct DOM structure', () => {
      const { container } = renderComponent();
      const rootElement = container.querySelector('.root-class');

      expect(rootElement).toBeInTheDocument();
      expect(rootElement).not.toHaveClass('active-class');
      expect(container.querySelector('.content-class')).toBeInTheDocument();
      expect(screen.queryByRole('heading')).not.toBeInTheDocument();
      expect(screen.queryByTestId('icon-check')).not.toBeInTheDocument();
    });

    it('should render complete active structure with all elements', () => {
      const { container } = renderComponent({
        title: 'Test Title',
        active: true,
        children: <span data-testid='child'>Content</span>,
      });

      const rootElement = container.querySelector('.root-class');
      const children = Array.from(rootElement!.children);

      expect(rootElement).toHaveClass('root-class active-class');
      expect(children).toHaveLength(3); // check icon, content, title
      expect(children[0]).toHaveClass('check-class');
      expect(children[1]).toHaveClass('content-class');
      expect(children[2]).toHaveClass('title-class');
      expect(screen.getByTestId('icon-check')).toBeInTheDocument();
      expect(screen.getByRole('heading', { level: 3 })).toHaveTextContent('Test Title');
      expect(screen.getByTestId('child')).toBeInTheDocument();
    });
  });
});
