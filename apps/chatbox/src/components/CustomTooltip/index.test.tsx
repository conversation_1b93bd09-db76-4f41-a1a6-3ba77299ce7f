import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeAll } from 'vitest';
import { MantineProvider } from '@mantine/core';
import CustomTooltip from './index';

beforeAll(() => {
  const mockMatchMedia = vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  }));

  Object.defineProperty(window, 'matchMedia', { writable: true, value: mockMatchMedia });
  Object.defineProperty(global, 'matchMedia', { writable: true, value: mockMatchMedia });

  ['ResizeObserver', 'IntersectionObserver'].forEach((name) => {
    Object.defineProperty(window, name, {
      writable: true,
      value: vi.fn().mockImplementation(() => ({
        disconnect: vi.fn(),
        observe: vi.fn(),
        unobserve: vi.fn(),
        root: null,
        rootMargin: '',
        thresholds: [],
      })),
    });
  });
});

vi.mock('@mantine/emotion', () => ({
  createStyles: (styleFunction: (theme: any) => any) => {
    const mockTheme = {
      colors: {
        midnightSerenade: { 9: '#1a1b1e' },
        pervenche: { 0: '#f8f9ff' },
      },
      radius: { md: '8px' },
      shadows: { md: '0 4px 6px rgba(0, 0, 0, 0.1)' },
      spacing: { sm: '8px', md: '16px' },
    };

    const styles = styleFunction(mockTheme);

    return () => ({
      classes: { tooltipWrapper: 'tooltip-wrapper-class' },
    });
  },
}));

const renderComponent = (props: any = {}) => {
  const defaultProps = { children: <span data-testid='child'>Child</span> };
  return render(
    <MantineProvider>
      <CustomTooltip {...defaultProps} {...props} />
    </MantineProvider>
  );
};

const hasTooltipWrapper = (container: HTMLElement) =>
  !!container.querySelector('.tooltip-wrapper-class');

describe('CustomTooltip', () => {
  describe('CSS Styles Execution', () => {
    it('should execute createStyles function when rendering tooltip', () => {
      renderComponent({ label: 'Test tooltip' });

      const child = screen.getByTestId('child');
      expect(child).toBeInTheDocument();
    });

    it('should execute styles even when not showing tooltip', () => {
      renderComponent({ label: '', maxLengthToShow: 5 });

      const child = screen.getByTestId('child');
      expect(child).toBeInTheDocument();
    });
  });

  describe('Basic Rendering', () => {
    it('should render children without tooltip by default', () => {
      renderComponent();
      expect(screen.getByTestId('child')).toBeInTheDocument();
    });

    it('should render children without tooltip when no label', () => {
      const { container } = renderComponent({ label: undefined });
      expect(screen.getByTestId('child')).toBeInTheDocument();
      expect(hasTooltipWrapper(container)).toBe(false);
    });

    it('should render children in Box wrapper', () => {
      const { container } = renderComponent();
      expect(container.querySelector('div')).toBeInTheDocument();
      expect(screen.getByTestId('child')).toBeInTheDocument();
    });
  });
});
