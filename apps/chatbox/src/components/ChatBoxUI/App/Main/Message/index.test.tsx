import React from 'react';
import { screen, act, waitFor, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import Message from './index';
import { useChatBoxUIContext } from '@/components/ChatBoxUI/context/chatBoxUIContext';
import { useMedia } from '@/contexts/MediaContext';
import ApiService from '@/components/ChatBoxUI/services/api';
import { renderWithAllProviders } from '@/utils/unitTest';
import { LIVECHAT_DISPLAY_EVENT_TYPES } from '@/components/ChatBoxUI/constants';
import type { NavigatorType } from '@/components/ChatBoxUI/models';
import type { MediaContextType } from '@/contexts/MediaContext';

// Mock dependencies
vi.mock('@/components/ChatBoxUI/context/chatBoxUIContext');
vi.mock('@/contexts/MediaContext');
vi.mock('@/components/ChatBoxUI/services/api');
vi.mock('react-device-detect', () => ({ isMobileOnly: false }));

// Mock child components
vi.mock('./ConversationHeader', () => ({
  default: () => <div data-testid='conversation-header'>Conversation Header</div>,
}));
vi.mock('./ChatHeader', () => ({
  default: () => <div data-testid='chat-header'>Chat Header</div>,
}));
vi.mock('./ChatState', () => ({ default: () => <div data-testid='chat-state'>Chat State</div> }));
vi.mock('./EmptyState', () => ({
  default: () => <div data-testid='empty-state'>Empty State</div>,
}));
vi.mock('./ConversationState', () => ({
  default: () => <div data-testid='conversation-state'>Conversation State</div>,
}));
vi.mock('../MessageInput', () => ({
  default: () => <input data-testid='message_input_textbox' role='textbox' />,
}));
vi.mock('./TriggerFlowButton', () => ({
  default: () => <div data-testid='trigger-flow-button'>Trigger Flow Button</div>,
}));

// Mock CSS styles
vi.mock('@mantine/emotion', async (importOriginal) => {
  const actual = await importOriginal<typeof import('@mantine/emotion')>();
  return {
    ...actual,
    createStyles: vi.fn((styleFunction) => {
      const mockTheme = {};
      const mockProps = {
        chatbox: { width: 300, height: 500, borderRadius: 4 },
        customChatboxSize: { customHeight: 400, customMaxHeight: 600 },
      };
      styleFunction(mockTheme, mockProps);
      return () => ({
        classes: { container: 'mocked-container-class' },
        cx: vi.fn((...args) => args.filter(Boolean).join(' ')),
      });
    }),
    emotionTransform: vi.fn(),
  };
});

// Mock file drop and validation
let mockDropHandler: ((files: File[]) => void) | null = null;
let mockAddFilesToInputHandler: ((files: File[]) => void) | null = null;
let mockFilesRejectHandler: ((rejectedFiles: File[]) => void) | null = null;

vi.mock('@resola-ai/ui', () => ({
  MessageFileDropzoneProvider: ({
    children,
    onFilesDrop,
    onAddFilesToInput,
    onFilesReject,
  }: any) => {
    mockDropHandler = onFilesDrop;
    mockAddFilesToInputHandler = onAddFilesToInput;
    mockFilesRejectHandler = onFilesReject;
    return children;
  },
}));

// Mock translation
const mockTranslate = vi.fn((key: string) => {
  const translations: Record<string, string> = {
    'errors.fileValidationError': 'File validation error',
    'errors.checkFileLimits': 'Check file limits',
    'errors.fetchConversationsFailed': 'Failed to fetch conversations',
    'errors.title': 'Error',
  };
  return translations[key] || key;
});

vi.mock('@tolgee/react', async (importOriginal) => {
  const actual = await importOriginal<typeof import('@tolgee/react')>();
  return {
    ...actual,
    useTranslate: () => ({ t: mockTranslate }),
    Tolgee: vi.fn(() => ({
      use: vi.fn().mockReturnThis(),
      addStaticData: vi.fn().mockReturnThis(),
      init: vi.fn().mockResolvedValue(undefined),
    })),
    InContextTools: vi.fn(),
    FormatSimple: vi.fn(),
  };
});

// Mock services and notifications
vi.mock('@resola-ai/services-shared', () => ({
  logger: { error: vi.fn() },
  createBaseConfig: vi.fn().mockReturnValue({
    apiUrl: 'test-api-url',
    apiKey: 'test-api-key',
    env: 'test',
    API_SERVER_URL: 'test-api-url',
    BASE_PATH: '/',
  }),
  axiosService: {
    init: vi.fn(),
    instance: { get: vi.fn(), post: vi.fn(), put: vi.fn(), delete: vi.fn() },
  },
  shareAppConfigService: { init: vi.fn(), getConfig: vi.fn() },
  datadogService: { init: vi.fn() },
  NotificationService: { sendNotification: vi.fn() },
}));

vi.mock('@mantine/notifications', () => ({
  notifications: { show: vi.fn() },
}));

// Add drag and drop polyfills
global.DragEvent = class DragEvent extends Event {
  dataTransfer: DataTransfer | null;
  constructor(type: string, eventInitDict?: DragEventInit) {
    super(type, eventInitDict);
    this.dataTransfer = eventInitDict?.dataTransfer || null;
  }
} as any;

global.DataTransfer = class DataTransfer {
  dropEffect = 'none';
  effectAllowed = 'all';
  files: FileList = [] as any;
  items: DataTransferItemList = [] as any;
  types: string[] = [];
  clearData(format?: string): void {}
  getData(format: string): string {
    return '';
  }
  setData(format: string, data: string): void {}
  setDragImage(image: Element, x: number, y: number): void {}
} as any;

// Mock file validation alert context
const mockShowValidationAlert = vi.fn();
const mockHideValidationAlert = vi.fn();

vi.mock('@/contexts/FileValidationAlertContext', async (importOriginal) => {
  const actual = await importOriginal<typeof import('@/contexts/FileValidationAlertContext')>();
  return {
    ...actual,
    useFileValidationAlert: () => ({
      showValidationAlert: mockShowValidationAlert,
      hideValidationAlert: mockHideValidationAlert,
      validationAlert: null,
    }),
    FileValidationAlertProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  };
});

describe('Message Component', () => {
  // Consolidated mock factories
  const createMockProps = (overrides = {}): any => ({
    launcher: {
      position: 'right' as const,
      size: 60,
      sideSpacing: 20,
      bottomSpacing: 20,
      launcherIconUrl: 'test-icon.png',
      launcherWidth: 60,
      launcherHeight: 60,
      closeButton: { show: false, width: 20, height: 20 },
      hideLauncherWhenOpenChatbox: false,
      shape: 'bubble',
    },
    chatbox: {
      width: 300,
      height: 500,
      borderRadius: 4,
      defaultPage: 'message' as NavigatorType,
      displayComponents: ['message'] as NavigatorType[],
      contentDisplay: 'top' as const,
      showChatBoxAsDefault: false,
    },
    home: {
      welcomeMessage: 'Welcome',
      background: {
        backgroundType: 'color' as const,
        backgroundValue: '#fff',
        fadeBackgroundToWhite: true,
      },
      styling: {},
      titleStyling: {},
      widgets: [],
      navigator: { text: 'Home' },
    },
    message: {
      styling: {},
      background: {
        backgroundType: 'color' as const,
        backgroundValue: '#fff',
        fadeBackgroundToWhite: true,
      },
      emptyState: {
        icon: { width: 30, height: 30 },
        header: { text: 'Empty' },
        content: { text: 'No messages', description: 'No messages yet' },
        action: { text: 'Send message' },
      },
      navigator: { text: 'Message' },
    },
    brand: { actionColor: '#000', backgroundColor: '#fff', useActionColorAsHyperlink: false },
    boxId: 'test-box',
    colorSettings: { buttonBackground: '#000', buttonText: '#fff', welcomeMessageColor: '#000' },
    customChatboxSize: { customHeight: 400, customMaxHeight: 600 },
    ...overrides,
  });

  const createMockContext = (overrides = {}): any => ({
    currentPage: 'message' as NavigatorType,
    messageNavigator: {
      isChat: () => true,
      isConversation: () => false,
      goToConversation: vi.fn(),
      goToChat: vi.fn(),
    },
    urlGetLiveChatConversationList: 'test-url',
    actionColor: '#000',
    liveChatConnected: false,
    isFinishedChatbotConversation: false,
    boxId: 'test-box',
    isTurnOnLiveChatIntegration: true,
    handleSendLiveChatDisplayEvent: vi.fn(),
    setIsFocusInput: vi.fn(),
    lastLiveChatMessage: null,
    organizationId: 'test-org',
    currentConversationId: '',
    setIsErrorFetchLCConversations: vi.fn(),
    ...overrides,
  });

  const createMockMediaContext = (overrides = {}): MediaContextType => ({
    isUploading: false,
    handleFileUploads: vi.fn(),
    createMessageWithMedia: vi.fn(),
    uploadedFiles: [],
    clearUploadedFiles: vi.fn(),
    clearProcessingFiles: vi.fn(),
    canAddMoreFiles: vi.fn(() => true),
    ...overrides,
  });

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(useChatBoxUIContext).mockReturnValue(createMockContext());
    vi.mocked(useMedia).mockReturnValue(createMockMediaContext());
    vi.mocked(ApiService.getLiveChatConversationList).mockResolvedValue({
      data: { conversations: [] },
      status: 'success',
      pagination: { next: '' },
    });
  });

  describe('Component Rendering', () => {
    it('renders null when currentPage is not message', async () => {
      vi.mocked(useChatBoxUIContext).mockReturnValue(
        createMockContext({ currentPage: 'not-message' as NavigatorType })
      );

      await act(async () => {
        const { container } = renderWithAllProviders(<Message {...createMockProps()} />);
        expect(container.firstChild).toBeNull();
      });
    });

    it('renders chat screen when live chat integration is disabled', async () => {
      vi.mocked(useChatBoxUIContext).mockReturnValue(
        createMockContext({ isTurnOnLiveChatIntegration: false })
      );

      await act(async () => {
        renderWithAllProviders(<Message {...createMockProps()} />);
      });

      await waitFor(() => {
        expect(screen.getByTestId('message_input_textbox')).toBeInTheDocument();
        expect(screen.getByTestId('chat-header')).toBeInTheDocument();
        expect(screen.getByTestId('chat-state')).toBeInTheDocument();
      });
    });

    it('renders conversation screen when live chat integration is enabled', async () => {
      vi.mocked(useChatBoxUIContext).mockReturnValue(
        createMockContext({
          messageNavigator: {
            isChat: () => false,
            isConversation: () => true,
            goToConversation: vi.fn(),
            goToChat: vi.fn(),
          },
        })
      );

      // Mock API to return conversations so conversation-state is shown
      vi.mocked(ApiService.getLiveChatConversationList).mockResolvedValueOnce({
        data: {
          conversations: [
            {
              id: '1',
              name: 'Test Conversation',
              lastEnduserMessage: 'Hello',
              lastOperatorMessage: 'Hi',
              status: 'new',
              teamId: 'team1',
              enduser: { id: 'user1', platform: 'web', name: 'User 1' },
              enduserId: 'user1',
              lastMessage: {
                id: 'msg1',
                data: {
                  id: 'msg1',
                  text: 'Hello',
                  type: 'text',
                  metadata: { sender: { name: 'User 1', type: 'user', iconUrl: 'test.png' } },
                },
                sender: { name: 'User 1', id: 'user1', type: 'user', picture: 'test.png' },
                created: '2023-01-01',
              },
              ocsChannel: 'channel1',
              integrationId: 'int1',
              unread: false,
              app: null,
              orgId: 'org1',
              created: '2023-01-01',
              updated: '2023-01-01',
              platform: { name: 'web', channelName: 'web' },
              fromChatbot: false,
              isNewConversation: false,
              isBookmark: false,
            },
          ],
        },
        status: 'success',
        pagination: { next: '' },
      });

      renderWithAllProviders(<Message {...createMockProps()} />);

      await waitFor(() => {
        expect(screen.getByTestId('conversation-state')).toBeInTheDocument();
      });
    });

    it('shows trigger flow button when chat is finished', async () => {
      vi.mocked(useChatBoxUIContext).mockReturnValue(
        createMockContext({
          isFinishedChatbotConversation: true,
          isTurnOnLiveChatIntegration: false,
        })
      );

      await act(async () => {
        renderWithAllProviders(<Message {...createMockProps()} />);
      });

      await waitFor(() => {
        expect(screen.getByTestId('trigger-flow-button')).toBeInTheDocument();
      });
    });
  });

  describe('File Upload Functionality', () => {
    it('handles global file drop successfully', async () => {
      const mockHandleFileUploads = vi.fn().mockResolvedValue(['url1', 'url2']);
      vi.mocked(useMedia).mockReturnValue(
        createMockMediaContext({ handleFileUploads: mockHandleFileUploads })
      );

      const mockDispatchEvent = vi.fn();
      const originalDispatchEvent = window.dispatchEvent;
      Object.defineProperty(window, 'dispatchEvent', { value: mockDispatchEvent, writable: true });

      const file = new File(['test'], 'test.pdf', { type: 'application/pdf' });
      renderWithAllProviders(<Message {...createMockProps()} />);

      await act(async () => {
        if (mockDropHandler) {
          await mockDropHandler([file]);
        }
      });

      await waitFor(() => {
        expect(mockHandleFileUploads).toHaveBeenCalledWith([file], 'test-org', '');
        expect(mockDispatchEvent).toHaveBeenCalledTimes(1);
        const eventCall = mockDispatchEvent.mock.calls[0][0];
        expect(eventCall.type).toBe('deca-chatbox-file-upload-success');
        expect(eventCall.detail).toEqual({ urls: ['url1', 'url2'], files: [file] });
      });

      Object.defineProperty(window, 'dispatchEvent', {
        value: originalDispatchEvent,
        writable: true,
      });
    });

    it('handles input file upload successfully', async () => {
      const mockHandleFileUploads = vi.fn().mockResolvedValue(['url1']);
      vi.mocked(useMedia).mockReturnValue(
        createMockMediaContext({ handleFileUploads: mockHandleFileUploads })
      );

      const file = new File(['test'], 'test.txt', { type: 'text/plain' });
      renderWithAllProviders(<Message {...createMockProps()} />);

      await act(async () => {
        if (mockAddFilesToInputHandler) {
          await mockAddFilesToInputHandler([file]);
        }
      });

      await waitFor(() => {
        expect(mockHandleFileUploads).toHaveBeenCalledWith([file], 'test-org', '');
      });
    });

    it('handles file rejection by showing validation alert', async () => {
      const file = new File(['test'], 'test.exe', { type: 'application/x-executable' });
      renderWithAllProviders(<Message {...createMockProps()} />);

      await act(async () => {
        if (mockFilesRejectHandler) {
          mockFilesRejectHandler([file]);
        }
      });

      expect(mockShowValidationAlert).toHaveBeenCalledWith('File validation error');
    });

    it('prevents upload when canAddMoreFiles returns false', async () => {
      const mockCanAddMoreFiles = vi.fn(() => false);
      const mockHandleFileUploads = vi.fn();

      vi.mocked(useMedia).mockReturnValue(
        createMockMediaContext({
          canAddMoreFiles: mockCanAddMoreFiles,
          handleFileUploads: mockHandleFileUploads,
        })
      );

      const file = new File(['test'], 'test.pdf', { type: 'application/pdf' });
      renderWithAllProviders(<Message {...createMockProps()} />);

      await act(async () => {
        if (mockDropHandler) {
          await mockDropHandler([file]);
        }
      });

      await waitFor(() => {
        expect(mockCanAddMoreFiles).toHaveBeenCalledWith(1);
        expect(mockHandleFileUploads).not.toHaveBeenCalled();
        expect(mockShowValidationAlert).toHaveBeenCalledWith('File validation error');
      });
    });

    it('handles file upload error gracefully', async () => {
      const mockHandleFileUploads = vi.fn().mockRejectedValue(new Error('Upload failed'));
      vi.mocked(useMedia).mockReturnValue(
        createMockMediaContext({ handleFileUploads: mockHandleFileUploads })
      );

      const file = new File(['test'], 'test.pdf', { type: 'application/pdf' });
      renderWithAllProviders(<Message {...createMockProps()} />);

      await act(async () => {
        if (mockDropHandler) {
          await mockDropHandler([file]);
        }
      });

      await waitFor(() => {
        expect(mockHandleFileUploads).toHaveBeenCalledWith([file], 'test-org', '');
      });
    });
  });

  describe('Live Chat Events', () => {
    it('handles focus and blur events when connected', async () => {
      const mockSetIsFocusInput = vi.fn();
      const mockHandleSendLiveChatDisplayEvent = vi.fn();

      vi.mocked(useChatBoxUIContext).mockReturnValue(
        createMockContext({
          liveChatConnected: true,
          isTurnOnLiveChatIntegration: false,
          setIsFocusInput: mockSetIsFocusInput,
          handleSendLiveChatDisplayEvent: mockHandleSendLiveChatDisplayEvent,
        })
      );

      await act(async () => {
        renderWithAllProviders(<Message {...createMockProps()} />);
      });

      const textbox = screen.getByTestId('message_input_textbox');

      await act(async () => {
        fireEvent.focus(textbox);
      });

      expect(mockSetIsFocusInput).toHaveBeenCalledWith(true);
      expect(mockHandleSendLiveChatDisplayEvent).toHaveBeenCalledWith(
        LIVECHAT_DISPLAY_EVENT_TYPES.FOCUS
      );

      await act(async () => {
        fireEvent.blur(textbox);
      });

      expect(mockSetIsFocusInput).toHaveBeenCalledWith(false);
      expect(mockHandleSendLiveChatDisplayEvent).toHaveBeenCalledWith(
        LIVECHAT_DISPLAY_EVENT_TYPES.UNFOCUS
      );
    });

    it('sends READ event when focusing with lastLiveChatMessage', async () => {
      const mockHandleSendLiveChatDisplayEvent = vi.fn();
      const mockLastMessage = {
        id: 'msg1',
        data: { id: 'msg1', text: 'Hello', type: 'text' },
        sender: { name: 'User 1', id: 'user1', type: 'user' },
        created: '2023-01-01',
      };

      vi.mocked(useChatBoxUIContext).mockReturnValue(
        createMockContext({
          liveChatConnected: true,
          isTurnOnLiveChatIntegration: false,
          lastLiveChatMessage: mockLastMessage,
          handleSendLiveChatDisplayEvent: mockHandleSendLiveChatDisplayEvent,
        })
      );

      await act(async () => {
        renderWithAllProviders(<Message {...createMockProps()} />);
      });

      await act(async () => {
        fireEvent.focus(screen.getByTestId('message_input_textbox'));
      });

      expect(mockHandleSendLiveChatDisplayEvent).toHaveBeenCalledWith(
        LIVECHAT_DISPLAY_EVENT_TYPES.FOCUS
      );
      expect(mockHandleSendLiveChatDisplayEvent).toHaveBeenCalledWith(
        LIVECHAT_DISPLAY_EVENT_TYPES.READ
      );
    });

    it('does not handle focus/blur events when liveChatConnected is false', async () => {
      const mockSetIsFocusInput = vi.fn();
      const mockHandleSendLiveChatDisplayEvent = vi.fn();

      vi.mocked(useChatBoxUIContext).mockReturnValue(
        createMockContext({
          liveChatConnected: false,
          isTurnOnLiveChatIntegration: false,
          setIsFocusInput: mockSetIsFocusInput,
          handleSendLiveChatDisplayEvent: mockHandleSendLiveChatDisplayEvent,
        })
      );

      await act(async () => {
        renderWithAllProviders(<Message {...createMockProps()} />);
      });

      await act(async () => {
        const textbox = screen.getByTestId('message_input_textbox');
        fireEvent.focus(textbox);
        fireEvent.blur(textbox);
      });

      expect(mockSetIsFocusInput).not.toHaveBeenCalled();
      expect(mockHandleSendLiveChatDisplayEvent).not.toHaveBeenCalled();
    });
  });

  describe('Conversation Management', () => {
    it('fetches conversations when live chat integration is enabled', async () => {
      vi.mocked(useChatBoxUIContext).mockReturnValue(
        createMockContext({
          messageNavigator: {
            isChat: () => false,
            isConversation: () => true,
            goToConversation: vi.fn(),
            goToChat: vi.fn(),
          },
        })
      );

      // Mock API to return conversations
      vi.mocked(ApiService.getLiveChatConversationList).mockResolvedValueOnce({
        data: {
          conversations: [
            {
              id: '1',
              name: 'Test Conversation',
              lastEnduserMessage: 'Hello',
              lastOperatorMessage: 'Hi',
              status: 'new',
              teamId: 'team1',
              enduser: { id: 'user1', platform: 'web', name: 'User 1' },
              enduserId: 'user1',
              lastMessage: {
                id: 'msg1',
                data: {
                  id: 'msg1',
                  text: 'Hello',
                  type: 'text',
                  metadata: { sender: { name: 'User 1', type: 'user', iconUrl: 'test.png' } },
                },
                sender: { name: 'User 1', id: 'user1', type: 'user', picture: 'test.png' },
                created: '2023-01-01',
              },
              ocsChannel: 'channel1',
              integrationId: 'int1',
              unread: false,
              app: null,
              orgId: 'org1',
              created: '2023-01-01',
              updated: '2023-01-01',
              platform: { name: 'web', channelName: 'web' },
              fromChatbot: false,
              isNewConversation: false,
              isBookmark: false,
            },
          ],
        },
        status: 'success',
        pagination: { next: '' },
      });

      await act(async () => {
        renderWithAllProviders(<Message {...createMockProps()} />);
      });

      expect(ApiService.getLiveChatConversationList).toHaveBeenCalledWith('test-box', 'test-url');

      await waitFor(() => {
        expect(screen.getByTestId('conversation-state')).toBeInTheDocument();
      });
    });

    it('handles conversation fetch error with notification', async () => {
      const { notifications } = await vi.importMock('@mantine/notifications');

      vi.mocked(useChatBoxUIContext).mockReturnValue(
        createMockContext({
          messageNavigator: {
            isChat: () => false,
            isConversation: () => true,
            goToConversation: vi.fn(),
            goToChat: vi.fn(),
          },
        })
      );

      vi.mocked(ApiService.getLiveChatConversationList).mockRejectedValueOnce(
        new Error('Failed to fetch')
      );

      renderWithAllProviders(<Message {...createMockProps()} />);

      await waitFor(() => {
        expect(screen.getByTestId('empty-state')).toBeInTheDocument();
        // expect(notifications.show).toHaveBeenCalledWith({
        //   message: 'Failed to fetch conversations',
        //   color: 'red',
        //   title: 'Error',
        // });
      });
    });

    it('handles API response with error status', async () => {
      const mockSetIsErrorFetchLCConversations = vi.fn();

      vi.mocked(useChatBoxUIContext).mockReturnValue(
        createMockContext({
          messageNavigator: {
            isChat: () => false,
            isConversation: () => true,
            goToConversation: vi.fn(),
            goToChat: vi.fn(),
          },
          setIsErrorFetchLCConversations: mockSetIsErrorFetchLCConversations,
        })
      );

      vi.mocked(ApiService.getLiveChatConversationList).mockResolvedValueOnce({
        data: { conversations: [] },
        status: 'error',
        pagination: { next: '' },
      });

      await act(async () => {
        renderWithAllProviders(<Message {...createMockProps()} />);
      });

      await waitFor(() => {
        expect(mockSetIsErrorFetchLCConversations).toHaveBeenCalledWith(true);
      });
    });

    it('does not fetch conversations when live chat integration is disabled', async () => {
      vi.mocked(useChatBoxUIContext).mockReturnValue(
        createMockContext({
          isTurnOnLiveChatIntegration: false,
          messageNavigator: {
            isChat: () => false,
            isConversation: () => true,
            goToConversation: vi.fn(),
            goToChat: vi.fn(),
          },
        })
      );

      await act(async () => {
        renderWithAllProviders(<Message {...createMockProps()} />);
      });

      expect(ApiService.getLiveChatConversationList).not.toHaveBeenCalled();
    });

    it('renders loading state when fetching conversations', async () => {
      vi.mocked(useChatBoxUIContext).mockReturnValue(
        createMockContext({
          messageNavigator: {
            isChat: () => false,
            isConversation: () => true,
            goToConversation: vi.fn(),
            goToChat: vi.fn(),
          },
        })
      );

      vi.mocked(ApiService.getLiveChatConversationList).mockImplementation(
        () =>
          new Promise((resolve) =>
            setTimeout(
              () =>
                resolve({
                  data: { conversations: [] },
                  status: 'success',
                  pagination: { next: '' },
                }),
              100
            )
          )
      );

      await act(async () => {
        renderWithAllProviders(<Message {...createMockProps()} />);
      });

      expect(screen.getByTestId('conversation-header')).toBeInTheDocument();
    });
  });
});
