import React from 'react';
import { renderHook, act } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ChatBoxUIContextProvider, useChatBoxUIContext } from './chatBoxUIContext';
import type { ChatBoxUIAppProps, MessageEventPayload } from '../models';

// Mock external dependencies
vi.mock('react-device-detect', () => ({ isMobileOnly: false }));
vi.mock('@resola-ai/utils', () => ({
  createCustomEventListener: vi.fn(),
  removeCustomEventListener: vi.fn(),
  sendCustomEvent: vi.fn(),
  defaultFontName: 'Inter, sans-serif',
}));
vi.mock('centrifuge', () => ({
  Centrifuge: vi.fn().mockImplementation(() => ({ publish: vi.fn() })),
}));

// Mock services
const mockAppServiceInstance = {
  decaBoxId: 'test-box-id',
  getUserId: vi.fn(() => 'test-user-id'),
  decaBotId: 'test-bot-id',
  decaOrgId: 'test-org-id',
};

vi.mock('../services/app', () => ({
  default: { getInstance: vi.fn(() => mockAppServiceInstance) },
}));

vi.mock('../services/api', () => ({
  default: {
    sendEventDataToHook: vi.fn().mockResolvedValue({}),
    sendArticleAnalyticEvent: vi.fn().mockResolvedValue({}),
  },
}));

vi.mock('../utils', () => ({
  getCurrentPage: vi.fn().mockReturnValue('home'),
  getUserAgent: vi.fn().mockReturnValue({ browser: 'Chrome', version: '120.0.0', os: 'MacOS' }),
  generateLoaderMessage: vi.fn().mockReturnValue({ id: 'loader-1', type: 'loader', data: {} }),
  generateActionSendChatboxDisplayStyleEvent: vi.fn().mockReturnValue('test-event'),
  getLocalStorageData: vi.fn().mockImplementation((key, defaultValue) => defaultValue),
}));

// Mock constants
vi.mock('@/constants', () => ({
  MESSAGE_SCREENS: { conversation: 'conversation', chat: 'chat' },
  BOT_TRIGGER_TYPES: { CLICK_A_BUTTON: 'CLICK_A_BUTTON', OPEN_PAGE: 'OPEN_PAGE' },
  randomDomId: vi.fn(() => 'random-id'),
}));

vi.mock('../constants', () => ({
  initLiveChatInfo: {
    name: '',
    picture: '',
    description: '',
    teamId: '',
    assigneeId: '',
    conversationId: '',
  },
  LIVECHAT_CONVERSATION_LIMIT: 10,
  LIVECHAT_MESSAGE_HISTORY_LIMIT: 50,
  LIVECHAT_CONVERSATION_STATUS: { COMPLETED: 'completed' },
  CHATBOT_SYSTEM_TYPES: { FINISHED: 'chatbot.finished' },
  CHATBOT_MESSAGE_HISTORY_LIMIT: 50,
  LIVECHAT_USER_EVENTS_CHANNEL: 'livechat-events',
  COMMON_MESSAGE_EVENTS: { STOP_STREAMING: 'stop-streaming' },
  DECA_LC_CONVERSATION_INFO_KEY: 'deca-lc-conversation-info-{boxId}',
}));

// Test utilities
const createMockProps = (overrides = {}): ChatBoxUIAppProps => ({
  boxId: 'test-box-id',
  launcher: { position: 'right', size: 60 } as any,
  chatbox: {
    defaultPage: 'home',
    showChatBoxAsDefault: false,
    displayComponents: ['home', 'message'],
    width: 300,
    height: 500,
    borderRadius: 4,
    contentDisplay: 'top',
  } as any,
  home: {
    welcomeMessage: 'Welcome!',
    background: {
      backgroundType: 'color',
      backgroundValue: '#ffffff',
      fadeBackgroundToWhite: true,
    },
    styling: {},
    titleStyling: {},
    widgets: [],
    navigator: { text: 'Home' },
  } as any,
  message: {
    styling: {},
    background: {
      backgroundType: 'color',
      backgroundValue: '#ffffff',
      fadeBackgroundToWhite: true,
    },
    emptyState: {
      icon: { width: 30, height: 30 },
      header: { text: 'No messages' },
      content: { text: 'Start a conversation', description: 'Send a message to get started' },
      action: { text: 'Send message' },
    },
    navigator: { text: 'Message' },
  } as any,
  general: { availability: true, autoOpen: false, botTrigger: 'OPEN_PAGE' } as any,
  brand: {
    actionColor: '#007bff',
    backgroundColor: '#ffffff',
    useActionColorAsHyperlink: false,
  } as any,
  colorSettings: {
    buttonBackground: '#007bff',
    buttonText: '#ffffff',
    welcomeMessageColor: '#000000',
  } as any,
  integrationSettings: {
    livechatConnected: false,
    livechatSettings: { incidentMessage: 'Live chat is currently unavailable' },
    chatbotSettings: {
      incidentMessage: 'Chatbot is currently unavailable',
      systemicBackButton: false,
    },
  } as any,
  widgets: [],
  ...overrides,
});

const renderChatBoxUIHook = (props = createMockProps()) => {
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <ChatBoxUIContextProvider setting={props}>{children}</ChatBoxUIContextProvider>
  );
  return renderHook(() => useChatBoxUIContext(), { wrapper });
};

const mockEventPayload: MessageEventPayload = {
  events: [
    {
      type: 'message',
      message: { type: 'text', data: { text: 'Hello' } },
      source: {
        type: 'user',
        userId: 'test-user',
        meta: { userAgent: { browser: 'Chrome', version: '120.0.0', os: 'MacOS' } },
      },
    },
    {
      type: 'message',
      message: { type: 'text', data: { text: 'World' } },
      source: {
        type: 'user',
        userId: 'test-user',
        meta: { userAgent: { browser: 'Chrome', version: '120.0.0', os: 'MacOS' } },
      },
    },
  ],
};

describe('ChatBoxUIContext', () => {
  let mockDateNow: any;

  beforeEach(() => {
    vi.clearAllMocks();
    mockDateNow = vi.spyOn(Date, 'now').mockReturnValue(5000);
    // Mock DOM methods
    Object.defineProperty(HTMLElement.prototype, 'clientHeight', {
      configurable: true,
      value: 100,
    });
    Object.defineProperty(HTMLElement.prototype, 'clientWidth', { configurable: true, value: 200 });
  });

  afterEach(() => {
    mockDateNow?.mockRestore();
  });

  describe('Initialization', () => {
    it('should initialize with correct default values', () => {
      const { result } = renderChatBoxUIHook();
      expect(result.current.currentPage).toBe('home');
      expect(result.current.chatBoxUIState).toBe(false);
      expect(result.current.chatBoxUIAvailability).toBe(true);
      expect(result.current.isDisplayDouble).toBe(true);
      expect(result.current.messages).toEqual([]);
      expect(result.current.liveChatConnected).toBe(false);
      expect(result.current.isStreaming).toBe(false);
    });

    it('should initialize with custom props', () => {
      const customProps = createMockProps({
        chatbox: {
          defaultPage: 'message',
          showChatBoxAsDefault: true,
          displayComponents: ['message'],
          width: 300,
          height: 500,
          borderRadius: 4,
          contentDisplay: 'top',
        },
        general: { availability: false, autoOpen: true },
      });
      const { result } = renderChatBoxUIHook(customProps);
      expect(result.current.currentPage).toBe('message');
      expect(result.current.chatBoxUIState).toBe(true);
      expect(result.current.chatBoxUIAvailability).toBe(false);
    });
  });

  describe('Event Payload Normalization', () => {
    it('should normalize event payload with unique timestamps', () => {
      const { result } = renderChatBoxUIHook();
      const normalizedPayload = result.current.normalizeEventPayload(mockEventPayload);
      expect(normalizedPayload.events).toHaveLength(2);
      expect(normalizedPayload.events[0].timestamp).toBe(5000);
      expect(normalizedPayload.events[1].timestamp).toBe(5001);
    });

    it('should preserve all other properties in events', () => {
      const { result } = renderChatBoxUIHook();
      const normalizedPayload = result.current.normalizeEventPayload(mockEventPayload);
      expect(normalizedPayload.events[0].type).toBe('message');
      expect(normalizedPayload.events[0].message).toEqual({
        type: 'text',
        data: { text: 'Hello' },
      });
    });

    it('should handle empty and single events', () => {
      const { result } = renderChatBoxUIHook();
      expect(result.current.normalizeEventPayload({ events: [] }).events).toHaveLength(0);
      expect(
        result.current.normalizeEventPayload({ events: [mockEventPayload.events[0]] }).events
      ).toHaveLength(1);
    });
  });

  describe('State Management', () => {
    it('should toggle chatbox state when clicking launcher', () => {
      const { result } = renderChatBoxUIHook();
      expect(result.current.chatBoxUIState).toBe(false);
      act(() => result.current.clickLauncherHandler());
      expect(result.current.chatBoxUIState).toBe(true);
      act(() => result.current.clickLauncherHandler());
      expect(result.current.chatBoxUIState).toBe(false);
    });

    it('should disable chatbox UI when close handler is called', () => {
      const { result } = renderChatBoxUIHook();
      const mockEvent = { preventDefault: vi.fn(), stopPropagation: vi.fn() };
      act(() => result.current.closeHandler(mockEvent));
      expect(result.current.chatBoxUIAvailability).toBe(false);
      expect(result.current.chatBoxUIState).toBe(false);
      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(mockEvent.stopPropagation).toHaveBeenCalled();
    });
  });

  describe('Navigation', () => {
    it('should navigate to different pages', () => {
      const { result } = renderChatBoxUIHook();
      act(() => result.current.navigateHandler('home'));
      expect(result.current.currentPage).toBe('home');
      act(() => result.current.navigateHandler('message'));
      expect(result.current.currentPage).toBe('message');
    });

    it('should handle message navigator operations', () => {
      const { result } = renderChatBoxUIHook();
      expect(result.current.messageNavigator.isConversation()).toBe(true);
      expect(result.current.messageNavigator.isChat()).toBe(false);

      act(() => result.current.messageNavigator.goToChat());
      expect(result.current.messageNavigator.isChat()).toBe(true);
      expect(result.current.messageNavigator.isConversation()).toBe(false);

      act(() => result.current.messageNavigator.goToConversation());
      expect(result.current.messageNavigator.isConversation()).toBe(true);
      expect(result.current.messageNavigator.isChat()).toBe(false);
    });
  });

  describe('Message Management', () => {
    it('should update messages correctly', () => {
      const { result } = renderChatBoxUIHook();
      const newMessage = {
        id: 'msg-1',
        type: 'text' as const,
        data: { text: 'Hello', type: 'text' },
        from: 'user' as const,
      };
      act(() => result.current.updateMessages(newMessage));
      expect(result.current.messages).toHaveLength(1);
      expect(result.current.messages[0]).toEqual(newMessage);
    });

    it('should remove loader and button messages when updating', () => {
      const { result } = renderChatBoxUIHook();
      const messages = [
        {
          id: 'loader-1',
          type: 'loader' as const,
          data: { variant: 'default', color: 'primary', size: 'sm' },
          from: 'bot' as const,
        },
        { id: 'btn-1', type: 'buttons' as const, data: { actions: [] }, from: 'bot' as const },
        {
          id: 'text-1',
          type: 'text' as const,
          data: { text: 'Hello', type: 'text' },
          from: 'user' as const,
        },
      ];

      act(() => result.current.setMessages(messages));
      expect(result.current.messages).toHaveLength(3);

      const newMessage = {
        id: 'text-2',
        type: 'text' as const,
        data: { text: 'World', type: 'text' },
        from: 'bot' as const,
      };
      act(() => result.current.updateMessages(newMessage));

      expect(result.current.messages).toHaveLength(2);
      expect(result.current.messages.find((msg) => msg.type === 'loader')).toBeUndefined();
      expect(result.current.messages.find((msg) => msg.type === 'buttons')).toBeUndefined();
    });
  });

  describe('Streaming Functionality', () => {
    it('should handle stop streaming correctly', () => {
      const { result } = renderChatBoxUIHook();
      act(() => {
        result.current.setIsStreaming(true);
        result.current.setStreamingMessageId('streaming-msg-1');
      });
      expect(result.current.isStreaming).toBe(true);
      expect(result.current.streamingMessageId).toBe('streaming-msg-1');

      act(() => result.current.handleStopStreaming());
      expect(result.current.isStreaming).toBe(false);
      expect(result.current.streamingMessageId).toBe('');
    });
  });

  describe('LiveChat Functionality', () => {
    it('should handle livechat connection state', () => {
      const { result } = renderChatBoxUIHook();
      expect(result.current.liveChatConnected).toBe(false);
      expect(result.current.isDisconnectLiveChat).toBe(true);
      expect(result.current.isConnectNotAssignedOperatorYet).toBe(false);

      act(() => result.current.setLiveChatConnected(true));
      expect(result.current.liveChatConnected).toBe(true);
      expect(result.current.isDisconnectLiveChat).toBe(false);
      expect(result.current.isConnectNotAssignedOperatorYet).toBe(true);

      act(() => result.current.setIsConversationAssignedToOperator(true));
      expect(result.current.isConnectAssignedOperator).toBe(true);
      expect(result.current.isConnectNotAssignedOperatorYet).toBe(false);
    });

    it('should handle conversation selection', () => {
      const { result } = renderChatBoxUIHook();
      const mockConversation = { id: 'conv-1', status: 'active', messages: [] };
      act(() => result.current.setConversationSelected(mockConversation as any));
      expect(result.current.conversationSelected).toEqual(mockConversation);
    });

    it('should send live chat display event', () => {
      const mockCentrifuge = { publish: vi.fn() };
      const { result } = renderChatBoxUIHook();
      act(() => {
        result.current.setCentrifugeInstance(mockCentrifuge as any);
        result.current.setCurrentConversationId('test-conv-id');
      });
      act(() => {
        result.current.handleSendLiveChatDisplayEvent('focus');
      });
      expect(mockCentrifuge.publish).toHaveBeenCalled();
    });

    it('should handle missing centrifuge instance', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const { result } = renderChatBoxUIHook();
      act(() => result.current.handleSendLiveChatDisplayEvent('focus'));
      expect(consoleSpy).toHaveBeenCalledWith('Centrifuge instance not initialized');
      consoleSpy.mockRestore();
    });
  });

  describe('DOM Reference Updates', () => {
    const testRefUpdate = async (refUpdateFn: string, expectedProperty: string) => {
      const { result } = renderChatBoxUIHook();
      const mockElement = document.createElement('div');
      act(() => (result.current as any)[refUpdateFn](mockElement));
      await act(async () => await new Promise((resolve) => setTimeout(resolve, 10)));
      expect((result.current.dom as any)[expectedProperty].height).toBe(100);
      expect((result.current.dom as any)[expectedProperty].width).toBe(200);
    };

    it('should update all ref dimensions correctly', async () => {
      await testRefUpdate('updateNavigatorRef', 'navigatorData');
      await testRefUpdate('updateInputElmRef', 'inputElmData');
      await testRefUpdate('updateHeaderRef', 'headerData');
      await testRefUpdate('updateEmptyStateHeaderRef', 'emptyStateHeaderData');
      await testRefUpdate('updateStartConversationButtonRef', 'startConversationButtonData');
      await testRefUpdate('updateTriggerFlowButtonRef', 'triggerFlowButtonElmData');
    });

    it('should handle null reference gracefully', () => {
      const { result } = renderChatBoxUIHook();
      act(() => result.current.updateNavigatorRef(null));
      expect(result.current.dom.navigatorData.height).toBe(0);
      expect(result.current.dom.navigatorData.width).toBe(0);
    });
  });

  describe('Chatbot System State', () => {
    it('should update chatbot system state correctly', () => {
      const { result } = renderChatBoxUIHook();
      const systemState = { type: 'chatbot.finished', text: 'Conversation ended' };
      act(() => result.current.setChatbotSystemState(systemState));
      expect(result.current.chatbotSystemState).toEqual(systemState);
      expect(result.current.isFinishedChatbotConversation).toBe(true);
    });

    it('should process system messages through updateMessages', () => {
      const { result } = renderChatBoxUIHook();
      const systemMessage = {
        id: 'sys-1',
        type: 'system' as const,
        data: { type: 'chatbot.finished', text: 'Chat completed' },
        from: 'bot' as const,
      };
      act(() => result.current.updateMessages(systemMessage));
      expect(result.current.messages).toHaveLength(1);
      expect(result.current.messages[0]).toEqual(systemMessage);
    });
  });

  describe('Advanced Functionality', () => {
    it('should handle trigger chatbot flow', () => {
      const { result } = renderChatBoxUIHook();
      act(() => result.current.handleTriggerChatbotFlow());
      expect(result.current.messages).toHaveLength(1);
      expect(result.current.messages[0].type).toBe('loader');
    });

    it('should handle go to chat', () => {
      const { result } = renderChatBoxUIHook();
      act(() => {
        result.current.navigateHandler('message');
        result.current.messageNavigator.goToChat();
      });
      expect(result.current.currentPage).toBe('message');
      expect(result.current.messageNavigator.isChat()).toBe(true);
    });

    it('should reset chat box state', () => {
      const { result } = renderChatBoxUIHook();
      act(() => result.current.setIsKBCardMessaging(true));
      expect(result.current.isKBCardMessaging).toBe(true);
      act(() => result.current.resetChatBoxState());
      expect(result.current.isKBCardMessaging).toBe(false);
    });

    it('should handle systemic back button event', () => {
      const { result } = renderChatBoxUIHook();
      act(() => result.current.handleSendSystemicBackButtonEvent());
      expect(result.current.disableBackNodeButton).toBe(true);
    });
  });

  describe('Context Properties and State Setters', () => {
    it('should expose all required context properties', () => {
      const { result } = renderChatBoxUIHook();
      const requiredProps = [
        'boxId',
        'organizationId',
        'wsUserId',
        'botId',
        'actionColor',
        'userAgent',
        'sourceInfo',
        'articleAnalyticEventPayload',
        'conversationInfoJson',
        'forceDisconnectLivechatPayload',
      ];
      requiredProps.forEach((prop) =>
        expect(result.current[prop as keyof typeof result.current]).toBeDefined()
      );
      expect(result.current.decaLCConversationInfoKey).toContain('test-box-id');
    });

    it('should handle all state setters correctly', () => {
      const { result } = renderChatBoxUIHook();
      const stateSetters = [
        ['setIsKBCardMessaging', 'isKBCardMessaging', true],
        ['setIsStreaming', 'isStreaming', true],
        ['setStreamingMessageId', 'streamingMessageId', 'test-id'],
        ['setDisableBackNodeButton', 'disableBackNodeButton', true],
        ['setDisableRichMessageInput', 'disableRichMessageInput', true],
        ['setIsFocusInput', 'isFocusInput', true],
        ['setIsErrorFetchLCConversations', 'isErrorFetchLCConversations', true],
        ['setCurrentConversationId', 'currentConversationId', 'test-conv-id'],
      ] as const;

      act(() => {
        stateSetters.forEach(([setter, , value]) => {
          (result.current as any)[setter](value);
        });
      });

      stateSetters.forEach(([, getter, expectedValue]) => {
        expect((result.current as any)[getter]).toBe(expectedValue);
      });
    });

    it('should handle array state setters', () => {
      const { result } = renderChatBoxUIHook();
      const arraySetters = [
        ['setActionButtonEvents', 'actionButtonEvents', ['event1', 'event2']],
        ['setSubmitFormMessageEvents', 'submitFormMessageEvents', ['form1', 'form2']],
        ['setArticleAnalyticsEvents', 'articleAnalyticsEvents', ['analytics1', 'analytics2']],
        ['setSelectedLiveChatTeamEvents', 'selectedLiveChatTeamEvents', ['team1', 'team2']],
        ['setLivechatConversationList', 'livechatConversationList', []],
      ] as const;

      act(() => {
        arraySetters.forEach(([setter, , value]) => {
          (result.current as any)[setter](value);
        });
      });

      arraySetters.forEach(([, getter, expectedValue]) => {
        expect((result.current as any)[getter]).toEqual(expectedValue);
      });
    });
  });

  describe('Error Handling', () => {
    it('should throw error when useChatBoxUIContext is used outside provider', () => {
      expect(() => renderHook(() => useChatBoxUIContext())).toThrow(
        'useChatBoxUIContext must be used inside ChatBoxUIContextProvider'
      );
    });
  });

  describe('Widget Integration', () => {
    it('should handle call-to-action widget events', () => {
      const propsWithWidget = createMockProps({
        widgets: [{ type: 'CALL_TO_ACTION', properties: { clickedEvent: 'test-widget-clicked' } }],
      });
      const { result } = renderChatBoxUIHook(propsWithWidget);
      expect(result.current.currentPage).toBe('home');
    });
  });
});
