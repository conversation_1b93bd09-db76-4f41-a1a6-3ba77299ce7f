import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeAll, beforeEach } from 'vitest';
import { MantineProvider } from '@mantine/core';
import DialogConfirmation from './index';

beforeAll(() => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });

  ['ResizeObserver', 'IntersectionObserver'].forEach((name) => {
    Object.defineProperty(window, name, {
      writable: true,
      value: vi.fn().mockImplementation(() => ({
        disconnect: vi.fn(),
        observe: vi.fn(),
        unobserve: vi.fn(),
      })),
    });
  });
});

vi.mock('@mantine/emotion', () => ({
  createStyles: (styleFunction: (theme: any) => any) => {
    // Execute the style function immediately to cover the CSS lines
    const mockTheme = {
      colors: {
        pervenche: ['', '', '', '', '', '#5B9BD5'],
        silverFox: ['', '', '#F5F5F5', '', '', '', '', '', '', '#333333'],
        decaNavy: ['#E8F4FD', '', '', '', '', '#1B4F72'],
      },
      spacing: { md: 16 },
    };

    // Execute the style function to ensure coverage of lines 9-34
    const styles = styleFunction(mockTheme);

    return () => ({
      classes: {
        buttonStyle: 'button-style',
        container: 'container-class',
        wrapAlertIcon: 'wrap-alert-icon',
        iconX: 'icon-x-class',
        iconAlertCircle: 'icon-alert-circle-class',
        containerCustomClass: 'container-custom-class',
      },
      cx: (...args: any[]) => args.filter(Boolean).join(' '),
    });
  },
}));

vi.mock('@mantine/core', async () => {
  const actual = await vi.importActual('@mantine/core');
  return {
    ...actual,
    useMantineTheme: () => ({
      colors: {
        pervenche: ['', '', '', '', '', '#5B9BD5'],
        silverFox: ['', '', '#F5F5F5'],
        decaNavy: ['#E8F4FD', '', '', '', '', '#1B4F72'],
      },
      spacing: { md: 16 },
      primaryColor: 'blue',
    }),
    useMantineColorScheme: () => ({ colorScheme: 'light', toggleColorScheme: vi.fn() }),
  };
});

vi.mock('@tabler/icons-react', () => ({
  IconAlertCircle: ({ className }: { className?: string }) => (
    <div data-testid='icon-alert-circle' className={className} />
  ),
  IconX: ({ className }: { className?: string }) => (
    <div data-testid='icon-x' className={className} />
  ),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        saveAndPublish: 'Save and Publish',
        saveAndPublishDesc: 'Are you sure you want to save and publish these changes?',
        cancel: 'Cancel',
        confirm: 'Confirm',
      };
      return translations[key] || key;
    },
  }),
}));

vi.mock('../CustomGroupButton', () => ({
  default: ({
    rejectTitle,
    approveTitle,
    onReject,
    onApprove,
    size,
    containerCustomClass,
  }: any) => (
    <div data-testid='custom-group-button' data-size={size} className={containerCustomClass}>
      <button data-testid='reject-button' onClick={onReject}>
        {rejectTitle}
      </button>
      <button data-testid='approve-button' onClick={onApprove}>
        {approveTitle}
      </button>
    </div>
  ),
}));

describe('DialogConfirmation', () => {
  let mockClose: ReturnType<typeof vi.fn>;
  let mockOnSave: ReturnType<typeof vi.fn>;

  const renderComponent = (props: any) =>
    render(
      <MantineProvider>
        <DialogConfirmation {...props} />
      </MantineProvider>
    );

  beforeEach(() => {
    mockClose = vi.fn();
    mockOnSave = vi.fn();
  });

  describe('CSS Styles Execution', () => {
    it('should execute createStyles function when rendering component', () => {
      renderComponent({ opened: true, close: mockClose, onSave: mockOnSave });

      expect(screen.getByTestId('icon-alert-circle')).toBeInTheDocument();
      expect(screen.getByTestId('icon-x')).toBeInTheDocument();
    });

    it('should execute styles even when modal is closed', () => {
      renderComponent({ opened: false, close: mockClose, onSave: mockOnSave });

      expect(screen.queryByTestId('icon-alert-circle')).not.toBeInTheDocument();
    });
  });

  describe('Basic Functionality', () => {
    it('should render all content when opened', () => {
      renderComponent({ opened: true, close: mockClose, onSave: mockOnSave });

      expect(screen.getByTestId('icon-alert-circle')).toBeInTheDocument();
      expect(screen.getByTestId('icon-x')).toBeInTheDocument();
      expect(screen.getByText('Save and Publish')).toBeInTheDocument();
      expect(
        screen.getByText('Are you sure you want to save and publish these changes?')
      ).toBeInTheDocument();
      expect(screen.getByTestId('custom-group-button')).toBeInTheDocument();
      expect(screen.getByTestId('reject-button')).toHaveTextContent('Cancel');
      expect(screen.getByTestId('approve-button')).toHaveTextContent('Confirm');
    });

    it('should not render content when closed', () => {
      renderComponent({ opened: false, close: mockClose, onSave: mockOnSave });

      expect(screen.queryByTestId('icon-alert-circle')).not.toBeInTheDocument();
      expect(screen.queryByText('Save and Publish')).not.toBeInTheDocument();
    });

    const interactionTests = [
      { element: 'icon-x', action: 'click', expectClose: true, expectSave: false },
      { element: 'reject-button', action: 'click', expectClose: true, expectSave: false },
      { element: 'approve-button', action: 'click', expectClose: true, expectSave: true },
    ];

    interactionTests.forEach(({ element, action, expectClose, expectSave }) => {
      it(`should handle ${element} ${action}`, async () => {
        renderComponent({ opened: true, close: mockClose, onSave: mockOnSave });

        const targetElement = screen.getByTestId(element);
        await userEvent.click(targetElement);

        expect(mockClose).toHaveBeenCalledTimes(expectClose ? 1 : 0);
        expect(mockOnSave).toHaveBeenCalledTimes(expectSave ? 1 : 0);
      });
    });
  });
});
