import { render, screen } from '@testing-library/react';
import { vi, describe, it, expect, beforeAll, beforeEach } from 'vitest';
import { BrowserRouter } from 'react-router-dom';
import { MantineProvider } from '@mantine/core';
import NavigationControls from './index';

const MOCK_CHATBOX_ID = 'test-chatbox-123';
const NAVIGATION_ITEMS = [
  { route: 'content-setting', iconTestId: 'layout-list', index: 0 },
  { route: 'styling-setting', iconTestId: 'tools', index: 1 },
  { route: 'integration', iconTestId: 'plug-connected', index: 2 },
  { route: 'general-setting', iconTestId: 'settings', index: 3 },
] as const;

const createWrapper = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>
    <MantineProvider>{children}</MantineProvider>
  </BrowserRouter>
);

const renderNavigationControls = () => render(<NavigationControls />, { wrapper: createWrapper });

const generatePaths = (chatboxId: string) =>
  NAVIGATION_ITEMS.map((item) => `/chatbox/${chatboxId}/${item.route}/`);

const mockSettingsContext = (overrides = {}) => ({
  currentChatboxId: '',
  addedChatbox: undefined,
  updatedChatbox: undefined,
  deletedChatbox: undefined,
  currentChatbox: undefined,
  loading: false,
  currentChatBoxClient: { configUrl: '', domId: '', orgId: '' },
  onSaveSettings: vi.fn(),
  setCurrentChatboxId: vi.fn(),
  updateContentSettings: vi.fn(),
  currentSettings: {},
  setCurrentSettings: vi.fn(),
  delOpened: false,
  delOpen: vi.fn(),
  delClose: vi.fn(),
  formOpened: false,
  formOpen: vi.fn(),
  formClose: vi.fn(),
  clickedChatbox: undefined,
  setClickChatbox: vi.fn(),
  handleSavingBox: vi.fn(),
  handleDelete: vi.fn(),
  uploadImage: vi.fn(),
  onSaveStyling: vi.fn(),
  onSaveGeneral: vi.fn(),
  onSaveContent: vi.fn(),
  reloadChatbox: vi.fn(),
  handleResetAddedChatbox: vi.fn(),
  chatboxList: [],
  setChatboxList: vi.fn(),
  LIMITATION_OF_WIDGETS: 0,
  LIMITATION_OF_CHATBOXES_PER_ORGANIZATION: 0,
  onSaveIntegration: vi.fn(),
  ...overrides,
});

const mockLocation = (pathname: string) => ({
  pathname,
  search: '',
  hash: '',
  state: null,
  key: 'default',
});

beforeAll(() => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn(() => ({
      matches: false,
      media: '',
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });
  ['ResizeObserver', 'IntersectionObserver'].forEach((name) => {
    Object.defineProperty(window, name, {
      writable: true,
      value: vi.fn(() => ({ disconnect: vi.fn(), observe: vi.fn(), unobserve: vi.fn() })),
    });
  });
});

vi.mock('@mantine/emotion', () => ({
  createStyles: (styleFunction: (theme: any) => any) => {
    // Execute the style function immediately to cover the CSS lines
    const mockTheme = {
      colors: {
        decaNavy: ['#E8F4FD', '', '', '', '', '#1B4F72'],
        silverFox: ['', '', '', '', '', '', '', '#9CA3AF'],
      },
    };

    // Execute the style function to ensure coverage of lines 14-29
    const styles = styleFunction(mockTheme);

    return () => ({
      classes: { activeIcon: 'active-icon-class', iconStyle: 'icon-style-class' },
      cx: (...args: any[]) =>
        args
          .flat()
          .filter(Boolean)
          .map((arg) =>
            typeof arg === 'string'
              ? arg
              : Object.entries(arg)
                  .filter(([, v]) => v)
                  .map(([k]) => k)
          )
          .flat()
          .join(' '),
    });
  },
}));

vi.mock('@mantine/core', async () => ({
  ...(await vi.importActual('@mantine/core')),
  useMantineTheme: () => ({
    colors: {
      decaNavy: ['#E8F4FD', '', '', '', '', '#1B4F72'],
      silverFox: ['', '', '', '', '', '', '', '#9CA3AF'],
    },
  }),
  useMantineColorScheme: () => ({ colorScheme: 'light', toggleColorScheme: vi.fn() }),
}));

vi.mock('@tabler/icons-react', () =>
  Object.fromEntries(
    ['IconLayoutList', 'IconTools', 'IconPlugConnected', 'IconSettings'].map((name) => [
      name,
      ({ stroke }: { stroke?: number }) => (
        <div
          data-testid={name
            .replace('Icon', '')
            .replace(/([A-Z])/g, '-$1')
            .toLowerCase()
            .slice(1)}
          data-stroke={stroke}
        />
      ),
    ])
  )
);

vi.mock('@resola-ai/ui/hooks', () => ({
  usePathParams: () => ({ createPathWithLngParam: vi.fn((path) => path) }),
}));
vi.mock('@/configs', () => ({ AppConfig: { BASE_PATH: '/chatbox/' } }));
vi.mock('@/contexts/SettingsContext', () => ({ useSettingsContext: vi.fn() }));
vi.mock('react-router-dom', async () => ({
  ...(await vi.importActual('react-router-dom')),
  useLocation: vi.fn(),
}));

const { useSettingsContext } = await import('@/contexts/SettingsContext');
const { useLocation } = await import('react-router-dom');

describe('NavigationControls', () => {
  beforeEach(() => {
    vi.mocked(useSettingsContext).mockReturnValue(
      mockSettingsContext({ currentChatboxId: MOCK_CHATBOX_ID })
    );
    vi.mocked(useLocation).mockReturnValue(
      mockLocation(`/chatbox/${MOCK_CHATBOX_ID}/content-setting/`)
    );
  });

  describe('CSS Styles Execution', () => {
    it('should execute createStyles function when rendering component', () => {
      renderNavigationControls();

      const links = screen.getAllByRole('link');
      const buttons = screen.getAllByRole('button');

      expect(links).toHaveLength(4);
      expect(buttons).toHaveLength(4);
    });

    it('should apply correct CSS classes based on active state', () => {
      const { container } = renderNavigationControls();

      // Check that buttons have the correct classes applied
      const buttons = container.querySelectorAll('button');
      expect(buttons[0]).toHaveClass('active-icon-class'); // First button should be active

      buttons.forEach((button) => {
        expect(button).toHaveClass('icon-style-class');
      });
    });
  });

  describe('Rendering and Structure', () => {
    it('should render all navigation items with correct structure and properties', () => {
      renderNavigationControls();

      const links = screen.getAllByRole('link');
      const buttons = screen.getAllByRole('button');

      expect(links).toHaveLength(4);
      expect(buttons).toHaveLength(4);
      expect(useSettingsContext).toHaveBeenCalled();

      NAVIGATION_ITEMS.forEach(({ iconTestId }, index) => {
        expect(screen.getByTestId(iconTestId)).toHaveAttribute('data-stroke', '2');
        expect(links[index]).toHaveAttribute('href');
        expect(links[index].tabIndex).not.toBe(-1);
        expect(buttons[index].tagName).toBe('BUTTON');
      });
    });

    it('should maintain consistent DOM hierarchy', () => {
      const { container } = renderNavigationControls();

      const stack = container.querySelector('[class*="Stack"]');
      const navLinks = container.querySelectorAll('a');
      const actionIcons = container.querySelectorAll('button');
      const icons = container.querySelectorAll('[data-testid]');

      expect(stack).toBeInTheDocument();
      expect(navLinks).toHaveLength(4);
      expect(actionIcons).toHaveLength(4);
      expect(icons).toHaveLength(4);

      // Verify hierarchy: Stack > NavLink > ActionIcon > Icon
      navLinks.forEach((link, index) => {
        expect(stack).toContainElement(link as HTMLElement);
        expect(link).toContainElement(actionIcons[index] as HTMLElement);
        expect(actionIcons[index]).toContainElement(icons[index] as HTMLElement);
      });
    });
  });
});
