import { render, screen, cleanup } from '@testing-library/react';
import { describe, it, expect, vi, afterEach } from 'vitest';
import { MantineProvider } from '@mantine/core';
import { themeConfigurations } from '@/constants/themeConfiguration';
import ChatboxAvatar from './index';
import { MantineEmotionProvider } from '@mantine/emotion';

vi.mock('@mantine/emotion', async (importOriginal) => {
  const actual = await importOriginal<typeof import('@mantine/emotion')>();
  return {
    ...actual,
    createStyles: vi.fn((styleFunction: (theme: any) => any) => {
      const mockTheme = {
        colors: {
          decaViolet: ['#violet0', '#violet1', '#violet2', '#violet3', '#violet4', '#violet5'],
        },
      };
      const styles = styleFunction(mockTheme);

      return () => ({
        classes: {
          avatar: 'mocked-avatar-class',
        },
        cx: vi.fn((...args) => args.filter(Boolean).join(' ')),
      });
    }),
  };
});

const renderComponent = (props = {}) => {
  const defaultProps = {
    imageUrl: undefined,
  };

  return render(
    <MantineProvider theme={themeConfigurations}>
      <MantineEmotionProvider>
        <ChatboxAvatar {...defaultProps} {...props} />
      </MantineEmotionProvider>
    </MantineProvider>
  );
};

describe('ChatboxAvatar Component', () => {
  afterEach(() => {
    cleanup();
  });
  it('uses provided imageUrl when available', () => {
    const customImageUrl = 'https://example.com/custom-avatar.jpg';

    renderComponent({ imageUrl: customImageUrl });

    const avatarElement = screen.getByAltText('avatar');
    expect(avatarElement).toBeInTheDocument();
    expect(avatarElement).toHaveAttribute('src', customImageUrl);
  });

  it('uses default image when imageUrl is undefined', () => {
    renderComponent({ imageUrl: undefined });

    const avatarElement = screen.getByAltText('avatar');
    expect(avatarElement).toBeInTheDocument();
    expect(avatarElement).toHaveAttribute('src', '/chatbox/svg/default-bot.svg');
  });

  it('uses default image when imageUrl is null', () => {
    renderComponent({ imageUrl: null });

    const avatarElement = screen.getByAltText('avatar');
    expect(avatarElement).toBeInTheDocument();
    expect(avatarElement).toHaveAttribute('src', '/chatbox/svg/default-bot.svg');
  });

  it('uses default image when imageUrl is empty string', () => {
    renderComponent({ imageUrl: '' });

    // When imageUrl is empty, Mantine shows placeholder instead of img
    const avatarContainer = screen.getByTitle('avatar');
    expect(avatarContainer).toBeInTheDocument();

    // Check that the container has the avatar class (line 30 logic)
    const avatarRoot = avatarContainer.closest('.mantine-Avatar-root');
    expect(avatarRoot).toHaveClass('mocked-avatar-class');
  });
});
