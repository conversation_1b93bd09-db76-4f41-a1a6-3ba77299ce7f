import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeAll, beforeEach } from 'vitest';
import { MantineProvider } from '@mantine/core';
import SavePageSettingControl from './index';

// Test setup and mocks
beforeAll(() => {
  // Setup DOM APIs
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(() => ({
      matches: false,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });

  ['ResizeObserver', 'IntersectionObserver'].forEach((name) => {
    Object.defineProperty(window, name, {
      writable: true,
      value: vi.fn().mockImplementation(() => ({
        disconnect: vi.fn(),
        observe: vi.fn(),
        unobserve: vi.fn(),
      })),
    });
  });
});

// Mock dependencies with style execution
vi.mock('@mantine/emotion', () => ({
  createStyles: (styleFunction: (theme: any) => any) => {
    // Execute the style function immediately to cover the CSS lines
    const mockTheme = {
      colors: {
        decaNavy: ['#E8F4FD', '', '', '', '', '#1B4F72', '#0F3A5E'],
        silverFox: ['', '', '#F5F5F5', '', '', '#9CA3AF'],
      },
      spacing: { md: 16 },
    };

    // Execute the style function to ensure coverage of lines 10-30
    const styles = styleFunction(mockTheme);

    return () => ({
      classes: { buttonStyle: 'button-style', container: 'container-class' },
      cx: (...args: any[]) => args.filter(Boolean).join(' '),
    });
  },
}));

vi.mock('@mantine/core', async () => {
  const actual = await vi.importActual('@mantine/core');
  return {
    ...actual,
    useMantineTheme: () => ({
      colors: { decaNavy: ['#E8F4FD', '', '', '', '', '#1B4F72'], silverFox: ['', '', '#F5F5F5'] },
      spacing: { md: 16 },
      primaryColor: 'blue',
    }),
    useMantineColorScheme: () => ({ colorScheme: 'light', toggleColorScheme: vi.fn() }),
  };
});

let mockOpen = vi.fn();
let mockClose = vi.fn();
let mockOpened = false;

vi.mock('@mantine/hooks', () => ({
  useDisclosure: vi.fn(() => [mockOpened, { open: mockOpen, close: mockClose }]),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => ({ saveAndPublish: 'Save and Publish', cancel: 'Cancel' })[key] || key,
  }),
}));

vi.mock('../DialogConfirmation', () => ({
  default: ({ opened, close, onSave }: any) =>
    opened ? (
      <div data-testid='dialog-confirmation'>
        <button data-testid='dialog-close' onClick={close}>
          Close
        </button>
        <button data-testid='dialog-save' onClick={() => onSave?.()}>
          Save
        </button>
      </div>
    ) : null,
}));

describe('SavePageSettingControl', () => {
  let mockOnSave: ReturnType<typeof vi.fn>;
  let mockForm: any;

  const defaultProps = {
    title: 'Test Settings',
    buttonText: 'Save Changes',
    form: null as any,
    onSave: null as any,
  };

  const renderComponent = (overrides = {}) => {
    const props = { ...defaultProps, form: mockForm, onSave: mockOnSave, ...overrides };
    return render(
      <MantineProvider>
        <SavePageSettingControl {...props} />
      </MantineProvider>
    );
  };

  beforeEach(() => {
    mockOnSave = vi.fn();
    mockForm = {
      trigger: vi.fn().mockResolvedValue(true),
      getValues: vi.fn().mockReturnValue({ field1: 'value1', field2: 'value2' }),
    };
    mockOpen = vi.fn();
    mockClose = vi.fn();
    mockOpened = false;
    vi.clearAllMocks();
  });

  // Helper function to get button and verify basic functionality
  const getButton = () => screen.getByRole('button', { name: defaultProps.buttonText });

  describe('CSS Styles Execution', () => {
    it('should execute createStyles function when rendering component', () => {
      renderComponent();

      expect(screen.getByText(defaultProps.title)).toBeInTheDocument();
      expect(getButton()).toBeInTheDocument();
    });

    it('should apply correct CSS classes to elements', () => {
      renderComponent();

      const container = screen.getByText(defaultProps.title).closest('div');
      const button = getButton();

      expect(container).toHaveClass('container-class');
      expect(button).toHaveClass('button-style');
    });
  });

  describe('Basic Rendering', () => {
    it.each([
      { description: 'with all props', overrides: {} },
      { description: 'without form', overrides: { form: undefined } },
      { description: 'without onSave', overrides: { onSave: undefined } },
      {
        description: 'with custom text',
        overrides: { title: 'Custom', buttonText: 'Custom Save' },
      },
    ])('should render correctly $description', ({ overrides }) => {
      renderComponent(overrides);

      const expectedTitle = overrides.title || defaultProps.title;
      const expectedButtonText = overrides.buttonText || defaultProps.buttonText;

      expect(screen.getByText(expectedTitle)).toBeInTheDocument();
      expect(screen.getByText(expectedButtonText)).toBeInTheDocument();
    });

    it('should apply correct CSS classes', () => {
      renderComponent();

      const container = screen.getByText(defaultProps.title).closest('div');
      expect(container).toHaveClass('container-class');
      expect(getButton()).toHaveClass('button-style');
    });
  });

  describe('Form Interaction', () => {
    it.each([
      { validation: true, description: 'when validation passes' },
      { validation: false, description: 'when validation fails' },
    ])('should trigger form validation $description', async ({ validation }) => {
      mockForm.trigger.mockResolvedValue(validation);
      renderComponent();

      await userEvent.click(getButton());
      expect(mockForm.trigger).toHaveBeenCalledTimes(1);
    });

    it('should handle multiple rapid clicks', async () => {
      renderComponent();
      const button = getButton();

      await Promise.all([
        userEvent.click(button),
        userEvent.click(button),
        userEvent.click(button),
      ]);

      expect(mockForm.trigger).toHaveBeenCalledTimes(3);
    });

    it('should work without form prop', async () => {
      renderComponent({ form: undefined });

      await userEvent.click(getButton());
      // Should not throw error
      expect(getButton()).toBeInTheDocument();
    });
  });

  describe('Dialog Behavior', () => {
    it('should not render dialog by default', () => {
      renderComponent();
      expect(screen.queryByTestId('dialog-confirmation')).not.toBeInTheDocument();
    });

    it('should render dialog when opened is true', () => {
      mockOpened = true;
      renderComponent();
      expect(screen.getByTestId('dialog-confirmation')).toBeInTheDocument();
    });

    it.each([
      { hasForm: true, expectedValue: { field1: 'value1', field2: 'value2' } },
      { hasForm: false, expectedValue: undefined },
    ])('should handle dialog save with form: $hasForm', async ({ hasForm, expectedValue }) => {
      mockOpened = true; // Ensure dialog is rendered
      renderComponent({ form: hasForm ? mockForm : undefined });

      const dialogSaveButton = screen.getByTestId('dialog-save');
      await userEvent.click(dialogSaveButton);
      expect(mockOnSave).toHaveBeenCalledWith(expectedValue);
    });

    it('should execute onSave callback function in DialogConfirmation', async () => {
      mockOpened = true; // Ensure dialog is rendered
      renderComponent();

      const dialogSaveButton = screen.getByTestId('dialog-save');
      await userEvent.click(dialogSaveButton);

      expect(mockOnSave).toHaveBeenCalledWith({ field1: 'value1', field2: 'value2' });
      expect(mockForm.getValues).toHaveBeenCalled();
    });

    it('should handle dialog close without triggering save', async () => {
      renderComponent();

      const dialogCloseButton = screen.queryByTestId('dialog-close');
      if (dialogCloseButton) {
        await userEvent.click(dialogCloseButton);
        expect(mockOnSave).not.toHaveBeenCalled();
      }
    });
  });
});
