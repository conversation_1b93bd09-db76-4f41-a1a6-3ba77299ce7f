import { render, screen } from '@testing-library/react';
import { MantineProvider } from '@mantine/core';
import { describe, it, expect, vi } from 'vitest';
import CustomCard from './index';

// Mock @mantine/emotion with style execution
vi.mock('@mantine/emotion', () => ({
  createStyles: (styleFunction: (theme: any) => any) => {
    // Execute the style function immediately to cover the CSS lines
    const mockTheme = {
      colors: {
        silverFox: {
          2: '#f8f9fa',
          5: '#dee2e6',
          9: '#212529',
        },
      },
    };

    // Execute the style function to ensure coverage
    const styles = styleFunction(mockTheme);

    return () => ({
      classes: {
        root: 'mocked-root-class',
        title: 'mocked-title-class',
      },
      cx: (...args: string[]) => args.filter(Boolean).join(' '),
    });
  },
}));

// Mock @mantine/core rem function
vi.mock('@mantine/core', async () => {
  const actual = await vi.importActual('@mantine/core');
  return {
    ...actual,
    rem: (value: number) => `${value}px`, // Simple rem mock that returns px
  };
});

// Helper function to render component with MantineProvider
const renderWithProvider = (component: React.ReactElement) => {
  return render(<MantineProvider>{component}</MantineProvider>);
};

// Helper function to get the CustomCard element specifically
const getCustomCardElement = () => {
  return screen.getByTestId('custom-card');
};

describe('CustomCard', () => {
  describe('Basic Rendering', () => {
    it('should render without crashing when no props are provided', () => {
      renderWithProvider(<CustomCard />);

      const cardElement = getCustomCardElement();
      expect(cardElement).toBeInTheDocument();
      expect(cardElement).toHaveClass('mocked-root-class');
    });

    it('should render with default empty className when className prop is not provided', () => {
      renderWithProvider(<CustomCard />);

      const cardElement = getCustomCardElement();
      expect(cardElement).toHaveClass('mocked-root-class');
    });

    it('should apply custom className along with default classes', () => {
      const customClass = 'custom-test-class';
      renderWithProvider(<CustomCard className={customClass} />);

      const cardElement = getCustomCardElement();
      expect(cardElement).toHaveClass('mocked-root-class', customClass);
    });

    it('should apply multiple custom classes correctly', () => {
      const multipleClasses = 'class-one class-two class-three';
      renderWithProvider(<CustomCard className={multipleClasses} />);

      const cardElement = getCustomCardElement();
      expect(cardElement).toHaveClass('mocked-root-class', multipleClasses);
    });
  });
});
