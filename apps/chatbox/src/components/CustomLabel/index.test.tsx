import { render } from '@testing-library/react';
import { vi, describe, it, expect } from 'vitest';
import { MantineProvider } from '@mantine/core';
import CustomLabel from './index';

vi.mock('@mantine/emotion', () => ({
  createStyles: (styleFunction: any) => {
    const mockTheme = {
      colors: {
        silverFox: Array(10).fill('#666666'),
      },
    };

    try {
      const styles = styleFunction(mockTheme);
      (global as any).lastExecutedStyles = styles;
    } catch (e) {
      console.warn('Style function execution failed:', e);
    }

    return () => ({
      classes: {
        text: 'text-class',
        bold: 'bold-class',
      },
      cx: (...args: any[]) => {
        const classNames: string[] = [];

        args.forEach((arg) => {
          if (typeof arg === 'string') {
            classNames.push(arg);
          } else if (typeof arg === 'object' && arg !== null) {
            Object.entries(arg).forEach(([key, value]) => {
              if (value) {
                classNames.push(key);
              }
            });
          }
        });

        return classNames.join(' ').trim();
      },
    });
  },
}));

const renderComponent = (props = {}) =>
  render(
    <MantineProvider>
      <CustomLabel {...props} />
    </MantineProvider>
  );

describe('CustomLabel', () => {
  describe('Basic Rendering', () => {
    it('should render with default props', () => {
      const { container } = renderComponent();

      const textElement = container.querySelector('.text-class');
      expect(textElement).toBeInTheDocument();
      expect(textElement?.tagName).toBe('P');
    });

    it('should render empty string by default', () => {
      const { container } = renderComponent();

      const textElement = container.querySelector('.text-class');
      expect(textElement).toHaveTextContent('');
    });

    it('should apply base text class', () => {
      const { container } = renderComponent();

      const textElement = container.querySelector('.text-class');
      expect(textElement).toHaveClass('text-class');
    });
  });

  describe('Text Content Rendering', () => {
    const textTestCases = [
      { name: 'simple text', text: 'Hello World' },
      { name: 'numbers', text: '12345' },
      { name: 'special characters', text: '!@#$%^&*()' },
      { name: 'unicode characters', text: '🚀 Hello 世界' },
      { name: 'empty string', text: '' },
      { name: 'single character', text: 'A' },
    ];

    textTestCases.forEach(({ name, text }) => {
      it(`should render ${name}`, () => {
        const { container } = renderComponent({ text });

        const textElement = container.querySelector('.text-class');
        expect(textElement).toBeInTheDocument();
        expect(textElement).toHaveTextContent(text);
      });
    });

    it('should render whitespace (normalized)', () => {
      const { container } = renderComponent({ text: '   ' });

      const textElement = container.querySelector('.text-class');
      // DOM normalizes whitespace, so we just check it renders
      expect(textElement).toBeInTheDocument();
    });

    it('should render multiline text (normalized)', () => {
      const multilineText = 'Line 1\nLine 2\nLine 3';
      const { container } = renderComponent({ text: multilineText });

      const textElement = container.querySelector('.text-class');
      // DOM normalizes newlines to spaces, so we check for normalized content
      expect(textElement).toHaveTextContent('Line 1 Line 2 Line 3');
    });

    it('should render long text', () => {
      const longText = 'A'.repeat(100);
      const { container } = renderComponent({ text: longText });

      const textElement = container.querySelector('.text-class');
      expect(textElement).toHaveTextContent(longText);
    });
  });
});
