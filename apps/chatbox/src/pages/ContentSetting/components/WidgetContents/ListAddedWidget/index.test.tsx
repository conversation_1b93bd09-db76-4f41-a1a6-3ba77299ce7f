import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AllTheProviders } from '@/utils/unitTest';
import { WIDGET_TYPE } from '@/constants/widgetType';
import type { CustomWidget } from '@/types';
import ListAddedWidget from './index';

let capturedOnDragEnd: any = null;

vi.mock('@hello-pangea/dnd', () => ({
  DragDropContext: ({ children, onDragEnd }: any) => {
    // Capture the onDragEnd callback for testing
    capturedOnDragEnd = onDragEnd;
    return (
      <div data-testid='drag-drop-context' data-on-drag-end={!!onDragEnd}>
        {children}
      </div>
    );
  },
  Droppable: ({ children, droppableId }: any) => (
    <div data-testid='droppable' data-droppable-id={droppableId}>
      {children({
        droppableProps: { 'data-droppable-props': true },
        innerRef: vi.fn(),
        placeholder: <div data-testid='droppable-placeholder' />,
      })}
    </div>
  ),
  Draggable: ({ children, draggableId, index }: any) => (
    <div data-testid='draggable' data-draggable-id={draggableId} data-index={index}>
      {children({
        innerRef: vi.fn(),
        draggableProps: { 'data-draggable-props': true },
        dragHandleProps: { 'data-drag-handle-props': true },
      })}
    </div>
  ),
}));

vi.mock('@mantine/core', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    Stack: ({ children, gap, ...props }: any) => (
      <div data-testid='stack' data-gap={gap} {...props}>
        {children}
      </div>
    ),
  };
});

vi.mock('./WrapperAddedWidgets', () => ({
  __esModule: true,
  default: ({ widget }: { widget: CustomWidget }) => (
    <div data-testid='wrapper-added-widgets'>
      <div data-testid='widget-id'>{widget.id}</div>
      <div data-testid='widget-type'>{widget.type}</div>
      <div data-testid='widget-properties'>{JSON.stringify(widget.properties)}</div>
    </div>
  ),
}));

const mockWidgets: CustomWidget[] = [
  {
    id: 'welcome-1',
    type: WIDGET_TYPE.WELCOME_MESSAGE,
    properties: { title: 'Welcome' },
    value: 'welcome-value',
  },
  {
    id: 'cta-1',
    type: WIDGET_TYPE.CALL_TO_ACTION,
    properties: { button_label: 'Click Me' },
    value: 'cta-value',
  },
  {
    id: 'large-img-1',
    type: WIDGET_TYPE.LARGE_IMAGE_LINK,
    properties: { image_title: 'Large Image' },
    value: 'large-img-value',
  },
];

const mockForm = {
  setValue: vi.fn(),
  getValue: vi.fn(),
  watch: vi.fn(),
  handleSubmit: vi.fn(),
};

vi.mock('@/pages/ContentSetting/ContentSettingsContext', () => ({
  useContentSettingsContext: vi.fn(),
}));

// Test utilities
const renderComponent = () => {
  return render(
    <AllTheProviders>
      <ListAddedWidget />
    </AllTheProviders>
  );
};

describe('ListAddedWidget Component', () => {
  beforeEach(async () => {
    vi.clearAllMocks();
    capturedOnDragEnd = null;

    const { useContentSettingsContext } = (await vi.importMock(
      '@/pages/ContentSetting/ContentSettingsContext'
    )) as any;
    useContentSettingsContext.mockReturnValue({
      widgets: mockWidgets,
      form: mockForm,
    });
  });

  describe('Component Structure', () => {
    it('should render with correct drag and drop structure', () => {
      renderComponent();

      expect(screen.getByTestId('drag-drop-context')).toBeInTheDocument();
      expect(screen.getByTestId('droppable')).toBeInTheDocument();
      expect(screen.getByTestId('stack')).toBeInTheDocument();
      expect(screen.getByTestId('droppable-placeholder')).toBeInTheDocument();
    });

    it('should configure drag drop context correctly', () => {
      renderComponent();

      const dragDropContext = screen.getByTestId('drag-drop-context');
      expect(dragDropContext).toHaveAttribute('data-on-drag-end', 'true');

      const droppable = screen.getByTestId('droppable');
      expect(droppable).toHaveAttribute('data-droppable-id', 'droppable_widget');
    });

    it('should render stack with correct gap', () => {
      renderComponent();

      const stack = screen.getByTestId('stack');
      expect(stack).toHaveAttribute('data-gap', 'calc(0.75rem * var(--mantine-scale))');
    });
  });

  describe('Widget Rendering', () => {
    it('should render all widgets from context', () => {
      renderComponent();

      const wrapperWidgets = screen.getAllByTestId('wrapper-added-widgets');
      expect(wrapperWidgets).toHaveLength(3);

      const widgetIds = screen.getAllByTestId('widget-id');
      expect(widgetIds[0]).toHaveTextContent('welcome-1');
      expect(widgetIds[1]).toHaveTextContent('cta-1');
      expect(widgetIds[2]).toHaveTextContent('large-img-1');

      const widgetTypes = screen.getAllByTestId('widget-type');
      expect(widgetTypes[0]).toHaveTextContent(WIDGET_TYPE.WELCOME_MESSAGE);
      expect(widgetTypes[1]).toHaveTextContent(WIDGET_TYPE.CALL_TO_ACTION);
      expect(widgetTypes[2]).toHaveTextContent(WIDGET_TYPE.LARGE_IMAGE_LINK);
    });

    it('should render welcome message widget without drag and drop', () => {
      renderComponent();

      const draggables = screen.getAllByTestId('draggable');
      expect(draggables).toHaveLength(2);

      expect(draggables[0]).toHaveAttribute('data-draggable-id', 'cta-1');
      expect(draggables[1]).toHaveAttribute('data-draggable-id', 'large-img-1');
    });

    it('should render non-welcome message widgets with drag and drop', () => {
      renderComponent();

      const draggables = screen.getAllByTestId('draggable');

      expect(draggables[0]).toHaveAttribute('data-draggable-id', 'cta-1');
      expect(draggables[0]).toHaveAttribute('data-index', '1');

      expect(draggables[1]).toHaveAttribute('data-draggable-id', 'large-img-1');
      expect(draggables[1]).toHaveAttribute('data-index', '2');
    });

    it('should pass correct props to WrapperAddedWidgets', () => {
      renderComponent();

      const widgetProperties = screen.getAllByTestId('widget-properties');
      expect(widgetProperties[0]).toHaveTextContent(JSON.stringify({ title: 'Welcome' }));
      expect(widgetProperties[1]).toHaveTextContent(JSON.stringify({ button_label: 'Click Me' }));
      expect(widgetProperties[2]).toHaveTextContent(JSON.stringify({ image_title: 'Large Image' }));
    });
  });

  describe('Drag and Drop Functionality', () => {
    it('should handle drag end with valid destination', async () => {
      const { useContentSettingsContext } = (await vi.importMock(
        '@/pages/ContentSetting/ContentSettingsContext'
      )) as any;

      // Mock the context with a fresh mock function
      const mockSetValue = vi.fn();
      useContentSettingsContext.mockReturnValue({
        widgets: mockWidgets,
        form: { setValue: mockSetValue },
      });

      renderComponent();

      // Verify onDragEnd callback is captured
      expect(capturedOnDragEnd).toBeDefined();
      expect(typeof capturedOnDragEnd).toBe('function');

      // Test the actual handleDragEnd function by simulating a drag result
      const mockDragResult = {
        source: { index: 1 },
        destination: { index: 2 },
        draggableId: 'cta-1',
        type: 'DEFAULT',
        reason: 'DROP' as const,
        mode: 'FLUID' as const,
        combine: null,
      };

      // Call the captured onDragEnd callback
      capturedOnDragEnd(mockDragResult);

      // Verify form.setValue was called with reordered items
      expect(mockSetValue).toHaveBeenCalledWith('widgetList', expect.any(Array));
      const calledWith = mockSetValue.mock.calls[0][1];
      expect(calledWith).toHaveLength(3);
      // Verify the items were reordered (moved item from index 1 to index 2)
      expect(calledWith[0]).toEqual(mockWidgets[0]); // welcome-1 stays at 0
      expect(calledWith[1]).toEqual(mockWidgets[2]); // large-img-1 moved to 1
      expect(calledWith[2]).toEqual(mockWidgets[1]); // cta-1 moved to 2
    });

    it('should not modify list when drag has no destination', async () => {
      const { useContentSettingsContext } = (await vi.importMock(
        '@/pages/ContentSetting/ContentSettingsContext'
      )) as any;

      // Mock the context with a fresh mock function
      const mockSetValue = vi.fn();
      useContentSettingsContext.mockReturnValue({
        widgets: mockWidgets,
        form: { setValue: mockSetValue },
      });

      renderComponent();

      // Test the handleDragEnd function with no destination
      const mockDragResult = {
        source: { index: 1 },
        destination: null,
        draggableId: 'cta-1',
        type: 'DEFAULT',
        reason: 'CANCEL' as const,
        mode: 'FLUID' as const,
        combine: null,
      };

      // Call the captured onDragEnd callback with no destination
      capturedOnDragEnd(mockDragResult);

      // The handleDragEnd function should return early if no destination
      expect(mockSetValue).not.toHaveBeenCalled();
    });

    it('should handle state updates when widgets change', async () => {
      const { useContentSettingsContext } = (await vi.importMock(
        '@/pages/ContentSetting/ContentSettingsContext'
      )) as any;

      const mockSetValue = vi.fn();
      useContentSettingsContext.mockReturnValue({
        widgets: mockWidgets,
        form: { setValue: mockSetValue },
      });

      const { rerender } = renderComponent();

      // Change the widgets to trigger useEffect
      const newWidgets = [
        ...mockWidgets,
        {
          id: 'new-widget',
          type: WIDGET_TYPE.CALL_TO_ACTION,
          properties: { button_label: 'New Button' },
          value: 'new-value',
        },
      ];

      useContentSettingsContext.mockReturnValue({
        widgets: newWidgets,
        form: { setValue: mockSetValue },
      });

      // Rerender to trigger useEffect
      rerender(
        <AllTheProviders>
          <ListAddedWidget />
        </AllTheProviders>
      );

      // Verify the new widget is rendered
      const wrapperWidgets = screen.getAllByTestId('wrapper-added-widgets');
      expect(wrapperWidgets).toHaveLength(4);
    });

    it('should handle empty widgets array', async () => {
      const { useContentSettingsContext } = (await vi.importMock(
        '@/pages/ContentSetting/ContentSettingsContext'
      )) as any;

      useContentSettingsContext.mockReturnValue({
        widgets: [],
        form: mockForm,
      });

      renderComponent();

      // Should render drag drop context but no widgets
      expect(screen.getByTestId('drag-drop-context')).toBeInTheDocument();
      expect(screen.getByTestId('stack')).toBeInTheDocument();
      expect(screen.queryByTestId('wrapper-added-widgets')).not.toBeInTheDocument();
    });

    it('should handle null widgets', async () => {
      const { useContentSettingsContext } = (await vi.importMock(
        '@/pages/ContentSetting/ContentSettingsContext'
      )) as any;

      useContentSettingsContext.mockReturnValue({
        widgets: null,
        form: mockForm,
      });

      renderComponent();

      // Should render drag drop context but no widgets
      expect(screen.getByTestId('drag-drop-context')).toBeInTheDocument();
      expect(screen.getByTestId('stack')).toBeInTheDocument();
      expect(screen.queryByTestId('wrapper-added-widgets')).not.toBeInTheDocument();
    });
  });
});
