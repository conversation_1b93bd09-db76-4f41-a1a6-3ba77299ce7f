import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AllTheProviders } from '@/utils/unitTest';
import { WIDGET_TYPE } from '@/constants/widgetType';
import type { CustomWidget } from '@/types';
import WrapperAddedWidgets from './index';

// Consolidated mock setup using vi.hoisted
const { mockOnSaveWidget, mockUseSettingsContext, mockMapStylingToChatBoxUIAppProps } = vi.hoisted(
  () => {
    const mockOnSaveWidget = vi.fn();
    const mockUseSettingsContext = vi.fn();
    const mockMapStylingToChatBoxUIAppProps = vi.fn();

    return {
      mockOnSaveWidget,
      mockUseSettingsContext,
      mockMapStylingToChatBoxUIAppProps,
    };
  }
);

// Mock dependencies
vi.mock('@mantine/core', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    Stack: ({ children, gap, p, className, ...props }: any) => (
      <div data-testid='stack' data-gap={gap} data-padding={p} className={className} {...props}>
        {children}
      </div>
    ),
    Flex: ({ children, justify, ...props }: any) => (
      <div data-testid='flex' data-justify={justify} {...props}>
        {children}
      </div>
    ),
    useMantineTheme: () => ({
      colors: {
        silverFox: ['#fff', '#f8f9fa', '#e9ecef', '#dee2e6', '#ced4da', '#adb5bd'],
      },
    }),
  };
});

vi.mock('@mantine/emotion', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    createStyles: vi.fn((styleFunction: (theme?: any) => any) => {
      // Execute the style function to cover CSS lines (13-28)
      const mockTheme = {
        colors: {
          silverFox: ['#fff', '#f8f9fa', '#e9ecef', '#dee2e6', '#ced4da', '#adb5bd'],
        },
      };
      const styles = styleFunction(mockTheme);
      return () => ({
        classes: {
          container: 'mocked-container-class',
          iconCustom: 'mocked-icon-custom-class',
          customTitle: 'mocked-custom-title-class',
          customWidget: 'mocked-custom-widget-class',
        },
      });
    }),
  };
});

vi.mock('../../CommonTitle', () => ({
  __esModule: true,
  default: ({ type, customTitle }: any) => (
    <div data-testid='common-title'>
      <div data-testid='widget-type'>{type}</div>
      <div data-testid='custom-title-class'>{customTitle}</div>
    </div>
  ),
}));

vi.mock('../GroupIconControls', () => ({
  __esModule: true,
  default: ({ widget }: any) => (
    <div data-testid='group-icon-controls'>
      <div data-testid='widget-id'>{widget.id}</div>
      <div data-testid='widget-type'>{widget.type}</div>
    </div>
  ),
}));

vi.mock('../../WidgetUIComponent', () => ({
  __esModule: true,
  default: ({
    widgetType,
    customWidget,
    properties,
    widget,
    styleWidgetContext,
    onSaveWidget,
  }: any) => (
    <div data-testid='widget-ui-component'>
      <div data-testid='widget-type'>{widgetType}</div>
      <div data-testid='custom-widget-class'>{customWidget}</div>
      <div data-testid='widget-properties'>{JSON.stringify(properties)}</div>
      <div data-testid='widget-id'>{widget.id}</div>
      <div data-testid='style-widget-context'>{JSON.stringify(styleWidgetContext)}</div>
      <button data-testid='save-widget' onClick={() => onSaveWidget({ test: 'data' })}>
        Save Widget
      </button>
    </div>
  ),
}));

vi.mock('@/components/ChatBoxPreview/mapping', () => ({
  mapStylingToChatBoxUIAppProps: mockMapStylingToChatBoxUIAppProps,
}));

vi.mock('@/pages/ContentSetting/ContentSettingsContext', () => ({
  useContentSettingsContext: () => ({
    onSaveWidget: mockOnSaveWidget,
  }),
}));

vi.mock('@/contexts/SettingsContext', () => ({
  useSettingsContext: mockUseSettingsContext,
}));

const createMockWidget = (type: string, overrides: Partial<CustomWidget> = {}): CustomWidget => ({
  id: 'test-widget-id',
  type,
  properties: {
    title: 'Test Title',
    description: 'Test Description',
  },
  value: 'test-value',
  ...overrides,
});

const createMockSettings = (overrides = {}) => ({
  stylingSettings: {
    colorSettings: {
      primaryColor: '#007bff',
    },
  },
  ...overrides,
});

const renderComponent = (widget: CustomWidget) => {
  return render(
    <AllTheProviders>
      <WrapperAddedWidgets widget={widget} />
    </AllTheProviders>
  );
};

describe('WrapperAddedWidgets Component', () => {
  const mockCurrentChatboxId = 'test-chatbox-id';
  const mockChatBoxUISettings = {
    colorSettings: {
      buttonBackground: '#007bff',
      buttonText: '#ffffff',
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();

    mockUseSettingsContext.mockReturnValue({
      currentSettings: createMockSettings(),
      currentChatboxId: mockCurrentChatboxId,
    });

    mockMapStylingToChatBoxUIAppProps.mockReturnValue(mockChatBoxUISettings);
  });

  describe('Component Structure and Styling', () => {
    it('renders with correct structure and CSS classes', () => {
      const widget = createMockWidget(WIDGET_TYPE.CALL_TO_ACTION);
      renderComponent(widget);

      const stack = screen.getByTestId('stack');
      const flex = screen.getByTestId('flex');

      expect(stack).toBeInTheDocument();
      expect(stack).toHaveClass('mocked-container-class');
      expect(stack).toHaveAttribute('data-gap', 'md');
      expect(stack).toHaveAttribute('data-padding', 'calc(0.75rem * var(--mantine-scale))');

      expect(flex).toBeInTheDocument();
      expect(flex).toHaveAttribute('data-justify', 'space-between');
    });

    it('renders all child components with correct props', () => {
      const widget = createMockWidget(WIDGET_TYPE.WELCOME_MESSAGE, {
        properties: { customProp: 'customValue' },
      });
      renderComponent(widget);

      // CommonTitle
      const commonTitle = screen.getByTestId('common-title');
      expect(commonTitle).toBeInTheDocument();
      const commonTitleWidgetType = commonTitle.querySelector('[data-testid="widget-type"]');
      expect(commonTitleWidgetType).toHaveTextContent(WIDGET_TYPE.WELCOME_MESSAGE);
      expect(screen.getByTestId('custom-title-class')).toHaveTextContent(
        'mocked-custom-title-class'
      );

      // GroupIconControls
      const groupIconControls = screen.getByTestId('group-icon-controls');
      expect(groupIconControls).toBeInTheDocument();
      const groupIconWidgetId = groupIconControls.querySelector('[data-testid="widget-id"]');
      expect(groupIconWidgetId).toHaveTextContent('test-widget-id');

      // WidgetUIComponent
      const widgetUIComponent = screen.getByTestId('widget-ui-component');
      expect(widgetUIComponent).toBeInTheDocument();
      expect(screen.getByTestId('custom-widget-class')).toHaveTextContent(
        'mocked-custom-widget-class'
      );
      expect(screen.getByTestId('widget-properties')).toHaveTextContent(
        JSON.stringify({ customProp: 'customValue' })
      );
    });

    it('has correct displayName', () => {
      expect(WrapperAddedWidgets.displayName).toBe('WrapperAddedWidgets');
    });
  });

  describe('Context Integration and Data Flow', () => {
    it('handles missing stylingSettings gracefully', () => {
      mockUseSettingsContext.mockReturnValue({
        currentSettings: null,
        currentChatboxId: mockCurrentChatboxId,
      });

      const widget = createMockWidget(WIDGET_TYPE.CALL_TO_ACTION);
      expect(() => renderComponent(widget)).not.toThrow();

      expect(mockMapStylingToChatBoxUIAppProps).toHaveBeenCalledWith(mockCurrentChatboxId, {});
    });

    it('passes correct styleWidgetContext to WidgetUIComponent', () => {
      const widget = createMockWidget(WIDGET_TYPE.CALL_TO_ACTION);
      renderComponent(widget);

      const styleWidgetContext = JSON.parse(
        screen.getByTestId('style-widget-context').textContent || '{}'
      );

      expect(styleWidgetContext).toEqual({
        widgetTemplates: {
          commonStyle: {
            backgroundColor: '#fff',
            borderColor: '#adb5bd',
          },
          callToAction: {
            backgroundColor: '#007bff',
            hoverBackgroundColor: '#007bff',
            color: '#ffffff',
          },
        },
      });
    });

    it('handles onSaveWidget callback correctly', () => {
      const widget = createMockWidget(WIDGET_TYPE.CALL_TO_ACTION);
      renderComponent(widget);

      const saveButton = screen.getByTestId('save-widget');
      fireEvent.click(saveButton);

      expect(mockOnSaveWidget).toHaveBeenCalledWith({ test: 'data' });
    });
  });
});
