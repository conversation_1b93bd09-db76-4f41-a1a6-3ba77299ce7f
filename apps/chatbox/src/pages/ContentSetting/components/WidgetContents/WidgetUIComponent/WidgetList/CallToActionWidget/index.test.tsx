import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AllTheProviders } from '@/utils/unitTest';
import CallToActionWidget from './index';

vi.mock('@mantine/core', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    rem: vi.fn((value: string) => `${value}rem`),
    Container: ({ children, fluid, className, ...props }: any) => (
      <div data-testid='container' data-fluid={fluid} className={className} {...props}>
        {children}
      </div>
    ),
    useMantineTheme: () => ({
      colors: {
        decaNavy: ['#navy0', '#navy1', '#navy2', '#navy3', '#navy4', '#navy5'],
        silverFox: ['#silver0', '#silver1', '#silver2', '#silver3', '#silver4', '#silver5'],
      },
    }),
  };
});

vi.mock('@mantine/emotion', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    createStyles: vi.fn((styleFunction: (theme: any) => any) => {
      const mockTheme = {};

      const styles = styleFunction(mockTheme);

      return () => ({
        classes: {
          container: 'mocked-container-class',
        },
        cx: vi.fn((...args) => args.filter(Boolean).join(' ')),
      });
    }),
  };
});

const mockTranslate = vi.fn();
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: mockTranslate,
  }),
}));

vi.mock('@resola-ai/widget-engine', () => ({
  Engine: ({ data, context, config, ...props }: any) => (
    <div data-testid='widget-engine' {...props}>
      <div data-testid='engine-data'>{JSON.stringify(data)}</div>
      <div data-testid='engine-context'>{JSON.stringify(context)}</div>
      <div data-testid='engine-config'>{JSON.stringify(config)}</div>
    </div>
  ),
}));

vi.mock('@/components/ChatBoxUI/App/Widgets/templates', () => ({
  getGeneralWidgetTemplates: vi.fn(),
}));

const renderComponent = (props = {}) => {
  const defaultProps = {
    customWidget: undefined,
    properties: undefined,
    widgetData: undefined,
    styleWidgetContext: undefined,
    engineConfig: undefined,
  };

  return render(
    <AllTheProviders>
      <CallToActionWidget {...defaultProps} {...props} />
    </AllTheProviders>
  );
};

describe('CallToActionWidget Component', () => {
  beforeEach(async () => {
    vi.clearAllMocks();

    // Default translation mock
    mockTranslate.mockImplementation((key: string) => {
      const translations: Record<string, string> = {
        callToActionDefaultLabel: 'Default Action Label',
      };
      return translations[key] || key;
    });

    // Default template mock
    const { getGeneralWidgetTemplates } = (await vi.importMock(
      '@/components/ChatBoxUI/App/Widgets/templates'
    )) as any;
    getGeneralWidgetTemplates.mockReturnValue({
      type: 'callToActionButton',
      data: 'mocked-template-data',
    });
  });

  describe('CSS Styles Execution', () => {
    it('should execute createStyles function when rendering component', () => {
      renderComponent();

      expect(screen.getByTestId('container')).toBeInTheDocument();
    });
  });

  describe('Component Structure', () => {
    it('should render with correct structure', () => {
      renderComponent();

      expect(screen.getByTestId('container')).toBeInTheDocument();
      expect(screen.getByTestId('widget-engine')).toBeInTheDocument();
    });

    it('should apply correct container styling', () => {
      renderComponent();

      const container = screen.getByTestId('container');
      expect(container).toHaveAttribute('data-fluid', 'true');
      expect(container).toHaveClass('mocked-container-class');
    });

    it('should apply custom widget class when provided', () => {
      const customWidget = 'custom-widget-class';
      renderComponent({ customWidget });

      const container = screen.getByTestId('container');
      expect(container).toHaveClass('mocked-container-class custom-widget-class');
    });

    it('should not apply custom widget class when not provided', () => {
      renderComponent();

      const container = screen.getByTestId('container');
      expect(container).toHaveClass('mocked-container-class');
      expect(container.className).not.toContain('undefined');
    });
  });

  describe('Data Processing and Props', () => {
    it('should handle properties prop with all values', () => {
      const properties = {
        id: 'test-id',
        label: 'Test Label',
        link: 'https://example.com',
        behavior: 'openInNewTab',
        clickedEvent: 'test-event',
      };

      renderComponent({ properties });

      const engineData = screen.getByTestId('engine-data');
      expect(engineData).toBeInTheDocument();
    });

    it('should handle properties prop with missing values and use defaults', () => {
      const properties = {
        id: 'test-id',
      };

      renderComponent({ properties });

      const engineData = screen.getByTestId('engine-data');
      expect(engineData).toBeInTheDocument();
    });

    it('should handle empty properties prop', () => {
      renderComponent({ properties: {} });

      const engineData = screen.getByTestId('engine-data');
      expect(engineData).toBeInTheDocument();
    });

    it('should handle undefined properties prop', () => {
      renderComponent({ properties: undefined });

      const engineData = screen.getByTestId('engine-data');
      expect(engineData).toBeInTheDocument();
    });

    it('should handle widgetData prop when provided', () => {
      const widgetData = {
        id: 'widget-id',
        text: 'Widget Text',
        href: 'https://widget.com',
        behavior: 'moveToUrl',
        target: '_blank',
        clickedEvent: 'widget-event',
      };

      renderComponent({ widgetData });

      const engineData = screen.getByTestId('engine-data');
      expect(engineData).toBeInTheDocument();
    });

    it('should handle styleWidgetContext prop when provided', () => {
      const styleWidgetContext = {
        widgetTemplates: {
          callToAction: {
            backgroundColor: '#custom-bg',
            hoverBackgroundColor: '#custom-hover',
            color: '#custom-color',
          },
        },
      };

      renderComponent({ styleWidgetContext });

      const engineContext = screen.getByTestId('engine-context');
      expect(engineContext).toBeInTheDocument();
    });

    it('should handle engineConfig prop when provided', () => {
      const engineConfig = {
        debug: true,
        theme: 'custom',
      };

      renderComponent({ engineConfig });

      const engineConfigElement = screen.getByTestId('engine-config');
      expect(engineConfigElement).toHaveTextContent(JSON.stringify(engineConfig));
    });

    it('should handle all props together', () => {
      const properties = {
        id: 'full-test-id',
        label: 'Full Test Label',
        link: 'https://full-test.com',
        behavior: 'openInNewTab',
        clickedEvent: 'full-test-event',
      };

      const widgetData = {
        id: 'override-id',
        text: 'Override Text',
        href: 'https://override.com',
        behavior: 'moveToUrl',
        target: '_self',
        clickedEvent: 'override-event',
      };

      const styleWidgetContext = {
        widgetTemplates: {
          callToAction: {
            backgroundColor: '#override-bg',
            hoverBackgroundColor: '#override-hover',
            color: '#override-color',
          },
        },
      };

      const engineConfig = {
        debug: false,
        theme: 'override',
      };

      const customWidget = 'full-custom-class';

      renderComponent({
        properties,
        widgetData,
        styleWidgetContext,
        engineConfig,
        customWidget,
      });

      expect(screen.getByTestId('container')).toHaveClass(
        'mocked-container-class full-custom-class'
      );
      expect(screen.getByTestId('widget-engine')).toBeInTheDocument();
      expect(screen.getByTestId('engine-data')).toBeInTheDocument();
      expect(screen.getByTestId('engine-context')).toBeInTheDocument();
      expect(screen.getByTestId('engine-config')).toHaveTextContent(JSON.stringify(engineConfig));
    });
  });
});
