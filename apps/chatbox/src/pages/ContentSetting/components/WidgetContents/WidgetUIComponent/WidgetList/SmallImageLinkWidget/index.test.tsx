import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AllTheProviders } from '@/utils/unitTest';
import SmallImageLinkWidget from './index';

// Consolidated mock setup using vi.hoisted
const { mockGetGeneralWidgetTemplates } = vi.hoisted(() => {
  const mockGetGeneralWidgetTemplates = vi.fn();
  return { mockGetGeneralWidgetTemplates };
});

// Mock dependencies
vi.mock('@/components/ChatBoxUI/App/Widgets/templates', () => ({
  getGeneralWidgetTemplates: mockGetGeneralWidgetTemplates,
}));

vi.mock('@mantine/core', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    rem: vi.fn((value: string) => `${value}rem`),
    Container: ({ children, fluid, className, ...props }: any) => (
      <div data-testid='container' data-fluid={fluid} className={className} {...props}>
        {children}
      </div>
    ),
  };
});

vi.mock('@mantine/emotion', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    createStyles: vi.fn((styleFunction: (theme?: any) => any) => {
      // Execute the style function to cover CSS lines (11-16)
      const styles = styleFunction();

      return () => ({
        classes: {
          container: 'mocked-container-class',
        },
        cx: vi.fn((...args) => args.filter(Boolean).join(' ')),
      });
    }),
  };
});

vi.mock('@resola-ai/widget-engine', () => ({
  Engine: ({ data, context, config, ...props }: any) => (
    <div data-testid='widget-engine' {...props}>
      <div data-testid='engine-data'>{JSON.stringify(data)}</div>
      <div data-testid='engine-context'>{JSON.stringify(context)}</div>
      <div data-testid='engine-config'>{JSON.stringify(config)}</div>
    </div>
  ),
}));

describe('SmallImageLinkWidget', () => {
  const createMockProps = (overrides = {}) => ({
    customWidget: undefined,
    widgetData: undefined,
    commonStyle: undefined,
    styleWidgetContext: undefined,
    properties: undefined,
    engineConfig: undefined,
    ...overrides,
  });

  const renderComponent = (props = {}) => {
    const finalProps = createMockProps(props);
    return render(
      <AllTheProviders>
        <SmallImageLinkWidget {...finalProps} />
      </AllTheProviders>
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockGetGeneralWidgetTemplates.mockReturnValue({
      type: 'smallImageLink',
      data: 'mocked-template-data',
    });
  });

  describe('Component Structure', () => {
    it('renders with correct basic structure', () => {
      renderComponent();

      expect(screen.getByTestId('container')).toBeInTheDocument();
      expect(screen.getByTestId('widget-engine')).toBeInTheDocument();
    });

    it('applies correct container styling and props', () => {
      renderComponent();

      const container = screen.getByTestId('container');
      expect(container).toHaveAttribute('data-fluid', 'true');
      expect(container).toHaveClass('mocked-container-class');
    });

    it('applies custom widget class when provided', () => {
      const customWidget = 'custom-small-image-class';
      renderComponent({ customWidget });

      const container = screen.getByTestId('container');
      expect(container).toHaveClass('mocked-container-class custom-small-image-class');
    });

    it('handles undefined customWidget gracefully', () => {
      renderComponent({ customWidget: undefined });

      const container = screen.getByTestId('container');
      expect(container).toHaveClass('mocked-container-class');
      expect(container.className).not.toContain('undefined');
    });

    it('handles null customWidget gracefully', () => {
      renderComponent({ customWidget: null });

      const container = screen.getByTestId('container');
      expect(container).toHaveClass('mocked-container-class');
      expect(container.className).not.toContain('null');
    });

    it('processes properties into initData correctly', () => {
      const properties = {
        imageSource: 'test-image.jpg',
        title: 'Test Title',
        description: 'Test Description',
        link: 'https://test.com',
      };

      renderComponent({ properties });

      const expectedInitData = {
        src: 'test-image.jpg',
        title: 'Test Title',
        description: 'Test Description',
        href: 'https://test.com',
        target: '',
      };

      expect(mockGetGeneralWidgetTemplates).toHaveBeenCalledWith(
        expect.objectContaining(expectedInitData),
        'smallImageLink',
        expect.objectContaining(expectedInitData)
      );
    });

    it('uses default values when properties are undefined', () => {
      renderComponent({ properties: undefined });

      const expectedInitData = {
        src: '',
        title: '',
        description: '',
        href: '',
        target: '',
      };

      expect(mockGetGeneralWidgetTemplates).toHaveBeenCalledWith(
        expect.objectContaining(expectedInitData),
        'smallImageLink',
        expect.objectContaining(expectedInitData)
      );
    });

    it('handles partial properties correctly', () => {
      const properties = {
        imageSource: 'partial-image.jpg',
        title: 'Partial Title',
        // description and link are missing
      };

      renderComponent({ properties });

      const expectedInitData = {
        src: 'partial-image.jpg',
        title: 'Partial Title',
        description: '',
        href: '',
        target: '',
      };

      expect(mockGetGeneralWidgetTemplates).toHaveBeenCalledWith(
        expect.objectContaining(expectedInitData),
        'smallImageLink',
        expect.objectContaining(expectedInitData)
      );
    });

    it('uses widgetData when provided instead of initData', () => {
      const widgetData = {
        src: 'widget-image.jpg',
        title: 'Widget Title',
        description: 'Widget Description',
        href: 'https://widget.com',
        target: '_blank',
      };

      const properties = {
        imageSource: 'ignored-image.jpg',
        title: 'Ignored Title',
        description: 'Ignored Description',
        link: 'https://ignored.com',
      };

      renderComponent({ widgetData, properties });

      expect(mockGetGeneralWidgetTemplates).toHaveBeenCalledWith(
        expect.objectContaining(widgetData),
        'smallImageLink',
        expect.objectContaining(widgetData)
      );
    });
  });
});
