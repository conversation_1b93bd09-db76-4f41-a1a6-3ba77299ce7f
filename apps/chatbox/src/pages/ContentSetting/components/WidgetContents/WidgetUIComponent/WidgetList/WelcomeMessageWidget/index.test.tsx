import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AllTheProviders } from '@/utils/unitTest';
import WelcomeMessageWidget from './index';

vi.mock('@mantine/core', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    rem: vi.fn((value: string) => `${value}rem`),
    Container: ({ children, fluid, className, ...props }: any) => (
      <div data-testid='container' data-fluid={fluid} className={className} {...props}>
        {children}
      </div>
    ),
  };
});

vi.mock('@mantine/emotion', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    createStyles: (styleFunction: (theme: any) => any) => {
      const mockTheme = {
        colors: {
          silverFox: [
            '#silverFox0',
            '#silverFox1',
            '#silverFox2',
            '#silverFox3',
            '#silverFox4',
            '#silverFox5',
            '#silverFox6',
            '#silverFox7',
            '#silverFox8',
            '#silverFox9',
          ],
        },
      };

      const styles = styleFunction(mockTheme);

      return () => ({
        classes: {
          container: 'mocked-welcome-container-class',
          inputTextArea: 'mocked-input-textarea-class',
        },
        cx: vi.fn((...args) => args.filter(Boolean).join(' ')),
      });
    },
  };
});

vi.mock('@resola-ai/widget-engine', () => ({
  Engine: ({ data, context, config, ...props }: any) => (
    <div data-testid='widget-engine' {...props}>
      <div data-testid='engine-data'>{JSON.stringify(data)}</div>
      <div data-testid='engine-context'>{JSON.stringify(context)}</div>
      <div data-testid='engine-config'>{JSON.stringify(config)}</div>
    </div>
  ),
}));

vi.mock('@/components/ChatBoxUI/App/Widgets/templates', () => ({
  getGeneralWidgetTemplates: vi.fn(),
}));

vi.mock('@/utils', () => ({
  replaceLineBreaks: vi.fn((text: string) => text?.replace(/\n/g, '<br/>') || ''),
}));

vi.mock('react-hook-form-mantine', () => ({
  Textarea: ({ name, control, autosize, minRows, classNames, onChange, ...props }: any) => (
    <textarea
      data-testid='form-textarea'
      data-name={name}
      data-autosize={autosize}
      data-minrows={minRows}
      className={classNames?.input}
      onChange={onChange}
      {...props}
    />
  ),
}));

vi.mock('react-hook-form', () => ({
  useForm: vi.fn(),
}));

const renderComponent = (props = {}) => {
  const defaultProps = {
    widgetData: undefined,
    customClassWelcomeWidget: undefined,
    commonStyle: undefined,
    styleWidgetContext: undefined,
    properties: undefined,
    editMode: false,
    widget: undefined,
    onSaveWidget: undefined,
    engineConfig: undefined,
  };

  return render(
    <AllTheProviders>
      <WelcomeMessageWidget {...defaultProps} {...props} />
    </AllTheProviders>
  );
};

describe('WelcomeMessageWidget Component', () => {
  const mockFormReturn = {
    control: { name: 'mocked-control' },
    formState: { errors: {} },
    handleSubmit: vi.fn(),
    reset: vi.fn(),
    setValue: vi.fn(),
    getValues: vi.fn(),
    watch: vi.fn(),
  };

  beforeEach(async () => {
    vi.clearAllMocks();

    const { useForm } = (await vi.importMock('react-hook-form')) as any;
    useForm.mockReturnValue(mockFormReturn);

    const { getGeneralWidgetTemplates } = (await vi.importMock(
      '@/components/ChatBoxUI/App/Widgets/templates'
    )) as any;
    getGeneralWidgetTemplates.mockReturnValue({
      type: 'welcomeMessage',
      data: 'mocked-welcome-template-data',
    });
  });

  describe('Component Structure - Display Mode', () => {
    it('should render display mode with correct structure', () => {
      renderComponent({ editMode: false });

      expect(screen.getByTestId('container')).toBeInTheDocument();
      expect(screen.getByTestId('widget-engine')).toBeInTheDocument();
      expect(screen.queryByTestId('form-textarea')).not.toBeInTheDocument();
    });

    it('should apply correct container styling in display mode', () => {
      renderComponent({ editMode: false });

      const container = screen.getByTestId('container');
      expect(container).toHaveAttribute('data-fluid', 'true');
      expect(container).toHaveClass('mocked-welcome-container-class');
    });

    it('should apply custom welcome widget class when provided', () => {
      const customClassWelcomeWidget = 'custom-welcome-class';
      renderComponent({ customClassWelcomeWidget, editMode: false });

      const container = screen.getByTestId('container');
      expect(container).toHaveClass('mocked-welcome-container-class custom-welcome-class');
    });

    it('should not apply custom welcome widget class when not provided', () => {
      renderComponent({ editMode: false });

      const container = screen.getByTestId('container');
      expect(container).toHaveClass('mocked-welcome-container-class');
      expect(container.className).not.toContain('undefined');
    });
  });

  describe('Component Structure - Edit Mode', () => {
    it('should render edit mode with form textarea', () => {
      renderComponent({ editMode: true });

      expect(screen.getByTestId('form-textarea')).toBeInTheDocument();
      expect(screen.queryByTestId('container')).not.toBeInTheDocument();
      expect(screen.queryByTestId('widget-engine')).not.toBeInTheDocument();
    });

    it('should configure textarea correctly in edit mode', () => {
      renderComponent({ editMode: true });

      const textarea = screen.getByTestId('form-textarea');
      expect(textarea).toHaveAttribute('data-name', 'title');
      expect(textarea).toHaveAttribute('data-autosize', 'true');
      expect(textarea).toHaveAttribute('data-minrows', '1');
      expect(textarea).toHaveClass('mocked-input-textarea-class');
    });

    it('should use form control in edit mode', async () => {
      const { useForm } = (await vi.importMock('react-hook-form')) as any;

      renderComponent({ editMode: true });

      expect(useForm).toHaveBeenCalledWith({
        defaultValues: {
          title: undefined, // No widget provided
        },
      });
    });

    it('should initialize form with widget properties', async () => {
      const { useForm } = (await vi.importMock('react-hook-form')) as any;
      const widget = {
        id: 'test-widget-id',
        type: 'WELCOME_MESSAGE',
        properties: {
          title: 'Initial Welcome Message',
        },
      };

      renderComponent({ editMode: true, widget });

      expect(useForm).toHaveBeenCalledWith({
        defaultValues: {
          title: 'Initial Welcome Message',
        },
      });
    });
  });

  describe('Event Handling', () => {
    it('should call onSaveWidget when textarea value changes', () => {
      const mockOnSaveWidget = vi.fn();
      const widget = {
        id: 'test-widget-id',
        type: 'WELCOME_MESSAGE',
        properties: {
          title: 'Initial Message',
        },
      };

      renderComponent({
        editMode: true,
        widget,
        onSaveWidget: mockOnSaveWidget,
      });

      const textarea = screen.getByTestId('form-textarea');
      const newValue = 'Updated Welcome Message';

      // Simulate change event using fireEvent
      fireEvent.change(textarea, { target: { value: newValue } });

      expect(mockOnSaveWidget).toHaveBeenCalledWith({
        ...widget.properties,
        title: newValue,
        type: widget.type,
        id: widget.id,
      });
    });

    it('should not call onSaveWidget when no widget is provided', () => {
      const mockOnSaveWidget = vi.fn();

      renderComponent({
        editMode: true,
        widget: undefined,
        onSaveWidget: mockOnSaveWidget,
      });

      const textarea = screen.getByTestId('form-textarea');

      // Simulate change event using fireEvent
      fireEvent.change(textarea, { target: { value: 'New Value' } });

      expect(mockOnSaveWidget).not.toHaveBeenCalled();
    });

    it('should not call onSaveWidget when onSaveWidget is not provided', () => {
      const widget = {
        id: 'test-widget-id',
        type: 'WELCOME_MESSAGE',
        properties: {
          title: 'Initial Message',
        },
      };

      // Should not throw error when onSaveWidget is undefined
      expect(() => {
        renderComponent({
          editMode: true,
          widget,
          onSaveWidget: undefined,
        });

        const textarea = screen.getByTestId('form-textarea');

        // Simulate change event using fireEvent
        fireEvent.change(textarea, { target: { value: 'New Value' } });
      }).not.toThrow();
    });
  });

  describe('Data Processing and Widget Engine', () => {
    it('should process widget data with replaceLineBreaks', async () => {
      const { replaceLineBreaks } = (await vi.importMock('@/utils')) as any;
      const properties = {
        title: 'Welcome\nMessage\nWith\nBreaks',
      };

      renderComponent({ editMode: false, properties });

      expect(replaceLineBreaks).toHaveBeenCalledWith('Welcome\nMessage\nWith\nBreaks');
    });

    it('should handle empty or undefined properties title', async () => {
      const { replaceLineBreaks } = (await vi.importMock('@/utils')) as any;

      renderComponent({ editMode: false, properties: {} });

      expect(replaceLineBreaks).toHaveBeenCalledWith('');
    });

    it('should pass correct data to WidgetEngine', () => {
      const properties = {
        title: 'Test Welcome Message',
      };
      const widgetData = {
        text: 'Custom widget data',
      };
      const engineConfig = { theme: 'dark' };

      renderComponent({
        editMode: false,
        properties,
        widgetData,
        engineConfig,
      });

      const engineElement = screen.getByTestId('widget-engine');
      expect(engineElement).toBeInTheDocument();
    });

    it('should use initData when widgetData is not provided', () => {
      const properties = {
        title: 'Test Message',
      };

      renderComponent({ editMode: false, properties });

      const engineElement = screen.getByTestId('widget-engine');
      expect(engineElement).toBeInTheDocument();
    });

    it('should use initStyleContext when styleWidgetContext is not provided', () => {
      renderComponent({ editMode: false });

      const engineElement = screen.getByTestId('widget-engine');
      expect(engineElement).toBeInTheDocument();
    });
  });
});
