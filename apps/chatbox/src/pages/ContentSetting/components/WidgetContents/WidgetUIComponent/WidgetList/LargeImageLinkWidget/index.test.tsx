import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AllTheProviders } from '@/utils/unitTest';
import LargeImageLinkWidget from './index';

vi.mock('@mantine/core', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    rem: vi.fn((value: string) => `${value}rem`),
    Container: ({ children, fluid, className, ...props }: any) => (
      <div data-testid='container' data-fluid={fluid} className={className} {...props}>
        {children}
      </div>
    ),
  };
});

vi.mock('@mantine/emotion', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    createStyles: (styleFunction: (theme: any) => any) => {
      const mockTheme = {};

      const styles = styleFunction(mockTheme);

      return () => ({
        classes: {
          container: 'mocked-container-class',
        },
        cx: vi.fn((...args) => args.filter(Boolean).join(' ')),
      });
    },
  };
});

vi.mock('@resola-ai/widget-engine', () => ({
  Engine: ({ data, context, config, ...props }: any) => (
    <div data-testid='widget-engine' {...props}>
      <div data-testid='engine-data'>{JSON.stringify(data)}</div>
      <div data-testid='engine-context'>{JSON.stringify(context)}</div>
      <div data-testid='engine-config'>{JSON.stringify(config)}</div>
    </div>
  ),
}));

vi.mock('@/components/ChatBoxUI/App/Widgets/templates', () => ({
  getGeneralWidgetTemplates: vi.fn(),
}));

const renderComponent = (props = {}) => {
  const defaultProps = {
    customWidget: undefined,
    properties: undefined,
    commonStyle: undefined,
    widgetData: undefined,
    styleWidgetContext: undefined,
    engineConfig: undefined,
  };

  return render(
    <AllTheProviders>
      <LargeImageLinkWidget {...defaultProps} {...props} />
    </AllTheProviders>
  );
};

describe('LargeImageLinkWidget Component', () => {
  beforeEach(async () => {
    vi.clearAllMocks();

    const { getGeneralWidgetTemplates } = (await vi.importMock(
      '@/components/ChatBoxUI/App/Widgets/templates'
    )) as any;
    getGeneralWidgetTemplates.mockReturnValue({
      type: 'largeImageLink',
      data: 'mocked-template-data',
    });
  });

  describe('Component Structure', () => {
    it('should render with correct structure', () => {
      renderComponent();

      expect(screen.getByTestId('container')).toBeInTheDocument();
      expect(screen.getByTestId('widget-engine')).toBeInTheDocument();
    });

    it('should apply correct container styling', () => {
      renderComponent();

      const container = screen.getByTestId('container');
      expect(container).toHaveAttribute('data-fluid', 'true');
      expect(container).toHaveClass('mocked-container-class');
    });

    it('should apply custom widget class when provided', () => {
      const customWidget = 'custom-widget-class';
      renderComponent({ customWidget });

      const container = screen.getByTestId('container');
      expect(container).toHaveClass('mocked-container-class custom-widget-class');
    });

    it('should not apply custom widget class when not provided', () => {
      renderComponent();

      const container = screen.getByTestId('container');
      expect(container).toHaveClass('mocked-container-class');
      expect(container.className).not.toContain('undefined');
    });
  });

  describe('Data Processing and Widget Engine', () => {
    it('should pass correct data to WidgetEngine with properties', () => {
      const properties = {
        imageSource: 'https://example.com/image.jpg',
        title: 'Test Image Title',
        description: 'Test Image Description',
        link: 'https://example.com/link',
      };
      const commonStyle = { color: 'red' };
      const engineConfig = { theme: 'dark' };

      renderComponent({ properties, commonStyle, engineConfig });

      const engineElement = screen.getByTestId('widget-engine');
      expect(engineElement).toBeInTheDocument();
    });

    it('should use default empty values when properties are not provided', () => {
      renderComponent();

      const engineElement = screen.getByTestId('widget-engine');
      expect(engineElement).toBeInTheDocument();
    });

    it('should use widgetData when provided instead of initData', () => {
      const widgetData = {
        src: 'https://widget.example.com/image.jpg',
        title: 'Widget Title',
        description: 'Widget Description',
        href: 'https://widget.example.com/link',
        target: '_blank',
      };

      renderComponent({ widgetData });

      const engineElement = screen.getByTestId('widget-engine');
      expect(engineElement).toBeInTheDocument();
    });

    it('should use styleWidgetContext when provided instead of initStyleContext', () => {
      const styleWidgetContext = {
        widgetTemplates: {
          customStyle: { backgroundColor: 'blue' },
        },
      };

      renderComponent({ styleWidgetContext });

      const engineElement = screen.getByTestId('widget-engine');
      expect(engineElement).toBeInTheDocument();
    });

    it('should handle empty properties object', () => {
      const properties = {};

      renderComponent({ properties });

      const engineElement = screen.getByTestId('widget-engine');
      expect(engineElement).toBeInTheDocument();
    });

    it('should handle partial properties', () => {
      const properties = {
        imageSource: 'https://example.com/image.jpg',
        title: 'Only Title Provided',
        // description and link are missing
      };

      renderComponent({ properties });

      const engineElement = screen.getByTestId('widget-engine');
      expect(engineElement).toBeInTheDocument();
    });

    it('should call getGeneralWidgetTemplates with correct parameters', async () => {
      const { getGeneralWidgetTemplates } = (await vi.importMock(
        '@/components/ChatBoxUI/App/Widgets/templates'
      )) as any;

      const properties = {
        imageSource: 'https://example.com/image.jpg',
        title: 'Test Title',
        description: 'Test Description',
        link: 'https://example.com/link',
      };

      renderComponent({ properties });

      expect(getGeneralWidgetTemplates).toHaveBeenCalled();
    });
  });

  describe('Props and Configuration', () => {
    it('should handle all props correctly', () => {
      const props = {
        customWidget: 'custom-class',
        properties: {
          imageSource: 'https://example.com/image.jpg',
          title: 'Full Props Test',
          description: 'Testing all props',
          link: 'https://example.com/link',
        },
        commonStyle: { fontSize: '16px' },
        widgetData: {
          src: 'https://widget.example.com/override.jpg',
          title: 'Widget Override',
          description: 'Widget Override Description',
          href: 'https://widget.example.com/override',
          target: '_self',
        },
        styleWidgetContext: {
          widgetTemplates: {
            customStyle: { margin: '10px' },
          },
        },
        engineConfig: {
          debug: true,
          theme: 'light',
        },
      };

      renderComponent(props);

      expect(screen.getByTestId('container')).toBeInTheDocument();
      expect(screen.getByTestId('widget-engine')).toBeInTheDocument();
      expect(screen.getByTestId('container')).toHaveClass('mocked-container-class custom-class');
    });

    it('should handle undefined props gracefully', () => {
      const props = {
        customWidget: undefined,
        properties: undefined,
        commonStyle: undefined,
        widgetData: undefined,
        styleWidgetContext: undefined,
        engineConfig: undefined,
      };

      expect(() => renderComponent(props)).not.toThrow();
    });
  });

  describe('CSS Styles Execution', () => {
    it('should execute createStyles function when rendering component', () => {
      renderComponent();

      expect(screen.getByTestId('container')).toBeInTheDocument();
      expect(screen.getByTestId('container')).toHaveClass('mocked-container-class');
    });

    it('should apply CSS classes correctly', () => {
      renderComponent();

      const container = screen.getByTestId('container');
      expect(container).toHaveClass('mocked-container-class');
    });
  });
});
