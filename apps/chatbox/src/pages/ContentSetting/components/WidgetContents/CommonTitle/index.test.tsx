import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AllTheProviders } from '@/utils/unitTest';
import { WIDGET_TYPE } from '@/constants/widgetType';
import CommonTitle from './index';

// Mock dependencies
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: vi.fn((key: string) => key) }),
}));

vi.mock('@mantine/emotion', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    createStyles: vi.fn((styleFunction: (theme: any) => any) => {
      // Execute the style function to cover lines 7-12
      const mockTheme = {};
      const styles = styleFunction(mockTheme);

      return () => ({
        classes: {
          container: 'mocked-container-class',
          iconCustom: 'mocked-icon-custom-class',
        },
        cx: vi.fn((...args) => args.filter(Boolean).join(' ')),
      });
    }),
  };
});

// Test data and utilities
const WIDGET_TEST_CASES = [
  [WIDGET_TYPE.CALL_TO_ACTION, 'callToActionButtonTitle', 'callToActionButtonSubTitle'],
  [WIDGET_TYPE.LARGE_IMAGE_LINK, 'largeImageLinksTitle', 'largeImageLinksSubTitle'],
  [WIDGET_TYPE.SMALL_IMAGE_LINK, 'smallImageLinksTitle', 'smallImageLinksSubTitle'],
  [WIDGET_TYPE.WELCOME_MESSAGE, 'welcomeMessageTitle', 'welcomeMessageSubTitle'],
] as const;

const renderComponent = (props: any = { type: WIDGET_TYPE.CALL_TO_ACTION }) =>
  render(
    <AllTheProviders>
      <CommonTitle {...props} />
    </AllTheProviders>
  );

const getElements = () => ({
  title: screen.getByRole('heading', { level: 6 }),
  subtitle: screen.getByText(/SubTitle/),
  container: screen.getByRole('heading', { level: 6 }).parentElement,
});

describe('CommonTitle Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Widget Types and Translations', () => {
    it('renders all widget types with correct translations', () => {
      WIDGET_TEST_CASES.forEach(([type, expectedTitle, expectedSubTitle]) => {
        const { unmount } = renderComponent({ type });

        expect(screen.getByText(expectedTitle)).toBeInTheDocument();
        expect(screen.getByText(expectedSubTitle)).toBeInTheDocument();

        unmount();
      });
    });
  });

  describe('Component Structure and Styling', () => {
    it('renders with correct structure and applies CSS styles', () => {
      renderComponent({ type: WIDGET_TYPE.CALL_TO_ACTION });
      const { title, container } = getElements();

      // Structure validation
      expect(title.tagName).toBe('H6');
      expect(container?.children).toHaveLength(2);
      expect(container?.className).toContain('mantine-Stack-root');

      // CSS styles coverage (lines 8-12)
      expect(container).toHaveClass('mocked-container-class');
    });

    it('applies custom classes when provided', () => {
      renderComponent({
        type: WIDGET_TYPE.CALL_TO_ACTION,
        customTitle: 'custom-title-class',
        customSubTitle: 'custom-subtitle-class',
      });

      expect(screen.getByRole('heading')).toHaveClass('custom-title-class');
      expect(screen.getByText('callToActionButtonSubTitle')).toHaveClass('custom-subtitle-class');
    });
  });
});
