import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { AllTheProviders } from '@/utils/unitTest';
import WidgetControl from './index';

// Global variable to capture onClose callbacks for testing
let capturedOnCloseCallbacks: any[] = [];

vi.mock('@mantine/core', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    Text: ({ children, c, size, ...props }: any) => (
      <span data-testid='text' data-color={c} data-size={size} {...props}>
        {children}
      </span>
    ),
    Drawer: ({
      opened,
      onClose,
      position,
      size,
      className,
      withCloseButton,
      closeOnEscape,
      children,
      ...props
    }: any) => {
      // Capture the onClose callback for testing
      if (onClose && !capturedOnCloseCallbacks.includes(onClose)) {
        capturedOnCloseCallbacks.push(onClose);
      }

      return (
        <div
          data-testid='drawer'
          data-opened={opened}
          data-position={position}
          data-size={size}
          data-with-close-button={withCloseButton}
          data-close-on-escape={closeOnEscape}
          className={className}
          {...props}
        >
          {opened && children}
          {opened && (
            <button data-testid='drawer-close-button' onClick={onClose}>
              Close Drawer
            </button>
          )}
        </div>
      );
    },
    rem: (value: number) => `${value}px`,
  };
});

vi.mock('@mantine/emotion', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    createStyles: (styleFunction: (theme: any) => any) => {
      // Execute the style function immediately to cover the CSS lines
      const mockTheme = {
        colors: {
          silverFox: ['', '#silverFox1', '', '', '', '', '', '', '', ''],
        },
      };

      // Execute the style function to ensure coverage of lines 14-28
      const styles = styleFunction(mockTheme);

      return () => ({
        classes: {
          drawerContainer: 'mocked-drawer-container-class',
          formContainer: 'mocked-form-container-class',
          buttonCustom: 'mocked-button-custom-class',
        },
      });
    },
  };
});

vi.mock('@mantine/hooks', () => ({
  useDisclosure: vi.fn(),
}));

vi.mock('@tabler/icons-react', () => ({
  IconPlus: ({ size, strokeWidth, ...props }: any) => (
    <div data-testid='icon-plus' data-size={size} data-stroke-width={strokeWidth} {...props}>
      +
    </div>
  ),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: vi.fn((key: string) => `translated_${key}`),
  }),
}));

vi.mock('@resola-ai/ui', () => ({
  CustomButton: ({ children, colorScheme, leftIcon, size, onClick, className, ...props }: any) => (
    <button
      data-testid='custom-button'
      data-color-scheme={colorScheme}
      data-size={size}
      className={className}
      onClick={onClick}
      {...props}
    >
      {leftIcon && <span data-testid='button-left-icon'>{leftIcon}</span>}
      {children}
    </button>
  ),
}));

vi.mock('./ContainerWidgets', () => ({
  __esModule: true,
  default: () => <div data-testid='container-widgets'>Container Widgets</div>,
}));

vi.mock('../EditFormWidgetLayout', () => ({
  __esModule: true,
  default: ({ widgetType, onCloseDrawer, widgetId }: any) => (
    <div data-testid='edit-form-widget-layout'>
      <div data-testid='widget-type'>{widgetType}</div>
      <div data-testid='widget-id'>{widgetId}</div>
      <button data-testid='close-drawer-button' onClick={onCloseDrawer}>
        Close
      </button>
    </div>
  ),
}));

const mockContentSettingsContext = {
  addingWidget: null,
  drawerForm: {
    reset: vi.fn(),
  },
  form: {
    watch: vi.fn(() => []),
  },
};

const mockSettingsContext = {
  LIMITATION_OF_WIDGETS: 5,
};

vi.mock('@/pages/ContentSetting/ContentSettingsContext', () => ({
  useContentSettingsContext: vi.fn(),
}));

vi.mock('@/contexts/SettingsContext', () => ({
  useSettingsContext: vi.fn(),
}));

vi.mock('@/utils/errorHandler', () => ({
  errorHandler: vi.fn(),
  ERROR_TYPES: {
    WIDGETS_LIMITATION: 'WIDGETS_LIMITATION',
  },
}));

const renderComponent = () => {
  return render(
    <AllTheProviders>
      <WidgetControl />
    </AllTheProviders>
  );
};

describe('WidgetControl Component', () => {
  const mockGetElementById = vi.fn();
  const mockSetAttribute = vi.fn();
  const mockRemoveAttribute = vi.fn();
  const mockOpen = vi.fn();
  const mockClose = vi.fn();
  const mockOpenEditModal = vi.fn();
  const mockCloseEditModal = vi.fn();

  beforeEach(async () => {
    vi.clearAllMocks();
    capturedOnCloseCallbacks = []; // Reset captured callbacks

    // Setup context mocks
    const { useContentSettingsContext } = (await vi.importMock(
      '@/pages/ContentSetting/ContentSettingsContext'
    )) as any;
    const { useSettingsContext } = (await vi.importMock('@/contexts/SettingsContext')) as any;
    const { useDisclosure } = (await vi.importMock('@mantine/hooks')) as any;

    useContentSettingsContext.mockReturnValue(mockContentSettingsContext);
    useSettingsContext.mockReturnValue(mockSettingsContext);

    // Setup default useDisclosure mock - returns two disclosure states
    useDisclosure
      .mockReturnValueOnce([false, { open: mockOpen, close: mockClose }]) // First call for main drawer
      .mockReturnValueOnce([false, { open: mockOpenEditModal, close: mockCloseEditModal }]); // Second call for edit modal

    // Mock DOM methods
    const mockElement = {
      setAttribute: mockSetAttribute,
      removeAttribute: mockRemoveAttribute,
    };
    mockGetElementById.mockReturnValue(mockElement);
    global.document.getElementById = mockGetElementById;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Component Structure', () => {
    it('should render with correct structure', () => {
      renderComponent();

      // Should render both drawers and the add button
      const drawers = screen.getAllByTestId('drawer');
      expect(drawers).toHaveLength(2);

      expect(screen.getByTestId('custom-button')).toBeInTheDocument();
      expect(screen.getByTestId('text')).toBeInTheDocument();
      expect(screen.getByTestId('icon-plus')).toBeInTheDocument();
    });

    it('should apply correct styling classes', () => {
      renderComponent();

      const drawers = screen.getAllByTestId('drawer');
      drawers.forEach((drawer) => {
        expect(drawer).toHaveClass('mocked-drawer-container-class');
      });

      const button = screen.getByTestId('custom-button');
      expect(button).toHaveClass('mocked-button-custom-class');
    });

    it('should configure drawers with correct props', () => {
      renderComponent();

      const drawers = screen.getAllByTestId('drawer');

      // First drawer (widget selection)
      expect(drawers[0]).toHaveAttribute('data-position', 'right');
      expect(drawers[0]).toHaveAttribute('data-size', 'md');
      expect(drawers[0]).toHaveAttribute('data-with-close-button', 'false');
      expect(drawers[0]).toHaveAttribute('data-close-on-escape', 'false');

      // Second drawer (edit modal)
      expect(drawers[1]).toHaveAttribute('data-position', 'right');
      expect(drawers[1]).toHaveAttribute('data-size', 'lg');
      expect(drawers[1]).toHaveAttribute('data-with-close-button', 'false');
    });

    it('should render add widget button with correct props', () => {
      renderComponent();

      const button = screen.getByTestId('custom-button');
      expect(button).toHaveAttribute('data-color-scheme', 'primary');
      expect(button).toHaveAttribute('data-size', 'sm');

      const leftIcon = screen.getByTestId('button-left-icon');
      expect(leftIcon).toBeInTheDocument();

      const iconPlus = screen.getByTestId('icon-plus');
      expect(iconPlus).toBeInTheDocument();
    });

    it('should render text with correct styling', () => {
      renderComponent();

      const text = screen.getByTestId('text');
      expect(text).toHaveAttribute('data-color', 'silverFox.1');
      expect(text).toHaveAttribute('data-size', '16px');
      expect(text).toHaveTextContent('translated_addWidgetTitle');
    });
  });

  describe('CSS Styles Execution', () => {
    it('should execute createStyles function when rendering component', () => {
      // Rendering the component should trigger the createStyles function
      // which will execute the style function and cover lines 14-28
      renderComponent();

      expect(screen.getByTestId('custom-button')).toBeInTheDocument();
    });
  });

  describe('useEffect Hooks', () => {
    it('should trigger edit modal when addingWidget changes', async () => {
      const { useContentSettingsContext } = (await vi.importMock(
        '@/pages/ContentSetting/ContentSettingsContext'
      )) as any;

      const mockContextWithWidget = {
        ...mockContentSettingsContext,
        addingWidget: { type: 'CALL_TO_ACTION', id: 'test-id' },
      };

      useContentSettingsContext.mockReturnValue(mockContextWithWidget);

      renderComponent();

      expect(mockClose).toHaveBeenCalled();
      expect(mockOpenEditModal).toHaveBeenCalled();
    });

    it('should handle DOM manipulation for aria-hidden attribute when edit modal opens', () => {
      renderComponent();

      // The useEffect should have run and called getElementById
      expect(mockGetElementById).toHaveBeenCalledWith('root');
      // Since openedEditModal is false by default, removeAttribute should be called
      expect(mockRemoveAttribute).toHaveBeenCalledWith('aria-hidden');
    });

    it('should handle DOM manipulation for aria-hidden attribute when edit modal closes', async () => {
      const { useContentSettingsContext } = (await vi.importMock(
        '@/pages/ContentSetting/ContentSettingsContext'
      )) as any;
      const { useDisclosure } = (await vi.importMock('@mantine/hooks')) as any;

      // Reset mocks and setup for edit modal closed
      vi.clearAllMocks();
      useContentSettingsContext.mockReturnValue(mockContentSettingsContext);
      useDisclosure
        .mockReturnValueOnce([false, { open: mockOpen, close: mockClose }])
        .mockReturnValueOnce([false, { open: mockOpenEditModal, close: mockCloseEditModal }]); // Edit modal closed

      renderComponent();

      // Should call removeAttribute when edit modal is closed
      expect(mockGetElementById).toHaveBeenCalledWith('root');
      expect(mockRemoveAttribute).toHaveBeenCalledWith('aria-hidden');
    });

    it('should handle case when root element is not found', () => {
      // Mock getElementById to return null
      mockGetElementById.mockReturnValue(null);

      // Should not throw error
      expect(() => renderComponent()).not.toThrow();

      // Should still call getElementById
      expect(mockGetElementById).toHaveBeenCalledWith('root');
    });
  });

  describe('Handler Functions', () => {
    it('should test handleAddWidget function with widgets under limit', async () => {
      const { useContentSettingsContext } = (await vi.importMock(
        '@/pages/ContentSetting/ContentSettingsContext'
      )) as any;

      const mockContextUnderLimit = {
        ...mockContentSettingsContext,
        form: {
          watch: vi.fn(() => []), // Empty array - under limit
        },
      };

      useContentSettingsContext.mockReturnValue(mockContextUnderLimit);

      renderComponent();

      const button = screen.getByTestId('custom-button');
      fireEvent.click(button);

      // Should call open function
      expect(mockOpen).toHaveBeenCalled();
    });

    it('should test handleAddWidget function with widgets at limit', async () => {
      const { useContentSettingsContext } = (await vi.importMock(
        '@/pages/ContentSetting/ContentSettingsContext'
      )) as any;
      const { errorHandler } = (await vi.importMock('@/utils/errorHandler')) as any;

      const mockContextAtLimit = {
        ...mockContentSettingsContext,
        form: {
          watch: vi.fn(() => Array(5).fill({})),
        },
      };

      useContentSettingsContext.mockReturnValue(mockContextAtLimit);

      renderComponent();

      const button = screen.getByTestId('custom-button');
      fireEvent.click(button);

      expect(errorHandler).toHaveBeenCalledWith('WIDGETS_LIMITATION', expect.any(Function));
      expect(mockOpen).not.toHaveBeenCalled();
    });

    it('should test handleCloseDrawer function execution through captured callback', async () => {
      const { useContentSettingsContext } = (await vi.importMock(
        '@/pages/ContentSetting/ContentSettingsContext'
      )) as any;

      const mockReset = vi.fn();
      const mockContextWithReset = {
        ...mockContentSettingsContext,
        drawerForm: { reset: mockReset },
      };

      vi.clearAllMocks();
      capturedOnCloseCallbacks = [];
      useContentSettingsContext.mockReturnValue(mockContextWithReset);

      renderComponent();

      expect(capturedOnCloseCallbacks).toHaveLength(2);

      const handleCloseDrawerCallback = capturedOnCloseCallbacks[1];

      handleCloseDrawerCallback();

      expect(mockCloseEditModal).toHaveBeenCalled();
      expect(mockReset).toHaveBeenCalled();
    });

    it('should test useEffect functions execution and coverage', async () => {
      const { useContentSettingsContext } = (await vi.importMock(
        '@/pages/ContentSetting/ContentSettingsContext'
      )) as any;

      const mockContextWithWidget = {
        ...mockContentSettingsContext,
        addingWidget: { type: 'TEST', id: 'test-id' },
      };

      vi.clearAllMocks();
      useContentSettingsContext.mockReturnValue(mockContextWithWidget);

      renderComponent();

      expect(mockClose).toHaveBeenCalled();
      expect(mockOpenEditModal).toHaveBeenCalled();

      expect(mockGetElementById).toHaveBeenCalledWith('root');
    });
  });
});
