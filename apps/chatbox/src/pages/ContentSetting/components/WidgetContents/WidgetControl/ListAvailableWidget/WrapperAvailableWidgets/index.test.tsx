import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AllTheProviders } from '@/utils/unitTest';
import { WIDGET_TYPE } from '@/constants/widgetType';
import type { CustomWidget } from '@/types';
import WrapperAvailableWidgets from './index';

vi.mock('@mantine/core', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    Stack: ({ children, gap, p, className, ...props }: any) => (
      <div data-testid='stack' data-gap={gap} data-padding={p} className={className} {...props}>
        {children}
      </div>
    ),
    Flex: ({ children, justify, ...props }: any) => (
      <div data-testid='flex' data-justify={justify} {...props}>
        {children}
      </div>
    ),
    rem: (value: number) => `${value}px`,
    useMantineTheme: () => ({
      colors: {
        pureApple: ['', '', '', '', '', '#pureApple5', '', '', '', ''],
        silverFox: ['', '', '#silverFox2', '', '', '#silverFox5', '', '', '', ''],
      },
    }),
  };
});

vi.mock('@mantine/emotion', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    createStyles: (styleFunction: (theme: any) => any) => {
      // Execute the style function immediately to cover the CSS lines
      const mockTheme = {
        colors: {
          silverFox: ['', '', '#silverFox2', '', '', '#silverFox5', '', '', '', ''],
        },
      };
      const mockRem = (value: number) => `${value}px`;

      // Execute the style function to ensure coverage of lines 11-23
      const styles = styleFunction(mockTheme);

      return () => ({
        classes: {
          container: 'mocked-container-class',
          customWidget: 'mocked-custom-widget-class',
          iconCustom: 'mocked-icon-custom-class',
        },
      });
    },
  };
});

vi.mock('@tabler/icons-react', () => ({
  IconCirclePlus: ({ className, size, strokeWidth, color, onClick, ...props }: any) => (
    <button
      data-testid='icon-circle-plus'
      className={className}
      data-size={size}
      data-stroke-width={strokeWidth}
      data-color={color}
      onClick={onClick}
      {...props}
    >
      Plus Icon
    </button>
  ),
}));

vi.mock('uuid', () => ({
  v4: vi.fn(() => 'mocked-uuid-123'),
}));

vi.mock('../../../CommonTitle', () => ({
  __esModule: true,
  default: ({ type }: { type: string }) => (
    <div data-testid='common-title'>
      <div data-testid='widget-type'>{type}</div>
    </div>
  ),
}));

vi.mock('../../../WidgetUIComponent', () => ({
  __esModule: true,
  default: ({ widgetType, properties, customWidget }: any) => (
    <div data-testid='widget-ui-component'>
      <div data-testid='widget-type'>{widgetType}</div>
      <div data-testid='widget-properties'>{JSON.stringify(properties || {})}</div>
      <div data-testid='custom-widget-class'>{customWidget}</div>
    </div>
  ),
}));

const mockOnAddWidget = vi.fn();
const mockHandleResetDrawerForm = vi.fn();

vi.mock('@/pages/ContentSetting/ContentSettingsContext', () => ({
  useContentSettingsContext: () => ({
    onAddWidget: mockOnAddWidget,
    handleResetDrawerForm: mockHandleResetDrawerForm,
  }),
}));

const createMockWidget = (type: string, overrides: Partial<CustomWidget> = {}): CustomWidget => ({
  id: 'test-widget-id',
  type,
  properties: {},
  value: 'test-value',
  ...overrides,
});

const renderComponent = (widget: CustomWidget) => {
  return render(
    <AllTheProviders>
      <WrapperAvailableWidgets widget={widget} />
    </AllTheProviders>
  );
};

describe('WrapperAvailableWidgets Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Component Structure', () => {
    it('should render with correct structure', () => {
      const widget = createMockWidget(WIDGET_TYPE.CALL_TO_ACTION);
      renderComponent(widget);

      expect(screen.getByTestId('stack')).toBeInTheDocument();
      expect(screen.getByTestId('flex')).toBeInTheDocument();
      expect(screen.getByTestId('common-title')).toBeInTheDocument();
      expect(screen.getByTestId('icon-circle-plus')).toBeInTheDocument();
      expect(screen.getByTestId('widget-ui-component')).toBeInTheDocument();
    });

    it('should apply correct CSS classes from useStyles', () => {
      const widget = createMockWidget(WIDGET_TYPE.LARGE_IMAGE_LINK);
      renderComponent(widget);

      const stack = screen.getByTestId('stack');
      const icon = screen.getByTestId('icon-circle-plus');

      expect(stack).toHaveClass('mocked-container-class');
      expect(icon).toHaveClass('mocked-icon-custom-class');
    });

    it('should render Stack with correct props', () => {
      const widget = createMockWidget(WIDGET_TYPE.SMALL_IMAGE_LINK);
      renderComponent(widget);

      const stack = screen.getByTestId('stack');
      expect(stack).toHaveAttribute('data-gap', 'md');
      expect(stack).toHaveAttribute('data-padding', '12px');
    });

    it('should render Flex with correct justify prop', () => {
      const widget = createMockWidget(WIDGET_TYPE.CALL_TO_ACTION);
      renderComponent(widget);

      const flex = screen.getByTestId('flex');
      expect(flex).toHaveAttribute('data-justify', 'space-between');
    });
  });

  describe('CSS Styles Execution', () => {
    it('should execute createStyles function when rendering component', () => {
      const widget = createMockWidget(WIDGET_TYPE.WELCOME_MESSAGE);
      renderComponent(widget);

      expect(screen.getByTestId('stack')).toBeInTheDocument();
      expect(screen.getByTestId('icon-circle-plus')).toBeInTheDocument();
    });

    it('should apply all CSS classes correctly', () => {
      const widget = createMockWidget(WIDGET_TYPE.CALL_TO_ACTION);
      renderComponent(widget);

      const stack = screen.getByTestId('stack');
      const icon = screen.getByTestId('icon-circle-plus');

      // Test that CSS classes are applied
      expect(stack).toHaveClass('mocked-container-class');
      expect(icon).toHaveClass('mocked-icon-custom-class');

      // Test that WidgetUIComponent receives the customWidget class
      const widgetUIComponent = screen.getByTestId('widget-ui-component');
      expect(screen.getByTestId('custom-widget-class')).toHaveTextContent(
        'mocked-custom-widget-class'
      );
    });
  });

  describe('Function Coverage', () => {
    it('should test handleAddWidget function execution', () => {
      const widget = createMockWidget(WIDGET_TYPE.CALL_TO_ACTION);
      renderComponent(widget);

      const addButton = screen.getByTestId('icon-circle-plus');

      fireEvent.click(addButton);

      expect(mockOnAddWidget).toHaveBeenCalledWith({
        id: 'mocked-uuid-123',
        type: WIDGET_TYPE.CALL_TO_ACTION,
      });

      expect(mockHandleResetDrawerForm).toHaveBeenCalledWith(WIDGET_TYPE.CALL_TO_ACTION);
    });

    it('should test handleAddWidget with different widget types', () => {
      const widgetTypes = [
        WIDGET_TYPE.LARGE_IMAGE_LINK,
        WIDGET_TYPE.SMALL_IMAGE_LINK,
        WIDGET_TYPE.WELCOME_MESSAGE,
      ];

      widgetTypes.forEach((type) => {
        vi.clearAllMocks();
        const widget = createMockWidget(type);
        const { unmount } = renderComponent(widget);

        const addButton = screen.getByTestId('icon-circle-plus');
        fireEvent.click(addButton);

        expect(mockOnAddWidget).toHaveBeenCalledWith({
          id: 'mocked-uuid-123',
          type: type,
        });
        expect(mockHandleResetDrawerForm).toHaveBeenCalledWith(type);

        unmount();
      });
    });
  });
});
