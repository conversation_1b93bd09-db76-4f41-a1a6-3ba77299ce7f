import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AllTheProviders } from '@/utils/unitTest';
import ContainerWidgets from './index';

const { mockTranslate } = vi.hoisted(() => {
  const mockTranslate = vi.fn();
  return { mockTranslate };
});

vi.mock('@mantine/core', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    Stack: ({ children, className, ...props }: any) => (
      <div data-testid='stack' className={className} {...props}>
        {children}
      </div>
    ),
    Title: ({ children, className, ...props }: any) => (
      <h1 data-testid='title' className={className} {...props}>
        {children}
      </h1>
    ),
  };
});

vi.mock('@mantine/emotion', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    createStyles: vi.fn((styleFunction: (theme?: any) => any) => {
      const mockTheme = {
        colors: {
          silverFox: [
            '#fff',
            '#f8f9fa',
            '#e9ecef',
            '#dee2e6',
            '#ced4da',
            '#adb5bd',
            '#6c757d',
            '#495057',
            '#343a40',
            '#000',
          ],
          decaNavy: [
            '#e6f0ff',
            '#b3d1ff',
            '#80b3ff',
            '#4d94ff',
            '#1a75ff',
            '#0066e6',
            '#0052cc',
            '#003d99',
            '#002966',
            '#001433',
          ],
        },
      };
      const styles = styleFunction(mockTheme);
      return () => ({
        classes: {
          container: 'mocked-container-class',
          customButton: 'mocked-custom-button-class',
          title: 'mocked-title-class',
        },
      });
    }),
  };
});

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: mockTranslate,
  }),
}));

vi.mock('../ListAvailableWidget', () => ({
  __esModule: true,
  default: () => (
    <div data-testid='list-available-widget'>
      <div data-testid='widget-item'>Call to Action Widget</div>
      <div data-testid='widget-item'>Large Image Link Widget</div>
      <div data-testid='widget-item'>Small Image Link Widget</div>
    </div>
  ),
}));

// Test utilities
const renderComponent = () => {
  return render(
    <AllTheProviders>
      <ContainerWidgets />
    </AllTheProviders>
  );
};

describe('ContainerWidgets Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockTranslate.mockReturnValue('Add Widget');
  });

  describe('Component Structure and Styling', () => {
    it('renders with correct structure and CSS classes', () => {
      renderComponent();

      const stack = screen.getByTestId('stack');
      const title = screen.getByTestId('title');
      const listWidget = screen.getByTestId('list-available-widget');

      expect(stack).toBeInTheDocument();
      expect(stack).toHaveClass('mocked-container-class');
      expect(stack).toContainElement(title);
      expect(stack).toContainElement(listWidget);

      expect(title).toBeInTheDocument();
      expect(title).toHaveClass('mocked-title-class');
      expect(title).toHaveTextContent('Add Widget');

      expect(listWidget).toBeInTheDocument();
    });

    it('renders all child components in correct order', () => {
      renderComponent();

      const stack = screen.getByTestId('stack');
      const children = Array.from(stack.children);

      expect(children).toHaveLength(2);
      expect(children[0]).toHaveAttribute('data-testid', 'title');
      expect(children[1]).toHaveAttribute('data-testid', 'list-available-widget');
    });

    it('applies CSS styles correctly', () => {
      renderComponent();

      const stack = screen.getByTestId('stack');
      const title = screen.getByTestId('title');

      expect(stack).toHaveClass('mocked-container-class');
      expect(title).toHaveClass('mocked-title-class');
    });
  });
});
