import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AllTheProviders } from '@/utils/unitTest';
import RemoveWidgetModal from './index';

const mockWidgets = [
  { id: 'widget-1', type: 'WELCOME_MESSAGE', properties: { title: 'Welcome' } },
  { id: 'widget-2', type: 'CALL_TO_ACTION', properties: { title: 'CTA' } },
  { id: 'widget-3', type: 'LARGE_IMAGE_LINK', properties: { title: 'Image Link' } },
];

const mockForm = { setValue: vi.fn() };
const mockTranslations = {
  removeWidgetTitle: 'Remove Widget',
  removeWidgetDescription:
    'Are you sure you want to remove this widget? This action cannot be undone.',
  cancel: 'Cancel',
  remove: 'Remove',
};

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: (key: string) => mockTranslations[key] || key }),
}));

vi.mock('@mantine/emotion', async (importOriginal) => ({
  ...(await importOriginal<any>()),
  createStyles: (styleFunction: (theme: any) => any) => {
    // Execute the style function immediately to cover the CSS lines
    const mockTheme = {
      colors: {
        redSalsa: ['#FFE8E8', '', '', '', '', '#DC2626'],
        silverFox: ['', '', '', '', '', '', '', '', '', '#1F2937'],
      },
    };

    // Execute the style function to ensure coverage of lines 10-36
    const styles = styleFunction(mockTheme);

    return () => ({ classes: { container: 'mocked-container-class' } });
  },
}));

vi.mock('@/pages/ContentSetting/ContentSettingsContext', () => ({
  useContentSettingsContext: vi.fn(() => ({
    widgets: mockWidgets,
    selectedWidget: mockWidgets[1], // widget-2
    form: mockForm,
  })),
}));

vi.mock('@/components', () => ({
  CustomGroupButton: ({
    rejectTitle,
    approveTitle,
    onReject,
    onApprove,
    size,
    containerCustomClass,
  }: any) => (
    <div data-testid='custom-group-button'>
      <span data-testid='reject-title'>{rejectTitle}</span>
      <span data-testid='approve-title'>{approveTitle}</span>
      <span data-testid='size'>{size}</span>
      <span data-testid='container-class'>{containerCustomClass}</span>
      <button data-testid='reject-button' onClick={onReject}>
        {rejectTitle}
      </button>
      <button data-testid='approve-button' onClick={onApprove}>
        {approveTitle}
      </button>
    </div>
  ),
}));

const renderModal = (props = {}) => {
  const defaultProps = { isOpen: true, close: vi.fn(), ...props };
  return {
    ...render(
      <AllTheProviders>
        <RemoveWidgetModal {...defaultProps} />
      </AllTheProviders>
    ),
    props: defaultProps,
  };
};

const getByTestId = (id: string) => screen.getByTestId(id);
const queryByRole = (role: string) => screen.queryByRole(role);
const getCloseButton = () =>
  document.querySelector('button[data-variant="filled"]') as HTMLButtonElement;

describe('RemoveWidgetModal Component', () => {
  beforeEach(() => vi.clearAllMocks());

  describe('CSS Styles Execution', () => {
    it('should execute createStyles function when rendering component', () => {
      renderModal();

      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText('Remove Widget')).toBeInTheDocument();
    });

    it('should apply correct CSS classes to elements', () => {
      renderModal();

      expect(getByTestId('container-class')).toHaveTextContent('mocked-container-class');
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });
  });

  describe('Rendering', () => {
    it('should render all UI elements when open', () => {
      renderModal();

      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText('Remove Widget')).toBeInTheDocument();
      expect(
        screen.getByText(
          'Are you sure you want to remove this widget? This action cannot be undone.'
        )
      ).toBeInTheDocument();
      expect(getByTestId('reject-title')).toHaveTextContent('Cancel');
      expect(getByTestId('approve-title')).toHaveTextContent('Remove');
      expect(getByTestId('size')).toHaveTextContent('sm');
      expect(document.querySelector('.tabler-icon-alert-circle')).toBeInTheDocument();
      expect(document.querySelector('.tabler-icon-x')).toBeInTheDocument();
    });

    it('should not render when closed', () => {
      renderModal({ isOpen: false });

      expect(queryByRole('dialog')).not.toBeInTheDocument();
      expect(screen.queryByText('Remove Widget')).not.toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('should handle close and cancel button clicks', () => {
      const { props } = renderModal();

      fireEvent.click(getCloseButton());
      expect(props.close).toHaveBeenCalledTimes(1);

      props.close.mockClear();
      fireEvent.click(getByTestId('reject-button'));
      expect(props.close).toHaveBeenCalledTimes(1);
    });

    it('should handle remove confirmation', () => {
      const { props } = renderModal();
      const expectedWidgetList = [mockWidgets[0], mockWidgets[2]];

      fireEvent.click(getByTestId('approve-button'));

      expect(mockForm.setValue).toHaveBeenCalledWith('widgetList', expectedWidgetList);
      expect(props.close).toHaveBeenCalledTimes(1);
    });
  });

  describe('Component Structure', () => {
    it('should apply correct styling and configuration', () => {
      renderModal();

      expect(getByTestId('container-class')).toHaveTextContent('mocked-container-class');
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });
  });
});
