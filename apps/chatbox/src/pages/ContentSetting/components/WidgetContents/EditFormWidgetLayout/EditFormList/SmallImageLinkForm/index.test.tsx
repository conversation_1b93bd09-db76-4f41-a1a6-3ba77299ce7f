import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MantineProvider, createTheme } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import SmallImageLinkForm from './index';

const {
  mockWatch,
  mockSetValue,
  mockUploadImage,
  mockHandleClearImageFile,
  mockControl,
  mockSelectedWidget,
  mockUseContentSettingsContext,
} = vi.hoisted(() => {
  const mockWatch = vi.fn();
  const mockSetValue = vi.fn();
  const mockUploadImage = vi.fn();
  const mockHandleClearImageFile = vi.fn();
  const mockControl = {
    register: vi.fn(),
    handleSubmit: vi.fn(),
    formState: { errors: {} },
    getValues: vi.fn(),
    setValue: mockSetValue,
    watch: vi.fn(),
    reset: vi.fn(),
  };

  const mockSelectedWidget = {
    properties: {
      description: 'Initial description content',
    },
  };

  const mockUseContentSettingsContext = vi.fn();

  return {
    mockWatch,
    mockSetValue,
    mockUploadImage,
    mockHandleClearImageFile,
    mockControl,
    mockSelectedWidget,
    mockUseContentSettingsContext,
  };
});

// Mock dependencies
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        smallImageLinksTitle: 'Small Image Links',
        smallImageLinksSubTitle: 'Configure your small image link widget',
        dropzoneIdleText: 'Drop images here or click to select',
        title: 'Title',
        addTitlePlaceholder: 'Add a title',
        link: 'Link',
        enterLinkUrlPlaceholder: 'Enter URL',
        description: 'Description',
        wrong_url_format: 'Invalid URL format',
      };
      return translations[key] || key;
    },
  }),
  useTolgee: () => ({ getLanguage: () => 'en' }),
}));

vi.mock('@/pages/ContentSetting/ContentSettingsContext', () => ({
  useContentSettingsContext: mockUseContentSettingsContext,
}));

vi.mock('@/constants', () => ({
  URL_PATTERN:
    /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,50}\b(?:[-a-zA-Z0-9()@:%_+.~#?&\/=]*)$/,
}));

vi.mock('@/constants/uploadSetting', () => ({
  DropZonePropsDefault: {
    maxFiles: 1,
    accept: { 'image/*': ['.jpg', '.jpeg', '.png', '.gif', '.webp'] },
    maxSize: 5 * 1024 * 1024,
  },
}));

vi.mock('@mantine/emotion', async (importOriginal) => {
  const actual = await importOriginal<typeof import('@mantine/emotion')>();
  return {
    ...actual,
    createStyles: vi.fn((styleFunction: (theme?: any) => any) => {
      const styles = styleFunction();
      return () => ({
        classes: { editor: 'mocked-editor-class' },
        cx: vi.fn((...args) => args.filter(Boolean).join(' ')),
      });
    }),
  };
});

vi.mock('react-hook-form-mantine', () => ({
  TextInput: ({ name, label, placeholder, control, rules, maxLength, className }: any) => (
    <div className={className}>
      <label htmlFor={name}>{label}</label>
      <input
        id={name}
        name={name}
        placeholder={placeholder}
        maxLength={maxLength}
        data-testid={`input-${name}`}
        data-rules={JSON.stringify(rules || {})}
        data-has-validation={rules?.validate ? 'true' : 'false'}
      />
    </div>
  ),
}));

vi.mock('@/components', () => ({
  CustomDropzone: ({
    onChange,
    attachmentUrl,
    isLoading,
    dropzoneTextIdle,
    onClearImages,
  }: any) => (
    <div data-testid='custom-dropzone'>
      <div data-testid='dropzone-text'>{dropzoneTextIdle}</div>
      <div data-testid='dropzone-loading'>{isLoading ? 'Loading...' : 'Not loading'}</div>
      <div data-testid='dropzone-attachment-url'>{attachmentUrl || 'No image'}</div>
      <button
        data-testid='dropzone-upload-button'
        onClick={() => {
          const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
          onChange([mockFile]);
        }}
      >
        Upload Image
      </button>
      <button data-testid='dropzone-clear-button' onClick={onClearImages}>
        Clear Image
      </button>
    </div>
  ),
}));

vi.mock('@resola-ai/blocknote-editor', () => ({
  BlockNoteMarkdown: ({ className, initialMarkdown, language, onChange }: any) => (
    <div
      data-testid='blocknote-markdown'
      className={className}
      data-initial-markdown={initialMarkdown}
      data-language={language}
    >
      <textarea
        data-testid='blocknote-textarea'
        defaultValue={initialMarkdown}
        onChange={(e) => onChange?.(e.target.value)}
        placeholder='Enter description...'
      />
    </div>
  ),
}));

vi.mock('../../FormTitle', () => ({
  default: ({ title, subTitle }: { title: string; subTitle?: string }) => (
    <div data-testid='form-title'>
      <h3>{title}</h3>
      {subTitle && <p>{subTitle}</p>}
    </div>
  ),
}));

// Test utilities
const theme = createTheme({
  colors: {
    silverFox: [
      '#f8f9fa',
      '#e9ecef',
      '#dee2e6',
      '#ced4da',
      '#adb5bd',
      '#6c757d',
      '#495057',
      '#343a40',
      '#212529',
      '#000000',
    ],
  },
});

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <MantineProvider theme={theme}>
    <MantineEmotionProvider>{children}</MantineEmotionProvider>
  </MantineProvider>
);

const renderComponent = () => render(<SmallImageLinkForm />, { wrapper: TestWrapper });

const createMockContext = (overrides = {}) => ({
  drawerForm: { control: mockControl, setValue: mockSetValue, watch: mockWatch },
  uploadImage: mockUploadImage,
  handleClearImageFile: mockHandleClearImageFile,
  selectedWidget: mockSelectedWidget,
  ...overrides,
});

describe('SmallImageLinkForm Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockWatch.mockReturnValue('');
    mockUseContentSettingsContext.mockReturnValue(createMockContext());
  });

  describe('Component Structure and Rendering', () => {
    it('renders all required elements with correct structure', () => {
      renderComponent();

      expect(screen.getByTestId('form-title')).toBeInTheDocument();
      expect(screen.getByText('Small Image Links')).toBeInTheDocument();
      expect(screen.getByText('Configure your small image link widget')).toBeInTheDocument();
      expect(screen.getByTestId('custom-dropzone')).toBeInTheDocument();
      expect(screen.getByTestId('input-image_title')).toBeInTheDocument();
      expect(screen.getByTestId('input-image_link')).toBeInTheDocument();
      expect(screen.getByTestId('blocknote-markdown')).toBeInTheDocument();
    });

    it('applies CSS styles correctly', () => {
      renderComponent();

      const editor = screen.getByTestId('blocknote-markdown');
      expect(editor).toHaveClass('mocked-editor-class');
    });
  });

  describe('Image Upload Functionality', () => {
    it('handles successful image upload and sets form value', async () => {
      const mockImageUrl = 'https://example.com/uploaded-image.jpg';
      mockUploadImage.mockResolvedValue(mockImageUrl);

      renderComponent();

      const uploadButton = screen.getByTestId('dropzone-upload-button');
      fireEvent.click(uploadButton);

      await waitFor(() => {
        expect(mockUploadImage).toHaveBeenCalledWith(expect.any(File));
        expect(mockSetValue).toHaveBeenCalledWith('image_src', mockImageUrl);
      });
    });

    it('shows loading state during upload and handles completion', async () => {
      mockUploadImage.mockImplementation(
        () => new Promise((resolve) => setTimeout(() => resolve('url'), 100))
      );

      renderComponent();

      const uploadButton = screen.getByTestId('dropzone-upload-button');
      fireEvent.click(uploadButton);

      expect(screen.getByTestId('dropzone-loading')).toHaveTextContent('Loading...');

      await waitFor(() => {
        expect(screen.getByTestId('dropzone-loading')).toHaveTextContent('Not loading');
      });
    });

    it('handles image clear functionality', () => {
      renderComponent();

      const clearButton = screen.getByTestId('dropzone-clear-button');
      fireEvent.click(clearButton);

      expect(mockHandleClearImageFile).toHaveBeenCalled();
    });

    it('displays current image when image_src is available', () => {
      const imageUrl = 'https://example.com/current-image.jpg';
      mockWatch.mockReturnValue(imageUrl);

      renderComponent();

      expect(screen.getByTestId('dropzone-attachment-url')).toHaveTextContent(imageUrl);
    });

    it('handles empty file array gracefully (covers line 42)', () => {
      renderComponent();

      const dropzone = screen.getByTestId('custom-dropzone');
      expect(dropzone).toBeInTheDocument();
    });
  });

  describe('Form Validation and Edge Cases', () => {
    it('handles BlockNote editor changes and calls setValue (covers lines 52-53)', () => {
      renderComponent();

      const textarea = screen.getByTestId('blocknote-textarea');
      fireEvent.change(textarea, { target: { value: 'New description content' } });

      expect(mockSetValue).toHaveBeenCalledWith('image_description', 'New description content');
    });

    it('handles missing selectedWidget properties gracefully', () => {
      mockUseContentSettingsContext.mockReturnValue(
        createMockContext({
          selectedWidget: null,
        })
      );

      expect(() => renderComponent()).not.toThrow();

      const editor = screen.getByTestId('blocknote-markdown');
      expect(editor).toHaveAttribute('data-initial-markdown', '');
    });

    it('handles selectedWidget without properties', () => {
      mockUseContentSettingsContext.mockReturnValue(
        createMockContext({
          selectedWidget: {},
        })
      );

      expect(() => renderComponent()).not.toThrow();

      const editor = screen.getByTestId('blocknote-markdown');
      expect(editor).toHaveAttribute('data-initial-markdown', '');
    });
  });
});
