import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { MantineProvider } from '@mantine/core';
import ChatboxItem from './index';

const createMockChatbox = (override = {}) => ({
  id: '1',
  title: 'Test Chatbox',
  desc: 'Test Description',
  created: '2024-01-01T00:00:00Z',
  config: {
    name: 'Config Name',
    description: 'Config Description',
    image: 'https://example.com/image.jpg',
    widgets: [
      { type: 'welcome', name: 'Welcome Widget', variant: 'primary' as const },
      { type: 'faq', name: 'FAQ Widget' },
    ],
  },
  ...override,
});

const { mockTranslate, mockContext, createMockContext } = vi.hoisted(() => {
  const createMockContext = (override = {}) => ({
    setClickChatbox: vi.fn(),
    delOpen: vi.fn(),
    formOpen: vi.fn(),
    ...override,
  });

  const mockTranslate = vi.fn((key: string) => `home.${key}`);
  const mockContext = createMockContext();
  return { mockTranslate, mockContext, createMockContext };
});

vi.mock('@tolgee/react', () => ({ useTranslate: vi.fn() }));
vi.mock('@/contexts/SettingsContext', () => ({ useSettingsContext: vi.fn() }));
vi.mock('@/components', () => ({
  CustomBadge: vi.fn(({ leftSection, variant, children }) => (
    <div data-testid='custom-badge' data-variant={variant || 'default'}>
      {leftSection}
      {children}
    </div>
  )),
}));
vi.mock('@resola-ai/ui', () => ({
  CustomImage: vi.fn(({ url, className }) => (
    <img data-testid='custom-image' src={url} className={className} alt='default' />
  )),
}));

vi.mock('@mantine/core', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    Box: ({ className, onClick, children, ...props }: any) => (
      <div data-testid='box' className={className} onClick={onClick} {...props}>
        {children}
      </div>
    ),
    Stack: ({ className, children, ...props }: any) => (
      <div data-testid='stack' className={className} {...props}>
        {children}
      </div>
    ),
    Flex: ({ justify, direction, className, wrap, gap, children, ...props }: any) => (
      <div
        data-testid='flex'
        data-justify={justify}
        data-direction={direction}
        data-wrap={wrap}
        data-gap={gap}
        className={className}
        {...props}
      >
        {children}
      </div>
    ),
    Title: ({ lineClamp, order, className, children, ...props }: any) => (
      <h5
        data-testid='title'
        data-line-clamp={lineClamp}
        data-order={order}
        className={className}
        {...props}
      >
        {children}
      </h5>
    ),
    Text: ({ lineClamp, className, children, ...props }: any) => (
      <p data-testid='text' data-line-clamp={lineClamp} className={className} {...props}>
        {children}
      </p>
    ),
    Menu: Object.assign(
      ({ position, trigger, children, ...props }: any) => (
        <div data-testid='menu' data-position={position} data-trigger={trigger} {...props}>
          {children}
        </div>
      ),
      {
        Target: ({ children, ...props }: any) => (
          <div data-testid='menu-target' {...props}>
            {children}
          </div>
        ),
        Dropdown: ({ onClick, children, ...props }: any) => (
          <div data-testid='menu-dropdown' onClick={onClick} {...props}>
            {children}
          </div>
        ),
        Item: ({ onClick, children, ...props }: any) => (
          <div data-testid='menu-item' onClick={onClick} {...props}>
            {children}
          </div>
        ),
      }
    ),
    Divider: (props: any) => <hr data-testid='divider' {...props} />,
    rem: vi.fn((value: number) => `${value}rem`),
  };
});

vi.mock('@mantine/emotion', () => ({
  createStyles: vi.fn((styleFunction: (theme?: any) => any) => {
    // Execute the style function to cover CSS lines (11-61)
    const mockTheme = {
      colors: {
        silverFox: ['#f8f9fa', '#e9ecef', '#dee2e6', '#ced4da', '#adb5bd', '#6c757d'],
        pervenche: ['#e7f5ff', '#d0ebff', '#a5d8ff', '#74c0fc', '#339af0', '#228be6'],
        decaViolet: ['#f3f0ff', '#e5dbff', '#d0bfff', '#b197fc', '#9775fa', '#845ef7'],
      },
    };
    const styles = styleFunction(mockTheme);

    return () => ({
      classes: {
        container: 'mock-container-class',
        wrapper: 'mock-wrapper-class',
        image: 'mock-image-class',
        content: 'mock-content-class',
        title: 'mock-title-class',
        description: 'mock-description-class',
        imageContainer: 'mock-image-container-class',
        defaultImage: 'mock-default-image-class',
      },
    });
  }),
}));

vi.mock('@tabler/icons-react', () => ({
  IconDotsVertical: ({ onClick, ...props }: any) => (
    <div data-testid='icon-dots-vertical' onClick={onClick} {...props}>
      ⋮
    </div>
  ),
  IconPlugConnected: ({ size, ...props }: any) => (
    <div data-testid='icon-plug-connected' data-size={size} {...props}>
      🔌
    </div>
  ),
}));

import { useTranslate } from '@tolgee/react';
import { useSettingsContext } from '@/contexts/SettingsContext';

describe('ChatboxItem', () => {
  const mockOnClick = vi.fn();

  const renderComponent = (item = createMockChatbox()) =>
    render(
      <MantineProvider>
        <ChatboxItem item={item} onClick={mockOnClick} />
      </MantineProvider>
    );

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(useTranslate).mockReturnValue({ t: mockTranslate } as any);
    vi.mocked(useSettingsContext).mockReturnValue(mockContext as any);
  });

  describe('Content Display and Fallbacks', () => {
    it('displays primary content when available', () => {
      renderComponent();

      expect(screen.getByTestId('title')).toHaveTextContent('Test Chatbox');
      expect(screen.getByTestId('text')).toHaveTextContent('Test Description');
    });

    it('falls back to config values when primary content missing', () => {
      const itemWithoutPrimary = createMockChatbox({ title: undefined, desc: undefined });
      renderComponent(itemWithoutPrimary);

      expect(screen.getByTestId('title')).toHaveTextContent('Config Name');
      expect(screen.getByTestId('text')).toHaveTextContent('Config Description');
    });

    it('handles empty/null values gracefully', () => {
      const itemWithNullValues = createMockChatbox({
        title: null,
        desc: null,
        config: { ...createMockChatbox().config, name: null, description: null },
      });
      renderComponent(itemWithNullValues);

      // Should render without crashing
      expect(screen.getByTestId('title')).toBeInTheDocument();
      expect(screen.getByTestId('text')).toBeInTheDocument();
    });
  });

  describe('Image Display Logic', () => {
    it('displays custom image when provided', () => {
      renderComponent();

      const image = screen.getByRole('img');
      expect(image).toHaveAttribute('src', 'https://example.com/image.jpg');
      expect(image).toHaveAttribute('alt', 'https://example.com/image.jpg');
      expect(image).toHaveClass('mock-image-class');
    });

    it('displays default image when custom image not provided', () => {
      const itemWithoutImage = createMockChatbox({
        config: { ...createMockChatbox().config, image: undefined },
      });
      renderComponent(itemWithoutImage);

      expect(screen.getByTestId('custom-image')).toBeInTheDocument();
      expect(screen.getByTestId('custom-image')).toHaveAttribute('src', 'svg/default-bot.svg');
      expect(screen.getByTestId('custom-image')).toHaveClass('mock-default-image-class');

      const imageContainer = screen
        .getAllByTestId('box')
        .find((box) => box.className?.includes('mock-image-container-class'));
      expect(imageContainer).toBeInTheDocument();
    });

    it('handles null/empty image values', () => {
      const itemWithNullImage = createMockChatbox({
        config: { ...createMockChatbox().config, image: null },
      });
      renderComponent(itemWithNullImage);

      expect(screen.getByTestId('custom-image')).toBeInTheDocument();
    });
  });

  describe('Event Handling and Menu Actions', () => {
    it('handles main container click', () => {
      renderComponent();

      fireEvent.click(screen.getByTestId('box'));
      expect(mockOnClick).toHaveBeenCalledTimes(1);
    });

    it('handles edit action correctly', () => {
      const testItem = createMockChatbox();
      renderComponent(testItem);

      const menuItems = screen.getAllByTestId('menu-item');
      fireEvent.click(menuItems[0]);

      expect(mockContext.setClickChatbox).toHaveBeenCalledWith(testItem);
      expect(mockContext.formOpen).toHaveBeenCalledTimes(1);
    });

    it('handles delete action correctly', () => {
      const testItem = createMockChatbox();
      renderComponent(testItem);

      const menuItems = screen.getAllByTestId('menu-item');
      fireEvent.click(menuItems[1]);

      expect(mockContext.setClickChatbox).toHaveBeenCalledWith(testItem);
      expect(mockContext.delOpen).toHaveBeenCalledTimes(1);
    });

    it('uses correct translations for menu items', () => {
      renderComponent();

      expect(mockTranslate).toHaveBeenCalledWith('editChatbox');
      expect(mockTranslate).toHaveBeenCalledWith('deleteChatbox');

      const menuItems = screen.getAllByTestId('menu-item');
      expect(menuItems[0]).toHaveTextContent('home.editChatbox');
      expect(menuItems[1]).toHaveTextContent('home.deleteChatbox');
    });
  });
});
