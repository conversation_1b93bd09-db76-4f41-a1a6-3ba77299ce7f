import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { MantineProvider } from '@mantine/core';
import FormModal from './FormModal';

const MOCK_CHATBOX_DATA = {
  id: '1',
  title: 'Test Chatbox',
  desc: 'Test Description',
  config: {
    image: 'test-image.png',
  },
};

const setupMocks = () => {
  const mockTranslate = vi.fn((key: string, params?: any) => {
    if (params) {
      return `home.${key}.${JSON.stringify(params)}`;
    }
    return `home.${key}`;
  });
  const mockHandleSavingBox = vi.fn().mockResolvedValue(undefined);
  const mockFormClose = vi.fn();
  const mockCheckBoxValidation = vi.fn().mockReturnValue(true);

  return {
    mockTranslate,
    mockHandleSavingBox,
    mockFormClose,
    mockCheckBoxValidation,
  };
};

vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(),
}));

vi.mock('@mantine/core', async () => {
  const actual = await vi.importActual('@mantine/core');
  return {
    ...actual,
    Modal: vi.fn(({ opened, onClose, title, centered, w, className, children }) =>
      opened ? (
        <div
          data-testid='modal'
          data-centered={centered}
          data-width={w}
          className={className}
          onClick={(e) => e.target === e.currentTarget && onClose?.()}
        >
          <div data-testid='modal-title'>{title}</div>
          <button data-testid='modal-close' onClick={onClose}>
            Close
          </button>
          {children}
        </div>
      ) : null
    ),
    Box: vi.fn(({ mb, mt, children }) => (
      <div data-testid='box' data-margin-bottom={mb} data-margin-top={mt}>
        {children}
      </div>
    )),
    Flex: vi.fn(({ gap, my, justify, children }) => (
      <div data-testid='flex' data-gap={gap} data-margin-y={my} data-justify={justify}>
        {children}
      </div>
    )),
    Text: vi.fn(({ mb, children }) => (
      <p data-testid='text' data-margin-bottom={mb}>
        {children}
      </p>
    )),
    rem: vi.fn((value: number) => `${value}rem`),
  };
});

vi.mock('@mantine/emotion', () => ({
  createStyles: (styleFunction: (theme: any) => any) => {
    return () => ({
      classes: {
        modal: 'mock-modal-class',
      },
    });
  },
}));

vi.mock('react-hook-form', () => ({
  useForm: vi.fn(() => ({
    control: {},
    reset: vi.fn(),
    handleSubmit: vi.fn((fn) => (e) => {
      e.preventDefault();
      fn({ title: 'Test Title', desc: 'Test Description' });
    }),
  })),
}));

vi.mock('react-hook-form-mantine', () => ({
  TextInput: vi.fn(({ label, withAsterisk, placeholder, name, control, rules }) => (
    <div data-testid={`text-input-${name}`}>
      <label>
        {label}
        {withAsterisk && ' *'}
      </label>
      <input
        placeholder={placeholder}
        onChange={(e) => {
          if (rules?.validate) {
            rules.validate(e.target.value);
          }
        }}
      />
    </div>
  )),
  Textarea: vi.fn(({ label, withAsterisk, name, control, placeholder, rules }) => (
    <div data-testid={`textarea-${name}`}>
      <label>
        {label}
        {withAsterisk && ' *'}
      </label>
      <textarea
        placeholder={placeholder}
        onChange={(e) => {
          if (rules?.validate) {
            rules.validate(e.target.value);
          }
        }}
      />
    </div>
  )),
}));

vi.mock('@/components', () => ({
  UploadImage: vi.fn(({ rounded, defaultValue, staticUrl, onChange }) => (
    <div
      data-testid='upload-image'
      data-rounded={rounded}
      data-default-value={defaultValue}
      data-static-url={staticUrl}
    >
      <input
        type='file'
        data-testid='file-input'
        onChange={(e) => onChange?.(e.target.files?.[0] || null)}
      />
    </div>
  )),
}));

vi.mock('@/contexts/SettingsContext', () => ({
  useSettingsContext: vi.fn(),
}));

vi.mock('@/constants', () => ({
  BOX_NAME_MAX_LENGTH: 50,
  BOX_NAME_MIN_LENGTH: 3,
}));

vi.mock('@/utils', () => ({
  checkBoxValidation: vi.fn(),
}));

vi.mock('@resola-ai/ui', () => ({
  CustomButton: vi.fn(({ colorScheme, size, onClick, type, disabled, children }) => (
    <button
      data-testid={`custom-button-${colorScheme}`}
      data-color-scheme={colorScheme}
      data-size={size}
      type={type}
      disabled={disabled}
      onClick={onClick}
    >
      {children}
    </button>
  )),
}));

import { useTranslate } from '@tolgee/react';
import { Modal, Box, Flex, Text, rem } from '@mantine/core';
import { useForm } from 'react-hook-form';
import { TextInput, Textarea } from 'react-hook-form-mantine';
import { UploadImage } from '@/components';
import { useSettingsContext } from '@/contexts/SettingsContext';
import { BOX_NAME_MAX_LENGTH, BOX_NAME_MIN_LENGTH } from '@/constants';
import { checkBoxValidation } from '@/utils';
import { CustomButton } from '@resola-ai/ui';

describe('FormModal', () => {
  let mockTranslate: any;
  let mockHandleSavingBox: any;
  let mockFormClose: any;
  let mockCheckBoxValidation: any;
  let mockFormReset: any;
  let mockHandleSubmit: any;

  const renderComponent = () => {
    return render(
      <MantineProvider>
        <FormModal />
      </MantineProvider>
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();

    const mocks = setupMocks();
    mockTranslate = mocks.mockTranslate;
    mockHandleSavingBox = mocks.mockHandleSavingBox;
    mockFormClose = mocks.mockFormClose;
    mockCheckBoxValidation = mocks.mockCheckBoxValidation;

    mockFormReset = vi.fn();
    mockHandleSubmit = vi.fn((fn) => (e) => {
      e.preventDefault();
      fn({ title: 'Test Title', desc: 'Test Description' });
    });

    vi.mocked(useTranslate).mockReturnValue({ t: mockTranslate } as any);
    vi.mocked(useSettingsContext).mockReturnValue({
      handleSavingBox: mockHandleSavingBox,
      clickedChatbox: null,
      formOpened: true,
      formClose: mockFormClose,
    } as any);
    vi.mocked(checkBoxValidation).mockImplementation(mockCheckBoxValidation);
    vi.mocked(useForm).mockReturnValue({
      control: {},
      reset: mockFormReset,
      handleSubmit: mockHandleSubmit,
    } as any);
  });

  describe('Modal Rendering and Structure', () => {
    it('should render modal when formOpened is true', () => {
      renderComponent();

      expect(screen.getByTestId('modal')).toBeInTheDocument();
      expect(Modal).toHaveBeenCalledWith(
        expect.objectContaining({
          opened: true,
          centered: true,
        }),
        expect.any(Object)
      );
    });

    it('should not render modal when formOpened is false', () => {
      vi.mocked(useSettingsContext).mockReturnValue({
        handleSavingBox: mockHandleSavingBox,
        clickedChatbox: null,
        formOpened: false,
        formClose: mockFormClose,
      } as any);

      renderComponent();

      expect(screen.queryByTestId('modal')).not.toBeInTheDocument();
    });

    it('should render all form elements', () => {
      renderComponent();

      expect(screen.getByTestId('upload-image')).toBeInTheDocument();
      expect(screen.getByTestId('text-input-title')).toBeInTheDocument();
      expect(screen.getByTestId('textarea-desc')).toBeInTheDocument();
      expect(screen.getByTestId('custom-button-tertiary')).toBeInTheDocument();
      expect(screen.getByTestId('custom-button-primary')).toBeInTheDocument();
    });

    it('should apply correct modal configuration', () => {
      renderComponent();

      expect(Modal).toHaveBeenCalledWith(
        expect.objectContaining({
          w: '440rem',
          centered: true,
          className: 'mock-modal-class',
        }),
        expect.any(Object)
      );
    });

    it('should render form with correct structure', () => {
      renderComponent();

      const form = screen.getByTestId('modal').querySelector('form');
      expect(form).toBeInTheDocument();
      expect(form).toHaveStyle({ padding: '10rem' });
    });
  });

  describe('Create vs Edit Mode', () => {
    it('should show create mode when clickedChatbox is null', () => {
      renderComponent();

      expect(mockTranslate).toHaveBeenCalledWith('newChatbox');
      expect(screen.getByTestId('modal-title')).toHaveTextContent('home.newChatbox');
      expect(screen.getByTestId('custom-button-primary')).toHaveTextContent('home.create');
    });

    it('should show edit mode when clickedChatbox exists', () => {
      vi.mocked(useSettingsContext).mockReturnValue({
        handleSavingBox: mockHandleSavingBox,
        clickedChatbox: MOCK_CHATBOX_DATA,
        formOpened: true,
        formClose: mockFormClose,
      } as any);

      renderComponent();

      expect(mockTranslate).toHaveBeenCalledWith('editChatbox');
      expect(screen.getByTestId('modal-title')).toHaveTextContent('home.editChatbox');
      expect(screen.getByTestId('custom-button-primary')).toHaveTextContent('home.save');
    });

    it('should populate form fields in edit mode', () => {
      vi.mocked(useSettingsContext).mockReturnValue({
        handleSavingBox: mockHandleSavingBox,
        clickedChatbox: MOCK_CHATBOX_DATA,
        formOpened: true,
        formClose: mockFormClose,
      } as any);

      renderComponent();

      expect(mockFormReset).toHaveBeenCalledWith({
        title: MOCK_CHATBOX_DATA.title,
        desc: MOCK_CHATBOX_DATA.desc,
      });
    });

    it('should populate UploadImage with existing image in edit mode', () => {
      vi.mocked(useSettingsContext).mockReturnValue({
        handleSavingBox: mockHandleSavingBox,
        clickedChatbox: MOCK_CHATBOX_DATA,
        formOpened: true,
        formClose: mockFormClose,
      } as any);

      renderComponent();

      expect(screen.getByTestId('upload-image')).toHaveAttribute(
        'data-static-url',
        MOCK_CHATBOX_DATA.config.image
      );
    });
  });

  describe('Event Handlers', () => {
    it('should call handleClose when cancel button is clicked', () => {
      renderComponent();

      const cancelButton = screen.getByTestId('custom-button-tertiary');
      fireEvent.click(cancelButton);

      expect(mockFormClose).toHaveBeenCalled();
      expect(mockFormReset).toHaveBeenCalled();
    });

    it('should call handleClose when modal close is triggered', () => {
      renderComponent();

      const modal = screen.getByTestId('modal');
      fireEvent.click(modal); // Click on modal backdrop

      expect(mockFormClose).toHaveBeenCalled();
      expect(mockFormReset).toHaveBeenCalled();
    });

    it('should reset file state when handleClose is called', () => {
      renderComponent();

      // First set a file
      const fileInput = screen.getByTestId('file-input');
      const mockFile = new File(['test'], 'test.png', { type: 'image/png' });
      fireEvent.change(fileInput, { target: { files: [mockFile] } });

      // Then close the modal
      const cancelButton = screen.getByTestId('custom-button-tertiary');
      fireEvent.click(cancelButton);

      expect(mockFormClose).toHaveBeenCalled();
      expect(mockFormReset).toHaveBeenCalled();
    });

    it('should reset saving state when handleClose is called', () => {
      renderComponent();

      const cancelButton = screen.getByTestId('custom-button-tertiary');
      fireEvent.click(cancelButton);

      expect(mockFormClose).toHaveBeenCalled();
    });
  });

  describe('Form Validation', () => {
    it('should validate title field with checkBoxValidation', () => {
      renderComponent();

      const titleInput = screen.getByTestId('text-input-title').querySelector('input');
      expect(titleInput).toBeInTheDocument();

      // Simulate input change to trigger validation
      fireEvent.change(titleInput!, { target: { value: 'test value' } });

      expect(mockCheckBoxValidation).toHaveBeenCalledWith(
        'test value',
        expect.stringContaining('nameMinMaxError')
      );
    });

    it('should validate description field with checkBoxValidation', () => {
      renderComponent();

      const descTextarea = screen.getByTestId('textarea-desc').querySelector('textarea');
      expect(descTextarea).toBeInTheDocument();

      // Simulate textarea change to trigger validation
      fireEvent.change(descTextarea!, { target: { value: 'test description' } });

      expect(mockCheckBoxValidation).toHaveBeenCalledWith(
        'test description',
        expect.stringContaining('descriptionMinMaxError')
      );
    });

    it('should pass correct validation parameters for title field', () => {
      renderComponent();

      const titleInput = screen.getByTestId('text-input-title').querySelector('input');
      expect(titleInput).toBeInTheDocument();

      // Trigger validation by changing input value
      fireEvent.change(titleInput!, { target: { value: 'test value' } });

      // Check that translation was called with correct parameters
      expect(mockTranslate).toHaveBeenCalledWith('nameMinMaxError', {
        min: 3, // BOX_NAME_MIN_LENGTH
        max: 50, // BOX_NAME_MAX_LENGTH
      });
    });

    it('should pass correct validation parameters for description field', () => {
      renderComponent();

      const descTextarea = screen.getByTestId('textarea-desc').querySelector('textarea');
      expect(descTextarea).toBeInTheDocument();

      // Trigger validation by changing textarea value
      fireEvent.change(descTextarea!, { target: { value: 'test description' } });

      // Check that translation was called with correct parameters
      expect(mockTranslate).toHaveBeenCalledWith('descriptionMinMaxError', {
        min: 3, // BOX_NAME_MIN_LENGTH
        max: 50, // BOX_NAME_MAX_LENGTH
      });
    });
  });
});
