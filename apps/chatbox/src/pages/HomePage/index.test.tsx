import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { MantineProvider } from '@mantine/core';
import HomePage from './index';

// Consolidated mock setup using vi.hoisted
const { mockTranslate, mockFormOpen, mockErrorHandler, mockSettingsContext } = vi.hoisted(() => {
  const mockTranslate = vi.fn((key: string) => `home.${key}`);
  const mockFormOpen = vi.fn();
  const mockErrorHandler = vi.fn();
  const mockSettingsContext = {
    formOpen: mockFormOpen,
    LIMITATION_OF_CHATBOXES_PER_ORGANIZATION: 5,
    chatboxList: [
      { id: '1', name: 'Chatbox 1' },
      { id: '2', name: 'Chatbox 2' },
    ],
  };

  return { mockTranslate, mockFormOpen, mockErrorHandler, mockSettingsContext };
});

// Mock dependencies
vi.mock('@tolgee/react', () => ({ useTranslate: vi.fn() }));
vi.mock('@/contexts/SettingsContext', () => ({ useSettingsContext: vi.fn() }));
vi.mock('@/utils/errorHandler', () => ({
  errorHandler: vi.fn(),
  ERROR_TYPES: { CHATBOXES_PER_ORGANIZATION_LIMITATION: 'CHATBOXES_PER_ORGANIZATION_LIMITATION' },
}));

vi.mock('@mantine/core', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    Container: ({ className, children, ...props }: any) => (
      <div data-testid='container' className={className} {...props}>
        {children}
      </div>
    ),
    Flex: ({ w, justify, align, children, ...props }: any) => (
      <div data-testid='flex' data-width={w} data-justify={justify} data-align={align} {...props}>
        {children}
      </div>
    ),
    Title: ({ className, children, ...props }: any) => (
      <h1 data-testid='title' className={className} {...props}>
        {children}
      </h1>
    ),
    Divider: ({ className, color, ...props }: any) => (
      <hr data-testid='divider' className={className} data-color={color} {...props} />
    ),
    rem: vi.fn((value: number) => `${value}rem`),
  };
});

vi.mock('@mantine/emotion', () => ({
  createStyles: vi.fn((styleFunction: (theme?: any) => any) => {
    // Execute the style function to cover CSS lines (14-28)
    const mockTheme = {
      colors: {
        silverFox: [
          '#f8f9fa',
          '#e9ecef',
          '#dee2e6',
          '#ced4da',
          '#adb5bd',
          '#6c757d',
          '#495057',
          '#343a40',
          '#212529',
          '#000000',
        ],
      },
    };
    const styles = styleFunction(mockTheme);

    return () => ({
      classes: {
        container: 'mock-container-class',
        title: 'mock-title-class',
        divider: 'mock-divider-class',
      },
    });
  }),
}));

vi.mock('@tabler/icons-react', () => ({
  IconPlus: (props: any) => (
    <div data-testid='icon-plus' {...props}>
      Plus Icon
    </div>
  ),
}));

vi.mock('@resola-ai/ui', () => ({
  CustomButton: ({ colorScheme, leftIcon, size, onClick, children, ...props }: any) => (
    <button
      data-testid='custom-button'
      data-color-scheme={colorScheme}
      data-size={size}
      onClick={onClick}
      {...props}
    >
      {leftIcon}
      {children}
    </button>
  ),
}));

vi.mock('./components', () => ({
  ChatboxList: (props: any) => (
    <div data-testid='chatbox-list' {...props}>
      ChatboxList
    </div>
  ),
}));

vi.mock('./components/DeleteModal', () => ({
  default: (props: any) => (
    <div data-testid='delete-modal' {...props}>
      DeleteModal
    </div>
  ),
}));

vi.mock('./components/FormModal', () => ({
  default: (props: any) => (
    <div data-testid='form-modal' {...props}>
      FormModal
    </div>
  ),
}));

// Import mocked modules
import { useTranslate } from '@tolgee/react';
import { useSettingsContext } from '@/contexts/SettingsContext';
import { errorHandler, ERROR_TYPES } from '@/utils/errorHandler';

describe('HomePage', () => {
  const renderComponent = () => {
    return render(
      <MantineProvider>
        <HomePage />
      </MantineProvider>
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(useTranslate).mockReturnValue({ t: mockTranslate } as any);
    vi.mocked(useSettingsContext).mockReturnValue(mockSettingsContext as any);
    vi.mocked(errorHandler).mockImplementation(mockErrorHandler);
  });

  describe('Component Structure and Styling', () => {
    it('renders main page structure with correct components and CSS classes', () => {
      renderComponent();

      expect(screen.getByTestId('container')).toBeInTheDocument();
      expect(screen.getByTestId('container')).toHaveClass('mock-container-class');
      expect(screen.getByTestId('flex')).toBeInTheDocument();
      expect(screen.getByTestId('title')).toBeInTheDocument();
      expect(screen.getByTestId('title')).toHaveClass('mock-title-class');
      expect(screen.getByTestId('custom-button')).toBeInTheDocument();
      expect(screen.getByTestId('divider')).toBeInTheDocument();
      expect(screen.getByTestId('divider')).toHaveClass('mock-divider-class');
      expect(screen.getByTestId('chatbox-list')).toBeInTheDocument();
      expect(screen.getByTestId('form-modal')).toBeInTheDocument();
      expect(screen.getByTestId('delete-modal')).toBeInTheDocument();
    });

    it('configures layout components with correct props', () => {
      renderComponent();

      expect(screen.getByTestId('flex')).toHaveAttribute('data-width', '100%');
      expect(screen.getByTestId('flex')).toHaveAttribute('data-justify', 'space-between');
      expect(screen.getByTestId('flex')).toHaveAttribute('data-align', 'center');
      expect(screen.getByTestId('divider')).toHaveAttribute('data-color', 'silverFox.5');
    });
  });

  describe('Content Display and Translations', () => {
    it('displays translated content correctly', () => {
      renderComponent();

      expect(mockTranslate).toHaveBeenCalledWith('pageTitle');
      expect(mockTranslate).toHaveBeenCalledWith('newChatbox');
      expect(screen.getByTestId('title')).toHaveTextContent('home.pageTitle');
      expect(screen.getByTestId('custom-button')).toHaveTextContent('home.newChatbox');
    });

    it('configures button with correct props', () => {
      renderComponent();

      expect(screen.getByTestId('custom-button')).toHaveAttribute('data-color-scheme', 'primary');
      expect(screen.getByTestId('custom-button')).toHaveAttribute('data-size', 'lg');
      expect(screen.getByTestId('icon-plus')).toBeInTheDocument();
    });
  });

  describe('Form Opening Logic and Limitations', () => {
    it('opens form when chatbox limit not reached', () => {
      renderComponent();

      fireEvent.click(screen.getByTestId('custom-button'));

      expect(mockFormOpen).toHaveBeenCalledTimes(1);
      expect(mockErrorHandler).not.toHaveBeenCalled();
    });

    it('shows error when chatbox limit reached', () => {
      // Mock context with chatboxList at limit
      vi.mocked(useSettingsContext).mockReturnValue({
        ...mockSettingsContext,
        chatboxList: new Array(5)
          .fill(0)
          .map((_, i) => ({ id: `${i + 1}`, name: `Chatbox ${i + 1}` })),
      } as any);

      renderComponent();
      fireEvent.click(screen.getByTestId('custom-button'));

      expect(mockErrorHandler).toHaveBeenCalledWith(
        ERROR_TYPES.CHATBOXES_PER_ORGANIZATION_LIMITATION,
        mockTranslate
      );
      expect(mockFormOpen).not.toHaveBeenCalled();
    });

    it('shows error when chatbox limit exceeded', () => {
      // Mock context with chatboxList over limit
      vi.mocked(useSettingsContext).mockReturnValue({
        ...mockSettingsContext,
        chatboxList: new Array(6)
          .fill(0)
          .map((_, i) => ({ id: `${i + 1}`, name: `Chatbox ${i + 1}` })),
      } as any);

      renderComponent();
      fireEvent.click(screen.getByTestId('custom-button'));

      expect(mockErrorHandler).toHaveBeenCalledWith(
        ERROR_TYPES.CHATBOXES_PER_ORGANIZATION_LIMITATION,
        mockTranslate
      );
      expect(mockFormOpen).not.toHaveBeenCalled();
    });

    it('handles edge case with empty chatbox list', () => {
      vi.mocked(useSettingsContext).mockReturnValue({
        ...mockSettingsContext,
        chatboxList: [],
      } as any);

      renderComponent();
      fireEvent.click(screen.getByTestId('custom-button'));

      expect(mockFormOpen).toHaveBeenCalledTimes(1);
      expect(mockErrorHandler).not.toHaveBeenCalled();
    });
  });
});
