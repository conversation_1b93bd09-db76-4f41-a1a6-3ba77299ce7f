import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { MantineProvider } from '@mantine/core';
import AutoOpenSection from './index';

// Consolidated mock setup using vi.hoisted
const { mockTranslate, mockForm } = vi.hoisted(() => {
  const mockTranslate = vi.fn((key: string) => `mocked.${key}`);
  const mockForm = {
    watch: vi.fn(),
    setValue: vi.fn(),
    getValues: vi.fn(),
    trigger: vi.fn(),
    formState: { errors: {}, isValid: true, isDirty: false },
  };

  return { mockTranslate, mockForm };
});

// Mock dependencies
vi.mock('@tolgee/react', () => ({ useTranslate: vi.fn() }));
vi.mock('@/pages/GeneralSetting/GeneralSettingsContext', () => ({
  useGeneralSettingsContext: vi.fn(),
}));

vi.mock('@/components', () => ({
  CustomCard: ({ title, children, ...props }: any) => (
    <div data-testid='custom-card' data-title={title} {...props}>
      {children}
    </div>
  ),
  CustomOption: ({ title, active, onClick, children, ...props }: any) => (
    <div
      data-testid='custom-option'
      data-title={title}
      data-active={active === undefined || active === null ? undefined : String(active)}
      onClick={onClick}
      className={active ? 'active' : 'inactive'}
      {...props}
    >
      {children}
    </div>
  ),
}));

vi.mock('@resola-ai/ui', () => ({
  CustomImage: ({ url, className, ...props }: any) => (
    <img data-testid='custom-image' src={url} className={className} alt='test-image' {...props} />
  ),
}));

vi.mock('@mantine/core', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    Box: ({ className, children, ...props }: any) => (
      <div data-testid='box' className={className} {...props}>
        {children}
      </div>
    ),
    Flex: ({ gap, w, children, ...props }: any) => (
      <div data-testid='flex' data-gap={gap} data-width={w} {...props}>
        {children}
      </div>
    ),
    rem: vi.fn((value: number) => `${value}rem`),
  };
});

vi.mock('@mantine/emotion', () => ({
  createStyles: vi.fn((styleFunction: (theme?: any) => any) => {
    // Execute the style function to cover CSS lines (9-27)
    const mockTheme = {
      colors: {
        decaNavy: ['#e7f5ff', '#d0ebff', '#a5d8ff', '#74c0fc', '#339af0', '#228be6'],
      },
    };
    const styles = styleFunction(mockTheme);

    return () => ({
      classes: {
        option: 'option-class',
        dot: 'dot-class',
        hidden: 'hidden-class',
        customImage: 'custom-image-class',
      },
      cx: vi.fn((...args) => args.filter(Boolean).join(' ')),
    });
  }),
}));

// Import mocked modules
import { useTranslate } from '@tolgee/react';
import { useGeneralSettingsContext } from '@/pages/GeneralSetting/GeneralSettingsContext';

describe('AutoOpenSection', () => {
  const renderComponent = () => {
    return render(
      <MantineProvider>
        <AutoOpenSection />
      </MantineProvider>
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(useTranslate).mockReturnValue({ t: mockTranslate } as any);
    vi.mocked(useGeneralSettingsContext).mockReturnValue({ form: mockForm } as any);
    mockForm.watch.mockReturnValue(false); // Default to false
  });

  describe('Component Structure and Styling', () => {
    it('renders with correct structure and CSS classes', () => {
      renderComponent();

      expect(screen.getByTestId('custom-card')).toBeInTheDocument();
      expect(screen.getByTestId('flex')).toBeInTheDocument();
      expect(screen.getAllByTestId('custom-option')).toHaveLength(2);
      expect(screen.getAllByTestId('custom-image')).toHaveLength(2);
      expect(screen.getAllByTestId('box')).toHaveLength(4); // 2 option containers + 2 Dot components

      // Check that dot elements are rendered with correct classes
      const dotElements = document.querySelectorAll('.dot-class');
      expect(dotElements).toHaveLength(2);
    });

    it('configures layout components with correct props', () => {
      renderComponent();

      expect(screen.getByTestId('flex')).toHaveAttribute('data-gap', '16rem');
      expect(screen.getByTestId('flex')).toHaveAttribute('data-width', '100%');
    });
  });

  describe('Content Display and Translations', () => {
    it('displays translated content correctly', () => {
      renderComponent();

      expect(mockTranslate).toHaveBeenCalledWith('autoOpenCardTitle');
      expect(mockTranslate).toHaveBeenCalledWith('autoOpenOptionTitle');
      expect(mockTranslate).toHaveBeenCalledWith('launcherOnly');

      expect(screen.getByTestId('custom-card')).toHaveAttribute(
        'data-title',
        'mocked.autoOpenCardTitle'
      );

      const options = screen.getAllByTestId('custom-option');
      expect(options[0]).toHaveAttribute('data-title', 'mocked.autoOpenOptionTitle');
      expect(options[1]).toHaveAttribute('data-title', 'mocked.launcherOnly');
    });

    it('renders images with correct URLs', () => {
      renderComponent();

      const images = screen.getAllByTestId('custom-image');
      expect(images).toHaveLength(2);
      images.forEach((image) => {
        expect(image).toHaveAttribute('src', 'images/cb-window.png');
      });
    });

    it('applies correct CSS classes to second image', () => {
      renderComponent();

      const images = screen.getAllByTestId('custom-image');
      expect(images[1]).toHaveClass('hidden-class custom-image-class');
    });
  });

  describe('Auto Open State Management', () => {
    it('shows correct active state when autoOpen is true', () => {
      mockForm.watch.mockReturnValue(true);
      renderComponent();

      const options = screen.getAllByTestId('custom-option');
      expect(options[0]).toHaveAttribute('data-active', 'true');
      expect(options[0]).toHaveClass('active');
      expect(options[1]).toHaveAttribute('data-active', 'false');
      expect(options[1]).toHaveClass('inactive');
    });

    it('shows correct active state when autoOpen is false', () => {
      mockForm.watch.mockReturnValue(false);
      renderComponent();

      const options = screen.getAllByTestId('custom-option');
      expect(options[0]).toHaveAttribute('data-active', 'false');
      expect(options[0]).toHaveClass('inactive');
      expect(options[1]).toHaveAttribute('data-active', 'true');
      expect(options[1]).toHaveClass('active');
    });

    it('handles form setValue when first option clicked', () => {
      renderComponent();

      const options = screen.getAllByTestId('custom-option');
      fireEvent.click(options[0]);

      expect(mockForm.setValue).toHaveBeenCalledWith('autoOpen', true);
    });

    it('handles form setValue when second option clicked', () => {
      renderComponent();

      const options = screen.getAllByTestId('custom-option');
      fireEvent.click(options[1]);

      expect(mockForm.setValue).toHaveBeenCalledWith('autoOpen', false);
    });
  });
});
