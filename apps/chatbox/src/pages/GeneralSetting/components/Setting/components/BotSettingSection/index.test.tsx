import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { MantineProvider } from '@mantine/core';
import BotSettingSection from './index';

const { mockTranslate, mockForm, mockCheckBoxValidation } = vi.hoisted(() => {
  const mockTranslate = vi.fn((key: string, options?: any) => {
    if (options?.ns) return `mocked.${options.ns}.${key}`;
    return `mocked.${key}`;
  });

  const mockForm = {
    setValue: vi.fn(),
    watch: vi.fn(),
    control: { name: 'mock-control' },
    getValues: vi.fn(),
    trigger: vi.fn(),
    formState: { errors: {}, isValid: true, isDirty: false },
  };

  const mockCheckBoxValidation = vi.fn();

  return { mockTranslate, mockForm, mockCheckBoxValidation };
});

vi.mock('@tolgee/react', () => ({ useTranslate: vi.fn() }));
vi.mock('@/pages/GeneralSetting/GeneralSettingsContext', () => ({
  useGeneralSettingsContext: vi.fn(),
}));
vi.mock('@/utils', () => ({ checkBoxValidation: mockCheckBoxValidation }));
vi.mock('@/constants', () => ({ BOX_NAME_MAX_LENGTH: 255, BOX_NAME_MIN_LENGTH: 3 }));

vi.mock('@/components', () => ({
  CustomCard: ({ title, children, ...props }: any) => (
    <div data-testid='custom-card' data-title={title} {...props}>
      {children}
    </div>
  ),
  CustomLabel: ({ text, ...props }: any) => (
    <div data-testid='custom-label' data-text={text} {...props}>
      {text}
    </div>
  ),
  UploadImage: ({ defaultValue, staticUrl, rounded, onChange, ...props }: any) => (
    <div data-testid='upload-image' {...props}>
      <div data-testid='upload-default-value'>{defaultValue}</div>
      <div data-testid='upload-static-url'>{staticUrl}</div>
      <div data-testid='upload-rounded'>{rounded ? 'true' : 'false'}</div>
      <button
        data-testid='upload-button'
        onClick={() => {
          const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
          onChange?.(mockFile);
        }}
      >
        Upload
      </button>
      <button data-testid='upload-clear-button' onClick={() => onChange?.(null)}>
        Clear
      </button>
    </div>
  ),
}));

vi.mock('react-hook-form-mantine', () => ({
  TextInput: ({ name, control, maxLength, rules, className, ...props }: any) => (
    <div data-testid='text-input' data-name={name} className={className} {...props}>
      <div data-testid='input-max-length'>{maxLength}</div>
      <input data-testid='text-field' onChange={(e) => rules?.validate?.(e.target.value)} />
    </div>
  ),
  Textarea: ({ name, control, minRows, maxRows, maxLength, rules, className, ...props }: any) => (
    <div data-testid='textarea' data-name={name} className={className} {...props}>
      <div data-testid='textarea-rows'>
        {minRows}-{maxRows}
      </div>
      <div data-testid='textarea-max-length'>{maxLength}</div>
      <textarea data-testid='textarea-field' onChange={(e) => rules?.validate?.(e.target.value)} />
    </div>
  ),
}));

vi.mock('@mantine/core', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    Stack: ({ gap, children, ...props }: any) => (
      <div data-testid='stack' data-gap={gap} {...props}>
        {children}
      </div>
    ),
    Flex: ({ gap, align, children, ...props }: any) => (
      <div data-testid='flex' data-gap={gap} data-align={align} {...props}>
        {children}
      </div>
    ),
    rem: vi.fn((value: number) => `${value}rem`),
  };
});

vi.mock('@mantine/emotion', () => ({
  createStyles: vi.fn((styleFunction: (theme?: any) => any) => {
    const mockTheme = {
      colors: {
        silverFox: [
          '#f8f9fa',
          '#e9ecef',
          '#dee2e6',
          '#ced4da',
          '#adb5bd',
          '#6c757d',
          '#495057',
          '#343a40',
          '#212529',
          '#000000',
        ],
      },
    };
    const styles = styleFunction(mockTheme);

    return () => ({
      classes: {
        input: 'input-class',
        button: 'button-class',
      },
    });
  }),
}));

// Import mocked modules
import { useTranslate } from '@tolgee/react';
import { useGeneralSettingsContext } from '@/pages/GeneralSetting/GeneralSettingsContext';
import { checkBoxValidation } from '@/utils';
import { BOX_NAME_MAX_LENGTH, BOX_NAME_MIN_LENGTH } from '@/constants';

describe('BotSettingSection', () => {
  const renderComponent = () => {
    return render(
      <MantineProvider>
        <BotSettingSection />
      </MantineProvider>
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockTranslate.mockClear();
    mockForm.setValue.mockClear();
    mockForm.watch.mockClear();
    mockCheckBoxValidation.mockClear();

    vi.mocked(useTranslate).mockReturnValue({ t: mockTranslate } as any);
    vi.mocked(useGeneralSettingsContext).mockReturnValue({ form: mockForm } as any);
    mockForm.watch.mockReturnValue('test-image.jpg');
    mockCheckBoxValidation.mockReturnValue(true);
  });

  describe('Component Structure and Styling', () => {
    it('renders with correct structure and CSS classes', () => {
      renderComponent();

      expect(screen.getByTestId('custom-card')).toBeInTheDocument();
      expect(screen.getAllByTestId('stack')).toHaveLength(3); // 3 Stack components
      expect(screen.getByTestId('flex')).toBeInTheDocument();
      expect(screen.getAllByTestId('custom-label')).toHaveLength(3);
      expect(screen.getByTestId('upload-image')).toBeInTheDocument();
      expect(screen.getByTestId('text-input')).toBeInTheDocument();
      expect(screen.getByTestId('textarea')).toBeInTheDocument();
    });

    it('configures layout components with correct props', () => {
      renderComponent();

      const stacks = screen.getAllByTestId('stack');
      stacks.forEach((stack) => {
        expect(stack).toHaveAttribute('data-gap', '8');
      });

      expect(screen.getByTestId('flex')).toHaveAttribute('data-gap', '8');
      expect(screen.getByTestId('flex')).toHaveAttribute('data-align', 'center');
    });
  });

  describe('Image Upload Functionality', () => {
    it('handles file upload correctly', () => {
      renderComponent();

      const uploadButton = screen.getByTestId('upload-button');
      fireEvent.click(uploadButton);

      expect(mockForm.setValue).toHaveBeenCalledWith('image', expect.any(File));
    });

    it('handles null file upload (clear)', () => {
      renderComponent();

      const clearButton = screen.getByTestId('upload-clear-button');
      fireEvent.click(clearButton);

      expect(mockForm.setValue).not.toHaveBeenCalled(); // null file should not trigger setValue
    });

    it('watches image value from form', () => {
      renderComponent();

      expect(mockForm.watch).toHaveBeenCalledWith('image');
      expect(screen.getByTestId('upload-static-url')).toHaveTextContent('test-image.jpg');
    });

    it('handles different image values from form watch', () => {
      mockForm.watch.mockReturnValue('different-image.png');
      renderComponent();

      expect(screen.getByTestId('upload-static-url')).toHaveTextContent('different-image.png');
    });
  });

  describe('Form Validation', () => {
    it('validates botName field with correct parameters', () => {
      renderComponent();

      const textField = screen.getByTestId('text-field');
      fireEvent.change(textField, { target: { value: 'Test Bot Name' } });

      expect(checkBoxValidation).toHaveBeenCalledWith(
        'Test Bot Name',
        'mocked.home.nameMinMaxError'
      );
    });

    it('validates botDescription field with correct parameters', () => {
      renderComponent();

      const textareaField = screen.getByTestId('textarea-field');
      fireEvent.change(textareaField, { target: { value: 'Test Bot Description' } });

      expect(checkBoxValidation).toHaveBeenCalledWith(
        'Test Bot Description',
        'mocked.home.descriptionMinMaxError'
      );
    });

    it('calls translation with correct parameters for validation messages', () => {
      renderComponent();

      // Trigger validation by changing input values
      fireEvent.change(screen.getByTestId('text-field'), { target: { value: 'test' } });

      expect(mockTranslate).toHaveBeenCalledWith('nameMinMaxError', {
        ns: 'home',
        min: BOX_NAME_MIN_LENGTH,
        max: BOX_NAME_MAX_LENGTH,
      });

      fireEvent.change(screen.getByTestId('textarea-field'), { target: { value: 'test' } });

      expect(mockTranslate).toHaveBeenCalledWith('descriptionMinMaxError', {
        ns: 'home',
        min: BOX_NAME_MIN_LENGTH,
        max: BOX_NAME_MAX_LENGTH,
      });
    });

    it('handles validation failure scenarios', () => {
      mockCheckBoxValidation.mockReturnValue('Validation error message');
      renderComponent();

      fireEvent.change(screen.getByTestId('text-field'), { target: { value: 'x' } });

      expect(checkBoxValidation).toHaveBeenCalledWith('x', 'mocked.home.nameMinMaxError');
    });
  });
});
