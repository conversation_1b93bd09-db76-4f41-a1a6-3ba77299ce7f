import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { MantineProvider } from '@mantine/core';
import BotTriggerSection from './index';

// Test constants
const MOCK_CHATBOX_ID = 'test-chatbox-id';
const MOCK_CLASSES = {
  input: 'input-class',
  customOption: 'custom-option-class',
  description: 'description-class',
  box: 'box-class',
  actionIcon: 'action-icon-class',
  scriptText: 'script-text-class',
};

// Mock setup
const setupMocks = () => {
  const mockTranslate = vi.fn((key: string) => `mocked.${key}`);
  const mockForm = {
    control: { name: 'mock-control' },
    watch: vi.fn().mockReturnValue('OPEN_PAGE'),
  };
  const mockSettingsContext = { currentChatboxId: MOCK_CHATBOX_ID };

  return { mockTranslate, mockForm, mockSettingsContext };
};

// Mock external dependencies
vi.mock('@tolgee/react', () => ({ useTranslate: vi.fn() }));
vi.mock('@/components', () => ({
  CustomCard: vi.fn(({ title, children }) => (
    <div data-testid='custom-card' data-title={title}>
      {children}
    </div>
  )),
  CustomLabel: vi.fn(({ text }) => (
    <div data-testid='custom-label' data-text={text}>
      {text}
    </div>
  )),
}));
vi.mock('react-hook-form-mantine', () => ({
  Select: vi.fn(({ name, data, className, withCheckIcon, allowDeselect }) => (
    <div data-testid='select-component' data-name={name} className={className}>
      <div data-testid='select-props'>{JSON.stringify({ withCheckIcon, allowDeselect })}</div>
      <div data-testid='select-data'>{JSON.stringify(data)}</div>
    </div>
  )),
}));
vi.mock('@mantine/core', async () => {
  const actual = await vi.importActual('@mantine/core');
  return {
    ...actual,
    Stack: ({ children, gap }: any) => (
      <div data-testid='stack' data-gap={gap}>
        {children}
      </div>
    ),
    Text: ({ children, className, ...props }: any) => (
      <div data-testid='text' className={className} {...props}>
        {children}
      </div>
    ),
    Tooltip: ({ label, children }: any) => (
      <div data-testid='tooltip' data-label={label}>
        {children}
      </div>
    ),
    CopyButton: ({ value, timeout, children }: any) => (
      <div data-testid='copy-button' data-value={value} data-timeout={timeout}>
        {children({ copied: false, copy: vi.fn() })}
      </div>
    ),
    ActionIcon: ({ className, onClick, children }: any) => (
      <button data-testid='action-icon' className={className} onClick={onClick}>
        {children}
      </button>
    ),
    rem: (value: number) => `${value}rem`,
  };
});
vi.mock('@tabler/icons-react', () => ({
  IconCheck: () => <div data-testid='icon-check'>Check</div>,
  IconCopy: () => <div data-testid='icon-copy'>Copy</div>,
}));
vi.mock('@/pages/GeneralSetting/GeneralSettingsContext', () => ({
  useGeneralSettingsContext: vi.fn(),
}));
vi.mock('@/contexts/SettingsContext', () => ({ useSettingsContext: vi.fn() }));
vi.mock('@/constants', () => ({
  BOT_TRIGGER_TYPES: {
    OPEN_PAGE: 'OPEN_PAGE',
    TIME_DISPLAY: 'TIME_DISPLAY',
    SCROLL_TO_HTML_ELEMENT: 'SCROLL_TO_HTML_ELEMENT',
    CLICK_A_BUTTON: 'CLICK_A_BUTTON',
  },
}));
vi.mock('@mantine/emotion', () => ({
  createStyles: (styleFunction: (theme: any) => any) => {
    // Execute the style function immediately to cover the CSS lines
    const mockTheme = {
      colors: {
        silverFox: [
          '#silverFox0',
          '#silverFox1',
          '#silverFox2',
          '#silverFox3',
          '#silverFox4',
          '#silverFox5',
          '#silverFox6',
          '#silverFox7',
          '#silverFox8',
          '#silverFox9',
        ],
        navy: [
          '#navy0',
          '#navy1',
          '#navy2',
          '#navy3',
          '#navy4',
          '#navy5',
          '#navy6',
          '#navy7',
          '#navy8',
          '#navy9',
        ],
        decaMono: [
          '#decaMono0',
          '#decaMono1',
          '#decaMono2',
          '#decaMono3',
          '#decaMono4',
          '#decaMono5',
          '#decaMono6',
          '#decaMono7',
          '#decaMono8',
          '#decaMono9',
        ],
      },
    };

    // Execute the style function to ensure coverage of lines 11-68
    const styles = styleFunction(mockTheme);

    return () => ({ classes: MOCK_CLASSES });
  },
}));

// Import mocked modules
import { useTranslate } from '@tolgee/react';
import { CustomCard, CustomLabel } from '@/components';
import { Select } from 'react-hook-form-mantine';
import { useGeneralSettingsContext } from '@/pages/GeneralSetting/GeneralSettingsContext';
import { useSettingsContext } from '@/contexts/SettingsContext';
import { BOT_TRIGGER_TYPES } from '@/constants';

describe('BotTriggerSection', () => {
  let mockForm: any, mockTranslate: any, mockSettingsContext: any;

  const renderComponent = (triggerValue = BOT_TRIGGER_TYPES.OPEN_PAGE) => {
    mockForm.watch.mockReturnValue(triggerValue);
    return render(
      <MantineProvider>
        <BotTriggerSection />
      </MantineProvider>
    );
  };

  const expectTranslationCalls = (...keys: string[]) => {
    keys.forEach((key) => expect(mockTranslate).toHaveBeenCalledWith(key));
  };

  const expectElementWithTestId = (testId: string, shouldExist = true) => {
    const element = screen.queryByTestId(testId);
    if (shouldExist) {
      expect(element).toBeInTheDocument();
    } else {
      expect(element).not.toBeInTheDocument();
    }
  };

  beforeEach(() => {
    vi.clearAllMocks();

    const mocks = setupMocks();
    mockForm = mocks.mockForm;
    mockTranslate = mocks.mockTranslate;
    mockSettingsContext = mocks.mockSettingsContext;

    vi.mocked(useGeneralSettingsContext).mockReturnValue({ form: mockForm } as any);
    vi.mocked(useTranslate).mockReturnValue({ t: mockTranslate } as any);
    vi.mocked(useSettingsContext).mockReturnValue(mockSettingsContext as any);
  });

  describe('Basic Rendering', () => {
    it('should render main components with correct structure and props', () => {
      renderComponent();

      // Structure
      expectElementWithTestId('custom-card');
      expectElementWithTestId('custom-label');
      expectElementWithTestId('select-component');
      expectElementWithTestId('stack');

      // Props and attributes
      expect(screen.getByTestId('custom-card')).toHaveAttribute('data-title', 'mocked.botTrigger');
      expect(screen.getByTestId('custom-label')).toHaveAttribute(
        'data-text',
        'mocked.appearanceTimingLabel'
      );
      expect(screen.getByTestId('select-component')).toHaveClass(MOCK_CLASSES.input);
      expect(screen.getByTestId('stack')).toHaveAttribute('data-gap', '8');

      // Select props
      const selectProps = JSON.parse(screen.getByTestId('select-props').textContent!);
      expect(selectProps).toEqual({ withCheckIcon: false, allowDeselect: false });

      // Translation calls
      expectTranslationCalls(
        'botTrigger',
        'appearanceTimingLabel',
        'triggerUserOpenPageOption',
        'triggerUserClickAButtonOption'
      );
    });

    it('should render Select with correct data options', () => {
      renderComponent();

      const expectedData = [
        { value: BOT_TRIGGER_TYPES.OPEN_PAGE, label: 'mocked.triggerUserOpenPageOption' },
        { value: BOT_TRIGGER_TYPES.CLICK_A_BUTTON, label: 'mocked.triggerUserClickAButtonOption' },
      ];

      expect(screen.getByTestId('select-data')).toHaveTextContent(JSON.stringify(expectedData));
    });
  });

  describe('Form Integration', () => {
    it('should integrate with form correctly', () => {
      renderComponent();

      expect(mockForm.watch).toHaveBeenCalledWith('botTrigger');
      expect(Select).toHaveBeenCalledWith(
        expect.objectContaining({ control: mockForm.control }),
        expect.any(Object)
      );
    });
  });

  describe('TriggerContents Conditional Rendering', () => {
    it('should show/hide trigger contents based on trigger type', () => {
      // Test OPEN_PAGE (no contents)
      renderComponent(BOT_TRIGGER_TYPES.OPEN_PAGE);
      expectElementWithTestId('copy-button', false);
      expectElementWithTestId('action-icon', false);

      // Test CLICK_A_BUTTON (show contents)
      renderComponent(BOT_TRIGGER_TYPES.CLICK_A_BUTTON);
      expectElementWithTestId('copy-button');
      expectElementWithTestId('action-icon');
      expectElementWithTestId('tooltip');

      // Additional translation calls for CLICK_A_BUTTON
      expectTranslationCalls(
        'userClickAButtonDescription',
        'exampleText',
        'openChatText',
        'ifOnClickEventNotSetText',
        'onClickEventLabel',
        'copy'
      );
    });

    it('should generate correct script and content for CLICK_A_BUTTON', () => {
      renderComponent(BOT_TRIGGER_TYPES.CLICK_A_BUTTON);

      const expectedScript = `onclick="window.__DECA_CLIENT__${MOCK_CHATBOX_ID}.chat.toggleChatWindow()"`;
      expect(screen.getByTestId('copy-button')).toHaveAttribute('data-value', expectedScript);

      // Check button sample contains chatbox ID
      const textElements = screen.getAllByTestId('text');
      const buttonSampleElement = textElements.find((el) =>
        el.textContent?.includes(`window.__DECA_CLIENT__${MOCK_CHATBOX_ID}.chat.toggleChatWindow()`)
      );
      expect(buttonSampleElement).toBeInTheDocument();
    });

    it('should not render contents for other trigger types', () => {
      [BOT_TRIGGER_TYPES.TIME_DISPLAY, BOT_TRIGGER_TYPES.SCROLL_TO_HTML_ELEMENT].forEach(
        (trigger) => {
          renderComponent(trigger);
          expectElementWithTestId('copy-button', false);
          expectElementWithTestId('action-icon', false);
        }
      );
    });
  });

  describe('Copy Functionality', () => {
    it('should render copy functionality with correct props and behavior', () => {
      renderComponent(BOT_TRIGGER_TYPES.CLICK_A_BUTTON);

      const expectedScript = `onclick="window.__DECA_CLIENT__${MOCK_CHATBOX_ID}.chat.toggleChatWindow()"`;
      const copyButton = screen.getByTestId('copy-button');
      const actionIcon = screen.getByTestId('action-icon');

      // Props
      expect(copyButton).toHaveAttribute('data-value', expectedScript);
      expect(copyButton).toHaveAttribute('data-timeout', '500');
      expect(actionIcon).toHaveClass(MOCK_CLASSES.actionIcon);

      // Default state (not copied)
      expectElementWithTestId('icon-copy');
      expectElementWithTestId('icon-check', false);

      // Click behavior
      expect(() => fireEvent.click(actionIcon)).not.toThrow();
    });

    it('should test copy functionality states', () => {
      renderComponent(BOT_TRIGGER_TYPES.CLICK_A_BUTTON);

      // Test that copy button renders children function correctly
      const copyButton = screen.getByTestId('copy-button');
      expect(copyButton).toBeInTheDocument();

      // Test that action icon is clickable
      const actionIcon = screen.getByTestId('action-icon');
      expect(actionIcon).toBeInTheDocument();

      // Test that tooltip is present
      const tooltip = screen.getByTestId('tooltip');
      expect(tooltip).toBeInTheDocument();

      // Test translations for both states
      expectTranslationCalls('copy');
    });

    it('should test copy button render function', () => {
      renderComponent(BOT_TRIGGER_TYPES.CLICK_A_BUTTON);

      const copyButton = screen.getByTestId('copy-button');
      expect(copyButton).toBeInTheDocument();

      expectTranslationCalls('copy');

      // Verify the copy functionality is set up correctly
      const actionIcon = screen.getByTestId('action-icon');
      expect(actionIcon).toBeInTheDocument();
    });
  });
});
