import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { MantineProvider } from '@mantine/core';
import Setting from './index';

const { mockConstants } = vi.hoisted(() => {
  const mockConstants = { HEADER_HEIGHT: '60px' };
  return { mockConstants };
});

vi.mock('@/constants', () => mockConstants);

vi.mock('@mantine/core', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    Stack: ({ p, gap, className, children, ...props }: any) => (
      <div data-testid='stack' data-padding={p} data-gap={gap} className={className} {...props}>
        {children}
      </div>
    ),
    rem: vi.fn((value: number) => `${value}rem`),
  };
});

vi.mock('@mantine/emotion', () => ({
  createStyles: vi.fn((styleFunction: (theme?: any) => any) => {
    // Execute the style function to cover CSS lines (12-20)
    const mockTheme = {};
    const styles = styleFunction(mockTheme);

    return () => ({
      classes: {
        container: 'mock-container-class',
      },
    });
  }),
}));

vi.mock('./components', () => ({
  AutoOpenSection: vi.fn(() => <div data-testid='auto-open-section'>AutoOpenSection</div>),
  BotTriggerSection: vi.fn(() => <div data-testid='bot-trigger-section'>BotTriggerSection</div>),
  Integration: vi.fn(() => <div data-testid='integration'>Integration</div>),
  SettingCWStateSection: vi.fn(() => (
    <div data-testid='setting-cw-state-section'>SettingCWStateSection</div>
  )),
}));

import {
  AutoOpenSection,
  BotTriggerSection,
  Integration,
  SettingCWStateSection,
} from './components';

describe('Setting', () => {
  const renderComponent = () => {
    return render(
      <MantineProvider>
        <Setting />
      </MantineProvider>
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Component Rendering and Structure', () => {
    it('renders main container with correct styling and structure', () => {
      renderComponent();

      const stack = screen.getByTestId('stack');
      expect(stack).toBeInTheDocument();
      expect(stack).toHaveClass('mock-container-class');
      expect(stack).toHaveAttribute('data-padding', '12rem');
      expect(stack).toHaveAttribute('data-gap', '12rem');
    });

    it('renders all child components in correct order', () => {
      renderComponent();

      expect(screen.getByTestId('integration')).toBeInTheDocument();
      expect(screen.getByTestId('auto-open-section')).toBeInTheDocument();
      expect(screen.getByTestId('setting-cw-state-section')).toBeInTheDocument();
      expect(screen.getByTestId('bot-trigger-section')).toBeInTheDocument();

      // Verify components are called
      expect(Integration).toHaveBeenCalled();
      expect(AutoOpenSection).toHaveBeenCalled();
      expect(SettingCWStateSection).toHaveBeenCalled();
      expect(BotTriggerSection).toHaveBeenCalled();
    });

    it('maintains correct component order in DOM', () => {
      renderComponent();

      const container = screen.getByTestId('stack');
      const children = Array.from(container.children);

      expect(children[0]).toHaveAttribute('data-testid', 'integration');
      expect(children[1]).toHaveAttribute('data-testid', 'auto-open-section');
      expect(children[2]).toHaveAttribute('data-testid', 'setting-cw-state-section');
      expect(children[3]).toHaveAttribute('data-testid', 'bot-trigger-section');
    });
  });
});
