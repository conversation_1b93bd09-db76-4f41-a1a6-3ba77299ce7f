import { render, screen, act } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import { SettingsContextProvider, useSettingsContext } from './SettingsContext';

// Mock all dependencies
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
  }),
}));

vi.mock('@/services/api', () => ({
  ChatboxAPI: {
    get: vi.fn(),
    save: vi.fn(),
    update: vi.fn(),
    remove: vi.fn(),
    uploadAsset: vi.fn(),
    getConfigurationLimits: vi.fn().mockResolvedValue({
      boxesPerOrganization: 10,
      widgetsPerBox: 5,
      boxesPerBot: 3,
    }),
  },
}));

vi.mock('swr', () => ({
  default: vi.fn(() => ({
    data: null,
    isLoading: false,
    mutate: vi.fn(),
  })),
}));

vi.mock('@mantine/hooks', () => ({
  useDisclosure: () => [false, { open: vi.fn(), close: vi.fn() }],
}));

vi.mock('@/utils', () => ({
  removeUnnecessaryObjectKey: (obj: any) => obj,
}));

vi.mock('@/utils/errorHandler', () => ({
  errorHandler: vi.fn(),
}));

// Mock global fetch
global.fetch = vi.fn().mockResolvedValue({
  json: () => Promise.resolve({}),
});

describe('SettingsContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset global fetch
    global.fetch = vi.fn().mockResolvedValue({
      json: () =>
        Promise.resolve({
          orgId: 'test-org',
          integrationId: 'test-integration',
          botId: 'test-bot',
        }),
    });
  });

  const TestComponent = () => {
    const context = useSettingsContext();
    return (
      <div>
        <div data-testid='current-chatbox-id'>{context.currentChatboxId}</div>
        <div data-testid='loading'>{String(context.loading)}</div>
        <div data-testid='limitations'>
          {context.LIMITATION_OF_WIDGETS}-{context.LIMITATION_OF_CHATBOXES_PER_ORGANIZATION}
        </div>
        <button data-testid='set-chatbox-id' onClick={() => context.setCurrentChatboxId('new-id')}>
          Set ID
        </button>
      </div>
    );
  };

  describe('SettingsContextProvider', () => {
    it('should render children without crashing', () => {
      render(
        <SettingsContextProvider>
          <div data-testid='test-child'>Test Child</div>
        </SettingsContextProvider>
      );

      expect(screen.getByTestId('test-child')).toBeInTheDocument();
    });

    it('should provide context value to children', () => {
      render(
        <SettingsContextProvider>
          <TestComponent />
        </SettingsContextProvider>
      );

      expect(screen.getByTestId('current-chatbox-id')).toBeInTheDocument();
      expect(screen.getByTestId('loading')).toBeInTheDocument();
      expect(screen.getByTestId('limitations')).toBeInTheDocument();
    });

    it('should initialize with correct default values', () => {
      render(
        <SettingsContextProvider>
          <TestComponent />
        </SettingsContextProvider>
      );

      expect(screen.getByTestId('current-chatbox-id')).toHaveTextContent('');
      expect(screen.getByTestId('loading')).toHaveTextContent('false');
      const limitationsText = screen.getByTestId('limitations').textContent;
      expect(limitationsText).toMatch(/^(0-0|5-10)$/);
    });
  });

  describe('useSettingsContext', () => {
    it('should throw error when used outside provider', () => {
      const TestComponentOutsideProvider = () => {
        useSettingsContext();
        return <div>Test</div>;
      };

      expect(() => render(<TestComponentOutsideProvider />)).toThrow(
        'useSettingsContext must be used inside SettingsContextProvider'
      );
    });

    it('should return context value when used inside provider', () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      expect(result.current).toBeDefined();
      expect(result.current.currentChatboxId).toBe('');
      expect(result.current.setCurrentChatboxId).toBeInstanceOf(Function);
      expect(result.current.onSaveStyling).toBeInstanceOf(Function);
    });
  });

  describe('Hook functionality', () => {
    it('should update currentChatboxId correctly', async () => {
      render(
        <SettingsContextProvider>
          <TestComponent />
        </SettingsContextProvider>
      );

      await act(async () => {
        screen.getByTestId('set-chatbox-id').click();
      });

      expect(screen.getByTestId('current-chatbox-id')).toHaveTextContent('new-id');
    });

    it('should provide context methods', () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      // Check that all expected methods are available
      expect(result.current.setCurrentChatboxId).toBeInstanceOf(Function);
      expect(result.current.onSaveStyling).toBeInstanceOf(Function);
      expect(result.current.onSaveGeneral).toBeInstanceOf(Function);
      expect(result.current.onSaveContent).toBeInstanceOf(Function);
      expect(result.current.onSaveIntegration).toBeInstanceOf(Function);
      expect(result.current.handleSavingBox).toBeInstanceOf(Function);
      expect(result.current.handleDelete).toBeInstanceOf(Function);
      expect(result.current.uploadImage).toBeInstanceOf(Function);
      expect(result.current.updateContentSettings).toBeInstanceOf(Function);
      expect(result.current.reloadChatbox).toBeInstanceOf(Function);
    });

    it('should handle content settings updates', async () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      const newContentSettings = {
        widgets: ['widget1', 'widget2'],
      };

      await act(async () => {
        result.current.updateContentSettings(newContentSettings);
      });

      expect(result.current.currentSettings.contentSettings).toMatchObject(newContentSettings);
    });

    it('should provide configuration limits', () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      // Configuration limits may not be loaded immediately, so check for default (0) or loaded values
      expect(result.current.LIMITATION_OF_WIDGETS).toBeGreaterThanOrEqual(0);
      expect(result.current.LIMITATION_OF_CHATBOXES_PER_ORGANIZATION).toBeGreaterThanOrEqual(0);
    });

    it('should handle state management correctly', async () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      const mockChatbox = {
        id: 'test-id',
        title: 'Test Chatbox',
        created: '2023-01-01',
      };

      await act(async () => {
        result.current.setClickChatbox(mockChatbox);
      });

      expect(result.current.clickedChatbox).toEqual(mockChatbox);

      await act(async () => {
        result.current.handleResetAddedChatbox();
      });

      expect(result.current.addedChatbox).toBeUndefined();
    });

    it('should provide modal state management', () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      expect(result.current.delOpened).toBe(false);
      expect(result.current.formOpened).toBe(false);
      expect(result.current.delOpen).toBeInstanceOf(Function);
      expect(result.current.delClose).toBeInstanceOf(Function);
      expect(result.current.formOpen).toBeInstanceOf(Function);
      expect(result.current.formClose).toBeInstanceOf(Function);
    });

    it('should provide current settings structure', () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      expect(result.current.currentSettings).toBeDefined();
      expect(result.current.currentSettings.stylingSettings).toBeDefined();
    });
  });

  describe('Context integration', () => {
    it('should handle context provider without errors', () => {
      const TestWrapper = ({ children }: { children: React.ReactNode }) => (
        <SettingsContextProvider>{children}</SettingsContextProvider>
      );

      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: TestWrapper,
      });

      expect(result.current).toBeDefined();
      expect(result.current.currentChatboxId).toBe('');
      expect(result.current.loading).toBe(false);
    });

    it('should provide client properties structure', () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      expect(result.current.currentChatBoxClient).toBeDefined();
      expect(result.current.currentChatBoxClient).toHaveProperty('configUrl');
      expect(result.current.currentChatBoxClient).toHaveProperty('domId');
      expect(result.current.currentChatBoxClient).toHaveProperty('orgId');
    });
  });

  describe('Function Execution and Loading States', () => {
    it('should call save functions without errors', async () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      act(() => {
        result.current.setCurrentChatboxId('test-id');
      });

      // Test that functions can be called without throwing
      expect(async () => {
        await result.current.onSaveStyling({
          styling: { home: { image: 'test.jpg' } },
          launcher: { image: 'test.jpg' },
        });
      }).not.toThrow();

      expect(async () => {
        await result.current.onSaveGeneral({ title: 'Test', desc: 'Test', image: 'test.jpg' });
      }).not.toThrow();

      expect(async () => {
        await result.current.onSaveContent({ widgets: ['test'] });
      }).not.toThrow();

      expect(async () => {
        await result.current.onSaveIntegration({ enabled: true });
      }).not.toThrow();
    });

    it('should handle loading states during operations', async () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      act(() => {
        result.current.setCurrentChatboxId('test-id');
      });

      expect(result.current.loading).toBe(false);

      // Test that loading state can be set
      await act(async () => {
        await result.current.onSaveGeneral({ title: 'Test' });
      });

      expect(result.current.loading).toBe(false);
    });
  });

  describe('State Management and UI Operations', () => {
    it('should provide all expected context properties', () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      // Check all context properties exist
      expect(result.current).toHaveProperty('currentChatboxId');
      expect(result.current).toHaveProperty('setCurrentChatboxId');
      expect(result.current).toHaveProperty('loading');
      expect(result.current).toHaveProperty('currentSettings');
      expect(result.current).toHaveProperty('setCurrentSettings');
      expect(result.current).toHaveProperty('updateContentSettings');
      expect(result.current).toHaveProperty('reloadChatbox');
      expect(result.current).toHaveProperty('chatboxList');
      expect(result.current).toHaveProperty('setChatboxList');
      expect(result.current).toHaveProperty('LIMITATION_OF_WIDGETS');
      expect(result.current).toHaveProperty('LIMITATION_OF_CHATBOXES_PER_ORGANIZATION');

      // State properties
      expect(result.current).toHaveProperty('addedChatbox');
      expect(result.current).toHaveProperty('updatedChatbox');
      expect(result.current).toHaveProperty('deletedChatbox');
      expect(result.current).toHaveProperty('clickedChatbox');

      // Modal properties
      expect(result.current).toHaveProperty('delOpened');
      expect(result.current).toHaveProperty('formOpened');
      expect(result.current).toHaveProperty('delOpen');
      expect(result.current).toHaveProperty('delClose');
      expect(result.current).toHaveProperty('formOpen');
      expect(result.current).toHaveProperty('formClose');

      // Save functions
      expect(result.current).toHaveProperty('onSaveStyling');
      expect(result.current).toHaveProperty('onSaveGeneral');
      expect(result.current).toHaveProperty('onSaveContent');
      expect(result.current).toHaveProperty('onSaveIntegration');
      expect(result.current).toHaveProperty('handleSavingBox');
      expect(result.current).toHaveProperty('handleDelete');
      expect(result.current).toHaveProperty('uploadImage');
      expect(result.current).toHaveProperty('handleResetAddedChatbox');

      // Client properties
      expect(result.current).toHaveProperty('currentChatbox');
      expect(result.current).toHaveProperty('currentChatBoxClient');
    });
  });

  describe('Modal and State Management', () => {
    it('should handle delete modal operations', () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      const mockChatbox = { id: 'test-id', title: 'Test', created: new Date().toISOString() };

      // Test setting clicked chatbox
      act(() => {
        result.current.setClickChatbox(mockChatbox);
      });

      expect(result.current.clickedChatbox).toEqual(mockChatbox);

      // Test modal state
      expect(result.current.delOpened).toBe(false);
      expect(result.current.formOpened).toBe(false);

      // Test modal functions exist
      expect(typeof result.current.delOpen).toBe('function');
      expect(typeof result.current.delClose).toBe('function');
      expect(typeof result.current.formOpen).toBe('function');
      expect(typeof result.current.formClose).toBe('function');
    });

    it('should handle form modal operations', () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      const mockChatbox = { id: 'test-id', title: 'Test', created: new Date().toISOString() };

      act(() => {
        result.current.setClickChatbox(mockChatbox);
      });

      expect(result.current.clickedChatbox).toEqual(mockChatbox);

      // Test that reset function exists and works
      act(() => {
        result.current.handleResetAddedChatbox();
      });

      expect(result.current.addedChatbox).toBeUndefined();
    });

    it('should handle delete modal close operations', () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      const mockChatbox = { id: 'test-id', title: 'Test', created: new Date().toISOString() };

      // Set clicked chatbox and open delete modal
      act(() => {
        result.current.setClickChatbox(mockChatbox);
        result.current.delOpen();
      });

      expect(result.current.clickedChatbox).toEqual(mockChatbox);

      // Close delete modal - this should clear clicked chatbox
      act(() => {
        result.current.delClose();
      });

      expect(result.current.clickedChatbox).toBeUndefined();
    });

    it('should handle form modal close operations', () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      const mockChatbox = { id: 'test-id', title: 'Test', created: new Date().toISOString() };

      // Set clicked chatbox and open form modal
      act(() => {
        result.current.setClickChatbox(mockChatbox);
        result.current.formOpen();
      });

      expect(result.current.clickedChatbox).toEqual(mockChatbox);

      // Close form modal - this should clear clicked chatbox
      act(() => {
        result.current.formClose();
      });

      expect(result.current.clickedChatbox).toBeUndefined();
    });
  });

  describe('Configuration and Fetch Operations', () => {
    it('should handle fetch operation for configUrl', async () => {
      const mockConfigData = {
        orgId: 'test-org-id',
        integrationId: 'test-integration-id',
        botId: 'test-bot-id',
      };

      // Mock fetch to return config data
      global.fetch = vi.fn().mockResolvedValue({
        json: () => Promise.resolve(mockConfigData),
      });

      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      // Test that we can call fetch directly (simulating what useEffect would do)
      const testUrl = 'https://example.com/config.json';
      const response = await fetch(testUrl);
      const data = await response.json();

      expect(global.fetch).toHaveBeenCalledWith(testUrl);
      expect(data.orgId).toBe('test-org-id');
      expect(data.integrationId).toBe('test-integration-id');
      expect(data.botId).toBe('test-bot-id');
    });

    it('should handle fetch error gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Mock fetch to reject
      global.fetch = vi.fn().mockRejectedValue(new Error('Fetch failed'));

      try {
        await fetch('https://example.com/config.json');
      } catch (error) {
        console.error(error);
      }

      expect(global.fetch).toHaveBeenCalledWith('https://example.com/config.json');
      expect(consoleSpy).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    it('should handle configuration limits properly', async () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      // Should have configuration limits properties
      expect(typeof result.current.LIMITATION_OF_WIDGETS).toBe('number');
      expect(typeof result.current.LIMITATION_OF_CHATBOXES_PER_ORGANIZATION).toBe('number');

      // Should be >= 0 (default values or loaded values)
      expect(result.current.LIMITATION_OF_WIDGETS).toBeGreaterThanOrEqual(0);
      expect(result.current.LIMITATION_OF_CHATBOXES_PER_ORGANIZATION).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Delete Operations', () => {
    it('should handle chatbox deletion successfully', async () => {
      const mockClickedChatbox = { id: 'delete-id', title: 'To Delete' };

      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      // Set clicked chatbox
      const mockClickedChatboxWithCreated = {
        ...mockClickedChatbox,
        created: new Date().toISOString(),
      };
      act(() => {
        return result.current.setClickChatbox(mockClickedChatboxWithCreated);
      });

      expect(result.current.clickedChatbox).toEqual(mockClickedChatboxWithCreated);

      // Execute delete
      await act(async () => {
        await result.current.handleDelete();
      });

      // Should set deleted chatbox
      expect(result.current.deletedChatbox).toEqual(mockClickedChatboxWithCreated);
    });
  });

  describe('Save Operations Coverage', () => {
    it('should execute save operations without throwing errors', async () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      act(() => {
        result.current.setCurrentChatboxId('test-id');
      });

      // Test handleSavingBox with no clicked chatbox (new chatbox path)
      await act(async () => {
        await result.current.handleSavingBox(
          { title: 'New Chatbox', desc: 'New Description' },
          null
        );
      });

      // Test handleSavingBox with clicked chatbox (update path)
      const mockClickedChatbox = {
        id: 'existing-id',
        title: 'Existing Chatbox',
        config: { image: 'old-image.jpg' },
        created: new Date().toISOString(),
      };
      act(() => {
        return result.current.setClickChatbox(mockClickedChatbox);
      });

      await act(async () => {
        await result.current.handleSavingBox(
          { title: 'Updated Chatbox', desc: 'Updated Description' },
          null
        );
      });

      // Test save operations with file
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

      await act(async () => {
        await result.current.handleSavingBox(
          { title: 'With File', desc: 'With File Description' },
          mockFile
        );
      });

      // Test uploadImage with file
      let uploadResult;
      await act(async () => {
        uploadResult = await result.current.uploadImage(mockFile, 'custom-id');
      });

      // uploadImage may return undefined based on the mock, which is fine
      expect(uploadResult).toBeUndefined();
    });

    it('should handle save styling operations', async () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      act(() => {
        result.current.setCurrentChatboxId('test-id');
      });

      // Test styling save with different image types
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

      // Test with File objects
      await act(async () => {
        await result.current.onSaveStyling({
          styling: {
            home: { image: mockFile },
            theme: 'light',
          },
          launcher: { image: mockFile },
        });
      });

      // Test with string images
      await act(async () => {
        await result.current.onSaveStyling({
          styling: {
            home: { image: 'existing-home-image.jpg' },
            theme: 'dark',
          },
          launcher: { image: 'existing-launcher-image.jpg' },
        });
      });
    });

    it('should handle save general operations', async () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      act(() => {
        result.current.setCurrentChatboxId('test-id');
      });

      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

      // Test with File object
      await act(async () => {
        await result.current.onSaveGeneral({
          title: 'New Title',
          desc: 'New Description',
          image: mockFile,
          autoOpen: true,
        });
      });

      // Test with string image
      await act(async () => {
        await result.current.onSaveGeneral({
          title: 'Another Title',
          desc: 'Another Description',
          image: 'existing-image.jpg',
          autoOpen: false,
        });
      });
    });

    it('should handle save content operations', async () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      act(() => {
        result.current.setCurrentChatboxId('test-id');
      });

      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

      // Test with File object
      await act(async () => {
        await result.current.onSaveContent({
          image: mockFile,
          widgets: ['widget1', 'widget2'],
          welcomeMessage: 'Welcome!',
        });
      });

      // Test with string image
      await act(async () => {
        await result.current.onSaveContent({
          image: 'existing-content-image.jpg',
          widgets: ['widget3', 'widget4'],
          welcomeMessage: 'Hello there!',
        });
      });
    });

    it('should handle save integration operations', async () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      act(() => {
        result.current.setCurrentChatboxId('test-id');
      });

      // Test integration settings
      await act(async () => {
        await result.current.onSaveIntegration({
          livechatConnected: true,
          chatbotSettings: { enabled: true, welcomeMessage: 'Hi!' },
          integrationSettings: { apiKey: 'test-key' },
        });
      });

      await act(async () => {
        await result.current.onSaveIntegration({
          livechatConnected: false,
          chatbotSettings: { enabled: false },
          integrationSettings: { apiKey: '' },
        });
      });
    });

    it('should handle reload chatbox operation', () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      // Test reload operation
      act(() => {
        result.current.reloadChatbox();
      });

      // Should not throw error
      expect(result.current).toBeDefined();
    });

    it('should handle updateContentSettings operation', () => {
      const { result } = renderHook(() => useSettingsContext(), {
        wrapper: SettingsContextProvider,
      });

      const newContentSettings = {
        widgets: ['new-widget1', 'new-widget2'],
        welcomeMessage: 'Updated welcome message',
        customField: 'custom value',
      };

      act(() => {
        result.current.updateContentSettings(newContentSettings);
      });

      expect(result.current.currentSettings.contentSettings).toMatchObject(newContentSettings);
    });
  });
});
