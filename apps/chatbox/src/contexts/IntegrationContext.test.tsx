import { render, screen, act } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import { IntegrationContextProvider, useIntegrationContext } from './IntegrationContext';
import { DEFAULT_INCIDENT_MESSAGE } from '@/constants';

// Mock dependencies
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
  }),
}));

vi.mock('@/services/api', () => ({
  IntegrationBotsAPI: {
    getList: vi.fn().mockResolvedValue([]),
    connect: vi.fn().mockResolvedValue({ success: true }),
    disconnectAll: vi.fn().mockResolvedValue({ success: true }),
  },
}));

vi.mock('swr', () => ({
  default: vi.fn(() => ({
    data: [],
    isLoading: false,
  })),
}));

vi.mock('@/utils/errorHandler', () => ({
  errorHandler: vi.fn(),
}));

vi.mock('react-hook-form', () => ({
  useForm: vi.fn(() => ({
    reset: vi.fn(),
    getValues: vi.fn(),
    setValue: vi.fn(),
    watch: vi.fn(),
    control: {},
  })),
}));

// Mock SettingsContext
const mockSettingsContext = {
  currentChatboxId: 'test-chatbox-id',
  reloadChatbox: vi.fn(),
  currentSettings: {
    integrationSettings: {
      livechatConnected: true,
      livechatSettings: {
        incidentMessage: DEFAULT_INCIDENT_MESSAGE,
      },
      chatbotSettings: {
        incidentMessage: DEFAULT_INCIDENT_MESSAGE,
        systemicBackButton: false,
      },
    },
  },
};

vi.mock('./SettingsContext', () => ({
  useSettingsContext: () => mockSettingsContext,
}));

describe('IntegrationContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const TestComponent = () => {
    const context = useIntegrationContext();
    return (
      <div>
        <div data-testid='loading'>{String(context.loading)}</div>
        <div data-testid='chatbots-count'>{context.listChatbots?.length || 0}</div>
        <button
          data-testid='connect-bot'
          onClick={() => context.connectBot({ botId: 'bot-1', chatBoxId: 'chatbox-1' })}
        >
          Connect Bot
        </button>
        <button data-testid='disconnect-bots' onClick={() => context.disconnectBots()}>
          Disconnect Bots
        </button>
      </div>
    );
  };

  describe('IntegrationContextProvider', () => {
    it('should render children without crashing', () => {
      render(
        <IntegrationContextProvider>
          <div data-testid='test-child'>Test Child</div>
        </IntegrationContextProvider>
      );

      expect(screen.getByTestId('test-child')).toBeInTheDocument();
    });

    it('should provide context value to children', () => {
      render(
        <IntegrationContextProvider>
          <TestComponent />
        </IntegrationContextProvider>
      );

      expect(screen.getByTestId('loading')).toBeInTheDocument();
      expect(screen.getByTestId('chatbots-count')).toBeInTheDocument();
      expect(screen.getByTestId('connect-bot')).toBeInTheDocument();
      expect(screen.getByTestId('disconnect-bots')).toBeInTheDocument();
    });

    it('should initialize with correct default values', () => {
      render(
        <IntegrationContextProvider>
          <TestComponent />
        </IntegrationContextProvider>
      );

      expect(screen.getByTestId('loading')).toHaveTextContent('false');
      expect(screen.getByTestId('chatbots-count')).toHaveTextContent('0');
    });

    it('should throw error when used outside provider', () => {
      const TestComponentOutsideProvider = () => {
        useIntegrationContext();
        return <div>Test</div>;
      };

      expect(() => render(<TestComponentOutsideProvider />)).toThrow(
        'useIntegrationContext must be used inside IntegrationContextProvider'
      );
    });

    it('should return context value when used inside provider', () => {
      const { result } = renderHook(() => useIntegrationContext(), {
        wrapper: IntegrationContextProvider,
      });

      expect(result.current).toBeDefined();
      expect(result.current.loading).toBe(false);
      expect(result.current.connectBot).toBeInstanceOf(Function);
      expect(result.current.disconnectBots).toBeInstanceOf(Function);
      expect(result.current.form).toBeDefined();
    });

    it('should handle SWR conditional key (null when no currentChatboxId)', () => {
      const originalChatboxId = mockSettingsContext.currentChatboxId;
      mockSettingsContext.currentChatboxId = '';

      const { result } = renderHook(() => useIntegrationContext(), {
        wrapper: IntegrationContextProvider,
      });

      expect(result.current).toBeDefined();
      expect(result.current.listChatbots).toEqual([]);

      // Restore original value
      mockSettingsContext.currentChatboxId = originalChatboxId;
    });

    it('should handle connectBot with error and finally block', async () => {
      const { result } = renderHook(() => useIntegrationContext(), {
        wrapper: IntegrationContextProvider,
      });

      const payload = { botId: 'bot-1', chatBoxId: 'chatbox-1' };

      await act(async () => {
        await result.current.connectBot(payload);
      });

      expect(result.current.loading).toBe(false);
    });

    it('should handle disconnectBots timeout logic', async () => {
      vi.useFakeTimers();

      const { result } = renderHook(() => useIntegrationContext(), {
        wrapper: IntegrationContextProvider,
      });

      const disconnectPromise = act(async () => {
        await result.current.disconnectBots();
      });

      await disconnectPromise;

      act(() => {
        vi.advanceTimersByTime(2000);
      });

      expect(mockSettingsContext.reloadChatbox).toHaveBeenCalled();

      vi.useRealTimers();
    });

    it('should handle form reset with different currentSettings values', () => {
      const { result } = renderHook(() => useIntegrationContext(), {
        wrapper: IntegrationContextProvider,
      });

      expect(result.current).toBeDefined();
      expect(result.current.form).toBeDefined();
    });

    it('should handle form reset with undefined integrationSettings', () => {
      mockSettingsContext.currentSettings = { integrationSettings: undefined } as any;

      const { result } = renderHook(() => useIntegrationContext(), {
        wrapper: IntegrationContextProvider,
      });

      expect(result.current).toBeDefined();
      expect(result.current.form).toBeDefined();
    });

    it('should handle SWR with valid currentChatboxId', () => {
      mockSettingsContext.currentChatboxId = 'test-chatbox-id';

      const { result } = renderHook(() => useIntegrationContext(), {
        wrapper: IntegrationContextProvider,
      });

      expect(result.current).toBeDefined();
      expect(result.current.listChatbots).toEqual([]);
    });

    it('should combine loading states correctly', () => {
      const { result } = renderHook(() => useIntegrationContext(), {
        wrapper: IntegrationContextProvider,
      });

      expect(result.current.loading).toBeDefined();
      expect(typeof result.current.loading).toBe('boolean');
    });
  });
});
