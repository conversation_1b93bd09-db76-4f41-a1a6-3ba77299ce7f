import path from 'node:path';
import react from '@vitejs/plugin-react-swc';
import dotenv from 'dotenv';
/// <reference types="vitest" />
import { defineConfig, loadEnv } from 'vite';
import tsconfigPaths from 'vite-tsconfig-paths';

const getCdnPrefix = () => {
  if (process.env.AWS_PULL_REQUEST_ID) {
    console.log('[getCdnPrefix] load .env.preview file');
    const previewEnvironmentVariables = dotenv.configDotenv({
      path: '.env.preview',
    });
    return previewEnvironmentVariables.parsed!.VITE_CDN_PREFIX;
  }
  // console.log('[getCdnPrefix] use default env');
  return process.env.VITE_CDN_PREFIX ?? undefined;
};

const cdnPrefix = getCdnPrefix();

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  const CDN_URL = cdnPrefix ?? '/';
  const BASE_PATH = env.VITE_BASE_PATH ?? '/';
  return {
    plugins: [tsconfigPaths(), react()],
    base: BASE_PATH,
    experimental: {
      renderBuiltUrl(
        filename: string,
        {
          hostId,
          type,
        }: { hostId: string; hostType: 'js' | 'css' | 'html'; type: 'public' | 'asset' }
      ) {
        if (type === 'public') {
          return CDN_URL + filename;
        }
        if (path.extname(hostId) === '.js') {
          return CDN_URL + filename;
        }
        return CDN_URL + filename;
      },
    },
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: './setupTest.js',
      coverage: {
        enabled: true,
        provider: 'v8',
        reporter: ['text', 'json', 'html', 'lcov'],
        reportsDirectory: './coverage',
        exclude: [
          'node_modules/**',
          'dist/**',
          'dist-client/**',
          'vite.config.ts',
          'client.vite.config.ts',
          '**/*.config.ts',
          '**/*.config.js',
          '**/*.d.ts',
          '**/*.test.ts',
          '**/*.test.tsx',
          '**/*.spec.ts',
          '**/*.spec.tsx',
          'vite.config.ts',
          '.eslintrc.cjs',
          'test/**',
          'tests/**',
          '**/__tests__/**',
          'src/types/**',
          'src/models/**',
          'src/utils/**',
          'src/services/api/**',
          'src/main.tsx',
          'src/App.tsx',
          'src/client/**',
          'src/pages/ChatBoxClientDemo/**',
          'src/components/ChatBoxPreview/**',
          'src/components/ChatBoxUI/App/Widgets/**',
          'src/components/ChatBoxUI/utils/**',
          'src/components/ChatBoxUI/services/**',
          'src/components/ChatBoxUI/models/**',
          'src/components/ChatBoxUI/setting/**',
        ],
      },
    },
  };
});
