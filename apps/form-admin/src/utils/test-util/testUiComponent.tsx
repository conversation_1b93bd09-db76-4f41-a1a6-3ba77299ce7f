import { render as testingLibraryRender } from '@testing-library/react';
import { createTheme, MantineProvider } from '@mantine/core';
import { emotionTransform, MantineEmotionProvider } from '@mantine/emotion';
import { themeConfigurations } from '@/constants/themeConfiguration';
import { I18nextProvider } from 'react-i18next';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import builder from '@/store/reducers/builder';
import headerNavigation from '@/store/reducers/headerNavigation';
import { historyMiddleware } from '@/store/middleware/historyMiddleware';
import { ModalsProvider } from '@mantine/modals';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import {SWRConfig} from "swr";
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// Helper function to clean text content for testing
export const cleanTextForTesting = (text: string): string => {
  // Remove invisible Unicode characters that Tolge<PERSON> might add
  return text.replace(/[\u200B-\u200D\uFEFF\u2060]/g, '');
};

// Create a simple i18n instance for testing without Tolgee
const testI18nInstance = i18n.createInstance();
testI18nInstance
  .use(initReactI18next)
  .init({
    lng: 'en',
    fallbackLng: 'en',
    ns: ['share', 'common'],
    defaultNS: 'share',
    resources: {
      en: {
        share: {
          sharingForm: 'sharingForm',
          embedWebPageLabel: 'embedWebPageLabel',
          launchPlaceholder: 'launchPlaceholder',
          launchByClick: 'launchByClick',
          launchByLoadPage: 'launchByLoadPage',
          launchByTime: 'launchByTime',
          launchByScrollEnd: 'launchByScrollEnd',
          launchByScrolling: 'launchByScrolling',
          fixed: 'fixed',
          auto: 'auto',
          launch: 'launch',
          closeOnSubmit: 'closeOnSubmit',
          size: 'size',
          height: 'height',
          scrollPercent: 'scrollPercent',
          showButton: 'showButton',
          second: 'second',
          formLink: 'formLink',
          copy: 'copy',
          customize: 'customize',
          embedModalTitle: 'embedModalTitle',
          customElement: 'customElement',
          snippetCodeCustom: 'snippetCodeCustom',
          snippetCodeCustomSecond: 'snippetCodeCustomSecond',
          snippetCode: 'snippetCode',
          copiedCodesnippet: 'copiedCodesnippet',
          viewCode: 'viewCode',
          unpublishChangesAlert: 'unpublishChangesAlert',
        },
        common: {},
      },
    },
    interpolation: {
      escapeValue: false,
    },
  });

const theme = createTheme({
  ...themeConfigurations,
  components: {
    Select: {
      defaultProps: {
        allowDeselect: false,
        withCheckIcon: false,
      },
    },
  },
});

export const render = (ui: React.ReactNode, optionalRootStore?: any) => {
  const store = configureStore({
    reducer: {
      builder,
      headerNavigation,
    },
    preloadedState: optionalRootStore,
    middleware: getDefaultMiddleware => getDefaultMiddleware().concat(historyMiddleware),
  } as any);
  return testingLibraryRender(<>{ui}</>, {
    wrapper: ({ children }: { children: React.ReactNode }) => (
      <MantineProvider stylesTransform={emotionTransform} theme={theme}>
        <MantineEmotionProvider>
          <ModalsProvider>
            <SWRConfig>
              <BrowserRouter window={window}>
                <I18nextProvider i18n={testI18nInstance}>
                  <DndProvider backend={HTML5Backend}>
                    <Provider store={store}>{children}</Provider>
                  </DndProvider>
                </I18nextProvider>
              </BrowserRouter>
            </SWRConfig>
          </ModalsProvider>
        </MantineEmotionProvider>
      </MantineProvider>
    ),
  });
};
