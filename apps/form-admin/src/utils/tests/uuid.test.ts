import { describe, it, expect } from 'vitest';
import { ulid } from '../uuid';

describe('UUID utilities', () => {
  describe('ulid', () => {
    it('should generate a unique ULID', () => {
      const id1 = ulid();
      const id2 = ulid();
      
      expect(id1).toBeDefined();
      expect(id2).toBeDefined();
      expect(id1).not.toBe(id2);
    });

    it('should generate a string', () => {
      const id = ulid();
      
      expect(typeof id).toBe('string');
    });

    it('should generate lowercase ULIDs', () => {
      const id = ulid();
      
      expect(id).toBe(id.toLowerCase());
      expect(id).not.toMatch(/[A-Z]/);
    });

    it('should generate ULIDs with correct length', () => {
      const id = ulid();
      
      // ULIDs are 26 characters long
      expect(id.length).toBe(26);
    });

    it('should generate ULIDs with valid characters', () => {
      const id = ulid();
      
      // ULIDs use Crockford's Base32 alphabet (0-9, A-V, but we convert to lowercase)
      // Note: The actual ULID library might use different characters, so we'll check for alphanumeric
      const validChars = /^[0-9a-z]+$/;
      expect(id).toMatch(validChars);
    });

    it('should generate multiple unique ULIDs', () => {
      const ids = Array.from({ length: 100 }, () => ulid());
      const uniqueIds = new Set(ids);
      
      expect(uniqueIds.size).toBe(100);
    });

    it('should generate ULIDs with consistent format', () => {
      const ids = Array.from({ length: 10 }, () => ulid());
      
      // All ULIDs should have the same length
      const lengths = ids.map(id => id.length);
      expect(lengths.every(length => length === 26)).toBe(true);
      
      // All should be lowercase
      expect(ids.every(id => id === id.toLowerCase())).toBe(true);
      
      // All should match the valid character pattern
      const validChars = /^[0-9a-z]+$/;
      expect(ids.every(id => validChars.test(id))).toBe(true);
    });

    it('should generate ULIDs that are lexicographically sortable', () => {
      const ids = Array.from({ length: 10 }, () => ulid());
      const sortedIds = [...ids].sort();
      
      // ULIDs should be sortable by their string representation
      // Note: Due to timing, the original array might not be in sorted order
      // but the sorted array should be properly ordered
      expect(sortedIds).toEqual([...sortedIds].sort());
    });

    it('should handle rapid successive calls', () => {
      const ids = [];
      const startTime = Date.now();
      
      // Generate many ULIDs rapidly
      for (let i = 0; i < 1000; i++) {
        ids.push(ulid() as never);
      }
      
      const endTime = Date.now();
      const uniqueIds = new Set(ids);
      
      // Should generate unique IDs even when called rapidly
      expect(uniqueIds.size).toBe(1000);
      
      // Should complete in reasonable time (less than 1 second)
      expect(endTime - startTime).toBeLessThan(1000);
    });
  });
});
