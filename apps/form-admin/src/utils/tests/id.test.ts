import { describe, it, expect } from 'vitest';
import { randomId, randomShortId, randomString } from '../id';

describe('ID utilities', () => {
  describe('randomId', () => {
    it('should generate a unique ID', () => {
      const id1 = randomId();
      const id2 = randomId();
      
      expect(id1).toBeDefined();
      expect(id2).toBeDefined();
      expect(id1).not.toBe(id2);
    });

    it('should generate a string', () => {
      const id = randomId();
      
      expect(typeof id).toBe('string');
    });

    it('should generate IDs with consistent length', () => {
      const id1 = randomId();
      const id2 = randomId();
      
      expect(id1.length).toBe(id2.length);
    });

    it('should generate multiple unique IDs', () => {
      const ids = Array.from({ length: 100 }, () => randomId());
      const uniqueIds = new Set(ids);
      
      expect(uniqueIds.size).toBe(100);
    });
  });

  describe('randomShortId', () => {
    it('should generate a short ID with correct length', () => {
      const id = randomShortId();
      
      expect(id).toBeDefined();
      expect(id.length).toBe(8);
    });

    it('should generate unique short IDs', () => {
      const id1 = randomShortId();
      const id2 = randomShortId();
      
      expect(id1).not.toBe(id2);
    });

    it('should generate IDs using only allowed characters', () => {
      const id = randomShortId();
      const allowedChars = /^[1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ]+$/;
      
      expect(id).toMatch(allowedChars);
    });

    it('should generate multiple unique short IDs', () => {
      const ids = Array.from({ length: 100 }, () => randomShortId());
      const uniqueIds = new Set(ids);
      
      expect(uniqueIds.size).toBe(100);
    });
  });

  describe('randomString', () => {
    it('should generate a string with correct length', () => {
      const str = randomString();
      
      expect(str).toBeDefined();
      expect(str.length).toBe(8);
    });

    it('should generate unique strings', () => {
      const str1 = randomString();
      const str2 = randomString();
      
      expect(str1).not.toBe(str2);
    });

    it('should generate strings using only alphabetic characters', () => {
      const str = randomString();
      const alphabeticChars = /^[abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ]+$/;
      
      expect(str).toMatch(alphabeticChars);
    });

    it('should not include numbers in generated strings', () => {
      const str = randomString();
      const hasNumbers = /\d/.test(str);
      
      expect(hasNumbers).toBe(false);
    });

    it('should generate multiple unique strings', () => {
      const strings = Array.from({ length: 100 }, () => randomString());
      const uniqueStrings = new Set(strings);
      
      expect(uniqueStrings.size).toBe(100);
    });

    it('should include both uppercase and lowercase letters', () => {
      const strings = Array.from({ length: 50 }, () => randomString());
      const hasUppercase = strings.some(str => /[A-Z]/.test(str));
      const hasLowercase = strings.some(str => /[a-z]/.test(str));
      
      expect(hasUppercase).toBe(true);
      expect(hasLowercase).toBe(true);
    });
  });

  describe('ID generation consistency', () => {
    it('should generate different types of IDs with different characteristics', () => {
      const ulid = randomId();
      const shortId = randomShortId();
      const string = randomString();
      
      expect(ulid).toBeDefined();
      expect(shortId).toBeDefined();
      expect(string).toBeDefined();
      
      // All should be different
      expect(ulid).not.toBe(shortId);
      expect(ulid).not.toBe(string);
      expect(shortId).not.toBe(string);
    });

    it('should maintain consistent behavior across multiple calls', () => {
      const ulids = Array.from({ length: 10 }, () => randomId());
      const shortIds = Array.from({ length: 10 }, () => randomShortId());
      const strings = Array.from({ length: 10 }, () => randomString());
      
      // All ULIDs should have the same length
      const ulidLengths = ulids.map(id => id.length);
      expect(ulidLengths.every(length => length === ulidLengths[0])).toBe(true);
      
      // All short IDs should have length 8
      expect(shortIds.every(id => id.length === 8)).toBe(true);
      
      // All strings should have length 8
      expect(strings.every(str => str.length === 8)).toBe(true);
    });
  });
});
