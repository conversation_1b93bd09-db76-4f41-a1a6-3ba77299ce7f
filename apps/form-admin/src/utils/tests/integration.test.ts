// getValidationSchemaByType.test.ts
import { describe, it, expect } from 'vitest';
import { IntegrationTypes } from '@/constants';
import { IntegrationUtils } from '@/utils';
import {TableFieldTypes} from "@/types";

const getValidationSchemaByType = IntegrationUtils.getValidationSchemaByType

describe('getValidationSchemaByType - Email integration schema', () => {
  it('accepts valid email array, trims and lowercases entries', () => {
    const schema = getValidationSchemaByType(IntegrationTypes.Email);
    const result: any = schema.safeParse({
      settings: {
        to: ['  <EMAIL>  ', '<EMAIL>'],
      },
    });

    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data.settings.to).toEqual(['<EMAIL>', '<EMAIL>']);
    }
  });

  it('rejects empty recipients array (requiredError)', () => {
    const schema = getValidationSchemaByType(IntegrationTypes.Email);
    const result = schema.safeParse({ settings: { to: [] } });

    expect(result.success).toBe(false);
    if (!result.success) {
      const messages = result.error.issues.map((i) => i.message);
      expect(messages).toContain('Array must contain at least 1 element(s)');
    }
  });

  it('rejects invalid email format (invalidEmailError)', () => {
    const schema = getValidationSchemaByType(IntegrationTypes.Email);
    const result = schema.safeParse({
      settings: { to: ['not-an-email'] },
    });

    expect(result.success).toBe(false);
    if (!result.success) {
      const messages = result.error.issues.map((i) => i.message);
      expect(messages).toContain('Invalid email');
    }
  });

  it('rejects duplicated emails (existedEmailError)', () => {
    const schema = getValidationSchemaByType(IntegrationTypes.Email);
    const result = schema.safeParse({
      settings: { to: ['<EMAIL>', '<EMAIL>'] }, // duplicates after lowercasing
    });

    expect(result.success).toBe(false);
    if (!result.success) {
      const messages = result.error.issues.map((i) => i.message);
      expect(messages).toContain('Invalid input');
    }
  });
});

describe('getValidationSchemaByType - Webhook integration schema', () => {
  it('accepts valid url and header entries', () => {
    const schema = getValidationSchemaByType(IntegrationTypes.Webhook);
    const result = schema.safeParse({
      settings: {
        url: 'https://example.com/webhook',
        headersTemp: [
          { key: 'X_Auth-Token', value: 'abc-123_:=/(){}[]' },
          // empty key/value are allowed
          { key: '', value: '' },
        ],
      },
    });

    expect(result.success).toBe(true);
  });

  it('rejects invalid url (invalidWebhookUrlError)', () => {
    const schema = getValidationSchemaByType(IntegrationTypes.Webhook);
    const result = schema.safeParse({
      settings: {
        url: 'not-a-url',
        headersTemp: [],
      },
    });

    expect(result.success).toBe(false);
    if (!result.success) {
      const messages = result.error.issues.map((i) => i.message);
      expect(messages).toContain('Invalid url');
    }
  });

  it('rejects invalid header key (invalidHeaderKeyError)', () => {
    const schema = getValidationSchemaByType(IntegrationTypes.Webhook);
    const result = schema.safeParse({
      settings: {
        url: 'https://example.com/hook',
        headersTemp: [{ key: 'Invalid Key With Space', value: 'ok' }],
      },
    });

    expect(result.success).toBe(false);
    if (!result.success) {
      const messages = result.error.issues.map((i) => i.message);
      expect(messages).toContain('Invalid input');
    }
  });

  it('rejects invalid header value (invalidHeaderValueError)', () => {
    const schema = getValidationSchemaByType(IntegrationTypes.Webhook);
    const result = schema.safeParse({
      settings: {
        url: 'https://example.com/hook',
        // value with space should fail the allowed-characters regex
        headersTemp: [{ key: 'X-Key', value: 'has space' }],
      },
    });

    expect(result.success).toBe(false);
    if (!result.success) {
      const messages = result.error.issues.map((i) => i.message);
      expect(messages).toContain('Invalid input');
    }
  });
});

describe('getValidationSchemaByType - Tables integration schema', () => {
  it('accepts valid base_id and table_id', () => {
    const schema = getValidationSchemaByType(IntegrationTypes.Tables);
    const result = schema.safeParse({
      settings: { base_id: 'base_123', table_id: 'tbl_456' },
    });
    expect(result.success).toBe(true);
  });

  it('rejects empty base_id and table_id (requiredError)', () => {
    const schema = getValidationSchemaByType(IntegrationTypes.Tables);
    const result = schema.safeParse({
      settings: { base_id: '', table_id: '' },
    });

    expect(result.success).toBe(false);
    if (!result.success) {
      const messages = result.error.issues.map((i) => i.message);
      expect(messages).toEqual(expect.arrayContaining(['String must contain at least 1 character(s)']));
    }
  });
});

describe('getValidationSchemaByType - Line integration schema (existing mode)', () => {
  it('enforces account_id required, liff_id required in specific mode, and liff_app_name max length', () => {
    const schema = getValidationSchemaByType(IntegrationTypes.Line);
    const result = schema.safeParse({
      mode: 'existing',
      id: 'line-acc-001',
      settings: {
        account_id: '', // should trigger requiredError
        liff_app_name: 'x'.repeat(257), // should trigger invalidMaxLengthError (max 256)
        liff_id: '', // required when step==='liff' and liff_mode==='manually'
        liff_mode: 'manually',
        step: 'liff',
      },
    });

    expect(result.success).toBe(false);
    if (!result.success) {
      const issues = result.error.issues;
      const messages = issues.map((i) => i.message);

      expect(messages).toContain('String must contain at least 1 character(s)'); // for account_id or liff_id
      expect(messages).toContain('String must contain at most 256 character(s)');

      // Ensure the liff_id error is reported on the correct path
      const liffIdIssue = issues.find(
          (i) => Array.isArray(i.path) && i.path.join('.') === 'settings.liff_id'
      );
      expect(liffIdIssue?.message).toBe('Invalid input');
    }
  });

  it('passes when liff_id is provided in manual liff step', () => {
    const schema = getValidationSchemaByType(IntegrationTypes.Line);
    const result = schema.safeParse({
      mode: 'existing',
      id: 'line-acc-002',
      settings: {
        account_id: 'acc_123',
        liff_app_name: 'My LIFF',
        liff_id: 'LIFF-123',
        liff_mode: 'manually',
        step: 'liff',
      },
    });

    expect(result.success).toBe(true);
  });
});

describe('IntegrationUtils.convertIntegrationHeader', () => {
  it('converts header array to object format', () => {
    const headers: any = [
      {key: 'Content-Type', value: 'application/json'},
      {key: 'X-Auth', value: 'token123'}
    ];

    const result = IntegrationUtils.convertIntegrationHeader(headers as any);

    expect(result[0].value).toEqual(headers[0]);
  });

  it('handles empty header array', () => {
    const result = IntegrationUtils.convertIntegrationHeader([] as any);
    expect(result[0].value).toEqual('');
  });
});

describe('IntegrationUtils.convertTableMappingDataForApi', () => {
  it('converts mapping data to API format', () => {
    const mappingData: any = [
      {field: 'name', column: 'Name', fieldTbl: TableFieldTypes.CreateNewField},
      {field: 'email', column: 'Email Address', fieldTbl: TableFieldTypes.ExcludeThisField},
      {field: 'name', column: 'Name', fieldTbl: TableFieldTypes.CreateNewField, fieldType: TableFieldTypes.ExcludeThisField },
      {field: 'name', column: 'Name', fieldTbl: JSON.stringify({id: '123', name: 'name'}), type: TableFieldTypes.ExcludeThisField },
    ];

    const result = IntegrationUtils.convertTableMappingDataForApi(mappingData);

    expect(result).toBeTruthy;
  });

  it('handles empty mapping data', () => {
    const result = IntegrationUtils.convertTableMappingDataForApi([]);
    expect(result).toEqual([]);
  });
});

describe('IntegrationUtils.tranformTableMappingData', () => {

  it('handles empty API data', () => {
    const result = IntegrationUtils.tranformTableMappingData([], []);

    expect(result).toEqual([]);
  });

  it('transforms mapping data correctly', () => {
    const questions = [
      {id: 'q1', type: 'text'},
      {id: 'q2', type: 'checkbox'}
    ];

    const mappings = [
      {
        question_id: 'q1',
        field_type: 'text',
        field_name: 'Name',
        is_excluded: false,
        field_id: 'f1'
      }
    ];

    const result = IntegrationUtils.tranformTableMappingData(questions as any, mappings);

    expect(result).toEqual([
      {
        questionId: 'q1',
        fieldTbl: JSON.stringify({
          id: 'f1',
          name: 'Name',
          type: 'text'
        }),
        fieldType: TableFieldTypes.SingleLineText
      },
      {
        questionId: 'q2',
        fieldTbl: TableFieldTypes.CreateNewField,
        fieldType: TableFieldTypes.Checkbox
      }
    ]);
  });

});

