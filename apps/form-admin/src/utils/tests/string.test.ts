import { describe, it, expect } from 'vitest';
import { StringUtils } from '../string';

describe('StringUtils', () => {
  describe('safeJSONParse', () => {
    it('should parse valid JSON string', () => {
      const validJson = '{"name": "<PERSON>", "age": 30}';
      const result = StringUtils.safeJSONParse(validJson);
      
      expect(result).toEqual({ name: '<PERSON>', age: 30 });
    });

    it('should parse valid JSON array', () => {
      const validJsonArray = '[1, 2, 3, "test"]';
      const result = StringUtils.safeJSONParse(validJsonArray);
      
      expect(result).toEqual([1, 2, 3, 'test']);
    });

    it('should parse valid JSON with nested objects', () => {
      const validJson = '{"user": {"name": "John", "details": {"age": 30, "city": "NYC"}}}';
      const result = StringUtils.safeJSONParse(validJson);
      
      expect(result).toEqual({
        user: {
          name: '<PERSON>',
          details: {
            age: 30,
            city: 'NYC'
          }
        }
      });
    });

    it('should return null for invalid JSON string', () => {
      const invalidJson = '{"name": "John", "age": 30'; // Missing closing brace
      const result = StringUtils.safeJSONParse(invalidJson);
      
      expect(result).toBeNull();
    });

    it('should return null for malformed JSON', () => {
      const malformedJson = '{name: "John", age: 30}'; // Missing quotes around keys
      const result = StringUtils.safeJSONParse(malformedJson);
      
      expect(result).toBeNull();
    });

    it('should return null for empty string', () => {
      const result = StringUtils.safeJSONParse('');
      
      expect(result).toBeNull();
    });

    it('should return null for non-JSON string', () => {
      const nonJsonString = 'This is not JSON';
      const result = StringUtils.safeJSONParse(nonJsonString);
      
      expect(result).toBeNull();
    });

    it('should parse JSON with special characters', () => {
      const jsonWithSpecialChars = '{"message": "Hello, \\"world\\"! \\n New line"}';
      const result = StringUtils.safeJSONParse(jsonWithSpecialChars);
      
      expect(result).toEqual({ message: 'Hello, "world"! \n New line' });
    });

    it('should parse JSON with boolean and null values', () => {
      const jsonWithPrimitives = '{"active": true, "count": null, "enabled": false}';
      const result = StringUtils.safeJSONParse(jsonWithPrimitives);
      
      expect(result).toEqual({ active: true, count: null, enabled: false });
    });

    it('should parse JSON with numbers (integers and floats)', () => {
      const jsonWithNumbers = '{"integer": 42, "float": 3.14, "negative": -10}';
      const result = StringUtils.safeJSONParse(jsonWithNumbers);
      
      expect(result).toEqual({ integer: 42, float: 3.14, negative: -10 });
    });
  });
});
