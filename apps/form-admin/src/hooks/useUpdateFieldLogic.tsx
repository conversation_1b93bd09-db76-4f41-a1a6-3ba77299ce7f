import { UPDATE_FIELD } from '@/store/action/actionTypes';
import { useAppDispatch } from '@/store/hooks';
import { selectingFieldSelector } from '@/store/reducers/builder';
import type { FormLogic } from '@/types/form-logic';
import { convertFormLogicToMongoFormat, filterIncompleteRules } from '@/utils/form-logic';
import { useCallback } from 'react';
import { useSelector } from 'react-redux';

export const useUpdateFieldLogic = () => {
  const dispatch = useAppDispatch();
  const selectingField = useSelector(selectingFieldSelector);

  const updateFieldLogic = useCallback(
    (logic: FormLogic) => {
      if (!selectingField) {
        return;
      }
      const getUpdatedLogic = (logic: FormLogic) => {
        const filteredConditions = filterIncompleteRules(logic.conditions);
        const mongoConditions = convertFormLogicToMongoFormat(filteredConditions);

        return {
          ...logic,
          conditions: mongoConditions,
        };
      };

      const updatedLogic = logic.conditions?.rules?.length > 0 ? [getUpdatedLogic(logic)] : [];

      dispatch({
        type: UPDATE_FIELD,
        payload: {
          ...selectingField,
          logics: updatedLogic,
        },
      });
    },
    [dispatch, selectingField]
  );

  return updateFieldLogic;
};
