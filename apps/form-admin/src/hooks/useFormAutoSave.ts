import { FormsAPI } from '@/services/api';
import { SET_UPDATE_FORM_SAVING } from '@/store/action/actionTypes';
import { formPagesDataSelector } from '@/store/reducers/builder';
import { isEqual } from 'lodash';
import { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';

export const SKIP_CONVERT_KEYS = ['conditions'];

const useSaveData = ({
  formId,
  delay,
  onSaveFailed,
}: {
  formId: string;
  delay?: number;
  onSaveFailed?: () => void;
}) => {
  const TIME_OUT = delay || 3000;
  const data = useSelector(formPagesDataSelector); // Adjust according to your state structure
  const isContentLoaded = useRef(false);
  const dispatch = useDispatch();
  const prevDataRef = useRef(data);
  const saveTimeoutRef = useRef<null | NodeJS.Timeout>(null);

  useEffect(() => {
    isContentLoaded.current = false;
  }, [formId]);

  useEffect(() => {
    if (data) {
      if (!isContentLoaded.current) {
        prevDataRef.current = data;
        isContentLoaded.current = true;
        return;
      }
    } else {
      return;
    }
    const hasChanges = !isEqual(data, prevDataRef.current);

    if (hasChanges) {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }

      dispatch({ type: SET_UPDATE_FORM_SAVING, payload: true });

      saveTimeoutRef.current = setTimeout(async () => {
        const dataWithDirtyFlag = data.map((item) => {
          const prevItem = prevDataRef.current.find((prev) => prev.id === item.id);
          if (!prevItem || !isEqual(item, prevItem)) {
            return { ...item, is_modified: true };
          }
          return item;
        });

        // Call your API with the modified data
        try {
          await FormsAPI.updateFormsData(formId, dataWithDirtyFlag, { skipConvertKeys: SKIP_CONVERT_KEYS });
          prevDataRef.current = data;
          dispatch({ type: SET_UPDATE_FORM_SAVING, payload: false });
        } catch (error) {
          console.error('Failed to save data', error);
          onSaveFailed?.();
          dispatch({ type: SET_UPDATE_FORM_SAVING, payload: null });
        }
      }, TIME_OUT);
    } else {
      dispatch({ type: SET_UPDATE_FORM_SAVING, payload: null });
    }

    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, [data]);

  // Return anything you might need from this hook
};

export default useSaveData;
