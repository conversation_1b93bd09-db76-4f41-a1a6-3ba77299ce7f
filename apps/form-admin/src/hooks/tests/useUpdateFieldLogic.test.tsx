import { renderHook, act } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { useUpdateFieldLogic } from '../useUpdateFieldLogic';
import { convertFormLogicToMongoFormat, filterIncompleteRules } from '@/utils/form-logic';
import type { FormLogic } from '@/types/form-logic';

// Mock the utility functions
vi.mock('@/utils/form-logic', () => ({
  convertFormLogicToMongoFormat: vi.fn((conditions) => conditions),
  filterIncompleteRules: vi.fn((rules) => rules),
}));

// Mock the selector
vi.mock('@/store/reducers/builder', () => ({
  selectingFieldSelector: (state: any) => state.builder.selectingField,
}));

const mockStore = configureStore({
  reducer: {
    builder: () => ({
      selectingField: {
        id: 'test-field',
        type: 'text',
        label: 'Test Field',
      },
    }),
  },
});

const renderHookWithProvider = (hook: any) => {
  return renderHook(hook, {
    wrapper: ({ children }) => (
      <Provider store={mockStore}>
        {children}
      </Provider>
    ),
  });
};

describe('useUpdateFieldLogic', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('returns a function', () => {
    const { result } = renderHookWithProvider(() => useUpdateFieldLogic());
    
    expect(typeof result.current).toBe('function');
  });

  it('does not dispatch when no selecting field', () => {
    const storeWithoutField = configureStore({
      reducer: {
        builder: () => ({
          selectingField: null,
        }),
      },
    });

    const { result } = renderHook(() => useUpdateFieldLogic(), {
      wrapper: ({ children }) => (
        <Provider store={storeWithoutField}>
          {children}
        </Provider>
      ),
    });

    const mockLogic: FormLogic = {
      conditions: {
        combinator: 'and',
        rules: [
          {
            field: 'test',
            operator: 'is',
            value: 'test',
          },
        ],
      },
      action: 'show',
    };

    act(() => {
      (result.current as any)(mockLogic);
    });

    // Should not call the utility functions when no selecting field
    expect(filterIncompleteRules).not.toHaveBeenCalled();
    expect(convertFormLogicToMongoFormat).not.toHaveBeenCalled();
  });

  it('dispatches UPDATE_FIELD action with updated logic', () => {
    const { result } = renderHookWithProvider(() => useUpdateFieldLogic());

    const mockLogic: FormLogic = {
      conditions: {
        combinator: 'and',
        rules: [
          {
            field: 'test',
            operator: 'is',
            value: 'test',
          },
        ],
      },
      action: 'show',
    };

    const mockFilteredRules = [
      {
        field: 'test',
        operator: 'is',
        value: 'test',
      },
    ];

    const mockMongoConditions = {
      combinator: 'and',
      rules: mockFilteredRules,
    };

    (filterIncompleteRules as any).mockReturnValue(mockFilteredRules);
    (convertFormLogicToMongoFormat as any).mockReturnValue(mockMongoConditions);

    act(() => {
      (result.current as any)(mockLogic);
    });

    expect(filterIncompleteRules).toHaveBeenCalledWith(mockLogic.conditions);
    expect(convertFormLogicToMongoFormat).toHaveBeenCalledWith(mockFilteredRules);
  });

  it('handles logic with empty rules array', () => {
    const { result } = renderHookWithProvider(() => useUpdateFieldLogic());

    const mockLogic: FormLogic = {
      conditions: {
        combinator: 'and',
        rules: [],
      },
      action: 'show',
    };

    act(() => {
      (result.current as any)(mockLogic);
    });

    // When rules array is empty, the utility functions should not be called
    // because the logic checks if rules.length > 0 before processing
    expect(filterIncompleteRules).not.toHaveBeenCalled();
    expect(convertFormLogicToMongoFormat).not.toHaveBeenCalled();
  });

  it('handles logic with non-empty rules array', () => {
    const { result } = renderHookWithProvider(() => useUpdateFieldLogic());

    const mockLogic: FormLogic = {
      conditions: {
        combinator: 'and',
        rules: [
          {
            field: 'test',
            operator: 'is',
            value: 'test',
          },
        ],
      },
      action: 'show',
    };

    const mockFilteredRules = [
      {
        field: 'test',
        operator: 'is',
        value: 'test',
      },
    ];

    const mockMongoConditions = {
      combinator: 'and',
      rules: mockFilteredRules,
    };

    (filterIncompleteRules as any).mockReturnValue(mockFilteredRules);
    (convertFormLogicToMongoFormat as any).mockReturnValue(mockMongoConditions);

    act(() => {
      (result.current as any)(mockLogic);
    });

    expect(filterIncompleteRules).toHaveBeenCalledWith(mockLogic.conditions);
    expect(convertFormLogicToMongoFormat).toHaveBeenCalledWith(mockFilteredRules);
  });

  it('updates field with correct payload structure', () => {
    const { result } = renderHookWithProvider(() => useUpdateFieldLogic());

    const mockLogic: FormLogic = {
      conditions: {
        combinator: 'and',
        rules: [
          {
            field: 'test',
            operator: 'is',
            value: 'test',
          },
        ],
      },
      action: 'show',
    };

    const mockFilteredRules = [
      {
        field: 'test',
        operator: 'is',
        value: 'test',
      },
    ];

    const mockMongoConditions = {
      combinator: 'and',
      rules: mockFilteredRules,
    };

    (filterIncompleteRules as any).mockReturnValue(mockFilteredRules);
    (convertFormLogicToMongoFormat as any).mockReturnValue(mockMongoConditions);

    act(() => {
      (result.current as any)(mockLogic);
    });

    // The dispatch should be called with the correct action type and payload
    // The actual dispatch call is handled by the Redux store
    expect(filterIncompleteRules).toHaveBeenCalled();
    expect(convertFormLogicToMongoFormat).toHaveBeenCalled();
  });

  it('handles different action types', () => {
    const { result } = renderHookWithProvider(() => useUpdateFieldLogic());

    const mockLogic: FormLogic = {
      conditions: {
        combinator: 'and',
        rules: [
          {
            field: 'test',
            operator: 'is',
            value: 'test',
          },
        ],
      },
      action: 'hide',
    };

    act(() => {
      (result.current as any)(mockLogic);
    });

    expect(filterIncompleteRules).toHaveBeenCalled();
    expect(convertFormLogicToMongoFormat).toHaveBeenCalled();
  });

  it('handles complex nested conditions', () => {
    const { result } = renderHookWithProvider(() => useUpdateFieldLogic());

    const mockLogic: FormLogic = {
      conditions: {
        combinator: 'or',
        rules: [
          {
            field: 'test1',
            operator: 'is',
            value: 'value1',
          },
          {
            field: 'test2',
            operator: 'is',
            value: 'value2',
          },
        ],
      },
      action: 'show',
    };

    const mockFilteredRules = [
      {
        combinator: 'or',
        rules: [
          {
            field: 'test1',
            operator: 'is',
            value: 'value1',
          },
          {
            field: 'test2',
            operator: 'is',
            value: 'value2',
          },
        ],
      },
    ];

    (filterIncompleteRules as any).mockReturnValue(mockFilteredRules);
    (convertFormLogicToMongoFormat as any).mockReturnValue(mockFilteredRules);

    act(() => {
      (result.current as any)(mockLogic);
    });

    expect(filterIncompleteRules).toHaveBeenCalledWith(mockLogic.conditions);
    expect(convertFormLogicToMongoFormat).toHaveBeenCalledWith(mockFilteredRules);
  });
});
