import { renderHook } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { useLogicFields } from '../useLogicFields';
import { JAPAN_PREFECTURE_MAP } from '@/constants/form-logic';
import { FieldType, FormLayoutType } from '@/types/form-builder';
import type { FormSection } from '@/types/form-builder';

// Mock the selector
vi.mock('@/store/reducers/builder', () => ({
  formPagesDataSelector: (state: any) => state.builder.formPagesData,
}));

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      if (key === 'otherLabel') return 'Other';
      if (key === 'form_builder') return 'Form Builder';
      return key;
    },
  }),
  initReactI18next: {},
}));

const mockFormPages: FormSection[] = [
  {
    id: 'page-1',
    type: FieldType.Section,
    name: 'Page 1',
    layout: {
      type: FormLayoutType.ImageTop,
      imageUrl: null,
      fieldWidth: '100%',
    },
    content: [
      {
        id: 'field-1',
        type: FieldType.ShortQA,
        label: '<p>Text Field</p>',
        name: 'field-1',
        validators: [],
        isHide: false,
      },
      {
        id: 'field-2',
        type: FieldType.Paragraph,
        label: '<p>Paragraph Field</p>',
        name: 'field-2',
        validators: [],
        isHide: false,
      },
      {
        id: 'field-3',
        type: FieldType.Checkbox,
        label: '<p>Checkbox Field</p>',
        name: 'field-3',
        validators: [],
        options: [
          { id: 'opt-1', label: '<p>Option 1</p>' },
          { id: 'opt-2', label: '<p>Option 2</p>' },
        ],
        isHide: false,
      },
      {
        id: 'field-4',
        type: FieldType.Rating,
        label: '<p>Rating Field</p>',
        name: 'field-4',
        validators: [],
        maxScale: 5,
        isHide: false,
      },
      {
        id: 'field-5',
        type: FieldType.ShortQA,
        label: '<p>Hidden Field</p>',
        name: 'field-5',
        validators: [],
        isHide: true,
      },
      {
        id: 'field-6',
        type: FieldType.ShortQA,
        label: '',
        name: 'field-6',
        validators: [],
        isHide: false,
      },
    ],
  },
  {
    id: 'page-2',
    type: FieldType.Section,
    name: 'Page 2',
    layout: {
      type: FormLayoutType.ImageTop,
      imageUrl: null,
      fieldWidth: '100%',
    },
    content: [
      {
        id: 'field-7',
        type: FieldType.ShortQA,
        label: '<p>Another Text Field</p>',
        name: 'field-7',
        validators: [],
        isHide: false,
      },
      {
        id: 'field-8',
        type: FieldType.ShortQA,
        label: '<p>Field with Group</p>',
        name: 'field-8',
        validators: [],
        isHide: false,
      },
      {
        id: 'field-9',
        type: FieldType.ShortQA,
        name: 'prefecture',
        label: '<p>Prefecture Field</p>',
        validators: [],
        isHide: false,
      },
    ],
  },
];

const mockStore = configureStore({
  reducer: {
    builder: () => ({
      formPagesData: mockFormPages,
    }),
  },
});

const renderHookWithProvider = (hook: any) => {
  return renderHook(hook, {
    wrapper: ({ children }) => (
      <Provider store={mockStore}>
        {children}
      </Provider>
    ),
  });
};

describe('useLogicFields', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('returns available fields from form pages', () => {
    const { result } = renderHookWithProvider(() => useLogicFields());

    const { availableFields } = result.current as any;

    expect(availableFields).toHaveLength(6); // Valid fields: field-1, field-3, field-4, field-7, field-8, field-9
    expect(availableFields[0]).toEqual({
      value: 'field-1',
      name: 'field-1',
      label: 'Text Field',
      fieldType: FieldType.ShortQA,
    });
  });

  it('filters out ignored field types', () => {
    const { result } = renderHookWithProvider(() => useLogicFields());

    const { availableFields } = result.current as any;

    // Should not include paragraph fields
    const paragraphField = availableFields.find(field => field.fieldType === FieldType.Paragraph);
    expect(paragraphField).toBeUndefined();
  });

  it('filters out hidden fields', () => {
    const { result } = renderHookWithProvider(() => useLogicFields());

    const { availableFields } = result.current as any;

    // Should not include hidden fields
    const hiddenField = availableFields.find(field => field.value === 'field-5');
    expect(hiddenField).toBeUndefined();
  });

  it('filters out fields with empty labels', () => {
    const { result } = renderHookWithProvider(() => useLogicFields());

    const { availableFields } = result.current as any;

    // Should not include fields with empty labels
    const emptyLabelField = availableFields.find(field => field.value === 'field-6');
    expect(emptyLabelField).toBeUndefined();
  });

  it('includes all valid fields from both pages', () => {
    const { result } = renderHookWithProvider(() => useLogicFields());

    const { availableFields } = result.current as any;

    // Should include fields from both pages
    const field1 = availableFields.find(field => field.value === 'field-1');
    const field7 = availableFields.find(field => field.value === 'field-7');
    const field9 = availableFields.find(field => field.value === 'field-9');
    
    expect(field1).toBeDefined();
    expect(field7).toBeDefined();
    expect(field9).toBeDefined();
    expect(field1?.label).toBe('Text Field');
    expect(field7?.label).toBe('Another Text Field');
    expect(field9?.label).toBe('Prefecture Field');
  });

  it('strips HTML tags from field labels', () => {
    const { result } = renderHookWithProvider(() => useLogicFields());

    const { availableFields } = result.current as any;

    const textField = availableFields.find(field => field.value === 'field-1');
    expect(textField?.label).toBe('Text Field'); // Should be stripped of <p> tags
  });

  it('handles checkbox fields correctly', () => {
    const { result } = renderHookWithProvider(() => useLogicFields());

    const { availableFields } = result.current as any;

    const checkboxField = availableFields.find(field => field.value === 'field-3');
    expect(checkboxField?.label).toBe('Option 1'); // Should use first option label for checkbox
  });

  it('returns empty array when no form content', () => {
    const emptyStore = configureStore({
      reducer: {
        builder: () => ({
          formPagesData: null,
        }),
      },
    });

    const { result } = renderHook(() => useLogicFields(), {
      wrapper: ({ children }) => (
        <Provider store={emptyStore}>
          {children}
        </Provider>
      ),
    });

    const { availableFields } = result.current as any;
    expect(availableFields).toEqual([]);
  });

  it('provides getFieldOptions for different field types', () => {
    const { result } = renderHookWithProvider(() => useLogicFields());

    const { getFieldOptions } = result.current as any;

    // Test checkbox field options
    const checkboxOptions = getFieldOptions('field-3');
    expect(checkboxOptions).toEqual([
      { value: 'opt-1', label: 'Option 1' },
      { value: 'opt-2', label: 'Option 2' },
    ]);

    // Test rating field options
    const ratingOptions = getFieldOptions('field-4');
    expect(ratingOptions).toEqual([
      { value: 1, label: 1 },
      { value: 2, label: 2 },
      { value: 3, label: 3 },
      { value: 4, label: 4 },
      { value: 5, label: 5 },
    ]);

    // Test prefecture field options
    const prefectureOptions = getFieldOptions('field-9');
    expect(prefectureOptions).toEqual(
      Object.entries(JAPAN_PREFECTURE_MAP).map(([_, value]) => ({
        value: value,
        label: value,
      }))
    );
  });

  it('returns empty array for non-existent field', () => {
    const { result } = renderHookWithProvider(() => useLogicFields());

    const { getFieldOptions } = result.current as any;

    const options = getFieldOptions('non-existent-field');
    expect(options).toEqual([]);
  });

  it('handles fields with other option', () => {
    const storeWithOtherField = configureStore({
      reducer: {
        builder: () => ({
          formPagesData: [
            {
              id: 'page-1',
              type: FieldType.Section,
              name: 'Page 1',
              layout: {
                type: FormLayoutType.ImageTop,
                imageUrl: null,
                fieldWidth: '100%',
              },
              content: [
                {
                  id: 'field-1',
                  type: FieldType.Checkbox,
                  label: '<p>Checkbox Field</p>',
                  options: [
                    { id: 'opt-1', label: '<p>Option 1</p>' },
                  ],
                  isOther: true,
                  isHide: false,
                },
              ],
            },
          ],
        }),
      },
    });

    const { result } = renderHook(() => useLogicFields(), {
      wrapper: ({ children }) => (
        <Provider store={storeWithOtherField}>
          {children}
        </Provider>
      ),
    });

    const { getFieldOptions } = result.current as any;
    const options = getFieldOptions('field-1');
    
    expect(options).toEqual([
      { value: 'opt-1', label: 'Option 1' },
      { value: 'other', label: 'Other' },
    ]);
  });

  it('provides getFieldData function', () => {
    const { result } = renderHookWithProvider(() => useLogicFields());

    const { getFieldData } = result.current as any;

    const fieldData = getFieldData('field-1');
    expect(fieldData).toEqual({
      id: 'field-1',
      type: FieldType.ShortQA,
      label: '<p>Text Field</p>',
      name: 'field-1',
      validators: [],
      isHide: false,
    });

    const nonExistentField = getFieldData('non-existent');
    expect(nonExistentField).toBeNull();
  });

  it('handles fields without options', () => {
    const { result } = renderHookWithProvider(() => useLogicFields());

    const { getFieldOptions } = result.current as any;

    const textFieldOptions = getFieldOptions('field-1');
    expect(textFieldOptions).toEqual([]);
  });

  it('handles opinion scale fields', () => {
    const storeWithOpinionScale = configureStore({
      reducer: {
        builder: () => ({
          formPagesData: [
            {
              id: 'page-1',
              type: FieldType.Section,
              name: 'Page 1',
              layout: {
                type: FormLayoutType.ImageTop,
                imageUrl: null,
                fieldWidth: '100%',
              },
              content: [
                {
                  id: 'field-1',
                  type: FieldType.OpinionScale,
                  label: '<p>Opinion Scale Field</p>',
                  maxScale: 3,
                  isHide: false,
                },
              ],
            },
          ],
        }),
      },
    });

    const { result } = renderHook(() => useLogicFields(), {
      wrapper: ({ children }) => (
        <Provider store={storeWithOpinionScale}>
          {children}
        </Provider>
      ),
    });

    const { getFieldOptions } = result.current as any;
    const options = getFieldOptions('field-1');
    
    expect(options).toEqual([
      { value: 1, label: 1 },
      { value: 2, label: 2 },
      { value: 3, label: 3 },
    ]);
  });
});
