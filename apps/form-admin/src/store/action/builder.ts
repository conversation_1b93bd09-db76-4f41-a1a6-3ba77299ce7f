import {
  ADD_FIELD_TO_GROUP,
  ADD_HIDDEN_FIELD,
  ADD_NEW_FIELD,
  ADD_NEW_PAGE,
  APPLY_BUILDER_BY_HISTORY,
  APPLY_CUSTOMIZE_FIELDS,
  APPLY_FILTER_FIELDS,
  <PERSON>AN<PERSON>_SECTION_ORDER,
  <PERSON><PERSON><PERSON>_APPLIED_CUSTOMIZED_FIELDS,
  D<PERSON><PERSON><PERSON><PERSON>E_FIELD,
  DUPLICATE_SECTION,
  INIT_BUILDER,
  REMOVE_FIELD,
  REMOVE_SECTION,
  RESET_BUILDER_STORE,
  SELECT_FIELD,
  SELECT_NESTED_FIELD,
  SET_ACTIVE_SECTION,
  SET_APPEARANCE,
  SET_APPEARANCE_SETTING_OPTION,
  SET_BUILDER_RIGHT_NAV,
  SET_CURRENT_FORM_ID,
  SET_CURRENT_VIEW_ID,
  SET_FORM_INFO,
  SET_INTEGRATION_ITEM,
  SET_INTEGRATION_SECTION,
  SET_IS_DRAGGING,
  SET_LAYOUT_SETTINGS_ALL_PAGE,
  SET_RESULT_HIDDEN_FIELD_IDS,
  SET_RESULT_PINNED_COLUMN_ID,
  SET_RESULT_SECTION,
  SET_SCREENSHOT,
  SET_SHARE_SECTION,
  SET_UPDATE_FORM_SAVING,
  UPDATE_EMBED_SETTINGS,
  UPDATE_FIELD,
  UPDATE_HIDDEN_FIELD,
  UPDATE_HIDDEN_SETTINGS,
  UPDATE_SECTION,
  UPDATE_SECTION_LAYOUT,
} from '@/store/action/actionTypes';
import type {
  BuilderRightMenuType,
  CustomizeFieldInfo,
  EmbedSettings,
  FormAppearance,
  FormBuilderData,
  FormSection,
  FormSectionLayout,
  HiddenField,
  Integration,
  Screenshot,
  ShareHiddenField,
} from '@/types';
import type { AppearanceSettingsOptionType } from '@/types/enum';
import { createAction } from '@reduxjs/toolkit';
import type { BuilderStoreType } from '../reducers/builder';

export const initBuilder = createAction<FormBuilderData, string>(INIT_BUILDER);
export const setFormInfo = createAction<Partial<FormBuilderData>, string>(SET_FORM_INFO);
export const resetBuilderStore = createAction<string>(RESET_BUILDER_STORE);
export const addNewPage = createAction<FormSection, string>(ADD_NEW_PAGE);
export const setAppearance = createAction<FormAppearance, string>(SET_APPEARANCE);
export const setActiveSection = createAction<string, string>(SET_ACTIVE_SECTION);

export const setShareSection = createAction<string, string>(SET_SHARE_SECTION);
export const setResultSection = createAction<string, string>(SET_RESULT_SECTION);
export const updateEmbedSettings = createAction<EmbedSettings, string>(UPDATE_EMBED_SETTINGS);
export const applyCustomizeFields = createAction<CustomizeFieldInfo[], string>(
  APPLY_CUSTOMIZE_FIELDS
);
export const applyFilterFields = createAction<{ [key: string]: string }, string>(
  APPLY_FILTER_FIELDS
);
export const updateHiddenSettings = createAction<ShareHiddenField[], string>(
  UPDATE_HIDDEN_SETTINGS
);
export const setResultHiddenFieldIds = createAction<string, string>(SET_RESULT_HIDDEN_FIELD_IDS);
export const setResultPinnedColumnId = createAction<string | null, string>(
  SET_RESULT_PINNED_COLUMN_ID
);
export const setCurrentViewId = createAction<string, string>(SET_CURRENT_VIEW_ID);
export const clearAppliedCustomizedFields = createAction<void, string>(
  CLEAR_APPLIED_CUSTOMIZED_FIELDS
);
export const removeSection = createAction<string, string>(REMOVE_SECTION);
export const changeSectionOrder = createAction<{ dragIndex: number; hoverIndex: number }, string>(
  CHANGE_SECTION_ORDER
);
export const duplicateSection = createAction<string, string>(DUPLICATE_SECTION);
export const updateSection = createAction<FormSection, string>(UPDATE_SECTION);
export const updateSectionLayout = createAction<{ id: string; layout: FormSectionLayout }, string>(
  UPDATE_SECTION_LAYOUT
);
export const addNewField = createAction<FormSection, string>(ADD_NEW_FIELD);
export const selectField = createAction<string | null, string>(SELECT_FIELD);
export const selectNestedField = createAction<{ groupFieldId: string; fieldId: string } | null, string>(SELECT_NESTED_FIELD);
export const updateField = createAction<FormSection, string>(UPDATE_FIELD);
export const removeField = createAction<string, string>(REMOVE_FIELD);
export const duplicateField = createAction<string, string>(DUPLICATE_FIELD);
export const addFieldToGroup = createAction<{ groupId: string; field: any; index?: number; removeFromOriginal?: boolean }, string>(ADD_FIELD_TO_GROUP);
export const setAppearanceSettingsOption = createAction<AppearanceSettingsOptionType, string>(
  SET_APPEARANCE_SETTING_OPTION
);
export const setBuilderRightNav = createAction<BuilderRightMenuType, string>(SET_BUILDER_RIGHT_NAV);

export const setCurrentFormId = createAction<string, string>(SET_CURRENT_FORM_ID);

export const setUpdateFormSaving = createAction<boolean, string>(SET_UPDATE_FORM_SAVING);

export const setIntegrationSection = createAction<string, string>(SET_INTEGRATION_SECTION);
export const setIntegrationItem = createAction<Integration, string>(SET_INTEGRATION_ITEM);
export const setScreenshot = createAction<Screenshot | null, string>(SET_SCREENSHOT);
export const setLayoutSettingsAllPage = createAction<Partial<FormSectionLayout>, string>(
  SET_LAYOUT_SETTINGS_ALL_PAGE
);

export const addHiddenField = createAction<HiddenField, string>(ADD_HIDDEN_FIELD);

export const updateHiddenField = createAction<HiddenField, string>(UPDATE_HIDDEN_FIELD);

export const applyStateByHistory = createAction<BuilderStoreType, string>(APPLY_BUILDER_BY_HISTORY);
export const setIsDragging = createAction<boolean, string>(SET_IS_DRAGGING);
