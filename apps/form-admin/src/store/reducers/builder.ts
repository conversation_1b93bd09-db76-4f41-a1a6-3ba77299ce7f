import {
  addHidden<PERSON>ield,
  addN<PERSON><PERSON>ield,
  addNewP<PERSON>,
  applyCustomizeFields,
  applyFilterFields,
  applyStateByHistory,
  changeSectionOrder,
  clearAppliedCustomizedFields,
  duplicateField,
  duplicateSection,
  initBuilder,
  removeField,
  removeSection,
  resetBuilderStore,
  selectField,
  setActiveSection,
  setAppearance,
  setAppearanceSettingsOption,
  setBuilderRightNav,
  setCurrentFormId,
  setCurrentViewId,
  setFormInfo,
  setIntegrationItem,
  setIntegrationSection,
  setIsDragging,
  setLayoutSettingsAllPage,
  setResultHiddenFieldIds,
  setResultPinnedColumnId,
  setResultSection,
  setScreenshot,
  setShareSection,
  setUpdateFormSaving,
  updateEmbedSettings,
  updateField,
  updateHiddenField,
  updateHiddenSettings,
  updateSection,
  updateSectionLayout,
} from '@/store/action/builder';
import type { RootState } from '@/store/store';
import {
  BuilderRightMenuType,
  type CustomizeFieldInfo,
  type EmbedSettings,
  FieldType,
  type FormBuilderData,
  type FormField,
  type FormSection,
  type Integration,
  ResultTab,
  type Screenshot,
  ShareAction,
  type ShareHiddenField,
} from '@/types';
import { IntegrationSectionTypes } from '@/types';
import { AppearanceSettingsOptionType } from '@/types/enum/appearanceSettings';
import { createNewIdForField, getNewBuilderJson } from '@/utils/builder-helper';
import { ulid } from '@/utils/uuid';
import { createReducer, createSelector } from '@reduxjs/toolkit';

export type BuilderStoreType = {
  builderJson: FormBuilderData;
  selectingSectionId: string;
  selectingShareSection: ShareAction;
  selectingResultSection: ResultTab;
  selectingEmbedSettings: EmbedSettings;
  applyCustomizeFields: CustomizeFieldInfo[];
  applyFilterFields: { [key: string]: string };
  updateHiddenSettings: ShareHiddenField[];
  resultHiddenFieldIds: string[];
  selectingItem: FormField;
  screenshot: Screenshot | null;
  selectingFieldId: string | null;
  selectedAppearanceSettings: AppearanceSettingsOptionType;
  rightMenuType: BuilderRightMenuType;
  currentFormId: string;
  formSaving: boolean | null;
  selectedIntegrationSection: string;
  resultPinnedColumnId: string | null;
  selectedIntegrationItem: Integration;
  currentViewId: string;
  isDragging: boolean;
  currentHistoryKey: string;
};

const initialState = {
  builderJson: getNewBuilderJson(),
  selectingSectionId: '',
  selectingShareSection: ShareAction.Share,
  selectingResultSection: ResultTab.Results,
  rightMenuType: BuilderRightMenuType.Question,
  selectedAppearanceSettings: AppearanceSettingsOptionType.Default,
  formSaving: null,
  selectedIntegrationSection: IntegrationSectionTypes.Installed,
  selectedIntegrationItem: {} as Integration,
  resultHiddenFieldIds: [] as string[],
  resultPinnedColumnId: null,
  screenshot: null,
  currentViewId: 'default',
  isDragging: false,
  currentHistoryKey: '',
} as BuilderStoreType;

function findSectionIndex(builderJson: any, id: string): number {
  return builderJson?.content.findIndex((section) => section.id === id);
}

const counter = createReducer(
  JSON.parse(JSON.stringify(initialState)) as BuilderStoreType,
  (builder) => {
    builder.addCase(initBuilder, (state, action) => {
      state.builderJson = action.payload;
    });
    builder.addCase(resetBuilderStore, () => {
      return JSON.parse(JSON.stringify(initialState));
    });
    builder.addCase(setFormInfo, (state, action) => {
      state.builderJson = {
        ...state.builderJson,
        ...action.payload,
      };
    });
    builder.addCase(addNewPage, (state, action) => {
      state.builderJson?.content?.push(action.payload);
      state.selectingSectionId = action.payload.id;
    });
    builder.addCase(setActiveSection, (state, action) => {
      state.selectingSectionId = action.payload;
    });

    builder.addCase(setShareSection, (state, action) => {
      state.selectingShareSection = action.payload as ShareAction;
    });
    builder.addCase(setResultSection, (state, action) => {
      state.selectingResultSection = action.payload as ResultTab;
    });
    builder.addCase(updateEmbedSettings, (state, action) => {
      state.selectingEmbedSettings = action.payload;
    });
    builder.addCase(applyCustomizeFields, (state, action) => {
      state.applyCustomizeFields = action.payload;
      state.resultHiddenFieldIds = state.applyCustomizeFields
        .filter((field) => field.isHidden)
        .map((field) => field.questionId);
    });
    builder.addCase(applyFilterFields, (state, action) => {
      state.applyFilterFields = action.payload;
    });
    builder.addCase(updateHiddenSettings, (state, action) => {
      state.updateHiddenSettings = action.payload;
    });
    builder.addCase(setResultHiddenFieldIds, (state, action) => {
      state.resultHiddenFieldIds.push(action.payload);
    });
    builder.addCase(setResultPinnedColumnId, (state, action) => {
      state.resultPinnedColumnId = action.payload;
    });
    builder.addCase(setScreenshot, (state, action) => {
      state.screenshot = action.payload;
    });
    // FIELD
    builder.addCase(addNewField, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));

      const selectingSectionIndex = findSectionIndex(
        newState.builderJson,
        newState.selectingSectionId
      );

      const section = newState.builderJson?.content[selectingSectionIndex] as FormSection;
      if (section) {
        // Check action.payload is add a group of fields or a single field
        if (Array.isArray(action.payload)) {
          // Should active the item settings whenever add a new field (not handle the group field yet)
          // newState.selectingItem = action.payload[0];
          action.payload.forEach((field) => {
            if (field.fields) {
              field.fields = field.fields.map((item) => ({
                ...item,
                id: ulid(),
              }));
            }
            section.content.push({ ...field, id: ulid() });
          });
        } else {
          // Should remove this line cause the payload always an array
          action.payload = { ...action.payload, id: ulid() };
          section.content.push(action.payload);
        }
      }

      return newState;
    });
    builder.addCase(selectField, (state, action) => {
      state.selectingFieldId = action.payload;
    });
    builder.addCase(updateField, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));
      const selectingSectionIndex = findSectionIndex(
        newState.builderJson,
        newState.selectingSectionId
      );

      if (selectingSectionIndex !== -1) {
        const section = newState.builderJson?.content[selectingSectionIndex] as FormSection;
        const selectingFieldIndex = section.content.findIndex(
          (field) => field.id === action.payload.id
        );
        if (selectingFieldIndex !== -1) {
          section.content[selectingFieldIndex] = action.payload;
        }
      }

      return newState;
    });
    builder.addCase(removeField, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));
      const selectingSectionIndex = findSectionIndex(
        newState.builderJson,
        newState.selectingSectionId
      );

      if (selectingSectionIndex !== -1) {
        const section = newState.builderJson?.content[selectingSectionIndex] as FormSection;
        const selectingFieldIndex = section.content.findIndex(
          (field) => field.id === action.payload
        );

        if (selectingFieldIndex !== -1) {
          section.content.splice(selectingFieldIndex, 1);
        }
      }

      return newState;
    });
    builder.addCase(duplicateField, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));
      const selectingSectionIndex = findSectionIndex(
        newState.builderJson,
        newState.selectingSectionId
      );

      if (selectingSectionIndex !== -1) {
        const section = newState.builderJson?.content[selectingSectionIndex] as FormSection;
        const selectingFieldIndex = section.content.findIndex(
          (field) => field.id === action.payload
        );

        if (selectingFieldIndex !== -1) {
          const field = section.content[selectingFieldIndex];
          const cloneField = JSON.parse(JSON.stringify(field));
          // Push to next index
          section.content.splice(selectingFieldIndex + 1, 0, createNewIdForField(cloneField));
        }
      }

      return newState;
    });
    // SECTION
    builder.addCase(updateSection, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));
      const selectingSectionIndex = findSectionIndex(newState.builderJson, action.payload.id);

      if (selectingSectionIndex !== -1) {
        let section = newState.builderJson?.content[selectingSectionIndex] as FormSection;
        section = { ...section, ...action.payload };

        newState.builderJson.content[selectingSectionIndex] = section;
      }

      return newState;
    });
    builder.addCase(duplicateSection, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));
      const sectionIdx = findSectionIndex(newState.builderJson, action.payload);

      if (sectionIdx !== -1) {
        const section = newState.builderJson?.content[sectionIdx] as FormSection;
        const cloneSection = JSON.parse(JSON.stringify(section));
        const newSection = createNewIdForField(cloneSection);
        newState.builderJson?.content?.push(newSection);
        newState.selectingSectionId = newSection.id;
      }

      return newState;
    });
    builder.addCase(removeSection, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));
      const sectionIdx = findSectionIndex(newState.builderJson, action.payload);

      if (sectionIdx !== -1) newState.builderJson?.content?.splice(sectionIdx, 1);

      newState.selectingSectionId =
        newState.builderJson?.content?.find((_) => _.type !== FieldType.Hidden)?.id || '';
      return newState;
    });
    builder.addCase(changeSectionOrder, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));
      const { dragIndex, hoverIndex } = action.payload;

      if (dragIndex === hoverIndex) return newState;

      const dragSection = newState.builderJson?.content[dragIndex];
      newState.builderJson?.content.splice(dragIndex, 1);
      newState.builderJson?.content.splice(hoverIndex, 0, dragSection);

      return newState;
    });

    builder.addCase(setAppearance, (state, action) => {
      if (state.builderJson) {
        state.builderJson.appearance = action.payload;
      }
    });

    builder.addCase(setLayoutSettingsAllPage, (state, action) => {
      const newState: BuilderStoreType = JSON.parse(JSON.stringify(state));
      newState.builderJson.content =
        newState.builderJson.content?.map((contentItem) =>
          contentItem.type === FieldType.Hidden
            ? contentItem
            : ({
              ...contentItem,
              layout: {
                ...(contentItem as FormSection).layout,
                ...action.payload,
              },
            } as FormSection)
        ) || null;

      return newState;
    });

    builder.addCase(setAppearanceSettingsOption, (state, action) => {
      state.selectedAppearanceSettings = action.payload;
    });
    builder.addCase(setBuilderRightNav, (state, action) => {
      state.rightMenuType = action.payload;
    });
    builder.addCase(setCurrentFormId, (state, action) => {
      state.currentFormId = action.payload;
    });
    builder.addCase(setCurrentViewId, (state, action) => {
      state.currentViewId = action.payload;
      // Reset customize fields when switching views
      state.applyCustomizeFields = [];
    });
    builder.addCase(clearAppliedCustomizedFields, (state) => {
      state.applyCustomizeFields = [];
    });

    builder.addCase(updateSectionLayout, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));
      const selectingSectionIndex = findSectionIndex(newState.builderJson, action.payload.id);

      if (selectingSectionIndex !== -1) {
        let section = newState.builderJson?.content[selectingSectionIndex] as FormSection;
        section = { ...section, layout: action.payload.layout };

        newState.builderJson.content[selectingSectionIndex] = section;
      }

      return newState;
    });
    builder.addCase(setUpdateFormSaving, (state, action) => {
      state.formSaving = action.payload;
      return state;
    });
    builder.addCase(setIntegrationSection, (state, action) => {
      state.selectedIntegrationSection = action.payload;
    });
    builder.addCase(setIntegrationItem, (state, action) => {
      state.selectedIntegrationItem = action.payload;
    });
    builder.addCase(addHiddenField, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));
      const hiddenSection = newState.builderJson?.content.find(
        (section) => section.type === FieldType.Hidden
      );

      if (hiddenSection) {
        hiddenSection.content.push(action.payload);
      } else {
        newState.builderJson?.content?.push({
          content: [action.payload],
          type: FieldType.Hidden,
          id: ulid(),
        });
      }

      return newState;
    });
    builder.addCase(updateHiddenField, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));
      const hiddenSection = newState.builderJson?.content.find(
        (section) => section.type === FieldType.Hidden
      );

      if (hiddenSection) {
        const fieldIndex = hiddenSection.content.findIndex(
          (field) => field.id === action.payload.id
        );
        if (fieldIndex !== -1) {
          hiddenSection.content[fieldIndex] = action.payload;
        }
      }

      return newState;
    });
    builder.addCase(applyStateByHistory, (state, action) => {
      return JSON.parse(JSON.stringify({ ...state, ...action.payload }));
    });
    builder.addCase(setIsDragging, (state, action) => {
      state.isDragging = action.payload;
    });
  }
);

// Todo find correct object by id
export const selectingFieldSelector = (state) => {
  const selectingSection = state.builder.builderJson?.content.find(
    (section) => section.id === state.builder.selectingSectionId
  );

  if (selectingSection) {
    return selectingSection.content.find((field) => field.id === state.builder.selectingFieldId);
  }
};
export const selectingSectionSelector = (state: RootState) => {
  return state.builder.builderJson?.content?.find(
    (section) => section.id === state.builder.selectingSectionId
  );
};
export const formPagesDataSelector = (state) => state.builder.builderJson?.content;
export const selectingItemSelector = (state) => state.selectingItem;

export const selectHiddenSection = (state) =>
  state.builder.builderJson?.content?.find((section) => section.type === FieldType.Hidden);

export const selectSectionWithoutHidden = createSelector(
  [(state) => state.builder.builderJson?.content],
  (content) => content?.filter((section) => section.type !== FieldType.Hidden) || []
);

export const selectHiddenFields = (state) =>
  state.builder.builderJson?.content?.find((section) => section.type === FieldType.Hidden)
    ?.content || [];

export default counter;
