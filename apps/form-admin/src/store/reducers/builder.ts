import {
  addFieldToGroup,
  addHiddenField,
  addNewField,
  addNewPage,
  applyCustomizeFields,
  applyFilterFields,
  applyStateByHistory,
  changeSectionOrder,
  clearAppliedCustomizedFields,
  duplicateField,
  duplicateSection,
  initBuilder,
  removeField,
  removeSection,
  resetBuilderStore,
  selectField,
  selectNestedField,
  setActiveSection,
  setAppearance,
  setAppearanceSettingsOption,
  setBuilderRightNav,
  setCurrentFormId,
  setCurrentViewId,
  setFormInfo,
  setIntegrationItem,
  setIntegrationSection,
  setIsDragging,
  setLayoutSettingsAllPage,
  setResultHiddenFieldIds,
  setResultPinnedColumnId,
  setResultSection,
  setScreenshot,
  setShareSection,
  setUpdateFormSaving,
  updateEmbedSettings,
  updateField,
  updateHiddenField,
  updateHiddenSettings,
  updateSection,
  updateSectionLayout,
} from '@/store/action/builder';
import type { RootState } from '@/store/store';
import {
  BuilderRightMenuType,
  type CustomizeFieldInfo,
  type EmbedSettings,
  FieldType,
  type FormBuilderData,
  type FormField,
  type FormSection,
  type Integration,
  ResultTab,
  type Screenshot,
  ShareAction,
  type ShareHiddenField,
} from '@/types';
import { IntegrationSectionTypes } from '@/types';
import { AppearanceSettingsOptionType } from '@/types/enum/appearanceSettings';
import { createNewIdForField, getNewBuilderJson } from '@/utils/builder-helper';
import { ulid } from '@/utils/uuid';
import { createReducer, createSelector } from '@reduxjs/toolkit';

export type BuilderStoreType = {
  builderJson: FormBuilderData;
  selectingSectionId: string;
  selectingShareSection: ShareAction;
  selectingResultSection: ResultTab;
  selectingEmbedSettings: EmbedSettings;
  applyCustomizeFields: CustomizeFieldInfo[];
  applyFilterFields: { [key: string]: string };
  updateHiddenSettings: ShareHiddenField[];
  resultHiddenFieldIds: string[];
  selectingItem: FormField;
  screenshot: Screenshot | null;
  selectingFieldId: string | null;
  selectingGroupFieldId: string | null; // For nested field selection within group fields
  selectedAppearanceSettings: AppearanceSettingsOptionType;
  rightMenuType: BuilderRightMenuType;
  currentFormId: string;
  formSaving: boolean | null;
  selectedIntegrationSection: string;
  resultPinnedColumnId: string | null;
  selectedIntegrationItem: Integration;
  currentViewId: string;
  isDragging: boolean;
  currentHistoryKey: string;
};

const initialState = {
  builderJson: getNewBuilderJson(),
  selectingSectionId: '',
  selectingShareSection: ShareAction.Share,
  selectingResultSection: ResultTab.Results,
  rightMenuType: BuilderRightMenuType.Question,
  selectedAppearanceSettings: AppearanceSettingsOptionType.Default,
  formSaving: null,
  selectedIntegrationSection: IntegrationSectionTypes.Installed,
  selectedIntegrationItem: {} as Integration,
  resultHiddenFieldIds: [] as string[],
  resultPinnedColumnId: null,
  screenshot: null,
  currentViewId: 'default',
  isDragging: false,
  currentHistoryKey: '',
  selectingGroupFieldId: null,
} as BuilderStoreType;

function findSectionIndex(builderJson: any, id: string): number {
  return builderJson?.content.findIndex((section) => section.id === id);
}

// Recursive utility function to find a field at any nesting level
function findFieldRecursively(fields: any[], fieldId: string): { field: any; parent: any; index: number } | null {
  for (let i = 0; i < fields.length; i++) {
    const field = fields[i];
    
    // Check if this is the field we're looking for
    if (field.id === fieldId) {
      return { field, parent: null, index: i };
    }
    
    // If this field has nested fields, search recursively
    if ('fields' in field && field.fields && Array.isArray(field.fields)) {
      const result = findFieldRecursively(field.fields, fieldId);
      if (result) {
        // If we found it in a nested field, update the parent reference
        if (!result.parent) {
          result.parent = field;
        }
        return result;
      }
    }
  }
  
  return null;
}

// Recursive utility function to find and update a field at any nesting level
function updateFieldRecursively(fields: any[], fieldId: string, updatedField: any): boolean {
  for (let i = 0; i < fields.length; i++) {
    const field = fields[i];
    
    // Check if this is the field we're looking for
    if (field.id === fieldId) {
      fields[i] = updatedField;
      return true;
    }
    
    // If this field has nested fields, search recursively
    if ('fields' in field && field.fields && Array.isArray(field.fields)) {
      if (updateFieldRecursively(field.fields, fieldId, updatedField)) {
        return true;
      }
    }
  }
  
  return false;
}

// Recursive utility function to find and remove a field at any nesting level
function removeFieldRecursively(fields: any[], fieldId: string): boolean {
  for (let i = 0; i < fields.length; i++) {
    const field = fields[i];
    
    // Check if this is the field we're looking for
    if (field.id === fieldId) {
      fields.splice(i, 1);
      return true;
    }
    
    // If this field has nested fields, search recursively
    if ('fields' in field && field.fields && Array.isArray(field.fields)) {
      if (removeFieldRecursively(field.fields, fieldId)) {
        return true;
      }
    }
  }
  
  return false;
}

// Recursive utility function to find and duplicate a field at any nesting level
function duplicateFieldRecursively(fields: any[], fieldId: string): boolean {
  for (let i = 0; i < fields.length; i++) {
    const field = fields[i];
    
    // Check if this is the field we're looking for
    if (field.id === fieldId) {
      const cloneField = JSON.parse(JSON.stringify(field));
      fields.splice(i + 1, 0, createNewIdForField(cloneField));
      return true;
    }
    
    // If this field has nested fields, search recursively
    if ('fields' in field && field.fields && Array.isArray(field.fields)) {
      if (duplicateFieldRecursively(field.fields, fieldId)) {
        return true;
      }
    }
  }
  
  return false;
}

const counter = createReducer(
  JSON.parse(JSON.stringify(initialState)) as BuilderStoreType,
  (builder) => {
    builder.addCase(initBuilder, (state, action) => {
      state.builderJson = action.payload;
    });
    builder.addCase(resetBuilderStore, () => {
      return JSON.parse(JSON.stringify(initialState));
    });
    builder.addCase(setFormInfo, (state, action) => {
      state.builderJson = {
        ...state.builderJson,
        ...action.payload,
      };
    });
    builder.addCase(addNewPage, (state, action) => {
      state.builderJson?.content?.push(action.payload);
      state.selectingSectionId = action.payload.id;
    });
    builder.addCase(setActiveSection, (state, action) => {
      state.selectingSectionId = action.payload;
    });

    builder.addCase(setShareSection, (state, action) => {
      state.selectingShareSection = action.payload as ShareAction;
    });
    builder.addCase(setResultSection, (state, action) => {
      state.selectingResultSection = action.payload as ResultTab;
    });
    builder.addCase(updateEmbedSettings, (state, action) => {
      state.selectingEmbedSettings = action.payload;
    });
    builder.addCase(applyCustomizeFields, (state, action) => {
      state.applyCustomizeFields = action.payload;
      state.resultHiddenFieldIds = state.applyCustomizeFields
        .filter((field) => field.isHidden)
        .map((field) => field.questionId);
    });
    builder.addCase(applyFilterFields, (state, action) => {
      state.applyFilterFields = action.payload;
    });
    builder.addCase(updateHiddenSettings, (state, action) => {
      state.updateHiddenSettings = action.payload;
    });
    builder.addCase(setResultHiddenFieldIds, (state, action) => {
      state.resultHiddenFieldIds.push(action.payload);
    });
    builder.addCase(setResultPinnedColumnId, (state, action) => {
      state.resultPinnedColumnId = action.payload;
    });
    builder.addCase(setScreenshot, (state, action) => {
      state.screenshot = action.payload;
    });
    // FIELD
    builder.addCase(addNewField, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));

      const selectingSectionIndex = findSectionIndex(
        newState.builderJson,
        newState.selectingSectionId
      );

      const section = newState.builderJson?.content[selectingSectionIndex] as FormSection;
      if (section) {
        // Check action.payload is add a group of fields or a single field
        if (Array.isArray(action.payload)) {
          // Should active the item settings whenever add a new field (not handle the group field yet)
          // newState.selectingItem = action.payload[0];
          action.payload.forEach((field) => {
            if (field.fields) {
              field.fields = field.fields.map((item) => ({
                ...item,
                id: ulid(),
              }));
            }
            section.content.push({ ...field, id: ulid() });
          });
        } else {
          // Should remove this line cause the payload always an array
          action.payload = { ...action.payload, id: ulid() };
          section.content.push(action.payload);
        }
      }

      return newState;
    });
    builder.addCase(selectField, (state, action) => {
      state.selectingFieldId = action.payload;
      // Clear nested field selection when selecting a main field
      state.selectingGroupFieldId = null;
    });
    builder.addCase(selectNestedField, (state, action) => {
      if (action.payload) {
        // Find the actual parent group field that contains the nested field
        const selectingSection = state.builderJson?.content?.find(
          (section) => section.id === state.selectingSectionId
        );
        
        if (selectingSection && 'content' in selectingSection && selectingSection.content) {
          const result = findFieldRecursively(selectingSection.content, action.payload.fieldId);
          if (result && result.parent) {
            // Find the top-level group field that contains this nested field
            let topLevelGroup = result.parent;
            while (topLevelGroup && 'fields' in topLevelGroup) {
              // Check if this group is directly in the section content
              const isTopLevel = selectingSection.content.some(field => field.id === topLevelGroup.id);
              if (isTopLevel) {
                break;
              }
              // Find the parent of this group
              const parentResult = findFieldRecursively(selectingSection.content, topLevelGroup.id);
              if (parentResult && parentResult.parent) {
                topLevelGroup = parentResult.parent;
              } else {
                break;
              }
            }
            
            state.selectingGroupFieldId = topLevelGroup.id;
            state.selectingFieldId = action.payload.fieldId;
          } else {
            // Fallback to the provided groupFieldId if we can't find the parent
            state.selectingGroupFieldId = action.payload.groupFieldId;
            state.selectingFieldId = action.payload.fieldId;
          }
        }
      } else {
        state.selectingGroupFieldId = null;
        state.selectingFieldId = null;
      }
    });
    builder.addCase(updateField, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));
      const selectingSectionIndex = findSectionIndex(
        newState.builderJson,
        newState.selectingSectionId
      );

      if (selectingSectionIndex !== -1) {
        const section = newState.builderJson?.content[selectingSectionIndex] as FormSection;
        
        // Use recursive function to find and update the field at any nesting level
        updateFieldRecursively(section.content, action.payload.id, action.payload);
      }

      return newState;
    });
    builder.addCase(removeField, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));
      const selectingSectionIndex = findSectionIndex(
        newState.builderJson,
        newState.selectingSectionId
      );

      if (selectingSectionIndex !== -1) {
        const section = newState.builderJson?.content[selectingSectionIndex] as FormSection;
        
        // Use recursive function to find and remove the field at any nesting level
        removeFieldRecursively(section.content, action.payload);
      }

      return newState;
    });
    builder.addCase(duplicateField, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));
      const selectingSectionIndex = findSectionIndex(
        newState.builderJson,
        newState.selectingSectionId
      );

      if (selectingSectionIndex !== -1) {
        const section = newState.builderJson?.content[selectingSectionIndex] as FormSection;
        
        // Use recursive function to find and duplicate the field at any nesting level
        duplicateFieldRecursively(section.content, action.payload);
      }

      return newState;
    });
    builder.addCase(addFieldToGroup, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));
      const { groupId, field, index, removeFromOriginal } = action.payload;
      
      // Find the section containing the group
      const selectingSectionIndex = findSectionIndex(
        newState.builderJson,
        newState.selectingSectionId
      );

      if (selectingSectionIndex !== -1) {
        const section = newState.builderJson?.content[selectingSectionIndex] as FormSection;
        
        // If we need to remove from original location, do it first
        if (removeFromOriginal && field.id) {
          const originalFieldIndex = section.content.findIndex(
            (f) => f.id === field.id
          );
          if (originalFieldIndex !== -1) {
            section.content.splice(originalFieldIndex, 1);
          }
        }
        
        const groupFieldIndex = section.content.findIndex(
          (f) => f.id === groupId
        );

        if (groupFieldIndex !== -1) {
          const groupField = section.content[groupFieldIndex] as any;
          if (groupField.fields) {
            // Create new field with new ID if it's not being moved from the same section
            const newField = removeFromOriginal ? { ...field } : createNewIdForField(field);
            
            // Insert at specified index or at the end
            if (typeof index === 'number') {
              groupField.fields.splice(index, 0, newField);
            } else {
              groupField.fields.push(newField);
            }
          }
        }
      }

      return newState;
    });
    // SECTION
    builder.addCase(updateSection, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));
      const selectingSectionIndex = findSectionIndex(newState.builderJson, action.payload.id);

      if (selectingSectionIndex !== -1) {
        let section = newState.builderJson?.content[selectingSectionIndex] as FormSection;
        section = { ...section, ...action.payload };

        newState.builderJson.content[selectingSectionIndex] = section;
      }

      return newState;
    });
    builder.addCase(duplicateSection, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));
      const sectionIdx = findSectionIndex(newState.builderJson, action.payload);

      if (sectionIdx !== -1) {
        const section = newState.builderJson?.content[sectionIdx] as FormSection;
        const cloneSection = JSON.parse(JSON.stringify(section));
        const newSection = createNewIdForField(cloneSection);
        newState.builderJson?.content?.push(newSection);
        newState.selectingSectionId = newSection.id;
      }

      return newState;
    });
    builder.addCase(removeSection, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));
      const sectionIdx = findSectionIndex(newState.builderJson, action.payload);

      if (sectionIdx !== -1) newState.builderJson?.content?.splice(sectionIdx, 1);

      newState.selectingSectionId =
        newState.builderJson?.content?.find((_) => _.type !== FieldType.Hidden)?.id || '';
      return newState;
    });
    builder.addCase(changeSectionOrder, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));
      const { dragIndex, hoverIndex } = action.payload;

      if (dragIndex === hoverIndex) return newState;

      const dragSection = newState.builderJson?.content[dragIndex];
      newState.builderJson?.content.splice(dragIndex, 1);
      newState.builderJson?.content.splice(hoverIndex, 0, dragSection);

      return newState;
    });

    builder.addCase(setAppearance, (state, action) => {
      if (state.builderJson) {
        state.builderJson.appearance = action.payload;
      }
    });

    builder.addCase(setLayoutSettingsAllPage, (state, action) => {
      const newState: BuilderStoreType = JSON.parse(JSON.stringify(state));
      newState.builderJson.content =
        newState.builderJson.content?.map((contentItem) =>
          contentItem.type === FieldType.Hidden
            ? contentItem
            : ({
              ...contentItem,
              layout: {
                ...(contentItem as FormSection).layout,
                ...action.payload,
              },
            } as FormSection)
        ) || null;

      return newState;
    });

    builder.addCase(setAppearanceSettingsOption, (state, action) => {
      state.selectedAppearanceSettings = action.payload;
    });
    builder.addCase(setBuilderRightNav, (state, action) => {
      state.rightMenuType = action.payload;
    });
    builder.addCase(setCurrentFormId, (state, action) => {
      state.currentFormId = action.payload;
    });
    builder.addCase(setCurrentViewId, (state, action) => {
      state.currentViewId = action.payload;
      // Reset customize fields when switching views
      state.applyCustomizeFields = [];
    });
    builder.addCase(clearAppliedCustomizedFields, (state) => {
      state.applyCustomizeFields = [];
    });

    builder.addCase(updateSectionLayout, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));
      const selectingSectionIndex = findSectionIndex(newState.builderJson, action.payload.id);

      if (selectingSectionIndex !== -1) {
        let section = newState.builderJson?.content[selectingSectionIndex] as FormSection;
        section = { ...section, layout: action.payload.layout };

        newState.builderJson.content[selectingSectionIndex] = section;
      }

      return newState;
    });
    builder.addCase(setUpdateFormSaving, (state, action) => {
      state.formSaving = action.payload;
      return state;
    });
    builder.addCase(setIntegrationSection, (state, action) => {
      state.selectedIntegrationSection = action.payload;
    });
    builder.addCase(setIntegrationItem, (state, action) => {
      state.selectedIntegrationItem = action.payload;
    });
    builder.addCase(addHiddenField, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));
      const hiddenSection = newState.builderJson?.content.find(
        (section) => section.type === FieldType.Hidden
      );

      if (hiddenSection) {
        hiddenSection.content.push(action.payload);
      } else {
        newState.builderJson?.content?.push({
          content: [action.payload],
          type: FieldType.Hidden,
          id: ulid(),
        });
      }

      return newState;
    });
    builder.addCase(updateHiddenField, (state, action) => {
      const newState = JSON.parse(JSON.stringify(state));
      const hiddenSection = newState.builderJson?.content.find(
        (section) => section.type === FieldType.Hidden
      );

      if (hiddenSection) {
        const fieldIndex = hiddenSection.content.findIndex(
          (field) => field.id === action.payload.id
        );
        if (fieldIndex !== -1) {
          hiddenSection.content[fieldIndex] = action.payload;
        }
      }

      return newState;
    });
    builder.addCase(applyStateByHistory, (state, action) => {
      return JSON.parse(JSON.stringify({ ...state, ...action.payload }));
    });
    builder.addCase(setIsDragging, (state, action) => {
      state.isDragging = action.payload;
    });
  }
);

// Todo find correct object by id
export const selectingFieldSelector = (state) => {
  const selectingSection = state.builder.builderJson?.content.find(
    (section) => section.id === state.builder.selectingSectionId
  );

  if (selectingSection && state.builder.selectingFieldId) {
    // Use recursive function to find the field at any nesting level
    const result = findFieldRecursively(selectingSection.content, state.builder.selectingFieldId);
    return result ? result.field : null;
  }
  
  return null;
};
export const selectingSectionSelector = (state: RootState) => {
  return state.builder.builderJson?.content?.find(
    (section) => section.id === state.builder.selectingSectionId
  );
};
export const formPagesDataSelector = (state) => state.builder.builderJson?.content;
export const selectingItemSelector = (state) => state.selectingItem;

export const selectHiddenSection = (state) =>
  state.builder.builderJson?.content?.find((section) => section.type === FieldType.Hidden);

export const selectSectionWithoutHidden = createSelector(
  [(state) => state.builder.builderJson?.content],
  (content) => content?.filter((section) => section.type !== FieldType.Hidden) || []
);

export const selectHiddenFields = (state) =>
  state.builder.builderJson?.content?.find((section) => section.type === FieldType.Hidden)
    ?.content || [];

// Selector to get the parent group field when a nested field is selected
export const selectingGroupFieldSelector = (state) => {
  if (state.builder.selectingGroupFieldId) {
    const selectingSection = state.builder.builderJson?.content.find(
      (section) => section.id === state.builder.selectingSectionId
    );
    
    if (selectingSection) {
      // Use recursive function to find the group field at any nesting level
      const result = findFieldRecursively(selectingSection.content, state.builder.selectingGroupFieldId);
      return result ? result.field : null;
    }
  }
  return null;
};

export default counter;
