import {
  ADD_FIELD_TO_GROUP,
  ADD_<PERSON><PERSON>DEN_FIELD,
  ADD_NEW_FIELD,
  ADD_NEW_PAGE,
  CHANGE_SECTION_ORDER,
  <PERSON><PERSON>LICATE_FIELD,
  <PERSON><PERSON><PERSON><PERSON>ATE_SECTION,
  REMOVE_FIELD,
  R<PERSON>OVE_SECTION,
  SET_ACTIVE_SECTION,
  SET_APPEARANCE,
  SET_FORM_INFO,
  SET_IS_DRAGGING,
  SET_LAYOUT_SETTINGS_ALL_PAGE,
  UPDATE_FIELD,
  UPDATE_HIDDEN_FIELD,
  UPDATE_SECTION,
  UPDATE_SECTION_LAYOUT,
} from '@/store/action/actionTypes';
import { BuilderHistory } from '@/utils/builder-history';
import type { Middleware } from '@reduxjs/toolkit';

// Actions that should trigger history recording
const HISTORY_ACTIONS = [
  ADD_NEW_FIELD,
  ADD_FIELD_TO_GROUP,
  UPDATE_FIELD,
  REMOVE_FIELD,
  <PERSON>UP<PERSON><PERSON><PERSON>E_FIELD,
  UPDATE_SECTION,
  D<PERSON>LICATE_SECTION,
  R<PERSON>OVE_SECTION,
  CHAN<PERSON>_SECTION_ORDER,
  UPDATE_SECTION_LAYOUT,
  SET_LAYOUT_SETTINGS_ALL_PAGE,
  ADD_HIDDEN_FIELD,
  UPDATE_HIDDEN_FIELD,
  ADD_NEW_PAGE,
  SET_ACTIVE_SECTION,
  SET_APPEARANCE,
  SET_FORM_INFO,
];

// Create history instance
const builderHistory = new BuilderHistory();

export const historyMiddleware: Middleware = (store) => (next) => (action: unknown) => {
  // Run the action first
  const result = next(action);

  // Check if we should record this action
  const state = store.getState();
  // eslint-disable-next-line
  // @ts-expect-error
  if (HISTORY_ACTIONS.includes(action?.type)) {
    if (
      !state.builder.isDragging &&
      state.builder.builderJson &&
      state.builder.builderJson.appearance &&
      !state.builder.builderJson.appearance.isDefault &&
      state.builder.builderJson.content
    ) {
      builderHistory.push({
        builderJson: state.builder.builderJson,
        selectingSectionId: state.builder.selectingSectionId,
        currentHistoryKey: new Date().toISOString(),
      });
    }

    // eslint-disable-next-line
    // @ts-expect-error
  } else if (action?.type === SET_IS_DRAGGING && action.payload === false) {
    builderHistory.push({
      builderJson: state.builder.builderJson,
      selectingSectionId: state.builder.selectingSectionId,
      currentHistoryKey: new Date().toISOString(),
    });
  }

  return result;
};

// Export for use in hooks
export const getBuilderHistory = () => builderHistory;
