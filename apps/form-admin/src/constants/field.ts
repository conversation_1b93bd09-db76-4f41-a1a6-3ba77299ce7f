import {
  AddressFieldsName,
  FieldType,
  type FormField,
  GroupFieldType,
  RatingShape,
  ValidatorType,
} from '@/types';
import {
  getDateTimeField,
  getEmailField,
  getGroupField,
  getLongQAField,
  getOpinionScaleField,
  getPostcodeField,
  getTextField,
  getUploaderField,
  getWebsiteField,
} from '@/utils/builder-helper';
import {
  IconAlignLeft,
  IconAt,
  IconBaselineDensityLarge,
  IconBaselineDensityMedium,
  IconCalendar,
  IconCalendarDue,
  IconCalendarTime,
  IconChartBar,
  IconCircleChevronDown,
  IconClockHour3,
  IconDeviceMobile,
  IconFolder,
  IconGavel,
  IconHeading,
  IconLink,
  IconListCheck,
  IconLocation,
  IconMapPin,
  IconSquareCheck,
  IconSquareRoundedCheck,
  IconStar,
  IconUpload,
  IconUser,
  IconWashTumbleDry,
} from '@tabler/icons-react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Colors } from './themeConfiguration';

export type FieldListType = {
  id: string;
  label: string;
  items: {
    icon: { name: any; color?: string; bgColor?: string };
    label: string;
    data: FormField[];
  }[];
};

export const useFieldByType = () => {
  const { t } = useTranslation('form_builder');

  const defaultNameGroup = getGroupField({
    type: GroupFieldType.Name,
    description: '',
    descriptionEnabled: false,
    hideLabel: true,
    fields: [
      getTextField({
        label: t('formField.name.first.label'),
        placeholder: t('formField.name.first.placeholder'),
        description: '',
        style: { width: '50%' },
        validators: [
          {
            type: ValidatorType.Required,
            value: true,
          },
          {
            type: ValidatorType.Alphabet,
            value: true,
          },
        ],
      }) as FormField,
      getTextField({
        label: t('formField.name.last.label'),
        placeholder: t('formField.name.last.placeholder'),
        style: { width: '50%' },
        validators: [
          {
            type: ValidatorType.Required,
            value: true,
          },
          {
            type: ValidatorType.Alphabet,
            value: true,
          },
        ],
      }) as FormField,
    ],
  });

  const defaultFullNameGroup = getGroupField({
    type: GroupFieldType.Name,
    description: t('formField.name.hiragana.description'),
    descriptionEnabled: true,
    fields: [
      getTextField({
        label: t('formField.name.full.label'),
        placeholder: t('formField.name.full.placeholder'),
        validators: [
          {
            type: ValidatorType.Required,
            value: true,
          },
          {
            type: ValidatorType.Hiragana,
            value: true,
          },
        ],
      }) as FormField,
    ],
  });

  const defaultSectionGroup = getGroupField({
    type: GroupFieldType.Section,
    descriptionEnabled: false,
    hideLabel: true,
    label: t('formField.section.label'),
    fields: [], // Empty by default
  });

  const fieldByType = useMemo(
    () => [
      {
        id: 'address_info',
        label: t('formField.type.contactInfo.label'),
        items: [
          {
            icon: { name: IconUser },
            label: t('formField.name.nav.label'),
            data: [defaultNameGroup],
          },
          {
            icon: { name: IconDeviceMobile },
            label: t('formField.phoneNumber.label'),
            data: [
              getTextField({
                label: t('formField.phoneNumber.label'),
                type: FieldType.PhoneNumber,
                placeholder: t('formField.phoneNumber.placeholder'),
                descriptionEnabled: false,
                description: t('formField.phoneNumber.description'),
                validators: [
                  {
                    type: ValidatorType.Required,
                    value: true,
                  },
                ],
              }) as FormField,
            ],
          },
          {
            icon: { name: IconMapPin },
            label: t('formField.address.label'),
            data: [
              getGroupField({
                label: t('formField.groupAddress.label'),
                type: GroupFieldType.Address,
                description: t('formField.groupAddress.description'),
                descriptionEnabled: true,
                fields: [
                  getTextField({
                    label: t('formField.postCode.label'),
                    name: AddressFieldsName.PostCode,
                    descriptionEnabled: false,
                    placeholder: t('formField.postCode.placeholder'),
                    validators: [
                      {
                        type: ValidatorType.Required,
                        value: true,
                      },
                    ],
                    style: { width: '50%' },
                  }) as FormField,
                  getTextField({
                    label: t('formField.prefecture.label'),
                    type: FieldType.Dropdown,
                    descriptionEnabled: false,
                    name: AddressFieldsName.Prefecture,
                    options: [
                      {
                        label: t('formField.prefecture.placeholder'),
                        defaultCheck: true,
                      },
                    ],
                    style: { width: '50%' },
                  }) as FormField,
                  getTextField({
                    label: t('formField.city.label'),
                    name: AddressFieldsName.City,
                    descriptionEnabled: false,
                    placeholder: '',
                    validators: [
                      {
                        type: ValidatorType.Required,
                        value: true,
                      },
                    ],
                    style: { width: '50%' },
                  }) as FormField,
                  getTextField({
                    label: t('formField.street.label'),
                    name: AddressFieldsName.Street,
                    descriptionEnabled: false,
                    placeholder: '',
                    validators: [
                      {
                        type: ValidatorType.Required,
                        value: true,
                      },
                    ],
                    style: { width: '50%' },
                  }) as FormField,
                  getTextField({
                    label: t('formField.building.label'),
                    placeholder: '',
                    descriptionEnabled: false,
                    name: AddressFieldsName.Building,
                    validators: [],
                    style: { width: '100%' },
                  }) as FormField,
                ],
              }),
            ],
          },
          {
            icon: { name: IconAt },
            label: t('formField.email.nav.label'),
            data: [
              {
                ...getEmailField(),
                label: t('formField.email.label'),
                description: t('formField.email.description'),
                placeholder: t('formField.email.placeholder'),
              },
            ],
          },
          {
            icon: { name: IconLink },
            label: t('formField.website.nav.label'),
            data: [
              {
                ...getWebsiteField(),
                label: t('formField.website.label'),
                description: t('formField.website.description'),
                placeholder: t('formField.website.placeholder'),
              },
            ],
          },
          {
            icon: { name: IconLocation },
            label: t('formField.postCode.label'),
            data: [
              {
                ...getPostcodeField(),
                label: t('formField.postCode.label'),
                description: t('formField.postCode.description'),
                placeholder: t('formField.postCode.placeholder'),
              },
            ],
          },
        ],
      },
      {
        id: 'question_and_text',
        label: t('formField.type.questionAndText.label'),
        items: [
          {
            icon: { name: IconHeading, color: Colors.decaViolet[6], bgColor: Colors.decaViolet[0] },
            label: t('formField.heading.nav.label'),
            data: [
              getTextField({
                type: FieldType.Heading,
                label: t('formField.heading.label'),
                placeholder: t('formField.heading.heading.placeholder'),
                description: t('formField.heading.description'),
              }),
            ],
          },
          {
            icon: {
              name: IconAlignLeft,
              color: Colors.decaViolet[6],
              bgColor: Colors.decaViolet[0],
            },
            label: t('formField.paragraph.nav.label'),
            data: [
              getTextField({
                type: FieldType.Paragraph,
                placeholder: t('formField.paragraph.paragraph.placeholder'),
                label: t('formField.paragraph.label'),
              }),
            ],
          },
          {
            icon: {
              name: IconBaselineDensityLarge,
              color: Colors.decaViolet[6],
              bgColor: Colors.decaViolet[0],
            },
            label: t('formField.shortQAndA.nav.label'),
            data: [
              getTextField({
                label: t('formField.shortQAndA.label'),
                description: t('formField.shortQAndA.description'),
                descriptionEnabled: true,
                validators: [
                  {
                    type: ValidatorType.Required,
                    value: true,
                  },
                ],
                placeholder: t('formField.shortQAndA.placeholder'),
              }),
            ],
          },
          {
            icon: {
              name: IconBaselineDensityMedium,
              color: Colors.decaViolet[6],
              bgColor: Colors.decaViolet[0],
            },
            label: t('formField.longQA.nav.label'),
            data: [
              getLongQAField({
                label: t('formField.longQA.label'),
                description: t('formField.longQA.description'),
                descriptionEnabled: true,
                validators: [
                  {
                    type: ValidatorType.Required,
                    value: true,
                  },
                ],
                placeholder: t('formField.longQA.placeholder'),
              }),
            ],
          },
          {
            icon: {
              name: IconChartBar,
              color: Colors.decaViolet[6],
              bgColor: Colors.decaViolet[0],
            },
            label: t('formField.opinionScale.nav.label'),
            data: [
              getOpinionScaleField({
                type: FieldType.OpinionScale,
                label: t('formField.opinionScale.label'),
                description: t('formField.opinionScale.description'),
                footerLabel: {
                  left: t('fieldSettings.opinion.extremely.poor'),
                },
              }),
            ],
          },
          {
            icon: {
              name: IconUpload,
              color: Colors.decaViolet[6],
              bgColor: Colors.decaViolet[0],
            },
            label: t('formField.fileUploader.nav.label'),
            data: [
              getUploaderField({
                label: t('formField.fileUploader.label'),
                description: t('formField.fileUploader.description'),
              }),
            ],
          },
          {
            icon: {
              name: IconStar,
              color: Colors.decaViolet[6],
              bgColor: Colors.decaViolet[0],
            },
            label: t('formField.rating.nav.label'),
            data: [
              getOpinionScaleField({
                type: FieldType.Rating,
                shape: RatingShape.Star,
                showNumber: true,
                label: t('formField.rating.label'),
                description: t('formField.rating.description'),
              }),
            ],
          },
        ],
      },
      {
        id: 'group',
        label: t('formField.type.group.label'),
        items: [
          {
            icon: { name: IconFolder, color: Colors.decaNavy[6], bgColor: Colors.decaNavy[0] },
            label: t('formField.section.title'),
            data: [defaultSectionGroup],
          },
        ],
      },
      {
        id: 'choices',
        label: t('formField.type.choice.label'),
        items: [
          {
            icon: {
              name: IconSquareRoundedCheck,
              color: Colors.decaGreen[6],
              bgColor: Colors.decaGreen[0],
            },
            label: t('formField.multipleChoice.nav.label'),
            data: [
              getTextField({
                type: FieldType.MultipleChoice,
                label: t('formField.multipleChoice.label'),
                description: t('formField.multipleChoice.description'),
                options: [
                  {
                    label: 'オプション1',
                    defaultCheck: false,
                  },
                ],
              }),
            ],
          },
          {
            icon: { name: IconListCheck, color: Colors.decaGreen[6], bgColor: Colors.decaGreen[0] },
            label: t('formField.checkboxes.nav.label'),
            data: [
              getTextField({
                type: FieldType.Checkboxes,
                label: t('formField.checkboxes.label'),
                description: t('formField.checkboxes.description'),
                options: [
                  {
                    label: 'オプション1',
                    defaultCheck: false,
                  },
                ],
              }),
            ],
          },
          {
            icon: {
              name: IconCircleChevronDown,
              color: Colors.decaGreen[6],
              bgColor: Colors.decaGreen[0],
            },
            label: t('formField.dropdown.nav.label'),
            defaultOption: {
              label: 'オプション2',
              defaultCheck: true,
            },
            data: [
              getTextField({
                type: FieldType.Dropdown,
                label: t('formField.dropdown.label'),
                description: t('formField.dropdown.description'),
                defaultOption: {
                  label: 'オプション1',
                  defaultCheck: true,
                },
                options: [
                  {
                    label: 'オプション1',
                    defaultCheck: true,
                  },
                  {
                    label: 'オプション2',
                    defaultCheck: false,
                  },
                  {
                    label: 'オプション3',
                    defaultCheck: false,
                  },
                ],
              }),
            ],
          },
          // TODO: Hide for this release
          // {
          //   icon: { name: IconPhotoCheck, color: Colors.decaGreen[6], bgColor: Colors.decaGreen[0] },
          //   label: 'Picture Choices',
          //   data: [
          //     getTextField({
          //       type: FieldType.PictureChoice,
          //       label: 'Type your question here',
          //       description: 'Type your description',
          //       options: [
          //         {
          //           label: 'Checkbox',
          //           defaultCheck: false,
          //         },
          //       ],
          //     }),
          //   ],
          // },
          {
            icon: {
              name: IconWashTumbleDry,
              color: Colors.decaGreen[6],
              bgColor: Colors.decaGreen[0],
            },
            label: t('formField.yesNo.nav.label'),
            data: [
              getTextField({
                type: FieldType.YesNo,
                label: t('formField.yesNo.label'),
                description: t('formField.yesNo.description'),
                options: [
                  {
                    label: t('formField.yesNo.yes'),
                    defaultCheck: false,
                  },
                  {
                    label: t('formField.yesNo.no'),
                    defaultCheck: false,
                  },
                ],
              }),
            ],
          },
          {
            icon: { name: IconGavel, color: Colors.decaGreen[6], bgColor: Colors.decaGreen[0] },
            label: t('formField.legal.nav.label'),
            data: [
              getTextField({
                type: FieldType.Legal,
                label: t('formField.legal.label'),
                description: t('formField.legal.description'),
                options: [
                  {
                    label: t('formField.legal.yes'),
                    defaultCheck: false,
                  },
                ],
              }),
            ],
          },
          {
            icon: {
              name: IconSquareCheck,
              color: Colors.decaGreen[6],
              bgColor: Colors.decaGreen[0],
            },
            label: t('formField.checkbox.nav.label'),
            data: [
              getTextField({
                type: FieldType.Checkbox,
                options: [
                  {
                    label: t('formField.checkbox.description'),
                    defaultCheck: false,
                  },
                ],
              }),
            ],
          },
        ],
      },
      {
        id: 'date_and_time',
        label: t('formField.type.dateAndTime.nav.label'),
        items: [
          {
            icon: {
              name: IconCalendar,
              color: Colors.decaYellow[6],
              bgColor: Colors.decaYellow[0],
            },
            label: t('formField.datePicker.nav.label'),
            data: [
              getDateTimeField({
                label: t('formField.datePicker.label'),
                placeholder: t('formField.datePicker.placeholder'),
                description: t('formField.datePicker.description'),
                dateFormat: 'YYYY/MM/DD',
                type: FieldType.Date,
              }),
            ],
          },
          {
            icon: {
              name: IconCalendarDue,
              color: Colors.decaYellow[6],
              bgColor: Colors.decaYellow[0],
            },
            label: t('formField.dateSelector.nav.label'),
            data: [
              getDateTimeField({
                label: t('formField.dateSelector.label'),
                dateFormat: 'YYYY/MM/DD',
                type: FieldType.DateSelector,
                validators: [
                  {
                    type: ValidatorType.Required,
                    value: true,
                  },
                ],
              }),
            ],
          },
          {
            icon: {
              name: IconCalendarDue,
              color: Colors.decaYellow[6],
              bgColor: Colors.decaYellow[0],
            },
            label: t('formField.dateRange.nav.label'),
            data: [
              getDateTimeField({
                label: t('formField.dateRange.label'),
                placeholder: t('formField.dateRange.placeholder'),
                description: t('formField.dateRange.description'),
                dateFormat: 'YYYY/MM/DD',
                type: FieldType.DateRange,
              }),
            ],
          },
          {
            icon: {
              name: IconClockHour3,
              color: Colors.decaYellow[6],
              bgColor: Colors.decaYellow[0],
            },
            label: t('formField.timePicker.nav.label'),
            data: [
              getDateTimeField({
                label: t('formField.timePicker.label'),
                placeholder: t('formField.timePicker.placeholder'),
                description: t('formField.timePicker.description'),
                timeFormat: 'hh:mm A',
                type: FieldType.Time,
              }),
            ],
          },
          {
            icon: {
              name: IconCalendarTime,
              color: Colors.decaYellow[6],
              bgColor: Colors.decaYellow[0],
            },
            label: t('formField.dateAndTimePicker.nav.label'),
            data: [
              getDateTimeField({
                label: t('formField.dateAndTimePicker.label'),
                placeholder: t('formField.dateAndTimePicker.placeholder'),
                description: t('formField.dateAndTimePicker.description'),
                dateFormat: 'YYYY/MM/DD',
                timeFormat: 'HH:mm',
                type: FieldType.DateTime,
              }),
            ],
          },
          {
            icon: {
              name: IconCalendarTime,
              color: Colors.decaYellow[6],
              bgColor: Colors.decaYellow[0],
            },
            label: t('formField.dateAndTimeRange.nav.label'),
            data: [
              getGroupField({
                type: GroupFieldType.DateTimeRange,
                hideLabel: true,
                descriptionEnabled: false,
                fields: [
                  getDateTimeField({
                    label: t('formField.dateAndTimeRange.start'),
                    placeholder: t('formField.dateAndTimeRange.start.label'),
                    dateFormat: 'YYYY/MM/DD',
                    timeFormat: 'HH:mm',
                    type: FieldType.DateTime,
                    validators: [
                      {
                        type: ValidatorType.Required,
                        value: true,
                      },
                    ],
                    style: { width: '50%' },
                  }) as FormField,
                  getDateTimeField({
                    label: t('formField.dateAndTimeRange.end'),
                    placeholder: t('formField.dateAndTimeRange.end.label'),
                    dateFormat: 'YYYY/MM/DD',
                    timeFormat: 'HH:mm',
                    type: FieldType.DateTime,
                    validators: [
                      {
                        type: ValidatorType.Required,
                        value: true,
                      },
                    ],
                    style: { width: '50%' },
                  }) as FormField,
                ],
              }),
            ],
          },
        ],
      },
    ],
    []
  );

  return {
    fieldByType,
    defaultFullNameGroup,
    defaultNameGroup,
    defaultSectionGroup,
  };
};
