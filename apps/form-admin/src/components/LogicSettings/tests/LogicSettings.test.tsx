import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { MantineProvider } from '@mantine/core';
import { LogicSettings } from '../LogicSettings';
import { useFieldLogic } from '@/hooks/useFieldLogic';
import { useUpdateFieldLogic } from '@/hooks/useUpdateFieldLogic';
import { DEFAULT_COMBINATOR, MAX_GROUPS } from '@/constants/form-logic';

// Mock the hooks
vi.mock('@/hooks/useFieldLogic');
vi.mock('@/hooks/useUpdateFieldLogic');
// Don't mock lodash - use the real debounce function

// Mock the ConditionsBuilder component
vi.mock('../ConditionsBuilder', () => ({
  default: ({ onQueryChange, onActionChange, query, action }: any) => (
    <div data-testid="conditions-builder">
      <button 
        onClick={() => onQueryChange({ combinator: 'and', rules: [] })}
        data-testid="query-change-btn"
      >
        Change Query
      </button>
      <button 
        onClick={() => onActionChange('show')}
        data-testid="action-change-btn"
      >
        Change Action
      </button>
      <div data-testid="query-data">{JSON.stringify(query)}</div>
      <div data-testid="action-data">{action}</div>
    </div>
  ),
}));

const mockStore = configureStore({
  reducer: {
    builder: () => ({
      selectingField: { id: 'test-field', type: 'text' },
    }),
  },
});

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <Provider store={mockStore}>
      <MantineProvider>
        {component}
      </MantineProvider>
    </Provider>
  );
};

describe('LogicSettings', () => {
  const mockSetLogic = vi.fn();
  const mockUpdateFieldLogic = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    
    (useFieldLogic as any).mockReturnValue({
      logic: {
        conditions: {
          combinator: DEFAULT_COMBINATOR,
          rules: [],
        },
        action: 'show',
      },
      setLogic: mockSetLogic,
    });

    (useUpdateFieldLogic as any).mockReturnValue(mockUpdateFieldLogic);
  });

  it('renders setup conditions text when no conditions exist', () => {
    renderWithProviders(<LogicSettings />);
    
    expect(screen.getByText('logic.setUpConditions')).toBeInTheDocument();
  });

  it('renders add condition button when conditions are less than max groups', () => {
    renderWithProviders(<LogicSettings />);
    
    expect(screen.getByText('logic.addCondition')).toBeInTheDocument();
  });

  it('does not render add condition button when max groups reached', () => {
    (useFieldLogic as any).mockReturnValue({
      logic: {
        conditions: {
          combinator: DEFAULT_COMBINATOR,
          rules: new Array(MAX_GROUPS).fill({}),
        },
        action: 'show',
      },
      setLogic: mockSetLogic,
    });

    renderWithProviders(<LogicSettings />);
    
    expect(screen.queryByText('logic.addCondition')).not.toBeInTheDocument();
  });

  it('renders ConditionsBuilder when conditions exist', () => {
    (useFieldLogic as any).mockReturnValue({
      logic: {
        conditions: {
          combinator: DEFAULT_COMBINATOR,
          rules: [{ field: 'test', operator: 'is', value: 'test' }],
        },
        action: 'show',
      },
      setLogic: mockSetLogic,
    });

    renderWithProviders(<LogicSettings />);
    
    expect(screen.getByTestId('conditions-builder')).toBeInTheDocument();
  });

  it('handles add condition button click', () => {
    renderWithProviders(<LogicSettings />);
    
    const addButton = screen.getByText('logic.addCondition');
    fireEvent.click(addButton);

    expect(mockSetLogic).toHaveBeenCalledWith({
      conditions: {
        combinator: DEFAULT_COMBINATOR,
        rules: [
          {
            combinator: DEFAULT_COMBINATOR,
            rules: [
              {
                field: '',
                operator: 'is',
                value: '',
              },
            ],
          },
        ],
      },
      action: 'show',
    });
  });

  it('handles query change', async () => {
    (useFieldLogic as any).mockReturnValue({
      logic: {
        conditions: {
          combinator: DEFAULT_COMBINATOR,
          rules: [{ field: 'test', operator: 'is', value: 'test' }],
        },
        action: 'show',
      },
      setLogic: mockSetLogic,
    });

    renderWithProviders(<LogicSettings />);
    
    const queryChangeBtn = screen.getByTestId('query-change-btn');
    fireEvent.click(queryChangeBtn);

    await waitFor(() => {
      expect(mockSetLogic).toHaveBeenCalledWith({
        conditions: { combinator: 'and', rules: [] },
        action: 'show',
      });
    });

    // Note: The debounced updateFieldLogic call is tested separately
    // since it has a 1000ms delay which is complex to test reliably
  });

  it('handles action change', async () => {
    (useFieldLogic as any).mockReturnValue({
      logic: {
        conditions: {
          combinator: DEFAULT_COMBINATOR,
          rules: [{ field: 'test', operator: 'is', value: 'test' }],
        },
        action: 'show',
      },
      setLogic: mockSetLogic,
    });

    renderWithProviders(<LogicSettings />);
    
    const actionChangeBtn = screen.getByTestId('action-change-btn');
    fireEvent.click(actionChangeBtn);

    await waitFor(() => {
      expect(mockSetLogic).toHaveBeenCalledWith({
        conditions: {
          combinator: DEFAULT_COMBINATOR,
          rules: [{ field: 'test', operator: 'is', value: 'test' }],
        },
        action: 'show',
      });
      expect(mockUpdateFieldLogic).toHaveBeenCalledWith({
        conditions: {
          combinator: DEFAULT_COMBINATOR,
          rules: [{ field: 'test', operator: 'is', value: 'test' }],
        },
        action: 'show',
      });
    });
  });

  it('returns null when logic is not available', () => {
    (useFieldLogic as any).mockReturnValue({
      logic: null,
      setLogic: mockSetLogic,
    });

    const { container } = renderWithProviders(<LogicSettings />);
    // The component should not render anything when logic is null
    expect(container.querySelector('[data-testid="conditions-builder"]')).not.toBeInTheDocument();
    expect(container.querySelector('button')).not.toBeInTheDocument();
  });

  it('does not call updateFieldLogic when adding condition', () => {
    renderWithProviders(<LogicSettings />);
    
    const addButton = screen.getByText('logic.addCondition');
    fireEvent.click(addButton);

    expect(mockSetLogic).toHaveBeenCalled();
    expect(mockUpdateFieldLogic).not.toHaveBeenCalled();
  });
});
