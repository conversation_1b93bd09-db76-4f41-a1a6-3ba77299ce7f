import { ActionIcon, Button, Group, Select, Stack, Text } from '@mantine/core';
import { IconPlus, IconTrash } from '@tabler/icons-react';
import type { RuleGroupType } from 'react-querybuilder';
import type { PageLogicRule } from '@/types/form-logic';
import PageConditionsBuilder from './PageConditionsBuilder';
import { useTranslation } from 'react-i18next';

const ENDING_CONFIG_VALUE = '__ending_configuration__';

interface PageLogicRuleComponentProps {
  rule: PageLogicRule;
  index: number;
  pageOptions: Array<{ value: string; label: string }>;
  canAddMoreConditions: boolean;
  onRemove: (ruleId: string) => void;
  onTargetChange: (ruleId: string, targetPageId: string | null) => void;
  onQueryChange: (ruleId: string, newQuery: RuleGroupType) => void;
  onAddCondition: (ruleId: string) => void;
}

export const PageLogicRuleComponent = ({
  rule,
  index,
  pageOptions,
  canAddMoreConditions,
  onRemove,
  onTargetChange,
  onQueryChange,
  onAddCondition,
}: PageLogicRuleComponentProps) => {
  const { t } = useTranslation('form_builder');
  
  const handleSelectChange = (value: string | null) => {
    onTargetChange(rule.id!, value);
  };

  return (
    <Stack gap="sm">
      <Group justify="space-between" align="center">
        <Text fw={500} size="md">{t('logic.rule_number', { number: index + 1 })}</Text>
        <ActionIcon 
          variant="transparent" 
          color="red" 
          onClick={() => onRemove(rule.id!)}
        >
          <IconTrash size={16} />
        </ActionIcon>
      </Group>
      
      <Text size="md" fw={500}>{t('logic.only_go_to')}</Text>
      <Select
        placeholder={t('logic.select_target_page')}
        data={[
          ...pageOptions,
          { value: ENDING_CONFIG_VALUE, label: t('logic.ending_configuration') }
        ]}
        value={rule.targetPageId}
        onChange={handleSelectChange}
      />
      
      <Text size="md" fw={500}>{t('logic.when')}</Text>
      
      <Stack gap="sm">
        <PageConditionsBuilder
          query={rule.conditions as RuleGroupType}
          onQueryChange={(newQuery) => onQueryChange(rule.id!, newQuery)}
        />
        
        {canAddMoreConditions && (
          <Button
            variant="transparent"
            color="decaBlue.5"
            px="0"
            fz="sm"
            fw={500}
            leftSection={<IconPlus size={14} />}
            onClick={() => onAddCondition(rule.id!)}
          >
            {t('logic.add_condition')}
          </Button>
        )}
      </Stack>
    </Stack>
  );
};
