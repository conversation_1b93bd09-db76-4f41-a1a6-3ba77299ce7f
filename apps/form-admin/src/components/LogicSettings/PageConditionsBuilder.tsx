import { useLogicFields } from '@/hooks/useLogicFields';
import { ulid } from '@/utils/uuid';
import { Stack } from '@mantine/core';
import { QueryBuilder, type RuleGroupType } from 'react-querybuilder';
import { CustomCombinatorSelector } from './CustomCombinatorSelector';
import { CustomRuleGroup } from './CustomRuleGroup';

interface PageConditionsBuilderProps {
  query: RuleGroupType;
  onQueryChange: (query: RuleGroupType) => void;
}

const PageConditionsBuilder = ({
  query,
  onQueryChange,
}: PageConditionsBuilderProps) => {
  const { availableFields } = useLogicFields();

  return (
    <Stack gap='sm' w='100%'>
      <QueryBuilder
        fields={availableFields}
        query={query}
        onQueryChange={onQueryChange}
        showCombinatorsBetweenRules={true}
        showNotToggle={false}
        addRuleToNewGroups={false}
        idGenerator={ulid}
        controlElements={{
          ruleGroup: CustomRuleGroup,
          combinatorSelector: CustomCombinatorSelector,
          addRuleAction: () => null,
          addGroupAction: () => null,
          removeGroupAction: () => null,
        }}
      />
    </Stack>
  );
};

export default PageConditionsBuilder;
