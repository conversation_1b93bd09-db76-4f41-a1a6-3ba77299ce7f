import { DEFAULT_COMBINATOR, MAX_CONDITIONS } from '@/constants/form-logic';
import { useAppSelector } from '@/store/hooks';
import { selectingSectionSelector } from '@/store/reducers/builder';
import { updateSection } from '@/store/action/builder';
import type { PageLogic, PageLogicRule } from '@/types/form-logic';
import type { FormSection } from '@/types/form-builder';
import { FieldType } from '@/types/form-builder';
import { Button, Stack, Text, Group } from '@mantine/core';
import { IconPlus } from '@tabler/icons-react';
import { debounce } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import type { RuleGroupType } from 'react-querybuilder';
import { useDispatch } from 'react-redux';
import { convertFormLogicToMongoFormat, convertMongoToFormLogic } from '@/utils/form-logic';
import { ulid } from '@/utils/uuid';
import { filterIncompleteRules } from '@/utils/form-logic';
import { useTranslation } from 'react-i18next';
import { ConditionalRulesSection } from './ConditionalRulesSection';
import { PageLogicEmptyState } from './PageLogicEmptyState';
import { NormalRedirectSection } from './NormalRedirectSection';

export const PageLogicSettings = () => {
  const { t } = useTranslation('form_builder');
  const dispatch = useDispatch();
  const selectingSection = useAppSelector(selectingSectionSelector) as FormSection | undefined;
  const [currentPageLogic, setCurrentPageLogic] = useState<PageLogic | null>(null);
  const allPages = useAppSelector((state) => state.builder.builderJson?.content?.filter(page => page.type === FieldType.Section) || []) as FormSection[];
  
  
  useEffect(() => {
    setCurrentPageLogic({
      ...selectingSection?.logics,
      conditionalRules: selectingSection?.logics?.conditionalRules?.map(rule => ({ ...rule, conditions: convertMongoToFormLogic(rule.conditions) })) as PageLogicRule[]
    });

  }, [selectingSection?.id]);


  // Get available pages for redirect options
  const pageOptions = useMemo(() => {
    const currentPageId = selectingSection?.id;
    return allPages
      .filter(page => page.id !== currentPageId)
      .map(page => ({
        value: page.id,
        label: `#${allPages.findIndex(p => p.id === page.id) + 1} ${page.name}` || `Page ${allPages.findIndex(p => p.id === page.id) + 1}`
      }));
  }, [allPages, selectingSection?.id]);


  const convertConditionalRulesToMongoFormat = (conditionalRules: PageLogicRule[]) => {
    return conditionalRules.map(rule => {
      const filteredConditionRules = filterIncompleteRules(rule.conditions);
      const mongoConditionRules = (!filteredConditionRules?.rules || filteredConditionRules.rules.length === 0) 
        ? {} 
        : convertFormLogicToMongoFormat(filteredConditionRules);
      return {
        ...rule,
        conditions: mongoConditionRules
      };
    });
  };

  const convertPageLogicToMongoFormat = (pageLogic: PageLogic) => {
    return {
      ...pageLogic,
      conditionalRules: convertConditionalRulesToMongoFormat(pageLogic.conditionalRules || [])
    };
  };

  const updatePageLogicStore = (updatedLogic: PageLogic) => {
    if (!selectingSection) return;

    
    const updatedSection: FormSection = {
      ...selectingSection,
      logics:  convertPageLogicToMongoFormat(updatedLogic) as PageLogic
    };
    
    dispatch(updateSection(updatedSection));
  };

  const updatePageLogic = (updatedLogic: PageLogic) => {
    updatePageLogicStore(updatedLogic);
    setCurrentPageLogic(updatedLogic);
  };

  const initializePageLogic = () => {
    const newLogic: PageLogic = {
      id: ulid(),
      normalRedirect: {
        targetPageId: null
      },
      conditionalRules: []
    };
    
    updatePageLogic(newLogic);
  };

  const handleNormalRedirectChange = (targetPageId: string | null) => {
    if (!currentPageLogic) return;

    const updatedLogic = {
      ...currentPageLogic,
      normalRedirect: {
        ...currentPageLogic.normalRedirect,
        targetPageId: targetPageId || null
      }
    };

    updatePageLogic(updatedLogic);
  };

  const handleAddRule = () => {
    if (!currentPageLogic) return;

    const newRule: PageLogicRule = {
      id: ulid(),
      action: 'redirect_to_page',
      targetPageId: '',
      conditions: {
        id: ulid(),
        combinator: DEFAULT_COMBINATOR,
        rules: [
          // Create a condition group (which gets the grey box)
          {
            id: ulid(),
            combinator: DEFAULT_COMBINATOR,
            rules: [
              {
                id: ulid(),
                field: '',
                operator: 'is' as const,
                value: '',
              },
            ],
          } as any,
        ],
      },
    };

    const updatedLogic = {
      ...currentPageLogic,
      conditionalRules: [...(currentPageLogic.conditionalRules || []), newRule]
    };

    updatePageLogic(updatedLogic);
  };

  const handleAddConditionToRule = (ruleId: string) => {
    if (!currentPageLogic) return;

    const updatedLogic = {
      ...currentPageLogic,
      conditionalRules: currentPageLogic.conditionalRules?.map(rule => {
        if (rule.id === ruleId) {
          // Create a condition group (which gets the grey box), not just a simple rule
          const newConditionGroup = {
            id: ulid(),
            combinator: DEFAULT_COMBINATOR,
            rules: [
              {
                id: ulid(),
                field: '',
                operator: 'is' as const,
                value: '',
              },
            ],
          };
          return {
            ...rule,
            conditions: {
              ...rule.conditions,
              rules: [...rule.conditions.rules, newConditionGroup as any]
            }
          };
        }
        return rule;
      }) || []
    };

    updatePageLogic(updatedLogic);
  };

  const handleRemoveRule = (ruleId: string) => {
    if (!currentPageLogic) return;

    const updatedLogic = {
      ...currentPageLogic,
      conditionalRules: currentPageLogic.conditionalRules?.filter(rule => rule.id !== ruleId) || []
    };

    updatePageLogic(updatedLogic);
  };

  const handleRuleTargetChange = (ruleId: string, targetPageId: string | null) => {
    if (!currentPageLogic || !targetPageId) return;

    const updatedLogic = {
      ...currentPageLogic,
      conditionalRules: currentPageLogic.conditionalRules?.map(rule => 
        rule.id === ruleId 
          ? { ...rule, targetPageId }
          : rule
      ) || []
    };

    updatePageLogic(updatedLogic);
  };

  const handleRuleQueryChange = (ruleId: string, newQuery: RuleGroupType) => {
    if (!currentPageLogic) return;

    const updatedLogic = {
      ...currentPageLogic,
      conditionalRules: currentPageLogic.conditionalRules?.map(rule => 
        rule.id === ruleId 
          ? { ...rule, conditions: newQuery as any }
          : rule
      ) || []
    };

    setCurrentPageLogic(updatedLogic);
    debounce(() => updatePageLogicStore(updatedLogic), 1000)();
  };

  if (!selectingSection) {
    return (
      <PageLogicEmptyState onInitialize={initializePageLogic} />
    );
  }

  if (!currentPageLogic) {
    return (
      <PageLogicEmptyState onInitialize={initializePageLogic} />
    );
  }

  const canAddMoreRules = !currentPageLogic.conditionalRules || currentPageLogic.conditionalRules.length < MAX_CONDITIONS;
  
  const canAddMoreConditions = (rule: PageLogicRule) => {
    // Count condition groups (each condition group gets a grey box)
    return rule.conditions.rules.length < MAX_CONDITIONS;
  };

  return (
    <Stack w='100%'>
      <Group justify="space-between" align="center">
        <Text fw={500} size="lg">{t('logic.page_logic')}</Text>
        {canAddMoreRules && (
          <Button
            variant="transparent"
            color="decaBlue.5"
            size="sm"
            leftSection={<IconPlus size={16} />}
            onClick={handleAddRule}
          >
            {t('logic.add_rule')}
          </Button>
        )}
      </Group>
      
      <NormalRedirectSection
        currentRedirectPageId={currentPageLogic.normalRedirect?.targetPageId  || ''}
        pageOptions={pageOptions}
        onRedirectChange={handleNormalRedirectChange}
      />

      <ConditionalRulesSection
        rules={currentPageLogic.conditionalRules || []}
        pageOptions={pageOptions}
        canAddMoreConditions={canAddMoreConditions}
        onRemoveRule={handleRemoveRule}
        onRuleTargetChange={handleRuleTargetChange}
        onRuleQueryChange={handleRuleQueryChange}
        onAddConditionToRule={handleAddConditionToRule}
      />
    </Stack>
  );
};
