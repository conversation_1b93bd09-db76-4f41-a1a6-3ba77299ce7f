import { Button, Card, Flex, Image, Stack, Text } from '@mantine/core';
import LogicEmptyImage from '@/assets/images/logic-iframe.svg';
import { useTranslation } from 'react-i18next';

interface PageLogicEmptyStateProps {
  onInitialize: () => void;
}

export const PageLogicEmptyState = ({ onInitialize }: PageLogicEmptyStateProps) => {
  const { t } = useTranslation('form_builder');

  return (
    <Card h='100%'>
      <Flex gap='sm' justify='space-between' direction='column' h='100%'>
        <Text fw={500} size="lg">{t('logic.page_logic')}</Text>

        <Stack gap='sm' align='center' justify='center' flex={1}>
          <Image src={LogicEmptyImage} alt={t('logic.logic_empty_alt')} />
          <Text size="sm" c="dimmed" ta='center'>
            {t('logic.setup_logic_description')}
          </Text>
          <Button
            variant="filled"
            color="decaNavy.5"
            onClick={onInitialize}
            w='100%'
          >
            {t('logic.add_page_logic')}
          </Button>
        </Stack>
      </Flex>
    </Card>
  );
};

