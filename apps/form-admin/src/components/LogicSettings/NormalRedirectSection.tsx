import { Select, Stack, Text } from '@mantine/core';
import { useTranslation } from 'react-i18next';

interface NormalRedirectSectionProps {
  currentRedirectPageId: string;
  pageOptions: Array<{ value: string; label: string }>;
  onRedirectChange: (targetPageId: string | null) => void;
}

const ENDING_CONFIG_VALUE = '__ending_configuration__';

export const NormalRedirectSection = ({ 
  currentRedirectPageId, 
  pageOptions, 
  onRedirectChange,
}: NormalRedirectSectionProps) => {
  const { t } = useTranslation('form_builder');

  const handleSelectChange = (value: string | null) => {
    if (value === ENDING_CONFIG_VALUE) {
      onRedirectChange(ENDING_CONFIG_VALUE);
    } else {
      onRedirectChange(value);
    }
  };

  return (
    <Stack gap="sm">
      <Text fw={500}>{t('logic.normal_condition_go_to')}</Text>
      <Select
        placeholder={t('logic.select_next_page')}
        data={[
          { value: '', label: t('logic.next_page_default') },
          ...pageOptions,
          { value: ENDING_CONFIG_VALUE, label: t('logic.ending_configuration') }
        ]}
        value={currentRedirectPageId || ''}
        onChange={handleSelectChange}
      />
    </Stack>
  );
};
