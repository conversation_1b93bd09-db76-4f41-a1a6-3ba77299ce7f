import { Stack, Text } from '@mantine/core';
import type { RuleGroupType } from 'react-querybuilder';
import type { PageLogicRule } from '@/types/form-logic';
import { PageLogicRuleComponent } from './PageLogicRuleComponent';
import { useTranslation } from 'react-i18next';

interface ConditionalRulesSectionProps {
  rules: PageLogicRule[];
  pageOptions: Array<{ value: string; label: string }>;
  canAddMoreConditions: (rule: PageLogicRule) => boolean;
  onRemoveRule: (ruleId: string) => void;
  onRuleTargetChange: (ruleId: string, targetPageId: string | null) => void;
  onRuleQueryChange: (ruleId: string, newQuery: RuleGroupType) => void;
  onAddConditionToRule: (ruleId: string) => void;
}

export const ConditionalRulesSection = ({
  rules,
  pageOptions,
  canAddMoreConditions,
  onRemoveRule,
  onRuleTargetChange,
  onRuleQueryChange,
  onAddConditionToRule, 
}: ConditionalRulesSectionProps) => {
  const { t } = useTranslation('form_builder');
  const hasRules = rules && rules.length > 0;

  return (
    <Stack gap="md">
      {hasRules ? (
        <Stack gap="md">
          {rules.map((rule, index) => (
            <PageLogicRuleComponent
              key={rule.id}
              rule={rule}
              index={index}
              pageOptions={pageOptions}
              canAddMoreConditions={canAddMoreConditions(rule)}
              onRemove={onRemoveRule}
              onTargetChange={onRuleTargetChange}
              onQueryChange={onRuleQueryChange}
              onAddCondition={onAddConditionToRule}
            />
          ))}
        </Stack>
      ) : (
        <Text size="sm" c="dimmed">
          {t('logic.no_conditional_rules')}
        </Text>
      )}
    </Stack>
  );
};
