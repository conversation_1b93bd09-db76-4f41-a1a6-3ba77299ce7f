import { Card, Stack, Text, Flex, ActionIcon, Divider, rem, Group } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { Handle, Position, type NodeProps } from '@xyflow/react';
import { IconChevronDown, IconChevronRight, IconBrandWalmart, IconDoorExit } from '@tabler/icons-react';
import { useState } from 'react';
import type { FormSection } from '@/types/form-builder';
import { stripHtml } from '@/utils/formResults';
import { useTranslation } from 'react-i18next';

const ENDING_CONFIG_VALUE = '__ending_configuration__';

const useStyles = createStyles((theme) => ({
  pageNode: {
    minWidth: 200,
    maxWidth: 250,
    cursor: 'pointer',
    border: `2px solid ${theme.colors.decaLight[2]}`,
    borderRadius: theme.radius.lg,
    transition: 'all 0.2s ease',
    '&:hover': {
      borderColor: theme.colors.decaNavy[4],
      boxShadow: theme.shadows.lg,
      transform: 'translateY(-2px)',
    },
    '&.selected': {
      borderColor: theme.colors.decaNavy[4],
      boxShadow: theme.shadows.md,
    },
  },
  pageTitle: {
    fontWeight: 600,
    color: theme.colors.decaDark[9],
    textAlign: 'left',
  },
  questionsList: {
    textAlign: 'left',
  },
  questionItem: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.decaGrey[7],
    minHeight: rem(20),  // Fixed height for consistency
    display: 'flex',
    alignItems: 'center',
  },
  pageNumber: {
    fontSize: theme.fontSizes.xs,
    fontWeight: 500,
    color: theme.colors.decaGrey[6],
  },
}));

export interface PageNodeData extends Record<string, unknown> {
  section: FormSection;
  pageNumber: number;
  totalPages: number;
  isSelected: boolean;
  onSelect: (sectionId: string) => void;
}

export const PageNode = ({ data }: NodeProps) => {
  const { t } = useTranslation('form_builder');
  const { classes } = useStyles();
  const { section, pageNumber, totalPages, isSelected, onSelect } = data as PageNodeData;
  const [isQuestionsExpanded, setIsQuestionsExpanded] = useState(true);

  // Get question labels from the section content - include all questions even if empty
  const questionLabels = section.content
    ?.filter((field) => field.type !== 'section' && field.type !== 'hidden')
    .map((field) => {
      if ('label' in field && field.label) {
        const cleanLabel = stripHtml(field.label);
        return cleanLabel.trim() || ''; // Return empty string instead of null for empty labels
      }
      return ''; // Return empty string for fields without labels
    }) || [];

  const handleClick = () => {
    onSelect(section.id);
  };

  const toggleQuestions = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent node selection when clicking toggle
    setIsQuestionsExpanded(!isQuestionsExpanded);
  };

  // Helper function to check if this page has logic
  const hasLogic = () => {
    const logic = section.logics;
    if (!logic) return false;
    
    // Check if there's normal redirect with targetPageId
    const hasNormalLogic = logic.normalRedirect?.targetPageId;
    
    // Check if there are conditional rules
    const hasConditionalRules = logic.conditionalRules && logic.conditionalRules.length > 0;
    
    return hasNormalLogic || hasConditionalRules;
  };

  // Helper function to check if this is an exit page
  const isExitPage = () => {
    const logic = section.logics;
    
    // Check if normal redirect goes to an ending page (null or ENDING_CONFIG_VALUE means ending)
    if (logic?.normalRedirect?.targetPageId === ENDING_CONFIG_VALUE) {
      return true;
    }
    
    // Check if any conditional rules go to ending page (null or ENDING_CONFIG_VALUE means ending)
    if (logic?.conditionalRules) {
      const hasEndingRule = logic.conditionalRules.some(rule => 
        rule.targetPageId === null || rule.targetPageId === ENDING_CONFIG_VALUE
      );
      if (hasEndingRule) return true;
    }
    
    // Check if this is the final page with no logic configured (default flow goes to ending)
    if (pageNumber === totalPages && (!logic || (!logic.normalRedirect?.targetPageId && (!logic.conditionalRules || logic.conditionalRules.length === 0)))) {
      return true;
    }
    
    return false;
  };

  return (
    <>
      <Handle 
        type="target" 
        position={Position.Left} 
        id={Position.Left}
        style={{ background: 'transparent',border: 'none', top: '25px' }} // Fixed position from title area
      />

      <Handle 
        type="target" 
        position={Position.Top} 
        id={Position.Top}
        style={{ background: 'transparent',border: 'none', left: '50%' }} // Fixed position from title area
      />

      <Handle 
        type="target" 
        position={Position.Bottom} 
        id={Position.Bottom}
        style={{ background: 'transparent',border: 'none', left: '50%' }} // Fixed position from title area
      />
      
      <Card
        className={`${classes.pageNode} ${isSelected ? 'selected' : ''}`}
        p={0}
        withBorder
        onClick={handleClick}
      >
        <Stack gap={0}>
          {/* Title Section - Fixed height for consistent connection points */}
          <Stack gap={4} p="md" pb="sm">
            <Group gap={6} align="center" wrap="nowrap">
              {/* Logic Icon - Show when page has logic (normal or conditional) */}
              {hasLogic() && (
                <ActionIcon 
                  size="xs" 
                  variant="light" 
                  color="blue"
                  title={t('logic.page_has_logic_rules')}
                >
                  <IconBrandWalmart size={12} />
                </ActionIcon>
              )}
              
              {/* Exit Page Icon - Show when page leads to ending */}
              {isExitPage() && (
                <ActionIcon 
                  size="xs" 
                  variant="light" 
                  color="orange"
                  title={t('logic.page_leads_to_ending')}
                >
                  <IconDoorExit size={12} />
                </ActionIcon>
              )}
              
              <Text className={classes.pageTitle} size="sm" lineClamp={2} style={{ flex: 1 }}>
                #{pageNumber}: {section.name || t('logic.welcome_page')}
              </Text>
            </Group>
          </Stack>
          
          {/* Separator Line */}
          <Divider />
          
          {/* Questions Section - Always show, collapsible */}
          <Stack gap={0}>
            <Flex 
              align="center" 
              justify="space-between" 
              p="sm" 
              onClick={toggleQuestions}
              style={{ cursor: 'pointer' }}
            >
              <Text size="sm" c="dimmed">
                {t('logic.questions_count', { count: questionLabels?.length || 0 })}
              </Text>
              <ActionIcon size="xs" variant="transparent">
                {isQuestionsExpanded ? <IconChevronDown size={12} /> : <IconChevronRight size={12} />}
              </ActionIcon>
            </Flex>
            
            {isQuestionsExpanded && (
                <Stack gap={rem(8)} p="sm" pt={0} className={classes.questionsList}>
                  {questionLabels && questionLabels.length > 0 ? (
                    questionLabels.map((label, index) => (
                      <Card key={index} withBorder p="xxs">
                        <Text className={classes.questionItem} lineClamp={1} title={label || undefined}>
                          {label || t('logic.untitled_question')}
                        </Text>
                      </Card>
                    ))
                  ) : (
                    <div className={classes.questionItem}>
                      <Text size="xs" c="dimmed" style={{ fontStyle: 'italic' }}>
                        {t('logic.no_questions_yet')}
                      </Text>
                    </div>
                  )}
                </Stack>
            )}
          </Stack>
        </Stack>
      </Card>
      
      <Handle 
        type="source" 
        position={Position.Right} 
        id={Position.Right}
        style={{ background: 'transparent',border: 'none', top: '25px' }} // Fixed position from title area
      />

      <Handle 
        type="source" 
        position={Position.Top} 
        id={Position.Top}
        style={{ background: 'transparent',border: 'none', left: '50%' }} // Fixed position from title area
      />
      
      <Handle 
        type="source" 
        position={Position.Bottom} 
        id={Position.Bottom}
        style={{ background: 'transparent',border: 'none', left: '50%' }} // Fixed position from title area
      />
    </>
  );
};
