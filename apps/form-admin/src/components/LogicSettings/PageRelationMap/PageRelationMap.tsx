import { useMemo, useEffect, useCallback } from 'react';
import {
  ReactFlow,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  type Node,
  type Edge,
  type NodeTypes,
  Position,
  MarkerType,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { Card, Stack, Text, useMantineTheme } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useAppSelector } from '@/store/hooks';
import { selectSectionWithoutHidden } from '@/store/reducers/builder';
import { setActiveSection } from '@/store/action/builder';
import { useDispatch } from 'react-redux';
import { debounce } from 'lodash';
import type { FormSection } from '@/types/form-builder';
import { PageNode } from './PageNode';
import { useTranslation } from 'react-i18next';

const useStyles = createStyles(() => ({
  mapContainer: {
    height: '100%',
    width: '100%',
  },
}));

// Define edge relation types for reference
export enum EdgeRelationType {
  DEFAULT = 'default',     // Default next page relation
  NORMAL = 'normal',       // Normal logic redirect
  CONDITIONAL = 'conditional', // Conditional logic redirect
}

interface PageRelationMapProps {
  onPageSelect?: (sectionId: string) => void;
}

export const PageRelationMap = ({ onPageSelect }: PageRelationMapProps) => {
  const { t } = useTranslation('form_builder');
  const { classes } = useStyles();
  const theme = useMantineTheme();
  const dispatch = useDispatch();
  const sections = useAppSelector(selectSectionWithoutHidden) as FormSection[];
  const selectedSectionId = useAppSelector((state) => state.builder.selectingSectionId);

  // Create nodes from sections - arranged horizontally
  const initialNodes: Node[] = useMemo(() => {
    return sections.map((section, index) => ({
      id: section.id,
      type: 'pageNode',
      position: { x: index * 400, y: 200 }, // Increased spacing and centered Y position
      data: {
        section,
        pageNumber: index + 1,
        isSelected: section.id === selectedSectionId,
        totalPages: sections.length,
        onSelect: (sectionId: string) => {
          dispatch(setActiveSection(sectionId));
          onPageSelect?.(sectionId);
        },
      },
    }));
  }, [sections, selectedSectionId, dispatch, onPageSelect]);

  // Create edges from page logic with better routing to avoid intersections
  const initialEdges: Edge[] = useMemo(() => {
    const edges: Edge[] = [];

    sections.forEach((section, index) => {
      const nextSection = sections[index + 1];
      
      // Default relation (to next page) - only for adjacent pages
      if (nextSection) {
        const hasLogic = section.logics && section.logics.normalRedirect?.targetPageId 
        
        
        if (!hasLogic) {
          edges.push({
            id: `default-${section.id}-${nextSection.id}`,
            source: section.id,
            target: nextSection.id,
            sourceHandle: Position.Right,   
            targetHandle: Position.Left,
            type: 'smoothstep',
            label: t('logic.default_flow'),
            style: { 
              stroke: theme.colors.decaGrey[5], 
              strokeWidth: 2, 
              opacity: 0.7
            },
            markerEnd: {
              type: MarkerType.Arrow,
              width: 16,
              height: 16,
              color: theme.colors.decaGrey[5],
            },
            animated: false,
          });
        }
      }

      // Normal logic relation
      if (section.logics?.normalRedirect?.targetPageId) {
        const targetSection = sections.find(s => s.id === section.logics!.normalRedirect!.targetPageId);
        if (targetSection) {
          
          edges.push({
            id: `normal-${section.id}-${targetSection.id}`,
            source: section.id,
            target: targetSection.id,
            sourceHandle: Position.Bottom,
            targetHandle: Position.Bottom,
            type: 'smoothstep',
            label: t('logic.normal_flow'),
            style: { 
              stroke: theme.colors.green[5], 
              strokeWidth: 2,
            },
            markerEnd: {
              type: MarkerType.Arrow,
              width: 18,
              height: 18,
              color: theme.colors.green[5],
            },
            animated: false,
          });
        }
      }

      // Conditional logic relations with offset routing
      if (section.logics?.conditionalRules) {
        section.logics.conditionalRules.forEach((rule, ruleIndex) => {
          const targetSection = sections.find(s => s.id === rule.targetPageId);
          if (targetSection) {
            
            edges.push({
              id: `conditional-${section.id}-${targetSection.id}-${ruleIndex}`,
              source: section.id,
              target: targetSection.id,
              sourceHandle: Position.Top,
              targetHandle: Position.Top,
              type: 'smoothstep',
              label: t('logic.rule_number', { number: ruleIndex + 1 }),
              style: { 
                stroke: theme.colors.decaBlue[6], 
                strokeWidth: 2,
              },
              markerEnd: {
                type: MarkerType.Arrow,
                width: 18,
                height: 18,
                color: theme.colors.decaBlue[6],
              },
              animated: false,
            });
          }
        });
      }
    });

    return edges;
  }, [sections, theme]);

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  // Create debounced update functions to prevent excessive rerenders
  const debouncedSetNodes = useCallback(
    debounce((newNodes: Node[]) => {
      setNodes(newNodes);
    }, 300),
    [setNodes]
  );

  const debouncedSetEdges = useCallback(
    debounce((newEdges: Edge[]) => {
      setEdges(newEdges);
    }, 300),
    [setEdges]
  );

  // Update nodes when initialNodes changes (when sections or logic changes)
  useEffect(() => {
    debouncedSetNodes(initialNodes);
  }, [initialNodes, debouncedSetNodes]);

  // Update edges when initialEdges changes (when page logic changes)
  useEffect(() => {
    debouncedSetEdges(initialEdges);
  }, [initialEdges, debouncedSetEdges]);

  // Update nodes when selection changes
  useMemo(() => {
    setNodes((nodes) =>
      nodes.map((node) => ({
        ...node,
        data: {
          ...node.data,
          isSelected: node.id === selectedSectionId,
        },
      }))
    );
  }, [selectedSectionId, setNodes]);

  // Define custom node types
  const nodeTypes: NodeTypes = useMemo(() => ({
    pageNode: PageNode,
  }), []);

  if (sections.length === 0) {
    return (
      <Card p="md">
        <Stack align="center">
          <Text size="lg" fw={500}>{t('logic.no_pages_found')}</Text>
          <Text size="sm" c="dimmed">
            {t('logic.create_pages_to_see_map')}
          </Text>
        </Stack>
      </Card>
    );
  }

  return (
    <Card className={classes.mapContainer} p={0}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        nodeTypes={nodeTypes}
        fitView
        fitViewOptions={{
          padding: 0.2,
          includeHiddenNodes: false,
        }}
        attributionPosition="top-right"
        panOnScroll
        selectionOnDrag
        panOnDrag={[1, 2]}
        proOptions={{
          hideAttribution: true,
        }}
      >
        <Background />
        <Controls />
        <MiniMap 
          nodeColor={(node) => {
            if (node.data?.isSelected) return theme.colors.decaBlue[6];
            return theme.colors.decaLight[2];
          }}
          maskColor="rgba(0, 0, 0, 0.1)"
          pannable
          zoomable
        />
      </ReactFlow>
    </Card>
  );
};
