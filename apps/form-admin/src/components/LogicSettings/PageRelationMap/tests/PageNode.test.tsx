import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { MantineProvider } from '@mantine/core';
import { PageNode } from '../PageNode';
import type { FormSection } from '@/types/form-builder';
import { FieldType, FormLayoutType } from '@/types/form-builder';

// Mock ReactFlow Handle component
vi.mock('@xyflow/react', () => ({
  Handle: ({ type, position, id, style }: any) => (
    <div 
      data-testid={`handle-${type}-${position}`}
      data-handle-id={id}
      style={style}
    />
  ),
  Position: {
    Left: 'left',
    Right: 'right',
    Top: 'top',
    Bottom: 'bottom',
  },
}));

// Mock stripHtml utility
vi.mock('@/utils/formResults', () => ({
  stripHtml: (html: string) => html.replace(/<[^>]*>/g, ''),
}));

// Mock <PERSON> emotion
vi.mock('@mantine/emotion', () => ({
  createStyles: () => () => ({
    classes: {
      pageNode: 'page-node',
      pageHeader: 'page-header',
      pageTitle: 'page-title',
      questionsList: 'questions-list',
      questionItem: 'question-item',
      questionLabel: 'question-label',
      logicIcon: 'logic-icon',
      exitIcon: 'exit-icon',
    },
  }),
}));

const mockSection: FormSection = {
  id: 'test-section',
  type: FieldType.Section,
  name: 'Test Section',
  layout: {
    type: FormLayoutType.ImageTop,
    imageUrl: null,
    fieldWidth: '100%',
  },
  content: [
    {
      id: 'field-1',
      type: FieldType.ShortQA,
      label: '<p>Question 1</p>',
      name: 'field-1',
      validators: [],
    },
    {
      id: 'field-2',
      type: FieldType.ShortQA,
      label: '<p>Question 2</p>',
      name: 'field-2',
      validators: [],
    },
    {
      id: 'field-3',
      type: FieldType.Section,
      label: 'Section Field',
      name: 'field-3',
      validators: [],
    },
  ],
  logics: {
    normalRedirect: {
      targetPageId: 'next-section',
    },
    conditionalRules: [
      {
        targetPageId: 'conditional-section',
        action: 'redirect_to_page',
        conditions: {
          combinator: 'and',
          rules: [],
        },
      },
    ],
  },
};

const mockData = {
  section: mockSection,
  pageNumber: 1,
  totalPages: 3,
  isSelected: true,
  onSelect: vi.fn(),
};

const createNodeProps = (data: any) => ({
  id: 'test-node',
  type: 'pageNode',
  selected: false,
  dragging: false,
  zIndex: 1,
  selectable: true,
  deletable: false,
  draggable: true,
  isConnectable: true,
  positionAbsoluteX: 0,
  positionAbsoluteY: 0,
  data,
});

const mockTheme = {
  colors: {
    decaLight: [
      '#f8f9fa',
      '#e9ecef',
      '#dee2e6',
      '#ced4da',
      '#adb5bd',
      '#6c757d',
      '#495057',
      '#343a40',
      '#212529',
      '#000000',
    ] as const,
    decaBlue: [
      '#e6f3ff',
      '#cce7ff',
      '#99cfff',
      '#66b7ff',
      '#339fff',
      '#0066cc',
      '#0052a3',
      '#003d7a',
      '#002952',
      '#001429',
    ] as const,
    green: [
      '#f0f9f0',
      '#e1f3e1',
      '#c3e7c3',
      '#a5dba5',
      '#87cf87',
      '#22c55e',
      '#1b9e4a',
      '#147737',
      '#0d5024',
      '#062911',
    ] as const,
    blue: [
      '#f0f9ff',
      '#e0f2fe',
      '#c1e5fe',
      '#a2d8fd',
      '#83cbfc',
      '#3b82f6',
      '#2f68c5',
      '#234e94',
      '#173463',
      '#0b1a32',
    ] as const,
    red: [
      '#fef2f2',
      '#fde5e5',
      '#fbcbcb',
      '#f9b1b1',
      '#f79797',
      '#ef4444',
      '#bf3636',
      '#8f2828',
      '#5f1a1a',
      '#2f0c0c',
    ] as const,
    decaGrey: [
      '#f9fafb',
      '#f3f4f6',
      '#e5e7eb',
      '#d1d5db',
      '#9ca3af',
      '#6b7280',
      '#4b5563',
      '#374151',
      '#1f2937',
      '#111827',
    ] as const,
  },
  radius: {
    lg: '8px',
  },
  primaryColor: 'blue',
  defaultRadius: 'sm',
};

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <MantineProvider theme={mockTheme}>
      {component}
    </MantineProvider>
  );
};

describe('PageNode', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders page node with correct data', () => {
    renderWithProviders(<PageNode {...createNodeProps(mockData)} />);

    expect(screen.getByText('#1: Test Section')).toBeInTheDocument();
    expect(screen.getByText('logic.questions_count')).toBeInTheDocument();
  });

  it('renders all handles for connections', () => {
    renderWithProviders(<PageNode {...createNodeProps(mockData)} />);

    // Should have 6 handles total: 3 target + 3 source
    expect(screen.getByTestId('handle-target-left')).toBeInTheDocument();
    expect(screen.getByTestId('handle-target-top')).toBeInTheDocument();
    expect(screen.getByTestId('handle-target-bottom')).toBeInTheDocument();
    expect(screen.getByTestId('handle-source-right')).toBeInTheDocument();
    expect(screen.getByTestId('handle-source-top')).toBeInTheDocument();
    expect(screen.getByTestId('handle-source-bottom')).toBeInTheDocument();
  });

  it('shows logic icon when page has logic', () => {
    renderWithProviders(<PageNode {...createNodeProps(mockData)} />);

    expect(screen.getByTitle('logic.page_has_logic_rules')).toBeInTheDocument();
  });

  it('does not show logic icon when page has no logic', () => {
    const sectionWithoutLogic = {
      ...mockSection,
      logics: undefined,
    };

    const dataWithoutLogic = {
      ...mockData,
      section: sectionWithoutLogic,
    };

    renderWithProviders(<PageNode {...createNodeProps(dataWithoutLogic)} />);

    expect(screen.queryByTitle('logic.page_has_logic_rules')).not.toBeInTheDocument();
  });

  it('shows exit page icon when page leads to ending', () => {
    const sectionWithEndingLogic = {
      ...mockSection,
      logics: {
        normalRedirect: {
          targetPageId: '__ending_configuration__',
        },
      },
    };

    const dataWithEnding = {
      ...mockData,
      section: sectionWithEndingLogic,
    };

    renderWithProviders(<PageNode {...createNodeProps(dataWithEnding)} />);

    expect(screen.getByTitle('logic.page_leads_to_ending')).toBeInTheDocument();
  });

  it('shows exit page icon for final page with no logic', () => {
    const finalPageData = {
      ...mockData,
      pageNumber: 3,
      totalPages: 3,
      section: {
        ...mockSection,
        logics: undefined,
      },
    };

    renderWithProviders(<PageNode {...createNodeProps(finalPageData)} />);

    expect(screen.getByTitle('logic.page_leads_to_ending')).toBeInTheDocument();
  });

  it('handles click to select page', () => {
    renderWithProviders(<PageNode {...createNodeProps(mockData)} />);

    const pageCard = screen.getByText('#1: Test Section').closest('.page-node');
    fireEvent.click(pageCard!);

    expect(mockData.onSelect).toHaveBeenCalledWith('test-section');
  });

  it('toggles questions visibility', () => {
    renderWithProviders(<PageNode {...createNodeProps(mockData)} />);

    // Questions should be expanded by default
    expect(screen.getByText('Question 1')).toBeInTheDocument();
    expect(screen.getByText('Question 2')).toBeInTheDocument();

    // Click to collapse
    const toggleButton = screen.getByText('logic.questions_count').closest('div')?.querySelector('button');
    fireEvent.click(toggleButton!);

    // Questions should be hidden
    expect(screen.queryByText('Question 1')).not.toBeInTheDocument();
    expect(screen.queryByText('Question 2')).not.toBeInTheDocument();
  });

  it('shows question labels without HTML tags', () => {
    renderWithProviders(<PageNode {...createNodeProps(mockData)} />);

    expect(screen.getByText('Question 1')).toBeInTheDocument();
    expect(screen.getByText('Question 2')).toBeInTheDocument();
  });

  it('shows untitled question for empty labels', () => {
    const sectionWithEmptyLabels = {
      ...mockSection,
      content: [
        {
          id: 'field-1',
          type: FieldType.ShortQA,
          label: '',
          name: 'field-1',
          validators: [],
        },
      ],
    };

    const dataWithEmptyLabels = {
      ...mockData,
      section: sectionWithEmptyLabels,
    };

    renderWithProviders(<PageNode {...createNodeProps(dataWithEmptyLabels)} />);

    expect(screen.getByText('logic.untitled_question')).toBeInTheDocument();
  });

  it('shows no questions message when no questions exist', () => {
    const sectionWithoutQuestions = {
      ...mockSection,
      content: [
        {
          id: 'field-1',
          type: 'section',
          label: 'Section Field',
        },
      ],
    };

    const dataWithoutQuestions = {
      ...mockData,
      section: sectionWithoutQuestions,
    };

    renderWithProviders(<PageNode {...createNodeProps(dataWithoutQuestions)} />);

    expect(screen.getByText('logic.no_questions_yet')).toBeInTheDocument();
  });

  it('filters out section and hidden fields from questions', () => {
    const sectionWithMixedFields = {
      ...mockSection,
      content: [
        {
          id: 'field-1',
          type: FieldType.ShortQA,
          label: '<p>Question 1</p>',
          name: 'field-1',
          validators: [],
        },
        {
          id: 'field-2',
          type: FieldType.Section,
          label: 'Section Field',
          name: 'field-2',
          validators: [],
        },
        {
          id: 'field-3',
          type: FieldType.Hidden,
          label: 'Hidden Field',
          name: 'field-3',
          validators: [],
        },
        {
          id: 'field-4',
          type: FieldType.ShortQA,
          label: '<p>Question 2</p>',
          name: 'field-4',
          validators: [],
        },
      ],
    };

    const dataWithMixedFields = {
      ...mockData,
      section: sectionWithMixedFields,
    };

    renderWithProviders(<PageNode {...createNodeProps(dataWithMixedFields)} />);

    // Should only show text fields, not section or hidden fields
    expect(screen.getByText('Question 1')).toBeInTheDocument();
    expect(screen.getByText('Question 2')).toBeInTheDocument();
    expect(screen.queryByText('Section Field')).not.toBeInTheDocument();
    expect(screen.queryByText('Hidden Field')).not.toBeInTheDocument();
  });

  it('applies selected styling when isSelected is true', () => {
    renderWithProviders(<PageNode {...createNodeProps(mockData)} />);

    const pageCard = screen.getByText('#1: Test Section').closest('.page-node');
    expect(pageCard).toHaveClass('selected');
  });

  it('does not apply selected styling when isSelected is false', () => {
    const unselectedData = {
      ...mockData,
      isSelected: false,
    };

    renderWithProviders(<PageNode {...createNodeProps(unselectedData)} />);

    const pageCard = screen.getByText('#1: Test Section').closest('.page-node');
    expect(pageCard).not.toHaveClass('selected');
  });

  it('shows welcome page text when section has no name', () => {
    const sectionWithoutName = {
      ...mockSection,
      name: '',
    };

    const dataWithoutName = {
      ...mockData,
      section: sectionWithoutName,
    };

    renderWithProviders(<PageNode {...createNodeProps(dataWithoutName)} />);

    expect(screen.getByText('#1: logic.welcome_page')).toBeInTheDocument();
  });
});
