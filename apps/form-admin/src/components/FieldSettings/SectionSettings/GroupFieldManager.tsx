import { UPDATE_FIELD, ADD_NEW_FIELD, SELECT_NESTED_FIELD } from '@/store/action/actionTypes';
import { type FormField, type GroupFormField } from '@/types/form-builder';
import { Card, Flex, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconChevronRight, IconGripVertical } from '@tabler/icons-react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { useDrag, useDrop } from 'react-dnd';
import { useRef } from 'react';

const useStyles = createStyles(theme => ({
  fieldCard: {
    backgroundColor: theme.white,
    border: `1px solid ${theme.colors.gray[3]}`,
    borderRadius: theme.radius.md,
    padding: rem(12),
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    '&:hover': {
      borderColor: theme.colors.blue[4],
      backgroundColor: theme.colors.blue[0],
    },
  },
  dragHandle: {
    cursor: 'grab',
    color: theme.colors.gray[6],
    '&:active': {
      cursor: 'grabbing',
    },
  },
  fieldName: {
    fontWeight: 600,
    fontSize: rem(14),
    color: theme.colors.gray[8],
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    width: '100%',
  },
  moveOutLink: {
    color: theme.colors.blue[6],
    fontSize: rem(12),
    cursor: 'pointer',
    textDecoration: 'none',
    '&:hover': {
      textDecoration: 'underline',
    },
  },
  chevronIcon: {
    color: theme.colors.gray[5],
    cursor: 'pointer',
  },
  emptyState: {
    textAlign: 'center',
    color: theme.colors.gray[5],
    fontSize: rem(14),
    fontStyle: 'italic',
    padding: rem(20),
  },
}));

interface GroupFieldManagerProps {
  group: GroupFormField;
}

const GROUP_CHANGE_ORDER = 'group-change-order';

const GroupFieldManager: React.FC<GroupFieldManagerProps> = ({ group }) => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const { t } = useTranslation('form_builder');

  const moveCard = (dragFieldId: string, hoverFieldId: string) => {
    if (!dragFieldId || !hoverFieldId || dragFieldId === hoverFieldId) {
      return;
    }

    const newFields = [...group.fields];
    const dragIndex = newFields.findIndex(field => field.id === dragFieldId);
    const hoverIndex = newFields.findIndex(field => field.id === hoverFieldId);

    if (dragIndex === -1 || hoverIndex === -1) {
      return;
    }

    // Swap the two fields
    [newFields[dragIndex], newFields[hoverIndex]] = [newFields[hoverIndex], newFields[dragIndex]];

    dispatch({
      type: UPDATE_FIELD,
      payload: { ...group, fields: newFields },
    });
  };

  const moveFieldOut = (field: FormField) => {
    // Remove field from group by ID
    const newFields = group.fields.filter(f => f.id !== field.id);

    dispatch({
      type: UPDATE_FIELD,
      payload: { ...group, fields: newFields },
    });

    // Add field to main form using the existing addNewField action
    // This will add it to the end of the current section
    dispatch({
      type: ADD_NEW_FIELD,
      payload: { ...field },
    });
  };

  const selectField = (field: FormField) => {
    dispatch({
      type: SELECT_NESTED_FIELD,
      payload: { groupFieldId: group.id, fieldId: field.id },
    });
  };

  if (!group.fields || group.fields.length === 0) {
    return <div className={classes.emptyState}>{t('formField.section.dragFields')}</div>;
  }

  return (
    <Flex direction='column' gap={rem(8)}>
      {group.fields.map(
        (field, index) =>
          field && (
            <DraggableFieldItem
              key={field.id}
              field={field}
              index={index}
              moveCard={moveCard}
              onMoveOut={() => moveFieldOut(field)}
              onSelectField={() => selectField(field)}
            />
          )
      )}
    </Flex>
  );
};

interface DraggableFieldItemProps {
  field: FormField;
  index: number;
  moveCard: (dragFieldId: string, hoverFieldId: string) => void;
  onMoveOut: () => void;
  onSelectField: () => void;
}

const DraggableFieldItem: React.FC<DraggableFieldItemProps> = ({
  field,
  index,
  moveCard,
  onMoveOut,
  onSelectField,
}) => {
  const { classes } = useStyles();
  const ref = useRef<HTMLDivElement>(null);
  const { t } = useTranslation('form_builder');

  const [{ handlerId }, drop] = useDrop({
    accept: [GROUP_CHANGE_ORDER],
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(item: any, monitor) {
      if (!ref.current) {
        return;
      }

      const dragFieldId = item.fieldId;
      const hoverFieldId = field.id;

      // Don't replace items with themselves
      if (dragFieldId === hoverFieldId) {
        return;
      }

      // Determine rectangle on screen
      const hoverBoundingRect = ref.current?.getBoundingClientRect();
      // Get vertical middle
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      // Determine mouse position
      const clientOffset = monitor.getClientOffset() || { x: 0, y: 0 };
      // Get pixels to the top
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;

      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%
      if (item.index < index && hoverClientY < hoverMiddleY) {
        return;
      }
      if (item.index > index && hoverClientY > hoverMiddleY) {
        return;
      }

      // Time to actually perform the action
      moveCard(dragFieldId, hoverFieldId);
      // Note: we're mutating the monitor item here!
      item.index = index;
    },
  });

  const [{ isDragging }, drag] = useDrag({
    type: GROUP_CHANGE_ORDER,
    item: () => ({
      type: GROUP_CHANGE_ORDER,
      index,
      fieldId: field.id,
      field,
    }),
    collect: monitor => ({
      isDragging: monitor.isDragging(),
    }),
  });

  drag(drop(ref));

  const textWithoutHtml = (htmlString: string) => {
    const doc = new DOMParser().parseFromString(htmlString || '', 'text/html');
    return doc.body.textContent || '';
  };

  return (
    <Card
      ref={ref}
      className={classes.fieldCard}
      style={{
        opacity: isDragging ? 0.5 : 1,
      }}
      data-handler-id={handlerId}
    >
      <Flex align='center' justify='space-between'>
        <Flex align='center' gap={rem(8)} style={{ flex: 1, minWidth: 0 }}>
          <div ref={drag} className={classes.dragHandle}>
            <IconGripVertical size={16} />
          </div>

          <Flex direction='column' gap={rem(4)} style={{ flex: 1, minWidth: 0 }}>
            <Text 
              className={classes.fieldName}
              onClick={onSelectField}
              style={{ cursor: 'pointer' }}
            >
              {textWithoutHtml(field.label) || 'Untitled Field'}
            </Text>
            <Text className={classes.moveOutLink} onClick={onMoveOut} w='fit-content' style={{ cursor: 'pointer' }}>
              {t('formField.section.moveOut')}
            </Text>
          </Flex>
        </Flex>

        <IconChevronRight size='16' onClick={onSelectField} />
      </Flex>
    </Card>
  );
};

export default GroupFieldManager;
