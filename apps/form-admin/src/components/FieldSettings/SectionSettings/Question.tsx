import { UPDATE_FIELD } from '@/store/action/actionTypes';
import { selectingFieldSelector } from '@/store/reducers/builder';
import { type GroupFormField } from '@/types/form-builder';
import { Divider, Flex, Group, Text, TextInput, rem } from '@mantine/core';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import GroupFieldManager from './GroupFieldManager';

const SectionSettings = () => {
  const selectingGroup = useSelector(selectingFieldSelector) as GroupFormField;
  const dispatch = useDispatch();
  const { t } = useTranslation('form_builder');

  const onInputLabel = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch({
      type: UPDATE_FIELD,
      payload: { ...selectingGroup, label: e.target.value },
    });
  };

  return (
    <Flex direction='column' gap={rem(16)}>
      <Group>
        <Text fw={500}>{t('formField.section.name')}</Text>
        <TextInput className='w-full' value={selectingGroup?.label} onInput={onInputLabel} />
      </Group>

      <Divider my='xs' style={{ margin: 0 }} />

      <Text fw={500} size='sm'>
        {t('formField.section.fieldsInSection', { count: selectingGroup?.fields?.length || 0 })}
      </Text>

      <GroupFieldManager group={selectingGroup} />
    </Flex>
  );
};

export default SectionSettings;
