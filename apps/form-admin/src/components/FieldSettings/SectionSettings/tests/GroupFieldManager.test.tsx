import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { MantineProvider } from '@mantine/core';
import GroupFieldManager from '../GroupFieldManager';
import { SELECT_NESTED_FIELD } from '@/store/action/actionTypes';
import { moveFieldOutOfGroup } from '@/store/action/builder';
import type { GroupFormField } from '@/types/form-builder';
import { FieldType, GroupFieldType } from '@/types/form-builder';

// Mock the action
vi.mock('@/store/action/builder', () => ({
  moveFieldOutOfGroup: vi.fn(),
}));

// Mock react-dnd
vi.mock('react-dnd', () => ({
  useDrag: () => [
    { isDragging: false },
    vi.fn(),
  ],
  useDrop: () => [
    { handlerId: 'test-handler' },
    vi.fn(),
  ],
}));

// Mock Mantine emotion
vi.mock('@mantine/emotion', () => ({
  createStyles: () => () => ({
    classes: {
      fieldItem: 'field-item',
      fieldLabel: 'field-label',
      dragHandle: 'drag-handle',
      moveOutButton: 'move-out-button',
    },
  }),
}));

const mockGroupField: GroupFormField = {
  id: 'group-1',
  type: GroupFieldType.Section,
  label: '<p>Test Section</p>',
  fields: [
    {
      id: 'field-1',
      type: FieldType.ShortQA,
      label: '<p>Field 1</p>',
      name: 'field-1',
      validators: [],
    },
    {
      id: 'field-2',
      type: FieldType.ShortQA,
      label: '<p>Field 2</p>',
      name: 'field-2',
      validators: [],
    },
    {
      id: 'field-3',
      type: FieldType.ShortQA,
      label: '<p>Field 3</p>',
      name: 'field-3',
      validators: [],
    },
  ],
};

const mockStore = configureStore({
  reducer: {
    builder: () => ({
      selectingField: mockGroupField,
    }),
  },
});

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <Provider store={mockStore}>
      <MantineProvider>
        {component}
      </MantineProvider>
    </Provider>
  );
};

describe('GroupFieldManager', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders empty state when no fields', () => {
    const emptyGroupField = {
      ...mockGroupField,
      fields: [],
    };

    const storeWithEmptyGroup = configureStore({
      reducer: {
        builder: () => ({
          selectingField: emptyGroupField,
        }),
      },
    });

    render(
      <Provider store={storeWithEmptyGroup}>
        <MantineProvider>
          <GroupFieldManager group={emptyGroupField} />
        </MantineProvider>
      </Provider>
    );

    expect(screen.getByText('formField.section.dragFields')).toBeInTheDocument();
  });

  it('renders all fields in the group', () => {
    renderWithProviders(<GroupFieldManager group={mockGroupField} />);

    expect(screen.getByText('Field 1')).toBeInTheDocument();
    expect(screen.getByText('Field 2')).toBeInTheDocument();
    expect(screen.getByText('Field 3')).toBeInTheDocument();
  });

  it('renders field labels without HTML tags', () => {
    renderWithProviders(<GroupFieldManager group={mockGroupField} />);

    expect(screen.getByText('Field 1')).toBeInTheDocument();
    expect(screen.getByText('Field 2')).toBeInTheDocument();
    expect(screen.getByText('Field 3')).toBeInTheDocument();
  });

  it('shows untitled field for empty labels', () => {
    const groupWithEmptyLabels = {
      ...mockGroupField,
      fields: [
        {
          id: 'field-1',
          type: FieldType.ShortQA,
          label: '',
          name: 'field-1',
          validators: [],
        },
      ],
    };

    const storeWithEmptyLabels = configureStore({
      reducer: {
        builder: () => ({
          selectingField: groupWithEmptyLabels,
        }),
      },
    });

    render(
      <Provider store={storeWithEmptyLabels}>
        <MantineProvider>
          <GroupFieldManager group={groupWithEmptyLabels} />
        </MantineProvider>
      </Provider>
    );

    expect(screen.getByText('Untitled Field')).toBeInTheDocument();
  });

  it('handles field selection', () => {
    const dispatch = vi.fn();
    const storeWithDispatch = configureStore({
      reducer: {
        builder: () => ({
          selectingField: mockGroupField,
        }),
      },
      middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
          thunk: false,
        }),
    });

    storeWithDispatch.dispatch = dispatch;

    render(
      <Provider store={storeWithDispatch}>
        <MantineProvider>
          <GroupFieldManager group={mockGroupField} />
        </MantineProvider>
      </Provider>
    );

    const field1 = screen.getByText('Field 1');
    fireEvent.click(field1);

    expect(dispatch).toHaveBeenCalledWith({
      type: SELECT_NESTED_FIELD,
      payload: {
        groupFieldId: 'group-1',
        fieldId: 'field-1',
      },
    });
  });

  it('handles move out action', () => {
    const dispatch = vi.fn();
    const storeWithDispatch = configureStore({
      reducer: {
        builder: () => ({
          selectingField: mockGroupField,
        }),
      },
      middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
          thunk: false,
        }),
    });

    storeWithDispatch.dispatch = dispatch;

    render(
      <Provider store={storeWithDispatch}>
        <MantineProvider>
          <GroupFieldManager group={mockGroupField} />
        </MantineProvider>
      </Provider>
    );

    const moveOutLinks = screen.getAllByText('formField.section.moveOut');
    fireEvent.click(moveOutLinks[0]); // Click the first one

    expect(moveFieldOutOfGroup).toHaveBeenCalledWith({
      groupId: 'group-1',
      fieldId: 'field-1',
    });
  });

  it('handles drag and drop reordering', () => {
    const dispatch = vi.fn();
    const storeWithDispatch = configureStore({
      reducer: {
        builder: () => ({
          selectingField: mockGroupField,
        }),
      },
      middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
          thunk: false,
        }),
    });

    storeWithDispatch.dispatch = dispatch;

    render(
      <Provider store={storeWithDispatch}>
        <MantineProvider>
          <GroupFieldManager group={mockGroupField} />
        </MantineProvider>
      </Provider>
    );

    // The drag and drop functionality is mocked, so we can't easily test the actual reordering
    // But we can verify that the component renders the fields in the correct order
    const fieldElements = screen.getAllByText(/Field \d/);
    expect(fieldElements[0]).toHaveTextContent('Field 1');
    expect(fieldElements[1]).toHaveTextContent('Field 2');
    expect(fieldElements[2]).toHaveTextContent('Field 3');
  });

  it('renders drag handles', () => {
    renderWithProviders(<GroupFieldManager group={mockGroupField} />);

    // Should have drag handles for each field (they are div elements with drag-handle class)
    const dragHandles = screen.getAllByText('Field 1'); // Each field should be rendered
    expect(dragHandles.length).toBeGreaterThan(0);
  });

  it('handles group field with undefined fields', () => {
    const groupWithUndefinedFields = {
      ...mockGroupField,
      fields: [],
    };

    const storeWithUndefinedFields = configureStore({
      reducer: {
        builder: () => ({
          selectingField: groupWithUndefinedFields,
        }),
      },
    });

    render(
      <Provider store={storeWithUndefinedFields}>
        <MantineProvider>
          <GroupFieldManager group={groupWithUndefinedFields} />
        </MantineProvider>
      </Provider>
    );

    expect(screen.getByText('formField.section.dragFields')).toBeInTheDocument();
  });

  it('handles group field with null fields', () => {
    const groupWithNullFields = {
      ...mockGroupField,
      fields: [],
    };

    const storeWithNullFields = configureStore({
      reducer: {
        builder: () => ({
          selectingField: groupWithNullFields,
        }),
      },
    });

    render(
      <Provider store={storeWithNullFields}>
        <MantineProvider>
          <GroupFieldManager group={groupWithNullFields} />
        </MantineProvider>
      </Provider>
    );

    expect(screen.getByText('formField.section.dragFields')).toBeInTheDocument();
  });

  it('filters out null fields from rendering', () => {
    const groupWithNullFields = {
      ...mockGroupField,
      fields: [
        {
          id: 'field-1',
          type: FieldType.ShortQA,
          label: '<p>Field 1</p>',
          name: 'field-1',
          validators: [],
        },
        {
          id: 'field-3',
          type: FieldType.ShortQA,
          label: '<p>Field 3</p>',
          name: 'field-3',
          validators: [],
        },
      ],
    };

    const storeWithNullFields = configureStore({
      reducer: {
        builder: () => ({
          selectingField: groupWithNullFields,
        }),
      },
    });

    render(
      <Provider store={storeWithNullFields}>
        <MantineProvider>
          <GroupFieldManager group={groupWithNullFields} />
        </MantineProvider>
      </Provider>
    );

    expect(screen.getByText('Field 1')).toBeInTheDocument();
    expect(screen.getByText('Field 3')).toBeInTheDocument();
    // Should not render null field
    expect(screen.queryByText('null')).not.toBeInTheDocument();
  });

  it('handles complex field labels with HTML', () => {
    const groupWithComplexLabels = {
      ...mockGroupField,
      fields: [
        {
          id: 'field-1',
          type: FieldType.ShortQA,
          label: '<p><strong>Bold Field</strong> with <em>emphasis</em></p>',
          name: 'field-1',
          validators: [],
        },
        {
          id: 'field-2',
          type: FieldType.ShortQA,
          label: '<div><span>Nested Field</span></div>',
          name: 'field-2',
          validators: [],
        },
      ],
    };

    const storeWithComplexLabels = configureStore({
      reducer: {
        builder: () => ({
          selectingField: groupWithComplexLabels,
        }),
      },
    });

    render(
      <Provider store={storeWithComplexLabels}>
        <MantineProvider>
          <GroupFieldManager group={groupWithComplexLabels} />
        </MantineProvider>
      </Provider>
    );

    expect(screen.getByText('Bold Field with emphasis')).toBeInTheDocument();
    expect(screen.getByText('Nested Field')).toBeInTheDocument();
  });
});
