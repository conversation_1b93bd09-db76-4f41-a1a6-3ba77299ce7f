import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { MantineProvider } from '@mantine/core';
import SectionSettings from '../Question';
import { UPDATE_FIELD } from '@/store/action/actionTypes';
import type { GroupFormField } from '@/types/form-builder';
import { FieldType, GroupFieldType } from '@/types/form-builder';

// Mock the selector
vi.mock('@/store/reducers/builder', () => ({
  selectingFieldSelector: (state: any) => state.builder.selectingField,
}));

// Mock the utility function
vi.mock('@resola-ai/ui/utils/string', () => ({
  cleanBadMarkdownContent: (content: string) => content || '',
}));

// Mock Mantine emotion
vi.mock('@mantine/emotion', () => ({
  createStyles: () => () => ({
    classes: {
      sectionSettings: 'section-settings',
      fieldCount: 'field-count',
      groupFieldManager: 'group-field-manager',
    },
  }),
}));

// Mock react-dnd
vi.mock('react-dnd', () => ({
  useDrag: () => [
    { isDragging: false },
    vi.fn(),
  ],
  useDrop: () => [
    { handlerId: 'test-handler' },
    vi.fn(),
  ],
}));

const mockGroupField: GroupFormField = {
  id: 'group-1',
  type: GroupFieldType.Section,
  label: '<p>Test Section</p>',
  fields: [
    {
      id: 'field-1',
      type: FieldType.ShortQA,
      label: '<p>Field 1</p>',
      name: 'field-1',
      validators: [],
    },
    {
      id: 'field-2',
      type: FieldType.ShortQA,
      label: '<p>Field 2</p>',
      name: 'field-2',
      validators: [],
    },
  ],
};

const mockStore = configureStore({
  reducer: {
    builder: () => ({
      selectingField: mockGroupField,
    }),
  },
});

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <Provider store={mockStore}>
      <MantineProvider>
        {component}
      </MantineProvider>
    </Provider>
  );
};

describe('SectionSettings', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders section name input with current value', () => {
    renderWithProviders(<SectionSettings />);

    const nameInput = screen.getByDisplayValue('<p>Test Section</p>');
    expect(nameInput).toBeInTheDocument();
  });

  it('renders fields count text', () => {
    renderWithProviders(<SectionSettings />);

    expect(screen.getByText('formField.section.fieldsInSection')).toBeInTheDocument();
    // The field count is displayed in the translation text
    expect(screen.getByText('formField.section.fieldsInSection')).toBeInTheDocument();
  });

  it('renders GroupFieldManager component', () => {
    renderWithProviders(<SectionSettings />);

    // GroupFieldManager should be rendered (we can't easily test its content without mocking)
    expect(screen.getByText('formField.section.fieldsInSection')).toBeInTheDocument();
  });

  it('updates field label on input change with valid input', async () => {
    const dispatch = vi.fn();
    const storeWithDispatch = configureStore({
      reducer: {
        builder: () => ({
          selectingField: mockGroupField,
        }),
      },
      middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
          thunk: false,
        }),
    });

    // Mock dispatch
    storeWithDispatch.dispatch = dispatch;

    render(
      <Provider store={storeWithDispatch}>
        <MantineProvider>
          <SectionSettings />
        </MantineProvider>
      </Provider>
    );

    const nameInput = screen.getByDisplayValue('<p>Test Section</p>');
    fireEvent.input(nameInput, { target: { value: 'Updated Section' } });

    await waitFor(() => {
      expect(dispatch).toHaveBeenCalledWith({
        type: UPDATE_FIELD,
        payload: {
          ...mockGroupField,
          label: 'Updated Section',
        },
      });
    });
  });

  it('shows error for invalid input length', async () => {
    renderWithProviders(<SectionSettings />);

    const nameInput = screen.getByDisplayValue('<p>Test Section</p>');
    
    // Test too short
    fireEvent.input(nameInput, { target: { value: 'A' } });
    
    await waitFor(() => {
      // The error message is displayed by Mantine TextInput component
expect(screen.getByRole('textbox')).toHaveAttribute('aria-invalid', 'true');
    });

    // Test too long
    fireEvent.input(nameInput, { target: { value: 'A'.repeat(50) } });
    
    await waitFor(() => {
      // The error message is displayed by Mantine TextInput component
expect(screen.getByRole('textbox')).toHaveAttribute('aria-invalid', 'true');
    });
  });

  it('clears error for valid input', async () => {
    renderWithProviders(<SectionSettings />);

    const nameInput = screen.getByDisplayValue('<p>Test Section</p>');
    
    // First set invalid input
    fireEvent.input(nameInput, { target: { value: 'A' } });
    
    await waitFor(() => {
      // The error message is displayed by Mantine TextInput component
expect(screen.getByRole('textbox')).toHaveAttribute('aria-invalid', 'true');
    });

    // Then set valid input
    fireEvent.input(nameInput, { target: { value: 'Valid Section Name' } });
    
    await waitFor(() => {
      // The error message is not displayed when validation passes
expect(screen.getByRole('textbox')).toHaveAttribute('aria-invalid', 'false');
    });
  });

  it('syncs local value with store value on mount', () => {
    const newGroupField = {
      ...mockGroupField,
      label: '<p>New Section</p>',
    };

    const storeWithNewField = configureStore({
      reducer: {
        builder: () => ({
          selectingField: newGroupField,
        }),
      },
    });

    render(
      <Provider store={storeWithNewField}>
        <MantineProvider>
          <SectionSettings />
        </MantineProvider>
      </Provider>
    );

    const nameInput = screen.getByDisplayValue('<p>New Section</p>');
    expect(nameInput).toBeInTheDocument();
  });

  it('handles empty label gracefully', () => {
    const groupFieldWithEmptyLabel = {
      ...mockGroupField,
      label: '',
    };

    const storeWithEmptyLabel = configureStore({
      reducer: {
        builder: () => ({
          selectingField: groupFieldWithEmptyLabel,
        }),
      },
    });

    render(
      <Provider store={storeWithEmptyLabel}>
        <MantineProvider>
          <SectionSettings />
        </MantineProvider>
      </Provider>
    );

    const nameInput = screen.getByDisplayValue('');
    expect(nameInput).toBeInTheDocument();
  });

  it('handles undefined label gracefully', () => {
    const groupFieldWithUndefinedLabel = {
      ...mockGroupField,
      label: undefined,
    };

    const storeWithUndefinedLabel = configureStore({
      reducer: {
        builder: () => ({
          selectingField: groupFieldWithUndefinedLabel,
        }),
      },
    });

    render(
      <Provider store={storeWithUndefinedLabel}>
        <MantineProvider>
          <SectionSettings />
        </MantineProvider>
      </Provider>
    );

    const nameInput = screen.getByDisplayValue('');
    expect(nameInput).toBeInTheDocument();
  });

  it('shows correct field count', () => {
    const groupFieldWithManyFields = {
      ...mockGroupField,
      fields: new Array(5).fill(null).map((_, index) => ({
        id: `field-${index}`,
        type: 'text',
        label: `<p>Field ${index}</p>`,
      })),
    };

    const storeWithManyFields = configureStore({
      reducer: {
        builder: () => ({
          selectingField: groupFieldWithManyFields,
        }),
      },
    });

    render(
      <Provider store={storeWithManyFields}>
        <MantineProvider>
          <SectionSettings />
        </MantineProvider>
      </Provider>
    );

    // The field count is displayed in the translation text
    expect(screen.getByText('formField.section.fieldsInSection')).toBeInTheDocument();
  });

  it('handles group field with no fields', () => {
    const groupFieldWithNoFields = {
      ...mockGroupField,
      fields: [],
    };

    const storeWithNoFields = configureStore({
      reducer: {
        builder: () => ({
          selectingField: groupFieldWithNoFields,
        }),
      },
    });

    render(
      <Provider store={storeWithNoFields}>
        <MantineProvider>
          <SectionSettings />
        </MantineProvider>
      </Provider>
    );

    // The field count is displayed in the translation text
    expect(screen.getByText('formField.section.fieldsInSection')).toBeInTheDocument();
  });

  it('handles group field with undefined fields', () => {
    const groupFieldWithUndefinedFields = {
      ...mockGroupField,
      fields: undefined,
    };

    const storeWithUndefinedFields = configureStore({
      reducer: {
        builder: () => ({
          selectingField: groupFieldWithUndefinedFields,
        }),
      },
    });

    render(
      <Provider store={storeWithUndefinedFields}>
        <MantineProvider>
          <SectionSettings />
        </MantineProvider>
      </Provider>
    );

    // The field count is displayed in the translation text
    expect(screen.getByText('formField.section.fieldsInSection')).toBeInTheDocument();
  });

  it('validates input length correctly', () => {
    renderWithProviders(<SectionSettings />);

    const nameInput = screen.getByDisplayValue('<p>Test Section</p>');

    // Test minimum length (2 characters)
    fireEvent.input(nameInput, { target: { value: 'AB' } });
    // The error message is not displayed when validation passes
expect(screen.getByRole('textbox')).toHaveAttribute('aria-invalid', 'false');

    // Test maximum length (40 characters)
    fireEvent.input(nameInput, { target: { value: 'A'.repeat(40) } });
    // The error message is not displayed when validation passes
expect(screen.getByRole('textbox')).toHaveAttribute('aria-invalid', 'false');

    // Test just under minimum
    fireEvent.input(nameInput, { target: { value: 'A' } });
    // The error message is displayed by Mantine TextInput component
expect(screen.getByRole('textbox')).toHaveAttribute('aria-invalid', 'true');

    // Test just over maximum
    fireEvent.input(nameInput, { target: { value: 'A'.repeat(41) } });
    // The error message is displayed by Mantine TextInput component
expect(screen.getByRole('textbox')).toHaveAttribute('aria-invalid', 'true');
  });
});
