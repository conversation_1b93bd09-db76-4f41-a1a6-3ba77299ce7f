import { selectingFieldSelector, selectingGroupFieldSelector } from '@/store/reducers/builder';
import { FieldType, GroupFieldType } from '@/types';
import { useSelector } from 'react-redux';
import { Flex, Text, rem } from '@mantine/core';
import { AddressSettings } from './AddressSettings';
import { CheckboxQuestionSettings } from './Checkbox';
import { CheckboxesQuestionSettings } from './Checkboxes';
import { DateSelectorQuestionSettings } from './DateSelector';
import { DateTimeFieldQuestionSettings } from './DateTimeField';
import { DropdownQuestionSettings } from './Dropdown';
import { FileUploaderQuestionSettings } from './FileUploader';
import { HeadingQuestionSettings } from './Heading';
import { LegalQuestionSettings } from './Legal';
import { MultipleChoiceQuestionSettings } from './MultipleChoice';
import { NameSettings } from './NameSettings';
import { OpinionScaleQuestionSettings } from './OpinionScale';
import { ParagraphQuestionSettings } from './Paragraph';
import { PictureChoiceQuestionSettings } from './PictureChoice';
import { RatingQuestionSettings } from './Rating';
import { SectionSettings } from './SectionSettings';
import { TextFieldQuestionSettings } from './TextField';
import { YesNoQuestionSettings } from './YesNo';

const ComponentMap = {
  [FieldType.Email]: TextFieldQuestionSettings,
  [FieldType.PhoneNumber]: TextFieldQuestionSettings,
  [FieldType.ShortQA]: TextFieldQuestionSettings,
  [FieldType.LongQA]: TextFieldQuestionSettings,
  [FieldType.Dropdown]: DropdownQuestionSettings,
  [FieldType.Paragraph]: ParagraphQuestionSettings,
  [FieldType.Heading]: HeadingQuestionSettings,
  [FieldType.Checkboxes]: CheckboxesQuestionSettings,
  [FieldType.MultipleChoice]: MultipleChoiceQuestionSettings,
  [FieldType.PictureChoice]: PictureChoiceQuestionSettings,
  [FieldType.Legal]: LegalQuestionSettings,
  [FieldType.YesNo]: YesNoQuestionSettings,
  [FieldType.Checkbox]: CheckboxQuestionSettings,
  [FieldType.Website]: TextFieldQuestionSettings,
  [FieldType.PostCode]: TextFieldQuestionSettings,
  [FieldType.Date]: DateTimeFieldQuestionSettings,
  [FieldType.Time]: DateTimeFieldQuestionSettings,
  [FieldType.DateTime]: DateTimeFieldQuestionSettings,
  [FieldType.DateRange]: DateTimeFieldQuestionSettings,
  [FieldType.DateSelector]: DateSelectorQuestionSettings,
  [FieldType.OpinionScale]: OpinionScaleQuestionSettings,
  [FieldType.Rating]: RatingQuestionSettings,
  [FieldType.FileUploader]: FileUploaderQuestionSettings,
  // Group
  [GroupFieldType.Name]: NameSettings,
  [GroupFieldType.Address]: AddressSettings,
  [GroupFieldType.DateTimeRange]: DateTimeFieldQuestionSettings,
  [GroupFieldType.Section]: SectionSettings,
};

const QuestionSettings = () => {
  const selectingField = useSelector(selectingFieldSelector);
  const selectingGroupField = useSelector(selectingGroupFieldSelector);


  const Component = ComponentMap[selectingField?.type];

  if (!Component) {
    return null;
  }


  return (
    <div>
      {selectingGroupField && (
        <Flex align="center" gap={rem(8)} mb={rem(16)}>
          <Text size="sm" c="dimmed">
            {selectingGroupField.label}
          </Text>
        </Flex>
      )}
      <Component />
    </div>
  );
};

export default QuestionSettings;
