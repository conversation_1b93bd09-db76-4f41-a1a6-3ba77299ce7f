import { selectField } from '@/store/action/builder';
import { Flex, Tabs, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton } from '@resola-ai/ui';
import clsx from 'clsx';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { selectingGroupFieldSelector } from '@/store/reducers/builder';
import { useDispatch, useSelector } from 'react-redux';
import QuestionSettings from './QuestionSettings';
import { SELECT_FIELD } from '@/store/action/actionTypes';

const useStyles = createStyles((theme) => ({
  sectionTitle: {
    fontWeight: 500,
    marginBottom: rem(16),
    marginTop: rem(20),
  },
  activeTab: {
    color: theme.colors.decaNavy[4],
    borderWidth: 2,
  },
  contentContainer: {
    padding: `${rem(20)} ${rem(0)}`,
  },
  textFieldSetting: {
    fontSize: rem(14),
  },
  tab: {
    fontWeight: 500,
  },
  tabList: {
    margin: `0 ${rem(-16)}`,
    padding: `0 ${rem(16)}`,
  },
}));

const tabs = [{ label: 'question', value: 'fields', component: <QuestionSettings /> }];

const FieldSettings = () => {
  const { t } = useTranslation('form_builder');
  const { classes } = useStyles();
  const [activeTab, setActiveTab] = useState<string | null>('fields');
  const dispatch = useDispatch();
  const selectingGroupField = useSelector(selectingGroupFieldSelector);

  const handleBackClick = () => {
    if (selectingGroupField) {
      dispatch({
        type: SELECT_FIELD,
        payload: selectingGroupField.id,
      });
      
      return;
    }

    dispatch(selectField(null));
  };
  return (
    <div className={clsx(classes.textFieldSetting, 'w-full h-full')}>
      <Flex align='center' justify='space-between'>
        <Text className={classes.sectionTitle}>{t('editField')}</Text>

        <DecaButton fz='sm' variant='secondary_text' p={rem(8)} onClick={handleBackClick}>
          {t('common.back')}
        </DecaButton>
      </Flex>
      <Tabs variant='outline' defaultValue='gallery' onChange={setActiveTab} value={activeTab}>
        <Tabs.List className={classes.tabList}>
          {tabs.map((tab) => (
            <Tabs.Tab
              key={tab.value}
              value={tab.value}
              className={clsx(classes.tab, activeTab === tab.value && classes.activeTab)}
            >
              {t(tab.label)}
            </Tabs.Tab>
          ))}
        </Tabs.List>

        {tabs.map((tab) => (
          <Tabs.Panel key={tab.value} value={tab.value} className={classes.contentContainer}>
            {tab.component}
          </Tabs.Panel>
        ))}
      </Tabs>
    </div>
  );
};

export default FieldSettings;
