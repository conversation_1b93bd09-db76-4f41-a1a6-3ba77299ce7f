import { useFormContext } from '@/contexts';
import { Box, Flex, Menu, Text, TextInput, rem, useMantineTheme } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton } from '@resola-ai/ui';
import { IconPlus, IconRefresh } from '@tabler/icons-react';
import { useTranslation } from 'react-i18next';
import { Mention, MentionsInput } from 'react-mentions';
import { Label } from '../Label';
import FormSettingsLayout from '../Layout';

const useStyles = createStyles((theme) => ({
  textInput: {
    flexGrow: 1,
  },
  characterCount: {
    flexGrow: 0,
    flexShrink: 0,
    marginLeft: rem(30),
    marginRight: rem(60),
  },
  inputWithVariable: {
    flexGrow: 1,
    position: 'relative',
    minWidth: 0,
    input: {
      border: `0.0625rem solid ${theme.colors.decaGray[3]}`,
      borderRadius: '0.25rem',
      minHeight: '2.20rem',
      lineHeight: '2.20rem',
      paddingLeft: 'calc(2.25rem / 3)',
      paddingRight: '2.25rem',
      fontSize: '0.75rem !important',
      fontFamily: theme.fontFamily,
      fontSmoothing: theme.fontSmoothing,
      '&:focus': {
        border: `0.0625rem solid ${theme.colors.decaNavy[3]}`,
      },
      '&.is-error__input': {
        border: '0.0625rem solid #fa5252 !important',
      },
    },
  },
  inputWithVariableError: {
    wordBreak: 'break-word',
    color: '#fa5252',
    fontSize: 'calc(0.75rem - 0.125rem)',
    lineHeight: 1.2,
    display: 'block',
    marginTop: 'calc(0.5rem / 2)',
  },
  addVariable: {
    position: 'absolute',
    top: '0.5rem',
    right: '1rem',
    '&[data-disabled=false]': {
      cursor: 'pointer',
    },
  },
}));

const SystemMessage = () => {
  const { t, i18n } = useTranslation('form_settings');
  const { classes } = useStyles();
  const theme = useMantineTheme();
  const form = useFormContext();

  const templateVariable = {
    startAt: {
      id: 'start_at',
      display: t('startAtVariable'),
    },
  };

  const handleAddVariable = (fieldName, variable) => {
    const currentValue = form.getInputProps(fieldName)?.value || '';
    form.setFieldValue(fieldName, `${currentValue}@[${variable.display}](${variable.id})`);
  };

  const handleChangeMentionInput = (e) => {
    let value = e?.target?.value || '';
    // Remove duplicate variable
    const variableStr = `@[${templateVariable.startAt.display}](${templateVariable.startAt.id})`;
    const splits = value.split(variableStr, 2);
    if (splits.length > 1) {
      value = splits[0] + variableStr + splits[1].replaceAll(variableStr, '');
    }
    // Truncate string
    value = value.slice(0, 200);
    form.setFieldValue(`setting.systemMessage.1.heading.${i18n.language}`, value);
    if (!value?.trim()) {
      form.setFieldError(
        `setting.systemMessage.1.heading.${i18n.language}`,
        t('requiredError', { ns: 'common' })
      );
    } else {
      form.clearFieldError(`setting.systemMessage.1.heading.${i18n.language}`);
    }
  };

  const handleResetClick = () => {
    form.setFieldValue(
      `setting.systemMessage.0.heading.${i18n.language}`,
      t('defaultDisplayIssueHeadingMsg')
    );
    form.setFieldValue(
      `setting.systemMessage.0.body.${i18n.language}`,
      t('defaultDisplayIssueBodyMsg')
    );
    form.setFieldValue(
      `setting.systemMessage.1.heading.${i18n.language}`,
      t('defaultNotOpenHeadingMsg', {
        startAt: `@[${templateVariable.startAt.display}](${templateVariable.startAt.id})`,
      })
    );
    form.setFieldValue(`setting.systemMessage.1.body.${i18n.language}`, t('defaultNotOpenBodyMsg'));
    form.setFieldValue(
      `setting.systemMessage.2.heading.${i18n.language}`,
      t('defaultLimitReachedHeadingMsg')
    );
    form.setFieldValue(
      `setting.systemMessage.2.body.${i18n.language}`,
      t('defaultLimitReachedBodyMsg')
    );
  };

  return (
    <FormSettingsLayout
      title={t('systemMessageLabel')}
      titleButton={
        <DecaButton
          variant='neutral'
          radius='xl'
          fz='sm'
          leftSection={<IconRefresh size={20} />}
          onClick={handleResetClick}
        >
          {t('resetToDefault')}
        </DecaButton>
      }
    >
      <Box component='div'>
        <Flex direction='column'>
          <Text tt='uppercase' mb={rem(28)} fw={500} fz={rem(12)} c='decaNavy.4'>
            {t('systemMessageTitle')}
          </Text>
          <Label text={t('displayIssueDesc')} mb={rem(12)} />
          <Flex justify='center' align='center'>
            <TextInput
              className={classes.textInput}
              label={
                <Text fz={rem(12)} c='decaGrey.6'>
                  {t('headingLabel')}
                </Text>
              }
              mb={rem(20)}
              maxLength={200}
              {...form.getInputProps(`setting.systemMessage.0.heading.${i18n.language}`)}
            />
            <Text className={classes.characterCount} fz={rem(12)}>
              {t('characterCount', {
                current:
                  form.getInputProps(`setting.systemMessage.0.heading.${i18n.language}`).value
                    ?.length || 0,
                max: 200,
              })}
            </Text>
          </Flex>

          <Flex justify='center' align='center'>
            <TextInput
              className={classes.textInput}
              label={
                <Text fz={rem(12)} c='decaGrey.6'>
                  {t('bodyLabel')}
                </Text>
              }
              mb={rem(20)}
              maxLength={200}
              {...form.getInputProps(`setting.systemMessage.0.body.${i18n.language}`)}
            />
            <Text className={classes.characterCount} fz={rem(12)}>
              {t('characterCount', {
                current:
                  form.getInputProps(`setting.systemMessage.0.body.${i18n.language}`).value
                    ?.length || 0,
                max: 200,
              })}
            </Text>
          </Flex>

          <Label text={t('notOpenDesc')} mb={rem(12)} />
          <Text fz={rem(12)} c='decaGrey.6'>
            {t('headingLabel')}
          </Text>
          <Flex justify='center' align='center' style={{ marginBottom: '1.25rem' }}>
            <div className={classes.inputWithVariable}>
              <MentionsInput
                singleLine
                value={
                  form.getInputProps(`setting.systemMessage.1.heading.${i18n.language}`)?.value ||
                  ''
                }
                style={{ lineHeight: '36px', fontSize: '0.75rem' }}
                className={`${form.getInputProps(`setting.systemMessage.1.heading.${i18n.language}`)?.error ? 'is-error' : ''}`}
                onChange={handleChangeMentionInput}
              >
                <Mention
                  data={[templateVariable.startAt]}
                  style={{
                    backgroundColor: theme.colors.decaBlue[0],
                    padding: '2px',
                    borderRadius: '4px',
                    marginLeft: '11px',
                  }}
                />
              </MentionsInput>

              <div
                className={classes.addVariable}
                data-disabled={form
                  .getInputProps(`setting.systemMessage.1.heading.${i18n.language}`)
                  ?.value?.includes(templateVariable.startAt.id)}
              >
                {form
                  .getInputProps(`setting.systemMessage.1.heading.${i18n.language}`)
                  ?.value?.includes(templateVariable.startAt.id) ? (
                  <IconPlus color={theme.colors.decaNavy[3]} size={15} />
                ) : (
                  <Menu shadow='md' width={170} position='bottom-end'>
                    <Menu.Target>
                      <IconPlus color={theme.colors.decaNavy[3]} size={15} />
                    </Menu.Target>
                    <Menu.Dropdown>
                      <Menu.Item
                        onClick={() =>
                          handleAddVariable(
                            `setting.systemMessage.1.heading.${i18n.language}`,
                            templateVariable.startAt
                          )
                        }
                      >
                        {templateVariable.startAt.display}
                      </Menu.Item>
                    </Menu.Dropdown>
                  </Menu>
                )}
              </div>
              {!!form.getInputProps(`setting.systemMessage.1.heading.${i18n.language}`)?.error && (
                <div className={classes.inputWithVariableError}>
                  {form.getInputProps(`setting.systemMessage.1.heading.${i18n.language}`)?.error}
                </div>
              )}
            </div>
            <Text className={classes.characterCount} fz={rem(12)}>
              {t('characterCount', {
                current:
                  form.getInputProps(`setting.systemMessage.1.heading.${i18n.language}`).value
                    ?.length || 0,
                max: 200,
              })}
            </Text>
          </Flex>

          <Flex justify='center' align='center'>
            <TextInput
              className={classes.textInput}
              label={
                <Text fz={rem(12)} c='decaGrey.6'>
                  {t('bodyLabel')}
                </Text>
              }
              mb={rem(20)}
              maxLength={200}
              {...form.getInputProps(`setting.systemMessage.1.body.${i18n.language}`)}
            />
            <Text className={classes.characterCount} fz={rem(12)}>
              {t('characterCount', {
                current:
                  form.getInputProps(`setting.systemMessage.1.body.${i18n.language}`).value
                    ?.length || 0,
                max: 200,
              })}
            </Text>
          </Flex>

          <Label text={t('limitReachedDesc')} mb={rem(12)} />
          <Flex justify='center' align='center'>
            <TextInput
              className={classes.textInput}
              label={
                <Text fz={rem(12)} c='decaGrey.6'>
                  {t('headingLabel')}
                </Text>
              }
              mb={rem(20)}
              maxLength={200}
              {...form.getInputProps(`setting.systemMessage.2.heading.${i18n.language}`)}
            />
            <Text className={classes.characterCount} fz={rem(12)}>
              {t('characterCount', {
                current:
                  form.getInputProps(`setting.systemMessage.2.heading.${i18n.language}`).value
                    ?.length || 0,
                max: 200,
              })}
            </Text>
          </Flex>

          <Flex justify='center' align='center'>
            <TextInput
              className={classes.textInput}
              label={
                <Text fz={rem(12)} c='decaGrey.6'>
                  {t('bodyLabel')}
                </Text>
              }
              mb={rem(20)}
              maxLength={200}
              {...form.getInputProps(`setting.systemMessage.2.body.${i18n.language}`)}
            />
            <Text className={classes.characterCount} fz={rem(12)}>
              {t('characterCount', {
                current:
                  form.getInputProps(`setting.systemMessage.2.body.${i18n.language}`).value
                    ?.length || 0,
                max: 200,
              })}
            </Text>
          </Flex>
        </Flex>
      </Box>
    </FormSettingsLayout>
  );
};

export default SystemMessage;
