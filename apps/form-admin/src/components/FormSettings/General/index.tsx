import { useFormContext } from '@/contexts';
import type { FormData } from '@/types';
import {
  Box,
  type ComboboxItem,
  Flex,
  NumberInput,
  Paper,
  Switch,
  TagsInput,
  Text,
  TextInput,
  rem,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Label } from '../Label';
import FormSettingsLayout from '../Layout';
import DateTimeInput from './DateTimeInput';

const MAX_SUBMISSION_LIMIT = 92233720368547760;

const useStyles = createStyles((theme) => ({
  toggle: {
    justifyContent: 'space-between',
    '> label': {
      cursor: 'pointer',
    },
  },
  track: {
    cursor: 'pointer',
  },
  iconCalendar: {
    width: rem(18),
    height: rem(18),
  },
  iconCalendarError: {
    width: rem(18),
    height: rem(18),
    color: theme.colors.red[6],
  },
  selectedTags: {
    backgroundColor: theme.colors.decaBlue[0],
    color: theme.colors.decaBlue[9],
    borderRadius: rem(16),
    display: 'flex',
    alignItems: 'center',
    '.mantine-MultiSelect-defaultValueRemove': {
      color: theme.colors.decaBlue[9],
    },
  },
  settingsBox: {
    border: `1px solid ${theme.colors.decaLight[2]}`,
    display: 'flex',
    '.mantine-InputWrapper-error': {
      position: 'absolute',
    },
  },
}));

const General = () => {
  const { t } = useTranslation('form_settings');
  const { classes } = useStyles();
  const [tagsData, setTagsData] = useState<ComboboxItem[]>([]);
  const form = useFormContext();
  const formValues = form.values as FormData;

  useEffect(() => {
    if (formValues.tags) {
      const tags = formValues.tags.map((tag) => ({ value: tag, label: tag }));
      setTagsData(tags);
    }
  }, [formValues.tags]);

  return (
    <FormSettingsLayout title={t('generalLabel')}>
      <Box component='div'>
        <Text tt='uppercase' my={rem(28)} fw={500} fz={rem(12)} c='decaNavy.4'>
          {t('formInformationTitle')}
        </Text>

        <TextInput
          label={<Label text={t('formNameLabel')} />}
          placeholder={t('formNamePlaceholder')}
          mb={rem(20)}
          {...form.getInputProps('name')}
        />

        <TagsInput
          data={tagsData}
          mb={rem(20)}
          label={<Label text={t('tags')} />}
          clearable
          classNames={{ pill: classes.selectedTags }}
          {...form.getInputProps('tags')}
        />

        <Text fw={500} mb={rem(12)}>
          {t('formValidityPeriod')}
        </Text>
        <Flex mt={rem(12)} gap={rem(20)}>
          <DateTimeInput
            inputName='startAt'
            label={t('startDateTimeLabel')}
            error={form?.errors?.startAt}
          />
          <DateTimeInput
            inputName='expiredAt'
            label={t('endDateTimeLabel')}
            minDate={
              formValues.startAt ? dayjs(formValues.startAt).add(1, 'hour').toDate() : new Date()
            }
          />
        </Flex>
      </Box>

      <Box component='div'>
        <Text tt='uppercase' my={rem(28)} fw={500} fz={rem(12)} c='decaNavy.4'>
          {t('access')}
        </Text>

        {/* TODO: Uncomment this code block for this release

        <Paper radius='md' p='md' withBorder mb={rem(20)} className={classes.settingsBox}>
          <Flex direction='column' className='flex-1'>
            <Label text=' Close form' mb={rem(4)} />
            <Text fz={rem(12)} c='decaGrey.6'>
              Close your form to new submissions.
            </Text>
          </Flex>
          <Switch
            classNames={{ body: classes.toggle }}
            size='xs'
            color='lime'
            labelPosition='left'
            {...form.getInputProps('status')}
            defaultChecked={formValues.status === FormStatus.Closed}
            onChange={event => {
              form.setFieldValue(
                'status',
                event.currentTarget.checked ? FormStatus.Closed : FormStatus.Draft
              );
            }}
          />
        </Paper> */}

        <Paper radius='md' p='md' withBorder className={classes.settingsBox} mb={rem(20)}>
          <Flex direction='column' className='flex-1'>
            <Label text={t('formSubmissionLimitLabel')} mb={rem(4)} />
            <Text fz={rem(12)} c='decaGrey.6'>
              {t('formSubmissionLimitDesc')}
            </Text>
            {formValues.setting.submission.limitResponse && (
              <Flex align='flex-end'>
                <NumberInput
                  w={rem(240)}
                  {...form.getInputProps('setting.submission.limitNumber')}
                  fz={rem(14)}
                  min={0}
                  max={MAX_SUBMISSION_LIMIT}
                  mt={rem(8)}
                />
                <Text ml={rem(12)} mb={rem(8)}>
                  {t('submissionsLabel')}
                </Text>
              </Flex>
            )}
          </Flex>
          <Switch
            classNames={{ body: classes.toggle }}
            size='xs'
            color='lime'
            labelPosition='left'
            {...form.getInputProps('setting.submission.limitResponse')}
          />
        </Paper>

        <Paper radius='md' p='md' withBorder className={classes.settingsBox}>
          <Flex direction='column' className='flex-1'>
            <Label text={t('formSubmissionConfirmationLabel')} mb={rem(4)} />
            <Text fz={rem(12)} c='decaGrey.6'>
              {t('formSubmissionConfirmationDesc')}
            </Text>
          </Flex>
          <Switch
            classNames={{ body: classes.toggle }}
            size='xs'
            color='lime'
            labelPosition='left'
            checked={formValues.setting.submission.enableConfirmationScreen}
            onChange={(event) => {
              form.setFieldValue(
                'setting.submission.enableConfirmationScreen',
                event.currentTarget.checked
              );
            }}
          />
        </Paper>
      </Box>
    </FormSettingsLayout>
  );
};

export default General;
