import { setIsDragging } from '@/store/action/builder';
import { Flex, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconGripVertical, IconTrash } from '@tabler/icons-react';
import clsx from 'clsx';
import { useEffect, useRef } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { getEmptyImage } from 'react-dnd-html5-backend';
import { useDispatch } from 'react-redux';

const useStyles = createStyles((theme) => ({
  FieldDraggableItem: {
    position: 'relative',
    '&:hover': {
      '.drag-container': {
        opacity: 1,
      },
    },
  },
  dragContainer: {
    display: 'flex',
    width: '100%',
    opacity: 1,
    padding: `${rem(8)} ${rem(4)}`,
    '&:hover, &.isActive': {
      outline: `1px solid ${theme.colors.decaNavy[4]}`,
      borderRadius: theme.radius.md,
      '.drag-icon': {
        opacity: 1,
      },
      '.item-actions': {
        opacity: 1,
      },
    },
    '&.undraggable:hover': {
      outline: 'none',
    },
    '.drag-icon, .item-actions': {
      opacity: 0,
    },
  },
  dragTarget: {
    color: theme.colors.decaLight[4],
    cursor: 'move',
  },
}));

interface DraggableItemProps {
  field: any;
  index: number;
  moveCard: (dragIndex: number, hoverIndex: number) => void;
  children: React.ReactNode;
  isActive: boolean;
  showRemove?: boolean;
  onMouseDown?: () => void;
  onRemove?: () => void;
  draggable?: boolean;
}

const DraggableItem = ({
  field,
  index,
  moveCard,
  children,
  showRemove = true,
  onMouseDown,
  onRemove,
  draggable = true,
}: DraggableItemProps) => {
  const ref = useRef<HTMLDivElement>(null);
  const { classes } = useStyles();
  const dispatch = useDispatch();

  const [{ handlerId }, drop] = useDrop({
    accept: 'FIELD_DRAG_CHANGE_ORDER',
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(items: any, monitor) {
      if (!ref.current) {
        return;
      }

      const item = items.data[0];

      const dragIndex = item.index;
      const hoverIndex = index;
      if (dragIndex === hoverIndex) {
        return;
      }
      const hoverBoundingRect = ref.current?.getBoundingClientRect();
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset() || { x: 0, y: 0 };
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }
      // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }
      moveCard(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
  });

  const [{ isDragging }, drag, preview] = useDrag({
    type: 'FIELD_DRAG_CHANGE_ORDER',
    item: () => ({
      data: [{ ...field, index, style: { ...field.style } }],
      width: ref.current?.clientWidth,
    }),
    end: () => {
      dispatch(setIsDragging(false));
    },
    collect: (monitor) => {
      if (monitor.isDragging()) {
        dispatch(setIsDragging(true));
      }
      return {
        isDragging: monitor.isDragging(),
      };
    },
  });

  useEffect(() => {
    preview(getEmptyImage(), { captureDraggingState: false });
  }, [preview]);

  draggable && drag(drop(ref));

  return (
    <div
      className={classes.FieldDraggableItem}
      ref={ref}
      style={{
        width: '100%',
        opacity: isDragging ? 0 : 1,
        marginBottom: rem(4),
      }}
      onMouseDown={() => onMouseDown && onMouseDown()}
      data-handler-id={handlerId}
    >
      <Flex
        align='center'
        className={clsx(classes.dragContainer, !draggable && 'undraggable')}
        gap={rem(4)}
      >
        {draggable && (
          <div>
            <IconGripVertical size={16} className={clsx(classes.dragTarget, 'drag-icon')} />
          </div>
        )}

        <div className='flex-1'>{children}</div>
        <div className='item-actions'>
          {showRemove && (
            <IconTrash
              size={16}
              color='red'
              style={{ cursor: 'pointer', zIndex: 1 }}
              onClick={onRemove}
            />
          )}
        </div>
      </Flex>
    </div>
  );
};

export default DraggableItem;
