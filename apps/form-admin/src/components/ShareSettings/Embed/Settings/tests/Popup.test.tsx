// Popup.test.tsx
import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import { render } from '@/utils/test-util/testUiComponent';
import Popup from '../Popup';

// Mock icons used in the component
vi.mock('@tabler/icons-react', async (importOriginal) => ({
  ...await importOriginal(),
  IconSquareLetterS: () => <span data-testid="icon-s" />,
  IconSquareLetterM: () => <span data-testid="icon-m" />,
  IconSquareLetterL: () => <span data-testid="icon-l" />,
}));


// Mock Mantine core components we interact with
vi.mock('@mantine/core', async (importOriginal) => {
  // Provide minimal versions of components needed for testing
  const MockSelect = ({
                        data = [],
                        value,
                        onChange,
                        placeholder,
                        className,
                      }: {
    data: Array<{ label: string; value: string }>;
    value?: string | null;
    onChange?: (val: string | null) => void;
    placeholder?: string;
    className?: string;
  }) => {
    // Clean placeholder to remove invisible characters
    const cleanPlaceholder = placeholder?.replace(/[\u200B-\u200D\uFEFF\u2060]/g, '') ?? 'select';
    return (
      <label>
        <span>{placeholder}</span>
        <select
            aria-label={placeholder}
            data-testid={`select-${cleanPlaceholder}`}
            className={className}
            value={value ?? ''}
            onChange={(e) => {
              onChange?.(e.target.value || null)
            }}
        >
          {data.map((opt) => (
              <option key={opt.value} value={opt.value}>
                {opt.label}
              </option>
          ))}
        </select>
      </label>
    );
  };

  MockSelect.extend = vi.fn()

  const MockSwitch = ({
                        checked,
                        onChange,
                        label,
                        'data-testid': dataTestId,
                      }: {
    checked?: boolean;
    onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
    label?: string;
    'data-testid'?: string;
  }) => (
      <label>
        <span>{label}</span>
        <input
            type="checkbox"
            aria-label={label}
            data-testid={dataTestId}
            checked={!!checked}
            onChange={onChange}
        />
      </label>
  );

  const MockTextInput = ({
                           value,
                           onChange,
                           placeholder,
                           'data-testid': dataTestId,
                         }: {
    value?: string | number;
    onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
    placeholder?: string;
    'data-testid'?: string;
  }) => (
      <input
          type="text"
          aria-label={placeholder}
          data-testid={dataTestId ?? `input-${placeholder ?? 'text'}`}
          value={value as any}
          onChange={onChange}
      />
  );

  MockTextInput.extend = vi.fn()

  return {
    // Components used in the file; only Select/Switch/TextInput need interactivity
    ...await importOriginal(),
    Select: MockSelect,
    Switch: MockSwitch,
    TextInput: MockTextInput,
    Divider: () => <hr />,
    rem: (n: number | string) => String(n),
  };
});

describe('Popup', () => {
  let onFormSetting: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    onFormSetting = vi.fn();
  });

  it('calls onFormSetting on mount with default values', async () => {
    render(<Popup onFormSetting={onFormSetting} />);

    await waitFor(() => expect(onFormSetting).toHaveBeenCalled());

    const lastCall =
        onFormSetting.mock.calls[onFormSetting.mock.calls.length - 1][0];

    expect(lastCall).toMatchObject({
      launch: 'click',
      closeSubmit: false,
      size: 'M',
      heightType: 'fixed',
      height: 500,
      heightUnit: 'px',
    });
    // Should NOT include time/percent/showButton for default launch 'click'
    expect(lastCall).not.toHaveProperty('time');
    expect(lastCall).not.toHaveProperty('percent');
    expect(lastCall).not.toHaveProperty('showButton');
  });

  it('includes time when launch is set to elapsed', async () => {
    render(<Popup onFormSetting={onFormSetting} />);

    // Select labeled by placeholder "launchPlaceholder" (translation key)
    const launchSelect = await screen.findByTestId('select-launchPlaceholder');
    fireEvent.change(launchSelect, { target: { value: 'elapsed' } });

    await waitFor(() => {
      const lastCall =
          onFormSetting.mock.calls[onFormSetting.mock.calls.length - 1][0];
      expect(lastCall.launch).toBe('elapsed');
      expect(lastCall).toHaveProperty('time', 0);
      // Should not include percent for elapsed
      expect(lastCall).not.toHaveProperty('percent');
    });
  });

  it('includes percent and showButton when launch is set to scrolling_to', async () => {
    render(<Popup onFormSetting={onFormSetting} />);

    const launchSelect = await screen.findByTestId('select-launchPlaceholder');
    fireEvent.change(launchSelect, { target: { value: 'scrolling_to' } });


    await waitFor(() => {
      expect(screen.getByText('scrollPercent')).toBeInTheDocument()

    });
  });

  it('includes showButton when launch is set to scrolling_end', async () => {
    render(<Popup onFormSetting={onFormSetting}/>);

    const launchSelect = await screen.findByTestId('select-launchPlaceholder');
    fireEvent.change(launchSelect, {target: {value: 'scrolling_end'}});

    await waitFor(() => {
      const lastCall = onFormSetting.mock.calls[onFormSetting.mock.calls.length - 1][0];
      expect(lastCall.launch).toBe('scrolling_end');
      expect(lastCall).toHaveProperty('showButton', false);
      expect(lastCall).not.toHaveProperty('percent');
      expect(lastCall).not.toHaveProperty('time');
    });
  });

  it('includes time property when launch is set to elapsed', async () => {
    render(<Popup onFormSetting={onFormSetting}/>);

    const launchSelect = await screen.findByTestId('select-launchPlaceholder');
    fireEvent.change(launchSelect, {target: {value: 'elapsed'}});

    await waitFor(() => {
      const lastCall = onFormSetting.mock.calls[onFormSetting.mock.calls.length - 1][0];
      expect(lastCall.launch).toBe('elapsed');
      expect(lastCall).toHaveProperty('time', 0);
      expect(lastCall).not.toHaveProperty('percent');
      expect(lastCall).not.toHaveProperty('showButton');
    });
  });

  it('updates height when valid number is entered', async () => {
    render(<Popup onFormSetting={onFormSetting}/>);

    const heightInput = screen.getByTestId('input-text');
    fireEvent.change(heightInput, {target: {value: '750'}});

    await waitFor(() => {
      const lastCall = onFormSetting.mock.calls[onFormSetting.mock.calls.length - 1][0];
      expect(lastCall.height).toBe(750);
    });
  });

  it('prevents negative height values', async () => {
    render(<Popup onFormSetting={onFormSetting}/>);

    const heightInput = screen.getByTestId('input-text');
    fireEvent.change(heightInput, {target: {value: '-100'}});

    await waitFor(() => {
      const lastCall = onFormSetting.mock.calls[onFormSetting.mock.calls.length - 1][0];
      expect(lastCall.height).toBe(500);
    });
  });

  it('updates time when launch is elapsed and seconds are changed', async () => {
    render(<Popup onFormSetting={onFormSetting}/>);

    const launchSelect = await screen.findByTestId('select-launchPlaceholder');
    fireEvent.change(launchSelect, {target: {value: 'elapsed'}});

    const timeInput = await screen.findByTestId('input-second');
    fireEvent.change(timeInput, {target: {value: '30'}});

    await waitFor(() => {
      const lastCall = onFormSetting.mock.calls[onFormSetting.mock.calls.length - 1][0];
      expect(lastCall.launch).toBe('elapsed');
      expect(lastCall.time).toBe(30);
    });
  });


  it('updates scrollPercent when valid number is entered', async () => {
    render(<Popup onFormSetting={onFormSetting}/>);

    const launchSelect = await screen.findByTestId('select-launchPlaceholder');
    fireEvent.change(launchSelect, {target: {value: 'scrolling_to'}});

    const percentInput = await screen.findByTestId('input-scrollPercent');
    fireEvent.change(percentInput, {target: {value: '50'}});

    await waitFor(() => {
      const lastCall = onFormSetting.mock.calls[onFormSetting.mock.calls.length - 1][0];
      expect(lastCall.percent).toBe(50);
    });
  });

  it('prevents negative scrollPercent values', async () => {
    render(<Popup onFormSetting={onFormSetting}/>);

    const launchSelect = await screen.findByTestId('select-launchPlaceholder');
    fireEvent.change(launchSelect, {target: {value: 'scrolling_to'}});

    const percentInput = await screen.findByTestId('input-scrollPercent');
    fireEvent.change(percentInput, {target: {value: '-10'}});

    await waitFor(() => {
      const lastCall = onFormSetting.mock.calls[onFormSetting.mock.calls.length - 1][0];
      expect(lastCall.percent).toBe(30);
    });
  });

  it('prevents scrollPercent values over 100', async () => {
    render(<Popup onFormSetting={onFormSetting}/>);

    const launchSelect = await screen.findByTestId('select-launchPlaceholder');
    fireEvent.change(launchSelect, {target: {value: 'scrolling_to'}});

    const percentInput = await screen.findByTestId('input-scrollPercent');
    fireEvent.change(percentInput, {target: {value: '150'}});

    await waitFor(() => {
      const lastCall = onFormSetting.mock.calls[onFormSetting.mock.calls.length - 1][0];
      expect(lastCall.percent).toBe(100);
    });
  });

});
