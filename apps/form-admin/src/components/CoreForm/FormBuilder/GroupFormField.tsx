import { useAppearanceStyle } from '@/store/getters/builder/useAppearanceStyle';
import { addFieldToGroup } from '@/store/action/builder';
import { FieldDragType } from '@/types';
import {
  FieldType,
  GroupFieldType,
  type FormField,
  type GroupFormField,
} from '@/types/form-builder';
import { Box, Flex, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import React, { useEffect, useRef, useState } from 'react';
import { useDrop } from 'react-dnd';
import { useDispatch } from 'react-redux';
import styles from '../FormStyling/Form.module.scss';
import FormFieldBase from './FormField';
import QuestionLabel from './common/QuestionLabel';
import RichTextWithMention from './common/RichTextWithMention';
import { useTranslation } from 'react-i18next';
import clsx from 'clsx';
import { isGroupType } from '@/utils';

// Field types that cannot be added to groups
const FIELDS_NOT_ALLOWED_IN_GROUPS = [
  GroupFieldType.Section,
  FieldType.Hidden,
  // Add any other field types that shouldn't be in groups
];

// Helper function to check if a field can be added to groups
const isFieldAllowedInGroup = (fieldType: string) => {
  return !FIELDS_NOT_ALLOWED_IN_GROUPS.includes(fieldType as FieldType);
};

const useStyles = createStyles(theme => ({
  groupContainer: {
    position: 'relative',
    transition: 'background-color 0.2s ease, border-color 0.2s ease',
    borderRadius: theme.radius.md,
    padding: rem(8),
    '&.canDrop': {
      backgroundColor: 'rgba(59, 130, 246, 0.05)',
      border: `2px dashed ${theme.colors.blue[4]}`,
    },
  },
  dropIndicator: {
    height: rem(2),
    backgroundColor: theme.colors.blue[5],
    borderRadius: rem(1),
    margin: `${rem(4)} 0`,
    opacity: 0,
    transition: 'opacity 0.2s ease',
    '&.show': {
      opacity: 1,
    },
  },
  emptySection: {
    padding: rem(20),
    width: '100%',
    textAlign: 'center',
    color: theme.colors.gray[5],
    fontSize: rem(14),
    fontStyle: 'italic',
    border: `1px dashed ${theme.colors.gray[3]}`,
    borderRadius: theme.radius.md,
    backgroundColor: theme.colors.gray[0],
    marginTop: rem(-12),
  },
  sectionContainer: {
    backgroundColor: theme.colors.gray[0],
  },
  level2GroupContainer: {
    // Reset padding for case group in group
    padding: 0,
    height: 'auto',
  },
}));

interface GroupFormFieldProps {
  group: GroupFormField;
  isLevel2Group?: boolean;
  onGroupChange: (newGroup: Partial<GroupFormField>) => void;
  onGroupFocus?: () => void;
}

const GroupFormFieldBase: React.FC<GroupFormFieldProps> = ({
  group,
  isLevel2Group = false,
  onGroupChange,
  onGroupFocus,
}) => {
  const { t } = useTranslation('form_builder');
  const values = { label: group.label, description: group.description };
  const [valuesState, setValues] = useState(values);
  const { textFieldStyle } = useAppearanceStyle();
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const [dropIndex, setDropIndex] = useState<number | null>(null);
  const [draggedFieldWidth, setDraggedFieldWidth] = useState<string | number | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: [FieldDragType.DRAG_CHANGE_ORDER, FieldDragType.DRAG_ADD_FIELD],
    canDrop: (dragItem: any) => {
      // Not allow to drop if this group is level 2 group
      if (isLevel2Group) return true;
      const draggedField = dragItem.data?.[0] || dragItem;
      // Just allow to drop if this group is Group Section, and the field is allowed in group
      return (
        group.type === GroupFieldType.Section &&
        isFieldAllowedInGroup(draggedField.type) &&
        draggedField.id !== group.id
      );
    },
    hover: (dragItem: any, monitor) => {
      if (isLevel2Group) return;
      if (!containerRef.current) return;

      const clientOffset = monitor.getClientOffset();
      if (!clientOffset) return;

      // Get the dragged field width
      if (!draggedFieldWidth) {
        const draggedField = dragItem.data?.[0] || dragItem;
        const width =
          dragItem.width ||
          (draggedField?.style?.width && draggedField.style.width !== '100%'
            ? draggedField.style.width
            : null);
        if (width) {
          setDraggedFieldWidth(typeof width === 'string' ? width : `${width}px`);
        }
      }

      const containerRect = containerRef.current.getBoundingClientRect();
      const relativeY = clientOffset.y - containerRect.top;

      // Calculate which field position to insert at (between any fields)
      // Get all direct children that are not drop indicators
      const allChildren = Array.from(containerRef.current.children);
      const fieldElements = allChildren.filter((child: Element) => {
        const htmlChild = child as HTMLElement;
        return (
          !htmlChild.classList.contains('show') &&
          htmlChild.style.flex &&
          htmlChild.style.flex.includes('calc')
        );
      });
      let insertIndex = group.fields.length;

      for (let i = 0; i < fieldElements.length; i++) {
        const fieldRect = fieldElements[i].getBoundingClientRect();
        const fieldRelativeTop = fieldRect.top - containerRect.top;
        const fieldMiddle = fieldRelativeTop + fieldRect.height / 2;

        if (relativeY < fieldMiddle) {
          insertIndex = i;
          break;
        }
      }

      setDropIndex(insertIndex);
    },
    drop: (dragItem: any) => {
      const draggedItem = dragItem.data?.[0] || dragItem;

      // Field validation is now handled by canDrop function
      dispatch(
        addFieldToGroup({
          groupId: group.id,
          field: draggedItem,
          index: dropIndex ?? undefined,
          removeFromOriginal: dragItem.type === FieldDragType.DRAG_CHANGE_ORDER,
        })
      );

      setDropIndex(null);
      setDraggedFieldWidth(null);

      // Return a result to indicate this drop was handled
      // This prevents the drop from bubbling up to parent drop handlers
      return { handled: true };
    },
    collect: monitor => ({
      isOver: monitor.isOver({ shallow: true }),
      canDrop: monitor.canDrop(),
    }),
  });

  const onFieldChange = (newField: Partial<FormField>, index) => {
    const newFields = [...group.fields];
    newFields[index] = { ...newFields[index], ...newField };
    onGroupChange({ ...group, fields: newFields });
  };

  const handleInputChange = (field: string, newValue: string) => {
    setValues({ ...valuesState, [field]: newValue });

    onGroupChange({ ...group, [field]: newValue });
  };

  useEffect(() => {
    setValues({ label: group.label, description: group.description });
  }, [group]);

  useEffect(() => {
    if (!isOver) {
      setDropIndex(null);
      setDraggedFieldWidth(null);
    }
  }, [isOver]);

  if (!isLevel2Group) {
    drop(containerRef);
  }

  return (
    <div
      ref={containerRef}
      className={clsx(
        styles['form-content-container'],
        classes.groupContainer,
        canDrop && isOver ? 'canDrop' : '',
        group.type === GroupFieldType.Section ? classes.sectionContainer : '',
        isLevel2Group && classes.level2GroupContainer
      )}
      onMouseDown={() => onGroupFocus && onGroupFocus()}
    >
      <Flex
        direction='column'
        gap={rem(8)}
        style={{
          flex: `0 0 calc(100% - ${styles.formFieldGapX})`,
          zIndex: group?.fields?.length + 1,
        }}
      >
        {!group.hideLabel && (
          <Box style={{ zIndex: 2 }}>
            <QuestionLabel
              label={valuesState.label}
              handleInputChange={handleInputChange}
              style={{ ...group?.style, width: 'auto', ...textFieldStyle.labelStyle }}
              isRequired={false}
            />
          </Box>
        )}

        {group.descriptionEnabled && (
          <RichTextWithMention
            value={valuesState.description || ''}
            onChange={content => handleInputChange('description', content)}
            style={{ ...group?.style, ...textFieldStyle.labelStyle, zIndex: 1 }}
            nonPadding={true}
            theme='paragraph'
          />
        )}
      </Flex>

      {/* Show empty section placeholder when no fields */}
      {group?.fields?.length === 0 ? (
        <div className={classes.emptySection}>
          {group.type === GroupFieldType.Section
            ? t('formField.section.dragFields')
            : t('formField.group.noFields')}
        </div>
      ) : (
        group?.fields?.map((field, index) => {
          if (field?.isHide || !field) return null;

          return (
            <React.Fragment key={field.id + index}>
              {/* Drop indicator before field */}
              {dropIndex === index && canDrop && (
                <div
                  className={`${classes.dropIndicator} show`}
                  style={{ width: draggedFieldWidth || '100%' }}
                />
              )}
              {isGroupType(field.type) ? (
                <GroupFormFieldBase
                  isLevel2Group={true}
                  group={field as unknown as GroupFormField}
                  onGroupChange={newField => onFieldChange?.(newField, index)}
                  onGroupFocus={onGroupFocus}
                />
              ) : (
                <FormFieldBase
                  zIndex={group?.fields?.length ? group.fields.length - index : 0}
                  field={field}
                  onFieldChange={newField => onFieldChange(newField, index)}
                />
              )}
            </React.Fragment>
          );
        })
      )}

      {/* Drop indicator at the end (only when there are fields) */}
      {group?.fields?.length !== 0 && dropIndex === group?.fields?.length && canDrop && (
        <div
          className={`${classes.dropIndicator} show`}
          style={{ width: draggedFieldWidth || '100%' }}
        />
      )}
    </div>
  );
};

export default GroupFormFieldBase;
