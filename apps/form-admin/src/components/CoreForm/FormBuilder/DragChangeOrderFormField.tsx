import { ModeContext } from '@/hooks/useScreenPreview';
import { DUPLICATE_FIELD, REMOVE_FIELD } from '@/store/action/actionTypes';
import { setIsDragging } from '@/store/action/builder';
import { FieldDragType } from '@/types';
import { isGroupType } from '@/utils';
import { ActionIcon, Popover, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCopy, IconGripVertical, IconTrash } from '@tabler/icons-react';
import clsx from 'clsx';
import { useEffect, useMemo, useRef } from 'react';
import { useContext } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { getEmptyImage } from 'react-dnd-html5-backend';
import { useDispatch } from 'react-redux';
import styles from '../FormStyling/Form.module.scss';

const useStyles = createStyles((theme) => ({
  FieldDraggableItem: {
    position: 'relative',
    '&.dragContainerActive': {
      zIndex: 199,
    },
    '&:hover': {
      zIndex: 200,
      '.drag-container': {
        opacity: 1,
      },
    },
  },
  dragContainer: {
    position: 'absolute',
    width: `calc(100% + ${rem(40)})`,
    height: `calc(100% + ${rem(20)})`,
    left: rem(-24),
    top: rem(-10),
    opacity: 0,
    border: `1px solid ${theme.colors.decaNavy[1]}`,
    borderRadius: theme.radius.md,
    zIndex: 0,
    '&:hover, &.isActive': {
      opacity: 1,
    },
  },
  dragTarget: {
    height: rem(16),
    width: rem(16),
    color: theme.colors.decaLight[4],
    cursor: 'move',
    position: 'absolute',
    top: '50%',
    left: 0,
    transform: 'translateY(-50%)',
  },

  fieldActions: {
    padding: `${rem(8)} ${rem(4)}`,
    '& > *': {
      cursor: 'pointer',
    },
    borderRadius: rem(30),
    border: `1px solid ${theme.colors.gray[2]}`,
    backgroundColor: 'white',
  },

  iconRed: {
    color: theme.colors.red[5],
  },
}));

interface DraggableFormFieldProps {
  field: any;
  index: number;
  zIndex?: number;
  moveCard: (dragIndex: number, hoverIndex: number) => void;
  children: React.ReactNode;
  isActive: boolean;
  onMouseDown?: () => void;
}

const DraggableFormField = ({
  field,
  index,
  zIndex = 0,
  moveCard,
  children,
  isActive,
  onMouseDown,
}: DraggableFormFieldProps) => {
  const ref = useRef<HTMLDivElement>(null);
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const { isDesktop } = useContext(ModeContext);

  const [{ handlerId }, drop] = useDrop({
    accept: [FieldDragType.DRAG_CHANGE_ORDER],
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(items: any, monitor) {
      if (!ref.current) {
        return;
      }

      const item = items.data[0];

      const dragIndex = item.index;
      const hoverIndex = index;
      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return;
      }
      // Determine rectangle on screen
      const hoverBoundingRect = ref.current?.getBoundingClientRect();
      // Get vertical middle
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      // Determine mouse position
      const clientOffset = monitor.getClientOffset() || { x: 0, y: 0 };
      // Get pixels to the top
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;
      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%
      // Dragging downwards
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }
      // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }
      // Time to actually perform the action
      moveCard(dragIndex, hoverIndex);
      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex;
    },
  });

  const [{ isDragging }, drag, preview] = useDrag({
    type: FieldDragType.DRAG_CHANGE_ORDER,
    item: () => {
      return {
        type: FieldDragType.DRAG_CHANGE_ORDER,
        data: [
          {
            ...field,
            index,
            style: {
              ...field.style,
            },
          },
        ],
        width: ref.current?.clientWidth,
      };
    },
    end: () => {
      dispatch(setIsDragging(false));
    },
    collect: (monitor) => {
      if (monitor.isDragging()) {
        dispatch(setIsDragging(true));
      }
      return {
        isDragging: monitor.isDragging(),
      };
    },
  });

  useEffect(() => {
    preview(getEmptyImage(), { captureDraggingState: false });
  }, [preview]);

  drag(drop(ref));

  const onRemoveField = () => {
    dispatch({ type: REMOVE_FIELD, payload: field.id });
  };

  const onDuplicateField = () => {
    dispatch({ type: DUPLICATE_FIELD, payload: field.id });
  };

  // Avoid nested gap
  const fieldGap = useMemo(() => {
    const isWidthFull = (isDesktop ? field?.style?.width || '100%' : '100%') === '100%';

    if (isGroupType(field.type) || isWidthFull) {
      return '0px';
    }
    return styles.formFieldGapX;
  }, [field.type, isDesktop, field.style?.width]);

  return (
    <div
      className={clsx(classes.FieldDraggableItem, {
        dragContainerActive: isActive,
      })}
      ref={ref}
      style={{
        flex: `0 0 calc(${isDesktop ? field?.style?.width || '100%' : '100%'} - ${fieldGap})`,
        opacity: isDragging ? 0 : 1,
        marginBottom: rem(10),
        zIndex: isActive ? 99999 : zIndex,
      }}
      onMouseDown={() => onMouseDown && onMouseDown()}
      data-handler-id={handlerId}
    >
      <Popover opened={true} position='right' withinPortal={false}>
        {children}
        <Popover.Target>
          <div className={clsx(classes.dragContainer, { isActive: isActive }, 'drag-container')}>
            {/* Drag icon */}
            <IconGripVertical className={classes.dragTarget} />

            {/* Action */}
            <Popover.Dropdown className={classes.fieldActions}>
              <ActionIcon
                variant='transparent'
                onClick={() => onDuplicateField && onDuplicateField()}
              >
                <IconCopy size={14} />
              </ActionIcon>
              <ActionIcon variant='transparent' onClick={() => onRemoveField && onRemoveField()}>
                <IconTrash className={classes.iconRed} size={14} />
              </ActionIcon>
            </Popover.Dropdown>
          </div>
        </Popover.Target>
      </Popover>
    </div>
  );
};

export default DraggableFormField;
