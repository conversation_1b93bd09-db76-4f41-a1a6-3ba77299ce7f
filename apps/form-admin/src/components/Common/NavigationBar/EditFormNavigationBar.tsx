import { useBuilderHistory } from '@/hooks/useBuilderHistory';
import { setBuilderRightNav } from '@/store/action/builder';
import { useAppSelector } from '@/store/hooks';
import { BuilderRightMenuType } from '@/types';
import { Flex } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton } from '@resola-ai/ui';
import {
  IconArrowBackUp,
  IconArrowForwardUp,
  IconColorFilter,
  IconHelp,
  IconRoute,
} from '@tabler/icons-react';
import clsx from 'clsx';
import type { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';

const useStyles = createStyles((theme) => ({
  editFormNavBar: {
    borderRadius: 8,
    border: `1px solid ${theme.colors.decaLight[2]}`,
  },
  editFormNavBarItem: {
    '&.active': {
      color: theme.colors.decaNavy[5],
      backgroundColor: theme.colors.decaNavy[1],
    },
  },
}));
const EditFormNavigationBar = () => {
  const { undo, redo, canUndo, canRedo, undoTooltip, redoTooltip } = useBuilderHistory();
  const { classes } = useStyles();
  const rightMenuType = useAppSelector((state) => state.builder.rightMenuType);
  const dispatch = useDispatch();
  const { t } = useTranslation('form_builder');

  const handleButtonClick = (type: BuilderRightMenuType) => () => {
    if (type !== rightMenuType) {
      dispatch(setBuilderRightNav(type));
    }
  };

  const renderButtons = (type: BuilderRightMenuType, icon: ReactNode, label: string) => {
    return (
      <DecaButton
        className={clsx(classes.editFormNavBarItem, { active: rightMenuType === type })}
        variant={'neutral_text'}
        leftSection={icon}
        onClick={handleButtonClick(type)}
      >
        {label}
      </DecaButton>
    );
  };

  return (
    <Flex
      className={classes.editFormNavBar}
      px={16}
      py={12}
      mb={12}
      bg='decaMono.1'
      gap={10}
      justify='space-between'
    >
      <Flex gap={10}>
        {renderButtons(
          BuilderRightMenuType.Question,
          <IconHelp size={16} />,
          t('common.pagetab.question')
        )}
        {renderButtons(
          BuilderRightMenuType.PageLogic,
          <IconRoute size={16} />,
          t('common.pagetab.logic')
        )}
        {renderButtons(
          BuilderRightMenuType.Appearance,
          <IconColorFilter size={16} />,
          t('common.pagetab.design')
        )}
      </Flex>
      <Flex gap={10}>
        <DecaButton
          className={classes.editFormNavBarItem}
          variant='neutral_text'
          onClick={undo}
          disabled={!canUndo}
          title={undoTooltip}
        >
          <IconArrowBackUp size={16} />
        </DecaButton>
        <DecaButton
          className={classes.editFormNavBarItem}
          variant='neutral_text'
          onClick={redo}
          disabled={!canRedo}
          title={redoTooltip}
        >
          <IconArrowForwardUp size={16} />
        </DecaButton>
      </Flex>
    </Flex>
  );
};

export default EditFormNavigationBar;
