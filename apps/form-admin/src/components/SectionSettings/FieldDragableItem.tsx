import { ADD_NEW_FIELD } from '@/store/action/actionTypes';
import { useAppDispatch } from '@/store/hooks';
import { FieldDragType, type FormField } from '@/types';
import { Flex, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useEffect } from 'react';
import { useDrag } from 'react-dnd';
import { getEmptyImage } from 'react-dnd-html5-backend';

type FieldItem = {
  label: string;
  data: Partial<FormField>;
  icon?: JSX.Element;
};

export const useStyles = createStyles(theme => ({
  fieldItem: {
    fontSize: 14,
    fontWeight: 400,
    height: rem(32),
    gap: rem(6),
    borderRadius: 6,
    padding: rem(4),
    '&:hover': {
      cursor: 'pointer',
      backgroundColor: theme.colors.decaLight[0],
    },
  },
}));

export const FieldDraggableItem = (item: FieldItem) => {
  const dispatch = useAppDispatch();
  const { classes } = useStyles();

  const [_, drag, preview] = useDrag(() => ({
    id: item.label,
    item: item,
    type: FieldDragType.DRAG_ADD_FIELD,
    end: (item, monitor) => {
      const dropResult = monitor.getDropResult();
      if (item && dropResult && !(dropResult as { handled?: boolean }).handled) {
        dispatch({
          type: ADD_NEW_FIELD,
          payload: JSON.parse(JSON.stringify(item.data)),
        });
      }
    },
    collect: monitor => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  useEffect(() => {
    preview(getEmptyImage(), { captureDraggingState: false });
  }, [preview]);

  return (
    <Flex className={classes.fieldItem} align='center' ref={drag}>
      {item.icon}
      <span>{item.label}</span>
    </Flex>
  );
};

export default FieldDraggableItem;
