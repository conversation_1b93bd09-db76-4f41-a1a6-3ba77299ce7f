import type { AssetApiResponse, Form, FormAssetsResponse, FormDataContent } from '@/types';
import {
  ConvertToType,
  convertObjectPropertyCase,
  convertSectionFromApiToFormSection,
} from '@/utils/apiHelper';
import type { ISuccessCreate } from '@resola-ai/models';
import { axiosService, logger } from '@resola-ai/services-shared';

const handleApiError = (error: unknown): undefined => {
  logger.error(error);
  return undefined;
};

interface ExportResultParams {
  formId: string;
  search?: string;
  filter?: string;
  sort?: string;
}

interface TagParams {
  page: number;
  perPage: number;
  source?: 'general' | 'workspace';
  search?: string;
  tag?: string;
}

interface FilterCondition {
  is_template?: { $eq: boolean };
  tags?: { $regex: string; $options: string };
}

const buildQueryParams = (
  params: Record<string, string | boolean | number | undefined>
): string => {
  const queryParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, String(value));
    }
  });
  return queryParams.toString();
};

export const FormsAPI = {
  getForms: async (
    page: number,
    perPage: number,
    search?: string,
    filter?: string,
    isFavorite?: boolean
  ) => {
    const params = {
      search: search ? search : undefined,
      page,
      per_page: perPage,
      sort: '-is_pinned,-created_at',
      is_favorited: isFavorite ? 'true' : undefined,
    };

    const filterString = filter
      ? `filter={"creator_id": ${encodeURIComponent(JSON.stringify(filter))}}`
      : '';

    const queryString = buildQueryParams(params);
    const finalQueryString = filterString ? `${queryString}&${filterString}` : queryString;

    try {
      const { data } = await axiosService.instance.get(`/forms?${finalQueryString}`);
      return {
        response: data.response,
        pagination: data.pagination,
      };
    } catch (error) {
      return handleApiError(error);
    }
  },
  getTemplates: async ({ page, perPage, source, search, tag }: TagParams) => {
    const params = buildQueryParams({
      page,
      per_page: perPage,
      template_source: source,
      search: search ? search : undefined,
    });

    const filterConditions: FilterCondition[] = [{ is_template: { $eq: true } }];
    if (!search && tag) {
      filterConditions.push({ tags: { $regex: tag, $options: 'i' } });
    }

    const filterString = `filter=${encodeURIComponent(JSON.stringify({ $and: filterConditions }))}`;
    const queryString = params ? `${params}&${filterString}` : filterString;

    try {
      const { data } = await axiosService.instance.get(`/forms?${queryString}`);
      return {
        response: data.response,
        pagination: data.pagination,
      };
    } catch (error) {
      return handleApiError(error);
    }
  },
  create: async (data: any) => {
    try {
      const response = await axiosService.instance.post<ISuccessCreate<Form>>('/forms', data);
      return response.data.response;
    } catch (error) {
      return handleApiError(error);
    }
  },
  delete: async (id: string) => {
    return await axiosService.instance.delete(`/forms/${id}`);
  },
  updateForm: async (id: string, data: any) => {
    const convertedData = convertObjectPropertyCase(data, ConvertToType.SnakeCase);
    return await axiosService.instance.patch(`/forms/${id}`, convertedData);
  },
  getForm: async (id: string) => {
    try {
      const response = await axiosService.instance.get(`/forms/${id}`);
      return convertObjectPropertyCase(response.data.response, ConvertToType.CamelCase);
    } catch (error) {
      return handleApiError(error);
    }
  },
  getFormForBuilder: async (id: string, options: { skipConvertKeys?: string[] } = {}) => {
    const { skipConvertKeys = [] } = options;
    const response = await axiosService.instance.get(`/forms/${id}/pages`);
    return response.data.response?.map((section) =>
      convertSectionFromApiToFormSection(
        convertObjectPropertyCase(section, ConvertToType.CamelCase, { skipKeys: skipConvertKeys })
      )
    );
  },
  updateFormsData: (
    id: string, 
    data: FormDataContent[], 
    options: { skipConvertKeys?: string[] } = {}
  ) => {
    const { skipConvertKeys = [] } = options;
    const convertedData = data?.map((_) => convertObjectPropertyCase(_, ConvertToType.SnakeCase, { skipKeys: skipConvertKeys }));
    try {
      return axiosService.instance.put(`/forms/${id}/pages`, convertedData);
    } catch (error) {
      return handleApiError(error);
    }
  },
  uploadFormAsset: async (formId: string, file: File) => {
    const formData = new FormData();
    formData.append('file', file, file.name);

    try {
      const result = await axiosService.instance.post<{ response: FormAssetsResponse }>(
        `/assets?form_id=${formId}`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      return result.data.response;
    } catch (error) {
      return handleApiError(error);
    }
  },
  listResultViews: async (formId: string) => {
    try {
      const response = await axiosService.instance.get(`/forms/${formId}/views`);
      return response.data.response;
    } catch (error) {
      return handleApiError(error);
    }
  },
  createResultView: async (formId: string, data: any) => {
    try {
      const transformedData = {
        ...convertObjectPropertyCase(data, ConvertToType.SnakeCase),
        columns: data.columns?.map((column) =>
          convertObjectPropertyCase(column, ConvertToType.SnakeCase)
        ),
      };

      return await axiosService.instance.post(`/forms/${formId}/views`, transformedData);
    } catch (error) {
      return handleApiError(error);
    }
  },
  getResultSettings: async (formId: string, viewId = 'default') => {
    try {
      const response = await axiosService.instance.get(`/forms/${formId}/views/${viewId}`);
      return convertObjectPropertyCase(response.data.response.columns, ConvertToType.CamelCase, { defaultList: [] });
    } catch (error) {
      return handleApiError(error);
    }
  },

  saveResultSettings: (formId: string, viewId: string, data: any) => {
    const params = {
      form_id: formId,
      view_id: viewId,
    };
    const body = convertObjectPropertyCase(data, ConvertToType.SnakeCase);
    try {
      return axiosService.instance.patch(`/forms/${formId}/views/${viewId}`, body, { params });
    } catch (error) {
      return handleApiError(error);
    }
  },

  deleteResultView: async (formId: string, viewId: string) => {
    return await axiosService.instance.delete(`/forms/${formId}/views/${viewId}`);
  },
  listQuestions: async (formId: string, params: Record<string, any>) => {
    try {
      const response = await axiosService.instance.get(`/forms/${formId}/questions`, {
        params: params,
      });
      return response.data.response;
    } catch (error) {
      return handleApiError(error);
    }
  },
  listResponses: async (formId: string, params: Record<string, any>) => {
    try {
      const response = await axiosService.instance.get(`/forms/${formId}/responses`, {
        params: params,
      });
      return {
        response: response.data.response,
        pagination: convertObjectPropertyCase(response.data.pagination, ConvertToType.CamelCase),
      };
    } catch (error) {
      return handleApiError(error);
    }
  },
  getDetailResponse: async (formId: string, responseId: string) => {
    try {
      const response = await axiosService.instance.get(`/forms/${formId}/responses/${responseId}`);
      return response.data;
    } catch (error) {
      return handleApiError(error);
    }
  },
  exportResultCsv: async (params: ExportResultParams, columns?: string[]) => {
    try {
      const {
        data: { response },
      } = await axiosService.instance.post<AssetApiResponse>(
        '/assets/export',
        {
          columns,
        },
        {
          params: convertObjectPropertyCase(params, ConvertToType.SnakeCase),
        }
      );
      return response;
    } catch (error) {
      return handleApiError(error);
    }
  },
  getExportResultCsv: async (resourceId: string) => {
    try {
      const {
        data: { response },
      } = await axiosService.instance.get<AssetApiResponse>(`/assets/${resourceId}`);
      return response;
    } catch (error) {
      return handleApiError(error);
    }
  },
  createTemplate: async (formId: string, templateName: string, sourceType?: string) => {
    try {
      const payload = {
        source_id: formId,
        target_type: 'template',
        target_name: templateName,
        source_type: sourceType,
      };

      const { data } = await axiosService.instance.post('/forms/duplicate', payload);
      return data.response;
    } catch (error) {
      return handleApiError(error);
    }
  },
  createForm: async (formId: string, sourceType?: string) => {
    try {
      const payload = {
        source_id: formId,
        target_type: 'form',
        source_type: sourceType,
      };

      const { data } = await axiosService.instance.post('/forms/duplicate', payload);
      return new Promise<any>((resolve, reject) => {
        let isLoading = false;
        let attempt = 0;
        const interval = setInterval(async () => {
          if (attempt > 30) {
            clearInterval(interval);
            return;
          }
          if (isLoading) {
            return;
          }
          isLoading = true;
          attempt++;
          try {
            const {
              data: { response },
            } = await axiosService.instance.get(`/general/tasks/${data.response.id}`);
            if (response.status === 'finished') {
              resolve(response.result.success);
              clearInterval(interval);
            }
            if (response.status === 'failed') {
              reject(response.result.success);
              clearInterval(interval);
            }
          } finally {
            isLoading = false;
          }
        }, 1000);
      });
    } catch (error) {
      return handleApiError(error);
    }
  },
};
