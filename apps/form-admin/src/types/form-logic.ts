export type FormLogicAction = 'show' | 'hide';

// Page logic actions
export type PageLogicAction = 'redirect_to_page';

export type FormLogicOperator = 
  | 'contains'
  | 'does_not_contain'
  | 'starts_with'
  | 'ends_with'
  | 'is_empty'
  | 'is_not_empty'
  | 'is_any_of'
  | 'is_not_any_of'
  | 'is_equal_to'
  | 'is_not_equal_to'
  | 'is'
  | 'is_not'
  | 'is_checked'
  | 'is_not_checked';
  

export type FormLogicCombinator = 'and' | 'or';

export interface FormConditionRule {
  id?: string;
  field: string;
  operator: FormLogicOperator;
  value: any; 
}

export interface FormConditions {
  id?: string;
  combinator: FormLogicCombinator;
  rules: FormConditionRule[];
}

export interface FormLogic {
  id?: string;
  action: FormLogicAction;
  conditions: FormConditions;
}

export interface OperatorOption {
  value: FormLogicOperator;
  label: string;
  description?: string;
}

// Field type to operators mapping
export interface FieldTypeOperators {
  [fieldType: string]: FormLogicOperator[];
}

// Page logic interfaces
export interface PageConditionRule {
  id?: string;
  field: string;
  operator: FormLogicOperator;
  value: any;
}

export interface PageConditions {
  id?: string;
  combinator: FormLogicCombinator;
  rules: PageConditionRule[];
}

export interface PageLogicRule {
  id?: string;
  action: PageLogicAction;
  targetPageId: string;
  conditions: PageConditions;
}

export interface PageLogic {
  id?: string;
  normalRedirect?: {
    targetPageId: string | null;
  };
  conditionalRules?: PageLogicRule[];
}
