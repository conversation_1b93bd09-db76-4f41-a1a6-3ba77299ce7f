// Apply for all form input
import type {
  AppearanceButtonStyle,
  AppearanceHeaderPosition,
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
  InputStyle,
} from '@/types/enum';
import type { CSSProperties } from 'react';
import type { FormIntegrations } from './form';
import type { FormLogic, PageLogic } from './form-logic';

export enum FormBuilderSteps {
  Create = 'Create',
  Share = 'Share',
  Integrations = 'Integrations',
  Settings = 'Settings',
  Results = 'Results',
}

export enum ShareAction {
  Share = 'default',
  Embed = 'embed',
}

export enum EmbedLayoutType {
  Standard = 'standard',
  FullPage = 'full',
  Popup = 'popup',
  Slider = 'slider',
  Popover = 'popover',
  Sidebar = 'sidebar',
}

export interface EmbedSettings {
  embedType: EmbedLayoutType;
  formSetting?: { [key: string]: string };
}

export interface ShareHiddenField {
  paramId: string;
  paramName: string;
  paramValue: string;
}

export enum ResultTab {
  Results = 'Results',
  Summary = 'Summary',
  Analytics = 'Analytics',
}

export interface CustomizeFieldInfo {
  isDeleted: boolean;
  isHidden: boolean;
  label: string;
  labelText: string;
  questionId: string;
  type: string;
}

export interface FilterFieldInfo {
  type: string;
  fieldType?: FieldType;
  questionId: string;
  value: string | string[];
}

export interface FormAppearance {
  headingStyle: {
    fontFamily: string;
    fontSize: number;
    color: string;
  };
  paragraphStyle: {
    fontFamily: string;
    fontSize: number;
    color: string;
  };
  buttonStyle: {
    type: AppearanceButtonStyle;
    fullWidth: boolean;
    backgroundColor: string;
    textColor: string;
    fontFamily: string;
    fontSize: number;
  };
  formFieldStyle: {
    color: {
      question: string;
      answer: string;
      icon: string;
      description: string;
      fieldStroke: string;
      placeholder: string;
      fieldBackground: string;
    };
    fontFamily: {
      question: string;
      text: string;
      answer: string;
    };
    fontSize: {
      question: number;
      text: number;
      answer: number;
    };
  };
  defaultSettings: {
    color: string;
    fontFamily: string;
    inputStyle: InputStyle;
  };
  customize: boolean;
  headerStyle: {
    position: AppearanceHeaderPosition;
    logoImage: File | string;
    logoSize: AppearanceSettingsLogoSize;
    logoAlign: AppearanceSettingsLogoAlign;
    isUsingText: boolean;
    text: string;
  };
  footerStyle: {
    logoImage: File | string;
    logoSize: AppearanceSettingsLogoSize;
    logoAlign: AppearanceSettingsLogoAlign;
    isUsingText: boolean;
    text: string;
  };
}

export interface FormStyle {
  fontFamily?: string;
  fontSize?: string;
  borderColor?: string;
  backgroundColor?: string;
}

export interface SectionStyle extends FormStyle {}

export interface FormFieldStyle extends FormStyle {
  width?: number | string;
}

export interface FormSectionLayout {
  type: FormLayoutType;
  imageUrl: string | null;
  fieldWidth: string;
}

// Form Section mostly used for page of form, layout purpose
export interface FormSection {
  id: string;
  type: FieldType.Section;
  name: string;
  layout: FormSectionLayout;
  content: FormDataContent[];
  logics?: PageLogic; // Page-level logic settings
}

export interface FormHiddenSection {
  id: string;
  type: FieldType.Hidden;
  content: FormDataContent[];
}

export enum FieldType {
  Email = 'email',
  PhoneNumber = 'phone_number',
  Checkbox = 'checkbox',
  Checkboxes = 'checkboxes',
  Radio = 'radio',
  Date = 'date',
  Time = 'time',
  DateTime = 'datetime',
  DateRange = 'date_range',
  DateSelector = 'date_selector',
  Number = 'number',
  Section = 'section',
  Heading = 'heading',
  Paragraph = 'paragraph',
  Dropdown = 'dropdown',
  LongQA = 'long_qa',
  ShortQA = 'short_qa',
  Legal = 'legal',
  Website = 'website',
  YesNo = 'yes_no',
  MultipleChoice = 'multiple_choice',
  PictureChoice = 'picture_choice',
  PostCode = 'postcode',
  OpinionScale = 'opinion_scale',
  FileUploader = 'file_uploader',
  Rating = 'rating',
  LineId = 'line_id', // hidden question don't show in builder
  Hidden = 'hidden',
}

export enum GroupFieldType {
  Name = 'name',
  Address = 'address',
  DateTimeRange = 'datetime_range',
  Section = 'group_section',
}

export enum ValidatorType {
  Required = 'required',
  Email = 'email',
  MinLength = 'min_length',
  MaxLength = 'max_length',
  MinValue = 'min_value',
  MaxValue = 'max_value',
  Pattern = 'pattern',
  Hiragana = 'hiragana',
  Katakana = 'katakana',
  Kanji = 'kanji',
  Alphabet = 'alphabet',
}

export interface Validator {
  type: ValidatorType;
  value?: any;
  message?: string;
}

export interface FieldOption {
  label: string;
  value?: string;
  defaultCheck?: boolean;
  imageUrl?: string;
  id?: string;
}

interface FooterDescription {
  left?: string;
  right?: string;
  middle?: string;
}

export interface FormField {
  id: string;
  type: FieldType | GroupFieldType;
  label: string;
  name: string;
  placeholder?: string;
  icon?: IconType;
  description?: string;
  descriptionEnabled?: boolean;
  fixedDateTitleEnabled?: boolean;
  inputDirectlyEnabled?: boolean;
  dobEnabled?: boolean;
  dateFormat?: string;
  timeFormat?: string;
  rightIcon?: boolean;
  isOther?: boolean; // For radio, checkbox
  required?: boolean;
  style?: FormFieldStyle;
  validators: Validator[];
  options?: FieldOption[]; // For radio, checkbox, select
  defaultOption?: FieldOption; // For radio, checkbox, select
  isHide?: boolean; // hide show feature in group field
  layout?: FieldLayoutType;
  maxScale?: number;
  footerLabel?: FooterDescription;
  footerHide?: boolean;
  theme?: FileUploaderTheme;
  supportedTypes?: UploaderSupportedType[];
  showNumber?: boolean;
  shape?: RatingShape;
  logics?: FormLogic[];
  requireLinkClick?: boolean;
}

// Support for group of fields  => remove, drag and drop group
export interface GroupFormField {
  id: string;
  type: GroupFieldType;
  description?: string;
  descriptionEnabled?: boolean;
  label: string;
  hideLabel?: boolean;
  style?: FormFieldStyle;
  fields: FormField[];
}

// Example checkbox
// export interface CheckboxField extends FormField {
//   type: FieldType.Checkbox;
//   options: string[];
// }

export interface HiddenField extends FormField {
  type: FieldType.Hidden;
  name: string;
}

export type FormDataContent = FormField | GroupFormField | FormSection | FormHiddenSection;

export interface FormBuilderData {
  name: string;
  description?: string;
  backgroundImageUrl?: string;
  appearance: FormAppearance;
  // One form could contain multiple sections or fields in case in future we need to support nested forms or multiple steps, section
  content: FormDataContent[] | null; // Array of fields or sections
  integrations?: FormIntegrations[];
  screenshot: Screenshot | null;
}

export interface FieldBuilderProps {
  field: FormField;
  onFieldChange?: (newField: Partial<FormField>) => void;
  textStyle?: CSSProperties;
  inputStyle?: CSSProperties;
  labelStyle?: CSSProperties;
  iconStyle?: CSSProperties;
  descriptionStyle?: CSSProperties;
  iconComponent?: React.ReactNode;
}

export enum FormLayoutType {
  ImageTopWithSpace = 'image-top-with-space',
  ImageLeft = 'image-left',
  ImageRight = 'image-right',
  ImageTop = 'image-top',
  ImageBottom = 'image-bottom',
  ImageBackground = 'image-background',
}

export enum IconType {
  IconEmail = 'IconEmail',
  IconLink = 'IconLink',
}

export enum BuilderRightMenuType {
  Question = 0,
  Logic = 1,
  Appearance = 2,
  PageLogic = 3,
}

export enum FieldLayoutType {
  OneColumn = 'one_column',
  TwoColumn = 'two_column',
}

export enum AddressFieldsName {
  PostCode = 'postcode',
  Prefecture = 'prefecture',
  City = 'city',
  Street = 'street',
  Building = 'building',
}

export interface Screenshot {
  original: string;
  thumbnail: string;
  preview?: string;
}

export enum FileUploaderTheme {
  Small = 'small',
  Large = 'large',
}

export enum UploaderSupportedType {
  Image = 'image',
  Document = 'document',
  Media = 'media',
}

export enum RatingShape {
  Star = 'star',
  Heart = 'heart',
  Crown = 'crown',
  Smiley = 'smiley',
  Like = 'like',
}
