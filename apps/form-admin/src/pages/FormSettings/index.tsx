import { General, Submission, SystemMessage } from '@/components/FormSettings';
import { FormProvider, useForm } from '@/contexts';
import { useRouteParams } from '@/hooks';
import { FormsAPI } from '@/services/api';
import { useAppSelector } from '@/store/hooks';
import type { FormData } from '@/types';
import { showNotificationToast } from '@/utils/notification';
import { Box, Container, Grid, Loader, LoadingOverlay, Space, Tabs, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { zodResolver } from '@mantine/form';
import { DecaButton } from '@resola-ai/ui';
import { IconArrowGuide, IconInfoCircle, IconMessage2Code } from '@tabler/icons-react';
import dayjs from 'dayjs';
import { cloneDeep, isEmpty } from 'lodash';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import useSWR from 'swr';
import { z } from 'zod';

enum TabValues {
  General = 'general',
  Submission = 'submission',
  SystemMessage = 'system-message',
}

const TABS = [
  {
    value: TabValues.General,
    icon: <IconInfoCircle size={20} />,
    label: 'generalLabel',
    component: General,
  },
  {
    value: TabValues.Submission,
    icon: <IconArrowGuide size={20} />,
    label: 'postSubmissionLabel',
    component: Submission,
  },
  {
    value: TabValues.SystemMessage,
    icon: <IconMessage2Code size={20} />,
    label: 'systemMessageLabel',
    component: SystemMessage,
  },
  // Hide these items for this sprint
  // {
  //   value: 'form-behavior',
  //   icon: <IconSettings2 size={20} />,
  //   label: 'Form Behavior',
  //   component: <FormBehavior />,
  // },
  // {
  //   value: 'language',
  //   icon: <IconTextRecognition size={20} />,
  //   label: 'Language',
  //   component: <Language />,
  // },
];

const useStyles = createStyles((theme) => ({
  container: {
    width: '100%',
  },
  title: {
    marginTop: rem(70),
    fontSize: rem(20),
    color: theme.colors.decaDark[7],
  },
  tab: {
    color: theme.colors.decaGrey[7],
    fontWeight: 500,
    fontSize: rem(14),
    padding: `${rem(10)} ${rem(12)}`,
    gap: rem(12),
    '&:hover, &[data-active]': {
      color: theme.colors.decaNavy[5],
    },
  },
}));

const FormSettingsPage = () => {
  const { t } = useTranslation(['form_settings', 'common', 'form']);
  useRouteParams();
  const { classes } = useStyles();
  const currentFormId = useAppSelector((state) => state.builder.currentFormId);
  const [currentTab, setCurrentTab] = useState<string | null>('general');

  const [isSubmitting, setIsSubmitting] = useState(false);
  const submissionSchema = z.object({
    limitNumber: z
      .number()
      .int()
      .min(0, { message: t('requiredError', { ns: 'common' }) }),
    thankMessage: z.object({
      emailHeaderText: z
        .string()
        .trim()
        .min(1, { message: t('requiredError', { ns: 'common' }) }),
    }),
  });
  const schema = z.object({
    name: z
      .string()
      .min(1, { message: t('requiredError', { ns: 'common' }) })
      .max(100, { message: t('validationFormNameLength', { max: 100, ns: 'form' }) }),
    setting: z.object({
      submission: z.discriminatedUnion('mode', [
        submissionSchema.extend({
          mode: z.literal('message'),
          message: z
            .string()
            .trim()
            .min(1, { message: t('requiredError', { ns: 'common' }) }),
        }),

        submissionSchema.extend({
          mode: z.literal('message_redirect'),
          message: z
            .string()
            .trim()
            .min(1, { message: t('requiredError', { ns: 'common' }) }),
          button: z
            .string()
            .trim()
            .min(1, { message: t('requiredError', { ns: 'common' }) }),
          redirectUrl: z
            .string()
            .trim()
            .min(1, { message: t('requiredError', { ns: 'common' }) })
            .url({
              message: t('invalidUrl'),
            }),
        }),
        submissionSchema.extend({
          mode: z.literal('redirect'),
          redirectUrl: z
            .string()
            .trim()
            .min(1, { message: t('requiredError', { ns: 'common' }) })
            .url({
              message: t('invalidUrl'),
            }),
        }),
      ]),
      systemMessage: z.array(
        z.object({
          heading: z.object({
            en: z
              .string()
              .trim()
              .min(1, { message: t('requiredError', { ns: 'common' }) })
              .max(200, {
                message: t('invalidMaxLengthError', { ns: 'common', max: 200 }),
              }),
            ja: z
              .string()
              .trim()
              .min(1, { message: t('requiredError', { ns: 'common' }) })
              .max(200, {
                message: t('invalidMaxLengthError', { ns: 'common', max: 200 }),
              }),
          }),
          body: z.object({
            en: z
              .string()
              .trim()
              .min(1, { message: t('requiredError', { ns: 'common' }) })
              .max(200, {
                message: t('invalidMaxLengthError', { ns: 'common', max: 200 }),
              }),
            ja: z
              .string()
              .trim()
              .min(1, { message: t('requiredError', { ns: 'common' }) })
              .max(200, {
                message: t('invalidMaxLengthError', { ns: 'common', max: 200 }),
              }),
          }),
        })
      ),
    }),
    tags: z
      .array(
        z
          .string()
          .trim()
          .min(1, { message: t('requiredError', { ns: 'common' }) })
      )
      .max(100, { message: t('validationTagLength', { max: 100, ns: 'form' }) }),
    startAt: z
      .date()
      .nullable()
      .refine(
        (date) => {
          if (!date) return true;
          const expiredAt = (form.values as FormData).expiredAt;
          return !expiredAt || !dayjs(date).isAfter(expiredAt);
        },
        { message: t('validationDateFromTo', { ns: 'form' }) }
      ),
    expiredAt: z.date().nullable(),
  });

  const validation =
    currentTab === TabValues.General
      ? schema.pick({ name: true, tags: true, startAt: true, expiredAt: true })
      : schema.pick({ setting: true });

  const form = useForm({
    initialValues: {
      name: '',
      tags: [],
      startAt: null,
      expiredAt: null,
      setting: {
        submission: {
          mode: '',
          message: '',
          caption: '',
          button: '',
          redirectUrl: '',
          enableBranding: true,
          limitResponse: false,
          limitNumber: 0,
          enableConfirmationScreen: false,
          thankMessage: {
            emailQuestionId: '',
            emailIsEnabled: false,
            emailDescription: '',
            emailHeaderLogo: '',
            emailHeaderText: '',
            lineDescription: '',
            lineIsEnabled: false,
            emailIncludeAnswer: false,
          },
        },
        notification: {
          isAutoresponse: false,
        },
        behavior: {
          isMultipleResponse: false,
        },
        systemMessage: [
          {
            heading: { en: '', ja: '' },
            body: { en: '', ja: '' },
            type: '',
          },
          {
            heading: { en: '', ja: '' },
            body: { en: '', ja: '' },
            type: '',
          },
          {
            heading: { en: '', ja: '' },
            body: { en: '', ja: '' },
            type: '',
          },
        ],
      },
    },
    validateInputOnChange: true,
    validate: zodResolver(validation),
  });

  const { data, isValidating } = useSWR<FormData>(
    currentFormId ? `/forms/${currentFormId}` : null,
    () => FormsAPI.getForm(currentFormId),
    {
      revalidateOnMount: true,
      revalidateOnFocus: true,
    }
  );

  useEffect(() => {
    if (data) {
      // Normalize data
      const _form = cloneDeep(data);
      if (data.startAt) _form.startAt = new Date(`${data.startAt}`);
      if (data.expiredAt) _form.expiredAt = new Date(`${data.expiredAt}`);

      if (form.initialized) {
        form.setInitialValues(_form);
        form.setValues(_form);
      } else {
        form.initialize(_form);
      }
    }
  }, [data]);

  if (isEmpty(form.values) || isValidating) return <LoadingOverlay visible />;

  const getTabComponent = (tab) => {
    const TabComponent = tab.component;
    return <TabComponent onSubmit={form.onSubmit(handleSubmit)} />;
  };

  const handleSubmit = (formData: unknown) => {
    const data = formData as FormData;

    setIsSubmitting(true);
    let _data = {};
    if (currentTab === TabValues.General) {
      const _setting = {
        submission: {
          limitResponse: data.setting.submission.limitResponse,
          limitNumber: data.setting.submission.limitNumber,
          enableConfirmationScreen: data.setting.submission.enableConfirmationScreen,
        },
      };
      _data = {
        ..._data,
        startAt: data.startAt?.toISOString() ?? null,
        expiredAt: data.expiredAt?.toISOString() ?? null,
        name: data.name,
        tags: data.tags,
        setting: _setting,
      };
    }
    if (currentTab === TabValues.Submission) {
      const _setting = {
        submission: data.setting.submission,
      };
      _data = {
        ..._data,
        setting: _setting,
      };
    }
    if (currentTab === TabValues.SystemMessage) {
      const _setting = {
        systemMessage: data.setting.systemMessage,
      };
      _data = {
        ..._data,
        setting: _setting,
      };
    }

    FormsAPI.updateForm(currentFormId, _data)
      .then(() => {
        if (currentTab === TabValues.Submission) {
          form.setInitialValues(data);
        }
        showNotificationToast({ message: t('saveSuccess') });
      })
      .catch(() => {
        showNotificationToast({
          message: t('common.save.error', { ns: 'form_builder' }),
          isError: true,
        });
      })
      .finally(() => {
        setIsSubmitting(false);
      });
  };

  const handleChangeTab = (tab: string) => {
    setCurrentTab(tab);
  };

  return (
    <Container fluid className={classes.container} p={0} pos='relative' bg='decaLight.0'>
      <Box
        bg='decaNavy.4'
        h={rem(140)}
        component='div'
        pos='absolute'
        top='0'
        left={0}
        w={'100%'}
        style={{ zIndex: 0 }}
      />
      <FormProvider form={form}>
        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Tabs
            pos='relative'
            variant='pills'
            orientation='vertical'
            defaultValue={TabValues.General}
            style={{ zIndex: 1 }}
            color='decaNavy.0'
            my={rem(40)}
            value={currentTab}
            onChange={(value) => handleChangeTab(value as string)}
            maw={'75%'}
            mx='auto'
            styles={{
              tabIcon: {
                marginRight: '0 !important',
              },
            }}
          >
            <Grid w={'100%'} m={0}>
              <Grid.Col span={{ md: 0, lg: 3 }}>
                <Tabs.List bg='white' p={rem(8)} style={{ borderRadius: rem(8) }}>
                  {TABS.map((tab) => (
                    <Tabs.Tab
                      key={tab.value}
                      h={rem(42)}
                      className={classes.tab}
                      value={tab.value}
                      leftSection={tab.icon}
                    >
                      {t(tab.label)}
                    </Tabs.Tab>
                  ))}
                </Tabs.List>
              </Grid.Col>
              <Space w={rem(20)} />
              <Grid.Col
                span={{ md: 12, lg: 8 }}
                bg='white'
                m={rem(8)}
                style={{ borderRadius: rem(8) }}
              >
                {TABS.map((tab) => (
                  <Tabs.Panel key={tab.value} value={tab.value} pl='xs'>
                    {getTabComponent(tab)}

                    <DecaButton
                      radius='xl'
                      color='decaNavy.5'
                      fw={500}
                      leftSection={
                        isSubmitting ? <Loader size={rem(20)} color='decaLight.2' /> : null
                      }
                      size='md'
                      my={rem(32)}
                      disabled={isSubmitting}
                      mx={rem(16)}
                      type='submit'
                      hidden={['form-behavior', 'language'].includes(tab.value)}
                    >
                      {t('saveChanges')}
                    </DecaButton>
                  </Tabs.Panel>
                ))}
              </Grid.Col>
            </Grid>
          </Tabs>
        </form>
      </FormProvider>
    </Container>
  );
};

export default FormSettingsPage;
