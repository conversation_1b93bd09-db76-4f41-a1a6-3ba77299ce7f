import { CHANGE_SECTION_ORDER } from '@/store/action/actionTypes';
import { useAppSelector } from '@/store/hooks';
import { FieldType, type FormBuilderData, type FormHiddenSection, type FormSection } from '@/types';
import { ActionIcon, Flex, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconArrowBarLeft, IconPlus } from '@tabler/icons-react';
import clsx from 'clsx';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import PageItem from './PageItem';

export const useListPageStyles = createStyles((theme) => ({
  navList: {
    marginTop: rem(16),
    height: 'calc(100% - 100px)',
    overflowY: 'auto',
    // Hide scrollbar by webkit
    scrollbarWidth: 'none',
    msOverflowStyle: 'none',
    display: 'flex',
    flexDirection: 'column',
    rowGap: rem(4),
  },

  navBottom: {
    height: '100%',
    paddingBottom: rem(12),
  },

  editingInput: {
    width: '100%',
    border: 'none',
    borderRadius: theme.radius.md,
    backgroundColor: 'transparent',
    color: theme.colors.decaDark[0],
    fontWeight: 500,
    fontSize: theme.fontSizes.sm,
    outline: 'none',
    lineHeight: 1.55,
  },

  navItem: {
    padding: `${rem(10)} ${rem(12)}`,
    width: '100%',
    height: rem(42),
    alignItems: 'center',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',

    '&.isHoveredOver': {
      border: `2px dashed ${theme.colors.decaNavy[5]}`,
      borderRadius: theme.radius.md,
      borderStyle: 'dashed',
    },

    '&:hover, &.active': {
      backgroundColor: theme.colors.decaNavy[0],
      color: theme.colors.decaNavy[5],
      cursor: 'pointer',
      borderRadius: theme.radius.md,
    },
    '&.miniMode': {
      display: 'block',
      padding: `${rem(8)} ${rem(0)}`,
      textAlign: 'center',
      width: '100%',
    },
  },

  iconAdd: {
    color: theme.colors.decaNavy[5],
    cursor: 'pointer',
    '&:hover': {
      color: theme.colors.decaNavy[7],
    },
  },

  collapsedIcon: {
    transform: 'rotate(180deg)',
  },

  navItemText: {
    fontSize: theme.fontSizes.sm,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
  },
}));

type Props = {
  isFullMode?: boolean;
  onCollapse?: () => void;
  onAddNewPage?: () => void;
};

const ListPage = ({ isFullMode, onCollapse, onAddNewPage }: Props) => {
  const { classes } = useListPageStyles();
  const builderJson = useAppSelector((state) => state.builder.builderJson as FormBuilderData);
  const content = builderJson.content as (FormSection | FormHiddenSection)[];
  const selectingPageId = useAppSelector((state) => state.builder.selectingSectionId);
  const dispatch = useDispatch();
  const { t } = useTranslation('form_builder');

  const moveNavItem = (dragIndex, hoverIndex) => {
    dispatch({
      type: CHANGE_SECTION_ORDER,
      payload: { dragIndex, hoverIndex },
    });
  };

  const hiddenPage = content.find((section) => section.type === FieldType.Hidden);
  // if hidden page is exist, able to delete if there is more than 2 pages
  const ableToDelete = hiddenPage ? content.length > 2 : content.length > 1;

  // Calculate visible index map (excluding hidden sections)
  const visibleIndexMap = content.reduce(
    (acc, section) => {
      if (section.type !== FieldType.Hidden) {
        acc[section.id] = acc.visibleCount + 1;
        acc.visibleCount += 1;
      }
      return acc;
    },
    { visibleCount: 0 } as { [key: string]: number; visibleCount: number }
  );

  const isFormContainLogic = content.some((page) => {
    if (page.type === FieldType.Hidden) return false;
    return page?.logics?.normalRedirect?.targetPageId || page?.logics?.conditionalRules?.length || 0 > 0;
  });

  return (
    <Flex direction='column' justify='space-between' className={classes.navBottom}>
      <div className={classes.navList}>
        {content.map((page, index) =>
          page.type === FieldType.Hidden ? null : (
            <PageItem
              key={page.id}
              isFullMode={!!isFullMode}
              page={page}
              isActive={selectingPageId === page.id}
              index={index}
              isFormContainLogic={isFormContainLogic}     
              visibleIndex={visibleIndexMap[page.id]}
              ableToDelete={ableToDelete}
              moveNavItem={moveNavItem}
            />
          )
        )}

        {/* Plus Button */}
        {!isFullMode && (
          <ActionIcon
            className={classes.iconAdd}
            variant='transparent'
            onClick={() => onAddNewPage && onAddNewPage()}
          >
            <IconPlus size={18} />
          </ActionIcon>
        )}
      </div>

      <Flex
        gap={8}
        justify='start'
        className={clsx(classes.navItem, { miniMode: !isFullMode })}
        onClick={() => onCollapse && onCollapse()}
      >
        <ActionIcon variant='transparent'>
          <IconArrowBarLeft size={18} className={isFullMode ? '' : classes.collapsedIcon} />
        </ActionIcon>
        {isFullMode && <Text className={classes.navItemText}>{t('common.pages.action')}</Text>}
      </Flex>
    </Flex>
  );
};

export default ListPage;
