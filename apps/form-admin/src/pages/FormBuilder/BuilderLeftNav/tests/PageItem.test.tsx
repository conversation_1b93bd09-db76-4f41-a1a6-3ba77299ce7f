// PageItem.test.tsx
import { describe, it, beforeEach, expect, vi } from 'vitest';
import { render } from '@/utils/test-util/testUiComponent';
import PageItem from '../PageItem';
import {fireEvent, screen} from "@testing-library/react";

// Hoisted shared mocks/state
const hoisted = vi.hoisted(() => ({
  dispatchMock: vi.fn(),
  dndApi: {
    lastDropSpec: null as any,
    lastDragSpec: null as any,
  },
}));

// Mock react-dnd to capture specs and let tests trigger hover/collect/end
vi.mock('react-dnd', async (importOriginal) => {
  return {
    ...(await importOriginal()),
    useDrop: (spec: any) => {
      hoisted.dndApi.lastDropSpec = spec;
      // Return [collectedProps, dropRef]
      return [{}, (el: any) => el];
    },
    useDrag: (spec: any) => {
      hoisted.dndApi.lastDragSpec = spec;
      // Call collect initially with isDragging=false
      const collected = spec.collect ? spec.collect({ isDragging: () => false }) : {};
      // Return [collectedProps, dragRef]
      return [collected || {}, (el: any) => el];
    },
  };
});

// Stub store hooks
vi.mock('@/store/hooks', () => ({
  useAppDispatch: () => hoisted.dispatchMock,
  useAppSelector: vi.fn(),
}));

// Stub Mantine modals (not used in these tests but safe to mock)
vi.mock('@mantine/modals', async (importOriginal) => ({
  ...await importOriginal(),
  modals: { openConfirmModal: vi.fn() },
}));

// Stub styles hook to provide required classes
vi.mock('../ListPage', () => ({
  useListPageStyles: () => ({ classes: { editingInput: 'editingInput' } }),
}));

// Pass-through sanitizer
vi.mock('@resola-ai/ui/utils/string', () => ({
  cleanBadMarkdownContent: (v: string) => v,
}));

// Mock PageActions component
vi.mock('../PageActions', () => ({
  default: ({onDelete, onDuplicate}: {
    onDelete: () => void,
    onDuplicate: () => void,
    disableDelete: boolean
  }) => (
      <div>
        <div data-testid="delete-action" onClick={onDelete}>Delete</div>
        <div data-testid="duplicate-action" onClick={onDuplicate}>Duplicate</div>
      </div>
  ),
}));

vi.mock('@mantine/core', async (importOriginal) => ({
  ...await importOriginal(),
  Flex: ({children, ...otherProps}: { children: React.ReactNode }) => <div data-testid='PageItem' {...otherProps}>{children}</div>,
}));



describe('PageItem - drag and drop interactions', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    hoisted.dndApi.lastDropSpec = null;
    hoisted.dndApi.lastDragSpec = null;
  });

  const baseProps = (): any => ({
    isFullMode: true,
    page: { id: 'page-1', name: 'Page 1' },
    isActive: false,
    index: 1,
    visibleIndex: 1,
    ableToDelete: true,
    moveNavItem: vi.fn(),
  });

  it('calls moveNavItem on drop when dragged item index differs', () => {
    const props = baseProps();
    render(<PageItem {...props} />);

    expect(hoisted.dndApi.lastDropSpec).toBeTruthy();

    const draggedItem = { index: 0 };
    // Drop on index 1 with a dragged item from index 0
    hoisted.dndApi.lastDropSpec.drop(draggedItem);

    expect(props.moveNavItem).toHaveBeenCalledTimes(1);
    expect(props.moveNavItem).toHaveBeenCalledWith(0, 1);
  });

  it('dispatches setIsDragging(true) when collect reports dragging, and setIsDragging(false) on end', () => {
    render(<PageItem {...baseProps()} />);

    // Simulate collect reporting isDragging=true
    hoisted.dndApi.lastDragSpec.collect({ isDragging: () => true });
    // Expect an action with payload true dispatched
    expect(hoisted.dispatchMock).toHaveBeenCalledWith(
        expect.objectContaining({ payload: true })
    );

    // Simulate drag end
    hoisted.dndApi.lastDragSpec.end?.();
    // Expect an action with payload false dispatched
    expect(hoisted.dispatchMock).toHaveBeenCalledWith(
        expect.objectContaining({ payload: false })
    );
  });


  it('dispatches setAciveSection(id) when mouseDown', () => {
    render(<PageItem {...baseProps()} />);

    fireEvent.mouseDown(screen.getByTestId('PageItem'))
  })

  it('dispatches setIsRenamingPage(true) when doubleClick', () => {
    render(<PageItem {...baseProps()} />);

    fireEvent.doubleClick(screen.getByTestId('PageItem'))
  })

  it('calls openConfirmModal when delete action is clicked', () => {
    const props = baseProps();
    render(<PageItem {...props} />);

    fireEvent.click(screen.getByTestId('delete-action'));
  });

  it('dispatches duplicatePage action when duplicate action is clicked', () => {
    const props = baseProps();
    render(<PageItem {...props} />);

    fireEvent.click(screen.getByTestId('duplicate-action'));
  });

  it('dispatches setIsRenamingPage(true) when doubleClick', () => {
    const {container} = render(<PageItem {...baseProps()} />);

    fireEvent.doubleClick(screen.getByTestId('PageItem'))

    fireEvent.change(container.querySelector('input')!, {target: {value: 'test'}});
    fireEvent.blur(container.querySelector('input')!)

  })
});
