import BuilderNavigationBar from '@/components/Common/NavigationBar/BuilderNavigationBar';
import { useBuilderHistory } from '@/hooks/useBuilderHistory';
import  useFormAutoSave, { SKIP_CONVERT_KEYS } from '@/hooks/useFormAutoSave';
import { FormsAPI } from '@/services/api';
import { RESET_BUILDER_STORE, SET_ACTIVE_SECTION } from '@/store/action/actionTypes';
import { setFormInfo, setScreenshot } from '@/store/action/builder';
import { setHeaderNavigationShowing } from '@/store/action/headerNavigation';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { formPagesDataSelector } from '@/store/reducers/builder';
import {
  FieldType,
  type FormBuilderData,
  FormBuilderSteps,
  type FormHiddenSection,
  type FormSection,
} from '@/types';
import { showNotificationToast } from '@/utils/notification';
import { LoadingOverlay, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { modals } from '@mantine/modals';
import { logger } from '@resola-ai/services-shared';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import useSWR from 'swr';
import MainBuilderContainer from './MainBuilderContainer';

const useStyles = createStyles(() => ({
  builderContainer: {
    width: '100%',
    height: `calc(100vh - ${rem(60)})`,

    '& *': {
      scrollbarWidth: 'thin' /* Firefox */,
      scrollbarColor: 'darkgrey #f1f1f1' /* Firefox */,

      '&::-webkit-scrollbar': {
        width: '4px' /* Width of the scrollbar */,
      },

      '&::-webkit-scrollbar-thumb': {
        backgroundColor: 'darkgrey' /* Color of the scrollbar thumb */,
        borderRadius: '10px' /* Rounded corners */,
      },

      '&::-webkit-scrollbar-track': {
        background: '#f1f1f1' /* Color of the scrollbar track */,
      },
    },
  },
}));

const FormBuilderPage = () => {
  const { id } = useParams();
  const [step, setStep] = useState<FormBuilderSteps>(FormBuilderSteps.Create);
  const navigate = useNavigate();
  const { reset } = useBuilderHistory();
  //Auto save form
  const { classes } = useStyles();
  const dispatch = useAppDispatch();
  const formContent = useAppSelector(formPagesDataSelector);
  // get active step params
  const [searchParams, setSearchParams] = useSearchParams();
  const activeTab = searchParams.get('active_tab');
  const { t } = useTranslation(['common', 'form_builder']);
  useFormAutoSave({
    formId: id || '',
    onSaveFailed: () =>
      showNotificationToast({
        message: t('common.save.error', { ns: 'form_builder' }),
        isError: true,
      }),
  });

  useEffect(() => {
    if (
      activeTab &&
      Object.values(FormBuilderSteps).find((_) => _.toLowerCase() === activeTab.toLowerCase())
    ) {
      setStep(activeTab as FormBuilderSteps);
    }
  }, [activeTab]);

  const changeTabHandler = (tab: FormBuilderSteps) => {
    setStep(tab);
    setSearchParams({ active_tab: tab });
  };

  const { data, error, isValidating } = useSWR<(FormSection | FormHiddenSection)[]>(
    id ? `/form-builder/${id}` : null,
    () => FormsAPI.getFormForBuilder(id || '', { skipConvertKeys: SKIP_CONVERT_KEYS }),
    {
      revalidateOnMount: true,
    }
  );
  
  const { data: formData, mutate } = useSWR<FormBuilderData>(id ? `/forms/${id}` : null, () =>
    FormsAPI.getForm(id || '')
  );
  
  useEffect(() => {
    if (error) {
      logger.error('Error fetching form data:', error);

      modals.openConfirmModal({
        title: t('error'),
        children: t('formNotFound'),
        labels: { confirm: t('OK'), cancel: '' },
        confirmProps: { color: 'red' },
        cancelProps: { display: 'none' }, // Hide the cancel button
        withCloseButton: false,
        closeOnClickOutside: false,
        onConfirm: () => {
          navigate('/forms/');
        },
      });
    }
  }, [error]);

  useEffect(() => {
    if (!data?.length || isValidating) return;

    const newBuilderJson = {
      content: data,
    };
    dispatch(
      setFormInfo({
        content: data,
      })
    );
    if (newBuilderJson?.content?.length) {
      dispatch({
        type: SET_ACTIVE_SECTION,
        payload: newBuilderJson.content?.find((_) => _.type !== FieldType.Hidden)?.id,
      });
    }

    return () => {
      reset();
      dispatch({ type: RESET_BUILDER_STORE });
    };
  }, [data, isValidating]);

  useEffect(() => {
    dispatch(setHeaderNavigationShowing(false));
    mutate();
    return () => {
      dispatch(setHeaderNavigationShowing(true));
    };
  }, []);

  useEffect(() => {
    if (formData) {
      dispatch(setScreenshot(formData.screenshot));
      dispatch(setFormInfo(formData));
    }
  }, [formData]);

  if (isValidating) {
    return <LoadingOverlay visible={true} />;
  }

  return (
    <div className={classes.builderContainer}>
      <BuilderNavigationBar step={step} onStep={changeTabHandler} pages={formContent} />
      {formContent && <MainBuilderContainer step={step} formData={formData} />}
    </div>
  );
};

export default FormBuilderPage;
