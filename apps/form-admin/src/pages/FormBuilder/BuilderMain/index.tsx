import EditFormNavigationBar from '@/components/Common/NavigationBar/EditFormNavigationBar';
import { useResponseId } from '@/hooks';
import { ModeProvider } from '@/hooks/useScreenPreview';
import FormSettingsPage from '@/pages/FormSettings';
import { useAppSelector } from '@/store/hooks';
import { type FormBuilderData, FormBuilderSteps, BuilderRightMenuType } from '@/types';
import { Box, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useSearchParams } from 'react-router-dom';
import FormContainer from './FormContainer';
import { IntegrationsContainer } from './IntegrationsContainer';
import ResultContainer from './ResultContainer';
import ShareContainer from './ShareContainer';

const useStyles = createStyles(
  (_, { scrollY, padding }: { scrollY: boolean; padding: string | number }) => ({
    builderMain: {
      width: '100%',
      padding: padding,
      flex: 1,
      display: 'flex',
      flexDirection: 'column',
      overflowX: 'hidden',
      overflowY: scrollY ? 'auto' : 'hidden',
    },
  })
);

type Props = {
  step: FormBuilderSteps;
  formData?: FormBuilderData;
};

const BuilderMain = ({ step, formData }: Props) => {
  const [searchParams] = useSearchParams();
  const activeTab = searchParams.get('active_tab');
  const responseId = useResponseId();
  const rightMenuType = useAppSelector((state) => state.builder.rightMenuType);
  
  const { classes } = useStyles({
    scrollY: !responseId,
    padding: activeTab?.toLocaleLowerCase() === 'results' ? 0 : rem(12),
  });

  // Show map when PageLogic is selected
  const isMapView = rightMenuType === BuilderRightMenuType.PageLogic;

  const renderContainer = () => {
    switch (step) {
      case FormBuilderSteps.Create:
        return <FormContainer formData={formData} isMapView={isMapView} />;
      case FormBuilderSteps.Share:
        return <ShareContainer />;
      case FormBuilderSteps.Integrations:
        return <IntegrationsContainer />;
      case FormBuilderSteps.Settings:
        return <FormSettingsPage />;
      case FormBuilderSteps.Results:
        return <ResultContainer />;
      default:
        return null;
    }
  };

  return (
    <Box className={classes.builderMain}>
      {step === FormBuilderSteps.Create && <EditFormNavigationBar />}
      <ModeProvider>{renderContainer()}</ModeProvider>
    </Box>
  );
};

export default BuilderMain;
