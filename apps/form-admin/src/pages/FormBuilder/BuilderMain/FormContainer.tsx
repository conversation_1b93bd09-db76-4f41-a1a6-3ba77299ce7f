import FormBase from '@/components/CoreForm/FormBuilder/FormBase';
import FormFooter from '@/components/CoreForm/FormBuilder/FormHeaderFooter/FormFooter';
import FormHeader from '@/components/CoreForm/FormBuilder/FormHeaderFooter/FormHeader';
import FieldPreviewDragWrapper from '@/components/SectionSettings/FieldPreviewDragWrapper';
import { PageRelationMap } from '@/components/LogicSettings/PageRelationMap';
import { FORM_CONTAINER_ID } from '@/constants';
import { ModeContext } from '@/hooks/useScreenPreview';
import { useAppSelector } from '@/store/hooks';
import { selectingSectionSelector } from '@/store/reducers/builder';
import { FieldDragType, type FormIntegrations } from '@/types';
import { IntegrationType } from '@/types/enum';
import type { FormBuilderData, FormDataContent, FormSection } from '@/types/form-builder';
import { Card } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { showNotification } from '@mantine/notifications';
import { IconAlertTriangleFilled } from '@tabler/icons-react';
import { useContext, useEffect, useState } from 'react';
import { useDrop } from 'react-dnd';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import PageEmptyField from './PageEmptyField';

const useStyles = createStyles((theme) => ({
  formContainer: {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    width: '100%',
    flex: 1,
    textAlign: 'center',
    alignItems: 'center',
    overflow: 'auto',
  },
  toastRoot: {
    backgroundColor: theme.colors.decaDark[9],
  },
  toastDesc: {
    color: theme.colors.decaMono[1],
  },
  toastIcon: {
    background: 'transparent !important',
    color: theme.colors.decaYellow[5],
  },
}));

type Props = {
  formData?: FormBuilderData;
  isMapView?: boolean;
};

const FormContainer = ({ formData, isMapView }: Props) => {
  const { t } = useTranslation('form_builder');
  const { classes } = useStyles();
  const sectionFormData = useSelector(selectingSectionSelector) as FormSection;
  const builderJson = useAppSelector((state) => state.builder.builderJson);
  const currentHistoryKey = useAppSelector((state) => state.builder.currentHistoryKey);
  const {
    screenSize,
    // setDesktopMode
  } = useContext(ModeContext);
  const formSaving = useAppSelector((state) => state.builder.formSaving);
  const [isShowing, setIsShowing] = useState(true);

  const hasDecaIntegrated = formData?.integrations?.some(
    (integration: FormIntegrations) =>
      integration.type === IntegrationType.DecaCRM ||
      integration?.type === IntegrationType.DecaTable
  );

  useEffect(() => {
    if (hasDecaIntegrated && isShowing && formSaving) {
      showNotification({
        message: t('notificationForIntegrations'),
        icon: <IconAlertTriangleFilled size={16} />,
        autoClose: 7000,
        withCloseButton: true,
        classNames: {
          root: classes.toastRoot,
          description: classes.toastDesc,
          icon: classes.toastIcon,
        },
      });
      setIsShowing(false);
    }
  }, [formSaving, hasDecaIntegrated, isShowing]);

  // TODO: Implement drag and drop UI
  const [_, drop] = useDrop(() => ({
    accept: FieldDragType.DRAG_ADD_FIELD,
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  }));

  if (!builderJson) {
    return null;
  }

  const isShowEmptyFieldUI = !sectionFormData?.layout.imageUrl && !sectionFormData?.content?.length;

  // Show map view if requested
  if (isMapView) {
    return (
      <Card className={classes.formContainer} id={FORM_CONTAINER_ID}>
        <div style={{ width: '100%', height: '100%' }}>
          <PageRelationMap />
        </div>
      </Card>
    );
  }

  return (
    <Card className={classes.formContainer} ref={drop} id={FORM_CONTAINER_ID}>
      <div style={{ width: '100%', maxWidth: screenSize, height: '100%' }}>
        <FieldPreviewDragWrapper />

        {isShowEmptyFieldUI ? (
          <PageEmptyField />
        ) : (
          <>
            {/* Drag Preview */}
            <FormHeader />

            {sectionFormData ? (
              <FormBase
                key={currentHistoryKey}
                isRoot={true}
                content={[sectionFormData] as FormDataContent[]}
              />
            ) : (
              'Please Select page'
            )}
          </>
        )}
      </div>
      <FormFooter />
    </Card>
  );
};

export default FormContainer;
