import ShareContainer from '../index';
import { ShareAction } from '@/types';
import { render, cleanTextForTesting } from "@/utils/test-util/testUiComponent";

describe('ShareContainer', () => {

  it('renders Share component when actionType is Share', () => {

    const { getByText } = render(<ShareContainer />, {
      builder: {
        selectingShareSection: ShareAction.Share,
      }
    });
    // Use a more flexible text matcher to handle invisible characters
    expect(getByText((content) => {
      return cleanTextForTesting(content) === 'sharingForm';
    })).toBeInTheDocument();
  });

  it('renders Embed component when actionType is Embed', () => {

    const { getByText } = render(<ShareContainer />, {
      builder: {
        selectingShareSection: ShareAction.Embed,
      }
    });
    // Use a more flexible text matcher to handle invisible characters
    expect(getByText((content) => {
      return cleanTextForTesting(content) === 'embedWebPageLabel';
    })).toBeInTheDocument();
  });
});
