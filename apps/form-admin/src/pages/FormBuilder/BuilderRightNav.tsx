import AppearanceSettings from '@/components/AppearanceSettings/AppearanceSettings';
import FieldSettings from '@/components/FieldSettings';
import SectionSettings from '@/components/SectionSettings';
import { PageLogicSettings } from '@/components/LogicSettings/PageLogicSettings';
import EmbedSettings from '@/components/ShareSettings/Embed';
import ShareSettings from '@/components/ShareSettings/Share';
import { useAppSelector } from '@/store/hooks';
import { selectingFieldSelector } from '@/store/reducers/builder';
import { BuilderRightMenuType, FormBuilderSteps, ShareAction } from '@/types';
import { Card, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useSelector } from 'react-redux';
import clsx from 'clsx';

const useStyles = createStyles(() => ({
  builderSidebar: {
    background: 'white',
    width: rem(290),
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    alignItems: 'center',
    overflow: 'auto',
    '&.create': {
      paddingRight: 0
    }
  },
}));

type Props = {
  step: FormBuilderSteps;
};

const BuilderRightNav = ({ step }: Props) => {
  const { classes } = useStyles();
  const selectingField = useSelector(selectingFieldSelector);
  const rightMenuType = useAppSelector((state) => state.builder.rightMenuType);
  const { actionType } = useAppSelector((state) => ({
    actionType: state.builder.selectingShareSection,
  }));

  let RightComponent = SectionSettings;

  if (!selectingField) {
    const isAppearanceMode = rightMenuType === BuilderRightMenuType.Appearance;
    const isPageLogicMode = rightMenuType === BuilderRightMenuType.PageLogic;
    
    if (isAppearanceMode) {
      RightComponent = AppearanceSettings;
    } else if (isPageLogicMode) {
      RightComponent = PageLogicSettings;
    } else {
      RightComponent = SectionSettings;
    }
  } else {
    RightComponent = FieldSettings;
  }

  const renderRightComponent = () => {
    if (step === FormBuilderSteps.Create) {
      return <RightComponent />;
    }
    if (step === FormBuilderSteps.Share) {
      if (actionType === ShareAction.Share) {
        return <ShareSettings />;
      }
      if (actionType === ShareAction.Embed) {
        return <EmbedSettings />;
      }
    }
  };

  return (
    <Card className={clsx(classes.builderSidebar, step === FormBuilderSteps.Create && !selectingField && 'create')} id='right-sidebar'>
      {renderRightComponent()}
    </Card>
  );
};

export default BuilderRightNav;
