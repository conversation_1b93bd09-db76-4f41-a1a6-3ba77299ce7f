import type { SortOption } from '@/components/KBSort';
import { act, renderHook } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useKBSortLocalStorage } from '../useKBSortLocalStorage';

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key];
    }),
    clear: vi.fn(() => {
      store = {};
    }),
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

const KB_SORT_STORAGE_KEY = 'kb-article-collection-sort';

const DEFAULT_SORT_OPTION: SortOption = {
  field: 'createdDate',
  order: 'descending',
};

const CUSTOM_SORT_OPTION: SortOption = {
  field: 'title',
  order: 'ascending',
};

describe('useKBSortLocalStorage', () => {
  beforeEach(() => {
    localStorageMock.clear();
    vi.clearAllMocks();
  });

  describe('Initial State', () => {
    it('should initialize with default sort option when localStorage is empty', () => {
      const { result } = renderHook(() => useKBSortLocalStorage());

      expect(result.current.sortOption).toEqual(DEFAULT_SORT_OPTION);
      expect(result.current.isLoaded).toBe(true);
    });

    it('should load sort option from localStorage when available', () => {
      localStorageMock.setItem(KB_SORT_STORAGE_KEY, JSON.stringify(CUSTOM_SORT_OPTION));

      const { result } = renderHook(() => useKBSortLocalStorage());

      expect(result.current.sortOption).toEqual(CUSTOM_SORT_OPTION);
      expect(result.current.isLoaded).toBe(true);
    });

    it('should use default sort option when localStorage contains invalid JSON', () => {
      localStorageMock.setItem(KB_SORT_STORAGE_KEY, 'invalid-json');
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      const { result } = renderHook(() => useKBSortLocalStorage());

      expect(result.current.sortOption).toEqual(DEFAULT_SORT_OPTION);
      expect(result.current.isLoaded).toBe(true);
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to parse KBSort from localStorage:',
        expect.any(Error)
      );
      expect(localStorageMock.removeItem).toHaveBeenCalledWith(KB_SORT_STORAGE_KEY);

      consoleSpy.mockRestore();
    });

    it('should use default sort option when localStorage contains incomplete data', () => {
      localStorageMock.setItem(KB_SORT_STORAGE_KEY, JSON.stringify({ field: 'title' }));

      const { result } = renderHook(() => useKBSortLocalStorage());

      expect(result.current.sortOption).toEqual(DEFAULT_SORT_OPTION);
      expect(result.current.isLoaded).toBe(true);
      expect(localStorageMock.removeItem).toHaveBeenCalledWith(KB_SORT_STORAGE_KEY);
    });
  });

  describe('updateSortOption', () => {
    it('should update sort option and save to localStorage', () => {
      const { result } = renderHook(() => useKBSortLocalStorage());

      act(() => {
        result.current.updateSortOption(CUSTOM_SORT_OPTION);
      });

      expect(result.current.sortOption).toEqual(CUSTOM_SORT_OPTION);
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        KB_SORT_STORAGE_KEY,
        JSON.stringify(CUSTOM_SORT_OPTION)
      );
    });

    it('should update state even when localStorage fails', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      localStorageMock.setItem.mockImplementationOnce(() => {
        throw new Error('localStorage error');
      });

      const { result } = renderHook(() => useKBSortLocalStorage());

      act(() => {
        result.current.updateSortOption(CUSTOM_SORT_OPTION);
      });

      expect(result.current.sortOption).toEqual(CUSTOM_SORT_OPTION);
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to save KBSort to localStorage:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('clearSortOption', () => {
    it('should clear sort option and reset to default', () => {
      // First set a custom sort option
      const { result } = renderHook(() => useKBSortLocalStorage());

      act(() => {
        result.current.updateSortOption(CUSTOM_SORT_OPTION);
      });

      expect(result.current.sortOption).toEqual(CUSTOM_SORT_OPTION);

      // Then clear it
      act(() => {
        result.current.clearSortOption();
      });

      expect(result.current.sortOption).toEqual(DEFAULT_SORT_OPTION);
      expect(localStorageMock.removeItem).toHaveBeenCalledWith(KB_SORT_STORAGE_KEY);
    });

    it('should reset to default even when localStorage fails', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      localStorageMock.removeItem.mockImplementationOnce(() => {
        throw new Error('localStorage error');
      });

      const { result } = renderHook(() => useKBSortLocalStorage());

      // First set a custom sort option
      act(() => {
        result.current.updateSortOption(CUSTOM_SORT_OPTION);
      });

      // Then clear it
      act(() => {
        result.current.clearSortOption();
      });

      expect(result.current.sortOption).toEqual(DEFAULT_SORT_OPTION);
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to clear KBSort from localStorage:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Persistence', () => {
    it('should persist sort option across hook instances', () => {
      // First hook instance
      const { result: result1 } = renderHook(() => useKBSortLocalStorage());

      act(() => {
        result1.current.updateSortOption(CUSTOM_SORT_OPTION);
      });

      // Second hook instance (simulating component remount)
      const { result: result2 } = renderHook(() => useKBSortLocalStorage());

      expect(result2.current.sortOption).toEqual(CUSTOM_SORT_OPTION);
      expect(result2.current.isLoaded).toBe(true);
    });

    it('should handle multiple sort field types correctly', () => {
      const { result } = renderHook(() => useKBSortLocalStorage());

      const testCases: SortOption[] = [
        { field: 'createdDate', order: 'ascending' },
        { field: 'updatedDate', order: 'descending' },
        { field: 'title', order: 'ascending' },
      ];

      testCases.forEach((sortOption) => {
        act(() => {
          result.current.updateSortOption(sortOption);
        });

        expect(result.current.sortOption).toEqual(sortOption);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle localStorage getItem errors gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      localStorageMock.getItem.mockImplementationOnce(() => {
        throw new Error('localStorage error');
      });

      const { result } = renderHook(() => useKBSortLocalStorage());

      expect(result.current.sortOption).toEqual(DEFAULT_SORT_OPTION);
      expect(result.current.isLoaded).toBe(true);
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to parse KBSort from localStorage:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });
});
