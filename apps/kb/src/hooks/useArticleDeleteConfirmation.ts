import { useAppContext } from '@/contexts';
import { use<PERSON><PERSON><PERSON>and<PERSON> } from '@/hooks';
import { ArticleAPI } from '@/services/api/v2';
import type { Article } from '@/types';
import { useTranslate } from '@tolgee/react';
import { useCallback } from 'react';

export const useArticleDeleteConfirmation = () => {
  const { t } = useTranslate('article');
  const { handleApiRequest } = useApiHandler();
  const { openConfirmModal, closeConfirmModal } = useAppContext();

  /**
   * Delete Article by KB ID and Article ID
   * @param {string} kbId
   * @param {string} articleId
   * @returns {Promise<any>}
   */
  const deleteArticle = useCallback(async (kbId: string, articleId: string) => {
    return ArticleAPI.delete(kbId, articleId);
  }, []);

  /**
   * Confirm and Delete Article
   * @param {Article} article
   * @param {Function} onDeleted
   * @param {Function} onLoading
   * @returns {Promise<void>}
   */
  const confirmThenDeleteArticle = useCallback(
    async (article: Article, onDeleted?: () => void, onLoading?: (isLoading: boolean) => void) => {
      // Check if article has shortcuts to other articles
      const hasShortcut =
        Array.isArray(article.shortcutArticleIds) && article.shortcutArticleIds.length > 0;

      openConfirmModal({
        alignment: 'left',
        onConfirm: async () => {
          onLoading?.(true);
          closeConfirmModal();

          await handleApiRequest(deleteArticle(article.baseId, article.id), {
            fallbackMessage: t('articleCollection.delete.failed.description'),
            fallbackTitle: t('articleCollection.delete.failed.title'),
            successMessage: t('articleCollection.delete.success.description'),
            successTitle: t('articleCollection.delete.success.title'),
            disableRedirect: true, // Disable 404 redirect for DELETE operations
            successCallback: () => {
              onDeleted?.();
              onLoading?.(false);
            },
            errorCallback: () => {
              onLoading?.(false);
            },
          });
        },
        onCancel: () => {
          closeConfirmModal();
          onLoading?.(false);
        },
        title: t('articleCollection.delete.confirmTitle'),
        content: hasShortcut
          ? t('articleCollection.delete.confirmContentWithShortcut')
          : t('articleCollection.delete.confirmContent'),
        options: {
          isRemoving: true,
        },
      });
    },
    [t, openConfirmModal, closeConfirmModal, handleApiRequest, deleteArticle]
  );

  return {
    confirmThenDeleteArticle,
  };
};
