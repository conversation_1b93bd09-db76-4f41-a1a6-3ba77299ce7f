import type { SortOption } from '@/components/KBSort';
import { useCallback, useEffect, useState } from 'react';

// Local storage key for KBSort state in Article Collection
const KB_SORT_STORAGE_KEY = 'kb-article-collection-sort';

// Default sort option
const DEFAULT_SORT_OPTION: SortOption = {
  field: 'createdDate',
  order: 'descending',
};

/**
 * Custom hook for managing KBSort state with localStorage persistence
 * Specifically designed for Article Collection page
 *
 * Features:
 * - Persists sort state to localStorage
 * - Loads sort state from localStorage on initialization
 * - Provides methods to update and clear sort state
 * - Handles JSON serialization/deserialization safely
 */
export const useKBSortLocalStorage = () => {
  const [sortOption, setSortOption] = useState<SortOption>(DEFAULT_SORT_OPTION);
  const [isLoaded, setIsLoaded] = useState(false);

  /**
   * Load sort option from localStorage
   */
  const loadSortFromStorage = useCallback(() => {
    try {
      const storedSort = localStorage.getItem(KB_SORT_STORAGE_KEY);
      if (storedSort) {
        const parsedSort = JSON.parse(storedSort) as SortOption;
        // Validate the parsed data has required fields
        if (parsedSort.field && parsedSort.order) {
          setSortOption(parsedSort);
        } else {
          // If invalid data, use default and clear storage
          setSortOption(DEFAULT_SORT_OPTION);
          localStorage.removeItem(KB_SORT_STORAGE_KEY);
        }
      } else {
        setSortOption(DEFAULT_SORT_OPTION);
      }
    } catch (error) {
      // If parsing fails, use default and clear storage
      console.warn('Failed to parse KBSort from localStorage:', error);
      setSortOption(DEFAULT_SORT_OPTION);
      localStorage.removeItem(KB_SORT_STORAGE_KEY);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  /**
   * Save sort option to localStorage
   */
  const saveSortToStorage = useCallback((newSortOption: SortOption) => {
    try {
      localStorage.setItem(KB_SORT_STORAGE_KEY, JSON.stringify(newSortOption));
      setSortOption(newSortOption);
    } catch (error) {
      console.warn('Failed to save KBSort to localStorage:', error);
      // Still update state even if localStorage fails
      setSortOption(newSortOption);
    }
  }, []);

  /**
   * Update sort option and persist to localStorage
   */
  const updateSortOption = useCallback(
    (newSortOption: SortOption) => {
      saveSortToStorage(newSortOption);
    },
    [saveSortToStorage]
  );

  /**
   * Clear sort option from localStorage and reset to default
   */
  const clearSortOption = useCallback(() => {
    try {
      localStorage.removeItem(KB_SORT_STORAGE_KEY);
      setSortOption(DEFAULT_SORT_OPTION);
    } catch (error) {
      console.warn('Failed to clear KBSort from localStorage:', error);
      // Still reset to default even if localStorage fails
      setSortOption(DEFAULT_SORT_OPTION);
    }
  }, []);

  /**
   * Load sort option from localStorage on mount
   */
  useEffect(() => {
    loadSortFromStorage();
  }, [loadSortFromStorage]);

  return {
    sortOption,
    updateSortOption,
    clearSortOption,
    isLoaded, // Indicates if the initial load from localStorage is complete
  };
};

export default useKBSortLocalStorage;
