import { JOB_TAB_VALUES } from '@/constants/job';
import { JobAPI } from '@/services/api/v2';
import {
  type IJobsRequestParams,
  type Job,
  type JobTabValue,
  JobType,
  KBDirectionQueryEnum,
  type KnowledgeBaseDirectionQuery,
} from '@/types';
import { getPaginationWithDirection } from '@/utils/api';
import { sortByCreatedAt } from '@/utils/dateTime';
import type { IPaginationNextPrevious } from '@resola-ai/models';
import { createContextHook } from '@resola-ai/utils';
import { useCallback, useState } from 'react';

interface ITabData {
  jobs: Job[];
  pagination: IPaginationNextPrevious;
  direction: KnowledgeBaseDirectionQuery;
  hasMoreData: boolean;
  lastFetched: number; // timestamp for cache validation
}

interface IJobContextState {
  jobsLoading: boolean;
  currentTabType: JobTabValue;
  tabDataCache: Partial<Record<JobTabValue, ITabData>>;
}

/**
 * Helper function to get job types based on tab value
 */
const getJobTypesByTab = (tabType: JobTabValue): JobType[] => {
  switch (tabType) {
    case JOB_TAB_VALUES.GENERATION:
      return [JobType.ContentGeneration];
    case JOB_TAB_VALUES.EXPORT:
      return [JobType.ArticleExport];
    case JOB_TAB_VALUES.ALL:
    default:
      return [JobType.ContentGeneration, JobType.ArticleExport];
  }
};

// Cache duration in milliseconds (5 minutes)
const CACHE_DURATION = 5 * 60 * 1000;

const useJob = () => {
  const [jobsState, setJobsState] = useState<IJobContextState>({
    jobsLoading: false,
    currentTabType: JOB_TAB_VALUES.ALL,
    tabDataCache: {},
  });

  // Helper function to check if cache is valid
  const isCacheValid = useCallback((tabData: ITabData | undefined): boolean => {
    if (!tabData) return false;
    return Date.now() - tabData.lastFetched < CACHE_DURATION;
  }, []);

  // Helper function to get current tab data
  const getCurrentTabData = useCallback((): ITabData | undefined => {
    return jobsState.tabDataCache[jobsState.currentTabType];
  }, [jobsState.tabDataCache, jobsState.currentTabType]);

  /**
   * Fetch jobs for a specific tab type with caching
   * @param {JobTabValue} tabType - The tab type to fetch jobs for
   * @param {boolean} forceRefresh - Whether to force refresh cache (default: false)
   * @returns {Promise<any>}
   */
  const fetchJobs = useCallback(
    async (tabType: JobTabValue = JOB_TAB_VALUES.ALL, forceRefresh = false) => {
      // Check if we can use cached data
      const cachedData = jobsState.tabDataCache[tabType];
      const canUseCache = !forceRefresh && isCacheValid(cachedData);

      // If switching to a tab with valid cached data, use it immediately
      if (canUseCache && cachedData) {
        setJobsState((prev) => ({
          ...prev,
          currentTabType: tabType,
        }));
        return;
      }

      setJobsState((prev) => ({
        ...prev,
        jobsLoading: true,
        currentTabType: tabType,
      }));

      const jobTypes = getJobTypesByTab(tabType);
      const params: IJobsRequestParams = {
        jobType: jobTypes,
        direction: KBDirectionQueryEnum.Backward,
        cursor: '',
      };

      try {
        const res = await JobAPI.getJobs(params);

        if (res?.status === 'success') {
          const hasMore = res?.pagination?.hasNextPage || res?.pagination?.hasPreviousPage || false;
          const sortedJobs = sortByCreatedAt(res?.data ?? []);

          const newTabData: ITabData = {
            jobs: sortedJobs,
            pagination: res?.pagination ?? {
              first: '',
              last: '',
              hasNextPage: false,
              hasPreviousPage: false,
            },
            direction: params.direction ?? KBDirectionQueryEnum.Backward,
            hasMoreData: hasMore,
            lastFetched: Date.now(),
          };

          setJobsState((prev) => ({
            ...prev,
            tabDataCache: {
              ...prev.tabDataCache,
              [tabType]: newTabData,
            },
            jobsLoading: false,
          }));
        } else {
          setJobsState((prev) => ({ ...prev, jobsLoading: false }));
        }

        return res;
      } catch (error) {
        setJobsState((prev) => ({ ...prev, jobsLoading: false }));
        throw error;
      }
    },
    [jobsState.tabDataCache, isCacheValid]
  );

  /**
   * Fetch more jobs with infinite scrolling for current tab
   * @returns {Promise<any>}
   */
  const fetchMoreJobs = useCallback(async () => {
    const currentTabData = getCurrentTabData();

    // Check if there's more data to load
    if (!currentTabData?.hasMoreData || jobsState.jobsLoading) {
      return;
    }

    const { cursor, direction, hasMoreData } = getPaginationWithDirection(
      currentTabData.pagination,
      currentTabData.direction
    );

    if (!hasMoreData) {
      return;
    }

    setJobsState((prev) => ({ ...prev, jobsLoading: true }));

    const jobTypes = getJobTypesByTab(jobsState.currentTabType);
    const params: IJobsRequestParams = {
      jobType: jobTypes,
      cursor,
      direction,
    };

    try {
      const res = await JobAPI.getJobs(params);

      if (res?.status === 'success') {
        const newJobs = res?.data ?? [];
        const hasMore = res?.pagination?.hasNextPage || res?.pagination?.hasPreviousPage || false;

        setJobsState((prev) => {
          const currentData = prev.tabDataCache[prev.currentTabType];
          if (!currentData) return prev;

          const updatedTabData: ITabData = {
            ...currentData,
            jobs: [...currentData.jobs, ...sortByCreatedAt(newJobs)],
            pagination: res?.pagination ?? currentData.pagination,
            direction,
            hasMoreData: hasMore && newJobs.length > 0,
            lastFetched: Date.now(),
          };

          return {
            ...prev,
            tabDataCache: {
              ...prev.tabDataCache,
              [prev.currentTabType]: updatedTabData,
            },
            jobsLoading: false,
          };
        });

        return res;
      }

      setJobsState((prev) => ({ ...prev, jobsLoading: false }));
      return res;
    } catch (error) {
      setJobsState((prev) => ({ ...prev, jobsLoading: false }));
      throw error;
    }
  }, [getCurrentTabData, jobsState.jobsLoading, jobsState.currentTabType]);

  /**
   * Delete a job and update all relevant caches
   * @param {string} jobId
   * @returns {Promise<any>}
   */
  const deleteJob = useCallback(async (jobId: string) => {
    const res = await JobAPI.deleteJob(jobId);
    if (res?.status === 'success') {
      setJobsState((prev) => {
        const updatedCache = { ...prev.tabDataCache };

        // Remove the job from all tab caches
        Object.keys(updatedCache).forEach((tabType) => {
          const tabData = updatedCache[tabType as JobTabValue];
          if (tabData) {
            updatedCache[tabType as JobTabValue] = {
              ...tabData,
              jobs: tabData.jobs.filter((job) => job.id !== jobId),
              lastFetched: Date.now(), // Update cache timestamp
            };
          }
        });

        return {
          ...prev,
          tabDataCache: updatedCache,
        };
      });
    }
    return res;
  }, []);

  /**
   * Invalidate cache for all tabs (use when new jobs are created)
   */
  const invalidateCache = useCallback(() => {
    setJobsState((prev) => ({
      ...prev,
      tabDataCache: {} as Partial<Record<JobTabValue, ITabData>>,
    }));
  }, []);

  /**
   * Refresh current tab data (force refresh)
   */
  const refreshCurrentTab = useCallback(() => {
    return fetchJobs(jobsState.currentTabType, true);
  }, [fetchJobs, jobsState.currentTabType]);

  // Get current tab data for consumers
  const currentTabData = getCurrentTabData();

  return {
    fetchJobs,
    fetchMoreJobs,
    deleteJob,
    invalidateCache,
    refreshCurrentTab,
    jobs: currentTabData?.jobs ?? [],
    jobsLoading: jobsState.jobsLoading,
    capturedPagination: currentTabData?.pagination ?? {
      first: '',
      last: '',
      hasNextPage: false,
      hasPreviousPage: false,
    },
    currentTabType: jobsState.currentTabType,
    hasMoreData: currentTabData?.hasMoreData ?? false,
  };
};

export const [JobContextProvider, useJobContext, JobContext] = createContextHook(
  useJob,
  'JobContext'
);
