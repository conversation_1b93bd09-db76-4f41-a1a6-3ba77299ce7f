import { DEFAULT_DRAWER_SCROLL_DURATION, DEFAULT_DRAWER_SCROLL_OFFSET } from '@/constants/ui';
import { useApiHandler } from '@/hooks';
import { useArticleDeleteConfirmation } from '@/hooks/useArticleDeleteConfirmation';
import { ArticleAPI } from '@/services/api/v2';
import { ScheduleAPI } from '@/services/api/v2/schedule';
import { VoteAPI } from '@/services/api/v2/vote';
import type { Article, ArticleVote, SchedulePayload, User, VoteType } from '@/types';
import { convertKeysToCamelCase } from '@/utils/article';
import { useDebouncedState, useResizeObserver, useScrollIntoView } from '@mantine/hooks';
import { createContextHook } from '@resola-ai/utils';
import { useCallback, useEffect, useState } from 'react';

const useArticleDetail = () => {
  const {
    scrollIntoView: scrollToTopOfArticle,
    targetRef: topOfArticleRef,
    scrollableRef: articleViewerScrollableRef,
  } = useScrollIntoView<HTMLDivElement, HTMLDivElement>({
    offset: DEFAULT_DRAWER_SCROLL_OFFSET,
    duration: DEFAULT_DRAWER_SCROLL_DURATION,
  });
  const { handleApiRequest } = useApiHandler();
  const [isDetailLoading, setIsDetailLoading] = useState<boolean>(false);
  const [isArticleChanged, setIsArticleChanged] = useState<boolean>(false);
  const [currentArticle, setCurrentArticle] = useState<Article | null | undefined>(undefined);
  const [articleVote, setArticleVote] = useState<ArticleVote | null>(null);
  const [isArticleVoting, setIsArticleVoting] = useState<boolean>(false);
  const [articleSaving, setArticleSaving] = useDebouncedState(false, 200);
  const [articleContentRef, articleContentRect] = useResizeObserver();

  const { confirmThenDeleteArticle } = useArticleDeleteConfirmation();

  /**
   * Get Article Detail
   * @param {string} baseId
   * @param {string} articleId
   * @dependencies ArticleAPI.get: function
   */
  const getArticleById = useCallback(async (articleId: string) => {
    setIsDetailLoading(true);
    try {
      const articleResponse = await ArticleAPI.get(articleId);

      if (articleResponse?.data) {
        const articleData = articleResponse.data;

        setCurrentArticle({
          ...articleData,
          createdBy: convertKeysToCamelCase(articleData.createdBy) as User,
        });
      }

      setIsDetailLoading(false);
    } catch (error) {
      setIsDetailLoading(false);
      setCurrentArticle(null);
    }
  }, []);

  /**
   * Update Article
   * @param {string} kbId
   * @param {string} articleId
   * @param {any} payload
   * @dependencies ArticleAPI.update: function, getCollection: function
   * @returns {void}
   */
  const updateArticle = useCallback(async (articleId: string, payload: any) => {
    return ArticleAPI.update(articleId, payload);
  }, []);

  /**
   * Upload File in Article
   * @param {string} baseId
   * @param {string} articleId
   * @param {File} file
   */
  const uploadFileInArticle = useCallback(async (baseId: string, articleId: string, file: File) => {
    const response = await ArticleAPI.uploadFile(baseId, articleId, file);
    return response?.data;
  }, []);

  /**
   * Upload File When Creating Article
   * @param {string} baseId
   * @param {File} file
   * @returns {Promise<any>}
   */
  const uploadFileWhenCreatingArticle = useCallback(async (baseId: string, file: File) => {
    const response = await ArticleAPI.uploadFileWhenCreating(baseId, file);
    return response?.data;
  }, []);

  /**
   * Update Article Vote
   * Handles user voting action (like/dislike/neutral) for an article
   * @param {string} articleId - ID of the article to vote on
   * @param {VoteType} type - Type of vote (LIKE, DISLIKE, or NEUTRAL)
   * @returns {Promise} - API response from vote update
   */
  const updateArticleVote = useCallback(async (articleId: string, type: VoteType) => {
    return VoteAPI.updateArticleVote(articleId, type);
  }, []);

  /**
   * Create Shortcut
   * Creates a reference to an article in another knowledge base
   * Sets loading state during the API request
   * @param {string} articleId - ID of the source article
   * @param {string} baseId - ID of the target knowledge base
   * @returns {Promise<any>} - API response from shortcut creation
   */
  const createShortcut = useCallback(async (articleId: string, baseId: string) => {
    setIsDetailLoading(true);
    const response = await ArticleAPI.createShortcut(articleId, baseId);
    setIsDetailLoading(false);

    return response;
  }, []);

  /**
   * Duplicate Article
   * @param {string} articleId
   * @param {string} baseId
   * @returns {Promise<any>} - API response from article duplication
   */
  const duplicateArticle = useCallback(async (articleId: string, baseId: string) => {
    setIsDetailLoading(true);
    try {
      const response = await ArticleAPI.duplicate(articleId, baseId);
      setIsDetailLoading(false);
      return response;
    } catch (error) {
      setIsDetailLoading(false);
      throw error;
    }
  }, []);

  /**
   * Fetch Article Vote Information
   * Retrieves the current vote status (likes/dislikes) for an article
   * Uses origin article ID for shortcuts to get votes from the original article
   */
  const fetchArticleVote = useCallback(async () => {
    if (currentArticle && !isArticleVoting) {
      const articleId =
        currentArticle.isShortcut && currentArticle.originArticleId
          ? currentArticle.originArticleId
          : currentArticle.id;

      const response = await VoteAPI.getArticleVote(articleId);
      if (response?.status === 'success') {
        setArticleVote(response.data);
      } else {
        setArticleVote(null);
      }
    }
  }, [currentArticle, isArticleVoting]);

  /**
   * Delete Article
   * Handles deletion of an article with confirmation dialog
   * @param {Article} article - Article object to delete
   * @param {Function} onDeleted - Optional callback after successful deletion
   */
  const deleteArticle = useCallback(
    (article: Article, onDeleted?: () => void) => {
      confirmThenDeleteArticle(article, onDeleted, setIsDetailLoading);
    },
    [confirmThenDeleteArticle]
  );

  /**
   * Schedule Article Publish
   * Schedules an article to be published or unpublished at a specific date and time
   * @param {string} articleId - ID of the article to schedule
   * @param {SchedulePayload} payload - Schedule configuration including scheduleType, dateTime, and timezone
   * @returns {Promise<any>} - API response from schedule operation
   */
  const schedulePublish = useCallback(
    async (articleId: string, payload: SchedulePayload) => {
      setIsDetailLoading(true);
      try {
        const response = await handleApiRequest(ArticleAPI.schedulePublish(articleId, payload));
        setIsDetailLoading(false);
        return response;
      } catch (error) {
        setIsDetailLoading(false);
        throw error;
      }
    },
    [handleApiRequest]
  );

  /**
   * Cancel Schedule
   * Cancels an existing schedule for an article
   * @param {string} scheduleId - ID of the schedule to cancel
   * @returns {Promise<any>} - API response from cancel operation
   */
  const cancelSchedule = useCallback(
    async (scheduleId: string) => {
      setIsDetailLoading(true);
      try {
        const response = await handleApiRequest(ScheduleAPI.cancel(scheduleId));
        setIsDetailLoading(false);
        return response;
      } catch (error) {
        setIsDetailLoading(false);
        throw error;
      }
    },
    [handleApiRequest]
  );

  // Fetch article vote data whenever the current article changes
  // or when voting status changes (to refresh vote counts)
  useEffect(() => {
    fetchArticleVote();
  }, [currentArticle, isArticleVoting, fetchArticleVote]);

  return {
    articleViewer: {
      scrollToTopOfArticle,
      topOfArticleRef,
      articleViewerScrollableRef,
    },
    articleContentRef,
    articleContentRect,
    isDetailLoading,
    setIsDetailLoading,
    isArticleChanged,
    setIsArticleChanged,
    currentArticle,
    getArticleById,
    updateArticle,
    updateArticleVote,
    isArticleVoting,
    setIsArticleVoting,
    articleVote,
    uploadFileInArticle,
    uploadFileWhenCreatingArticle,
    articleSaving,
    setArticleSaving,
    deleteArticle,
    createShortcut,
    duplicateArticle,
    schedulePublish,
    cancelSchedule,
  };
};

export type ArticleDetailContextType = ReturnType<typeof useArticleDetail>;

export const [ArticleDetailContextProvider, useArticleDetailContext, ArticleDetailContext] =
  createContextHook(useArticleDetail, 'ArticleDetail');
