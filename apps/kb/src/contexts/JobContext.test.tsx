import { JOB_TAB_VALUES } from '@/constants/job';
import { JobAPI } from '@/services/api/v2';
import { JOB_STATUS, JobType } from '@/types/job';
import { renderHook, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { JobContextProvider, useJobContext } from './JobContext';

// Mock Date.now for consistent testing
const mockDateNow = vi.fn();
const OriginalDate = Date;
vi.stubGlobal(
  'Date',
  class extends OriginalDate {
    static now = mockDateNow;
  }
);

// Mock the JobAPI
vi.mock('@/services/api/v2', () => ({
  JobAPI: {
    getJobs: vi.fn(),
    deleteJob: vi.fn(),
  },
}));

// Mock utility functions
vi.mock('@/utils/api', () => ({
  getPaginationWithDirection: vi.fn(() => ({
    cursor: 'test-cursor',
    direction: 'backward',
    hasMoreData: true,
  })),
}));

vi.mock('@/utils/dateTime', () => ({
  sortByCreatedAt: vi.fn((jobs) => jobs),
}));

// Test data constants
const MOCK_JOBS_RESPONSE = {
  status: 'success',
  data: [
    {
      id: 'job-1',
      name: 'Test Job 1',
      jobType: JobType.ContentGeneration,
      status: JOB_STATUS.succeeded,
      createdAt: new Date('2024-01-01'),
      initiatedBy: 'user-1',
      baseName: 'Test Job',
      error: '',
      config: { schedule: {}, retriable: true },
      metadata: { documentIds: ['doc-1'] },
      data: {},
      createdBy: {
        id: 'user-1',
        orgId: 'org-1',
        createdAt: '2024-01-01T00:00:00Z',
        displayName: 'Test User',
        email: '<EMAIL>',
        familyName: 'User',
        givenName: 'Test',
        picture: '',
        updatedAt: '2024-01-01T00:00:00Z',
      },
    },
  ],
  pagination: {
    first: 'first-cursor',
    last: 'last-cursor',
    hasNextPage: true,
    hasPreviousPage: false,
  },
};

const MOCK_MORE_JOBS_RESPONSE = {
  status: 'success',
  data: [
    {
      id: 'job-2',
      name: 'Test Job 2',
      jobType: JobType.ArticleExport,
      status: JOB_STATUS.succeeded,
      createdAt: new Date('2024-01-02'),
      initiatedBy: 'user-1',
      baseName: 'Test Job 2',
      error: '',
      config: { schedule: {}, retriable: true },
      metadata: { documentIds: ['doc-2'] },
      data: {},
      createdBy: {
        id: 'user-1',
        orgId: 'org-1',
        createdAt: '2024-01-01T00:00:00Z',
        displayName: 'Test User',
        email: '<EMAIL>',
        familyName: 'User',
        givenName: 'Test',
        picture: '',
        updatedAt: '2024-01-01T00:00:00Z',
      },
    },
  ],
  pagination: {
    first: 'first-cursor-2',
    last: 'last-cursor-2',
    hasNextPage: false,
    hasPreviousPage: true,
  },
};

describe('JobContext', () => {
  const mockGetJobs = vi.mocked(JobAPI.getJobs);
  const mockDeleteJob = vi.mocked(JobAPI.deleteJob);

  beforeEach(() => {
    vi.clearAllMocks();
    mockDateNow.mockReturnValue(1000000); // Fixed timestamp for testing
  });

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <JobContextProvider>{children}</JobContextProvider>
  );

  describe('fetchJobs', () => {
    it('fetches jobs for ALL tab with correct job types', async () => {
      mockGetJobs.mockResolvedValueOnce(MOCK_JOBS_RESPONSE);

      const { result } = renderHook(() => useJobContext(), { wrapper });

      await result.current.fetchJobs(JOB_TAB_VALUES.ALL);

      expect(mockGetJobs).toHaveBeenCalledWith({
        jobType: [JobType.ContentGeneration, JobType.ArticleExport],
        direction: 'backward',
        cursor: '',
      });

      await waitFor(() => {
        expect(result.current.jobs).toEqual(MOCK_JOBS_RESPONSE.data);
        expect(result.current.currentTabType).toBe(JOB_TAB_VALUES.ALL);
        expect(result.current.hasMoreData).toBe(true);
      });
    });

    it('fetches jobs for GENERATION tab with correct job types', async () => {
      mockGetJobs.mockResolvedValueOnce(MOCK_JOBS_RESPONSE);

      const { result } = renderHook(() => useJobContext(), { wrapper });

      await result.current.fetchJobs(JOB_TAB_VALUES.GENERATION);

      expect(mockGetJobs).toHaveBeenCalledWith({
        jobType: [JobType.ContentGeneration],
        direction: 'backward',
        cursor: '',
      });

      await waitFor(() => {
        expect(result.current.currentTabType).toBe(JOB_TAB_VALUES.GENERATION);
      });
    });

    it('fetches jobs for EXPORT tab with correct job types', async () => {
      mockGetJobs.mockResolvedValueOnce(MOCK_JOBS_RESPONSE);

      const { result } = renderHook(() => useJobContext(), { wrapper });

      await result.current.fetchJobs(JOB_TAB_VALUES.EXPORT);

      expect(mockGetJobs).toHaveBeenCalledWith({
        jobType: [JobType.ArticleExport],
        direction: 'backward',
        cursor: '',
      });

      await waitFor(() => {
        expect(result.current.currentTabType).toBe(JOB_TAB_VALUES.EXPORT);
      });
    });

    it('uses cached data for subsequent calls to same tab', async () => {
      mockGetJobs.mockResolvedValue(MOCK_JOBS_RESPONSE);

      const { result } = renderHook(() => useJobContext(), { wrapper });

      // First call should make API request
      await result.current.fetchJobs(JOB_TAB_VALUES.ALL);

      await waitFor(() => {
        expect(result.current.jobs).toBeDefined();
      });

      // Second call with same tab should use cache (no forceRefresh)
      await result.current.fetchJobs(JOB_TAB_VALUES.ALL);

      expect(mockGetJobs).toHaveBeenCalledTimes(1);
    });

    it('forces refresh when forceRefresh is true', async () => {
      mockGetJobs.mockResolvedValue(MOCK_JOBS_RESPONSE);

      const { result } = renderHook(() => useJobContext(), { wrapper });

      // First call
      await result.current.fetchJobs(JOB_TAB_VALUES.ALL);

      await waitFor(() => {
        expect(result.current.jobs).toBeDefined();
      });

      // Second call with forceRefresh should make another API request
      await result.current.fetchJobs(JOB_TAB_VALUES.ALL, true);

      expect(mockGetJobs).toHaveBeenCalledTimes(2);
    });

    it('handles API error gracefully', async () => {
      mockGetJobs.mockResolvedValueOnce({
        status: 'error',
        data: [],
        pagination: { first: '', last: '', hasNextPage: false, hasPreviousPage: false },
      });

      const { result } = renderHook(() => useJobContext(), { wrapper });

      await result.current.fetchJobs(JOB_TAB_VALUES.ALL);

      await waitFor(() => {
        expect(result.current.jobsLoading).toBe(false);
        expect(result.current.jobs).toEqual([]);
      });
    });
  });

  describe('fetchMoreJobs', () => {
    it('fetches more jobs with correct pagination', async () => {
      mockGetJobs
        .mockResolvedValueOnce(MOCK_JOBS_RESPONSE)
        .mockResolvedValueOnce(MOCK_MORE_JOBS_RESPONSE);

      const { result } = renderHook(() => useJobContext(), { wrapper });

      // First, fetch initial jobs
      await result.current.fetchJobs(JOB_TAB_VALUES.ALL);

      await waitFor(() => {
        expect(result.current.jobs).toBeDefined();
        expect(result.current.hasMoreData).toBe(true);
      });

      // Then fetch more jobs
      await result.current.fetchMoreJobs();

      expect(mockGetJobs).toHaveBeenCalledTimes(2);
      expect(mockGetJobs).toHaveBeenLastCalledWith({
        jobType: [JobType.ContentGeneration, JobType.ArticleExport],
        cursor: 'test-cursor',
        direction: 'backward',
      });

      await waitFor(() => {
        expect(result.current.jobs).toHaveLength(2);
      });
    });

    it('does not fetch more jobs when hasMoreData is false', async () => {
      mockGetJobs.mockResolvedValueOnce({
        ...MOCK_JOBS_RESPONSE,
        pagination: {
          ...MOCK_JOBS_RESPONSE.pagination,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      });

      const { result } = renderHook(() => useJobContext(), { wrapper });

      await result.current.fetchJobs(JOB_TAB_VALUES.ALL);
      await result.current.fetchMoreJobs();

      expect(mockGetJobs).toHaveBeenCalledTimes(1);
    });

    it('does not fetch more jobs when already loading', async () => {
      mockGetJobs.mockImplementationOnce(
        () => new Promise((resolve) => setTimeout(() => resolve(MOCK_JOBS_RESPONSE), 100))
      );

      const { result } = renderHook(() => useJobContext(), { wrapper });

      // Start fetching jobs (will be loading)
      result.current.fetchJobs(JOB_TAB_VALUES.ALL);

      // Try to fetch more jobs while loading
      await result.current.fetchMoreJobs();

      // Should not make additional API call
      expect(mockGetJobs).toHaveBeenCalledTimes(1);
    });
  });

  describe('deleteJob', () => {
    it('deletes job and removes it from all cached tabs', async () => {
      const jobToDelete = { ...MOCK_JOBS_RESPONSE.data[0], id: 'job-to-delete' };
      const allJobsResponse = { ...MOCK_JOBS_RESPONSE, data: [jobToDelete] };
      const genJobsResponse = { ...MOCK_JOBS_RESPONSE, data: [jobToDelete] };

      mockGetJobs.mockResolvedValueOnce(allJobsResponse).mockResolvedValueOnce(genJobsResponse);
      mockDeleteJob.mockResolvedValueOnce({ status: 'success', data: jobToDelete });

      const { result } = renderHook(() => useJobContext(), { wrapper });

      // Fetch both tabs to populate cache
      await result.current.fetchJobs(JOB_TAB_VALUES.ALL);
      await result.current.fetchJobs(JOB_TAB_VALUES.GENERATION);

      // Delete the job
      await result.current.deleteJob('job-to-delete');

      expect(mockDeleteJob).toHaveBeenCalledWith('job-to-delete');

      // Job should be removed from current tab
      await waitFor(() => {
        expect(result.current.jobs).toHaveLength(0);
      });

      // Switch back to ALL tab - job should also be removed from that cache
      await result.current.fetchJobs(JOB_TAB_VALUES.ALL);
      await waitFor(() => {
        expect(result.current.jobs).toHaveLength(0);
      });
    });

    it('handles delete API error gracefully', async () => {
      mockGetJobs.mockResolvedValueOnce(MOCK_JOBS_RESPONSE);
      mockDeleteJob.mockResolvedValueOnce({ status: 'error', data: MOCK_JOBS_RESPONSE.data[0] });

      const { result } = renderHook(() => useJobContext(), { wrapper });

      await result.current.fetchJobs(JOB_TAB_VALUES.ALL);
      await result.current.deleteJob('job-1');

      // Job should still be in the list since delete failed
      await waitFor(() => {
        expect(result.current.jobs).toHaveLength(1);
      });
    });
  });

  describe('loading states', () => {
    it('sets loading state correctly during fetch operations', async () => {
      let resolvePromise: (value: any) => void;
      const promise = new Promise<any>((resolve) => {
        resolvePromise = resolve;
      });

      mockGetJobs.mockReturnValueOnce(promise);

      const { result } = renderHook(() => useJobContext(), { wrapper });

      // Start fetching (don't await immediately)
      const fetchPromise = result.current.fetchJobs(JOB_TAB_VALUES.ALL);

      // Check if loading state is set
      await waitFor(() => {
        expect(result.current.jobsLoading).toBe(true);
      });

      // Resolve the promise
      resolvePromise!(MOCK_JOBS_RESPONSE);

      // Wait for the fetch to complete
      await fetchPromise;

      await waitFor(() => {
        expect(result.current.jobsLoading).toBe(false);
      });
    });
  });

  describe('context state management', () => {
    it('initializes with correct default state', () => {
      const { result } = renderHook(() => useJobContext(), { wrapper });

      expect(result.current.jobs).toEqual([]);
      expect(result.current.jobsLoading).toBe(false);
      expect(result.current.currentTabType).toBe(JOB_TAB_VALUES.ALL);
      expect(result.current.hasMoreData).toBe(false);
    });

    it('updates currentTabType when fetching different tabs', async () => {
      mockGetJobs.mockResolvedValue(MOCK_JOBS_RESPONSE);

      const { result } = renderHook(() => useJobContext(), { wrapper });

      await result.current.fetchJobs(JOB_TAB_VALUES.GENERATION);

      await waitFor(() => {
        expect(result.current.currentTabType).toBe(JOB_TAB_VALUES.GENERATION);
      });

      await result.current.fetchJobs(JOB_TAB_VALUES.EXPORT);

      await waitFor(() => {
        expect(result.current.currentTabType).toBe(JOB_TAB_VALUES.EXPORT);
      });
    });
  });

  describe('caching functionality', () => {
    it('caches data per tab type', async () => {
      const allJobsResponse = {
        ...MOCK_JOBS_RESPONSE,
        data: [{ ...MOCK_JOBS_RESPONSE.data[0], id: 'all-job' }],
      };
      const genJobsResponse = {
        ...MOCK_JOBS_RESPONSE,
        data: [{ ...MOCK_JOBS_RESPONSE.data[0], id: 'gen-job' }],
      };

      mockGetJobs.mockResolvedValueOnce(allJobsResponse).mockResolvedValueOnce(genJobsResponse);

      const { result } = renderHook(() => useJobContext(), { wrapper });

      // Fetch ALL tab data
      await result.current.fetchJobs(JOB_TAB_VALUES.ALL);
      await waitFor(() => {
        expect(result.current.jobs[0]?.id).toBe('all-job');
      });

      // Fetch GENERATION tab data
      await result.current.fetchJobs(JOB_TAB_VALUES.GENERATION);
      await waitFor(() => {
        expect(result.current.jobs[0]?.id).toBe('gen-job');
      });

      // Switch back to ALL tab - should use cached data
      await result.current.fetchJobs(JOB_TAB_VALUES.ALL);
      await waitFor(() => {
        expect(result.current.jobs[0]?.id).toBe('all-job');
      });

      // Should have only made 2 API calls (not 3)
      expect(mockGetJobs).toHaveBeenCalledTimes(2);
    });

    it('invalidates cache when invalidateCache is called', async () => {
      mockGetJobs.mockResolvedValue(MOCK_JOBS_RESPONSE);

      const { result } = renderHook(() => useJobContext(), { wrapper });

      // Fetch data first
      await result.current.fetchJobs(JOB_TAB_VALUES.ALL);
      expect(mockGetJobs).toHaveBeenCalledTimes(1);

      // Invalidate cache
      result.current.invalidateCache();

      // Fetch again - should make new API call
      await result.current.fetchJobs(JOB_TAB_VALUES.ALL);
      expect(mockGetJobs).toHaveBeenCalledTimes(2);
    });

    it('refreshes current tab when refreshCurrentTab is called', async () => {
      mockGetJobs.mockResolvedValue(MOCK_JOBS_RESPONSE);

      const { result } = renderHook(() => useJobContext(), { wrapper });

      // Fetch data first
      await result.current.fetchJobs(JOB_TAB_VALUES.ALL);
      expect(mockGetJobs).toHaveBeenCalledTimes(1);

      // Refresh current tab
      await result.current.refreshCurrentTab();
      expect(mockGetJobs).toHaveBeenCalledTimes(2);
    });
  });

  describe('cache expiration', () => {
    it('refetches data when cache is expired', async () => {
      mockGetJobs.mockResolvedValue(MOCK_JOBS_RESPONSE);

      const { result } = renderHook(() => useJobContext(), { wrapper });

      // First fetch
      await result.current.fetchJobs(JOB_TAB_VALUES.ALL);
      expect(mockGetJobs).toHaveBeenCalledTimes(1);

      // Simulate cache expiration (5 minutes + 1ms)
      mockDateNow.mockReturnValue(1000000 + 5 * 60 * 1000 + 1);

      // Second fetch should make new API call due to expired cache
      await result.current.fetchJobs(JOB_TAB_VALUES.ALL);
      expect(mockGetJobs).toHaveBeenCalledTimes(2);
    });
  });
});
