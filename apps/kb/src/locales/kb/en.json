{"articleLabel": "Article Collection", "articleDescription": "Knowledge Base to save articles type", "createNewKnowledgeBase": "Create Knowledge Base", "kbCreateModalTitle": "Knowledge Base Details", "generatePageTitle": "Auto-generate new QnA", "kbName": "Name", "unknownKBName": "Unknown", "kbNameRequired": "Name is required", "kbNameTooLong": "Name should have at most {maxLength} characters", "kbNamePlaceholder": "Unique name for this knowledge base", "kbDescription": "Description", "kbDescriptionPlaceholder": "Short description for this knowledge base", "kbDescriptionTooLong": "Description should have at most {maxLength} characters", "kbType": "Type", "qnaLabel": "QnA", "qnaDescription": "Knowledge Base to register QnA list used for Chatbot responses", "documentLabel": "Document Collection", "documentDescription": "Knowledge Base to save documents type", "cancel": "Cancel", "create": "Create", "save": "Save", "edit": "Edit", "delete": "Delete", "upload": "Upload", "move": "Move", "readyToReview": "Ready to Review", "generating": "Generating", "QnACreateModalTitle": "QnA Editor", "qnaQuestion": "Question", "qnaQuestionPlaceholder": "Enter the question", "questionRequired": "Question is required", "questionTooShort": "Question is too short", "questionTooLong": "Question is too long", "qnaAnswer": "Answer", "qnaAnswerPlaceholder": "Enter the answer", "answerRequired": "Answer is required", "answerTooShort": "Answer is too short", "answerTooLong": "Answer is too long", "selectDocument": "Please select a document knowledge base", "searchByDocumentName": "Search by document <PERSON><PERSON>'s name", "noDocumentList": "There is no Document Knowledge Base yet.", "noDocumentListDescription": "Please create a Knowledge Base to get started!.", "goToKnowledgeBase": "Go to Knowledge Base", "customizePrompt": "Customize Prompt", "generateQnA": "Generate QnA", "generatingQnA": "The QnA is generating. Please wait a moment.", "generatingQnADescription": "You can move to other pages and come back to check later.", "continue": "Continue", "selectAll": "Select All", "qnaGenerateStep1": "Select a  Document KB & input prompt", "qnaGenerateStep2": "Generate QnA", "qnaGenerateStep3": "Select QnA from generated list", "qnaGenerateStep4": "Complete", "totalSelected": "Total Selected: {total} QnA", "saveAsDraft": "Save as Draft", "generateQnASuccess": "The QnA has been added successfully into the {kbName}!", "checkTheQnAList": "Check the QnA list", "publish": "Publish", "apply": "Apply", "customizePromptModalTitle": "Customize Prompt", "noDataAddedToQnAKB": "There will be no QnA added into the {kbName}!", "noDocumentFound": "No matching results found", "defaultPrompt": "Please create a list of 20 questions (Q) and answers (A) in Japanese from the sentences below. For answer (A), please write your answer in as much detail as possible.", "qnaGenerateFailed": "There is something wrong with the generation. Please check the document and generate again.", "fromDatasourceLabel": "From a Datasource", "fromDatasourceDescription": "Knowledge Base to save datasource type", "modal": {"create": "Create New", "createFolder": "Create a New Folder", "createKnowledgeBase": "Create a New Knowledge Base", "uploadFile": "Upload File", "uploadFileDescription": "Only PDF, TXT, CSV, Doc, Docx, MD, JPG, JPEG, PNG, GIF, SVG, WEBP are supported. Capacity up to {maxSize}MB", "editFolder": "Edit <PERSON>", "cancel": "Cancel", "uploadingFiles": "Files Upload", "uploadingFilesDescription": "You can choose to set all files as either Public or Private here.", "switchAll": "Switch All"}, "folder": {"createError": "Failed to create folder", "updateError": "Failed to update folder", "pathPlaceholder": "Please fill in the folder name here (required)", "pathRequired": "Folder name is required", "pathTooLong": "The folder name should have at most {maxLength} characters"}, "search": {"searchResuls": "Search results", "emptyMessage": "Sorry, there are no results that match your search"}, "openAction": {"folder": "Open Folder", "base": "Open Knowledge Base", "document": "Open File", "article": "Open Article"}, "accessLevel": {"changed": {"title": "Please wait...", "content": "Changing the public scope takes time. You can check the progress in Jobs page."}, "label": "Public Scope", "description": "You can set the public scope of your knowledge base.", "processing": "Changing", "private": {"label": "Private", "description": "Only users logged in to DECA Cloud products can view this Knowledge Base"}, "public": {"label": "Public", "description": "Through DECA Cloud products such as chatbots and FAQs, even users who are not logged in to DECA Cloud can view this Knowledge Base. (The knowledge base is not directly viewable.)"}}, "kbMoving": {"title": "Move Knowledge Base", "description": "You can move knowledge bases between folders.", "subDescription": "All articles, custom data fields, and associated metadata are retained during the move."}, "createKB": {"successTitle": "Success", "successMessage": "The knowledge base has been created successfully."}, "documentSelector": {"title": "Select the Files", "description": "You can select files from the below list to generate articles. Multiple files can be selected.", "confirmTitle": "Generate Articles", "confirmMessage": "Proceed with generating articles from the selected files?"}, "promptCustomize": {"title": "Customize the Prompt", "description": "You can customize the prompt here. The changes will be automatically saved for future use.", "placeholder": "Please create a list of 20 questions (Q) and answers (A) in Japanese from the sentences below. For answer (A), please write your answer in as much detail as possible.", "formTitle": "Prompt Title", "formContent": "Prompt Content", "formContentLimit": "{currentLength}/{maxLength} Characters", "pastCustomPrompts": "Past Customized Prompts", "pastCustomPromptsDescription": "Only the latest {limit} prompts are saved, older ones are automatically removed.", "createSuccess": {"title": "Success", "message": "A new prompt has been saved!"}, "updateSuccess": {"title": "Success", "message": "Your prompt has been saved!"}, "error": {"title": "Error", "message": "Something went wrong while updating the prompt"}, "deleteConfirmTitle": "Are you sure you want to delete this prompt?", "resetTooltip": "Resetting will return to the default prompt.\nClicking 'Save' will create a new prompt."}, "promptViewer": {"title": "Prompt", "description": "Prompt content:"}, "error": {"fileSize": "The file size is too large. Please select a file smaller than {size}."}, "document": {"detail": {"contentType": "File Type", "status": "Status", "size": "Size", "createdAt": "Created At", "createdBy": "Created By", "deleteConfirmTitle": "Are you sure you want to delete this file?", "updateAccessLevelTitle": "This knowledge base will be {accessLevel}.", "notFoundTitle": "File Not Found", "notFoundDesc": "The file you are looking for does not exist or has been deleted."}}, "validation": {"titleRequired": "Title is required", "contentRequired": "Content is required", "contentMaxLength": "Content must not exceed {maxLength} characters"}, "sorting": {"sort": "Sort", "title": "Sort Options", "reset": "Reset to default", "sortBy": "Sort By", "sortOrder": "Sort Order", "fields": {"createdDate": "Created Date", "updatedDate": "Updated Date", "title": "Title"}, "orders": {"ascending": "Ascending", "descending": "Descending"}}}