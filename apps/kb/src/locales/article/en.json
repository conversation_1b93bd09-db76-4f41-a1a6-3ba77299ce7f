{"articleCollection": {"createNew": "Create Article", "createArticleManually": "Create a new article manually", "importArticle": "Import articles from CSV", "aiGenerate": "Generate Article with AI", "name": "Name", "status": "Status", "lastUpdated": "Last Updated", "createdAt": "Created At", "title": "Title", "content": "Content", "openArticle": "Open Article", "keyPhrases": "Key phrases", "keyPhrasesPlaceholder": "Add a Key Phrase and press Enter", "info": "Info", "emptyTitle": "This Article Knowledge Base is empty", "emptyMessage": "Please create articles to get started", "changedWarningTitle": "Your changes wont be saved", "changedWarningDescription": "We wont be able to save the article if you move away from this page.", "discardChanges": "Discard the Article", "goBack": "Go Back", "related": "Related Articles", "addRelatedArticle": "Add Related Article", "removeRelatedArticle": "Are you sure you want to remove this article from the related articles?", "delete": {"confirmTitle": "Are you sure you want to delete this article?", "confirmContent": "This action cannot be undone.", "confirmContentWithShortcut": "Deleting the original article will also delete shortcuts in other knowledge bases. This action cannot be undone.", "success": {"title": "Article deleted successfully", "description": "The article has been deleted successfully"}, "failed": {"title": "Failed to delete the article", "description": "Please try again later"}}, "save": {"failed": {"title": "Failed to save the article", "description": "Please try again later"}, "success": {"title": "Article saved successfully", "description": "The article has been saved successfully"}, "notReady": {"title": "Article is not ready to be updated", "description": "Please try again later in a few minutes"}}, "vote": {"success": {"title": "Article voted successfully", "description": "The article has been voted successfully"}, "failed": {"title": "Failed to vote the article", "description": "Please try again later"}}}, "errors": {"titleRequired": "Article title is required", "contentRequired": "Article content is required", "longContent": "The content should have at most {maxLength} characters", "longTitle": "The title should have at most {maxLength} characters"}, "articleMoving": {"title": "Move Article", "description": "You can move the article between different article knowledge bases.", "subDescription": "All associated metadata, custom data fields, and formatting are preserved during the move.", "successTitle": "Success", "successMessage": "The article has been moved successfully.", "failedTitle": "Failed to move the article", "failedMessage": "Please try again later", "confirmMovingMessage": "Are you sure you want to move this article to \"{toKBName}\"? \nThis action cannot be undone."}, "articleAnalytics": {"viewCount": "Number of Views", "rating": "Reviews from Customer", "goodRating": "Positive Reviews", "badRating": "Negative Reviews", "countSuffix": "", "count": "", "chatbot": "<PERSON><PERSON><PERSON>", "faq": "FAQ", "management": "Management screen", "aiwidget": "AI Widget", "chatwindow": "Chatwindow", "page": "Pages", "customerSide": "Customer side", "operatorSide": "Operator side", "noData": "No data available", "quickFilter": {"last7Days": "Last 7 days", "last28Days": "Last 28 days", "last90Days": "Last 90 days", "allTime": "All time", "customRange": "Custom Range"}, "placeholder": {"selectPeriod": "Select period", "dateRange": "YYYY/MM/DD-YYYY/MM/DD"}}, "comments": {"title": "Comments", "placeholder": "Please add a comment", "empty": "There are no comments yet", "deleteConfirmTitle": "Are you sure you want to delete this comment?", "seeMore": "See more"}, "articleSelector": {"title": "Select related articles", "description": "Please select relevant articles. You can select up to {limit} articles.", "confirmTitle": "Generate article", "confirmMessage": "Generate article from selected files?", "selectAll": "Select all", "placeholderSearch": "Search by article title, related phrases, and content"}, "import": {"csvInstructions": "Ensure your CSV file includes columns for title and content like this.", "downloadTemplate": "Download CSV template", "dropzone": "Drop your formatted CSV file here or click to upload", "maxFileSizeAndRows": "Max file size: {size}, max rows allowed: {rows}", "descriptionConfirm": "The articles are ready to import to \"{KBName}\"", "confirmImport": "Confirm Import", "importArticleSuccess": "You are good to go!", "importArticleSuccessDescription": "{count} articles has been imported successfully.", "import_article_missing_data": "Missing data in the CSV file at rows: {rows}", "file-too-large": "File is too large. Please upload a file smaller than {size}.", "file-too-small": "File is too small. Please upload a file larger than {size}.", "file-invalid-type": "File type is invalid. Please upload a file with the following type: {type}.", "too-many-files": "Too many files. Please upload only one file.", "import_article_too_many_rows": "Too many rows. Please upload a file with less than {maxRows} rows.", "import_csv_min_number_column": "Require at least 2 columns.", "import_article_title_too_long": "Article title is longer than {maxLengthTitle} characters", "import_article_content_too_long": "Article content is longer than {maxLengthContent} characters", "uploading": "Crunching all the data...", "uploadingDesc": "Please have a rest and give us a couple of seconds.", "uploadingError": "Something went wrong. Please try again later.", "csvEncodingDetectionFailed": "Unsupported file encoding. Please upload a file encoded in UTF-8, Shift JIS, or CP932."}, "articleShortcut": {"badge": "shortcut", "viewOriginal": "View original article", "creating": {"title": "Create shortcut", "description": "You can create a shortcut of this article and save it to another knowledge base.\nThe input of custom fields and related metadata will be preserved.", "success": {"title": "Article shortcut created successfully", "description": "The article shortcut has been created successfully"}, "failed": {"title": "Failed to create article shortcut", "description": "Please try again later"}}}, "articleDuplication": {"title": "Select a Location to Duplicate the Article", "description": "You can duplicate the article and save it in another knowledge base.\nAfter duplication, all the input in custom fields and associated metadata will be retained.", "success": {"title": "Success to duplicate the article", "description": "The article has been duplicated successfully"}, "failed": {"title": "Failed to duplicate the article", "description": "Please try again later"}, "confirm": {"title": "Are you sure you want to duplicate this article?", "description": "This action cannot be undone."}, "duplicateButton": "Duplicate Article"}}