/**
 * Centralized page size validation utility
 * Ensures consistency between URL parameters and component state
 *
 * This utility provides a single source of truth for page size validation
 * across the application, preventing inconsistencies between components
 * and URL parameters.
 *
 * @example
 * ```typescript
 * // Validate a URL parameter
 * const pageSize = validatePageSizeFromString(searchParams.get('limit'));
 *
 * // Check if a value is valid
 * if (isValidPageSize(userInput)) {
 *   // Use the value
 * }
 *
 * // Get options for a select component
 * const options = getPageSizeOptions();
 * ```
 */

// Supported page size options - should match PageSizeSelection component
// These values are used consistently across the application
export const SUPPORTED_PAGE_SIZES = [10, 20, 30, 40, 50] as const;

// Default page size when validation fails
// This should be a reasonable default that works well for most use cases
export const DEFAULT_PAGE_SIZE = 20;

// Type for supported page sizes
// This provides compile-time type safety
export type SupportedPageSize = (typeof SUPPORTED_PAGE_SIZES)[number];

/**
 * Validates if a page size value is supported
 * @param value - The page size value to validate
 * @returns true if the value is supported, false otherwise
 */
export const isValidPageSize = (value: number): value is SupportedPageSize => {
  return SUPPORTED_PAGE_SIZES.includes(value as SupportedPageSize);
};

/**
 * Validates and normalizes a page size value
 * Returns the default page size if the value is not supported
 * @param value - The page size value to validate
 * @returns A valid page size value
 */
export const validatePageSize = (value: number): SupportedPageSize => {
  if (isValidPageSize(value)) {
    return value;
  }
  return DEFAULT_PAGE_SIZE;
};

/**
 * Validates a page size value from URL parameters or other string sources
 *
 * This function handles various input types and edge cases:
 * - String numbers (e.g., "20", "30")
 * - Numeric values (e.g., 20, 30)
 * - Null/undefined values
 * - Empty strings
 * - Invalid strings (e.g., "abc", "NaN")
 * - Negative numbers
 * - Zero
 *
 * @param value - The page size value (can be string or number)
 * @returns A valid page size value
 *
 * @example
 * ```typescript
 * validatePageSizeFromString("20") // returns 20
 * validatePageSizeFromString(30) // returns 30
 * validatePageSizeFromString("abc") // returns 20 (default)
 * validatePageSizeFromString(null) // returns 20 (default)
 * validatePageSizeFromString("") // returns 20 (default)
 * ```
 */
export const validatePageSizeFromString = (
  value: string | number | null | undefined
): SupportedPageSize => {
  // Handle null, undefined, and empty string cases
  if (value === null || value === undefined || value === '') {
    return DEFAULT_PAGE_SIZE;
  }

  // Convert string to number if needed
  const numericValue = typeof value === 'string' ? Number.parseInt(value, 10) : value;

  // Handle invalid numeric values
  if (Number.isNaN(numericValue) || numericValue <= 0 || !Number.isFinite(numericValue)) {
    return DEFAULT_PAGE_SIZE;
  }

  return validatePageSize(numericValue);
};

/**
 * Gets the closest valid page size to a given value
 * If the value is not supported, returns the closest smaller supported value
 * @param value - The page size value
 * @returns The closest valid page size
 */
export const getClosestValidPageSize = (value: number): SupportedPageSize => {
  if (isValidPageSize(value)) {
    return value;
  }

  // Handle edge cases
  if (Number.isNaN(value) || value <= 0 || !Number.isFinite(value)) {
    return DEFAULT_PAGE_SIZE;
  }

  // Find the closest smaller supported value
  const sortedSizes = [...SUPPORTED_PAGE_SIZES].sort((a, b) => b - a);
  const closestSmaller = sortedSizes.find((size) => size <= value);

  return closestSmaller || DEFAULT_PAGE_SIZE;
};

/**
 * Creates page size options for Select components
 *
 * @returns Array of page size options with value and label
 *
 * @example
 * ```typescript
 * const options = getPageSizeOptions();
 * // Returns: [
 * //   { value: '10', label: '10' },
 * //   { value: '20', label: '20' },
 * //   { value: '30', label: '30' },
 * //   { value: '40', label: '40' },
 * //   { value: '50', label: '50' }
 * // ]
 * ```
 */
export const getPageSizeOptions = () => {
  return SUPPORTED_PAGE_SIZES.map((size) => ({
    value: size.toString(),
    label: size.toString(),
  }));
};

/**
 * Logs a warning when an invalid page size is encountered
 * This helps with debugging and monitoring invalid usage
 *
 * @param invalidValue - The invalid value that was provided
 * @param context - Additional context about where the validation occurred
 */
export const logInvalidPageSizeWarning = (invalidValue: unknown, context?: string): void => {
  const contextInfo = context ? ` in ${context}` : '';
  console.warn(
    `Invalid page size "${invalidValue}" encountered${contextInfo}. ` +
      `Using default value ${DEFAULT_PAGE_SIZE}. ` +
      `Supported values: ${SUPPORTED_PAGE_SIZES.join(', ')}`
  );
};

/**
 * Validates page size with logging for invalid values
 * Useful for debugging and monitoring
 *
 * @param value - The page size value to validate
 * @param context - Optional context for logging
 * @returns A valid page size value
 */
export const validatePageSizeWithLogging = (
  value: string | number | null | undefined,
  context?: string
): SupportedPageSize => {
  const result = validatePageSizeFromString(value);

  // Log warning if the original value was invalid
  if (result === DEFAULT_PAGE_SIZE && value !== DEFAULT_PAGE_SIZE.toString()) {
    logInvalidPageSizeWarning(value, context);
  }

  return result;
};

/**
 * Gets the maximum supported page size
 *
 * @returns The maximum supported page size
 */
export const getMaxPageSize = (): SupportedPageSize => {
  return Math.max(...SUPPORTED_PAGE_SIZES) as SupportedPageSize;
};

/**
 * Gets the minimum supported page size
 *
 * @returns The minimum supported page size
 */
export const getMinPageSize = (): SupportedPageSize => {
  return Math.min(...SUPPORTED_PAGE_SIZES) as SupportedPageSize;
};
