import {
  DEFAULT_PAGE_SIZE,
  SUPPORTED_PAGE_SIZES,
  type SupportedPageSize,
  getClosestValidPageSize,
  getMaxPageSize,
  getMinPageSize,
  getPageSizeOptions,
  isValidPageSize,
  logInvalidPageSizeWarning,
  validatePageSize,
  validatePageSizeFromString,
  validatePageSizeWithLogging,
} from '@/utils/pageSizeValidation';
import { describe, expect, test, vi } from 'vitest';

describe('pageSizeValidation', () => {
  describe('SUPPORTED_PAGE_SIZES', () => {
    test('should contain expected page sizes', () => {
      expect(SUPPORTED_PAGE_SIZES).toEqual([10, 20, 30, 40, 50]);
    });

    test('should be readonly array', () => {
      // Test that the array is frozen/readonly by checking it doesn't change
      const originalLength = SUPPORTED_PAGE_SIZES.length;
      expect(originalLength).toBe(5);
      // The array should remain unchanged
      expect(SUPPORTED_PAGE_SIZES).toEqual([10, 20, 30, 40, 50]);
    });
  });

  describe('DEFAULT_PAGE_SIZE', () => {
    test('should be 20', () => {
      expect(DEFAULT_PAGE_SIZE).toBe(20);
    });

    test('should be a supported page size', () => {
      expect(isValidPageSize(DEFAULT_PAGE_SIZE)).toBe(true);
    });
  });

  describe('isValidPageSize', () => {
    test('should return true for supported page sizes', () => {
      expect(isValidPageSize(10)).toBe(true);
      expect(isValidPageSize(20)).toBe(true);
      expect(isValidPageSize(30)).toBe(true);
      expect(isValidPageSize(40)).toBe(true);
      expect(isValidPageSize(50)).toBe(true);
    });

    test('should return false for unsupported page sizes', () => {
      expect(isValidPageSize(5)).toBe(false);
      expect(isValidPageSize(15)).toBe(false);
      expect(isValidPageSize(25)).toBe(false);
      expect(isValidPageSize(35)).toBe(false);
      expect(isValidPageSize(45)).toBe(false);
      expect(isValidPageSize(60)).toBe(false);
      expect(isValidPageSize(100)).toBe(false);
    });

    test('should return false for edge cases', () => {
      expect(isValidPageSize(0)).toBe(false);
      expect(isValidPageSize(-1)).toBe(false);
      expect(isValidPageSize(Number.NaN)).toBe(false);
      expect(isValidPageSize(Number.POSITIVE_INFINITY)).toBe(false);
    });
  });

  describe('validatePageSize', () => {
    test('should return the same value for valid page sizes', () => {
      expect(validatePageSize(10)).toBe(10);
      expect(validatePageSize(20)).toBe(20);
      expect(validatePageSize(30)).toBe(30);
      expect(validatePageSize(40)).toBe(40);
      expect(validatePageSize(50)).toBe(50);
    });

    test('should return default page size for invalid page sizes', () => {
      expect(validatePageSize(5)).toBe(DEFAULT_PAGE_SIZE);
      expect(validatePageSize(15)).toBe(DEFAULT_PAGE_SIZE);
      expect(validatePageSize(25)).toBe(DEFAULT_PAGE_SIZE);
      expect(validatePageSize(100)).toBe(DEFAULT_PAGE_SIZE);
    });

    test('should return default page size for edge cases', () => {
      expect(validatePageSize(0)).toBe(DEFAULT_PAGE_SIZE);
      expect(validatePageSize(-1)).toBe(DEFAULT_PAGE_SIZE);
      expect(validatePageSize(Number.NaN)).toBe(DEFAULT_PAGE_SIZE);
    });
  });

  describe('validatePageSizeFromString', () => {
    test('should parse and validate string numbers correctly', () => {
      expect(validatePageSizeFromString('10')).toBe(10);
      expect(validatePageSizeFromString('20')).toBe(20);
      expect(validatePageSizeFromString('30')).toBe(30);
      expect(validatePageSizeFromString('40')).toBe(40);
      expect(validatePageSizeFromString('50')).toBe(50);
    });

    test('should handle numeric inputs', () => {
      expect(validatePageSizeFromString(10)).toBe(10);
      expect(validatePageSizeFromString(20)).toBe(20);
      expect(validatePageSizeFromString(30)).toBe(30);
    });

    test('should return default for invalid string inputs', () => {
      expect(validatePageSizeFromString('5')).toBe(DEFAULT_PAGE_SIZE);
      expect(validatePageSizeFromString('15')).toBe(DEFAULT_PAGE_SIZE);
      expect(validatePageSizeFromString('100')).toBe(DEFAULT_PAGE_SIZE);
      expect(validatePageSizeFromString('abc')).toBe(DEFAULT_PAGE_SIZE);
      expect(validatePageSizeFromString('')).toBe(DEFAULT_PAGE_SIZE);
    });

    test('should handle null and undefined inputs', () => {
      expect(validatePageSizeFromString(null)).toBe(DEFAULT_PAGE_SIZE);
      expect(validatePageSizeFromString(undefined)).toBe(DEFAULT_PAGE_SIZE);
    });

    test('should handle edge cases', () => {
      expect(validatePageSizeFromString('0')).toBe(DEFAULT_PAGE_SIZE);
      expect(validatePageSizeFromString('-1')).toBe(DEFAULT_PAGE_SIZE);
      expect(validatePageSizeFromString('NaN')).toBe(DEFAULT_PAGE_SIZE);
      expect(validatePageSizeFromString('Infinity')).toBe(DEFAULT_PAGE_SIZE);
    });

    test('should handle whitespace in strings', () => {
      expect(validatePageSizeFromString(' 20 ')).toBe(20);
      expect(validatePageSizeFromString('  10  ')).toBe(10);
    });
  });

  describe('getClosestValidPageSize', () => {
    test('should return the same value for valid page sizes', () => {
      expect(getClosestValidPageSize(10)).toBe(10);
      expect(getClosestValidPageSize(20)).toBe(20);
      expect(getClosestValidPageSize(30)).toBe(30);
      expect(getClosestValidPageSize(40)).toBe(40);
      expect(getClosestValidPageSize(50)).toBe(50);
    });

    test('should return closest smaller valid page size', () => {
      expect(getClosestValidPageSize(15)).toBe(10);
      expect(getClosestValidPageSize(25)).toBe(20);
      expect(getClosestValidPageSize(35)).toBe(30);
      expect(getClosestValidPageSize(45)).toBe(40);
      expect(getClosestValidPageSize(55)).toBe(50);
    });

    test('should return default for values smaller than minimum', () => {
      expect(getClosestValidPageSize(5)).toBe(DEFAULT_PAGE_SIZE);
      expect(getClosestValidPageSize(0)).toBe(DEFAULT_PAGE_SIZE);
      expect(getClosestValidPageSize(-1)).toBe(DEFAULT_PAGE_SIZE);
    });

    test('should return default for very large values', () => {
      expect(getClosestValidPageSize(100)).toBe(50); // 50 is the closest smaller value
      expect(getClosestValidPageSize(1000)).toBe(50); // 50 is the closest smaller value
    });

    test('should handle edge cases', () => {
      expect(getClosestValidPageSize(Number.NaN)).toBe(DEFAULT_PAGE_SIZE);
      expect(getClosestValidPageSize(Number.POSITIVE_INFINITY)).toBe(DEFAULT_PAGE_SIZE);
    });
  });

  describe('getPageSizeOptions', () => {
    test('should return correct options format', () => {
      const options = getPageSizeOptions();
      expect(options).toEqual([
        { value: '10', label: '10' },
        { value: '20', label: '20' },
        { value: '30', label: '30' },
        { value: '40', label: '40' },
        { value: '50', label: '50' },
      ]);
    });

    test('should return options with string values', () => {
      const options = getPageSizeOptions();
      options.forEach((option) => {
        expect(typeof option.value).toBe('string');
        expect(typeof option.label).toBe('string');
        expect(option.value).toBe(option.label);
      });
    });

    test('should have same length as supported page sizes', () => {
      const options = getPageSizeOptions();
      expect(options).toHaveLength(SUPPORTED_PAGE_SIZES.length);
    });
  });

  describe('Type safety', () => {
    test('SupportedPageSize type should work correctly', () => {
      const validSizes: SupportedPageSize[] = [10, 20, 30, 40, 50];
      validSizes.forEach((size) => {
        expect(isValidPageSize(size)).toBe(true);
      });
    });

    test('should prevent invalid assignments at compile time', () => {
      // This test ensures TypeScript type checking works
      // The following would cause TypeScript errors:
      // const invalidSize: SupportedPageSize = 15; // Should cause TS error
      // const invalidSize2: SupportedPageSize = 100; // Should cause TS error

      // Valid assignments should work
      const validSize: SupportedPageSize = 20;
      expect(validSize).toBe(20);
    });
  });

  describe('New utility functions', () => {
    describe('getMaxPageSize', () => {
      test('should return the maximum supported page size', () => {
        expect(getMaxPageSize()).toBe(50);
      });

      test('should return a valid supported page size', () => {
        const maxSize = getMaxPageSize();
        expect(isValidPageSize(maxSize)).toBe(true);
      });
    });

    describe('getMinPageSize', () => {
      test('should return the minimum supported page size', () => {
        expect(getMinPageSize()).toBe(10);
      });

      test('should return a valid supported page size', () => {
        const minSize = getMinPageSize();
        expect(isValidPageSize(minSize)).toBe(true);
      });
    });

    describe('logInvalidPageSizeWarning', () => {
      test('should log warning for invalid values', () => {
        const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

        logInvalidPageSizeWarning('100');

        expect(consoleSpy).toHaveBeenCalledWith(
          expect.stringContaining('Invalid page size "100" encountered')
        );

        consoleSpy.mockRestore();
      });

      test('should include context in warning message', () => {
        const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

        logInvalidPageSizeWarning('abc', 'URL parameter');

        expect(consoleSpy).toHaveBeenCalledWith(
          expect.stringContaining('Invalid page size "abc" encountered in URL parameter')
        );

        consoleSpy.mockRestore();
      });

      test('should include supported values in warning', () => {
        const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

        logInvalidPageSizeWarning('999');

        expect(consoleSpy).toHaveBeenCalledWith(
          expect.stringContaining('Supported values: 10, 20, 30, 40, 50')
        );

        consoleSpy.mockRestore();
      });
    });

    describe('validatePageSizeWithLogging', () => {
      test('should validate and return correct value', () => {
        expect(validatePageSizeWithLogging('20')).toBe(20);
        expect(validatePageSizeWithLogging(30)).toBe(30);
      });

      test('should log warning for invalid values', () => {
        const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

        validatePageSizeWithLogging('100');

        expect(consoleSpy).toHaveBeenCalledWith(
          expect.stringContaining('Invalid page size "100" encountered')
        );

        consoleSpy.mockRestore();
      });

      test('should not log warning for valid values', () => {
        const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

        validatePageSizeWithLogging('20');

        expect(consoleSpy).not.toHaveBeenCalled();

        consoleSpy.mockRestore();
      });

      test('should not log warning for default value', () => {
        const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

        validatePageSizeWithLogging('20'); // This is the default value

        expect(consoleSpy).not.toHaveBeenCalled();

        consoleSpy.mockRestore();
      });

      test('should include context in warning when provided', () => {
        const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

        validatePageSizeWithLogging('abc', 'Component prop');

        expect(consoleSpy).toHaveBeenCalledWith(
          expect.stringContaining('Invalid page size "abc" encountered in Component prop')
        );

        consoleSpy.mockRestore();
      });
    });
  });

  describe('Integration scenarios', () => {
    test('should handle URL parameter validation scenario', () => {
      // Simulate URL parameter scenarios
      const urlParams = ['10', '20', '100', 'abc', '', null, undefined];
      const expectedResults = [
        10,
        20,
        DEFAULT_PAGE_SIZE,
        DEFAULT_PAGE_SIZE,
        DEFAULT_PAGE_SIZE,
        DEFAULT_PAGE_SIZE,
        DEFAULT_PAGE_SIZE,
      ];

      urlParams.forEach((param, index) => {
        expect(validatePageSizeFromString(param)).toBe(expectedResults[index]);
      });
    });

    test('should handle component state validation scenario', () => {
      // Simulate component state scenarios
      const componentStates = [10, 20, 30, 100, 0, -5];
      const expectedResults = [10, 20, 30, DEFAULT_PAGE_SIZE, DEFAULT_PAGE_SIZE, DEFAULT_PAGE_SIZE];

      componentStates.forEach((state, index) => {
        expect(validatePageSize(state)).toBe(expectedResults[index]);
      });
    });

    test('should maintain consistency between validation functions', () => {
      const testValues = [10, 20, 30, 100, 0, -5, Number.NaN];

      testValues.forEach((value) => {
        const validated1 = validatePageSize(value);
        const validated2 = validatePageSizeFromString(value.toString());

        // Both should return the same result for the same input
        expect(validated1).toBe(validated2);

        // Both results should be valid page sizes
        expect(isValidPageSize(validated1)).toBe(true);
        expect(isValidPageSize(validated2)).toBe(true);
      });
    });

    test('should work with logging functions', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      // Test that logging functions work correctly
      const result1 = validatePageSizeWithLogging('100', 'Test context');
      const result2 = validatePageSizeWithLogging('20', 'Test context');

      expect(result1).toBe(DEFAULT_PAGE_SIZE);
      expect(result2).toBe(20);
      expect(consoleSpy).toHaveBeenCalledTimes(1); // Only for invalid value

      consoleSpy.mockRestore();
    });
  });
});
