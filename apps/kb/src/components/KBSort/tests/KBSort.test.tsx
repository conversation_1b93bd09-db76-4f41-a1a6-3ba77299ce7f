import { MantineProvider } from '@mantine/core';
import { render, screen } from '@testing-library/react';
import { TolgeeProvider } from '@tolgee/react';
import { describe, expect, it, vi } from 'vitest';
import { KBSort } from '../KBSort';
import type { SortOption } from '../KBSort';

// Mock Tolgee
const mockTolgee = {
  t: (key: string) => {
    const translations: Record<string, string> = {
      'sorting.sort': 'Sort',
      'sorting.title': 'Sort Options',
      'sorting.reset': 'Reset to default',
      'sorting.sortBy': 'Sort By',
      'sorting.sortOrder': 'Sort Order',
      'sorting.fields.createdDate': 'Created Date',
      'sorting.fields.updatedDate': 'Updated Date',
      'sorting.fields.title': 'Title',
      'sorting.orders.ascending': 'Ascending',
      'sorting.orders.descending': 'Descending',
    };
    return translations[key] || key;
  },
};

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: mockTolgee.t }),
  TolgeeProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <MantineProvider>
    <TolgeeProvider tolgee={null as any} fallback=''>
      {children}
    </TolgeeProvider>
  </MantineProvider>
);

const DEFAULT_SORT_OPTION: SortOption = {
  field: 'createdDate',
  order: 'descending',
};

const CUSTOM_SORT_OPTION: SortOption = {
  field: 'title',
  order: 'ascending',
};

describe('KBSort', () => {
  describe('Basic Rendering', () => {
    it('should render sort button with correct text', () => {
      render(
        <TestWrapper>
          <KBSort />
        </TestWrapper>
      );

      expect(screen.getByRole('button', { name: /sort/i })).toBeInTheDocument();
    });

    it('should display correct sort icon based on order', () => {
      const { rerender } = render(
        <TestWrapper>
          <KBSort sortOption={{ field: 'createdDate', order: 'ascending' }} />
        </TestWrapper>
      );

      // Check for ascending icon (should be present)
      expect(screen.getByRole('button')).toBeInTheDocument();

      // Rerender with descending order
      rerender(
        <TestWrapper>
          <KBSort sortOption={{ field: 'createdDate', order: 'descending' }} />
        </TestWrapper>
      );

      // Check for descending icon (should be present)
      expect(screen.getByRole('button')).toBeInTheDocument();
    });
  });

  describe('Reset Button Logic', () => {
    it('should determine correct default sort state', () => {
      // Test with default sort - component should render without errors
      const { rerender } = render(
        <TestWrapper>
          <KBSort sortOption={DEFAULT_SORT_OPTION} />
        </TestWrapper>
      );

      expect(screen.getByRole('button', { name: /sort/i })).toBeInTheDocument();

      // Test with custom sort - component should render without errors
      rerender(
        <TestWrapper>
          <KBSort sortOption={CUSTOM_SORT_OPTION} />
        </TestWrapper>
      );

      expect(screen.getByRole('button', { name: /sort/i })).toBeInTheDocument();
    });
  });

  describe('Callback Functionality', () => {
    it('should accept onReset callback prop', () => {
      const mockOnReset = vi.fn();

      render(
        <TestWrapper>
          <KBSort sortOption={CUSTOM_SORT_OPTION} onReset={mockOnReset} />
        </TestWrapper>
      );

      // Component should render successfully with onReset prop
      expect(screen.getByRole('button', { name: /sort/i })).toBeInTheDocument();
    });

    it('should accept onSortChange callback prop', () => {
      const mockOnSortChange = vi.fn();

      render(
        <TestWrapper>
          <KBSort sortOption={DEFAULT_SORT_OPTION} onSortChange={mockOnSortChange} />
        </TestWrapper>
      );

      // Component should render successfully with onSortChange prop
      expect(screen.getByRole('button', { name: /sort/i })).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper button roles', () => {
      render(
        <TestWrapper>
          <KBSort />
        </TestWrapper>
      );

      expect(screen.getByRole('button', { name: /sort/i })).toBeInTheDocument();
    });

    it('should be keyboard navigable', () => {
      render(
        <TestWrapper>
          <KBSort />
        </TestWrapper>
      );

      const sortButton = screen.getByRole('button', { name: /sort/i });
      expect(sortButton).not.toBeDisabled();
    });
  });

  describe('Props Handling', () => {
    it('should handle different size props', () => {
      render(
        <TestWrapper>
          <KBSort size='lg' />
        </TestWrapper>
      );

      const sortButton = screen.getByRole('button', { name: /sort/i });
      expect(sortButton).toHaveAttribute('data-size', 'lg');
    });

    it('should handle different variant props', () => {
      render(
        <TestWrapper>
          <KBSort variant='filled' />
        </TestWrapper>
      );

      const sortButton = screen.getByRole('button', { name: /sort/i });
      expect(sortButton).toHaveAttribute('data-variant', 'filled');
    });

    it('should handle disabled state', () => {
      render(
        <TestWrapper>
          <KBSort disabled />
        </TestWrapper>
      );

      const sortButton = screen.getByRole('button', { name: /sort/i });
      expect(sortButton).toBeDisabled();
    });
  });
});
