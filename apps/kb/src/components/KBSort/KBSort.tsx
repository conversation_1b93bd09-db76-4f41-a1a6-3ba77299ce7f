import { ActionIcon, Box, Button, Flex, <PERSON>u, <PERSON>ack, Text, Tooltip } from '@mantine/core';
import {
  IconCircleCheckFilled,
  IconRefresh,
  IconSortAscendingLetters,
  IconSortDescendingLetters,
} from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useState } from 'react';

// Types for sort options
export type SortField = 'createdDate' | 'updatedDate' | 'title';
export type SortOrder = 'ascending' | 'descending';

export interface SortOption {
  field: SortField;
  order: SortOrder;
}

export interface KBSortProps {
  /**
   * Current sort configuration
   */
  sortOption?: SortOption;
  /**
   * Callback when sort option changes
   */
  onSortChange?: (sortOption: SortOption) => void;
  /**
   * Callback when reset is triggered
   */
  onReset?: () => void;
  /**
   * Button size variant
   */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  /**
   * Button variant
   */
  variant?: 'filled' | 'light' | 'outline' | 'subtle' | 'default';
  /**
   * Disable the sort button
   */
  disabled?: boolean;
}

// Sort field options with icons
const SORT_FIELDS = [
  {
    value: 'createdDate' as SortField,
    icon: null,
  },
  {
    value: 'updatedDate' as SortField,
    icon: null,
  },
  {
    value: 'title' as SortField,
    icon: null,
  },
] as const;

// Sort order options
const SORT_ORDERS = [
  {
    value: 'ascending' as SortOrder,
    icon: IconSortAscendingLetters,
  },
  {
    value: 'descending' as SortOrder,
    icon: IconSortDescendingLetters,
  },
] as const;

/**
 * KBSort Component - Provides sorting functionality with dropdown menu
 *
 * Features:
 * - Button with sort icon that changes based on current order (A-Z/Z-A)
 * - Dropdown menu with Sort By and Sort Order sections
 * - Default sort: Created Date, Descending
 * - Accessible and keyboard navigable
 */
export const KBSort: React.FC<KBSortProps> = ({
  sortOption = { field: 'createdDate', order: 'descending' },
  onSortChange,
  onReset,
  size = 'sm',
  variant = 'default',
  disabled = false,
}) => {
  const [opened, setOpened] = useState(false);
  const { t } = useTranslate('kb');

  // Get current sort icon based on order
  const getSortIcon = () => {
    return sortOption.order === 'ascending' ? IconSortAscendingLetters : IconSortDescendingLetters;
  };

  // Handle sort field change
  const handleSortFieldChange = (field: SortField) => {
    const newSortOption = { ...sortOption, field };
    onSortChange?.(newSortOption);
  };

  // Handle sort order change
  const handleSortOrderChange = (order: SortOrder) => {
    const newSortOption = { ...sortOption, order };
    onSortChange?.(newSortOption);
  };

  // Handle reset to default
  const handleReset = () => {
    onReset?.();
    setOpened(false);
  };

  // Check if current sort is default
  const isDefaultSort = sortOption.field === 'createdDate' && sortOption.order === 'descending';

  const SortIcon = getSortIcon();

  return (
    <Menu opened={opened} onChange={setOpened} position='bottom-start' shadow='md' width={300}>
      <Menu.Target>
        <Button
          variant={variant}
          size={size}
          disabled={disabled}
          leftSection={<SortIcon size={16} />}
          styles={{
            section: {
              marginLeft: size === 'xs' ? 4 : 6,
              marginRight: size === 'xs' ? 4 : 6,
            },
          }}
        >
          {t('sorting.sort')}
        </Button>
      </Menu.Target>

      <Menu.Dropdown>
        <Stack gap='xs' p='xs'>
          {/* Header with Reset Button */}
          <Flex justify='space-between' align='center' mb='xs'>
            <Text size='sm' fw={600} c='dark'>
              {t('sorting.title')}
            </Text>
            {!isDefaultSort && (
              <Tooltip label={t('sorting.reset')} position='top' withArrow>
                <ActionIcon
                  variant='subtle'
                  color='gray'
                  size='sm'
                  onClick={handleReset}
                  data-testid='reset-button'
                >
                  <IconRefresh size={14} data-testid='refresh-icon' />
                </ActionIcon>
              </Tooltip>
            )}
          </Flex>

          {/* Sort By Section */}
          <Box>
            <Text size='sm' fw={500} c='dimmed' mb='xs'>
              {t('sorting.sortBy')}
            </Text>
            <Stack gap={2}>
              {SORT_FIELDS.map((field) => {
                const isSelected = sortOption.field === field.value;

                return (
                  <Menu.Item
                    key={field.value}
                    onClick={() => handleSortFieldChange(field.value)}
                    rightSection={
                      isSelected ? (
                        <IconCircleCheckFilled size={16} color='var(--mantine-color-green-6)' />
                      ) : null
                    }
                    bg={isSelected ? 'var(--mantine-color-gray-1)' : undefined}
                    styles={{
                      item: {
                        borderRadius: 'var(--mantine-radius-sm)',
                      },
                    }}
                  >
                    <Text size='sm' fz={14}>
                      {t(`sorting.fields.${field.value}`)}
                    </Text>
                  </Menu.Item>
                );
              })}
            </Stack>
          </Box>

          <Menu.Divider />

          {/* Sort Order Section */}
          <Box>
            <Text size='sm' fw={500} c='dimmed' mb='xs'>
              {t('sorting.sortOrder')}
            </Text>
            <Stack gap={2}>
              {SORT_ORDERS.map((order) => {
                const OrderIcon = order.icon;
                const isSelected = sortOption.order === order.value;

                return (
                  <Menu.Item
                    key={order.value}
                    onClick={() => handleSortOrderChange(order.value)}
                    leftSection={<OrderIcon size={16} />}
                    rightSection={
                      isSelected ? (
                        <IconCircleCheckFilled size={16} color='var(--mantine-color-green-6)' />
                      ) : null
                    }
                    bg={isSelected ? 'var(--mantine-color-gray-1)' : undefined}
                    styles={{
                      item: {
                        borderRadius: 'var(--mantine-radius-sm)',
                      },
                    }}
                  >
                    <Text size='sm' fz={14}>
                      {t(`sorting.orders.${order.value}`)}
                    </Text>
                  </Menu.Item>
                );
              })}
            </Stack>
          </Box>
        </Stack>
      </Menu.Dropdown>
    </Menu>
  );
};

export default KBSort;
