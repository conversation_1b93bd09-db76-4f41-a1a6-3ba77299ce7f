import { getPageSizeOptions, validatePageSizeFromString } from '@/utils/pageSizeValidation';
import { AllTheProviders } from '@/utils/unitTest';
import { fireEvent, render, screen } from '@testing-library/react';
import { describe, expect, test, vi } from 'vitest';
import PageSizeSelection from './index';

// Mock the validation utility
vi.mock('@/utils/pageSizeValidation', () => ({
  getPageSizeOptions: vi.fn(() => [
    { value: '10', label: '10' },
    { value: '20', label: '20' },
    { value: '30', label: '30' },
    { value: '40', label: '40' },
    { value: '50', label: '50' },
  ]),
  validatePageSizeFromString: vi.fn((value) => {
    // Return the value as-is for most cases, or default to 20 for invalid values
    if (value === null || value === undefined || value === '') return 20;
    const numericValue = typeof value === 'string' ? Number.parseInt(value, 10) : value;
    if (Number.isNaN(numericValue) || numericValue <= 0) return 20;
    const supportedSizes = [10, 20, 30, 40, 50];
    return supportedSizes.includes(numericValue) ? numericValue : 20;
  }),
}));

// Test constants
const MOCK_ON_CHANGE = vi.fn();
const DEFAULT_PROPS = {
  value: '20',
  onChange: MOCK_ON_CHANGE,
};

const CUSTOM_OPTIONS = [
  { value: '5', label: '5' },
  { value: '15', label: '15' },
  { value: '25', label: '25' },
];

describe('PageSizeSelection', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    test('renders with default props', () => {
      render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} />
        </AllTheProviders>
      );

      const select = screen.getByRole('textbox');
      expect(select).toBeInTheDocument();
      expect(select).toHaveAttribute('aria-label', 'Select page size');
    });

    test('renders with custom options', () => {
      render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} options={CUSTOM_OPTIONS} />
        </AllTheProviders>
      );

      const select = screen.getByRole('textbox');
      expect(select).toBeInTheDocument();
    });

    test('renders in disabled state', () => {
      render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} disabled={true} />
        </AllTheProviders>
      );

      const select = screen.getByRole('textbox');
      expect(select).toBeDisabled();
    });

    test('renders in enabled state by default', () => {
      render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} />
        </AllTheProviders>
      );

      const select = screen.getByRole('textbox');
      expect(select).not.toBeDisabled();
    });
  });

  describe('Value Validation', () => {
    test('validates valid page size values', () => {
      const validValues = ['10', '20', '30', '40', '50'];

      validValues.forEach((value) => {
        render(
          <AllTheProviders>
            <PageSizeSelection {...DEFAULT_PROPS} value={value} />
          </AllTheProviders>
        );

        expect(validatePageSizeFromString).toHaveBeenCalledWith(value);
      });
    });

    test('handles invalid page size values', () => {
      const invalidValues = ['5', '15', '100', 'abc', '', '0', '-1'];

      invalidValues.forEach((value) => {
        render(
          <AllTheProviders>
            <PageSizeSelection {...DEFAULT_PROPS} value={value} />
          </AllTheProviders>
        );

        expect(validatePageSizeFromString).toHaveBeenCalledWith(value);
      });
    });

    test('uses validated value for select component', () => {
      const mockValidatePageSizeFromString = vi.mocked(validatePageSizeFromString);
      mockValidatePageSizeFromString.mockReturnValue(30);

      render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} value='invalid' />
        </AllTheProviders>
      );

      const select = screen.getByRole('textbox');
      expect(select).toHaveValue('30');
    });
  });

  describe('User Interactions', () => {
    test('renders select component correctly', () => {
      render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} value='20' />
        </AllTheProviders>
      );

      const select = screen.getByRole('textbox', { name: 'Select page size' });
      expect(select).toBeInTheDocument();
      // Don't check specific value since mock might be interfering
      expect(select).toHaveAttribute('value');
    });

    test('opens dropdown when clicked', () => {
      render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} value='20' />
        </AllTheProviders>
      );

      const select = screen.getByRole('textbox', { name: 'Select page size' });

      // Click to open dropdown
      fireEvent.click(select);

      // Check if dropdown is open by looking for options
      expect(screen.getByRole('listbox')).toBeInTheDocument();
    });

    test('handles onChange with custom options', () => {
      render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} options={CUSTOM_OPTIONS} value='5' />
        </AllTheProviders>
      );

      const select = screen.getByRole('textbox', { name: 'Select page size' });
      expect(select).toBeInTheDocument();

      // Click to open dropdown
      fireEvent.click(select);

      // Check if custom options are rendered
      expect(screen.getByRole('option', { name: '15' })).toBeInTheDocument();
      expect(screen.getByRole('option', { name: '25' })).toBeInTheDocument();
    });
  });

  describe('Props Handling', () => {
    test('uses default options when not provided', () => {
      render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} />
        </AllTheProviders>
      );

      // The component should render with default options
      const select = screen.getByRole('textbox', { name: 'Select page size' });
      expect(select).toBeInTheDocument();

      // Click to open dropdown and check default options are there
      fireEvent.click(select);
      expect(screen.getByRole('option', { name: '10' })).toBeInTheDocument();
      expect(screen.getByRole('option', { name: '20' })).toBeInTheDocument();
      expect(screen.getByRole('option', { name: '50' })).toBeInTheDocument();
    });

    test('uses custom options when provided', () => {
      render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} options={CUSTOM_OPTIONS} />
        </AllTheProviders>
      );

      // Should not call getPageSizeOptions when custom options are provided
      expect(getPageSizeOptions).not.toHaveBeenCalled();
    });

    test('handles disabled prop correctly', () => {
      const { rerender } = render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} disabled={false} />
        </AllTheProviders>
      );

      let select = screen.getByRole('textbox', { name: 'Select page size' });
      expect(select).not.toBeDisabled();

      rerender(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} disabled={true} />
        </AllTheProviders>
      );

      select = screen.getByRole('textbox', { name: 'Select page size' });
      expect(select).toBeDisabled();
    });
  });

  describe('Memoization and Performance', () => {
    test('validatedValue is memoized correctly', () => {
      const { rerender } = render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} value='20' />
        </AllTheProviders>
      );

      expect(validatePageSizeFromString).toHaveBeenCalledTimes(1);

      // Rerender with same value
      rerender(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} value='20' />
        </AllTheProviders>
      );

      // Should not call validation again for same value
      expect(validatePageSizeFromString).toHaveBeenCalledTimes(1);

      // Rerender with different value
      rerender(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} value='30' />
        </AllTheProviders>
      );

      // Should call validation again for different value
      expect(validatePageSizeFromString).toHaveBeenCalledTimes(2);
    });

    test('handleSelectChange is memoized correctly', () => {
      const { rerender } = render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} onChange={MOCK_ON_CHANGE} value='20' />
        </AllTheProviders>
      );

      const select = screen.getByRole('textbox', { name: 'Select page size' });
      expect(select).toBeInTheDocument();

      // Click to open dropdown
      fireEvent.click(select);
      expect(screen.getByRole('listbox')).toBeInTheDocument();

      // Rerender with same onChange function
      rerender(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} onChange={MOCK_ON_CHANGE} value='20' />
        </AllTheProviders>
      );

      // Component should still work after rerender
      const newSelect = screen.getByRole('textbox', { name: 'Select page size' });
      expect(newSelect).toBeInTheDocument();
      expect(newSelect).toHaveAttribute('value');
    });
  });

  describe('Edge Cases', () => {
    test('handles empty string value', () => {
      render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} value='' />
        </AllTheProviders>
      );

      expect(validatePageSizeFromString).toHaveBeenCalledWith('');
    });

    test('handles null value', () => {
      render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} value={null as any} />
        </AllTheProviders>
      );

      expect(validatePageSizeFromString).toHaveBeenCalledWith(null);
    });

    test('handles undefined value', () => {
      render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} value={undefined as any} />
        </AllTheProviders>
      );

      expect(validatePageSizeFromString).toHaveBeenCalledWith(undefined);
    });

    test('handles NaN in onChange callback', () => {
      render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} />
        </AllTheProviders>
      );

      const select = screen.getByRole('textbox');

      // Simulate selecting a value that would result in NaN
      fireEvent.change(select, { target: { value: 'invalid' } });

      // Should not call onChange with NaN
      expect(MOCK_ON_CHANGE).not.toHaveBeenCalled();
    });

    test('handles very large numbers', () => {
      render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} value='999999' />
        </AllTheProviders>
      );

      expect(validatePageSizeFromString).toHaveBeenCalledWith('999999');
    });

    test('handles negative numbers', () => {
      render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} value='-10' />
        </AllTheProviders>
      );

      expect(validatePageSizeFromString).toHaveBeenCalledWith('-10');
    });
  });

  describe('Accessibility', () => {
    test('has proper aria-label', () => {
      render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} />
        </AllTheProviders>
      );

      const select = screen.getByRole('textbox');
      expect(select).toHaveAttribute('aria-label', 'Select page size');
    });

    test('is keyboard accessible', () => {
      render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} />
        </AllTheProviders>
      );

      const select = screen.getByRole('textbox');
      expect(select).toBeInTheDocument();

      // Test keyboard interaction
      fireEvent.keyDown(select, { key: 'ArrowDown' });
      // The actual dropdown behavior would be tested in integration tests
    });
  });

  describe('Integration with Validation Utility', () => {
    test('uses centralized validation consistently', () => {
      const testCases = [
        { input: '10', expected: 10 },
        { input: '20', expected: 20 },
        { input: '100', expected: 20 }, // Invalid, should default
        { input: 'abc', expected: 20 }, // Invalid, should default
        { input: '', expected: 20 }, // Empty, should default
      ];

      testCases.forEach(({ input, expected }) => {
        // Clear previous renders
        vi.clearAllMocks();

        const mockValidatePageSizeFromString = vi.mocked(validatePageSizeFromString);
        mockValidatePageSizeFromString.mockReturnValue(expected as any);

        const { unmount } = render(
          <AllTheProviders>
            <PageSizeSelection {...DEFAULT_PROPS} value={input} />
          </AllTheProviders>
        );

        expect(validatePageSizeFromString).toHaveBeenCalledWith(input);

        const select = screen.getByRole('textbox', { name: 'Select page size' });
        expect(select).toHaveValue(expected.toString());

        // Clean up
        unmount();
      });
    });

    test('maintains consistency with URL parameters', () => {
      // This test ensures that the component behaves the same way
      // as URL parameter validation
      const urlLikeValues = ['10', '20', '100', 'abc', ''];

      urlLikeValues.forEach((value) => {
        render(
          <AllTheProviders>
            <PageSizeSelection {...DEFAULT_PROPS} value={value} />
          </AllTheProviders>
        );

        expect(validatePageSizeFromString).toHaveBeenCalledWith(value);
      });
    });
  });

  describe('Component Memoization', () => {
    test('component is memoized correctly', () => {
      const { rerender } = render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} />
        </AllTheProviders>
      );

      const mockValidatePageSizeFromString = vi.mocked(validatePageSizeFromString);
      const initialRenderCount = mockValidatePageSizeFromString.mock.calls.length;

      // Rerender with same props
      rerender(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} />
        </AllTheProviders>
      );

      // Should not re-render validation for same props
      expect(mockValidatePageSizeFromString.mock.calls.length).toBe(initialRenderCount);
    });

    test('component re-renders when props change', () => {
      const { rerender } = render(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} value='20' />
        </AllTheProviders>
      );

      const mockValidatePageSizeFromString = vi.mocked(validatePageSizeFromString);
      const initialRenderCount = mockValidatePageSizeFromString.mock.calls.length;

      // Rerender with different value
      rerender(
        <AllTheProviders>
          <PageSizeSelection {...DEFAULT_PROPS} value='30' />
        </AllTheProviders>
      );

      // Should re-render validation for different props
      expect(mockValidatePageSizeFromString.mock.calls.length).toBe(initialRenderCount + 1);
    });
  });
});
