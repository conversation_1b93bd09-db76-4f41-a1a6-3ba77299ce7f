import { getPageSizeOptions, validatePageSizeFromString } from '@/utils/pageSizeValidation';
import { Select, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconChevronDown } from '@tabler/icons-react';
import { memo, useCallback, useMemo } from 'react';

/**
 * Props for the PageSizeSelection component
 */
interface PageSizeSelectionProps {
  /** Current selected page size value as string */
  value: string;
  /** Whether the select is disabled */
  disabled?: boolean;
  /** Custom options for the select (defaults to supported page sizes) */
  options?: { value: string; label: string }[];
  /** Callback fired when page size changes */
  onChange: (value: number) => void;
}

const useStyles = createStyles((theme) => ({
  select: {
    width: rem(70),
    margin: `0 ${rem(4)}`,
    borderColor: theme.colors.decaLight[4],
  },
  input: {
    fontWeight: 500,
    '&:focus-within': {
      borderColor: theme.colors.decaLight[5],
    },
  },
}));

// Use centralized page size options
const DEFAULT_PAGE_SIZE_OPTIONS = getPageSizeOptions();

/**
 * PageSizeSelection component for selecting page size in pagination
 *
 * Features:
 * - Validates input values against supported page sizes
 * - Uses centralized validation logic
 * - Provides consistent behavior across the application
 * - Handles invalid values gracefully by defaulting to supported values
 *
 * @param props - Component props
 * @returns JSX element
 */
const PageSizeSelection: React.FC<PageSizeSelectionProps> = ({
  value,
  disabled = false,
  options = DEFAULT_PAGE_SIZE_OPTIONS,
  onChange,
}) => {
  const { classes } = useStyles();

  // Validate that the current value exists in options using centralized validation
  // This ensures consistency with URL parameters and other parts of the application
  const validatedValue = useMemo(() => {
    const validatedPageSize = validatePageSizeFromString(value);
    return validatedPageSize.toString();
  }, [value]);

  /**
   * Handles select change events
   * Converts string value to number and calls onChange callback
   */
  const handleSelectChange = useCallback(
    (selectedValue: string | null) => {
      if (selectedValue) {
        const numericValue = Number.parseInt(selectedValue, 10);
        // Additional safety check - should not be needed due to validation
        if (!Number.isNaN(numericValue)) {
          onChange(numericValue);
        }
      }
    },
    [onChange]
  );

  return (
    <Select
      classNames={{ wrapper: classes.select, input: classes.input }}
      rightSection={<IconChevronDown size={16} />}
      disabled={disabled}
      radius='xs'
      withCheckIcon={false}
      value={validatedValue}
      onChange={handleSelectChange}
      data={options}
      rightSectionPointerEvents='none'
      aria-label='Select page size'
    />
  );
};

export default memo(PageSizeSelection);
