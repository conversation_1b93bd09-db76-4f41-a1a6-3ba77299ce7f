import { modals } from '@mantine/modals';
import { useTranslate } from '@tolgee/react';
import { useCallback } from 'react';

import { KBDetailForm } from '@/components/common';
import { ROOT_PATH } from '@/constants/folder';
import { useAppContext } from '@/contexts/AppContext';
import { KBSelectionContextProvider } from '@/contexts/KBSelectionContext';
import { useApiHand<PERSON>, useModalManager, useNotifications } from '@/hooks';
import { ArticleAPI, KbAPI } from '@/services/api/v2';
import type { Article } from '@/types';
import ArticleMovingModalContent from './ArticleMovingModalContent';

export const useArticleMovingModal = () => {
  const { t } = useTranslate(['article', 'kb']);
  const { modalClasses, createModal } = useModalManager();
  const { notifyMessage } = useNotifications(t);
  const { openConfirmModal, closeConfirmModal } = useAppContext();
  const { handleApiRequest } = useApiHandler();

  /**
   * Move Article to another Knowledge Base
   * @param {Article} article
   * @param {string} newBaseId
   * @param {() => void} afterMoving
   */
  const moveArticle = useCallback(
    async (article: Article, newBaseId: string, afterMoving?: () => void) => {
      const { id: articleId } = article;

      await handleApiRequest(
        ArticleAPI.update(articleId, {
          ...article,
          relatedArticles:
            article.relatedArticles?.map((relatedArticle) => relatedArticle.id) || [],
          baseId: newBaseId,
        }),
        {
          fallbackMessage: t('articleMoving.failedMessage'),
          fallbackTitle: t('articleMoving.failedTitle'),
          successMessage: t('articleMoving.successMessage'),
          successTitle: t('articleMoving.successTitle'),
          disableRedirect: true, // Disable 404 redirect for MOVE operations
          successCallback: () => {
            modals.closeAll();
            closeConfirmModal();
            afterMoving?.();
          },
        }
      );
    },
    [t, handleApiRequest, closeConfirmModal]
  );

  /**
   * Confirm and Move Article to another Knowledge Base
   * @param {Article} article
   * @param {string} newBaseId
   * @param {() => void} afterMoving
   */
  const confirmAndMoveArticle = useCallback(
    async (article: Article, newBaseId: string, afterMoving?: () => void) => {
      if (!newBaseId) return;

      const { data: newBase } = await KbAPI.get(newBaseId);

      if (newBase) {
        const moveHandler = async () => {
          await moveArticle(article, newBaseId, afterMoving);
        };

        const toKBName = newBase.data?.name || t('unknownKBName', { ns: 'kb' });
        openConfirmModal({
          onConfirm: moveHandler,
          title: t('articleMoving.confirmMovingMessage', { toKBName } as any, { ns: 'article' }),
          confirmText: t('move', { ns: 'kb' }),
          cancelText: t('cancel', { ns: 'kb' }),
          options: {
            className: modalClasses.confirmModal,
            modalSize: '465px',
          },
        });
      }
    },
    [moveArticle, t, openConfirmModal, modalClasses]
  );

  /**
   * Article Moving Modal
   * @type {ModalProps}
   */
  const buildArticleMovingModal = useCallback(
    (
      currentArticle: Article,
      currentFolderId: string = ROOT_PATH,
      currentBaseId = '',
      afterMoving?: () => void
    ) =>
      createModal({
        title: t('articleMoving.title'),
        classNames: {
          content: modalClasses.largeModal,
        },
        onClose: () => modals.closeAll(),
        children: (
          <KBSelectionContextProvider>
            <ArticleMovingModalContent
              currentArticle={currentArticle}
              currentFolderId={currentFolderId}
              currentBaseId={currentBaseId}
              onMoveAsync={async (newBaseId: string) => {
                await confirmAndMoveArticle(currentArticle, newBaseId, afterMoving);
              }}
              onClose={() => modals.closeAll()}
              onCreateNewKB={(parentFolderId: string) =>
                modals.open(buildCreateKBModal(currentArticle, parentFolderId, afterMoving))
              }
            />
          </KBSelectionContextProvider>
        ),
      }),
    [confirmAndMoveArticle, modalClasses, t, createModal]
  );

  /**
   * Create Knowledge Base Modal
   * @type {ModalProps}
   */
  const buildCreateKBModal = useCallback(
    (currentArticle: Article, parentFolderId: string, afterMoving?: () => void) => {
      return createModal({
        title: t('modal.createKnowledgeBase', { ns: 'kb' }),
        classNames: {
          content: modalClasses.largeModal,
        },
        onClose: () =>
          modals.open(buildArticleMovingModal(currentArticle, parentFolderId, '', afterMoving)),
        children: (
          <KBDetailForm
            onCancel={() =>
              modals.open(buildArticleMovingModal(currentArticle, parentFolderId, '', afterMoving))
            }
            onSubmitted={(response: any) => {
              if (response?.status === 'success') {
                notifyMessage(
                  t('createKB.successTitle', { ns: 'kb' }),
                  t('createKB.successMessage', { ns: 'kb' })
                );
                modals.open(
                  buildArticleMovingModal(
                    currentArticle,
                    parentFolderId,
                    response.data.id,
                    afterMoving
                  )
                );
              }
            }}
            parentDirId={parentFolderId}
          />
        ),
      });
    },
    [buildArticleMovingModal, modalClasses, t, notifyMessage, createModal]
  );

  return {
    openArticleMovingModal: (
      article: Article,
      parentFolderId: string = ROOT_PATH,
      baseId = '',
      afterMoving?: () => void
    ) => {
      modals.open(buildArticleMovingModal(article, parentFolderId, baseId, afterMoving));
    },
  };
};
