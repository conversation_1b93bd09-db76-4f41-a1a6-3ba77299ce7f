import type { Job } from '@/types/job';
import { Grid } from '@mantine/core';
import { memo, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import JobCard from '../JobCard';

interface JobsGridProps {
  jobs: Job[];
  onDelete?: (jobId: string) => void;
}

/**
 * JobsGrid component that renders a grid of job cards
 * Jobs are pre-filtered at the API level based on the current tab
 */

const JobsGrid = ({ jobs, onDelete }: JobsGridProps) => {
  const navigate = useNavigate();

  const goToJobDetail = useCallback(
    (jobId: string) => {
      navigate(`/kb/jobs/${jobId}`);
    },
    [navigate]
  );

  const goToJobResult = useCallback(
    (jobId: string) => {
      navigate(`/kb/jobs/${jobId}/result`);
    },
    [navigate]
  );

  return (
    <Grid gutter='md'>
      {jobs.map((job) => (
        <Grid.Col key={job.id} span={{ base: 4, xs: 6, md: 4, xl: 3 }}>
          <JobCard
            job={job}
            onDelete={onDelete}
            onDetail={goToJobDetail}
            onResult={goToJobResult}
          />
        </Grid.Col>
      ))}
    </Grid>
  );
};

export default memo(JobsGrid);
