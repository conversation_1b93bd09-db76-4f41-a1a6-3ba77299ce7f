import type { Job } from '@/types/job';
import { JOB_STATUS, JobType } from '@/types/job';
import { renderWithProviders } from '@/utils/unitTest';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import JobsGrid from './index';

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', () => ({
  useNavigate: () => mockNavigate,
}));

// Mock JobCard component
vi.mock('../JobCard', () => ({
  default: ({ job, onDelete, onDetail, onResult }: any) => (
    <div data-testid={`job-card-${job.id}`}>
      <span data-testid='job-name'>{job.name}</span>
      <span data-testid='job-type'>{job.jobType}</span>
      <button data-testid='detail-button' onClick={() => onDetail(job.id)} type='button'>
        Detail
      </button>
      <button data-testid='result-button' onClick={() => onResult(job.id)} type='button'>
        Result
      </button>
      <button data-testid='delete-button' onClick={() => onDelete?.(job.id)} type='button'>
        Delete
      </button>
    </div>
  ),
}));

// Test data constants
const MOCK_GENERATION_JOB: Job = {
  id: 'gen-job-1',
  name: 'Generation Job 1',
  jobType: JobType.ContentGeneration,
  status: JOB_STATUS.succeeded,
  createdAt: new Date('2024-01-01'),
  initiatedBy: 'user-1',
  baseName: 'Generation Job',
  error: '',
  config: {
    schedule: {},
    retriable: true,
  },
  metadata: {
    documentIds: ['doc-1'],
  },
  data: {},
  createdBy: {
    id: 'user-1',
    orgId: 'org-1',
    createdAt: '2024-01-01T00:00:00Z',
    displayName: 'Test User',
    email: '<EMAIL>',
    familyName: 'User',
    givenName: 'Test',
    picture: '',
    updatedAt: '2024-01-01T00:00:00Z',
  },
};

const MOCK_EXPORT_JOB: Job = {
  id: 'exp-job-1',
  name: 'Export Job 1',
  jobType: JobType.ArticleExport,
  status: JOB_STATUS.succeeded,
  createdAt: new Date('2024-01-02'),
  initiatedBy: 'user-1',
  baseName: 'Export Job',
  error: '',
  config: {
    schedule: {},
    retriable: true,
  },
  metadata: {
    documentIds: ['doc-2'],
    fileFormat: 'excel',
    contentFormat: 'html',
    includeStats: true,
    includeCustomData: false,
  },
  data: {},
  createdBy: {
    id: 'user-1',
    orgId: 'org-1',
    createdAt: '2024-01-01T00:00:00Z',
    displayName: 'Test User',
    email: '<EMAIL>',
    familyName: 'User',
    givenName: 'Test',
    picture: '',
    updatedAt: '2024-01-01T00:00:00Z',
  },
};

const MOCK_OTHER_JOB: Job = {
  id: 'other-job-1',
  name: 'Other Job 1',
  jobType: JobType.DocumentProcessing,
  status: JOB_STATUS.running,
  createdAt: new Date('2024-01-03'),
  initiatedBy: 'user-1',
  baseName: 'Other Job',
  error: '',
  config: {
    schedule: {},
    retriable: false,
  },
  metadata: {
    documentIds: ['doc-3'],
  },
  data: {},
  createdBy: {
    id: 'user-1',
    orgId: 'org-1',
    createdAt: '2024-01-01T00:00:00Z',
    displayName: 'Test User',
    email: '<EMAIL>',
    familyName: 'User',
    givenName: 'Test',
    picture: '',
    updatedAt: '2024-01-01T00:00:00Z',
  },
};

const MOCK_JOBS = [MOCK_GENERATION_JOB, MOCK_EXPORT_JOB, MOCK_OTHER_JOB];

describe('JobsGrid', () => {
  const mockOnDelete = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockNavigate.mockClear();
  });

  describe('Job Rendering', () => {
    it('renders all provided jobs', () => {
      renderWithProviders(<JobsGrid jobs={MOCK_JOBS} onDelete={mockOnDelete} />);

      // Should render all 3 jobs
      expect(screen.getByTestId('job-card-gen-job-1')).toBeInTheDocument();
      expect(screen.getByTestId('job-card-exp-job-1')).toBeInTheDocument();
      expect(screen.getByTestId('job-card-other-job-1')).toBeInTheDocument();
    });

    it('renders only generation jobs when only generation jobs are provided', () => {
      renderWithProviders(<JobsGrid jobs={[MOCK_GENERATION_JOB]} onDelete={mockOnDelete} />);

      // Should render only generation job
      expect(screen.getByTestId('job-card-gen-job-1')).toBeInTheDocument();
      expect(screen.queryByTestId('job-card-exp-job-1')).not.toBeInTheDocument();
      expect(screen.queryByTestId('job-card-other-job-1')).not.toBeInTheDocument();
    });

    it('renders only export jobs when only export jobs are provided', () => {
      renderWithProviders(<JobsGrid jobs={[MOCK_EXPORT_JOB]} onDelete={mockOnDelete} />);

      // Should render only export job
      expect(screen.queryByTestId('job-card-gen-job-1')).not.toBeInTheDocument();
      expect(screen.getByTestId('job-card-exp-job-1')).toBeInTheDocument();
      expect(screen.queryByTestId('job-card-other-job-1')).not.toBeInTheDocument();
    });

    it('renders empty grid when no jobs are provided', () => {
      renderWithProviders(<JobsGrid jobs={[]} onDelete={mockOnDelete} />);

      // Should render grid but no job cards
      const grid = document.querySelector('.mantine-Grid-root');
      const gridInner = document.querySelector('.mantine-Grid-inner');
      expect(grid).toBeInTheDocument();
      expect(gridInner?.children).toHaveLength(0);
    });
  });

  describe('Navigation Callbacks', () => {
    it('passes correct callbacks to JobCard component', () => {
      renderWithProviders(<JobsGrid jobs={[MOCK_GENERATION_JOB]} onDelete={mockOnDelete} />);

      // Verify JobCard is rendered with correct props (mocked implementation)
      expect(screen.getByTestId('job-card-gen-job-1')).toBeInTheDocument();
      expect(screen.getByTestId('detail-button')).toBeInTheDocument();
      expect(screen.getByTestId('result-button')).toBeInTheDocument();
    });

    it('renders correct number of JobCard components', () => {
      renderWithProviders(<JobsGrid jobs={MOCK_JOBS} onDelete={mockOnDelete} />);

      // Should render all 3 job cards
      expect(screen.getByTestId('job-card-gen-job-1')).toBeInTheDocument();
      expect(screen.getByTestId('job-card-exp-job-1')).toBeInTheDocument();
      expect(screen.getByTestId('job-card-other-job-1')).toBeInTheDocument();
    });
  });

  describe('Delete Functionality', () => {
    it('calls onDelete with correct job id when delete button is clicked', () => {
      renderWithProviders(<JobsGrid jobs={[MOCK_GENERATION_JOB]} onDelete={mockOnDelete} />);

      const deleteButton = screen.getByTestId('delete-button');
      fireEvent.click(deleteButton);

      expect(mockOnDelete).toHaveBeenCalledWith('gen-job-1');
    });

    it('works correctly when onDelete is not provided', () => {
      expect(() => {
        renderWithProviders(<JobsGrid jobs={[MOCK_GENERATION_JOB]} />);

        const deleteButton = screen.getByTestId('delete-button');
        fireEvent.click(deleteButton);
      }).not.toThrow();
    });
  });

  describe('Grid Layout', () => {
    it('renders jobs in a grid layout with correct responsive columns', () => {
      const { container } = renderWithProviders(
        <JobsGrid jobs={MOCK_JOBS} onDelete={mockOnDelete} />
      );

      const gridContainer = container.querySelector('.mantine-Grid-root');
      expect(gridContainer).toBeInTheDocument();
      expect(gridContainer).toHaveClass('mantine-Grid-root');
    });
  });

  describe('Empty State', () => {
    it('renders empty grid when no jobs are provided', () => {
      const { container } = renderWithProviders(<JobsGrid jobs={[]} onDelete={mockOnDelete} />);

      // Grid should be present but inner should be empty
      const grid = container.querySelector('.mantine-Grid-root');
      const gridInner = container.querySelector('.mantine-Grid-inner');
      expect(grid).toBeInTheDocument();
      expect(gridInner?.children).toHaveLength(0);
    });

    it('renders jobs when jobs array has data', () => {
      renderWithProviders(<JobsGrid jobs={[MOCK_GENERATION_JOB]} onDelete={mockOnDelete} />);

      // Should render the job card
      expect(screen.getByTestId('job-card-gen-job-1')).toBeInTheDocument();
      expect(screen.getByTestId('job-name')).toHaveTextContent('Generation Job 1');
      expect(screen.getByTestId('job-type')).toHaveTextContent('content-generation');
    });
  });

  describe('Component Memoization', () => {
    it('component is properly memoized', () => {
      const { rerender } = renderWithProviders(
        <JobsGrid jobs={MOCK_JOBS} onDelete={mockOnDelete} />
      );

      // Re-render with same props should not cause unnecessary re-renders
      rerender(<JobsGrid jobs={MOCK_JOBS} onDelete={mockOnDelete} />);

      // Verify component still works correctly
      expect(screen.getByTestId('job-card-gen-job-1')).toBeInTheDocument();
      expect(screen.getByTestId('job-card-exp-job-1')).toBeInTheDocument();
      expect(screen.getByTestId('job-card-other-job-1')).toBeInTheDocument();
    });
  });
});
