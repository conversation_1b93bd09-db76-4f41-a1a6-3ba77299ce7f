import { JOB_TAB_VALUES } from '@/constants/job';
import { JobContextProvider } from '@/contexts/JobContext';
import { JobAPI } from '@/services/api/v2';
import type { JobTabValue } from '@/types/job';
import { JOB_STATUS, JobType } from '@/types/job';
import { MantineProvider, createTheme } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { act, fireEvent, render, screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import KBJobs from './KBJobs';

// Mock the JobAPI
vi.mock('@/services/api/v2', () => ({
  JobAPI: {
    getJobs: vi.fn(),
    deleteJob: vi.fn(),
  },
}));

// Mock utility functions
vi.mock('@/utils/api', () => ({
  getPaginationWithDirection: vi.fn(() => ({
    cursor: 'test-cursor',
    direction: 'backward',
    hasMoreData: true,
  })),
}));

vi.mock('@/utils/dateTime', () => ({
  sortByCreatedAt: vi.fn((jobs) => jobs),
}));

// Mock translation hook
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'tabs.allJobs': 'All Jobs',
        'tabs.generationJobs': 'Generation Jobs',
        'tabs.exportJobs': 'Export Jobs',
      };
      return translations[key] || key;
    },
  }),
  TolgeeProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

// Mock intersection hook
vi.mock('@mantine/hooks', () => ({
  useIntersection: () => ({
    ref: vi.fn(),
    entry: { isIntersecting: false } as IntersectionObserverEntry,
  }),
}));

// Mock child components
vi.mock('./JobsEmpty', () => ({
  default: () => <div data-testid='jobs-empty'>No jobs found</div>,
}));

vi.mock('./JobsGrid', () => ({
  default: ({ jobs, onDelete }: { jobs: any[]; onDelete: (id: string) => void }) => (
    <div data-testid='jobs-grid'>
      {jobs.map((job) => (
        <div key={job.id} data-testid={`job-item-${job.id}`}>
          <span>{job.name}</span>
          <button
            type='button'
            onClick={() => onDelete(job.id)}
            data-testid={`delete-btn-${job.id}`}
          >
            Delete
          </button>
        </div>
      ))}
    </div>
  ),
}));

// Mock data
const MOCK_JOBS_RESPONSE = {
  status: 'success',
  data: [
    {
      id: 'job-1',
      name: 'Generation Job 1',
      jobType: JobType.ContentGeneration,
      status: JOB_STATUS.succeeded,
      createdAt: new Date('2024-01-01'),
      initiatedBy: 'user-1',
      baseName: 'Generation Job',
      error: '',
      config: { schedule: {}, retriable: true },
      metadata: { documentIds: ['doc-1'] },
      data: {},
      createdBy: {
        id: 'user-1',
        orgId: 'org-1',
        createdAt: '2024-01-01T00:00:00Z',
        displayName: 'Test User',
        email: '<EMAIL>',
        familyName: 'User',
        givenName: 'Test',
        picture: '',
        updatedAt: '2024-01-01T00:00:00Z',
      },
    },
  ],
  pagination: {
    first: 'first-cursor',
    last: 'last-cursor',
    hasNextPage: false,
    hasPreviousPage: false,
  },
};

const EMPTY_JOBS_RESPONSE = {
  status: 'success',
  data: [],
  pagination: {
    first: '',
    last: '',
    hasNextPage: false,
    hasPreviousPage: false,
  },
};

// Test wrapper component with proper theme that includes custom colors
const testTheme = createTheme({
  colors: {
    decaLight: [
      '#f8f9fa',
      '#e9ecef',
      '#dee2e6',
      '#ced4da',
      '#adb5bd',
      '#6c757d',
      '#495057',
      '#343a40',
      '#212529',
      '#000000',
    ],
    decaDark: [
      '#f8f9fa',
      '#e9ecef',
      '#dee2e6',
      '#ced4da',
      '#6c757d',
      '#495057',
      '#343a40',
      '#212529',
      '#000000',
      '#000000',
    ],
    decaNavy: [
      '#f1f3f4',
      '#e8eaed',
      '#dadce0',
      '#bdc1c6',
      '#1a73e8',
      '#1557b0',
      '#0f4c75',
      '#0a3d62',
      '#082e4a',
      '#061f32',
    ],
  },
});

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <MantineProvider theme={testTheme}>
    <MantineEmotionProvider>
      <JobContextProvider>{children}</JobContextProvider>
    </MantineEmotionProvider>
  </MantineProvider>
);

describe('KBJobs Component', () => {
  const mockGetJobs = vi.mocked(JobAPI.getJobs);
  const mockDeleteJob = vi.mocked(JobAPI.deleteJob);

  beforeEach(() => {
    vi.clearAllMocks();
    mockGetJobs.mockResolvedValue(MOCK_JOBS_RESPONSE);
    mockDeleteJob.mockResolvedValue({ status: 'success', data: MOCK_JOBS_RESPONSE.data[0] });
  });

  describe('Component Rendering', () => {
    it('renders the component successfully', async () => {
      render(<KBJobs />, { wrapper: TestWrapper });

      await waitFor(() => {
        expect(screen.getByText('All Jobs')).toBeInTheDocument();
        expect(screen.getByText('Generation Jobs')).toBeInTheDocument();
        expect(screen.getByText('Export Jobs')).toBeInTheDocument();
      });
    });

    it('renders jobs grid when jobs are available', async () => {
      render(<KBJobs />, { wrapper: TestWrapper });

      await waitFor(() => {
        const jobsGrids = screen.getAllByTestId('jobs-grid');
        expect(jobsGrids.length).toBeGreaterThan(0);
        expect(screen.getAllByTestId('job-item-job-1')).toHaveLength(3);
      });
    });

    it('renders empty state when no jobs are available', async () => {
      mockGetJobs.mockResolvedValue(EMPTY_JOBS_RESPONSE);

      render(<KBJobs />, { wrapper: TestWrapper });

      await waitFor(() => {
        const emptyStates = screen.getAllByTestId('jobs-empty');
        expect(emptyStates.length).toBeGreaterThan(0);
        expect(screen.queryAllByTestId('tabs-jobs-grid')).toHaveLength(0);
      });
    });

    it('renders loading overlay when loading', async () => {
      // Make the API call take longer to simulate loading
      mockGetJobs.mockImplementation(
        () => new Promise((resolve) => setTimeout(() => resolve(MOCK_JOBS_RESPONSE), 100))
      );

      const { container } = render(<KBJobs />, { wrapper: TestWrapper });

      // LoadingOverlay should be present initially
      expect(container.querySelector('.mantine-LoadingOverlay-root')).toBeInTheDocument();

      await waitFor(() => {
        const jobsGrids = screen.getAllByTestId('jobs-grid');
        expect(jobsGrids.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Tab Functionality', () => {
    it('initializes with ALL tab active', async () => {
      render(<KBJobs />, { wrapper: TestWrapper });

      await waitFor(() => {
        const allTab = screen.getByRole('tab', { name: /all jobs/i });
        expect(allTab).toHaveAttribute('aria-selected', 'true');
      });
    });

    it('calls fetchJobs on component mount', async () => {
      render(<KBJobs />, { wrapper: TestWrapper });

      await waitFor(() => {
        expect(mockGetJobs).toHaveBeenCalledWith({
          jobType: [JobType.ContentGeneration, JobType.ArticleExport],
          cursor: '',
          direction: 'backward',
        });
      });
    });

    it('switches tabs correctly', async () => {
      render(<KBJobs />, { wrapper: TestWrapper });

      await waitFor(() => {
        const jobsGrids = screen.getAllByTestId('jobs-grid');
        expect(jobsGrids.length).toBeGreaterThan(0);
      });

      const generationTab = screen.getByRole('tab', { name: /generation jobs/i });

      await act(async () => {
        fireEvent.click(generationTab);
      });

      await waitFor(() => {
        expect(generationTab).toHaveAttribute('aria-selected', 'true');
      });
    });
  });

  describe('Job Actions', () => {
    it('calls deleteJob when delete button is clicked', async () => {
      render(<KBJobs />, { wrapper: TestWrapper });

      await waitFor(() => {
        expect(screen.getAllByTestId('job-item-job-1')).toHaveLength(3);
      });

      const deleteButtons = screen.getAllByTestId('delete-btn-job-1');
      const deleteButton = deleteButtons[0];
      fireEvent.click(deleteButton);

      await waitFor(() => {
        expect(mockDeleteJob).toHaveBeenCalledWith('job-1');
      });
    });
  });

  describe('Performance and Memoization', () => {
    it('uses renderTabContent callback efficiently with jobs', async () => {
      render(<KBJobs />, { wrapper: TestWrapper });

      await waitFor(() => {
        const jobsGrids = screen.getAllByTestId('jobs-grid');
        expect(jobsGrids.length).toBeGreaterThan(0);
        expect(screen.queryAllByTestId('jobs-empty')).toHaveLength(0);
      });
    });

    it('uses renderTabContent callback efficiently with empty jobs', async () => {
      mockGetJobs.mockResolvedValue(EMPTY_JOBS_RESPONSE);

      render(<KBJobs />, { wrapper: TestWrapper });

      await waitFor(() => {
        const emptyStates = screen.getAllByTestId('jobs-empty');
        expect(emptyStates.length).toBeGreaterThan(0);
        expect(screen.queryAllByTestId('tabs-jobs-grid')).toHaveLength(0);
      });
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('handles empty jobs array gracefully', async () => {
      mockGetJobs.mockResolvedValue(EMPTY_JOBS_RESPONSE);

      expect(() => render(<KBJobs />, { wrapper: TestWrapper })).not.toThrow();

      await waitFor(() => {
        const emptyStates = screen.getAllByTestId('jobs-empty');
        expect(emptyStates.length).toBeGreaterThan(0);
      });
    });

    it('handles API errors gracefully', async () => {
      // Mock console.error to prevent error logs in test output
      const originalConsoleError = console.error;
      console.error = vi.fn();

      // Add a temporary unhandled rejection handler to prevent the warning
      const originalUnhandledRejection = process.listeners('unhandledRejection');
      process.removeAllListeners('unhandledRejection');
      process.on('unhandledRejection', () => {
        // Suppress the unhandled rejection for this test
      });

      mockGetJobs.mockRejectedValue(new Error('API Error'));

      expect(() => render(<KBJobs />, { wrapper: TestWrapper })).not.toThrow();

      // Wait for the component to handle the error and still render
      await waitFor(() => {
        // Component should still render, possibly with empty state
        expect(screen.getByText('All Jobs')).toBeInTheDocument();
      });

      // Restore original handlers
      process.removeAllListeners('unhandledRejection');
      originalUnhandledRejection.forEach((listener) => {
        process.on('unhandledRejection', listener);
      });
      console.error = originalConsoleError;
    });
  });

  describe('Tab Panel Content', () => {
    it('renders tab panels correctly', async () => {
      render(<KBJobs />, { wrapper: TestWrapper });

      await waitFor(() => {
        const tabPanels = screen.getAllByRole('tabpanel', { hidden: true });
        expect(tabPanels).toHaveLength(3);
      });
    });

    it('shows active tab panel content', async () => {
      render(<KBJobs />, { wrapper: TestWrapper });

      await waitFor(() => {
        const allJobsPanel = screen.getByRole('tabpanel', { name: /all jobs/i });
        expect(allJobsPanel).toBeInTheDocument();
        expect(allJobsPanel).not.toHaveAttribute('hidden');
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes for tabs', async () => {
      render(<KBJobs />, { wrapper: TestWrapper });

      await waitFor(() => {
        const tabList = screen.getByRole('tablist');
        expect(tabList).toBeInTheDocument();

        const tabs = screen.getAllByRole('tab');
        expect(tabs).toHaveLength(3);

        tabs.forEach((tab) => {
          expect(tab).toHaveAttribute('aria-selected');
        });
      });
    });

    it('has proper tab panels with correct associations', async () => {
      render(<KBJobs />, { wrapper: TestWrapper });

      await waitFor(() => {
        const tabPanels = screen.getAllByRole('tabpanel', { hidden: true });
        expect(tabPanels).toHaveLength(3);

        tabPanels.forEach((panel) => {
          expect(panel).toHaveAttribute('aria-labelledby');
        });
      });
    });
  });

  describe('Constants and Types', () => {
    it('uses correct JOB_TAB_VALUES constants', () => {
      expect(JOB_TAB_VALUES.ALL).toBe('all');
      expect(JOB_TAB_VALUES.GENERATION).toBe('generation');
      expect(JOB_TAB_VALUES.EXPORT).toBe('export');
    });

    it('handles JobTabValue type correctly', () => {
      const allValue: JobTabValue = JOB_TAB_VALUES.ALL;
      const generationValue: JobTabValue = JOB_TAB_VALUES.GENERATION;
      const exportValue: JobTabValue = JOB_TAB_VALUES.EXPORT;

      expect(allValue).toBe('all');
      expect(generationValue).toBe('generation');
      expect(exportValue).toBe('export');
    });
  });

  describe('Integration with Context', () => {
    it('passes correct props to JobsGrid', async () => {
      render(<KBJobs />, { wrapper: TestWrapper });

      await waitFor(() => {
        const jobsGrids = screen.getAllByTestId('jobs-grid');
        expect(jobsGrids).toHaveLength(3); // One in each tab panel
        expect(screen.getAllByTestId('job-item-job-1')).toHaveLength(3);
      });
    });

    it('handles job deletion through context', async () => {
      render(<KBJobs />, { wrapper: TestWrapper });

      await waitFor(() => {
        expect(screen.getAllByTestId('job-item-job-1')).toHaveLength(3);
      });

      const deleteButtons = screen.getAllByTestId('delete-btn-job-1');
      fireEvent.click(deleteButtons[0]); // Click the first one

      await waitFor(() => {
        expect(mockDeleteJob).toHaveBeenCalledWith('job-1');
      });
    });
  });

  describe('Loading States', () => {
    it('shows loading overlay when jobs are loading', async () => {
      // Make the API call take longer to simulate loading
      mockGetJobs.mockImplementation(
        () => new Promise((resolve) => setTimeout(() => resolve(MOCK_JOBS_RESPONSE), 100))
      );

      const { container } = render(<KBJobs />, { wrapper: TestWrapper });

      const loadingOverlay = container.querySelector('.mantine-LoadingOverlay-root');
      expect(loadingOverlay).toBeInTheDocument();

      await waitFor(() => {
        const jobsGrids = screen.getAllByTestId('jobs-grid');
        expect(jobsGrids.length).toBeGreaterThan(0);
      });
    });

    it('hides loading overlay when not loading', async () => {
      const { container } = render(<KBJobs />, { wrapper: TestWrapper });

      await waitFor(() => {
        const jobsGrids = screen.getAllByTestId('jobs-grid');
        expect(jobsGrids.length).toBeGreaterThan(0);
      });

      // Loading overlay should not be visible after loading
      const loadingOverlay = container.querySelector('.mantine-LoadingOverlay-root');
      // Check if overlay is present but not loading
      if (loadingOverlay) {
        expect(loadingOverlay).toBeInTheDocument();
      }
    });
  });
});
