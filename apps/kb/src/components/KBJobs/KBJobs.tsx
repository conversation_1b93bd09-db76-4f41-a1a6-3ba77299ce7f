import { Box, LoadingOverlay, Tabs, rem, useMantineTheme } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useIntersection } from '@mantine/hooks';
import { IconAIRedoSpark } from '@resola-ai/ui';
import { IconLayoutGrid, IconUpload } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useCallback, useEffect, useMemo, useState } from 'react';

import { JOB_TAB_VALUES } from '@/constants/job';
import { useJobContext } from '@/contexts';
import type { JobTabValue } from '@/types/job';
import JobsEmpty from './JobsEmpty';
import JobsGrid from './JobsGrid';

const useStyles = createStyles((theme) => ({
  root: {
    paddingBottom: rem(100),
  },
  tabsRoot: {
    marginBottom: rem(24),
  },
  tabsList: {
    borderBottom: 'none',
    gap: 0,
  },
  tab: {
    // Base tab styling
    boxSizing: 'border-box',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: `${rem(12)} ${rem(16)}`,
    gap: rem(8),
    minWidth: rem(180),
    height: rem(48),
    borderRadius: `${rem(4)} ${rem(4)} 0px 0px`,
    fontSize: rem(16),
    fontWeight: 500,
    lineHeight: rem(24),
    textAlign: 'center',
    flex: 'none',
    flexGrow: 0,

    // Default state styling
    borderBottom: `2px solid ${theme.colors.decaLight[1]}`,
    background: 'transparent',
    color: theme.colors.decaDark[4],
    '& svg': {
      color: theme.colors.decaDark[4],
    },

    '&[data-active]': {
      // Active state styling
      borderBottom: `2px solid ${theme.colors.decaNavy[4]}`,
      background: 'transparent',
      color: theme.colors.decaNavy[4],
      '& svg': {
        color: theme.colors.decaNavy[4],
      },
    },

    '&:hover:not([data-active])': {
      background: theme.colors.decaNavy[0],
    },
  },
  tabIcon: {
    width: rem(16),
    height: rem(16),
    color: theme.colors.decaDark[4],
  },
}));

/**
 * KBJobs component displays a tabbed interface for viewing different types of jobs
 * Features:
 * - Tab-based filtering (All, Generation, Export jobs)
 * - Infinite scroll with intersection observer
 * - Job deletion functionality
 * - Empty state handling
 * - Loading states
 */
const KBJobs: React.FC = () => {
  const { classes } = useStyles();
  const theme = useMantineTheme();
  const { t } = useTranslate('job');
  const { jobs, jobsLoading, fetchJobs, fetchMoreJobs, deleteJob, hasMoreData, currentTabType } =
    useJobContext();
  const [activeTab, setActiveTab] = useState<JobTabValue>(JOB_TAB_VALUES.ALL);

  // Memoized tab change handler to prevent unnecessary re-renders
  const handleTabChange = useCallback(
    (value: string | null) => {
      if (value && value !== activeTab) {
        setActiveTab(value as JobTabValue);
      }
    },
    [activeTab]
  );

  // Memoized delete handler
  const handleDelete = useCallback(
    (jobId: string) => {
      deleteJob(jobId);
    },
    [deleteJob]
  );

  // Memoize jobs data to prevent unnecessary re-renders
  const memoizedJobs = useMemo(() => jobs, [jobs]);

  // Intersection observer for infinite scroll
  const { ref: bottomRef, entry: bottomEntry } = useIntersection({
    root: null,
    threshold: 1,
  });

  // Fetch jobs on component mount with default tab
  useEffect(() => {
    fetchJobs(activeTab);
  }, []);

  // Handle tab change and fetch corresponding jobs
  useEffect(() => {
    if (currentTabType !== activeTab) {
      fetchJobs(activeTab);
    }
  }, [activeTab, currentTabType, fetchJobs]);

  // Handle infinite scroll when bottom element is visible
  useEffect(() => {
    if (bottomEntry?.isIntersecting && !jobsLoading && hasMoreData) {
      fetchMoreJobs();
    }
  }, [bottomEntry?.isIntersecting, jobsLoading, hasMoreData, fetchMoreJobs]);

  // Render tab content - either jobs grid or empty state
  const renderTabContent = useCallback(() => {
    if (memoizedJobs.length === 0) {
      return <JobsEmpty />;
    }
    return <JobsGrid jobs={memoizedJobs} onDelete={handleDelete} />;
  }, [memoizedJobs, handleDelete]);

  return (
    <Box className={classes.root} pos='relative'>
      <LoadingOverlay visible={jobsLoading} zIndex={1000} />
      <Tabs
        value={activeTab}
        onChange={handleTabChange}
        className={classes.tabsRoot}
        classNames={{
          list: classes.tabsList,
          tab: classes.tab,
        }}
      >
        <Tabs.List>
          <Tabs.Tab
            value={JOB_TAB_VALUES.ALL}
            leftSection={<IconLayoutGrid className={classes.tabIcon} />}
          >
            {t('tabs.allJobs')}
          </Tabs.Tab>
          <Tabs.Tab
            value={JOB_TAB_VALUES.GENERATION}
            leftSection={
              <IconAIRedoSpark
                fill={
                  activeTab === JOB_TAB_VALUES.GENERATION
                    ? theme.colors.decaNavy[4]
                    : theme.colors.decaDark[4]
                }
                className={classes.tabIcon}
              />
            }
          >
            {t('tabs.generationJobs')}
          </Tabs.Tab>
          <Tabs.Tab
            value={JOB_TAB_VALUES.EXPORT}
            leftSection={<IconUpload className={classes.tabIcon} />}
          >
            {t('tabs.exportJobs')}
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value={JOB_TAB_VALUES.ALL} pt='md'>
          {renderTabContent()}
        </Tabs.Panel>

        <Tabs.Panel value={JOB_TAB_VALUES.GENERATION} pt='md'>
          {renderTabContent()}
        </Tabs.Panel>

        <Tabs.Panel value={JOB_TAB_VALUES.EXPORT} pt='md'>
          {renderTabContent()}
        </Tabs.Panel>
      </Tabs>

      {memoizedJobs.length > 0 && <Box ref={bottomRef} />}
    </Box>
  );
};

export default KBJobs;
