import { NotFound } from '@/components/common/NotFound';
import { useArticleDetailContext } from '@/contexts';
import { useApi<PERSON>and<PERSON>, useArticleDetailStyles, useKbAccessControl } from '@/hooks';
import { type ArticlePayload, EntityType, type KBTemplateField, type PartOfArticle } from '@/types';
import { Divider, LoadingOverlay, rem } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback, useEffect, useState } from 'react';
import ArticleContentForm from '../ArticleContentForm';
import { ArticleContentView } from '../ArticleContentView';
import ArticleCustomData from '../ArticleCustomData';
import ArticleKeyPhrases from '../ArticleKeyPhrases';
import ArticleRelated from '../ArticleRelated';
import ArticleSideBar from '../ArticleSideBar';
import type { OnUpdatedParams } from '../ArticleViewerActions';

interface ArticleDetailProps {
  baseId: string;
  articleId: string;
  isFullView?: boolean;
  editMode: boolean;
  setEditMode: React.Dispatch<React.SetStateAction<boolean>>;
  onUpdated?: (params: OnUpdatedParams) => void;
  onEditToggle: () => void;
  onDeleted?: () => void;
}

const ArticleDetail: React.FC<ArticleDetailProps> = ({
  baseId,
  articleId,
  isFullView = false,
  onUpdated,
  editMode,
  setEditMode,
  onEditToggle,
  onDeleted,
}) => {
  const { t } = useTranslate(['article', 'common']);
  const { classes } = useArticleDetailStyles();
  const { permArticle } = useKbAccessControl();
  const { isDetailLoading, currentArticle, getArticleById, updateArticle, setIsDetailLoading } =
    useArticleDetailContext();

  const [customData, setCustomData] = useState<Partial<KBTemplateField>[]>([]);
  const { handleApiRequest } = useApiHandler();

  /**
   * Handle updating the article with new data
   * Manages success/failure notifications and edit mode toggling
   */
  const handleUpdateArticle = useCallback(
    async (updateData: ArticlePayload) => {
      if (!currentArticle) return;

      setIsDetailLoading(true);

      // If related articles are not provided, use the current related articles
      if (!updateData.relatedArticles && currentArticle.relatedArticles) {
        updateData.relatedArticles = currentArticle.relatedArticles.map(
          (relatedArticle) => relatedArticle.id
        );
      }

      handleApiRequest(
        updateArticle(currentArticle.id, {
          ...currentArticle,
          ...updateData,
        }),
        {
          fallbackMessage: t('articleCollection.save.failed.description'),
          fallbackTitle: t('articleCollection.save.failed.title'),
          successMessage: t('articleCollection.save.success.description'),
          successTitle: t('articleCollection.save.success.title'),
          successCallback: async () => {
            await getArticleById(currentArticle.id);

            setEditMode(false);
            onUpdated?.({
              showLoader: true,
            });
            setIsDetailLoading(false);
          },
          errorCallback: () => {
            setEditMode(false);
            setIsDetailLoading(false);
          },
        }
      );
    },
    [
      currentArticle,
      updateArticle,
      t,
      handleApiRequest,
      getArticleById,
      setEditMode,
      onUpdated,
      setIsDetailLoading,
    ]
  );

  /**
   * Handle Keywords Change
   */
  const handleKeywordsChange = useCallback(
    (keywords: string[]) => {
      handleUpdateArticle({ keywords });
    },
    [handleUpdateArticle]
  );

  /**
   * Handle adding related articles
   */
  const handleAddRelatedArticles = useCallback(
    (articleIds: string[]) => {
      handleUpdateArticle({ relatedArticles: articleIds });
    },
    [handleUpdateArticle]
  );

  /**
   * Handle content save from editor
   */
  const handleContentSave = useCallback(
    async (data: PartOfArticle, callback?: () => void) => {
      await handleUpdateArticle({
        title: data.title,
        content: data.content,
        contentRaw: data.contentRaw,
      });

      callback?.();
    },
    [handleUpdateArticle]
  );

  /**
   * Handle updating article custom data
   */
  const handleUpdateCustomData = useCallback((newCustomData: Partial<KBTemplateField>[]) => {
    setCustomData(newCustomData);
  }, []);

  /**
   * Handle submitting custom data
   */
  const handleSubmitCustomData = useCallback(
    (newCustomData: Partial<KBTemplateField>[] | null) => {
      handleUpdateArticle({ customData: newCustomData || customData });
    },
    [customData, handleUpdateArticle]
  );

  /**
   * Fetch article data on component mount or articleId change
   */
  const fetchArticle = useCallback(
    async (articleId: string) => {
      getArticleById(articleId);
      setEditMode(false);
    },
    [getArticleById, setEditMode]
  );

  useEffect(() => {
    if (articleId) {
      fetchArticle(articleId);
    }
  }, [articleId]);

  return (
    <>
      <LoadingOverlay
        data-testid='loading-overlay'
        style={{ zIndex: 1000 }}
        visible={isDetailLoading}
      />
      {currentArticle === null && (
        <NotFound className={classes.articleNotFound} entityType={EntityType.ARTICLE} />
      )}
      {currentArticle && (
        <>
          {editMode ? (
            <ArticleContentForm
              isShownActions={false}
              baseId={baseId}
              article={currentArticle}
              onCancel={onEditToggle}
              onSave={handleContentSave}
            />
          ) : (
            <ArticleContentView
              article={currentArticle}
              onEditToggle={onEditToggle}
              onUpdated={onUpdated}
              onDeleted={onDeleted}
            />
          )}

          <Divider mb={rem(24)} />
          <ArticleKeyPhrases
            disabled={!permArticle.canUpdate || currentArticle.isShortcut}
            article={currentArticle}
            onKeywordsChange={handleKeywordsChange}
          />
          <ArticleRelated
            disabled={!permArticle.canUpdate || currentArticle.isShortcut}
            article={currentArticle}
            onRelatedArticlesChange={handleAddRelatedArticles}
          />
          <ArticleCustomData
            disabled={!permArticle.canUpdate || currentArticle.isShortcut}
            article={currentArticle}
            callingSubmitWhenEnter={true}
            onCustomDataChanged={handleUpdateCustomData}
            onCustomDataSubmitted={handleSubmitCustomData}
          />

          <ArticleSideBar article={currentArticle} isFullView={isFullView} />
        </>
      )}
    </>
  );
};

export default ArticleDetail;
