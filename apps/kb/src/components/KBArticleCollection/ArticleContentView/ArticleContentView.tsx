import { useArticleDetailStyles } from '@/hooks/useArticleStyles';
import type { Article } from '@/types';
import { Box, Flex, Title } from '@mantine/core';
import { BlockNoteViewer, isBlockNoteMarkdownContent } from '@resola-ai/blocknote-editor';
import type React from 'react';
import { ArticleShortcutBadge } from '../ArticleShortcutBadge';
import { ArticleViewerActions, type OnUpdatedParams } from '../ArticleViewerActions';

interface ArticleContentViewProps {
  article: Article;
  onEditToggle: () => void;
  onUpdated?: (params: OnUpdatedParams) => void;
  onDeleted?: () => void;
  showActions?: boolean;
}

const ArticleContentView: React.FC<ArticleContentViewProps> = ({
  article,
  onEditToggle,
  onUpdated,
  onDeleted,
  showActions = false,
}) => {
  const { classes } = useArticleDetailStyles();

  return (
    <Box className={classes.articleWrapper}>
      <Box className={classes.articleHeader}>
        <Flex align='baseline' gap='lg'>
          <Title className={classes.articleTitle} order={2}>
            {article.title}
          </Title>
          {article.isShortcut && <ArticleShortcutBadge className={classes.articleBadge} />}
        </Flex>

        {showActions && (
          <ArticleViewerActions
            onEditToggle={onEditToggle}
            onUpdated={onUpdated}
            onDeleted={onDeleted}
          />
        )}
      </Box>
      <Box className={classes.articleContent}>
        <BlockNoteViewer
          isMarkdown={isBlockNoteMarkdownContent(article.contentRaw)}
          isUsingInlineCSS
          initialHTML={article.contentRaw}
          data-testid='block-note-viewer'
        />
      </Box>
    </Box>
  );
};

export default ArticleContentView;
