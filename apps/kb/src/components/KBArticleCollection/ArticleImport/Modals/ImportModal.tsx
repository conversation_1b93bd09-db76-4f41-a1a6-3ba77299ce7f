import ArticleImport from '@/components/KBArticleCollection/ArticleImport';
import { API_ERROR_CODES } from '@/constants/api';
import { useArticleContext } from '@/contexts/ArticleContext';
import { Modal, Text } from '@mantine/core';
import { HTTP_ERROR_STATUS } from '@resola-ai/shared-constants';
import { getPublicUrl } from '@resola-ai/utils';
import { useTranslate } from '@tolgee/react';
import { AxiosError } from 'axios';
import type React from 'react';
import { useCallback, useState } from 'react';

export interface ImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (count: number) => void;
}

export const ImportModal: React.FC<ImportModalProps> = ({ isOpen, onClose, onSuccess }) => {
  const { t } = useTranslate('article');
  const [isUploading, setIsUploading] = useState(false);
  const { state: articleCollectionState, services: articleCollectionServices } =
    useArticleContext();

  const handleUpload = useCallback(
    async (file: File, onError: (error: { message: string; expandData?: any }) => void) => {
      const kbId = articleCollectionState.currentKb?.id;
      if (!kbId) {
        onError({ message: 'uploadingError' });
        return;
      }

      setIsUploading(true);
      try {
        const response = await articleCollectionServices.importArticleFromCSV(
          articleCollectionState.currentKb?.id,
          file
        );

        onSuccess?.(response?.data?.rows);
      } catch (error) {
        let rows = [];
        let message = 'uploadingError';
        let expandData: any = {};

        if (error instanceof AxiosError) {
          // Handle CSV encoding detection failed error (status 400)
          if (
            error?.response?.status === HTTP_ERROR_STATUS.BAD_REQUEST &&
            error?.response?.data?.error?.code === API_ERROR_CODES.CSV_ENCODING_DETECTION_FAILED
          ) {
            message = 'csvEncodingDetectionFailed';
            expandData = { filename: file.name };
          }
          // Handle unprocessable entity errors (status 422)
          else if (
            error?.response?.status === HTTP_ERROR_STATUS.UNPROCESSABLE_ENTITY &&
            error?.response?.data?.detail?.length
          ) {
            rows = (error?.response?.data?.detail[0]?.rows || [])
              .map((row: number) => row + 1)
              .join(', ');
            message =
              error.response?.data?.detail[0]?.message ||
              error.response?.data?.detail[0] ||
              'uploadingError';
            expandData = { rows };
          }
        }

        onError({ message, expandData });
      } finally {
        setIsUploading(false);
      }
    },
    [onSuccess, articleCollectionState.currentKb?.id, articleCollectionServices]
  );

  return (
    <Modal
      opened={isOpen}
      onClose={onClose}
      size='xl'
      centered
      withCloseButton={!isUploading}
      closeOnClickOutside={!isUploading}
      title={
        !isUploading ? (
          <Text size='xl' fw={700}>
            {t('articleCollection.importArticle')}
          </Text>
        ) : null
      }
    >
      <ArticleImport
        onChange={onSuccess}
        template={{
          description: t('articleCollection.importArticleDescription'),
          url: `${getPublicUrl('templates/import_article.csv')}`,
        }}
        KBName={articleCollectionState.currentKb?.name}
        onUpload={handleUpload}
        onClose={onClose}
        isUploading={isUploading}
      />
    </Modal>
  );
};

export default ImportModal;
