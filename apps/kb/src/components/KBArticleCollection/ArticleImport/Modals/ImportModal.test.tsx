import { API_ERROR_CODES } from '@/constants/api';
import { useArticleContext } from '@/contexts/ArticleContext';
import { AllTheProviders } from '@/utils/unitTest';
import { HTTP_ERROR_STATUS } from '@resola-ai/shared-constants';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AxiosError } from 'axios';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import ImportModal from './ImportModal';

// Mock dependencies
vi.mock('@/contexts/ArticleContext');

// Create a mock function for testing error handling
let mockOnUpload: any;

vi.mock('@/components/KBArticleCollection/ArticleImport', () => ({
  default: ({ onUpload, onClose }: any) => {
    mockOnUpload = onUpload;
    return (
      <div data-testid='article-import'>
        <button
          type='button'
          data-testid='trigger-upload'
          onClick={() => {
            const mockFile = new File(['test'], 'test.csv', { type: 'text/csv' });
            const mockOnError = vi.fn();
            onUpload(mockFile, mockOnError);
          }}
        >
          Upload
        </button>
        <button type='button' data-testid='close-import' onClick={onClose}>
          Close
        </button>
      </div>
    );
  },
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string, params?: Record<string, any>) => {
      if (params) {
        let result = key;
        Object.entries(params).forEach(([k, v]) => {
          result = result.replace(`{${k}}`, String(v));
        });
        return result;
      }
      return key;
    },
  }),
}));

vi.mock('@resola-ai/utils', async (importOriginal) => {
  const actual = await importOriginal<typeof import('@resola-ai/utils')>();
  return {
    ...actual,
    getPublicUrl: (path: string) => `https://example.com/${path}`,
  };
});

// Test constants
const MOCK_KB = {
  id: 'kb-123',
  name: 'Test KB',
};

const MOCK_ARTICLE_CONTEXT = {
  state: {
    currentKb: MOCK_KB,
  },
  services: {
    importArticleFromCSV: vi.fn(),
  },
};

const DEFAULT_PROPS = {
  isOpen: true,
  onClose: vi.fn(),
  onSuccess: vi.fn(),
};

const renderComponent = (props = {}) => {
  return render(
    <AllTheProviders>
      <ImportModal {...DEFAULT_PROPS} {...props} />
    </AllTheProviders>
  );
};

describe('ImportModal', () => {
  beforeEach(() => {
    vi.resetAllMocks();
    (useArticleContext as any).mockReturnValue(MOCK_ARTICLE_CONTEXT);
  });

  it('should render the modal when isOpen is true', () => {
    renderComponent();

    expect(screen.getByText('articleCollection.importArticle')).toBeInTheDocument();
    expect(screen.getByTestId('article-import')).toBeInTheDocument();
  });

  it('should not render the modal when isOpen is false', () => {
    renderComponent({ isOpen: false });

    expect(screen.queryByText('articleCollection.importArticle')).not.toBeInTheDocument();
    expect(screen.queryByTestId('article-import')).not.toBeInTheDocument();
  });

  it('should call onClose when close button is clicked', async () => {
    const onClose = vi.fn();
    renderComponent({ onClose });

    await userEvent.click(screen.getByTestId('close-import'));

    expect(onClose).toHaveBeenCalled();
  });

  describe('handleUpload', () => {
    it('should handle successful upload', async () => {
      const onSuccess = vi.fn();
      const mockResponse = { data: { rows: 5 } };
      MOCK_ARTICLE_CONTEXT.services.importArticleFromCSV.mockResolvedValue(mockResponse);

      renderComponent({ onSuccess });

      await userEvent.click(screen.getByTestId('trigger-upload'));

      await waitFor(() => {
        expect(MOCK_ARTICLE_CONTEXT.services.importArticleFromCSV).toHaveBeenCalledWith(
          MOCK_KB.id,
          expect.any(File)
        );
        expect(onSuccess).toHaveBeenCalledWith(5);
      });
    });

    it('should handle CSV encoding detection failed error (status 400)', async () => {
      const mockError = new AxiosError();
      mockError.response = {
        status: HTTP_ERROR_STATUS.BAD_REQUEST,
        data: {
          error: {
            code: API_ERROR_CODES.CSV_ENCODING_DETECTION_FAILED,
            detail:
              "Unable to detect file encoding for 'test_file.csv'. Please ensure the file is in a supported encoding format.",
          },
        },
      } as any;

      MOCK_ARTICLE_CONTEXT.services.importArticleFromCSV.mockRejectedValue(mockError);

      renderComponent();

      // Directly test the handleUpload function
      const mockFile = new File(['test'], 'test_file.csv', { type: 'text/csv' });
      const mockOnError = vi.fn();

      await mockOnUpload(mockFile, mockOnError);

      await waitFor(() => {
        expect(mockOnError).toHaveBeenCalledWith({
          message: 'csvEncodingDetectionFailed',
          expandData: { filename: 'test_file.csv' },
        });
      });
    });

    it('should use file.name for CSV encoding detection failed error', async () => {
      const mockError = new AxiosError();
      mockError.response = {
        status: HTTP_ERROR_STATUS.BAD_REQUEST,
        data: {
          error: {
            code: API_ERROR_CODES.CSV_ENCODING_DETECTION_FAILED,
            detail:
              "Unable to detect file encoding for 'some_file.csv'. Please ensure the file is in a supported encoding format.",
          },
        },
      } as any;

      MOCK_ARTICLE_CONTEXT.services.importArticleFromCSV.mockRejectedValue(mockError);

      renderComponent();

      const mockFile = new File(['test'], 'actual_filename.csv', { type: 'text/csv' });
      const mockOnError = vi.fn();

      await mockOnUpload(mockFile, mockOnError);

      await waitFor(() => {
        expect(mockOnError).toHaveBeenCalledWith({
          message: 'csvEncodingDetectionFailed',
          expandData: { filename: 'actual_filename.csv' },
        });
      });
    });

    it('should handle unprocessable entity errors (status 422)', async () => {
      const mockError = new AxiosError();
      mockError.response = {
        status: HTTP_ERROR_STATUS.UNPROCESSABLE_ENTITY,
        data: {
          detail: [
            {
              rows: [1, 3, 5],
              message: 'Missing required data',
            },
          ],
        },
      } as any;

      MOCK_ARTICLE_CONTEXT.services.importArticleFromCSV.mockRejectedValue(mockError);

      renderComponent();

      const mockFile = new File(['test'], 'test.csv', { type: 'text/csv' });
      const mockOnError = vi.fn();

      await mockOnUpload(mockFile, mockOnError);

      await waitFor(() => {
        expect(mockOnError).toHaveBeenCalledWith({
          message: 'Missing required data',
          expandData: { rows: '2, 4, 6' }, // rows are incremented by 1
        });
      });
    });

    it('should handle generic errors', async () => {
      const mockError = new Error('Generic error');
      MOCK_ARTICLE_CONTEXT.services.importArticleFromCSV.mockRejectedValue(mockError);

      renderComponent();

      const mockFile = new File(['test'], 'test.csv', { type: 'text/csv' });
      const mockOnError = vi.fn();

      await mockOnUpload(mockFile, mockOnError);

      await waitFor(() => {
        expect(mockOnError).toHaveBeenCalledWith({
          message: 'uploadingError',
          expandData: {},
        });
      });
    });

    it('should handle error when no KB is selected', async () => {
      const mockContextWithoutKB = {
        ...MOCK_ARTICLE_CONTEXT,
        state: {
          currentKb: null,
        },
      };
      (useArticleContext as any).mockReturnValue(mockContextWithoutKB);

      renderComponent();

      const mockFile = new File(['test'], 'test.csv', { type: 'text/csv' });
      const mockOnError = vi.fn();

      await mockOnUpload(mockFile, mockOnError);

      expect(mockOnError).toHaveBeenCalledWith({
        message: 'uploadingError',
      });
    });
  });
});
