import { MantineWrapper } from '@/utils/unitTest';
import { modals } from '@mantine/modals';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { SuccessBox } from './SuccessBox';

// Mock dependencies
vi.mock('@mantine/modals', () => ({
  modals: {
    closeAll: vi.fn(),
  },
}));

vi.mock('@/components/Icons/IconImportSuccess', () => ({
  default: (props: any) => (
    <svg data-testid='icon-import-success' {...props}>
      <title>Import success icon</title>
    </svg>
  ),
}));

// Test constants
const MOCK_COUNT = 5;

const DEFAULT_PROPS = {
  count: MOCK_COUNT,
};

const renderComponent = (props = {}) => {
  return render(
    <MantineWrapper>
      <SuccessBox {...DEFAULT_PROPS} {...props} />
    </MantineWrapper>
  );
};

describe('SuccessBox', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  describe('Rendering', () => {
    it('should render the success icon', () => {
      renderComponent();

      expect(screen.getByTestId('icon-import-success')).toBeInTheDocument();
    });

    it('should render the success title', () => {
      renderComponent();

      expect(screen.getByText('import.importArticleSuccess')).toBeInTheDocument();
    });

    it('should render the success description with count', () => {
      renderComponent();

      expect(screen.getByText('import.importArticleSuccessDescription')).toBeInTheDocument();
    });

    it('should render the close button', () => {
      renderComponent();

      expect(screen.getByRole('button', { name: 'closeBtn' })).toBeInTheDocument();
    });
  });

  describe('Count Display', () => {
    it('should display the component with count value 1', () => {
      const { unmount } = renderComponent({ count: 1 });
      expect(screen.getByText('import.importArticleSuccessDescription')).toBeInTheDocument();
      unmount();
    });

    it('should display the component with count value 0', () => {
      const { unmount } = renderComponent({ count: 0 });
      expect(screen.getByText('import.importArticleSuccessDescription')).toBeInTheDocument();
      unmount();
    });

    it('should display the component with count value 999', () => {
      const { unmount } = renderComponent({ count: 999 });
      expect(screen.getByText('import.importArticleSuccessDescription')).toBeInTheDocument();
      unmount();
    });
  });

  describe('Close Button Interaction', () => {
    it('should call modals.closeAll when close button is clicked', async () => {
      const user = userEvent.setup();
      renderComponent();

      const closeButton = screen.getByRole('button', { name: 'closeBtn' });
      await user.click(closeButton);

      expect(modals.closeAll).toHaveBeenCalledTimes(1);
    });

    it('should not call window.location.reload when close button is clicked', async () => {
      const user = userEvent.setup();
      // Mock window.location.reload since it doesn't exist in JSDOM
      const mockReload = vi.fn();
      Object.defineProperty(window, 'location', {
        value: { ...window.location, reload: mockReload },
        writable: true,
      });

      renderComponent();

      const closeButton = screen.getByRole('button', { name: 'closeBtn' });
      await user.click(closeButton);

      expect(mockReload).not.toHaveBeenCalled();
    });

    it('should handle multiple clicks on close button', async () => {
      const user = userEvent.setup();
      renderComponent();

      const closeButton = screen.getByRole('button', { name: 'closeBtn' });

      await user.click(closeButton);
      await user.click(closeButton);
      await user.click(closeButton);

      expect(modals.closeAll).toHaveBeenCalledTimes(3);
    });
  });

  describe('Component Structure', () => {
    it('should have proper CSS classes applied', () => {
      const { container } = renderComponent();

      // Check that the main container has the expected structure
      const mainBox = container.querySelector('div');
      expect(mainBox).toBeInTheDocument();
      expect(mainBox).toHaveStyle({
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
      });
    });

    it('should render elements in correct order', () => {
      renderComponent();

      // Check individual elements exist
      expect(screen.getByText('import.importArticleSuccess')).toBeInTheDocument();
      expect(screen.getByText('import.importArticleSuccessDescription')).toBeInTheDocument();
      expect(screen.getByText('closeBtn')).toBeInTheDocument();

      // Verify the structural order by checking the DOM tree
      const container = screen.getByTestId('icon-import-success').parentElement;
      const children = Array.from(container!.children);
      expect(children[0]).toHaveAttribute('data-testid', 'icon-import-success');
      expect(children[1]).toHaveTextContent('import.importArticleSuccess');
      expect(children[2]).toHaveTextContent('import.importArticleSuccessDescription');
      expect(children[3]).toHaveTextContent('closeBtn');
    });

    it('should have proper semantic structure', () => {
      renderComponent();

      // Check that the close button is properly marked as a button
      const closeButton = screen.getByRole('button', { name: 'closeBtn' });
      expect(closeButton).toHaveAttribute('type', 'button');
    });
  });

  describe('Accessibility', () => {
    it('should have accessible button', () => {
      renderComponent();

      const closeButton = screen.getByRole('button', { name: 'closeBtn' });
      expect(closeButton).toBeInTheDocument();
      expect(closeButton).toHaveAccessibleName('closeBtn');
    });

    it('should have proper icon accessibility', () => {
      renderComponent();

      const icon = screen.getByTestId('icon-import-success');
      expect(icon).toBeInTheDocument();
    });
  });

  describe('Translation Integration', () => {
    it('should render translation keys correctly', () => {
      renderComponent();

      // Verify that translation keys are used (they will be translated by the real app)
      expect(screen.getByText('import.importArticleSuccess')).toBeInTheDocument();
      expect(screen.getByText('import.importArticleSuccessDescription')).toBeInTheDocument();
      expect(screen.getByText('closeBtn')).toBeInTheDocument();
    });
  });

  describe('Props Validation', () => {
    it('should handle negative count gracefully', () => {
      const { unmount } = renderComponent({ count: -1 });
      expect(screen.getByText('import.importArticleSuccessDescription')).toBeInTheDocument();
      unmount();
    });

    it('should handle undefined count gracefully', () => {
      const { unmount } = renderComponent({ count: undefined });
      expect(screen.getByText('import.importArticleSuccessDescription')).toBeInTheDocument();
      unmount();
    });

    it('should handle zero count gracefully', () => {
      const { unmount } = renderComponent({ count: 0 });
      expect(screen.getByText('import.importArticleSuccessDescription')).toBeInTheDocument();
      unmount();
    });
  });

  describe('Styling', () => {
    it('should apply correct text styling for title', () => {
      renderComponent();

      const title = screen.getByText('import.importArticleSuccess');
      expect(title).toHaveStyle({
        '--text-fz': 'var(--mantine-font-size-xl)',
        'font-weight': '700',
        color: 'var(--mantine-color-decaNavy-text)',
      });
    });

    it('should apply correct text styling for description', () => {
      renderComponent();

      const description = screen.getByText('import.importArticleSuccessDescription');
      expect(description).toHaveStyle({
        '--text-fz': 'var(--mantine-font-size-lg)',
        color: 'var(--mantine-color-dimmed)',
      });
    });

    it('should apply default variant to button', () => {
      renderComponent();

      const button = screen.getByRole('button', { name: 'closeBtn' });
      expect(button).toHaveAttribute('data-variant', 'default');
    });
  });

  describe('Click Handler Functionality', () => {
    it('should execute onClick handler correctly', async () => {
      const user = userEvent.setup();
      renderComponent();

      const closeButton = screen.getByRole('button', { name: 'closeBtn' });

      // Verify the button exists and is clickable
      expect(closeButton).toBeEnabled();

      // Click and verify modals.closeAll is called
      await user.click(closeButton);
      expect(modals.closeAll).toHaveBeenCalledTimes(1);
    });

    it('should verify button behavior without page reload', async () => {
      const user = userEvent.setup();

      // Spy on location reload to ensure it's not called
      const reloadSpy = vi.fn();
      Object.defineProperty(window, 'location', {
        value: { ...window.location, reload: reloadSpy },
        writable: true,
      });

      renderComponent();

      const closeButton = screen.getByRole('button', { name: 'closeBtn' });
      await user.click(closeButton);

      // Verify modals.closeAll was called but not window.location.reload
      expect(modals.closeAll).toHaveBeenCalledTimes(1);
      expect(reloadSpy).not.toHaveBeenCalled();
    });
  });

  describe('Component Props', () => {
    it('should accept and use count prop correctly', () => {
      const testCount = 42;
      renderComponent({ count: testCount });

      // The component should render with the translation key
      // In a real app, this would be translated to show the count
      expect(screen.getByText('import.importArticleSuccessDescription')).toBeInTheDocument();
    });

    it('should handle edge cases for count prop', () => {
      // Test with various edge case values
      const testCases = [0, -1, 1000000, null, undefined];

      testCases.forEach((count) => {
        const { unmount } = renderComponent({ count });
        expect(screen.getByText('import.importArticleSuccessDescription')).toBeInTheDocument();
        unmount();
      });
    });
  });
});
