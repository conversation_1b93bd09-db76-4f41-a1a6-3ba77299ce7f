import { useArticleMovingModal, useKBSelectionModal } from '@/components';
import { ArticlePublishDropdown, PublishStatus } from '@/components/KBArticlePublish';
import { ROOT_PATH } from '@/constants/folder';
import { useArticleDetailContext } from '@/contexts';
import { useApiHand<PERSON>, useArticleDetailStyles, useKbAccessControl } from '@/hooks';
import { type SchedulePayload, type ScheduleType, VoteType } from '@/types';
import { Flex } from '@mantine/core';
import { DecaButton } from '@resola-ai/ui';
import { IconEdit, IconEye } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { memo, useCallback, useMemo } from 'react';

import ArticleRate from '../ArticleRate';
import { ArticleThreeDotsMenu } from '../ArticleThreeDotsMenu';

// Types for reusable helper functions
interface ApiRequestConfig {
  fallbackMessage: string;
  fallbackTitle: string;
  successMessage: string;
  successTitle: string;
  successCallback?: () => Promise<void>;
}

export interface OnUpdatedParams {
  showLoader: boolean;
  shouldCloseViewer?: boolean;
}

interface ArticleViewerActionsProps {
  onEditToggle: () => void;
  onUpdated?: (params: OnUpdatedParams) => void;
  onDeleted?: () => void;
  onViewOriginal?: (articleId: string) => void;
}

// Helper functions to reduce code duplication
const getEffectiveArticleId = (article: any): string | null => {
  if (!article) return null;
  return article.isShortcut ? article.originArticleId : article.id;
};

const createOnUpdatedCallback = (shouldCloseViewer = false): OnUpdatedParams => ({
  showLoader: false,
  shouldCloseViewer,
});

const createSuccessCallback =
  (
    getArticleById: (id: string) => Promise<any>,
    currentArticleId: string,
    onUpdated: ((params: OnUpdatedParams) => void) | undefined
  ) =>
  async () => {
    await getArticleById(currentArticleId);
    onUpdated?.(createOnUpdatedCallback());
  };

const createApiRequestConfig = (
  t: (key: string, options?: { ns?: string }) => string,
  operation: string,
  namespace = 'article'
): ApiRequestConfig => ({
  fallbackMessage: t(`${operation}.failed.description`, { ns: namespace }),
  fallbackTitle: t(`${operation}.failed.title`, { ns: namespace }),
  successMessage: t(`${operation}.success.description`, { ns: namespace }),
  successTitle: t(`${operation}.success.title`, { ns: namespace }),
});

const ArticleViewerActions: React.FC<ArticleViewerActionsProps> = memo(
  ({ onEditToggle, onUpdated, onDeleted, onViewOriginal }) => {
    const { t } = useTranslate(['kb', 'article', 'common']);
    const { classes } = useArticleDetailStyles();
    const { permArticle, permVoting } = useKbAccessControl();
    const {
      currentArticle,
      articleVote,
      isArticleVoting,
      setIsArticleVoting,
      updateArticle,
      updateArticleVote,
      deleteArticle,
      createShortcut,
      getArticleById,
      duplicateArticle,
      schedulePublish,
      cancelSchedule,
    } = useArticleDetailContext();
    const { handleApiRequest, API_RESPONSE_STATUS } = useApiHandler();

    const { openArticleMovingModal } = useArticleMovingModal({
      afterMoving: () => {
        onUpdated?.({
          showLoader: false,
          shouldCloseViewer: true,
        });
      },
    });

    const { openKBSelectionModal, closeKBSelectionModal } = useKBSelectionModal({
      title: t('articleShortcut.creating.title', { ns: 'article' }),
      description: t('articleShortcut.creating.description', { ns: 'article' }),
    });

    const {
      openKBSelectionModal: openKBSelectionModalForDuplicate,
      closeKBSelectionModal: closeKBSelectionModalForDuplicate,
    } = useKBSelectionModal({
      title: t('articleDuplication.title', { ns: 'article' }),
      description: t('articleDuplication.description', { ns: 'article' }),
      saveButtonText: t('articleDuplication.duplicateButton', { ns: 'article' }),
    });

    const canClickVote = useMemo(() => {
      return !isArticleVoting && permVoting.canVote;
    }, [isArticleVoting, permVoting.canVote]);

    /**
     * Convert article status to PublishStatus enum
     * Memoized to prevent unnecessary re-calculations
     */
    const publishStatus = useMemo((): PublishStatus => {
      if (!currentArticle?.status) return PublishStatus.DRAFT;

      const statusMap: Record<string, PublishStatus> = {
        draft: PublishStatus.DRAFT,
        published: PublishStatus.PUBLISHED,
        schedule_publish: PublishStatus.SCHEDULE_PUBLISH,
        schedule_unpublish: PublishStatus.SCHEDULE_UNPUBLISH,
      };

      return statusMap[currentArticle.status] || PublishStatus.DRAFT;
    }, [currentArticle?.status]);

    /**
     * Handle publish status change
     * Optimized with better error handling and performance
     */
    const handlePublishStatusChange = useCallback(
      async (newStatus: PublishStatus) => {
        if (!currentArticle) return;

        try {
          const articleId = getEffectiveArticleId(currentArticle);

          if (!articleId) {
            console.warn('Article ID not found for publish status change');
            return;
          }

          // Create optimized article data for API
          const articleUpdateData = {
            ...currentArticle,
            // Convert related articles to IDs if they exist
            ...(currentArticle.relatedArticles && {
              relatedArticles: currentArticle.relatedArticles.map((article) => article.id),
            }),
            status: newStatus,
          };

          const apiConfig = createApiRequestConfig(t, 'articleCollection.save');
          await handleApiRequest(updateArticle(articleId, articleUpdateData), {
            ...apiConfig,
            successCallback: createSuccessCallback(getArticleById, currentArticle.id, onUpdated),
          });
        } catch (error) {
          console.error('Error updating article status:', error);
        }
      },
      [currentArticle, updateArticle, handleApiRequest, t, getArticleById, onUpdated]
    );

    /**
     * Handle schedule publish
     * Schedules an article to be published or changed to draft at a specific date and time
     * Optimized with better validation, error handling, and performance improvements
     */
    const handleSchedulePublish = useCallback(
      async (_status: PublishStatus, scheduleType: ScheduleType, dateTime: Date) => {
        if (!currentArticle) {
          console.warn('No current article available for scheduling');
          return;
        }

        try {
          const articleId = getEffectiveArticleId(currentArticle);

          if (!articleId) {
            console.warn('Article ID not found for scheduling');
            return;
          }

          // Validate dateTime - more comprehensive validation
          if (!dateTime || !(dateTime instanceof Date) || Number.isNaN(dateTime.getTime())) {
            console.error('Invalid date provided for scheduling');
            return;
          }

          // Additional validation: ensure date is in the future
          if (dateTime <= new Date()) {
            console.error('Scheduled date must be in the future');
            return;
          }

          const schedulePayload: SchedulePayload = {
            scheduleType,
            scheduledDatetime: dateTime.toISOString(),
          };

          const apiConfig = createApiRequestConfig(t, 'articleSchedule', 'schedules');
          await handleApiRequest(schedulePublish(articleId, schedulePayload), {
            ...apiConfig,
            successCallback: createSuccessCallback(getArticleById, currentArticle.id, onUpdated),
          });
        } catch (error) {
          console.error('Error scheduling article:', error);
        }
      },
      [currentArticle, schedulePublish, handleApiRequest, getArticleById, onUpdated, t]
    );

    /**
     * Handle cancel schedule
     * Cancels an existing schedule for the article
     */
    const handleCancelSchedule = useCallback(
      async (scheduleId: string) => {
        if (!currentArticle) {
          console.warn('No current article available for schedule cancellation');
          return;
        }

        try {
          const apiConfig = createApiRequestConfig(t, 'scheduleCancel', 'schedules');
          await handleApiRequest(cancelSchedule(scheduleId), {
            ...apiConfig,
            successCallback: createSuccessCallback(getArticleById, currentArticle.id, onUpdated),
          });
        } catch (error) {
          console.error('Error cancelling schedule:', error);
          throw error; // Re-throw to let the dropdown handle the error
        }
      },
      [currentArticle, cancelSchedule, handleApiRequest, t, getArticleById, onUpdated]
    );

    /**
     * Handle Update Vote for Article (Like/Dislike)
     * Toggles between like/dislike/neutral states based on current vote status
     * Optimized with better error handling and state management
     */
    const handleVoteClick = useCallback(
      async (type: VoteType) => {
        if (!currentArticle) {
          console.warn('No current article available for voting');
          return;
        }

        if (isArticleVoting) {
          return;
        }

        try {
          setIsArticleVoting(true);
          const voteType = articleVote?.feedbackType === type ? VoteType.NEUTRAL : type;

          const apiConfig = createApiRequestConfig(t, 'articleCollection.vote');
          const response = await handleApiRequest(
            updateArticleVote(currentArticle.id, voteType),
            apiConfig
          );

          if (response?.status === API_RESPONSE_STATUS.SUCCESS) {
            onUpdated?.(createOnUpdatedCallback());
          }
        } catch (error) {
          console.error('Error updating article vote:', error);
        } finally {
          setIsArticleVoting(false);
        }
      },
      [
        currentArticle,
        articleVote?.feedbackType,
        isArticleVoting,
        updateArticleVote,
        handleApiRequest,
        t,
        onUpdated,
        setIsArticleVoting,
        API_RESPONSE_STATUS.SUCCESS,
      ]
    );

    /**
     * Handle Move Article
     * Opens modal for moving article to another knowledge base
     */
    const handleMoveArticle = useCallback(() => {
      if (!currentArticle) return;
      const parentDirId = currentArticle.base?.parentDirId ?? ROOT_PATH;
      openArticleMovingModal(currentArticle, parentDirId);
    }, [currentArticle, openArticleMovingModal]);

    /**
     * Handle Delete Article
     * Opens confirmation modal before deleting the article
     */
    const handleDeleteArticle = useCallback(() => {
      if (!currentArticle) return;

      deleteArticle(currentArticle, () => {
        onDeleted?.();
      });
    }, [currentArticle, deleteArticle, onDeleted]);

    /**
     * Handle creating a shortcut
     * Creates a reference to this article in another knowledge base
     */
    const createShortcutHandler = useCallback(
      async (selectedBaseId: string) => {
        if (!currentArticle) return;

        const apiConfig = createApiRequestConfig(t, 'articleShortcut.creating');
        const response = await handleApiRequest(
          createShortcut(currentArticle.id, selectedBaseId),
          apiConfig
        );

        if (response?.status === API_RESPONSE_STATUS.SUCCESS) {
          onUpdated?.(createOnUpdatedCallback());
          closeKBSelectionModal();
        }
      },
      [
        currentArticle,
        createShortcut,
        closeKBSelectionModal,
        handleApiRequest,
        t,
        onUpdated,
        API_RESPONSE_STATUS.SUCCESS,
      ]
    );

    /**
     * Handle Duplicate Article
     * Duplicates the article in another knowledge base
     */
    const duplicateArticleHandler = useCallback(
      async (selectedBaseId: string) => {
        if (!currentArticle) return;

        const apiConfig = createApiRequestConfig(t, 'articleDuplication');
        const response = await handleApiRequest(
          duplicateArticle(currentArticle.id, selectedBaseId),
          apiConfig
        );

        if (response?.status === API_RESPONSE_STATUS.SUCCESS) {
          onUpdated?.(createOnUpdatedCallback());
          closeKBSelectionModalForDuplicate();
        }
      },
      [
        currentArticle,
        duplicateArticle,
        closeKBSelectionModalForDuplicate,
        handleApiRequest,
        t,
        onUpdated,
        API_RESPONSE_STATUS.SUCCESS,
      ]
    );

    /**
     * Handle Create Shortcut
     * Opens knowledge base selection modal for shortcut creation
     */
    const handleCreateShortcut = useCallback(() => {
      if (!permArticle.canShortcut || !currentArticle || currentArticle.isShortcut) return;

      openKBSelectionModal({
        parentFolderId: currentArticle.base?.parentDirId ?? ROOT_PATH,
        currentBaseId: currentArticle.baseId,
        onSelect: async (selectedBaseId) => {
          await createShortcutHandler(selectedBaseId);
        },
      });
    }, [currentArticle, createShortcutHandler, permArticle.canShortcut]);

    /**
     * Handle Duplicate Article
     * Opens knowledge base selection modal for article duplication
     */
    const handleDuplicateArticle = useCallback(() => {
      if (!permArticle.canDuplicate || !currentArticle || currentArticle.isShortcut) return;
      openKBSelectionModalForDuplicate({
        parentFolderId: currentArticle.base?.parentDirId ?? ROOT_PATH,
        currentBaseId: currentArticle.baseId,
        onSelect: async (selectedBaseId) => {
          await duplicateArticleHandler(selectedBaseId);
        },
      });
    }, [currentArticle, permArticle.canDuplicate, duplicateArticleHandler]);

    /**
     * Handle View Original Article
     * Navigates to the original article when viewing a shortcut
     */
    const handleViewOriginal = useCallback(() => {
      if (!currentArticle?.originArticleId) return;

      onViewOriginal?.(currentArticle.originArticleId);
    }, [currentArticle, onViewOriginal]);

    // If no article is loaded yet, don't render anything
    if (!currentArticle) return null;

    return (
      <Flex align='center' gap='lg' data-testid='article-viewer-actions'>
        {/* Voting buttons (like/dislike) */}
        {permVoting.canView && (
          <>
            <DecaButton
              data-testid='like-button'
              disabled={!canClickVote}
              radius='xl'
              variant='action'
              onClick={() => canClickVote && handleVoteClick(VoteType.LIKE)}
            >
              <ArticleRate
                type={VoteType.LIKE}
                count={articleVote?.likeCount}
                fill={articleVote?.feedbackType === VoteType.LIKE}
              />
            </DecaButton>
            <DecaButton
              data-testid='dislike-button'
              disabled={!canClickVote}
              radius='xl'
              variant='action'
              onClick={() => canClickVote && handleVoteClick(VoteType.DISLIKE)}
            >
              <ArticleRate
                type={VoteType.DISLIKE}
                count={articleVote?.dislikeCount}
                fill={articleVote?.feedbackType === VoteType.DISLIKE}
              />
            </DecaButton>
          </>
        )}

        {/* Publish status dropdown - only for regular articles and if user can update */}
        {permArticle.canUpdate && (
          <ArticlePublishDropdown
            data-testid='article-publish-dropdown'
            status={publishStatus}
            schedule={currentArticle.schedule}
            onStatusChange={handlePublishStatusChange}
            onSchedule={handleSchedulePublish}
            onCancelSchedule={handleCancelSchedule}
            disabled={currentArticle.isShortcut}
          />
        )}

        {/* Edit button for regular articles */}
        {!currentArticle.isShortcut && permArticle.canUpdate && (
          <DecaButton
            data-testid='edit-button'
            className={classes.articleEditButton}
            radius='xl'
            variant='neutral'
            leftSection={<IconEdit size={20} />}
            onClick={onEditToggle}
          >
            {t('edit', { ns: 'kb' })}
          </DecaButton>
        )}

        {/* View original button for shortcut articles */}
        {currentArticle.isShortcut && (
          <DecaButton
            data-testid='view-original-button'
            className={classes.articleEditButton}
            radius='xl'
            variant='neutral'
            leftSection={<IconEye size={20} />}
            onClick={handleViewOriginal}
          >
            {t('articleShortcut.viewOriginal', { ns: 'article' })}
          </DecaButton>
        )}

        {/* More actions menu (move, delete, create shortcut) */}
        <ArticleThreeDotsMenu
          data-testid='article-three-dots-menu'
          onMove={permArticle.canMove ? handleMoveArticle : undefined}
          onDelete={permArticle.canDelete ? handleDeleteArticle : undefined}
          onCreateShortcut={
            permArticle.canShortcut && !currentArticle.isShortcut ? handleCreateShortcut : undefined
          }
          onDuplicate={
            permArticle.canDuplicate && !currentArticle.isShortcut
              ? handleDuplicateArticle
              : undefined
          }
        />
      </Flex>
    );
  }
);

ArticleViewerActions.displayName = 'ArticleViewerActions';

export default ArticleViewerActions;
