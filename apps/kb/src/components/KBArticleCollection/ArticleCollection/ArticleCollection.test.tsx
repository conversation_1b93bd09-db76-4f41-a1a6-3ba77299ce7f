import type { Article, KnowledgeBase } from '@/types';
import { KB_TYPE } from '@/types';
import { AllTheProviders } from '@/utils/unitTest';
import { fireEvent, render, screen } from '@testing-library/react';
import type React from 'react';
import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest';
import ArticleCollection from './index';

import { useKBSortLocalStorage } from '@/hooks';
// Import the hook to mock it properly
import { useFeatureFlags } from '@/hooks/useFeatureFlags';

// Test constants
const MOCK_KB: KnowledgeBase = {
  id: 'kb-123',
  name: 'Test Knowledge Base',
  description: 'Test description',
  baseType: KB_TYPE.article,
  type: KB_TYPE.article,
  orgId: 'org-123',
  createdAt: new Date('2023-01-01'),
  updatedAt: new Date('2023-01-01'),
  createdBy: {
    id: 'user-123',
    orgId: 'org-123',
    createdAt: '2023-01-01',
    updatedAt: '2023-01-01',
    displayName: 'Test User',
    email: '<EMAIL>',
    familyName: 'User',
    givenName: 'Test',
    picture: '',
  },
  deletedAt: null,
  parentDirId: 'parent-123',
  templateId: 'template-123',
  isDeleted: false,
  isPublic: false,
  language: 'en',
  logoUrl: '',
  keywords: [],
  customData: [],
  relatedBases: [],
  articles: [],
  articlesCount: 0,
  publishedArticlesCount: 0,
  totalFeedbackCount: 0,
  totalViewCount: 0,
  aiConfig: null,
  logoFileId: null,
  isCollaborative: false,
  isInternalToOrg: false,
  isSearchable: true,
  isAnalyticsEnabled: false,
  aiSearchConfig: null,
} as KnowledgeBase;

const MOCK_ARTICLE: Article = {
  id: 'article-123',
  baseId: 'kb-123',
  title: 'Test Article',
  content: 'Test content',
  contentRaw: 'Test content',
  status: 'published',
  keywords: ['test'],
  relatedArticles: [],
  customData: [],
  createdAt: new Date('2023-01-01'),
  updatedAt: new Date('2023-01-02'),
  createdBy: {
    id: 'user-123',
    orgId: 'org-123',
    createdAt: '2023-01-01',
    updatedAt: '2023-01-01',
    displayName: 'Test User',
    email: '<EMAIL>',
    familyName: 'User',
    givenName: 'Test',
    picture: '',
  },
  isShortcut: false,
};

const MOCK_DRAFT_ARTICLE: Article = {
  ...MOCK_ARTICLE,
  id: 'article-456',
  title: 'Draft Article',
  status: 'draft',
};

const MOCK_ARTICLES: Article[] = [MOCK_ARTICLE, MOCK_DRAFT_ARTICLE];

const MOCK_PAGINATION = {
  hasNext: true,
  hasPrevious: false,
  nextCursor: 'next-cursor',
  previousCursor: '',
  totalCount: 2,
};

// Mock dependencies
vi.mock('@/components/KBArticleGenerator', () => ({
  GeneratorButton: ({ baseId }: { baseId: string }) => (
    <button type='button' data-testid='generator-button' data-base-id={baseId}>
      Generate Article
    </button>
  ),
}));

vi.mock('@/components/KBExport/components/ExportJobByEntityButton', () => ({
  default: ({ entityId, onSuccess }: { entityId: string; onSuccess: () => void }) => (
    <button
      type='button'
      data-testid='export-button'
      data-entity-id={entityId}
      onClick={() => onSuccess()}
    >
      Export
    </button>
  ),
}));

// Mock for article moving modal
const mockOpenArticleMovingModal = vi.fn();
let mockAfterMovingCallback: (() => void) | undefined;

vi.mock('@/components/KBMoving', () => ({
  useArticleMovingModal: (options: { afterMoving?: () => void }) => {
    mockAfterMovingCallback = options?.afterMoving;
    return {
      openArticleMovingModal: mockOpenArticleMovingModal,
    };
  },
}));

vi.mock('@/components/KBSort', () => ({
  KBSort: ({
    sortOption,
    onSortChange,
    onReset,
  }: { sortOption: any; onSortChange: (option: any) => void; onReset?: () => void }) => (
    <div data-testid='kb-sort'>
      <button
        type='button'
        data-testid='sort-button'
        data-sort-field={sortOption.field}
        data-sort-order={sortOption.order}
        onClick={() => onSortChange({ field: 'title', order: 'ascending' })}
      >
        Sort
      </button>
      {onReset && (
        <button type='button' data-testid='reset-button' onClick={() => onReset()}>
          Reset
        </button>
      )}
    </div>
  ),
}));

vi.mock('@/components/common', () => ({
  CustomPagination: ({ onChange, onPageSizeChange, pageSize }: any) => (
    <div data-testid='pagination'>
      <button
        type='button'
        data-testid='pagination-next'
        onClick={() => onChange('forward', 'next-cursor')}
      >
        Next
      </button>
      <button
        type='button'
        data-testid='pagination-prev'
        onClick={() => onChange('backward', 'prev-cursor')}
      >
        Previous
      </button>
      <select
        data-testid='page-size-select'
        value={pageSize}
        onChange={(e) => onPageSizeChange(Number(e.target.value))}
      >
        <option value={10}>10</option>
        <option value={20}>20</option>
        <option value={50}>50</option>
      </select>
    </div>
  ),
  GridTable: ({ columns, rows }: any) => (
    <div data-testid='grid-table'>
      <table>
        <thead>
          <tr>
            {columns.map((col: any) => (
              <th key={col.key}>{col.title}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {rows.map((row: any) => (
            <tr
              key={row.data.id}
              data-testid={`article-row-${row.data.id}`}
              style={row.props.style}
              data-activated={row.props.activated}
            >
              {columns.map((col: any, colIndex: number) => (
                <td key={col.key}>
                  {colIndex === 0 ? (
                    <button
                      type='button'
                      onClick={row.props.onClick}
                      style={{
                        border: 'none',
                        background: 'transparent',
                        padding: 0,
                        margin: 0,
                        font: 'inherit',
                        color: 'inherit',
                        cursor: 'pointer',
                        textAlign: 'left',
                        width: '100%',
                      }}
                    >
                      {row.data[col.key]}
                    </button>
                  ) : (
                    row.data[col.key]
                  )}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  ),
  SearchBox: ({ onSearch }: any) => (
    <input
      data-testid='search-box'
      placeholder='search'
      onChange={(e) => onSearch(e.target.value)}
    />
  ),
}));

vi.mock('@/contexts', () => ({
  useArticleContext: () => ({
    articleCollection: {
      data: mockArticleContextData.articles,
      pagination: mockArticleContextData.pagination,
    },
    state: {
      isLoadingArticles: mockArticleContextData.isLoadingArticles,
      capturedPayload: mockArticleContextData.capturedPayload,
      activeArticleId: mockArticleContextData.activeArticleId,
      pageLimit: mockArticleContextData.pageLimit,
      searchKeyword: mockArticleContextData.searchKeyword,
    },
    actions: {
      setActiveArticleId: mockArticleContextData.setActiveArticleId,
      setCurrentKb: mockArticleContextData.setCurrentKb,
      setSearchKeyword: mockArticleContextData.setSearchKeyword,
      setPageLimit: mockArticleContextData.setPageLimit,
    },
    services: {
      getCollection: mockArticleContextData.getCollection,
      getArticles: mockArticleContextData.getArticles,
      createArticle: mockArticleContextData.createArticle,
      deleteArticle: mockArticleContextData.deleteArticle,
    },
  }),
  UploaderContextProvider: ({ children }: { children: React.ReactNode }) => children,
}));

vi.mock('@/hooks', () => ({
  useKbAccessControl: () => ({
    permArticle: {
      canGenerate: true,
      canCreate: true,
      canUpdate: true,
      canDelete: true,
      canMove: true,
    },
  }),
  useKBSortLocalStorage: vi.fn(() => ({
    sortOption: { field: 'createdDate', order: 'descending' },
    updateSortOption: vi.fn(),
    clearSortOption: vi.fn(),
    isLoaded: true,
  })),
}));

vi.mock('@/hooks/useFeatureFlags');

// Mock useFeatureFlags hook (already exported from @/hooks)
// Note: useKbAccessControl is already mocked above

vi.mock('../ArticleActions', () => ({
  default: ({ onOpen, onDelete, onMove }: any) => (
    <div data-testid='article-actions'>
      <button type='button' data-testid='action-open' onClick={onOpen}>
        Open
      </button>
      <button type='button' data-testid='action-delete' onClick={onDelete}>
        Delete
      </button>
      <button type='button' data-testid='action-move' onClick={onMove}>
        Move
      </button>
    </div>
  ),
}));

vi.mock('../ArticleCreateNew', () => ({
  default: ({ onImport }: { onImport: () => void }) => (
    <button type='button' data-testid='create-new-button' onClick={() => onImport()}>
      Create New
    </button>
  ),
}));

vi.mock('../ArticleEmpty', () => ({
  default: ({ onCreate, onImport }: { onCreate: () => void; onImport: () => void }) => (
    <div data-testid='article-empty'>
      <button type='button' data-testid='empty-create' onClick={() => onCreate()}>
        Create Article
      </button>
      <button type='button' data-testid='empty-import' onClick={() => onImport()}>
        Import Articles
      </button>
    </div>
  ),
}));

vi.mock('../ArticleFeedback', () => ({
  ArticleFeedback: ({ article }: { article: Article }) => (
    <div data-testid='article-feedback' data-article-id={article.id}>
      Feedback
    </div>
  ),
}));

vi.mock('../ArticleName', () => ({
  ArticleName: ({ article }: { article: Article }) => (
    <div data-testid='article-name' data-article-id={article.id}>
      {article.title}
    </div>
  ),
}));

vi.mock('../ArticleStatus', () => ({
  default: ({ status }: { status: string }) => (
    <div data-testid='article-status' data-status={status}>
      {status}
    </div>
  ),
}));

vi.mock('../ArticleViewer', () => ({
  default: ({ backTitle, onUpdated, onClosed }: any) => (
    <div data-testid='article-viewer'>
      <button type='button' data-testid='viewer-back' onClick={onClosed}>
        {backTitle}
      </button>
      <button
        type='button'
        data-testid='viewer-update'
        onClick={() => onUpdated({ showLoader: true, isOpenOtherArticle: false })}
      >
        Update
      </button>
      <button
        type='button'
        data-testid='viewer-update-no-loader'
        onClick={() => onUpdated({ showLoader: false, isOpenOtherArticle: false })}
      >
        Update No Loader
      </button>
    </div>
  ),
}));

vi.mock('@/utils/article', () => ({
  cleanBadCharacters: (str: string) => str,
}));

vi.mock('@resola-ai/ui/utils/dateTime', () => ({
  formatDateTime: (date: Date) => date.toISOString(),
}));

// Mock functions for KBSortLocalStorage
const mockUpdateSortOption = vi.fn();
const mockClearSortOption = vi.fn();

// Mock sort state that can be updated during tests
const mockSortState = { field: 'createdDate', order: 'descending' };

// Mock context data
const mockArticleContextData = {
  articles: MOCK_ARTICLES,
  pagination: MOCK_PAGINATION,
  isLoadingArticles: false,
  capturedPayload: { direction: 'forward' as const, cursor: '' },
  activeArticleId: '',
  pageLimit: 10,
  searchKeyword: '',
  setActiveArticleId: vi.fn(),
  setCurrentKb: vi.fn(),
  setSearchKeyword: vi.fn(),
  setPageLimit: vi.fn(),
  getCollection: vi.fn(),
  getArticles: vi.fn(),
  createArticle: vi.fn(),
  deleteArticle: vi.fn(),
};

// Mock additional functions
// const mockOpenArticleViewer = vi.fn();
// const mockHandleCreateArticle = vi.fn();

describe('ArticleCollection', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.resetModules();

    // Clear sort-related mocks
    mockUpdateSortOption.mockClear();
    mockClearSortOption.mockClear();

    // Mock timers for setTimeout functionality
    vi.useFakeTimers();

    // Mock useFeatureFlags with default values (all features enabled)
    vi.mocked(useFeatureFlags).mockReturnValue({
      isKBSortingEnabled: true,
      isSchedulePublishEnabled: true,
      isScheduleUnpublishEnabled: true,
      isScheduleModalEnabled: false,
      isAnyScheduleFeatureEnabled: () => true,
      getAllFlags: () => ({
        KB_SORTING_ARTICLE: true,
        KB_SCHEDULE_PUBLISH: true,
        KB_SCHEDULE_UNPUBLISH: true,
        KB_SCHEDULE_PUBLISH_UNPUBLISH_MODAL: false,
      }),
    });

    // Reset mock data to default state
    mockArticleContextData.articles = MOCK_ARTICLES;
    mockArticleContextData.pagination = MOCK_PAGINATION;
    mockArticleContextData.isLoadingArticles = false;
    mockArticleContextData.activeArticleId = '';
    mockArticleContextData.searchKeyword = '';
    mockArticleContextData.pageLimit = 10;

    // Reset sort state to default
    mockSortState.field = 'createdDate';
    mockSortState.order = 'descending';
  });

  afterEach(() => {
    vi.useRealTimers();
    mockAfterMovingCallback = undefined;
    mockOpenArticleMovingModal.mockClear();
  });

  describe('Initial Rendering', () => {
    test('renders toolbar with search, sort, and action buttons', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.getByTestId('search-box')).toBeInTheDocument();
      expect(screen.getByTestId('kb-sort')).toBeInTheDocument();
      expect(screen.getByTestId('sort-button')).toBeInTheDocument();
      expect(screen.getByTestId('export-button')).toBeInTheDocument();
      expect(screen.getByTestId('generator-button')).toBeInTheDocument();
      expect(screen.getByTestId('create-new-button')).toBeInTheDocument();
    });

    test('renders search and sort components in proper layout', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Both search and sort components should be present
      const searchBox = screen.getByTestId('search-box');
      const sortComponent = screen.getByTestId('kb-sort');

      expect(searchBox).toBeInTheDocument();
      expect(sortComponent).toBeInTheDocument();
    });

    test('renders article table with correct columns', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.getByTestId('grid-table')).toBeInTheDocument();
      expect(screen.getByText('articleCollection.name')).toBeInTheDocument();
      expect(screen.queryByText('articleCollection.status')).not.toBeInTheDocument();
      expect(screen.getByText('articleCollection.lastUpdated')).toBeInTheDocument();
      expect(screen.getByText('articleCollection.createdAt')).toBeInTheDocument();
    });

    test('renders article rows with correct data', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Check first article
      expect(screen.getByTestId('article-row-article-123')).toBeInTheDocument();
      expect(screen.getAllByTestId('article-name')).toHaveLength(2);
      expect(screen.getAllByTestId('article-status')).toHaveLength(2);
      expect(screen.getAllByTestId('article-feedback')).toHaveLength(2);
      expect(screen.getAllByTestId('article-actions')).toHaveLength(2);

      // Check second article
      expect(screen.getByTestId('article-row-article-456')).toBeInTheDocument();
    });

    test('renders pagination controls', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.getByTestId('pagination')).toBeInTheDocument();
      expect(screen.getByTestId('pagination-next')).toBeInTheDocument();
      expect(screen.getByTestId('pagination-prev')).toBeInTheDocument();
      expect(screen.getByTestId('page-size-select')).toBeInTheDocument();
    });
  });

  describe('Loading States', () => {
    test('shows loading overlay when loading articles', () => {
      mockArticleContextData.isLoadingArticles = true;

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Check that loading overlay is visible
      expect(document.querySelector('.mantine-LoadingOverlay-root')).toBeInTheDocument();
    });

    test('hides loading overlay when not loading', () => {
      mockArticleContextData.isLoadingArticles = false;

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Check that loading overlay is not visible (should be hidden)
      const loadingOverlay = document.querySelector('.mantine-LoadingOverlay-root');
      // When not loading, the overlay should be hidden or not rendered
      expect(loadingOverlay).not.toBeInTheDocument();
    });
  });

  describe('Empty State', () => {
    test('shows empty state when no articles', () => {
      mockArticleContextData.articles = [];

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.getByTestId('article-empty')).toBeInTheDocument();
      expect(screen.getByTestId('empty-create')).toBeInTheDocument();
      expect(screen.getByTestId('empty-import')).toBeInTheDocument();
    });

    test('hides grid table when no articles', () => {
      mockArticleContextData.articles = [];

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.queryByTestId('grid-table')).not.toBeInTheDocument();
    });

    test('hides pagination when no articles', () => {
      mockArticleContextData.articles = [];

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.queryByTestId('pagination')).not.toBeInTheDocument();
    });
  });

  describe('Sort Functionality', () => {
    test('renders sort component with default sort option', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const sortButton = screen.getByTestId('sort-button');
      expect(sortButton).toBeInTheDocument();
      expect(sortButton).toHaveAttribute('data-sort-field', 'createdDate');
      expect(sortButton).toHaveAttribute('data-sort-order', 'descending');
    });

    test('handles sort change interaction', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const sortButton = screen.getByTestId('sort-button');
      fireEvent.click(sortButton);

      // Verify that getArticles is called when sort changes
      expect(mockArticleContextData.getArticles).toHaveBeenCalled();
    });

    test('calls getArticles with correct parameters on sort change', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Clear the initial mount call
      vi.clearAllMocks();

      const sortButton = screen.getByTestId('sort-button');
      fireEvent.click(sortButton);

      // Should call getArticles to refetch with new sort
      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          take: 10,
          direction: 'forward',
          cursor: '',
          query: '',
          sortBy: 'title',
          sortOrder: 'asc',
          withBase: false,
          withUserInfo: false,
          withSchedule: true,
          baseId: 'kb-123',
        }),
        false
      );
    });

    test('handles sort reset functionality', () => {
      // Set up mock with non-default sort state so reset button is visible
      vi.mocked(useKBSortLocalStorage).mockReturnValue({
        sortOption: { field: 'title', order: 'ascending' },
        updateSortOption: mockUpdateSortOption,
        clearSortOption: mockClearSortOption,
        isLoaded: true,
      });

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Clear the initial mount call
      vi.clearAllMocks();

      const resetButton = screen.getByTestId('reset-button');
      fireEvent.click(resetButton);

      // Should call clearSortOption
      expect(mockClearSortOption).toHaveBeenCalled();

      // Should call getArticles to refetch with default sort
      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          take: 10,
          direction: 'forward',
          cursor: '',
          query: '',
          sortBy: 'createdAt',
          sortOrder: 'desc',
          withBase: false,
          withUserInfo: false,
          withSchedule: true,
          baseId: 'kb-123',
        }),
        false
      );
    });
  });

  describe('User Interactions', () => {
    test('handles search input changes', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const searchBox = screen.getByTestId('search-box');
      fireEvent.change(searchBox, { target: { value: 'test query' } });

      // Component should handle search change (indirect test - component renders without errors)
      expect(searchBox).toHaveValue('test query');
    });

    test('handles pagination next button click', () => {
      // Ensure mock returns default state
      vi.mocked(useKBSortLocalStorage).mockReturnValue({
        sortOption: { field: 'createdDate', order: 'descending' },
        updateSortOption: mockUpdateSortOption,
        clearSortOption: mockClearSortOption,
        isLoaded: true,
      });

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const nextButton = screen.getByTestId('pagination-next');
      fireEvent.click(nextButton);

      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          take: 10,
          direction: 'forward',
          cursor: 'next-cursor',
          query: '',
          sortBy: 'createdAt',
          sortOrder: 'desc',
          baseId: 'kb-123',
        }),
        false
      );
    });

    test('handles pagination previous button click', () => {
      // Ensure mock returns default state
      vi.mocked(useKBSortLocalStorage).mockReturnValue({
        sortOption: { field: 'createdDate', order: 'descending' },
        updateSortOption: mockUpdateSortOption,
        clearSortOption: mockClearSortOption,
        isLoaded: true,
      });

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const prevButton = screen.getByTestId('pagination-prev');
      fireEvent.click(prevButton);

      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          take: 10,
          direction: 'backward',
          cursor: 'prev-cursor',
          query: '',
          sortBy: 'createdAt',
          sortOrder: 'desc',
          baseId: 'kb-123',
        }),
        false
      );
    });

    test('handles page size change', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const pageSizeSelect = screen.getByTestId('page-size-select');
      fireEvent.change(pageSizeSelect, { target: { value: '20' } });

      // Component should handle page size change (indirect test - component renders without errors)
      expect(pageSizeSelect).toBeInTheDocument();
    });

    test('handles article row click', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const articleRow = screen.getByTestId('article-row-article-123');
      fireEvent.click(articleRow);

      // Component should handle row click (indirect test - component renders without errors)
      expect(articleRow).toHaveAttribute('data-testid', 'article-row-article-123');
    });

    test('handles article delete action', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const deleteButtons = screen.getAllByTestId('action-delete');
      fireEvent.click(deleteButtons[0]);

      expect(mockArticleContextData.deleteArticle).toHaveBeenCalledWith(
        MOCK_ARTICLE,
        expect.any(Function)
      );
    });

    test('handles export button click', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const exportButton = screen.getByTestId('export-button');
      fireEvent.click(exportButton);

      expect(mockArticleContextData.getArticles).toHaveBeenCalled();
    });
  });

  describe('Article Status Display', () => {
    test('displays published status for published articles', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const statusElements = screen.getAllByTestId('article-status');
      const publishedStatus = statusElements.find(
        (el) => el.getAttribute('data-status') === 'published'
      );
      expect(publishedStatus).toBeInTheDocument();
    });

    test('displays draft status for draft articles', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const statusElements = screen.getAllByTestId('article-status');
      const draftStatus = statusElements.find((el) => el.getAttribute('data-status') === 'draft');
      expect(draftStatus).toBeInTheDocument();
    });
  });

  describe('Article Actions', () => {
    test('renders action buttons for each article', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const actionButtons = screen.getAllByTestId('article-actions');
      expect(actionButtons).toHaveLength(2);
    });

    test('handles article open action', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const openButtons = screen.getAllByTestId('action-open');
      fireEvent.click(openButtons[0]);

      // Component should handle action click (indirect test - component renders without errors)
      expect(openButtons[0]).toBeInTheDocument();
    });
  });

  describe('Article Viewer Integration', () => {
    test('renders article viewer component', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.getByTestId('article-viewer')).toBeInTheDocument();
    });

    test('handles article viewer close', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const backButton = screen.getByTestId('viewer-back');
      fireEvent.click(backButton);

      expect(mockArticleContextData.setActiveArticleId).toHaveBeenCalledWith('');
    });

    test('handles article viewer update', () => {
      // Ensure mock returns default state
      vi.mocked(useKBSortLocalStorage).mockReturnValue({
        sortOption: { field: 'createdDate', order: 'descending' },
        updateSortOption: mockUpdateSortOption,
        clearSortOption: mockClearSortOption,
        isLoaded: true,
      });

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Clear initial mount calls
      vi.clearAllMocks();

      const updateButton = screen.getByTestId('viewer-update');
      fireEvent.click(updateButton);

      // Verify that setTimeout was called (1 timer: delayed refresh only, no open first article)
      expect(vi.getTimerCount()).toBe(1);

      // Fast-forward time by UPDATING_DELAY_GET_ARTICLES (1000ms)
      vi.advanceTimersByTime(1000);

      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          take: 10,
          direction: 'forward',
          cursor: '',
          query: '',
          sortBy: 'createdAt',
          sortOrder: 'desc',
          baseId: 'kb-123',
        }),
        true
      );
    });
  });

  describe('Initialization', () => {
    test('fetches collection on mount', () => {
      // Ensure mock returns default state
      vi.mocked(useKBSortLocalStorage).mockReturnValue({
        sortOption: { field: 'createdDate', order: 'descending' },
        updateSortOption: mockUpdateSortOption,
        clearSortOption: mockClearSortOption,
        isLoaded: true,
      });

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(mockArticleContextData.setCurrentKb).toHaveBeenCalledWith(MOCK_KB);
      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          take: 10,
          direction: 'forward',
          cursor: '',
          query: '',
          sortBy: 'createdAt',
          sortOrder: 'desc',
          baseId: 'kb-123',
        }),
        true
      );
    });

    test('refetches collection when KB ID changes', () => {
      // Ensure mock returns default state
      vi.mocked(useKBSortLocalStorage).mockReturnValue({
        sortOption: { field: 'createdDate', order: 'descending' },
        updateSortOption: mockUpdateSortOption,
        clearSortOption: mockClearSortOption,
        isLoaded: true,
      });

      const { rerender } = render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const newKb = { ...MOCK_KB, id: 'new-kb-id' };
      rerender(
        <AllTheProviders>
          <ArticleCollection kb={newKb} />
        </AllTheProviders>
      );

      expect(mockArticleContextData.setCurrentKb).toHaveBeenCalledWith(newKb);
      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          take: 10,
          direction: 'forward',
          cursor: '',
          query: '',
          sortBy: 'createdAt',
          sortOrder: 'desc',
          baseId: 'new-kb-id',
        }),
        true
      );
    });
  });

  describe('Conditional Rendering', () => {
    test('hides create new button when articles are empty', () => {
      mockArticleContextData.articles = [];

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.queryByTestId('create-new-button')).not.toBeInTheDocument();
    });

    test('shows create new button when articles exist', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.getByTestId('create-new-button')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    test('handles undefined articles data gracefully', () => {
      mockArticleContextData.articles = undefined as any;

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // When articles is undefined, it might not show empty state but should not crash
      expect(screen.queryByTestId('grid-table')).not.toBeInTheDocument();
    });

    test('handles null pagination gracefully', () => {
      mockArticleContextData.pagination = null as any;

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.queryByTestId('pagination')).not.toBeInTheDocument();
    });
  });

  describe('Feature Flags', () => {
    describe('KB Sorting Feature', () => {
      test('shows KBSort component when KB_SORTING_ARTICLE flag is enabled', () => {
        // Feature flag is enabled by default in beforeEach
        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        expect(screen.getByTestId('kb-sort')).toBeInTheDocument();
      });

      test('hides KBSort component when KB_SORTING_ARTICLE flag is disabled', () => {
        // Mock feature flags with sorting disabled
        vi.mocked(useFeatureFlags).mockReturnValue({
          isKBSortingEnabled: false,
          isSchedulePublishEnabled: true,
          isScheduleUnpublishEnabled: true,
          isScheduleModalEnabled: false,
          isAnyScheduleFeatureEnabled: () => true,
          getAllFlags: () => ({
            KB_SORTING_ARTICLE: false,
            KB_SCHEDULE_PUBLISH: true,
            KB_SCHEDULE_UNPUBLISH: true,
            KB_SCHEDULE_PUBLISH_UNPUBLISH_MODAL: false,
          }),
        });

        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        expect(screen.queryByTestId('kb-sort')).not.toBeInTheDocument();
      });

      test('handles sort change when KBSort is enabled', () => {
        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        const sortButton = screen.getByTestId('sort-button');
        fireEvent.click(sortButton);

        // Verify that getArticles is called (sort change triggers refetch)
        expect(mockArticleContextData.getArticles).toHaveBeenCalled();
      });
    });

    describe('Search functionality', () => {
      test('search box is always visible regardless of sorting feature flag', () => {
        // Test with sorting disabled
        vi.mocked(useFeatureFlags).mockReturnValue({
          isKBSortingEnabled: false,
          isSchedulePublishEnabled: true,
          isScheduleUnpublishEnabled: true,
          isScheduleModalEnabled: false,
          isAnyScheduleFeatureEnabled: () => true,
          getAllFlags: () => ({
            KB_SORTING_ARTICLE: false,
            KB_SCHEDULE_PUBLISH: true,
            KB_SCHEDULE_UNPUBLISH: true,
            KB_SCHEDULE_PUBLISH_UNPUBLISH_MODAL: false,
          }),
        });

        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        expect(screen.getByTestId('search-box')).toBeInTheDocument();
        expect(screen.queryByTestId('kb-sort')).not.toBeInTheDocument();
      });
    });

    describe('Feature flag integration', () => {
      test('calls useFeatureFlags hook on component mount', () => {
        const mockUseFeatureFlags = vi.mocked(useFeatureFlags);

        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        expect(mockUseFeatureFlags).toHaveBeenCalled();
      });

      test('responds to feature flag changes', () => {
        const { rerender } = render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Initially sorting should be visible
        expect(screen.getByTestId('kb-sort')).toBeInTheDocument();

        // Change feature flag to disable sorting
        vi.mocked(useFeatureFlags).mockReturnValue({
          isKBSortingEnabled: false,
          isSchedulePublishEnabled: true,
          isScheduleUnpublishEnabled: true,
          isScheduleModalEnabled: false,
          isAnyScheduleFeatureEnabled: () => true,
          getAllFlags: () => ({
            KB_SORTING_ARTICLE: false,
            KB_SCHEDULE_PUBLISH: true,
            KB_SCHEDULE_UNPUBLISH: true,
            KB_SCHEDULE_PUBLISH_UNPUBLISH_MODAL: false,
          }),
        });

        rerender(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Sorting should now be hidden
        expect(screen.queryByTestId('kb-sort')).not.toBeInTheDocument();
      });
    });
  });

  describe('New Sorting Functionality', () => {
    beforeEach(() => {
      // Enable sorting for these tests
      vi.mocked(useFeatureFlags).mockReturnValue({
        isKBSortingEnabled: true,
        isSchedulePublishEnabled: true,
        isScheduleUnpublishEnabled: false,
        isScheduleModalEnabled: false,
        isAnyScheduleFeatureEnabled: () => true,
        getAllFlags: () => ({
          KB_SORTING_ARTICLE: true,
          KB_SCHEDULE_PUBLISH: true,
          KB_SCHEDULE_UNPUBLISH: false,
          KB_SCHEDULE_PUBLISH_UNPUBLISH_MODAL: false,
        }),
      });
    });

    test('uses getArticles instead of getCollection when sorting is enabled', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Should use getArticles for initial fetch
      expect(mockArticleContextData.getArticles).toHaveBeenCalled();
    });

    test('calls getArticles with correct sorting parameters when sort changes', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const sortButton = screen.getByTestId('sort-button');
      fireEvent.click(sortButton);

      // Should call getArticles with sorting parameters
      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          sortBy: 'title',
          sortOrder: 'asc',
          cursor: '',
          direction: 'forward',
          baseId: 'kb-123',
        }),
        false
      );
    });

    test('handles search with new getArticles method', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Clear previous calls from component mount
      vi.clearAllMocks();

      const searchBox = screen.getByTestId('search-box');
      fireEvent.change(searchBox, { target: { value: 'test search' } });

      // Search should trigger setSearchKeyword and getArticles calls
      expect(mockArticleContextData.setSearchKeyword).toHaveBeenCalledWith('test search');
      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          query: 'test search',
          direction: 'forward',
          cursor: '',
          baseId: 'kb-123',
        }),
        true
      );
    });

    test('handles pagination with sorting parameters', () => {
      // Ensure mock returns default state
      vi.mocked(useKBSortLocalStorage).mockReturnValue({
        sortOption: { field: 'createdDate', order: 'descending' },
        updateSortOption: mockUpdateSortOption,
        clearSortOption: mockClearSortOption,
        isLoaded: true,
      });

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const nextButton = screen.getByTestId('pagination-next');
      fireEvent.click(nextButton);

      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          direction: 'forward',
          cursor: 'next-cursor',
          sortBy: 'createdAt',
          sortOrder: 'desc',
          baseId: 'kb-123',
        }),
        false
      );
    });

    test('handles page size change with sorting', () => {
      // Ensure mock returns default state
      vi.mocked(useKBSortLocalStorage).mockReturnValue({
        sortOption: { field: 'createdDate', order: 'descending' },
        updateSortOption: mockUpdateSortOption,
        clearSortOption: mockClearSortOption,
        isLoaded: true,
      });

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const pageSizeSelect = screen.getByTestId('page-size-select');
      fireEvent.change(pageSizeSelect, { target: { value: '20' } });

      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          take: 20,
          sortBy: 'createdAt',
          sortOrder: 'desc',
          baseId: 'kb-123',
        }),
        false
      );
    });

    test('maintains sort state across operations', () => {
      // Set up mock with custom sort state
      vi.mocked(useKBSortLocalStorage).mockReturnValue({
        sortOption: { field: 'title', order: 'ascending' },
        updateSortOption: mockUpdateSortOption,
        clearSortOption: mockClearSortOption,
        isLoaded: true,
      });

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Then trigger pagination
      const nextButton = screen.getByTestId('pagination-next');
      fireEvent.click(nextButton);

      // Should maintain the sort parameters
      expect(mockArticleContextData.getArticles).toHaveBeenLastCalledWith(
        expect.objectContaining({
          sortBy: 'title',
          sortOrder: 'asc',
          direction: 'forward',
          cursor: 'next-cursor',
        }),
        false
      );
    });

    test('resets cursor and direction when sort changes', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const sortButton = screen.getByTestId('sort-button');
      fireEvent.click(sortButton);

      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          cursor: '',
          direction: 'forward',
          sortBy: 'title',
          sortOrder: 'asc',
          baseId: 'kb-123',
        }),
        false
      );
    });

    test('handles article viewer updates with sorting', () => {
      // Set up mock with custom sort state
      vi.mocked(useKBSortLocalStorage).mockReturnValue({
        sortOption: { field: 'title', order: 'ascending' },
        updateSortOption: mockUpdateSortOption,
        clearSortOption: mockClearSortOption,
        isLoaded: true,
      });

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Clear previous calls
      vi.clearAllMocks();

      // Then trigger viewer update
      const updateButton = screen.getByTestId('viewer-update');
      fireEvent.click(updateButton);

      // Fast-forward time by UPDATING_DELAY_GET_ARTICLES (1000ms)
      vi.advanceTimersByTime(1000);

      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          sortBy: 'title',
          sortOrder: 'asc',
          baseId: 'kb-123',
        }),
        true
      );
    });

    test('handles delete article with sorting maintained', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Change sort first
      const sortButton = screen.getByTestId('sort-button');
      fireEvent.click(sortButton);

      // Then delete an article
      const deleteButtons = screen.getAllByTestId('action-delete');
      fireEvent.click(deleteButtons[0]);

      // Verify delete was called with callback
      expect(mockArticleContextData.deleteArticle).toHaveBeenCalledWith(
        MOCK_ARTICLE,
        expect.any(Function)
      );

      // Simulate the callback execution
      const deleteCallback = mockArticleContextData.deleteArticle.mock.calls[0][1];
      deleteCallback();

      // Should maintain sorting in the refetch
      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          sortBy: 'title',
          sortOrder: 'asc',
          baseId: 'kb-123',
        }),
        false
      );
    });

    test('handles KB change with sorting enabled', () => {
      // Ensure mock returns default state
      vi.mocked(useKBSortLocalStorage).mockReturnValue({
        sortOption: { field: 'createdDate', order: 'descending' },
        updateSortOption: mockUpdateSortOption,
        clearSortOption: mockClearSortOption,
        isLoaded: true,
      });

      const { rerender } = render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Change to new KB
      const newKb = { ...MOCK_KB, id: 'new-kb-id' };
      rerender(
        <AllTheProviders>
          <ArticleCollection kb={newKb} />
        </AllTheProviders>
      );

      expect(mockArticleContextData.setCurrentKb).toHaveBeenCalledWith(newKb);
      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          sortBy: 'createdAt',
          sortOrder: 'desc',
          baseId: 'new-kb-id',
        }),
        true
      );
    });

    test('requires getArticles method to be available', () => {
      // This test ensures getArticles is properly provided by context
      expect(mockArticleContextData.getArticles).toBeDefined();
      expect(typeof mockArticleContextData.getArticles).toBe('function');
    });

    test('handles empty search keyword correctly with sorting', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const searchBox = screen.getByTestId('search-box');
      // First add some text
      fireEvent.change(searchBox, { target: { value: 'test' } });
      // Then clear it
      fireEvent.change(searchBox, { target: { value: '' } });

      expect(mockArticleContextData.setSearchKeyword).toHaveBeenCalledWith('');
      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          query: '',
          direction: 'forward', // Should use forward for empty search
          baseId: 'kb-123',
        }),
        true
      );
    });

    test('maps sort field values correctly', () => {
      // Mock different sort options
      const mockSortOptions = [
        {
          field: 'createdDate',
          order: 'descending',
          expectedField: 'createdAt',
          expectedOrder: 'desc',
        },
        {
          field: 'updatedDate',
          order: 'ascending',
          expectedField: 'updatedAt',
          expectedOrder: 'asc',
        },
        { field: 'title', order: 'ascending', expectedField: 'title', expectedOrder: 'asc' },
      ];

      mockSortOptions.forEach(() => {
        // Update the mock to return specific sort values
        vi.clearAllMocks();

        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Simulate sort change - we need to update our mock to handle different values
        const sortButtons = screen.getAllByTestId('sort-button');
        const sortButton = sortButtons[0]; // Use the first sort button

        // Since our mock always returns the same values, we'll test the mapping logic
        // by verifying the expected API calls
        fireEvent.click(sortButton);

        // The actual mapping happens in the component, so we test the default behavior
        expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
          expect.objectContaining({
            sortBy: expect.any(String),
            sortOrder: expect.any(String),
            baseId: 'kb-123',
          }),
          expect.any(Boolean)
        );
      });
    });

    test('handles sort option changes through callback', () => {
      // Ensure mock returns default state
      vi.mocked(useKBSortLocalStorage).mockReturnValue({
        sortOption: { field: 'createdDate', order: 'descending' },
        updateSortOption: mockUpdateSortOption,
        clearSortOption: mockClearSortOption,
        isLoaded: true,
      });

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Verify initial call with default sort (should be called on mount)
      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          sortBy: 'createdAt',
          sortOrder: 'desc',
          baseId: 'kb-123',
          take: 10,
          direction: 'forward',
          cursor: '',
          query: '',
        }),
        true
      );
    });

    test('resets pagination when sort changes', async () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Initial render should call getArticles
      expect(mockArticleContextData.getArticles).toHaveBeenCalled();

      // Verify that sort changes reset cursor and direction to default
      const initialCall = mockArticleContextData.getArticles.mock.calls[0];
      expect(initialCall[0]).toEqual(
        expect.objectContaining({
          cursor: '',
          direction: 'forward',
        })
      );
    });

    test('maintains sort during search operations', () => {
      // Ensure mock returns default state
      vi.mocked(useKBSortLocalStorage).mockReturnValue({
        sortOption: { field: 'createdDate', order: 'descending' },
        updateSortOption: mockUpdateSortOption,
        clearSortOption: mockClearSortOption,
        isLoaded: true,
      });

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Clear initial mount calls
      vi.clearAllMocks();

      // Simulate search
      const searchBox = screen.getByPlaceholderText('search');
      fireEvent.change(searchBox, { target: { value: 'test query' } });

      // Search should maintain sort parameters
      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          query: 'test query',
          sortBy: 'createdAt', // Should maintain sort field
          sortOrder: 'desc', // Should maintain sort order
          direction: 'forward', // Search uses forward direction
          cursor: '',
        }),
        true
      );
    });

    test('handles edge case sort field mappings', () => {
      // Ensure mock returns default state
      vi.mocked(useKBSortLocalStorage).mockReturnValue({
        sortOption: { field: 'createdDate', order: 'descending' },
        updateSortOption: mockUpdateSortOption,
        clearSortOption: mockClearSortOption,
        isLoaded: true,
      });

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Test that default sort field is used
      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          sortBy: 'createdAt', // Default when no specific field provided
          sortOrder: 'desc', // Default when no specific order provided
        }),
        expect.any(Boolean)
      );
    });
  });

  describe('Edge Cases and Error Handling', () => {
    test('handles KB with empty name', () => {
      const kbWithEmptyName = { ...MOCK_KB, name: '' };

      render(
        <AllTheProviders>
          <ArticleCollection kb={kbWithEmptyName} />
        </AllTheProviders>
      );

      // Should render and use fallback name
      expect(screen.getByTestId('search-box')).toBeInTheDocument();
    });

    test('handles empty articles array', () => {
      mockArticleContextData.articles = [];

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Should show empty state when articles array is empty
      expect(screen.getByTestId('article-empty')).toBeInTheDocument();
    });

    test('handles search with special characters', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Clear initial mount calls
      vi.clearAllMocks();

      const searchBox = screen.getByPlaceholderText('search');
      const specialCharQuery = '<script>alert("xss")</script>';

      fireEvent.change(searchBox, { target: { value: specialCharQuery } });

      // Search should handle special characters properly
      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          query: specialCharQuery,
        }),
        true
      );
    });

    test('handles very long search queries', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Clear initial mount calls
      vi.clearAllMocks();

      const searchBox = screen.getByPlaceholderText('search');
      const longQuery = 'a'.repeat(1000);

      fireEvent.change(searchBox, { target: { value: longQuery } });

      // Search should handle long queries properly
      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          query: longQuery,
        }),
        true
      );
    });

    test('renders component without crashing', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Component should render without crashing
      expect(screen.getByTestId('search-box')).toBeInTheDocument();
      expect(screen.getByTestId('grid-table')).toBeInTheDocument();
    });

    test('handles rapid search input changes', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Clear initial mount calls
      vi.clearAllMocks();

      const searchBox = screen.getByPlaceholderText('search');

      // Rapid typing simulation - final value should be used
      fireEvent.change(searchBox, { target: { value: 'a' } });
      fireEvent.change(searchBox, { target: { value: 'ab' } });
      fireEvent.change(searchBox, { target: { value: 'abc' } });

      // Should be called with final value
      expect(mockArticleContextData.getArticles).toHaveBeenLastCalledWith(
        expect.objectContaining({
          query: 'abc',
        }),
        true
      );
    });

    test('handles pagination with missing pagination data', () => {
      mockArticleContextData.pagination = null as any;

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Should not render pagination when pagination data is missing
      expect(screen.queryByTestId('custom-pagination')).not.toBeInTheDocument();
    });

    test('handles article viewer opening with invalid article data', () => {
      const articlesWithInvalidData = [
        {
          ...MOCK_ARTICLES[0],
          id: null,
        },
        {
          ...MOCK_ARTICLES[1],
          baseId: undefined,
        },
      ] as any;

      mockArticleContextData.articles = articlesWithInvalidData;

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Should render table but handle invalid data gracefully
      expect(screen.getByTestId('grid-table')).toBeInTheDocument();
    });

    test('handles article deletion with invalid article', async () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const deleteButtons = screen.getAllByTestId('action-delete');
      fireEvent.click(deleteButtons[0]);

      // Should handle deletion attempt gracefully
      expect(mockArticleContextData.deleteArticle).toHaveBeenCalled();
    });

    test('handles page size change with invalid values', async () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Simulate page size change through pagination component
      // The actual implementation would validate the page size
      expect(screen.getByTestId('search-box')).toBeInTheDocument();
    });

    test('handles activeArticleId changes', () => {
      mockArticleContextData.activeArticleId = 'invalid-article-id';

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Should handle invalid activeArticleId without errors
      expect(screen.getByTestId('grid-table')).toBeInTheDocument();
    });

    test('handles concurrent fetch operations', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Clear initial mount calls
      vi.clearAllMocks();

      const searchBox = screen.getByPlaceholderText('search');

      // Trigger multiple operations quickly
      fireEvent.change(searchBox, { target: { value: 'test1' } });
      fireEvent.change(searchBox, { target: { value: 'test2' } });

      // Should handle multiple concurrent requests - final value should be used
      expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
        expect.objectContaining({
          query: 'test2',
        }),
        true
      );
    });

    test('handles component unmounting during async operations', () => {
      const { unmount } = render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const searchBox = screen.getByPlaceholderText('search');
      fireEvent.change(searchBox, { target: { value: 'test' } });
      fireEvent.keyDown(searchBox, { key: 'Enter', code: 'Enter' });

      // Unmount component while async operation might be in progress
      unmount();

      // Should not throw errors during cleanup
      expect(true).toBe(true);
    });

    test('handles feature flag changes during runtime', () => {
      const { rerender } = render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.getByTestId('kb-sort')).toBeInTheDocument();

      // Disable sorting feature flag
      vi.mocked(useFeatureFlags).mockReturnValue({
        isKBSortingEnabled: false,
        isSchedulePublishEnabled: true,
        isScheduleUnpublishEnabled: true,
        isScheduleModalEnabled: false,
        isAnyScheduleFeatureEnabled: () => true,
        getAllFlags: () => ({
          KB_SORTING_ARTICLE: false,
          KB_SCHEDULE_PUBLISH: true,
          KB_SCHEDULE_UNPUBLISH: true,
          KB_SCHEDULE_PUBLISH_UNPUBLISH_MODAL: false,
        }),
      });

      rerender(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.queryByTestId('kb-sort')).not.toBeInTheDocument();
    });
  });

  describe('Timeout Functionality', () => {
    describe('Article Moving with Timeout', () => {
      test('calls fetchCollection immediately and after MOVING_DELAY_GET_ARTICLES when afterMoving callback is executed', async () => {
        // Ensure mock returns default state
        vi.mocked(useKBSortLocalStorage).mockReturnValue({
          sortOption: { field: 'createdDate', order: 'descending' },
          updateSortOption: mockUpdateSortOption,
          clearSortOption: mockClearSortOption,
          isLoaded: true,
        });

        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Verify that the afterMoving callback is set up
        expect(mockAfterMovingCallback).toBeDefined();

        // Execute the afterMoving callback to simulate article moving completion
        if (mockAfterMovingCallback) {
          mockAfterMovingCallback();
        }

        // Verify that getArticles was called immediately (first call)
        expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
          expect.objectContaining({
            take: 10,
            direction: 'forward',
            cursor: '',
            query: '',
            sortBy: 'createdAt',
            sortOrder: 'desc',
            baseId: 'kb-123',
          }),
          true
        );

        // Verify that setTimeout was called for delayed refresh (open first article timeout only triggers on articlesData change)
        expect(vi.getTimerCount()).toBe(1);

        // Clear mock calls to check second call
        vi.clearAllMocks();

        // Fast-forward time by MOVING_DELAY_GET_ARTICLES (1200ms)
        vi.advanceTimersByTime(1200);

        // Verify that getArticles was called again after the timeout (second call)
        expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
          expect.objectContaining({
            take: 10,
            direction: 'forward',
            cursor: '',
            query: '',
            sortBy: 'createdAt',
            sortOrder: 'desc',
            baseId: 'kb-123',
          }),
          true
        );
      });

      test('calls fetchCollection immediately but second call only after timeout', () => {
        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Execute the afterMoving callback
        if (mockAfterMovingCallback) {
          mockAfterMovingCallback();
        }

        // Verify that getArticles was called immediately (first call)
        expect(mockArticleContextData.getArticles).toHaveBeenCalledTimes(1);

        // Clear mock calls to track second call
        vi.clearAllMocks();

        // Fast-forward time by less than MOVING_DELAY_GET_ARTICLES (500ms < 1200ms)
        vi.advanceTimersByTime(500);

        // Verify that second getArticles call was NOT made yet
        expect(mockArticleContextData.getArticles).not.toHaveBeenCalled();

        // Fast-forward the remaining time to complete the timeout
        vi.advanceTimersByTime(700);

        // Now second getArticles call should be made
        expect(mockArticleContextData.getArticles).toHaveBeenCalledTimes(1);
      });

      test('handles multiple afterMoving callbacks correctly', () => {
        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Execute the afterMoving callback multiple times
        if (mockAfterMovingCallback) {
          mockAfterMovingCallback();
          mockAfterMovingCallback();
        }

        // Should only have 1 timer pending (previous timeout was cleared, new moving delay only)
        expect(vi.getTimerCount()).toBe(1);

        // Fast-forward time by MOVING_DELAY_GET_ARTICLES (1200ms)
        vi.advanceTimersByTime(1200);

        // Should be called 3 times: 2 immediate calls + 1 delayed call (previous timeout was cleared)
        expect(mockArticleContextData.getArticles).toHaveBeenCalledTimes(3);
      });
    });

    describe('Article Import with Timeout', () => {
      test('calls fetchCollection after IMPORT_DELAY_GET_ARTICLES when onImport is triggered', async () => {
        // Ensure mock returns default state
        vi.mocked(useKBSortLocalStorage).mockReturnValue({
          sortOption: { field: 'createdDate', order: 'descending' },
          updateSortOption: mockUpdateSortOption,
          clearSortOption: mockClearSortOption,
          isLoaded: true,
        });

        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Verify create new button exists (articles must not be empty)
        expect(mockArticleContextData.articles).not.toEqual([]);
        const createNewButton = screen.getByTestId('create-new-button');
        expect(createNewButton).toBeInTheDocument();

        // Trigger the import functionality
        fireEvent.click(createNewButton);

        // Verify that setTimeout was called (1 timer: delayed refresh only, no open first article)
        expect(vi.getTimerCount()).toBe(1);

        // Fast-forward time by IMPORT_DELAY_GET_ARTICLES (2000ms)
        vi.advanceTimersByTime(2000);

        // Verify that getArticles was called after the timeout
        expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
          expect.objectContaining({
            take: 10,
            direction: 'forward',
            cursor: '',
            query: '',
            sortBy: 'createdAt',
            sortOrder: 'desc',
            baseId: 'kb-123',
          }),
          true
        );
      });

      test('calls fetchCollection after timeout when empty state import is triggered', async () => {
        // Set empty articles to show empty state
        mockArticleContextData.articles = [];

        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Trigger import from empty state
        const importButton = screen.getByTestId('empty-import');
        fireEvent.click(importButton);

        // Verify that setTimeout was called (1 timer: delayed refresh only, no open first article)
        expect(vi.getTimerCount()).toBe(1);

        // Fast-forward time by IMPORT_DELAY_GET_ARTICLES (2000ms)
        vi.advanceTimersByTime(2000);

        // Verify that getArticles was called after the timeout
        expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
          expect.objectContaining({
            baseId: 'kb-123',
          }),
          true
        );
      });

      test('calls fetchCollection immediately but second call only after timeout', () => {
        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Trigger the import functionality
        const createNewButton = screen.getByTestId('create-new-button');
        fireEvent.click(createNewButton);

        // Verify that getArticles was called immediately (first call)
        expect(mockArticleContextData.getArticles).toHaveBeenCalledTimes(1);

        // Clear mock calls to track second call
        vi.clearAllMocks();

        // Fast-forward time by less than IMPORT_DELAY_GET_ARTICLES (1500ms < 2000ms)
        vi.advanceTimersByTime(1500);

        // Verify that second getArticles call was NOT made yet
        expect(mockArticleContextData.getArticles).not.toHaveBeenCalled();

        // Complete the timeout
        vi.advanceTimersByTime(500);

        // Now second getArticles call should be made
        expect(mockArticleContextData.getArticles).toHaveBeenCalledTimes(1);
      });

      test('handles multiple import operations with timeouts', () => {
        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Trigger multiple import operations
        const createNewButton = screen.getByTestId('create-new-button');
        fireEvent.click(createNewButton);
        fireEvent.click(createNewButton);

        // Should only have 1 timer pending (previous timeout was cleared, new delayed only)
        expect(vi.getTimerCount()).toBe(1);

        // Fast-forward time by IMPORT_DELAY_GET_ARTICLES (2000ms)
        vi.advanceTimersByTime(2000);

        // Should be called 3 times: 2 immediate calls + 1 delayed call (previous timeout was cleared)
        expect(mockArticleContextData.getArticles).toHaveBeenCalledTimes(3);
      });
    });

    describe('Article Update with Timeout', () => {
      test('calls fetchCollection after UPDATING_DELAY_GET_ARTICLES when onUpdated is triggered with showLoader=true', async () => {
        // Ensure mock returns default state
        vi.mocked(useKBSortLocalStorage).mockReturnValue({
          sortOption: { field: 'createdDate', order: 'descending' },
          updateSortOption: mockUpdateSortOption,
          clearSortOption: mockClearSortOption,
          isLoaded: true,
        });

        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Trigger the update functionality with showLoader=true
        const updateButton = screen.getByTestId('viewer-update');
        fireEvent.click(updateButton);

        // Verify that setTimeout was called (1 timer: delayed refresh only, no open first article)
        expect(vi.getTimerCount()).toBe(1);

        // Fast-forward time by UPDATING_DELAY_GET_ARTICLES (1000ms)
        vi.advanceTimersByTime(1000);

        // Verify that getArticles was called after the timeout with correct parameters
        expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
          expect.objectContaining({
            take: 10,
            direction: 'forward',
            cursor: '',
            query: '',
            sortBy: 'createdAt',
            sortOrder: 'desc',
            baseId: 'kb-123',
          }),
          true
        );
      });

      test('does not call fetchCollection when onUpdated is triggered with showLoader=false', async () => {
        // Ensure mock returns default state
        vi.mocked(useKBSortLocalStorage).mockReturnValue({
          sortOption: { field: 'createdDate', order: 'descending' },
          updateSortOption: mockUpdateSortOption,
          clearSortOption: mockClearSortOption,
          isLoaded: true,
        });

        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Trigger the update functionality with showLoader=false
        const updateNoLoaderButton = screen.getByTestId('viewer-update-no-loader');
        fireEvent.click(updateNoLoaderButton);

        // Verify that setTimeout was called (1 timer: delayed refresh only, no open first article)
        expect(vi.getTimerCount()).toBe(1);

        // Fast-forward time by UPDATING_DELAY_GET_ARTICLES (1000ms)
        vi.advanceTimersByTime(1000);

        // Verify that getArticles was called after the timeout with showLoader=false
        expect(mockArticleContextData.getArticles).toHaveBeenCalledWith(
          expect.objectContaining({
            take: 10,
            direction: 'forward',
            cursor: '',
            query: '',
            sortBy: 'createdAt',
            sortOrder: 'desc',
            baseId: 'kb-123',
          }),
          false
        );
      });

      test('calls fetchCollection immediately but second call only after timeout', () => {
        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Trigger the update functionality
        const updateButton = screen.getByTestId('viewer-update');
        fireEvent.click(updateButton);

        // Verify that getArticles was called immediately (first call)
        expect(mockArticleContextData.getArticles).toHaveBeenCalledTimes(1);

        // Clear mock calls to track second call
        vi.clearAllMocks();

        // Fast-forward time by less than UPDATING_DELAY_GET_ARTICLES (600ms < 1200ms)
        vi.advanceTimersByTime(600);

        // Verify that second getArticles call was NOT made yet
        expect(mockArticleContextData.getArticles).not.toHaveBeenCalled();

        // Complete the timeout
        vi.advanceTimersByTime(600);

        // Now second getArticles call should be made
        expect(mockArticleContextData.getArticles).toHaveBeenCalledTimes(1);
      });

      test('handles multiple update operations with timeouts', () => {
        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Trigger multiple update operations
        const updateButton = screen.getByTestId('viewer-update');
        fireEvent.click(updateButton);
        fireEvent.click(updateButton);

        // Should only have 1 timer pending (previous timeout was cleared, new delayed only)
        expect(vi.getTimerCount()).toBe(1);

        // Fast-forward time by UPDATING_DELAY_GET_ARTICLES (1200ms)
        vi.advanceTimersByTime(1200);

        // Should be called 3 times: 2 immediate calls + 1 delayed call (previous timeout was cleared)
        expect(mockArticleContextData.getArticles).toHaveBeenCalledTimes(3);
      });

      test('maintains correct timeout duration for update operations', () => {
        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Trigger update timeout operation
        const updateButton = screen.getByTestId('viewer-update');
        fireEvent.click(updateButton);

        // Clear immediate call to test only the delayed call
        vi.clearAllMocks();

        // Test exact timing - 1199ms should not trigger (UPDATING_DELAY_GET_ARTICLES = 1200ms)
        vi.advanceTimersByTime(1199);
        expect(mockArticleContextData.getArticles).not.toHaveBeenCalled();

        // 1ms more (total 1200ms) should trigger
        vi.advanceTimersByTime(1);
        expect(mockArticleContextData.getArticles).toHaveBeenCalled();
      });
    });

    describe('Timer Edge Cases', () => {
      test('handles component unmount with pending timers', () => {
        const { unmount } = render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Trigger multiple timeout operations
        if (mockAfterMovingCallback) {
          mockAfterMovingCallback();
        }
        const createNewButton = screen.getByTestId('create-new-button');
        fireEvent.click(createNewButton);
        const updateButton = screen.getByTestId('viewer-update');
        fireEvent.click(updateButton);

        // Verify timers are pending (5 timers: 3 immediate calls + 2 delayed refresh timers, moving callback sets flag but doesn't trigger open first article timeout until articlesData changes)
        expect(vi.getTimerCount()).toBe(5);

        // Unmount component
        unmount();

        // Fast-forward time
        vi.advanceTimersByTime(2000);

        // Should not crash or cause issues
        expect(true).toBe(true);
      });

      test('handles rapid timeout operations', () => {
        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Trigger multiple operations rapidly (using import functionality)
        const createNewButton = screen.getByTestId('create-new-button');
        for (let i = 0; i < 5; i++) {
          fireEvent.click(createNewButton);
        }

        // Should only have 1 timer pending (previous timeouts were cleared, final operation has delayed only, no move operation so no open first article delay)
        expect(vi.getTimerCount()).toBe(1);

        // Fast-forward time by IMPORT_DELAY_GET_ARTICLES (2000ms)
        vi.advanceTimersByTime(2000);

        // Should be called 6 times: 5 immediate calls + 1 delayed call (previous timeouts were cleared)
        expect(mockArticleContextData.getArticles).toHaveBeenCalledTimes(6);
        expect(vi.getTimerCount()).toBe(0);
      });

      test('maintains correct timeout duration for moving operations', () => {
        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Trigger moving timeout operation
        if (mockAfterMovingCallback) {
          mockAfterMovingCallback();
        }

        // Clear immediate call to test only the delayed call
        vi.clearAllMocks();

        // Test exact timing - 1199ms should not trigger (MOVING_DELAY_GET_ARTICLES = 1200ms)
        vi.advanceTimersByTime(1199);
        expect(mockArticleContextData.getArticles).not.toHaveBeenCalled();

        // 1ms more (total 1200ms) should trigger
        vi.advanceTimersByTime(1);
        expect(mockArticleContextData.getArticles).toHaveBeenCalled();
      });

      test('maintains correct timeout duration for import operations', () => {
        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Trigger import timeout operation
        const createNewButton = screen.getByTestId('create-new-button');
        fireEvent.click(createNewButton);

        // Clear immediate call to test only the delayed call
        vi.clearAllMocks();

        // Test exact timing - 1999ms should not trigger (IMPORT_DELAY_GET_ARTICLES = 2000ms)
        vi.advanceTimersByTime(1999);
        expect(mockArticleContextData.getArticles).not.toHaveBeenCalled();

        // 1ms more (total 2000ms) should trigger
        vi.advanceTimersByTime(1);
        expect(mockArticleContextData.getArticles).toHaveBeenCalled();
      });

      test('clears previous timeout when new timeout is set', () => {
        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Trigger first import operation
        const createNewButton = screen.getByTestId('create-new-button');
        fireEvent.click(createNewButton);

        // Should have 1 timer (delayed only)
        expect(vi.getTimerCount()).toBe(1);

        // Clear the immediate call to test only the delayed call
        vi.clearAllMocks();

        // Advance time partially (not enough to trigger)
        vi.advanceTimersByTime(1000);
        expect(mockArticleContextData.getArticles).not.toHaveBeenCalled();

        // Trigger second import operation (should clear the first timeout)
        fireEvent.click(createNewButton);

        // Should still have 1 timer (new delayed only, old one was cleared)
        expect(vi.getTimerCount()).toBe(1);

        // Advance time by full timeout duration
        vi.advanceTimersByTime(2000);

        // Should be called twice: once from second immediate call + once from second timeout
        expect(mockArticleContextData.getArticles).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('Article Moving Behavior', () => {
    describe('Article Detail Panel After Moving', () => {
      test('refreshes article list when afterMoving callback is triggered', async () => {
        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Trigger the afterMoving callback
        if (mockAfterMovingCallback) {
          mockAfterMovingCallback();
        }

        // Verify that getArticles was called immediately and after delay
        expect(mockArticleContextData.getArticles).toHaveBeenCalledTimes(1);

        // Fast-forward time by MOVING_DELAY_GET_ARTICLES (1200ms)
        vi.advanceTimersByTime(1200);

        // Verify second call after delay
        expect(mockArticleContextData.getArticles).toHaveBeenCalledTimes(2);
      });

      test('isHandlingMoveRef is ONLY set to true for actual move operations', async () => {
        const { rerender } = render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Test 1: Article move operation should set isHandlingMoveRef to true
        if (mockAfterMovingCallback) {
          mockAfterMovingCallback();
        }

        // Simulate articles data change to trigger the handleOpenFirstArticle useEffect
        mockArticleContextData.articles = [...MOCK_ARTICLES];
        rerender(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Should set timeout for opening first article since isHandlingMoveRef is true
        expect(vi.getTimerCount()).toBe(2); // Moving delay + open first article delay

        // Fast-forward time to reset isHandlingMoveRef
        vi.advanceTimersByTime(2000);
        vi.clearAllMocks();

        // Test 2: Article import operation should NOT set isHandlingMoveRef
        const createNewButton = screen.getAllByTestId('create-new-button')[0];
        fireEvent.click(createNewButton);

        // Change articles data to trigger useEffect
        mockArticleContextData.articles = [
          ...MOCK_ARTICLES,
          { ...MOCK_ARTICLE, id: 'new-imported' },
        ];
        rerender(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Should NOT set timeout for opening first article since isHandlingMoveRef is false
        expect(vi.getTimerCount()).toBe(1); // Only import delay timeout, no open first article timeout

        // Clean up timers and reset state for next test
        vi.advanceTimersByTime(2000);
        vi.clearAllMocks();

        // Test 3: Article update operation should NOT set isHandlingMoveRef
        const updateButton = screen.getByTestId('viewer-update');
        fireEvent.click(updateButton);

        // Change articles data to trigger useEffect
        mockArticleContextData.articles = [...MOCK_ARTICLES];
        rerender(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Should NOT set timeout for opening first article since isHandlingMoveRef is false
        expect(vi.getTimerCount()).toBe(1); // Only update delay timeout, no open first article timeout
      });

      test('verifies isHandlingMoveRef flag behavior with different operation types', async () => {
        const { rerender } = render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        vi.clearAllMocks();

        // Test: Only move operations should trigger isHandlingMoveRef behavior
        // 1. Test article import (should NOT trigger move behavior)
        const createNewButton = screen.getAllByTestId('create-new-button')[0];
        fireEvent.click(createNewButton);

        // Simulate articles changing after import
        mockArticleContextData.articles = [
          ...MOCK_ARTICLES,
          { ...MOCK_ARTICLE, id: 'imported-article' },
        ];
        rerender(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Should NOT have open first article timeout (only import timeout)
        expect(vi.getTimerCount()).toBe(1); // Only import delay, no open first article

        vi.advanceTimersByTime(2000);
        vi.clearAllMocks();

        // 2. Test article update (should NOT trigger move behavior)
        const updateButton = screen.getByTestId('viewer-update');
        fireEvent.click(updateButton);

        // Simulate articles changing after update
        mockArticleContextData.articles = [...MOCK_ARTICLES];
        rerender(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Should NOT have open first article timeout (only update timeout)
        expect(vi.getTimerCount()).toBe(1); // Only update delay, no open first article

        vi.advanceTimersByTime(1000);
        vi.clearAllMocks();

        // 3. Test article move (SHOULD trigger move behavior)
        if (mockAfterMovingCallback) {
          mockAfterMovingCallback();
        }

        // Simulate articles changing after move
        mockArticleContextData.articles = [...MOCK_ARTICLES];
        rerender(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Should have move timeout, open first article timeout, and potentially an additional timer from the immediate fetch
        expect(vi.getTimerCount()).toBe(3); // Move delay + open first article delay + immediate fetch timer

        // Verify the open first article timeout is actually working
        vi.advanceTimersByTime(2000);
        expect(vi.getTimerCount()).toBe(0); // All timeouts completed
      });

      test('opens first article after OPEN_FIRST_ARTICLE_DELAY when articles data changes after move', async () => {
        // Set up initial state with an active article
        mockArticleContextData.activeArticleId = 'article-123';

        const { rerender } = render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Trigger the afterMoving callback to set isHandlingMoveRef to true
        if (mockAfterMovingCallback) {
          mockAfterMovingCallback();
        }

        // Simulate articles data change (which would happen after the move operation completes)
        mockArticleContextData.articles = [...MOCK_ARTICLES];
        rerender(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Verify that the timeout for opening first article is set (total timers should be 2: moving delay + open first article delay)
        expect(vi.getTimerCount()).toBe(2);

        // Fast-forward time by OPEN_FIRST_ARTICLE_DELAY (2000ms)
        vi.advanceTimersByTime(2000);

        // Verify that the first article opening timeout has been processed
        // The handleOpenFirstArticle function should have been called
        // Both timers should be completed by now (OPEN_FIRST_ARTICLE_DELAY = 2000ms > MOVING_DELAY_GET_ARTICLES = 1200ms)
        expect(vi.getTimerCount()).toBe(0);
      });

      test('does not open first article when isHandlingMoveRef is false', async () => {
        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Don't trigger afterMoving callback, so isHandlingMoveRef remains false
        // Just change articlesData to trigger the useEffect
        mockArticleContextData.articles = [
          ...MOCK_ARTICLES,
          {
            ...MOCK_ARTICLE,
            id: 'new-article',
            title: 'New Article',
          },
        ];

        // Re-render to trigger useEffect with new articles data
        const { rerender } = render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        rerender(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Should not set any timeout for opening first article since isHandlingMoveRef is false
        expect(vi.getTimerCount()).toBe(0);
      });

      test('handles opening first article when articles data is undefined', async () => {
        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Trigger the afterMoving callback
        if (mockAfterMovingCallback) {
          mockAfterMovingCallback();
        }

        // Set articles data to undefined
        mockArticleContextData.articles = undefined as any;

        // Re-render to trigger useEffect with undefined articles
        const { rerender } = render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        rerender(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Should not crash and should not set timeout for opening first article
        expect(vi.getTimerCount()).toBe(1); // Only the moving delay timer
      });

      test('handles opening first article when articles array is empty', async () => {
        const { rerender } = render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Trigger the afterMoving callback
        if (mockAfterMovingCallback) {
          mockAfterMovingCallback();
        }

        // Set articles data to empty array and re-render to trigger useEffect
        mockArticleContextData.articles = [];
        rerender(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Should set timeout even with empty articles
        expect(vi.getTimerCount()).toBe(2); // Moving delay + open first article delay

        // Fast-forward time by OPEN_FIRST_ARTICLE_DELAY (2000ms)
        vi.advanceTimersByTime(2000);

        // Should handle empty articles gracefully (no article to open)
        // Both timers should be completed by now (OPEN_FIRST_ARTICLE_DELAY = 2000ms > MOVING_DELAY_GET_ARTICLES = 1200ms)
        expect(vi.getTimerCount()).toBe(0);
      });

      test('resets isHandlingMoveRef after OPEN_FIRST_ARTICLE_DELAY', async () => {
        const { rerender } = render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Trigger the afterMoving callback to set isHandlingMoveRef to true
        if (mockAfterMovingCallback) {
          mockAfterMovingCallback();
        }

        // Simulate articles data change to trigger the handleOpenFirstArticle useEffect
        mockArticleContextData.articles = [...MOCK_ARTICLES];
        rerender(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Verify timeout is set
        expect(vi.getTimerCount()).toBe(2);

        // Fast-forward time by OPEN_FIRST_ARTICLE_DELAY (2000ms)
        vi.advanceTimersByTime(2000);

        // The isHandlingMoveRef should be reset to false
        // This can be verified indirectly by checking that subsequent article data changes don't trigger timeouts
        vi.clearAllMocks();

        // Change articles data again
        mockArticleContextData.articles = [MOCK_ARTICLE];
        rerender(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Should not set new timeout since isHandlingMoveRef is now false
        expect(vi.getTimerCount()).toBe(0); // All timers should be completed by now
      });

      test('maintains correct timeout duration for opening first article', async () => {
        const { rerender } = render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Trigger the afterMoving callback
        if (mockAfterMovingCallback) {
          mockAfterMovingCallback();
        }

        // Simulate articles data change to trigger the handleOpenFirstArticle useEffect
        mockArticleContextData.articles = [...MOCK_ARTICLES];
        rerender(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Test exact timing - 1199ms should not complete either timeout
        vi.advanceTimersByTime(1199);
        expect(vi.getTimerCount()).toBe(2); // Both timers should still be active

        // 1ms more (total 1200ms) should complete the moving delay timeout
        vi.advanceTimersByTime(1);
        expect(vi.getTimerCount()).toBe(1); // Only open first article timer should remain

        // 800ms more (total 2000ms) should complete the open first article timeout
        vi.advanceTimersByTime(800);
        expect(vi.getTimerCount()).toBe(0); // All timers should be completed
      });

      test('handles gracefully when articles data is undefined', async () => {
        render(
          <AllTheProviders>
            <ArticleCollection kb={MOCK_KB} />
          </AllTheProviders>
        );

        // Clear initial mount calls
        vi.clearAllMocks();

        // Trigger the afterMoving callback
        if (mockAfterMovingCallback) {
          mockAfterMovingCallback();
        }

        // Should not crash and should still call getArticles
        expect(mockArticleContextData.getArticles).toHaveBeenCalledTimes(1);

        // Fast-forward time by MOVING_DELAY_GET_ARTICLES
        vi.advanceTimersByTime(1200);

        // Should complete the delayed call
        expect(mockArticleContextData.getArticles).toHaveBeenCalledTimes(2);
      });
    });
  });
});
