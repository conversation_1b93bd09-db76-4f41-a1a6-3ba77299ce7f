import { GeneratorButton } from '@/components/KBArticleGenerator';
import ExportJobByEntityButton from '@/components/KBExport/components/ExportJobByEntityButton';
import { useArticleMovingModal } from '@/components/KBMoving';
import { KBSort, type SortOption } from '@/components/KBSort';
import {
  CustomPagination,
  GridTable,
  type GridTableColumn,
  type GridTableRow,
  SearchBox,
} from '@/components/common';
import { ROOT_PATH } from '@/constants/folder';
import { useArticleContext } from '@/contexts';
import { useKBSortLocalStorage, useKbAccessControl } from '@/hooks';
import { useFeatureFlags } from '@/hooks/useFeatureFlags';
import {
  type Article,
  EntityType,
  type IArticleRequestParams,
  KBDirectionQueryEnum,
  type KnowledgeBase,
  type KnowledgeBaseDirectionQuery,
  type PartOfArticle,
} from '@/types';
import { cleanBadCharacters } from '@/utils/article';
import { validatePageSizeFromString } from '@/utils/pageSizeValidation';
import { Box, Flex, LoadingOverlay, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { formatDateTime } from '@resola-ai/ui/utils/dateTime';
import { useTranslate } from '@tolgee/react';
import isEmpty from 'lodash/isEmpty';
import type React from 'react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import ArticleActions from '../ArticleActions';
import ArticleCreateNew from '../ArticleCreateNew';
import ArticleEmpty from '../ArticleEmpty';
import { ArticleFeedback } from '../ArticleFeedback';
import { ArticleName } from '../ArticleName';
import ArticleStatus from '../ArticleStatus';
import ArticleViewer, { type ArticleViewerRef } from '../ArticleViewer';

const useStyles = createStyles((theme) => ({
  toolbarSection: {
    marginBottom: theme.spacing.xl,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: theme.spacing.md,
    flexWrap: 'wrap',
  },
  toolbarRight: {
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing.md,
  },
  collectionListSection: {
    marginBottom: theme.spacing.xl,
    position: 'relative',
  },
  paginationWrapper: {
    paddingBottom: theme.spacing.xl,
    display: 'flex',
    justifyContent: 'center',
  },
  detailOpened: {
    justifyContent: 'flex-start',
  },
  searchBox: {
    width: rem(400),
    '& input': {
      width: rem(400),
      height: rem(38),
    },
  },
}));

export interface KBArticleCollectionProps {
  kb: KnowledgeBase;
}

// Delay constants for article refresh operations
// These ensure backend operations complete before final data fetch
const MOVING_DELAY_GET_ARTICLES = 1200; // Article move operations
const UPDATING_DELAY_GET_ARTICLES = 1200; // Article update/delete operations
const IMPORT_DELAY_GET_ARTICLES = 1000; // Article import operations

/**
 * KBArticleCollection
 * @param {KBArticleCollectionProps} props
 * @returns {JSX.Element}
 * @dependencies t: i18n function, useStyles: styles
 */
const KBArticleCollection: React.FC<KBArticleCollectionProps> = ({ kb }) => {
  const { t } = useTranslate(['article', 'common']);
  const { cx, classes } = useStyles();
  const articleViewerRef = useRef<ArticleViewerRef>(null);
  const movingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const importTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const updatingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Loading states for delayed operations
  const [isMovingLoading, setIsMovingLoading] = useState(false);
  const [isUpdatingLoading, setIsUpdatingLoading] = useState(false);
  const { openArticleMovingModal } = useArticleMovingModal();
  const { permArticle } = useKbAccessControl();
  const { isKBSortingEnabled } = useFeatureFlags();

  // Sort state management with localStorage persistence
  const {
    sortOption,
    updateSortOption,
    clearSortOption,
    isLoaded: isSortLoaded,
  } = useKBSortLocalStorage();

  /**
   * Init Article Context and get necessary states, actions and services
   * @dependencies useArticleContext: function
   */
  const {
    articleCollection,
    state: contextState,
    actions: contextActions,
    services: contextServices,
  } = useArticleContext();

  const { data: articlesData, pagination } = articleCollection;
  const { isLoadingArticles, capturedPayload, activeArticleId, pageLimit, searchKeyword } =
    contextState;
  const { setActiveArticleId, setCurrentKb, setSearchKeyword, setPageLimit } = contextActions;
  const { getArticles, createArticle, deleteArticle } = contextServices;

  const isEmptyArticles = useMemo(() => isEmpty(articlesData), [articlesData]);
  const isInitializedArticles = useMemo(() => articlesData !== undefined, [articlesData]);

  // Combined loading state for delayed operations
  const isDelayedOperationLoading = useMemo(
    () => isMovingLoading || isUpdatingLoading,
    [isMovingLoading, isUpdatingLoading]
  );

  // Helper function to map KBSort field to API sort field
  const mapSortFieldToAPI = (field: string) => {
    switch (field) {
      case 'createdDate':
        return 'createdAt';
      case 'updatedDate':
        return 'updatedAt';
      case 'title':
        return 'title';
      default:
        return 'createdAt';
    }
  };

  // Helper function to map KBSort order to API sort order
  const mapSortOrderToAPI = (order: string) => {
    switch (order) {
      case 'ascending':
        return 'asc';
      case 'descending':
        return 'desc';
      default:
        return 'desc';
    }
  };

  /**
   * Fetch Collection with Sorting Support
   * @dependencies kb: KnowledgeBase, getArticles: function
   * @returns {Promise<void>}
   */
  const fetchCollection = useCallback(
    (options?: {
      direction?: KnowledgeBaseDirectionQuery;
      cursor?: string;
      pageSize?: number;
      showLoader?: boolean;
      sortOption?: SortOption;
      query?: string;
    }) => {
      const {
        direction = KBDirectionQueryEnum.Forward,
        cursor = '',
        pageSize = pageLimit,
        showLoader = true,
        sortOption: customSortOption,
        query = searchKeyword,
      } = options || {};

      const currentSort = customSortOption || sortOption;

      // Validate page size before using it in API call
      const validatedPageSize = validatePageSizeFromString(pageSize);

      const params: IArticleRequestParams = {
        take: validatedPageSize,
        direction,
        cursor,
        query,
        sortBy: mapSortFieldToAPI(currentSort.field),
        sortOrder: mapSortOrderToAPI(currentSort.order),
        withBase: false,
        withUserInfo: false,
        withSchedule: true,
        baseId: kb?.id,
      };

      const result = getArticles(params, showLoader);
      return result;
    },
    [kb?.id, getArticles, searchKeyword, pageLimit, sortOption]
  );

  /**
   * Common helper to refresh articles with immediate and delayed fetch
   * Provides better UX with immediate feedback and data consistency
   */
  const refreshArticlesWithDelay = useCallback(
    (
      timeoutRef: React.MutableRefObject<NodeJS.Timeout | null>,
      setLoading: (loading: boolean) => void,
      delay: number
    ) => {
      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set loading state
      setLoading(true);

      // Delayed refresh for data consistency
      timeoutRef.current = setTimeout(async () => {
        try {
          await fetchCollection({
            direction: capturedPayload.direction,
            cursor: capturedPayload.cursor,
          });
        } catch (error) {
          console.error('Error in delayed article refresh:', error);
        } finally {
          setLoading(false);
        }
        timeoutRef.current = null;
      }, delay);
    },
    [fetchCollection, pageLimit, capturedPayload]
  );

  /**
   * Handle Search Articles
   * @param {string} keyword
   * @dependencies fetchCollection: function
   * @returns {void}
   */
  const handleSearchArticles = useCallback(
    async (keyword: string) => {
      setSearchKeyword(keyword);
      await fetchCollection({
        direction: KBDirectionQueryEnum.Forward,
        cursor: '',
        pageSize: pageLimit,
        showLoader: true,
        query: keyword,
      });
    },
    [fetchCollection, pageLimit, setSearchKeyword]
  );

  /**
   * Open Article Viewer
   * @param {Article} article
   * @dependencies articleViewerRef: ArticleViewerRef
   * @returns {void}
   */
  const openArticleViewer = useCallback(
    (articleId: string, baseId: string) => {
      if (!articleViewerRef.current || !articleId || !baseId) return;

      articleViewerRef.current?.setData?.({
        articleId,
        baseId,
      });
      articleViewerRef.current?.open?.();

      setActiveArticleId(articleId);
      contextActions.closeCreateNewArticleDrawer();
    },
    [articleViewerRef, kb, setActiveArticleId, contextActions]
  );

  /**
   * Handle Create Article
   * @param {PartOfArticle} data
   * @dependencies kb: KnowledgeBase, createArticle: function
   * @returns {void}
   */
  const handleCreateArticle = useCallback(
    async (data: PartOfArticle) => {
      if (!kb?.id) return;

      await createArticle(kb?.id, {
        title: data.title,
        content: data.content,
        contentRaw: data.contentRaw,
        status: 'draft',
        keywords: [],
      });
    },
    [kb?.id, createArticle]
  );

  /**
   * Handle Page Size Change
   * @param {number} pageSize
   * @dependencies setPageLimit: function, fetchCollection: function
   * @returns {void}
   */
  const handlePageSizeChange = useCallback(
    async (pageSize: number) => {
      // Validate the page size before using it
      const validatedPageSize = validatePageSizeFromString(pageSize);
      await fetchCollection({ pageSize: validatedPageSize, showLoader: false });
      setPageLimit(validatedPageSize);
    },
    [setPageLimit, fetchCollection]
  );

  /**
   * Handle Sort Change
   * @param {SortOption} newSortOption
   * @dependencies updateSortOption: function, fetchCollection: function
   * @returns {void}
   */
  const handleSortChange = useCallback(
    async (newSortOption: SortOption) => {
      updateSortOption(newSortOption);
      // Refetch collection with new sort applied
      await fetchCollection({
        showLoader: false,
        sortOption: newSortOption,
        cursor: '', // Reset cursor when sorting changes
        direction: KBDirectionQueryEnum.Forward, // Reset to forward when sorting changes
      });
    },
    [updateSortOption, fetchCollection]
  );

  /**
   * Handle Sort Reset
   * @dependencies clearSortOption: function, fetchCollection: function
   * @returns {void}
   */
  const handleSortReset = useCallback(async () => {
    clearSortOption();
    // Refetch collection with default sort applied
    const defaultSortOption = { field: 'createdDate', order: 'descending' } as SortOption;
    await fetchCollection({
      showLoader: false,
      sortOption: defaultSortOption,
      cursor: '', // Reset cursor when sorting changes
      direction: KBDirectionQueryEnum.Forward, // Reset to forward when sorting changes
    });
  }, [clearSortOption, fetchCollection]);

  /**
   * Handle Delete Article
   * @param {Article} article
   * @dependencies deleteArticle: function
   * @returns {void}
   */
  const handleDeleteArticle = useCallback(
    (article: Article) => {
      deleteArticle(article, () => {
        fetchCollection({
          direction: capturedPayload.direction,
          cursor: capturedPayload.cursor,
          showLoader: true, // Show loading state when refreshing after delete
        });
      });
    },
    [deleteArticle, fetchCollection, capturedPayload]
  );

  /**
   * Handle article import completion
   * Refreshes article list after import operations with immediate and delayed fetch
   */
  const onImportArticles = useCallback(() => {
    refreshArticlesWithDelay(
      importTimeoutRef,
      () => {}, // No loading state for import operations
      IMPORT_DELAY_GET_ARTICLES
    );
  }, [refreshArticlesWithDelay]);

  /**
   * Handle article updates from detail viewer (update/delete operations, and move operations)
   * Refreshes article list and closes viewer if shouldCloseViewer is true
   */
  const onUpdatedArticles = useCallback(
    ({
      shouldCloseViewer,
    }: {
      shouldCloseViewer?: boolean;
    }) => {
      // If this is a move operation, close the viewer and clear active article
      if (shouldCloseViewer) {
        articleViewerRef.current?.close?.();
        setActiveArticleId('');
      }

      // Refresh articles with immediate and delayed fetch
      refreshArticlesWithDelay(
        updatingTimeoutRef,
        setIsUpdatingLoading,
        UPDATING_DELAY_GET_ARTICLES
      );
    },
    [refreshArticlesWithDelay, pageLimit, setActiveArticleId]
  );

  /**
   * Article Collection Columns
   * @returns {JSX.Element[]}
   * @dependencies t: i18n function
   */
  const ArticleCollectionColumns: GridTableColumn[] = useMemo(
    () => [
      {
        title: t('articleCollection.name'),
        key: 'name',
        size: 8,
      },
      {
        title: t('articleCollection.lastUpdated'),
        key: 'lastUpdated',
      },
      {
        title: t('articleCollection.createdAt'),
        key: 'createdAt',
      },
      {
        title: '',
        key: 'actions',
        size: 'content',
      },
    ],
    [t]
  );

  /**
   * Article Collection Rows
   * @returns {JSX.Element[]}
   * @dependencies ArticleCollection
   */
  const ArticleCollectionRows: GridTableRow[] = useMemo(
    () =>
      articlesData
        ? articlesData.map((articleItem: Article) => ({
            props: {
              onClick: () => openArticleViewer(articleItem.id, articleItem.baseId),
              style: {
                cursor: 'pointer',
              },
              activated: articleItem.id === activeArticleId,
            },
            data: {
              id: articleItem.id,
              name: (
                <Flex align='center' justify='space-between' w='100%' gap='lg' pr='lg'>
                  <ArticleName article={articleItem} />
                  <Flex align='center' gap='md'>
                    <ArticleStatus
                      status={articleItem.status}
                      schedule={articleItem.schedule}
                      size={articleItem.schedule ? 'large' : 'medium'}
                    />
                    <ArticleFeedback article={articleItem} />
                  </Flex>
                </Flex>
              ),
              lastUpdated: formatDateTime(articleItem.updatedAt),
              createdAt: formatDateTime(articleItem.createdAt),
              actions: (
                <ArticleActions
                  onOpen={() => openArticleViewer(articleItem.id, articleItem.baseId)}
                  onDelete={() => handleDeleteArticle(articleItem)}
                  onMove={() =>
                    openArticleMovingModal(articleItem, ROOT_PATH, articleItem.baseId, () => {
                      // Close the current article viewer and refresh articles
                      articleViewerRef.current?.close?.();
                      setActiveArticleId('');
                      refreshArticlesWithDelay(
                        movingTimeoutRef,
                        setIsMovingLoading,
                        MOVING_DELAY_GET_ARTICLES
                      );
                    })
                  }
                />
              ),
            },
          }))
        : [],
    [articlesData, activeArticleId]
  );

  /**
   * Fetch Collection and set current kb to state
   * when mounted and kb id changed
   * Wait for sort state to be loaded from localStorage before fetching
   */
  useEffect(() => {
    if (isSortLoaded) {
      setCurrentKb(kb);
      fetchCollection();
    }
  }, [kb.id, isSortLoaded]);

  /**
   * Open Article Viewer when activeArticleId and kb.id changed
   * and activeArticleId is getting from URL
   * @dependencies activeArticleId: string, kb: KnowledgeBase, openArticleViewer: function
   */
  useEffect(() => {
    if (activeArticleId && kb.id) {
      openArticleViewer(activeArticleId, kb.id);
    }
  }, [kb.id]);

  /**
   * Cleanup timeouts when component unmounts
   */
  useEffect(() => {
    return () => {
      // Clear moving timeout on unmount
      if (movingTimeoutRef.current) {
        clearTimeout(movingTimeoutRef.current);
        movingTimeoutRef.current = null;
      }
      // Clear import timeout on unmount
      if (importTimeoutRef.current) {
        clearTimeout(importTimeoutRef.current);
        importTimeoutRef.current = null;
      }
      // Clear updating timeout on unmount
      if (updatingTimeoutRef.current) {
        clearTimeout(updatingTimeoutRef.current);
        updatingTimeoutRef.current = null;
      }
      // Reset loading states on unmount
      setIsMovingLoading(false);
      setIsUpdatingLoading(false);
    };
  }, []);

  return (
    <>
      <LoadingOverlay visible={isLoadingArticles || isDelayedOperationLoading} />
      <Box className={classes.toolbarSection}>
        <Flex gap='md' align='center'>
          <SearchBox
            className={classes.searchBox}
            placeholder={t('search', { ns: 'common' })}
            onSearch={handleSearchArticles}
          />
          {isKBSortingEnabled && (
            <KBSort
              sortOption={sortOption}
              onSortChange={handleSortChange}
              onReset={handleSortReset}
              size='sm'
              variant='default'
            />
          )}
        </Flex>
        <Box className={classes.toolbarRight}>
          <ExportJobByEntityButton
            entityId={kb.id}
            entityType={EntityType.BASE}
            onSuccess={fetchCollection}
          />
          {permArticle.canGenerate && <GeneratorButton baseId={kb.id} />}
          {permArticle.canCreate && !isEmptyArticles && (
            <ArticleCreateNew onImport={onImportArticles} />
          )}
        </Box>
      </Box>

      {!isEmptyArticles && (
        <>
          <Box className={classes.collectionListSection}>
            <GridTable columns={ArticleCollectionColumns} rows={ArticleCollectionRows} />
          </Box>
          {pagination && (
            <Box className={cx(classes.paginationWrapper, activeArticleId && classes.detailOpened)}>
              <CustomPagination
                direction={KBDirectionQueryEnum.Forward}
                pagination={pagination}
                onChange={(direction, cursor) =>
                  fetchCollection({
                    direction,
                    cursor,
                    pageSize: pageLimit,
                    showLoader: false,
                  })
                }
                pageSize={pageLimit}
                onPageSizeChange={handlePageSizeChange}
              />
            </Box>
          )}
        </>
      )}
      {isInitializedArticles && isEmptyArticles && (
        <ArticleEmpty onCreate={handleCreateArticle} onImport={onImportArticles} />
      )}

      <ArticleViewer
        ref={articleViewerRef}
        backTitle={(() => {
          const kbName = cleanBadCharacters(kb?.name) || t('unknownKBName', { ns: 'kb' });
          return t('backToKBDetail', { kbName, ns: 'common' });
        })()}
        onUpdated={onUpdatedArticles}
        onClosed={() => setActiveArticleId('')}
      />
    </>
  );
};

export default KBArticleCollection;
