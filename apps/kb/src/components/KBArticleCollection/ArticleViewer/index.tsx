import { Box } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { useTranslate } from '@tolgee/react';
import { forwardRef, useCallback, useImperativeHandle, useMemo, useState } from 'react';

import { EditorActions } from '@/components';
import { DEFAULT_DRAWER_OFFSET, DEFAULT_DRAWER_WIDTH } from '@/constants/ui';
import { useAppContext, useArticleDetailContext } from '@/contexts';
import withArticleDetailProviders from '@/hocs/withArticleDetailProviders';
import { useArticleViewerStyles } from '@/hooks/useArticleStyles';
import { DynamicDrawer } from '@resola-ai/ui';
import { ArticleDetail } from '../ArticleDetail';
import { ArticleViewerActions, type OnUpdatedParams } from '../ArticleViewerActions';

interface ArticleViewerProps {
  className?: string;
  backTitle?: string;
  onUpdated?: (params: OnUpdatedParams) => void;
  onClosed?: () => void;
}

interface ArticleViewerData {
  articleId: string | null;
  baseId: string | null;
}

export type ArticleViewerRef = HTMLDivElement & {
  open?: () => void;
  close?: () => void;
  setData: (data: ArticleViewerData) => void;
};

const ArticleViewer = forwardRef(function ArticleViewerHandler(
  { className = '', backTitle = '', onUpdated, onClosed }: ArticleViewerProps,
  ref
) {
  const { t } = useTranslate('article');
  const { classes, cx } = useArticleViewerStyles({
    drawerWidth: DEFAULT_DRAWER_WIDTH,
  });

  const [articleId, setArticleId] = useState<string | null>(null);
  const [baseId, setBaseId] = useState<string | null>(null);
  const [isFullView, setIsFullView] = useState<boolean>(false);
  const [editMode, setEditMode] = useState(false);

  const [opened, { open, close }] = useDisclosure(false);
  const { openConfirmModal, closeConfirmModal } = useAppContext();
  const {
    articleViewer: { topOfArticleRef, articleViewerScrollableRef },
    articleContentRef,
    isArticleChanged,
    setArticleSaving,
  } = useArticleDetailContext();

  /**
   * Resize Handler
   * @param {boolean} isMaximize
   * @returns {void}
   */
  const onResize = useCallback(
    (isMaximize: boolean) => {
      setIsFullView(isMaximize);
    },
    [setIsFullView]
  );

  /**
   * Confirm Save Changes
   * @param {() => void} onConfirm
   * @returns {void}
   * @dependencies openConfirmModal: function, closeConfirmModal: function
   */
  const checkUnsavedChanges = useCallback(
    (callback: () => void) => {
      if (editMode && isArticleChanged) {
        openConfirmModal({
          onConfirm: () => {
            callback();
            closeConfirmModal();
          },
          title: t('articleCollection.changedWarningTitle'),
          content: t('articleCollection.changedWarningDescription'),
          confirmText: t('articleCollection.discardChanges'),
          cancelText: t('articleCollection.goBack'),
        });
      } else {
        callback();
      }
    },
    [editMode, isArticleChanged, openConfirmModal, closeConfirmModal, t]
  );

  /**
   * Handle Edit Toggle
   * @returns {void}
   */
  const onEditToggle = useCallback(() => {
    checkUnsavedChanges(() => setEditMode((prevEditMode) => !prevEditMode));
  }, [checkUnsavedChanges, setEditMode]);

  /**
   * Closed Handler
   * @returns {void}
   */
  const onClosedHandler = useCallback(() => {
    checkUnsavedChanges(() => {
      close();
      onClosed?.();
    });
  }, [checkUnsavedChanges, onClosed, close]);

  /**
   * Drawer Header Right Action
   * @returns {React.ReactNode}
   * @dependencies editMode, ArticleFormEditorActionsActions, setArticleSaving, onEditToggle
   */
  const drawerHeaderRightAction = useMemo(
    () =>
      editMode ? (
        <EditorActions
          onSave={() => {
            setArticleSaving(true);
          }}
          onCancel={() => {
            onEditToggle();
          }}
        />
      ) : (
        <ArticleViewerActions
          onEditToggle={onEditToggle}
          onUpdated={(params) => {
            onUpdated?.(params);
            if (params.shouldCloseViewer) {
              onClosedHandler();
            }
          }}
          onDeleted={() => {
            onUpdated?.({
              showLoader: false,
            });
            onClosedHandler();
          }}
          onViewOriginal={(newArticleId: string) => {
            if (newArticleId) {
              setArticleId(newArticleId);
            }
          }}
        />
      ),
    [editMode, onEditToggle, setArticleSaving, onUpdated, onClosedHandler, setArticleId, setBaseId]
  );

  /**
   * Use Imperative Handle Ref Methods for Article Viewer
   * @returns {void}
   * @dependencies open: function, close: function, setData: function
   */
  useImperativeHandle(ref, () => ({
    open,
    close,
    setData: ({ articleId, baseId }: ArticleViewerData) => {
      setArticleId(articleId);
      setBaseId(baseId);
    },
  }));

  return (
    <DynamicDrawer
      contentRef={articleViewerScrollableRef}
      headerRef={topOfArticleRef}
      className={cx(classes.articleDrawer, className, isFullView ? classes.drawerFullView : '')}
      opened={opened}
      backTitle={backTitle}
      withOverlay={false}
      closeOnEscape={false}
      lockScroll={false}
      offset={DEFAULT_DRAWER_OFFSET}
      zIndex={100}
      rightAction={drawerHeaderRightAction}
      onResize={onResize}
      onClose={onClosedHandler}
    >
      <Box ref={articleContentRef} className={isFullView ? classes.bodyFullView : ''}>
        {articleId && baseId && (
          <ArticleDetail
            articleId={articleId}
            baseId={baseId}
            editMode={editMode}
            isFullView={isFullView}
            onUpdated={(params) => {
              onUpdated?.(params);
              if (params.shouldCloseViewer) {
                onClosedHandler();
              }
            }}
            setEditMode={setEditMode}
            onEditToggle={onEditToggle}
            onDeleted={() => {
              onUpdated?.({
                showLoader: false,
              });
              onClosedHandler();
            }}
          />
        )}
      </Box>
    </DynamicDrawer>
  );
});

export default withArticleDetailProviders(ArticleViewer);
