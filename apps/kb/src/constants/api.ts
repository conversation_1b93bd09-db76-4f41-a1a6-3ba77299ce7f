export const KB_API_PATH_V1 = '/v1/knowledgebases';
export const KB_API_PATH = '/v2/bases';
export const KB_DATASOURCES_PATH = '/v2/datasources';
export const KB_JOBS_PATH = '/v2/jobs';
export const KB_FOLDERS_PATH = '/v2/folders/';
export const KB_SEARCH_PATH = '/v2/search';
export const KB_RECENTLY_VIEWED_PATH = '/v2/recentViews/';
export const KB_ARTICLE_TEMPLATE_PATH = '/v2/articleTemplates';
export const KB_ARTICLES_PATH = '/v2/articles';
export const KB_ARTICLES_V20250606_PATH = '/v20250606/articles/';
export const KB_STATISTICS_PATH = '/v2/statistics';
export const KB_DOCUMENT_PATH = '/v2/documents';
export const KB_GEN_ARTICLE_PATH = '/v2/genarticle';
export const KB_EXPLORER_PATH = '/v2/explorer/';
export const KB_AUTOCOMPLETE_PATH = '/v2/autocompletion';
export const KB_CUSTOM_PROMPT_PATH = '/v2/customPrompts/';
export const KB_SCHEDULES_PATH = '/v2/schedules';
export const KB_DOCUMENT_API_PATH = (kbId: string) => `${KB_API_PATH}/${kbId}/documents/`;
export const KB_ARTICLE_API_PATH = (kbId: string) => `${KB_API_PATH}/${kbId}/articles/`;
export const KB_COMMENT_API_PATH = (kbId: string, articleId: string) =>
  `${KB_ARTICLE_API_PATH(kbId)}${articleId}/comments/`;

// DEFAULT PAGINATION LIMITS
export const DEFAULT_PAGINATION_LIMIT = 10;
export const DEFAULT_ARTICLES_RETRIEVE_LIMIT = 20;
export const DEFAULT_COMMENTS_RETRIEVE_LIMIT = 10;
export const DEFAULT_TEMPLATES_RETRIEVE_LIMIT = 20;
export const DEFAULT_TEMPLATE_FIELDS_RETRIEVE_LIMIT = 50;

// API ERROR CODES
export const API_RESPONSE_STATUS = {
  SUCCESS: 'success',
  ERROR: 'error',
};
export const API_ERROR_CODES = {
  ARTICLE_NOT_READY_TO_WRITE: 'ARTICLE_NOT_READY_TO_WRITE',
  CSV_ENCODING_DETECTION_FAILED: 'CSV_ENCODING_DETECTION_FAILED',
};

export const TEMP_KB_PAYLOAD = {
  parentDirId: '',
  documentId: '',
  articleId: '',
  datasourceId: '',
};
