export const DEFAULT_JOB_RETRIEVE_LIMIT = 20;

/**
 * Constants for KBJobs tab values
 */
export const JOB_TAB_VALUES = {
  ALL: 'all',
  GENERATION: 'generation',
  EXPORT: 'export',
} as const;
export const ARTICLE_GENERATOR_PROMPT_MIN_ROWS = 5;
export const ARTICLE_GENERATOR_PROMPT_MAX_ROWS = 10;
export const PROMPT_VIEWER_MIN_ROWS = 8;
export const PROMPT_VIEWER_MAX_ROWS = 15;
export const ARTICLE_GENERATOR_PROMPT_MAX_LENGTH = 2000;
export const PROMPT_LIST_MAX_LENGTH = 10;
export const MAX_SELECTABLE_ARTICLES_IN_KB_JOB = 100;
export const DEFAULT_ARTICLE_GENERATOR_PROMPT_TITLE = 'プロンプト';
export const DEFAULT_ARTICLE_GENERATOR_PROMPT_CONTENT = `#依頼
あなたは優秀なシナリオライターです。
段階的に考えて以下の#内容から、精度の高い質問(Q)と回答(A)のリストを日本語で作成してください。

#手順
1.#制約条件、#参照を出力に反映させて、文章からFAQを網羅的に生成する
2.同じ項目から複数の類似FAQが生成されている場合は、可能な限り1つのFAQとして類似FAQそれぞれの内容を網羅させたQとAに修正する
3.生成されたすべてのQを対象に、文法構造を可能な限り、統一するように修正する
4.すべてFAQの言葉で表記揺れがないかをチェックし、必要に応じて修正する
5.#制約条件、#参照を再度確認して、出力に反映させる

#制約条件
・FAQは原則、一問一答であり、一意の文にする
・FAQはです・ます調にする
・Qを生成する際に、#参照の1,2,3を反映させて出力する
・Aは文章中の言葉のみを使用し、推定や想像をしてはならない（推測や推定や想像されたAはFAQを探すユーザーに甚大な不利益を与えます）
・Aは必ず、Qに対する結論を先に書き、必要最小限の情報だけを記載する
・ユーザーが最も困っている問題に関する質問を優先して１００個を超えないように出力する

#参照
1.Qは「問題+解決案」の文体にする：FAQを探すユーザーはなんらかの「問題」があり、「解決」を求めているため
  ※修正例：
    -ログインできない → ログインできないので、新しいパスワードの設定方法を教えて
    -新規会員登録をしたい → 新規会員登録したいので、登録条件を教えて

2.Qは下記例示に類似するあいまいな表現はしない：FAQを探すユーザーに誤解を与えないため
  ※例示：「どこですか？」「なせですか？」「どのようにすればよいですか？」「いつですか？」「どれくらいですか？」「どれですか？」「だれですか？」「いくらですか？」
  ※修正例：
    -ログインはどこからしますか？ → ログインURLを教えて
    -ゴミはどのように捨てればよいですか？ → 粗大ゴミの回収を依頼するWebサイトを教えて

3.Qは可能な限り、下記例示に類似するYes/Noクエスチョンにしない：Qに解決案がなく、ユーザーが知りたい内容が気づけないため
  ※例示：「できますか？」「可能ですか？」「ありますか？」
  ※修正例：
    -契約は延長できますか？ → 契約の延長方法を教えて
    -搬入は頼めますか？ → 搬入依頼の方法を教えて`;
