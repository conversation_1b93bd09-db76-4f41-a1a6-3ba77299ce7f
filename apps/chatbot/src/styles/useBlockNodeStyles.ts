import { TITLE_HEIGHT, TITLE_WIDTH, WIDTH_NODE_BLOCK } from '@/constants/flow';
import type { TColorPaletteSetConfig } from '@/types';
import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';

type UseBlockNodeStylesProps = {
  isSelected: boolean;
  colorConfig: TColorPaletteSetConfig;
  isStepInvalid?: boolean;
};

export const useBlockNodeStyles = createStyles(
  (theme, { isSelected, isStepInvalid = false, colorConfig }: UseBlockNodeStylesProps) => ({
    container: {
      position: 'relative',
      borderRadius: rem(8),
      padding: 8,
      fontSize: rem(12),
      backgroundColor: isStepInvalid ? theme.colors.redSalsa[0] : colorConfig.backgroundColor,
      display: 'flex',
      flexDirection: 'column',
      gap: 8,
    },
    title: {
      height: rem(TITLE_HEIGHT),
      width: 'max-content',
      maxWidth: rem(WIDTH_NODE_BLOCK - 16),
      padding: `0 ${rem(9)}`,
      fontSize: 'small',
      fontWeight: 500,
      cursor: 'text',
      zIndex: isSelected ? 1001 : 1,
      transition: 'background-color 0.2s ease-in-out',
      borderRadius: rem(4),
      '&:hover': {
        backgroundColor: colorConfig.color,
      },
    },
    input: {
      '.mantine-Input-input': {
        height: rem(TITLE_HEIGHT),
        width: rem(TITLE_WIDTH),
        minHeight: rem(TITLE_HEIGHT),
        paddingLeft: rem(8),
        fontSize: 'small',
        borderRadius: rem(4),
      },
    },
    handle: {
      position: 'absolute',
      width: '100%',
      height: '100%',
      left: 0,
      top: 0,
    },
    content: {
      position: 'relative',
      backgroundColor: theme.colors.silverFox[0],
      borderRadius: rem(8),
      gap: rem(9),
      display: 'flex',
      alignItems: 'center',
      fontSize: rem(16),
      padding: `0 ${rem(12)}`,
    },
    richMenuIcon: {
      color: theme.colors.gray[6],
      opacity: 0,
      visibility: 'hidden',
      transition: 'opacity 0.3s ease-in-out',
    },
    richMenuIconActive: {
      opacity: 1,
      visibility: 'visible',
    },
    richMenuEnabled: {
      color: theme.colors.decaNavy[6],
    },
  })
);
