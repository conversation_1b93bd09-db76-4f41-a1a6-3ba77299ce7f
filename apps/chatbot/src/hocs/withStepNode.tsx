import { NodeMenuToolbar } from '@/components/FlowEditor/components';
import { COLOR_PALETTE_KEYS, COLOR_PALETTE_SET, SHOW_NODE_MENU_TOOLBAR } from '@/constants/flow';
import { useFlowContext } from '@/contexts/FlowContext';
import type { ENodeType, TColorPaletteSetConfig, TReactFlowNodeProps } from '@/types';
import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { get } from 'lodash';
import type { ComponentType } from 'react';

type UseStylesProps = {
  colorConfig: TColorPaletteSetConfig;
  hasBorder: boolean;
};

const useStyles = createStyles((theme, { colorConfig, hasBorder }: UseStylesProps) => ({
  wrapped: {
    border: `${rem(1)} solid ${hasBorder ? colorConfig.color : 'transparent'}`,
    transition: 'box-shadow 0.2s ease-in-out',
  },
  active: {
    border: `${rem(1)} solid ${colorConfig.lineSelected} !important`,
  },
  activeHover: {
    ':hover': {
      boxShadow: `0 0 ${rem(4)} ${rem(2)} ${theme.colors.decaLight[3]} !important`,
    },
  },
  activeConnectingHover: {
    ':hover': {
      border: `${rem(1)} solid ${colorConfig.lineSelected} !important`,
    },
  },
  error: {
    border: `${rem(1)} solid ${theme.colors.decaRed[5]} !important`,
  },
}));

function withStepNode(
  WrappedComponent: ComponentType<TReactFlowNodeProps<ENodeType>>
): ComponentType<TReactFlowNodeProps<ENodeType>> {
  return function WithStepNode(props: TReactFlowNodeProps<ENodeType>) {
    const { id, type } = props;
    const { classes, cx } = useStyles({
      colorConfig:
        COLOR_PALETTE_SET[get(props, 'data.properties.setting.color', COLOR_PALETTE_KEYS[0])],
      hasBorder: SHOW_NODE_MENU_TOOLBAR.includes(type as ENodeType),
    });
    const { selectedNode, isConnecting, mapFlowErrorsRef, selectedNodes } = useFlowContext();

    return (
      <>
        {SHOW_NODE_MENU_TOOLBAR.includes(type as ENodeType) && <NodeMenuToolbar {...props} />}
        <WrappedComponent
          {...{
            ...props,
            className: cx(
              classes.wrapped,
              selectedNode?.id === id || selectedNodes.some((node) => node.id === id)
                ? classes.active
                : '',
              isConnecting ? classes.activeConnectingHover : classes.activeHover,
              mapFlowErrorsRef.current?.get(id)?.length ? classes.error : '',
              props.className
            ),
          }}
        />
      </>
    );
  };
}

export default withStepNode;
