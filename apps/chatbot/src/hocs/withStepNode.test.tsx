import { ENodeType, type TReactFlowNodeProps } from '@/types';
import { cleanup, render, screen } from '@testing-library/react';
import type React from 'react';
import type { ComponentType } from 'react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Hoist mocks to ensure they're available before any imports
const mockUseFlowContext = vi.hoisted(() => vi.fn());

// Mock styles to return deterministic class names and a simple cx combiner
// Additionally, execute provided style factory with a minimal theme to cover style lines.
vi.mock('@mantine/emotion', () => {
  return {
    createStyles: (factory?: (theme: any, props: any) => any) => {
      // Execute the factory function to ensure all branches are covered
      if (typeof factory === 'function') {
        const theme = {
          colors: {
            decaNavy: Array.from({ length: 10 }, (_, i) => `decaNavy-${i}`),
            decaRed: Array.from({ length: 10 }, (_, i) => `decaRed-${i}`),
            decaLight: Array.from({ length: 10 }, (_, i) => `decaLight-${i}`),
          },
        };
        try {
          // Execute with both hasBorder true and false to cover all branches
          factory(theme, { colorConfig: { color: '#B2B7E2' }, hasBorder: true });
          factory(theme, { colorConfig: { color: '#B2B7E2' }, hasBorder: false });
        } catch {
          // ignore style execution errors in tests
        }
      }
      return () => ({
        classes: {
          wrapped: 'wrapped',
          active: 'active',
          activeHover: 'activeHover',
          activeConnectingHover: 'activeConnectingHover',
          error: 'error',
        },
        cx: (...args: Array<string | undefined | false>) => args.filter(Boolean).join(' '),
      });
    },
  };
});

// Mock NodeMenuToolbar component
vi.mock('@/components/FlowEditor/components/NodeMenuToolbar', () => ({
  default: ({ id, type }: any) => (
    <div data-testid='node-menu-toolbar' data-node-id={id} data-node-type={type} />
  ),
}));

// Mock constants
vi.mock('@/constants/flow', () => ({
  COLOR_PALETTE_KEYS: ['#B2B7E2', '#94C4FA', '#E0C6D9'],
  COLOR_PALETTE_SET: {
    '#B2B7E2': {
      color: '#B2B7E2',
      backgroundColor: '#F2F2F6',
      lineSelected: '#1D2088',
    },
    '#94C4FA': {
      color: '#94C4FA',
      backgroundColor: '#E1EFFE',
      lineSelected: '#1F84F4',
    },
    '#E0C6D9': {
      color: '#E0C6D9',
      backgroundColor: '#F8F2F6',
      lineSelected: '#A81B8D',
    },
  },
  SHOW_NODE_MENU_TOOLBAR: ['block', 'livechat', 'action'],
}));

// Mock FlowContext
vi.mock('@/contexts/FlowContext', () => ({
  useFlowContext: () => mockUseFlowContext(),
}));

// Mock the nodes that import withStepNode to prevent circular dependency issues
vi.mock('@/components/FlowEditor/nodes', () => ({
  ActionNode: vi.fn(),
  ActionNodeSettings: vi.fn(),
  APINode: vi.fn(),
  APINodeSettings: vi.fn(),
  BlockNode: vi.fn(),
  BlockNodeSettings: vi.fn(),
  ButtonNode: vi.fn(),
  ButtonNodeSettings: vi.fn(),
  CaptureInputNode: vi.fn(),
  CaptureInputNodeSettings: vi.fn(),
  CaptureIntentNode: vi.fn(),
  CaptureIntentNodeSettings: vi.fn(),
  CarouselNode: vi.fn(),
  CarouselNodeSettings: vi.fn(),
  ConditionNode: vi.fn(),
  ConditionNodeSettings: vi.fn(),
  CustomTemplateNode: vi.fn(),
  CustomTemplateNodeSettings: vi.fn(),
  DynamicNode: vi.fn(),
  DynamicNodeSettings: vi.fn(),
  EmailNode: vi.fn(),
  EmailNodeSettings: vi.fn(),
  EndNode: vi.fn(),
  EntityNode: vi.fn(),
  EntityNodeSettings: vi.fn(),
  FinishNode: vi.fn(),
  FormNode: vi.fn(),
  FormNodeSettings: vi.fn(),
  GaiNode: vi.fn(),
  GaiNodeSettings: vi.fn(),
  IntentNode: vi.fn(),
  IntentNodeSettings: vi.fn(),
  LivechatNode: vi.fn(),
  LivechatNodeSettings: vi.fn(),
  MenuNode: vi.fn(),
  QnANode: vi.fn(),
  QnANodeSettings: vi.fn(),
  ScriptNode: vi.fn(),
  ScriptNodeSettings: vi.fn(),
  SetVariableNode: vi.fn(),
  SetVariableNodeSettings: vi.fn(),
  StartNode: vi.fn(),
  TableNode: vi.fn(),
  TableNodeSettings: vi.fn(),
  TextNode: vi.fn(),
  TextNodeSettings: vi.fn(),
}));

// Import withStepNode after mocks are defined
import withStepNode from './withStepNode';

function createWrapped(): ComponentType<TReactFlowNodeProps<ENodeType>> {
  const Wrapped: React.FC<TReactFlowNodeProps<ENodeType>> = ({ id, className }) => (
    <div data-testid='node' data-id={id} className={className} />
  );
  return Wrapped;
}

function renderWithContext(ctx: any, props?: Partial<TReactFlowNodeProps<ENodeType>>) {
  mockUseFlowContext.mockReturnValue({
    selectedNode: null,
    selectedNodes: [],
    isConnecting: false,
    mapFlowErrorsRef: { current: new Map<string, any[]>() },
    ...ctx,
  });

  const Enhanced = withStepNode(createWrapped());

  const defaultProps: TReactFlowNodeProps<ENodeType> = {
    id: 'n1' as any,
    data: { properties: {} as any },
    selected: false,
    dragging: false,
    xPos: 0,
    yPos: 0,
    zIndex: 0,
    isConnectable: false,
    isSelectable: true,
    isDraggable: true,
    type: 'default',
    scale: 1,
    width: 100,
    height: 50,
    positionAbsoluteX: 0,
    positionAbsoluteY: 0,
    internalsSymbol: Symbol('internals') as any,
    ...((props as any) || {}),
  } as any;

  return render(<Enhanced {...defaultProps} />);
}

describe('withStepNode', () => {
  beforeEach(() => {
    mockUseFlowContext.mockReset();
  });
  afterEach(() => {
    cleanup();
  });

  describe('Basic functionality', () => {
    it('applies base and hover classes by default', () => {
      renderWithContext({});
      const el = screen.getByTestId('node');
      expect(el).toHaveClass('wrapped');
      expect(el).toHaveClass('activeHover');
      expect(el).not.toHaveClass('active');
      expect(el).not.toHaveClass('error');
      expect(el).not.toHaveClass('activeConnectingHover');
    });

    it('applies active class when node is selected', () => {
      renderWithContext({ selectedNode: { id: 'n1' } });
      const el = screen.getByTestId('node');
      expect(el).toHaveClass('active');
    });

    it('applies active class when node is in multi-selection list', () => {
      renderWithContext({ selectedNodes: [{ id: 'n1' }] });
      const el = screen.getByTestId('node');
      expect(el).toHaveClass('active');
    });

    it('uses connecting hover class when connecting', () => {
      renderWithContext({ isConnecting: true });
      const el = screen.getByTestId('node');
      expect(el).toHaveClass('activeConnectingHover');
      expect(el).not.toHaveClass('activeHover');
    });

    it('applies error class when flow has errors for node', () => {
      const map = new Map<string, any[]>();
      map.set('n1', [{}]);
      renderWithContext({ mapFlowErrorsRef: { current: map } });
      const el = screen.getByTestId('node');
      expect(el).toHaveClass('error');
    });
  });

  describe('NodeMenuToolbar rendering', () => {
    it('renders NodeMenuToolbar for Block node type', () => {
      renderWithContext({}, { type: ENodeType.Block });
      expect(screen.getByTestId('node-menu-toolbar')).toBeInTheDocument();
      expect(screen.getByTestId('node-menu-toolbar')).toHaveAttribute(
        'data-node-type',
        ENodeType.Block
      );
    });

    it('renders NodeMenuToolbar for Livechat node type', () => {
      renderWithContext({}, { type: ENodeType.Livechat });
      expect(screen.getByTestId('node-menu-toolbar')).toBeInTheDocument();
      expect(screen.getByTestId('node-menu-toolbar')).toHaveAttribute(
        'data-node-type',
        ENodeType.Livechat
      );
    });

    it('does not render NodeMenuToolbar for other node types', () => {
      renderWithContext({}, { type: ENodeType.Text });
      expect(screen.queryByTestId('node-menu-toolbar')).not.toBeInTheDocument();
    });

    it('does not render NodeMenuToolbar for Start node type', () => {
      renderWithContext({}, { type: ENodeType.Start });
      expect(screen.queryByTestId('node-menu-toolbar')).not.toBeInTheDocument();
    });
  });

  describe('Color configuration', () => {
    it('uses default color palette when no color is set', () => {
      renderWithContext({}, { data: { properties: {} } });
      const el = screen.getByTestId('node');
      expect(el).toHaveClass('wrapped');
    });

    it('uses specified color palette from data properties', () => {
      renderWithContext(
        {},
        {
          data: {
            properties: {
              setting: {
                color: '#94C4FA',
              },
            },
          },
        }
      );
      const el = screen.getByTestId('node');
      expect(el).toHaveClass('wrapped');
    });

    it('handles missing setting object in data properties', () => {
      renderWithContext(
        {},
        {
          data: {
            properties: {},
          },
        }
      );
      const el = screen.getByTestId('node');
      expect(el).toHaveClass('wrapped');
    });

    it('handles missing color property in setting', () => {
      renderWithContext(
        {},
        {
          data: {
            properties: {
              setting: {},
            },
          },
        }
      );
      const el = screen.getByTestId('node');
      expect(el).toHaveClass('wrapped');
    });
  });

  describe('Border logic', () => {
    it('applies border for Block node type', () => {
      renderWithContext({}, { type: ENodeType.Block });
      const el = screen.getByTestId('node');
      expect(el).toHaveClass('wrapped');
    });

    it('applies border for Livechat node type', () => {
      renderWithContext({}, { type: ENodeType.Livechat });
      const el = screen.getByTestId('node');
      expect(el).toHaveClass('wrapped');
    });

    it('does not apply border for other node types', () => {
      renderWithContext({}, { type: ENodeType.Text });
      const el = screen.getByTestId('node');
      expect(el).toHaveClass('wrapped');
    });

    it('covers hasBorder false branch in styles', () => {
      // Test with a node type that should not have border (hasBorder = false)
      // This ensures we cover the 'transparent' branch in the ternary operator
      renderWithContext({}, { type: ENodeType.Start });
      const el = screen.getByTestId('node');
      expect(el).toHaveClass('wrapped');
    });

    it('covers hasBorder true branch in styles', () => {
      // Test with a node type that should have border (hasBorder = true)
      // This ensures we cover the 'colorConfig.color' branch in the ternary operator
      renderWithContext({}, { type: ENodeType.Block });
      const el = screen.getByTestId('node');
      expect(el).toHaveClass('wrapped');
    });
  });

  describe('Complex state combinations', () => {
    it('applies active and error classes together', () => {
      const map = new Map<string, any[]>();
      map.set('n1', [{}]);
      renderWithContext({
        selectedNode: { id: 'n1' },
        mapFlowErrorsRef: { current: map },
      });
      const el = screen.getByTestId('node');
      expect(el).toHaveClass('active');
      expect(el).toHaveClass('error');
    });

    it('applies active class from selectedNodes and error class together', () => {
      const map = new Map<string, any[]>();
      map.set('n1', [{}]);
      renderWithContext({
        selectedNodes: [{ id: 'n1' }],
        mapFlowErrorsRef: { current: map },
      });
      const el = screen.getByTestId('node');
      expect(el).toHaveClass('active');
      expect(el).toHaveClass('error');
    });

    it('applies connecting hover and error classes together', () => {
      const map = new Map<string, any[]>();
      map.set('n1', [{}]);
      renderWithContext({
        isConnecting: true,
        mapFlowErrorsRef: { current: map },
      });
      const el = screen.getByTestId('node');
      expect(el).toHaveClass('activeConnectingHover');
      expect(el).toHaveClass('error');
      expect(el).not.toHaveClass('activeHover');
    });

    it('applies all classes when node is selected, connecting, and has errors', () => {
      const map = new Map<string, any[]>();
      map.set('n1', [{}]);
      renderWithContext({
        selectedNode: { id: 'n1' },
        isConnecting: true,
        mapFlowErrorsRef: { current: map },
      });
      const el = screen.getByTestId('node');
      expect(el).toHaveClass('active');
      expect(el).toHaveClass('activeConnectingHover');
      expect(el).toHaveClass('error');
      expect(el).not.toHaveClass('activeHover');
    });
  });

  describe('Edge cases', () => {
    it('handles empty selectedNodes array', () => {
      renderWithContext({ selectedNodes: [] });
      const el = screen.getByTestId('node');
      expect(el).not.toHaveClass('active');
    });

    it('handles null selectedNode', () => {
      renderWithContext({ selectedNode: null });
      const el = screen.getByTestId('node');
      expect(el).not.toHaveClass('active');
    });

    it('handles undefined selectedNode', () => {
      renderWithContext({ selectedNode: undefined });
      const el = screen.getByTestId('node');
      expect(el).not.toHaveClass('active');
    });

    it('handles empty error map', () => {
      const map = new Map<string, any[]>();
      renderWithContext({ mapFlowErrorsRef: { current: map } });
      const el = screen.getByTestId('node');
      expect(el).not.toHaveClass('error');
    });

    it('handles null error map', () => {
      renderWithContext({ mapFlowErrorsRef: { current: null } });
      const el = screen.getByTestId('node');
      expect(el).not.toHaveClass('error');
    });

    it('handles undefined error map', () => {
      renderWithContext({ mapFlowErrorsRef: { current: undefined } });
      const el = screen.getByTestId('node');
      expect(el).not.toHaveClass('error');
    });

    it('handles errors for different node id', () => {
      const map = new Map<string, any[]>();
      map.set('different-node-id', [{}]);
      renderWithContext({ mapFlowErrorsRef: { current: map } });
      const el = screen.getByTestId('node');
      expect(el).not.toHaveClass('error');
    });

    it('handles empty error array for node', () => {
      const map = new Map<string, any[]>();
      map.set('n1', []);
      renderWithContext({ mapFlowErrorsRef: { current: map } });
      const el = screen.getByTestId('node');
      expect(el).not.toHaveClass('error');
    });
  });

  describe('Props passing', () => {
    it('passes all props to wrapped component', () => {
      mockUseFlowContext.mockReturnValue({
        selectedNode: null,
        selectedNodes: [],
        isConnecting: false,
        mapFlowErrorsRef: { current: new Map<string, any[]>() },
      });

      const customProps = {
        id: 'custom-id',
        type: ENodeType.Text,
        data: { properties: { custom: 'value' } },
        customProp: 'test',
      };

      const Enhanced = withStepNode(createWrapped());
      render(<Enhanced {...(customProps as any)} />);

      const el = screen.getByTestId('node');
      expect(el).toHaveAttribute('data-id', 'custom-id');
    });

    it('merges className prop correctly', () => {
      mockUseFlowContext.mockReturnValue({
        selectedNode: null,
        selectedNodes: [],
        isConnecting: false,
        mapFlowErrorsRef: { current: new Map<string, any[]>() },
      });

      const Enhanced = withStepNode(createWrapped());
      render(
        <Enhanced
          {...({
            id: 'n1',
            type: ENodeType.Text,
            data: { properties: {} },
            className: 'custom-class',
          } as any)}
        />
      );

      const el = screen.getByTestId('node');
      expect(el).toHaveClass('custom-class');
      expect(el).toHaveClass('wrapped');
    });
  });
});
