import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the dependencies
vi.mock('@/constants/flow', () => ({
  STEP_NODES: ['text', 'email', 'buttons', 'carousel'],
  SINGLE_NODES: ['start', 'end'],
  BASIC_NODES: ['intent', 'script', 'condition'],
  WIDTH_NODE_BLOCK: 300,
  WIDTH_STEP: 280,
  ACTION_NODE_LARGE_WIDTH: 200,
  ACTION_NODE_SMALL_WIDTH: 100,
  BUILT_IN_PORT_ELSE: 'built-in.else',
  BUILT_IN_PORT_NEXT: 'built-in.next',
  BUILT_IN_PORT_FAILURE: 'built-in.failure',
  BUILT_IN_PORT_FINISH: 'built-in.finish',
  BUILT_IN_PORT_SUCCESS: 'built-in.success',
  COLOR_PALETTE_KEYS: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'],
  NEED_REVALIDATE_DATA_NODES: ['buttons', 'carousel', 'choice', 'condition', 'script', 'dynamic'],
}));

vi.mock('@/types', () => ({
  BUILT_IN_PORT: 'built-in',
  ENodeType: {
    Block: 'block',
    Action: 'action',
    Text: 'text',
    Email: 'email',
    Buttons: 'buttons',
    Carousel: 'carousel',
    Start: 'start',
    End: 'end',
    Intent: 'intent',
    Script: 'script',
    Condition: 'condition',
    Menu: 'menu',
  },
  PORT_TYPE: {
    action: 'action',
    menu: 'menu',
    else: 'else',
    next: 'next',
    failure: 'failure',
    finish: 'finish',
  },
  EdgeTypes: {
    Action: 'action-edge',
    Menu: 'menu-edge',
    Goto: 'goto-edge',
  },
  DYNAMIC_PORT: 'dynamic',
}));

vi.mock('./shared', () => ({
  getStepsInstanceByParent: vi.fn(() => ({
    getIds: vi.fn(() => ['step1', 'step2']),
  })),
  getActionNodesInDecaData: vi.fn(),
  isLargeWidthAction: vi.fn((type) => type === 'url' || type === 'variable'),
  isEnabledActionHandler: vi.fn((type) => type === 'url' || type === 'variable'),
  getKeyDynamicData: vi.fn((nodeType) => {
    const mapping = {
      buttons: 'buttons',
      carousel: 'cards',
      choice: 'choices',
      condition: 'expressions',
      script: 'paths',
      dynamic: 'buttons',
    };
    return mapping[nodeType];
  }),
  getStepPositionY: vi.fn(({ index }) => 30 + index * 100),
}));

import { BUILT_IN_PORT, DYNAMIC_PORT, ENodeType, EdgeTypes, PORT_TYPE } from '@/types';
// Import after mocking
import {
  convertActionNodesFromDecaDataToFlowData,
  convertApiDataToReactFlowData,
  convertClientFlowToApiFlow,
} from './convertor';
import {
  getActionNodesInDecaData,
  getKeyDynamicData,
  getStepPositionY,
  isLargeWidthAction,
} from './shared';

describe('Flow Convertor Utils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('convertClientFlowToApiFlow', () => {
    it('should convert client flow to API flow format', () => {
      const mockNodes = [
        {
          id: 'node1',
          type: ENodeType.Start,
          position: { x: 100, y: 100 },
          data: { properties: { name: 'Start Node' } },
        },
        {
          id: 'node2',
          type: ENodeType.Block,
          position: { x: 200, y: 200 },
          parentId: undefined,
          data: { properties: { name: 'Block Node' } },
        },
      ];

      const mockEdges = [
        {
          id: 'edge1',
          source: 'node1',
          target: 'node2',
          sourceHandle: 'built-in.next',
        },
      ];

      const result = convertClientFlowToApiFlow(mockNodes, mockEdges);

      expect(result).toEqual({
        node1: {
          ID: 'node1',
          type: ENodeType.Start,
          parentId: undefined,
          portId: undefined,
          coords: [100, 100],
          data: {
            name: 'Start Node',
            ports: {
              [BUILT_IN_PORT]: {
                next: {
                  id: 'edge1',
                  type: undefined,
                  target: 'node2',
                  setting: {
                    color: undefined,
                  },
                },
              },
            },
          },
        },
        node2: {
          ID: 'node2',
          type: ENodeType.Block,
          parentId: undefined,
          portId: undefined,
          coords: [200, 200],
          data: {
            name: 'Block Node',
            steps: ['step1', 'step2'],
            ports: { [BUILT_IN_PORT]: {} },
          },
        },
      });
    });

    it('should handle nodes without edges', () => {
      const mockNodes = [
        {
          id: 'node1',
          type: ENodeType.Start,
          position: { x: 100, y: 100 },
          data: { properties: {} },
        },
      ];

      const result = convertClientFlowToApiFlow(mockNodes, []);

      expect(result.node1.data.ports).toEqual({ [BUILT_IN_PORT]: {} });
    });

    it('should handle action nodes with portId', () => {
      const mockNodes = [
        {
          id: 'action1',
          type: ENodeType.Action,
          position: { x: 150, y: 150 },
          parentId: 'node1',
          data: { properties: { portId: 'port1', actionType: 'url' } },
        },
      ];

      const result = convertClientFlowToApiFlow(mockNodes, []);

      expect(result.action1).toEqual({
        ID: 'action1',
        type: ENodeType.Action,
        parentId: 'node1',
        portId: 'port1',
        coords: [150, 150],
        data: {
          actionType: 'url',
          ports: { [BUILT_IN_PORT]: {} },
        },
      });
    });

    it('should handle edges without sourceHandle', () => {
      const mockNodes = [
        {
          id: 'node1',
          type: ENodeType.Start,
          position: { x: 100, y: 100 },
          data: { properties: {} },
        },
      ];

      const mockEdges = [
        {
          id: 'edge1',
          source: 'node1',
          target: 'node2',
          sourceHandle: null,
        },
      ];

      const result = convertClientFlowToApiFlow(mockNodes, mockEdges);

      expect(result.node1.data.ports).toEqual({ [BUILT_IN_PORT]: {} });
    });

    it('should skip Menu nodes', () => {
      const mockNodes = [
        {
          id: 'node1',
          type: ENodeType.Start,
          position: { x: 100, y: 100 },
          data: { properties: { name: 'Start Node' } },
        },
        {
          id: 'menu1',
          type: ENodeType.Menu,
          position: { x: 200, y: 200 },
          data: { properties: { name: 'Menu Node' } },
        },
        {
          id: 'node2',
          type: ENodeType.End,
          position: { x: 300, y: 300 },
          data: { properties: { name: 'End Node' } },
        },
      ];

      const result = convertClientFlowToApiFlow(mockNodes, []);

      // Should only include non-Menu nodes
      expect(result).toHaveProperty('node1');
      expect(result).toHaveProperty('node2');
      expect(result).not.toHaveProperty('menu1');
      expect(Object.keys(result)).toHaveLength(2);
    });

    it('should handle edges with color settings', () => {
      const mockNodes = [
        {
          id: 'node1',
          type: ENodeType.Start,
          position: { x: 100, y: 100 },
          data: { properties: { name: 'Start Node' } },
        },
        {
          id: 'node2',
          type: ENodeType.End,
          position: { x: 200, y: 200 },
          data: { properties: { name: 'End Node' } },
        },
      ];

      const mockEdges = [
        {
          id: 'edge1',
          source: 'node1',
          target: 'node2',
          sourceHandle: 'built-in.next',
          type: EdgeTypes.Goto,
          data: {
            setting: {
              color: '#FF6B6B',
            },
            hover: false,
            type: EdgeTypes.Goto,
          },
        },
      ];

      const result = convertClientFlowToApiFlow(mockNodes, mockEdges);

      expect(result.node1.data.ports[BUILT_IN_PORT].next).toEqual({
        id: 'edge1',
        type: EdgeTypes.Goto,
        target: 'node2',
        setting: {
          color: '#FF6B6B',
        },
      });
    });

    it('should handle edges without color settings', () => {
      const mockNodes = [
        {
          id: 'node1',
          type: ENodeType.Start,
          position: { x: 100, y: 100 },
          data: { properties: { name: 'Start Node' } },
        },
        {
          id: 'node2',
          type: ENodeType.End,
          position: { x: 200, y: 200 },
          data: { properties: { name: 'End Node' } },
        },
      ];

      const mockEdges = [
        {
          id: 'edge1',
          source: 'node1',
          target: 'node2',
          sourceHandle: 'built-in.next',
          type: EdgeTypes.Goto,
          data: {
            setting: {
              color: '',
            },
            hover: false,
            type: EdgeTypes.Goto,
          },
        },
      ];

      const result = convertClientFlowToApiFlow(mockNodes, mockEdges);

      expect(result.node1.data.ports[BUILT_IN_PORT].next).toEqual({
        id: 'edge1',
        type: EdgeTypes.Goto,
        target: 'node2',
        setting: {
          color: '',
        },
      });
    });

    it('should handle edges without data property', () => {
      const mockNodes = [
        {
          id: 'node1',
          type: ENodeType.Start,
          position: { x: 100, y: 100 },
          data: { properties: { name: 'Start Node' } },
        },
        {
          id: 'node2',
          type: ENodeType.End,
          position: { x: 200, y: 200 },
          data: { properties: { name: 'End Node' } },
        },
      ];

      const mockEdges = [
        {
          id: 'edge1',
          source: 'node1',
          target: 'node2',
          sourceHandle: 'built-in.next',
          type: EdgeTypes.Goto,
        },
      ];

      const result = convertClientFlowToApiFlow(mockNodes, mockEdges);

      expect(result.node1.data.ports[BUILT_IN_PORT].next).toEqual({
        id: 'edge1',
        type: EdgeTypes.Goto,
        target: 'node2',
        setting: {
          color: undefined,
        },
      });
    });
  });

  describe('convertApiDataToReactFlowData', () => {
    it('should convert API data to React Flow format for basic nodes', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Start,
          coords: [100, 100] as [number, number],
          data: {
            name: 'Start Node',
            ports: {
              [BUILT_IN_PORT]: {
                next: {
                  id: 'edge1',
                  type: 'goto',
                  target: 'node2',
                },
              },
            },
          },
        },
        node2: {
          ID: 'node2',
          type: ENodeType.End,
          coords: [200, 200] as [number, number],
          data: { name: 'End Node' },
        },
      };

      const result = convertApiDataToReactFlowData(nodesObject);

      expect(result.nodes).toHaveLength(2);
      expect(result.edges).toHaveLength(1);
      expect(result.nodes[0]).toEqual({
        id: 'node1',
        type: ENodeType.Start,
        position: { x: 100, y: 100 },
        data: {
          properties: {
            name: 'Start Node',
            ports: nodesObject.node1.data.ports,
          },
        },
      });
      expect(result.edges[0]).toMatchObject({
        id: 'edge1',
        source: 'node1',
        target: 'node2',
        sourceHandle: 'built-in.next',
        type: EdgeTypes.Goto,
        markerEnd: {
          type: 'arrowclosed',
          color: '#FF6B6B',
        },
        data: {
          setting: {
            color: '#FF6B6B',
          },
          hover: false,
          type: EdgeTypes.Goto,
        },
        style: {
          stroke: '#FF6B6B',
          strokeWidth: 2,
        },
      });
    });

    it('should skip step and action nodes at root level', () => {
      const nodesObject = {
        step1: {
          ID: 'step1',
          type: ENodeType.Text,
          coords: [100, 100] as [number, number],
          data: {},
        },
        action1: {
          ID: 'action1',
          type: ENodeType.Action,
          coords: [150, 150] as [number, number],
          data: {},
        },
      };

      const result = convertApiDataToReactFlowData(nodesObject);

      expect(result.nodes).toHaveLength(0);
      expect(result.edges).toHaveLength(0);
    });

    it('should handle block nodes with steps', () => {
      const nodesObject = {
        block1: {
          ID: 'block1',
          type: ENodeType.Block,
          coords: [100, 100] as [number, number],
          data: {
            steps: ['step1', 'step2'],
            setting: {
              line: {
                richMenu: {
                  id: 'menu1',
                  enabled: true,
                  forceDisable: false,
                },
              },
            },
          },
        },
        step1: {
          ID: 'step1',
          type: ENodeType.Text,
          coords: [0, 30] as [number, number],
          data: {
            content: 'Step 1',
            ports: {
              [BUILT_IN_PORT]: {
                next: {
                  id: 'edge1',
                  type: 'goto',
                  target: 'node3',
                },
              },
            },
          },
        },
        step2: {
          ID: 'step2',
          type: ENodeType.Buttons,
          coords: [0, 130] as [number, number],
          height: 80,
          data: { content: 'Step 2' },
        },
      };

      (getStepPositionY as any).mockImplementation(({ index }) => 30 + index * 100);

      const result = convertApiDataToReactFlowData(nodesObject);

      expect(result.nodes).toHaveLength(3); // block + 2 steps
      expect(result.nodes[0].type).toBe(ENodeType.Block);
      expect(result.nodes[1]).toMatchObject({
        id: 'step1',
        type: ENodeType.Text,
        parentId: 'block1',
        position: { x: 0, y: 30 },
        data: {
          canHandle: false,
          stepIndex: 0,
        },
      });
      expect(result.nodes[2]).toMatchObject({
        id: 'step2',
        type: ENodeType.Buttons,
        parentId: 'block1',
        position: { x: 0, y: 130 },
        height: 80,
        data: {
          canHandle: true,
          stepIndex: 1,
        },
      });
    });

    it('should filter out block nodes without valid steps', () => {
      const nodesObject = {
        block1: {
          ID: 'block1',
          type: ENodeType.Block,
          coords: [100, 100] as [number, number],
          data: {
            steps: ['nonexistent1', 'nonexistent2'],
          },
        },
      };

      const result = convertApiDataToReactFlowData(nodesObject);

      expect(result.nodes).toHaveLength(0);
      expect(result.edges).toHaveLength(0);
    });

    it('should handle block with empty steps array', () => {
      const nodesObject = {
        block1: {
          ID: 'block1',
          type: ENodeType.Block,
          coords: [100, 100] as [number, number],
          data: {
            steps: [],
          },
        },
      };

      const result = convertApiDataToReactFlowData(nodesObject);

      expect(result.nodes).toHaveLength(0);
    });

    it('should handle nodes with action nodes', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Intent,
          coords: [100, 100] as [number, number],
          data: {
            name: 'Intent Node',
            ports: {
              [BUILT_IN_PORT]: {
                next: { target: 'action1' },
              },
            },
          },
        },
        action1: {
          ID: 'action1',
          type: ENodeType.Action,
          parentId: 'node1',
          coords: [150, 100] as [number, number],
          data: { actionType: 'url' },
        },
      };

      // Mock should return the action when called with 'node1'
      (getActionNodesInDecaData as any).mockImplementation((nodeId) => {
        if (nodeId === 'node1') {
          return [nodesObject.action1];
        }
        return [];
      });

      const result = convertApiDataToReactFlowData(nodesObject);

      expect(result.nodes).toHaveLength(2); // intent + action
      expect(result.nodes[1]).toMatchObject({
        id: 'action1',
        type: ENodeType.Action,
      });
    });

    it('should handle block node without setting property', () => {
      const nodesObject = {
        block1: {
          ID: 'block1',
          type: ENodeType.Block,
          coords: [100, 100] as [number, number],
          data: {
            steps: ['step1'],
          },
        },
        step1: {
          ID: 'step1',
          type: ENodeType.Text,
          coords: [0, 30] as [number, number],
          data: { content: 'Step 1' },
        },
      };

      const result = convertApiDataToReactFlowData(nodesObject);

      expect(result.nodes[0].data.properties.setting.line.richMenu).toEqual({
        id: '',
        enabled: false,
        forceDisable: false,
      });
    });

    it('should handle block with single step when array.at() is not supported', () => {
      // Mock Array.prototype.at to return undefined to test the fallback
      const originalAt = Array.prototype.at;
      Array.prototype.at = vi.fn(() => undefined);

      const nodesObject = {
        block1: {
          ID: 'block1',
          type: ENodeType.Block,
          coords: [100, 100] as [number, number],
          data: {
            steps: ['step1'],
          },
        },
        step1: {
          ID: 'step1',
          type: ENodeType.Text,
          coords: [0, 30] as [number, number],
          data: { content: 'Step 1' },
        },
      };

      (getActionNodesInDecaData as any).mockReturnValue([]);

      const result = convertApiDataToReactFlowData(nodesObject);

      expect(result.nodes).toHaveLength(2); // block + step
      expect(getActionNodesInDecaData).toHaveBeenCalledWith('step1', nodesObject);

      // Restore Array.prototype.at
      Array.prototype.at = originalAt;
    });

    it('should handle menu edge type in ports', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Start,
          coords: [100, 100] as [number, number],
          data: {
            ports: {
              dynamic: {
                button1: {
                  id: 'edge1',
                  type: PORT_TYPE.menu,
                  target: 'node2',
                },
              },
            },
          },
        },
      };

      const result = convertApiDataToReactFlowData(nodesObject);

      expect(result.edges[0]).toMatchObject({
        type: EdgeTypes.Menu,
        sourceHandle: 'dynamic.button1',
        markerEnd: {
          type: 'arrowclosed',
          color: '#FF6B6B',
        },
        data: {
          setting: {
            color: '#FF6B6B',
          },
          hover: false,
          type: EdgeTypes.Menu,
        },
        style: {
          stroke: '#FF6B6B',
          strokeWidth: 2,
        },
      });
    });

    it('should handle action edge type in ports', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Start,
          coords: [100, 100] as [number, number],
          data: {
            ports: {
              action: {
                action1: {
                  id: 'edge1',
                  type: PORT_TYPE.action,
                  target: 'node2',
                },
              },
            },
          },
        },
      };

      const result = convertApiDataToReactFlowData(nodesObject);

      expect(result.edges[0]).toMatchObject({
        type: EdgeTypes.Action,
        sourceHandle: 'action.action1',
        selectable: false,
        deletable: false,
        markerEnd: {
          type: 'arrowclosed',
          color: '#FF6B6B',
        },
        data: {
          setting: {
            color: '#FF6B6B',
          },
          hover: false,
          type: EdgeTypes.Action,
        },
        style: {
          stroke: '#FF6B6B',
          strokeWidth: 2,
        },
      });
    });

    it('should handle edges with color settings', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Start,
          coords: [100, 100] as [number, number],
          data: {
            ports: {
              [BUILT_IN_PORT]: {
                next: {
                  id: 'edge1',
                  type: 'goto',
                  target: 'node2',
                  setting: {
                    color: '#4ECDC4',
                  },
                },
              },
            },
          },
        },
      };

      const result = convertApiDataToReactFlowData(nodesObject);

      expect(result.edges[0]).toMatchObject({
        id: 'edge1',
        source: 'node1',
        target: 'node2',
        sourceHandle: 'built-in.next',
        type: EdgeTypes.Goto,
        markerEnd: {
          type: 'arrowclosed',
          color: '#4ECDC4',
        },
        data: {
          setting: {
            color: '#4ECDC4',
          },
          hover: false,
          type: EdgeTypes.Goto,
        },
        style: {
          stroke: '#4ECDC4',
          strokeWidth: 2,
        },
      });
    });

    it('should handle edges without color settings and use default color', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Start,
          coords: [100, 100] as [number, number],
          data: {
            ports: {
              [BUILT_IN_PORT]: {
                next: {
                  id: 'edge1',
                  type: 'goto',
                  target: 'node2',
                },
              },
            },
          },
        },
      };

      const result = convertApiDataToReactFlowData(nodesObject);

      expect(result.edges[0]).toMatchObject({
        id: 'edge1',
        source: 'node1',
        target: 'node2',
        sourceHandle: 'built-in.next',
        type: EdgeTypes.Goto,
        markerEnd: {
          type: 'arrowclosed',
          color: '#FF6B6B', // First color from COLOR_PALETTE_KEYS
        },
        data: {
          setting: {
            color: '#FF6B6B',
          },
          hover: false,
          type: EdgeTypes.Goto,
        },
        style: {
          stroke: '#FF6B6B',
          strokeWidth: 2,
        },
      });
    });

    it('should handle edges with empty settings object', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Start,
          coords: [100, 100] as [number, number],
          data: {
            ports: {
              [BUILT_IN_PORT]: {
                next: {
                  id: 'edge1',
                  type: 'goto',
                  target: 'node2',
                  setting: {},
                },
              },
            },
          },
        },
      };

      const result = convertApiDataToReactFlowData(nodesObject);

      expect(result.edges[0]).toMatchObject({
        id: 'edge1',
        source: 'node1',
        target: 'node2',
        sourceHandle: 'built-in.next',
        type: EdgeTypes.Goto,
        markerEnd: {
          type: 'arrowclosed',
          color: '#FF6B6B', // Default color when settings.color is undefined
        },
        data: {
          setting: {
            color: '#FF6B6B',
          },
          hover: false,
          type: EdgeTypes.Goto,
        },
        style: {
          stroke: '#FF6B6B',
          strokeWidth: 2,
        },
      });
    });

    it('should handle menu edge type with color settings', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Start,
          coords: [100, 100] as [number, number],
          data: {
            ports: {
              dynamic: {
                button1: {
                  id: 'edge1',
                  type: PORT_TYPE.menu,
                  target: 'node2',
                  setting: {
                    color: '#45B7D1',
                  },
                },
              },
            },
          },
        },
      };

      const result = convertApiDataToReactFlowData(nodesObject);

      expect(result.edges[0]).toMatchObject({
        id: 'edge1',
        source: 'node1',
        target: 'node2',
        sourceHandle: 'dynamic.button1',
        type: EdgeTypes.Menu,
        markerEnd: {
          type: 'arrowclosed',
          color: '#45B7D1',
        },
        data: {
          setting: {
            color: '#45B7D1',
          },
          hover: false,
          type: EdgeTypes.Menu,
        },
        style: {
          stroke: '#45B7D1',
          strokeWidth: 2,
        },
      });
    });

    it('should skip ports without target', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Start,
          coords: [100, 100] as [number, number],
          data: {
            ports: {
              [BUILT_IN_PORT]: {
                next: {
                  id: 'edge1',
                  type: 'goto',
                  target: null,
                },
              },
            },
          },
        },
      };

      const result = convertApiDataToReactFlowData(nodesObject);

      expect(result.edges).toHaveLength(0);
    });
  });

  describe('convertActionNodesFromDecaDataToFlowData', () => {
    it('should handle empty action nodes', () => {
      (getActionNodesInDecaData as any).mockReturnValue([]);

      const result = convertActionNodesFromDecaDataToFlowData('node1', {});

      expect(result).toEqual({ nodes: [], edges: [] });
    });

    it('should convert action nodes with dynamic ports', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Buttons,
          coords: [100, 100] as [number, number],
          data: {
            buttons: [
              { port: 'button1', label: 'Button 1' },
              { port: 'button2', label: 'Button 2' },
            ],
            ports: {
              [DYNAMIC_PORT]: {
                button1: {
                  target: 'action1',
                },
                button2: {
                  target: 'action2',
                },
              },
            },
          },
        },
        action1: {
          ID: 'action1',
          type: ENodeType.Action,
          parentId: 'node1',
          coords: [150, 100] as [number, number],
          data: { actionType: 'url' },
        },
        action2: {
          ID: 'action2',
          type: ENodeType.Action,
          parentId: 'node1',
          coords: [150, 200] as [number, number],
          data: { actionType: 'variable' },
        },
      };

      const mockActionNodes = [nodesObject.action1, nodesObject.action2];

      (getActionNodesInDecaData as any).mockReturnValue(mockActionNodes);
      (getKeyDynamicData as any).mockReturnValue('buttons');

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result.nodes).toHaveLength(2);
      expect(result.nodes[0]).toMatchObject({
        id: 'action1',
        type: ENodeType.Action,
        data: {
          properties: {
            actionType: 'url',
            portId: 'dynamic.button1',
          },
        },
      });
      expect(result.nodes[1]).toMatchObject({
        id: 'action2',
        type: ENodeType.Action,
        data: {
          properties: {
            actionType: 'variable',
            portId: 'dynamic.button2',
          },
        },
      });
    });

    it('should handle built-in ports (else, next, failure, finish)', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Condition,
          coords: [100, 100] as [number, number],
          data: {
            ports: {
              'built-in.else': { target: 'action1' },
              'built-in.next': { target: 'action2' },
              'built-in.failure': { target: 'action3' },
              'built-in.finish': { target: 'action4' },
            },
          },
        },
        action1: {
          ID: 'action1',
          type: ENodeType.Action,
          coords: [150, 100] as [number, number],
          data: { actionType: 'intent' },
        },
        action2: {
          ID: 'action2',
          type: ENodeType.Action,
          coords: [150, 100] as [number, number],
          data: { actionType: 'node' },
        },
        action3: {
          ID: 'action3',
          type: ENodeType.Action,
          coords: [150, 200] as [number, number],
          data: { actionType: 'end' },
        },
        action4: {
          ID: 'action4',
          type: ENodeType.Action,
          coords: [150, 200] as [number, number],
          data: { actionType: 'url' },
        },
      };

      const mockActionNodes = [
        nodesObject.action1,
        nodesObject.action2,
        nodesObject.action3,
        nodesObject.action4,
      ];

      (getActionNodesInDecaData as any).mockReturnValue(mockActionNodes);

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      // Each port creates action nodes for all actions on the same Y coordinate
      // Port else (action1 at Y=100) → includes action1, action2
      // Port next (action2 at Y=100) → includes action1, action2
      // Port failure (action3 at Y=200) → includes action3, action4
      // Port finish (action4 at Y=200) → includes action3, action4
      // Total: 8 nodes
      expect(result.nodes).toHaveLength(8);

      // Check that the first two nodes have the correct portIds
      const elsePortNodes = result.nodes.filter(
        (n) => n.data.properties.portId === 'built-in.else'
      );
      const nextPortNodes = result.nodes.filter(
        (n) => n.data.properties.portId === 'built-in.next'
      );

      expect(elsePortNodes).toHaveLength(2); // action1 and action2
      expect(nextPortNodes).toHaveLength(2); // action1 and action2
    });

    it('should handle action nodes with ports and create edges', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Text,
          coords: [100, 100] as [number, number],
          data: {
            ports: {
              [BUILT_IN_PORT]: {
                next: { target: 'action1' },
              },
            },
          },
        },
        action1: {
          ID: 'action1',
          type: ENodeType.Action,
          coords: [150, 100] as [number, number],
          data: {
            actionType: 'url',
            ports: {
              [BUILT_IN_PORT]: {
                next: {
                  id: 'edge1',
                  type: 'goto',
                  target: 'node2',
                },
              },
            },
          },
        },
      };

      (getActionNodesInDecaData as any).mockReturnValue([nodesObject.action1]);

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result.nodes).toHaveLength(1);
      expect(result.edges).toHaveLength(1);
      expect(result.edges[0]).toMatchObject({
        id: 'edge1',
        source: 'action1',
        target: 'node2',
        sourceHandle: 'built-in.next',
        markerEnd: {
          type: 'arrowclosed',
          color: '#FF6B6B',
        },
        data: {
          setting: {
            color: '#FF6B6B',
          },
          hover: false,
          type: EdgeTypes.Goto,
        },
        style: {
          stroke: '#FF6B6B',
          strokeWidth: 2,
        },
      });
    });

    it('should skip dynamic ports without target', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Buttons,
          coords: [100, 100] as [number, number],
          data: {
            buttons: [{ port: 'button1', label: 'Button 1' }],
            ports: {
              [DYNAMIC_PORT]: {
                button1: {},
              },
            },
          },
        },
      };

      (getActionNodesInDecaData as any).mockReturnValue([]);
      (getKeyDynamicData as any).mockReturnValue('buttons');

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result.nodes).toHaveLength(0);
    });

    it('should skip dynamic ports with missing port data', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Buttons,
          coords: [100, 100] as [number, number],
          data: {
            buttons: [{ port: 'button1', label: 'Button 1' }],
            ports: {
              [DYNAMIC_PORT]: {
                // button1 is missing from dynamicPorts
              },
            },
          },
        },
      };

      (getActionNodesInDecaData as any).mockReturnValue([]);
      (getKeyDynamicData as any).mockReturnValue('buttons');

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result.nodes).toHaveLength(0);
    });

    it('should skip dynamic ports with null port data', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Buttons,
          coords: [100, 100] as [number, number],
          data: {
            buttons: [{ port: 'button1', label: 'Button 1' }],
            ports: {
              [DYNAMIC_PORT]: {
                button1: null,
              },
            },
          },
        },
      };

      (getActionNodesInDecaData as any).mockReturnValue([]);
      (getKeyDynamicData as any).mockReturnValue('buttons');

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result.nodes).toHaveLength(0);
    });

    it('should skip built-in ports without data', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Condition,
          coords: [100, 100] as [number, number],
          data: {
            ports: {},
          },
        },
      };

      (getActionNodesInDecaData as any).mockReturnValue([]);

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result.nodes).toHaveLength(0);
    });

    it('should skip built-in ports with null port data', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Condition,
          coords: [100, 100] as [number, number],
          data: {
            ports: {
              'built-in.else': null,
              'built-in.next': null,
              'built-in.failure': null,
              'built-in.finish': null,
            },
          },
        },
      };

      (getActionNodesInDecaData as any).mockReturnValue([]);

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result.nodes).toHaveLength(0);
    });

    it('should skip built-in ports with undefined port data', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Condition,
          coords: [100, 100] as [number, number],
          data: {
            ports: {
              'built-in.else': undefined,
              'built-in.next': undefined,
              'built-in.failure': undefined,
              'built-in.finish': undefined,
            },
          },
        },
      };

      (getActionNodesInDecaData as any).mockReturnValue([]);

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result.nodes).toHaveLength(0);
    });

    it('should handle node without ports', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Text,
          coords: [100, 100] as [number, number],
          data: {},
        },
      };

      (getActionNodesInDecaData as any).mockReturnValue([]);

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result).toEqual({ nodes: [], edges: [] });
    });

    it('should handle missing first action node for dynamic port', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Buttons,
          coords: [100, 100] as [number, number],
          data: {
            buttons: [{ port: 'button1', label: 'Button 1' }],
            ports: {
              [DYNAMIC_PORT]: {
                button1: { target: 'nonexistent' },
              },
            },
          },
        },
      };

      (getActionNodesInDecaData as any).mockReturnValue([]);
      (getKeyDynamicData as any).mockReturnValue('buttons');

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result.nodes).toHaveLength(0);
    });

    it('should handle missing first action node for built-in port', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Condition,
          coords: [100, 100] as [number, number],
          data: {
            ports: {
              'built-in.else': { target: 'nonexistent' },
            },
          },
        },
      };

      (getActionNodesInDecaData as any).mockReturnValue([]);

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result.nodes).toHaveLength(0);
    });

    it('should only include actions on the same Y coordinate as first action', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Buttons,
          coords: [100, 100] as [number, number],
          data: {
            buttons: [{ port: 'button1', label: 'Button 1' }],
            ports: {
              [DYNAMIC_PORT]: {
                button1: { target: 'action1' },
              },
            },
          },
        },
        action1: {
          ID: 'action1',
          type: ENodeType.Action,
          coords: [150, 100] as [number, number],
          data: { actionType: 'url' },
        },
        action2: {
          ID: 'action2',
          type: ENodeType.Action,
          coords: [250, 100] as [number, number],
          data: { actionType: 'variable' },
        },
        action3: {
          ID: 'action3',
          type: ENodeType.Action,
          coords: [350, 200] as [number, number], // Different Y coordinate
          data: { actionType: 'intent' },
        },
      };

      const mockActionNodes = [nodesObject.action1, nodesObject.action2, nodesObject.action3];

      (getActionNodesInDecaData as any).mockReturnValue(mockActionNodes);
      (getKeyDynamicData as any).mockReturnValue('buttons');

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result.nodes).toHaveLength(2); // Only action1 and action2
      expect(result.nodes.map((n) => n.id)).toEqual(['action1', 'action2']);
    });

    it('should set correct width based on action type', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Text,
          coords: [100, 100] as [number, number],
          data: {
            ports: {
              [BUILT_IN_PORT]: {
                next: { target: 'action1' },
              },
            },
          },
        },
        action1: {
          ID: 'action1',
          type: ENodeType.Action,
          coords: [150, 100] as [number, number],
          data: { actionType: 'intent' },
        },
        action2: {
          ID: 'action2',
          type: ENodeType.Action,
          coords: [250, 100] as [number, number],
          data: { actionType: 'url' },
        },
      };

      (getActionNodesInDecaData as any).mockReturnValue([nodesObject.action1, nodesObject.action2]);
      (isLargeWidthAction as any).mockImplementation((type) => type === 'url');

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result.nodes[0]?.style?.width).toBe(100); // Small width for intent
      expect(result.nodes[1]?.style?.width).toBe(200); // Large width for url
    });

    // Additional test cases to improve branch coverage
    it('should handle action nodes without ports data', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Text,
          coords: [100, 100] as [number, number],
          data: {
            ports: {
              [BUILT_IN_PORT]: {
                next: { target: 'action1' },
              },
            },
          },
        },
        action1: {
          ID: 'action1',
          type: ENodeType.Action,
          coords: [150, 100] as [number, number],
          data: { actionType: 'intent' }, // No ports data
        },
      };

      (getActionNodesInDecaData as any).mockReturnValue([nodesObject.action1]);

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result.nodes).toHaveLength(1);
      expect(result.edges).toHaveLength(0); // No edges because action has no ports
    });

    it('should handle action nodes with empty ports data', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Text,
          coords: [100, 100] as [number, number],
          data: {
            ports: {
              [BUILT_IN_PORT]: {
                next: { target: 'action1' },
              },
            },
          },
        },
        action1: {
          ID: 'action1',
          type: ENodeType.Action,
          coords: [150, 100] as [number, number],
          data: {
            actionType: 'intent',
            ports: {}, // Empty ports
          },
        },
      };

      (getActionNodesInDecaData as any).mockReturnValue([nodesObject.action1]);

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result.nodes).toHaveLength(1);
      expect(result.edges).toHaveLength(0); // No edges because ports is empty
    });

    // Test cases to cover the remaining uncovered branches
    it('should handle dynamic port with missing portData (covers line 291)', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Buttons,
          coords: [100, 100] as [number, number],
          data: {
            buttons: [{ port: 'button1', label: 'Button 1' }],
            ports: {
              [DYNAMIC_PORT]: {
                // button1 is intentionally missing from dynamicPorts
              },
            },
          },
        },
        action1: {
          ID: 'action1',
          type: ENodeType.Action,
          coords: [150, 100] as [number, number],
          data: { actionType: 'url' },
        },
      };

      (getActionNodesInDecaData as any).mockReturnValue([nodesObject.action1]);
      (getKeyDynamicData as any).mockReturnValue('buttons');

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result.nodes).toHaveLength(0);
      expect(result.edges).toHaveLength(0);
    });

    it('should handle dynamic port with missing firstActionNode (covers line 293)', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Buttons,
          coords: [100, 100] as [number, number],
          data: {
            buttons: [{ port: 'button1', label: 'Button 1' }],
            ports: {
              [DYNAMIC_PORT]: {
                button1: { target: 'nonexistent-action' },
              },
            },
          },
        },
        action1: {
          ID: 'action1',
          type: ENodeType.Action,
          coords: [150, 100] as [number, number],
          data: { actionType: 'url' },
        },
      };

      (getActionNodesInDecaData as any).mockReturnValue([nodesObject.action1]);
      (getKeyDynamicData as any).mockReturnValue('buttons');

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result.nodes).toHaveLength(0);
      expect(result.edges).toHaveLength(0);
    });

    it('should handle built-in port with missing firstActionNode (covers line 314)', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Condition,
          coords: [100, 100] as [number, number],
          data: {
            ports: {
              'built-in.else': { target: 'nonexistent-action' },
              'built-in.next': { target: 'nonexistent-action' },
              'built-in.failure': { target: 'nonexistent-action' },
              'built-in.finish': { target: 'nonexistent-action' },
            },
          },
        },
        action1: {
          ID: 'action1',
          type: ENodeType.Action,
          coords: [150, 100] as [number, number],
          data: { actionType: 'intent' },
        },
      };

      (getActionNodesInDecaData as any).mockReturnValue([nodesObject.action1]);

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result.nodes).toHaveLength(0);
      expect(result.edges).toHaveLength(0);
    });

    // Test case to cover the getDynamicData function (lines 167-169)
    it('should handle dynamic data retrieval for nodes that need revalidation', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Buttons,
          coords: [100, 100] as [number, number],
          data: {
            buttons: [
              { port: 'button1', label: 'Button 1' },
              { port: 'button2', label: 'Button 2' },
            ],
            ports: {
              [DYNAMIC_PORT]: {
                button1: { target: 'action1' },
                button2: { target: 'action2' },
              },
            },
          },
        },
        action1: {
          ID: 'action1',
          type: ENodeType.Action,
          parentId: 'node1',
          coords: [150, 100] as [number, number],
          data: { actionType: 'url' },
        },
        action2: {
          ID: 'action2',
          type: ENodeType.Action,
          parentId: 'node1',
          coords: [150, 200] as [number, number],
          data: { actionType: 'variable' },
        },
      };

      const mockActionNodes = [nodesObject.action1, nodesObject.action2];

      (getActionNodesInDecaData as any).mockReturnValue(mockActionNodes);
      (getKeyDynamicData as any).mockReturnValue('buttons');

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result.nodes).toHaveLength(2);
      expect(getKeyDynamicData).toHaveBeenCalledWith(ENodeType.Buttons);
    });

    // Test case to cover the default case in getEdgeType function
    it('should handle unknown port type and default to goto edge type', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Start,
          coords: [100, 100] as [number, number],
          data: {
            ports: {
              [BUILT_IN_PORT]: {
                next: {
                  id: 'edge1',
                  type: 'unknown-type', // This should default to goto
                  target: 'node2',
                },
              },
            },
          },
        },
      };

      const result = convertApiDataToReactFlowData(nodesObject);

      expect(result.edges).toHaveLength(1);
      expect(result.edges[0]).toMatchObject({
        id: 'edge1',
        source: 'node1',
        target: 'node2',
        sourceHandle: 'built-in.next',
        type: EdgeTypes.Goto, // Should default to Goto for unknown types
        markerEnd: {
          type: 'arrowclosed',
          color: '#FF6B6B',
        },
        data: {
          setting: {
            color: '#FF6B6B',
          },
          hover: false,
          type: EdgeTypes.Goto,
        },
        style: {
          stroke: '#FF6B6B',
          strokeWidth: 2,
        },
      });
    });

    // Test case to cover edge case where port data has no id
    it('should handle port data without id field', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Start,
          coords: [100, 100] as [number, number],
          data: {
            ports: {
              [BUILT_IN_PORT]: {
                next: {
                  // Missing id field
                  type: 'goto',
                  target: 'node2',
                },
              },
            },
          },
        },
      };

      const result = convertApiDataToReactFlowData(nodesObject);

      expect(result.edges).toHaveLength(1);
      expect(result.edges[0]).toMatchObject({
        id: undefined, // Should be undefined when no id is provided
        source: 'node1',
        target: 'node2',
        sourceHandle: 'built-in.next',
        type: EdgeTypes.Goto,
        markerEnd: {
          type: 'arrowclosed',
          color: '#FF6B6B',
        },
        data: {
          setting: {
            color: '#FF6B6B',
          },
          hover: false,
          type: EdgeTypes.Goto,
        },
        style: {
          stroke: '#FF6B6B',
          strokeWidth: 2,
        },
      });
    });

    // Test case to specifically cover the getDynamicData function (lines 167-169)
    it('should handle dynamic data retrieval and exercise getDynamicData function', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Buttons,
          coords: [100, 100] as [number, number],
          data: {
            buttons: [
              { port: 'button1', label: 'Button 1' },
              { port: 'button2', label: 'Button 2' },
            ],
            ports: {
              [DYNAMIC_PORT]: {
                button1: { target: 'action1' },
                button2: { target: 'action2' },
              },
            },
          },
        },
        action1: {
          ID: 'action1',
          type: ENodeType.Action,
          parentId: 'node1',
          coords: [150, 100] as [number, number],
          data: { actionType: 'url' },
        },
        action2: {
          ID: 'action2',
          type: ENodeType.Action,
          parentId: 'node1',
          coords: [150, 200] as [number, number],
          data: { actionType: 'variable' },
        },
      };

      const mockActionNodes = [nodesObject.action1, nodesObject.action2];

      (getActionNodesInDecaData as any).mockReturnValue(mockActionNodes);
      (getKeyDynamicData as any).mockReturnValue('buttons');

      // This should call getDynamicData internally
      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result.nodes).toHaveLength(2);
      expect(getKeyDynamicData).toHaveBeenCalledWith(ENodeType.Buttons);

      // Verify that the dynamic data was processed correctly
      expect(result.nodes[0]).toMatchObject({
        id: 'action1',
        type: ENodeType.Action,
        data: {
          properties: {
            actionType: 'url',
            portId: 'dynamic.button1',
          },
        },
      });
      expect(result.nodes[1]).toMatchObject({
        id: 'action2',
        type: ENodeType.Action,
        data: {
          properties: {
            actionType: 'variable',
            portId: 'dynamic.button2',
          },
        },
      });
    });

    // Test case to cover the getDynamicData function through convertApiDataToReactFlowData
    it('should handle block nodes with dynamic step nodes that need revalidation', () => {
      const nodesObject = {
        block1: {
          ID: 'block1',
          type: ENodeType.Block,
          coords: [100, 100] as [number, number],
          data: {
            steps: ['step1'],
            setting: {
              line: {
                richMenu: {
                  id: 'menu1',
                  enabled: true,
                  forceDisable: false,
                },
              },
            },
          },
        },
        step1: {
          ID: 'step1',
          type: ENodeType.Buttons, // This type needs revalidation
          coords: [0, 30] as [number, number],
          data: {
            buttons: [
              { port: 'button1', label: 'Button 1' },
              { port: 'button2', label: 'Button 2' },
            ],
            ports: {
              [DYNAMIC_PORT]: {
                button1: { target: 'action1' },
                button2: { target: 'action2' },
              },
            },
          },
        },
        action1: {
          ID: 'action1',
          type: ENodeType.Action,
          parentId: 'step1',
          coords: [150, 30] as [number, number],
          data: { actionType: 'url' },
        },
        action2: {
          ID: 'action2',
          type: ENodeType.Action,
          parentId: 'step1',
          coords: [250, 30] as [number, number],
          data: { actionType: 'variable' },
        },
      };

      (getActionNodesInDecaData as any).mockReturnValue([nodesObject.action1, nodesObject.action2]);
      (getKeyDynamicData as any).mockReturnValue('buttons');
      (getStepPositionY as any).mockImplementation(({ index }) => 30 + index * 100);

      const result = convertApiDataToReactFlowData(nodesObject);

      expect(result.nodes).toHaveLength(6); // block + step + 2 actions from step + 2 actions from block
      expect(result.nodes[0].type).toBe(ENodeType.Block);
      expect(result.nodes[1]).toMatchObject({
        id: 'step1',
        type: ENodeType.Buttons,
        parentId: 'block1',
      });

      // Find action nodes by type
      const actionNodes = result.nodes.filter((node) => node.type === ENodeType.Action);
      expect(actionNodes).toHaveLength(4); // 2 from step + 2 from block

      // Verify that getKeyDynamicData was called for the Buttons type
      expect(getKeyDynamicData).toHaveBeenCalledWith(ENodeType.Buttons);
    });

    it('should handle action nodes with null/undefined color settings and use default color', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Text,
          coords: [100, 100] as [number, number],
          data: {
            ports: {
              [BUILT_IN_PORT]: {
                next: { target: 'action1' },
              },
            },
          },
        },
        action1: {
          ID: 'action1',
          type: ENodeType.Action,
          coords: [150, 100] as [number, number],
          data: {
            actionType: 'url',
            setting: { color: null },
          },
        },
        action2: {
          ID: 'action2',
          type: ENodeType.Action,
          coords: [150, 100] as [number, number],
          data: {
            actionType: 'variable',
            setting: { color: undefined },
          },
        },
        action3: {
          ID: 'action3',
          type: ENodeType.Action,
          coords: [150, 100] as [number, number],
          data: {
            actionType: 'intent',
            setting: { color: '' },
          },
        },
      };

      (getActionNodesInDecaData as any).mockReturnValue([
        nodesObject.action1,
        nodesObject.action2,
        nodesObject.action3,
      ]);

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result.nodes).toHaveLength(3);

      // Check that null and undefined colors are converted to default, but empty string is preserved
      expect(result.nodes[0].data.properties.setting.color).toBe('#FF6B6B'); // null -> default
      expect(result.nodes[1].data.properties.setting.color).toBe('#FF6B6B'); // undefined -> default
      expect(result.nodes[2].data.properties.setting.color).toBe(''); // empty string -> preserved as is
    });

    it('should handle action nodes with missing setting property and use default color', () => {
      const nodesObject = {
        node1: {
          ID: 'node1',
          type: ENodeType.Text,
          coords: [100, 100] as [number, number],
          data: {
            ports: {
              [BUILT_IN_PORT]: {
                next: { target: 'action1' },
              },
            },
          },
        },
        action1: {
          ID: 'action1',
          type: ENodeType.Action,
          coords: [150, 100] as [number, number],
          data: {
            actionType: 'url',
            // No setting property at all - this should trigger the fallback to COLOR_PALETTE_KEYS[0]
          },
        },
      };

      (getActionNodesInDecaData as any).mockReturnValue([nodesObject.action1]);

      const result = convertActionNodesFromDecaDataToFlowData('node1', nodesObject);

      expect(result.nodes).toHaveLength(1);

      // The action should have the default color from COLOR_PALETTE_KEYS[0]
      expect(result.nodes[0].data.properties.setting.color).toBe('#FF6B6B'); // First color from COLOR_PALETTE_KEYS
    });
  });
});
