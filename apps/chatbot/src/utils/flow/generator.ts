import { COLOR_PALETTE_KEYS, STEP_NODES, WIDTH_NODE_BLOCK, WIDTH_STEP } from '@/constants/flow';
import {
  ArticleDisplayType,
  ENodeType,
  EdgeTypes,
  EnumApiStepBody,
  EnumApiStepMethod,
  INTENT_SCOPE,
  KbInputDataType,
  KbInputType,
  KbMode,
  OutputType,
  type TApiStepData,
  type TBlockData,
  type TButtonStepData,
  type TCaptureInputStepData,
  type TCarouselStepData,
  type TChoiceStepData,
  type TConditionStepData,
  type TCustomTemplateStepData,
  type TDynamicStepData,
  type TEdge,
  type TEmailStepData,
  type TEntityStepData,
  type TFormStepData,
  type TGaiStepData,
  type TIntentData,
  type TKBStepData,
  type TLiveChatNodeData,
  type TReactFlowNode,
  type TScriptStepData,
  type TSetVariableData,
  type TTableData,
  type TTextStepData,
} from '@/types';
import { TableOperation } from '@/types/nodes/table';
import { MarkerType } from '@xyflow/react';
import { randomId, randomShortId } from '../id';

export const generateReactFlowNode = (type: ENodeType, name: string): TReactFlowNode => {
  const node: TReactFlowNode = {
    id: randomId(),
    type,
    position: { x: 0, y: 0 },
    data: {
      properties: {},
    },
  };

  if (STEP_NODES.includes(type)) {
    node.width = WIDTH_STEP;
  }

  switch (type) {
    case ENodeType.Block: {
      node.data.properties = {
        name,
        steps: [],
        setting: {
          line: {
            richMenu: {
              enabled: false,
              forceDisable: false,
              id: '',
            },
          },
          color: COLOR_PALETTE_KEYS[0],
        },
      } as TBlockData;
      node.width = WIDTH_NODE_BLOCK;
      break;
    }
    case ENodeType.Livechat:
      node.data.properties = {
        name,
        fetchConversationHistory: false,
        enableTeamSelection: false,
        teamSelectionText: '',
        selectedTeams: [],
        ports: { builtIn: {} },
        setting: {
          color: COLOR_PALETTE_KEYS[0],
        },
      } as TLiveChatNodeData;
      break;

    case ENodeType.Text:
      node.data.properties = {
        texts: [name],
        ports: { builtIn: {} },
      } as TTextStepData;
      break;

    case ENodeType.QnA:
      node.data.properties = {
        mode: KbMode.Specified,
        articleDisplayType: ArticleDisplayType.Single,
        inputType: KbInputType.Select,
        inputDataType: KbInputDataType.Base,
        language: 'jp',
        answerLimit: 1,
        notFoundPath: false,
        ports: { builtIn: {} },
      } as TKBStepData;
      break;

    case ENodeType.Capture:
      node.data.properties = {
        intentScope: 'GLOBAL',
        capture: {
          variable: '',
          type: '',
        },
        ports: { builtIn: {} },
      } as TCaptureInputStepData;
      break;

    case ENodeType.Buttons:
      node.data.properties = {
        text: '',
        buttons: [
          {
            id: randomShortId(),
            label: name,
            port: randomShortId(),
          },
        ],
        noMatchPath: false,
        intentScope: 'GLOBAL',
        ports: {
          builtIn: {},
          dynamic: {},
        },
      } as TButtonStepData;
      break;

    case ENodeType.Condition:
      node.data.properties = {
        expressions: [
          {
            expression: name,
            port: randomShortId(),
          },
        ],
        ports: {
          builtIn: {},
          dynamic: {},
        },
      } as TConditionStepData;
      break;

    case ENodeType.Form:
      node.data.properties = {
        name: '',
        submit: {
          label: '',
        },
        variable: '',
        inputs: [],
        ports: { builtIn: {} },
        fallbackPath: false,
      } as TFormStepData;
      break;

    case ENodeType.Variables:
      node.data.properties = {
        variables: [],
        ports: { builtIn: {} },
      } as TSetVariableData;
      break;

    case ENodeType.Email:
      node.data.properties = {
        name: '',
        to: [],
        cc: [],
        bcc: [],
        title: '',
        body: '',
        ports: { builtIn: {} },
      } as TEmailStepData;
      break;

    case ENodeType.Api:
      node.data.properties = {
        method: EnumApiStepMethod.GET,
        url: '',
        content: '{}',
        headers: [],
        mappings: [],
        params: [],
        bodyType: EnumApiStepBody.none,
        body: [],
        expectedStatus: [],
        ports: { builtIn: {} },
      } as TApiStepData;
      break;

    case ENodeType.Carousel: {
      node.data.properties = {
        cards: [
          {
            id: randomShortId(),
            title: 'Title card',
            description: '',
            image: {
              url: '',
              alt: '',
            },
            buttons: [],
            port: randomShortId(),
          },
        ],
        intentScope: INTENT_SCOPE.GLOBAL,
        ports: { builtIn: {}, dynamic: {} },
      } as TCarouselStepData;
      break;
    }

    case ENodeType.Intent: {
      node.data.properties = {
        intentScope: INTENT_SCOPE.GLOBAL,
        intentId: '',
        ports: { builtIn: {} },
      } as TIntentData;
      break;
    }

    case ENodeType.Choice: {
      node.data.properties = {
        choices: [
          {
            id: randomShortId(),
            port: randomShortId(),
          },
        ],
        noMatchPath: false,
        noMatch: '',
        ports: {
          builtIn: {},
          dynamic: {},
        },
      } as TChoiceStepData;
      break;
    }

    case ENodeType.FindRecord: {
      node.data.properties = {
        operation: TableOperation.FindRecord,
        payload: {
          type: TableOperation.FindRecord,
        },
        ports: { builtIn: {} },
      } as TTableData;
      node.type = ENodeType.Table;
      break;
    }

    case ENodeType.InsertRecord: {
      node.data.properties = {
        operation: TableOperation.InsertRecord,
        payload: {
          type: TableOperation.InsertRecord,
        },
        ports: { builtIn: {} },
      } as TTableData;
      node.type = ENodeType.Table;
      break;
    }

    case ENodeType.UpdateRecord: {
      node.data.properties = {
        operation: TableOperation.UpdateRecord,
        payload: {
          type: TableOperation.UpdateRecord,
        },
        ports: { builtIn: {} },
      } as TTableData;
      node.type = ENodeType.Table;
      break;
    }

    case ENodeType.DeleteRecord: {
      node.data.properties = {
        operation: TableOperation.DeleteRecord,
        payload: {
          type: TableOperation.DeleteRecord,
        },
        ports: { builtIn: {} },
      } as TTableData;
      node.type = ENodeType.Table;
      break;
    }

    case ENodeType.Entity: {
      node.data.properties = {
        entities: [],
        notFoundPath: false,
        retry: 3,
        variableName: '',
        ports: { builtIn: {} },
      } as TEntityStepData;
      node.type = ENodeType.Entity;
      break;
    }

    case ENodeType.CustomTemplate: {
      node.data.properties = {
        platform: '',
        template: '',
        fallbackPath: false,
        ports: { builtIn: {} },
      } as TCustomTemplateStepData;
      node.type = ENodeType.CustomTemplate;
      break;
    }

    case ENodeType.Gai: {
      node.data.properties = {
        prompt: '',
        variableName: '',
        outputType: OutputType.text,
        promptType: 'system',
        fallbackPath: false,
        ports: { builtIn: {} },
      } as TGaiStepData;
      node.type = ENodeType.Gai;
      break;
    }

    case ENodeType.Script: {
      node.data.properties = {
        script: '',
        paths: [],
        fallbackPath: true,
        variableName: '',
        ports: { builtIn: {} },
      } as TScriptStepData;
      node.type = ENodeType.Script;
      break;
    }

    case ENodeType.Dynamic: {
      node.data.properties = {
        text: '',
        labelAttribute: '',
        inputVariable: '',
        outputVariable: '',
        valueAttribute: '',
        buttons: [],
        ports: { builtIn: {}, dynamic: {} },
      } as TDynamicStepData;
      break;
    }

    default:
      break;
  }
  return node;
};

export const generateReactFlowEdge = (
  source: string,
  target: string,
  sourceHandle: string,
  type: EdgeTypes
): TEdge => {
  const color = COLOR_PALETTE_KEYS[0];
  const style = {
    stroke: color,
    strokeWidth: 2,
  };
  const markerEnd = {
    type: MarkerType.ArrowClosed,
    color,
  };
  const data = {
    hover: false,
    type,
    setting: {
      color,
    },
  };
  switch (type) {
    case EdgeTypes.Action:
      return {
        id: randomShortId(),
        source,
        target,
        sourceHandle,
        selectable: false,
        deletable: false,
        type,
        style,
        data,
        markerEnd,
      };
    case EdgeTypes.Menu:
      return {
        id: randomShortId(),
        source,
        target,
        sourceHandle,
        type,
        style,
        data,
      };

    case EdgeTypes.Goto:
      return {
        id: randomShortId(),
        source,
        target,
        sourceHandle,
        type,
        data,
        style,
        markerEnd,
      };
  }
};
