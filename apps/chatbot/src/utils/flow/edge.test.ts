import { describe, expect, it } from 'vitest';
import { getMenuPosition } from './edge';

describe('Edge Utils', () => {
  describe('getMenuPosition', () => {
    it('should calculate position when sourceX >= targetX', () => {
      const result = getMenuPosition(
        100, // labelX
        200, // sourceX (>= targetX)
        50, // sourceY
        150, // targetX
        100, // targetY
        10 // offset
      );

      // When sourceX >= targetX, positionY = (sourceY + targetY) / 2 - offset
      // positionY = (50 + 100) / 2 - 10 = 75 - 10 = 65
      expect(result).toEqual({
        x: 100,
        y: 65,
      });
    });

    it('should calculate position when sourceX < targetX and sourceY > targetY', () => {
      const result = getMenuPosition(
        100, // labelX
        100, // sourceX (< targetX)
        150, // sourceY (> targetY)
        200, // targetX
        50, // targetY
        15 // offset
      );

      // When sourceX < targetX and sourceY > targetY, positionY = targetY - offset
      // positionY = 50 - 15 = 35
      expect(result).toEqual({
        x: 100,
        y: 35,
      });
    });

    it('should calculate position when sourceX < targetX and sourceY <= targetY', () => {
      const result = getMenuPosition(
        100, // labelX
        100, // sourceX (< targetX)
        50, // sourceY (<= targetY)
        200, // targetX
        150, // targetY
        20 // offset
      );

      // When sourceX < targetX and sourceY <= targetY, positionY = sourceY - offset
      // positionY = 50 - 20 = 30
      expect(result).toEqual({
        x: 100,
        y: 30,
      });
    });

    it('should calculate position when sourceX < targetX and sourceY equals targetY', () => {
      const result = getMenuPosition(
        100, // labelX
        100, // sourceX (< targetX)
        100, // sourceY (= targetY)
        200, // targetX
        100, // targetY
        5 // offset
      );

      // When sourceX < targetX and sourceY = targetY, positionY = sourceY - offset
      // positionY = 100 - 5 = 95
      expect(result).toEqual({
        x: 100,
        y: 95,
      });
    });

    it('should handle zero offset', () => {
      const result = getMenuPosition(
        50, // labelX
        200, // sourceX (>= targetX)
        100, // sourceY
        150, // targetX
        200, // targetY
        0 // offset
      );

      // When sourceX >= targetX, positionY = (sourceY + targetY) / 2 - offset
      // positionY = (100 + 200) / 2 - 0 = 150 - 0 = 150
      expect(result).toEqual({
        x: 50,
        y: 150,
      });
    });

    it('should handle negative offset', () => {
      const result = getMenuPosition(
        75, // labelX
        100, // sourceX (< targetX)
        80, // sourceY (> targetY)
        200, // targetX
        60, // targetY
        -5 // offset (negative)
      );

      // When sourceX < targetX and sourceY > targetY, positionY = targetY - offset
      // positionY = 60 - (-5) = 60 + 5 = 65
      expect(result).toEqual({
        x: 75,
        y: 65,
      });
    });

    it('should handle large offset values', () => {
      const result = getMenuPosition(
        200, // labelX
        300, // sourceX (>= targetX)
        100, // sourceY
        250, // targetX
        200, // targetY
        100 // offset (large)
      );

      // When sourceX >= targetX, positionY = (sourceY + targetY) / 2 - offset
      // positionY = (100 + 200) / 2 - 100 = 150 - 100 = 50
      expect(result).toEqual({
        x: 200,
        y: 50,
      });
    });

    it('should handle decimal coordinates', () => {
      const result = getMenuPosition(
        123.45, // labelX
        100.5, // sourceX (< targetX)
        75.25, // sourceY (<= targetY)
        200.75, // targetX
        100.0, // targetY
        12.5 // offset
      );

      // When sourceX < targetX and sourceY <= targetY, positionY = sourceY - offset
      // positionY = 75.25 - 12.5 = 62.75
      expect(result).toEqual({
        x: 123.45,
        y: 62.75,
      });
    });

    it('should handle edge case where sourceX equals targetX', () => {
      const result = getMenuPosition(
        100, // labelX
        150, // sourceX (= targetX)
        80, // sourceY
        150, // targetX
        120, // targetY
        10 // offset
      );

      // When sourceX >= targetX (equals), positionY = (sourceY + targetY) / 2 - offset
      // positionY = (80 + 120) / 2 - 10 = 100 - 10 = 90
      expect(result).toEqual({
        x: 100,
        y: 90,
      });
    });

    it('should handle negative coordinates', () => {
      const result = getMenuPosition(
        -50, // labelX
        -100, // sourceX (< targetX)
        -80, // sourceY (> targetY)
        -50, // targetX
        -120, // targetY
        5 // offset
      );

      // When sourceX < targetX and sourceY > targetY, positionY = targetY - offset
      // positionY = -120 - 5 = -125
      expect(result).toEqual({
        x: -50,
        y: -125,
      });
    });

    it('should handle mixed positive and negative coordinates', () => {
      const result = getMenuPosition(
        0, // labelX
        50, // sourceX (>= targetX)
        -30, // sourceY
        -25, // targetX
        70, // targetY
        15 // offset
      );

      // When sourceX >= targetX, positionY = (sourceY + targetY) / 2 - offset
      // positionY = (-30 + 70) / 2 - 15 = 40 / 2 - 15 = 20 - 15 = 5
      expect(result).toEqual({
        x: 0,
        y: 5,
      });
    });
  });
});
