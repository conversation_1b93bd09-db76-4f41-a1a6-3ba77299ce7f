import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the constants and types before importing the module
vi.mock('@/constants/flow', () => ({
  STEP_NODES: [
    'text',
    'email',
    'buttons',
    'form',
    'qna',
    'capture',
    'variables',
    'carousel',
    'api',
    'condition',
    'table',
    'findRecord',
    'insertRecord',
    'updateRecord',
    'deleteRecord',
    'entity',
    'customTemplate',
    'gai',
    'script',
    'dynamic',
  ],
  WIDTH_STEP: 220,
  WIDTH_NODE_BLOCK: 236,
  COLOR_PALETTE_KEYS: ['#B2B7E2', '#94C4FA', '#E0C6D9', '#FDC8CB', '#C6D6C9', '#FFDB7A', '#D6B4E9'],
}));

vi.mock('@/types', () => ({
  ENodeType: {
    Start: 'start',
    End: 'end',
    Finish: 'finish',
    Text: 'text',
    Email: 'email',
    Block: 'block',
    Form: 'form',
    Buttons: 'buttons',
    QnA: 'qna',
    Livechat: 'livechat',
    Capture: 'capture',
    Variables: 'variables',
    Choice: 'choice',
    Action: 'action',
    Carousel: 'carousel',
    Api: 'api',
    Condition: 'condition',
    Intent: 'intent',
    Menu: 'menu',
    Table: 'table',
    FindRecord: 'findRecord',
    InsertRecord: 'insertRecord',
    UpdateRecord: 'updateRecord',
    DeleteRecord: 'deleteRecord',
    Entity: 'entity',
    CustomTemplate: 'customTemplate',
    Gai: 'gai',
    Script: 'script',
    Dynamic: 'dynamic',
  },
  KbMode: {
    Specified: 'specified',
    Search: 'search',
    Rag: 'rag',
  },
  ArticleDisplayType: {
    Single: 'single',
    Multiple: 'multiple',
  },
  KbInputType: {
    Select: 'select',
    Text: 'text',
  },
  KbInputDataType: {
    Base: 'base',
    Custom: 'custom',
  },
  EnumApiStepMethod: {
    GET: 'GET',
    POST: 'POST',
    PUT: 'PUT',
    DELETE: 'DELETE',
  },
  EnumApiStepBody: {
    none: 'none',
    json: 'json',
    form: 'form',
  },
  INTENT_SCOPE: {
    GLOBAL: 'GLOBAL',
    LOCAL: 'LOCAL',
  },
  OutputType: {
    text: 'text',
    json: 'json',
  },
  TableOperation: {
    FindRecord: 'findRecord',
    InsertRecord: 'insertRecord',
    UpdateRecord: 'updateRecord',
    DeleteRecord: 'deleteRecord',
  },
  EdgeTypes: {
    Goto: 'goto',
    Action: 'action',
    Menu: 'menu',
  },
}));

// Mock the id utility functions
vi.mock('../id', () => ({
  randomShortId: vi.fn(() => 'mockShortId'),
  randomId: vi.fn(() => 'mockId'),
}));

import { ENodeType, EdgeTypes } from '@/types';
// Import the module after mocking
import { generateReactFlowEdge, generateReactFlowNode } from './generator';

describe('generator.ts', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('generateReactFlowNode', () => {
    it('should generate a basic node with correct structure', () => {
      const result = generateReactFlowNode(ENodeType.Text, 'Test Node');

      expect(result).toMatchObject({
        id: 'mockId',
        type: ENodeType.Text,
        position: { x: 0, y: 0 },
        data: {
          properties: expect.any(Object),
        },
      });
    });

    it('should set width for step nodes', () => {
      const result = generateReactFlowNode(ENodeType.Text, 'Test Node');

      expect(result.width).toBe(220);
    });

    it('should not set width for non-step nodes', () => {
      const result = generateReactFlowNode(ENodeType.Start, 'Test Node');

      expect(result.width).toBeUndefined();
    });

    describe('Block node', () => {
      it('should generate block node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.Block, 'Test Block');

        expect(result).toMatchObject({
          type: ENodeType.Block,
          width: 236,
          data: {
            properties: {
              name: 'Test Block',
              steps: [],
              setting: {
                line: {
                  richMenu: {
                    enabled: false,
                    forceDisable: false,
                    id: '',
                  },
                },
                color: '#B2B7E2',
              },
            },
          },
        });
      });
    });

    describe('Livechat node', () => {
      it('should generate livechat node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.Livechat, 'Test Livechat');

        expect(result).toMatchObject({
          type: ENodeType.Livechat,
          data: {
            properties: {
              name: 'Test Livechat',
              fetchConversationHistory: false,
              enableTeamSelection: false,
              teamSelectionText: '',
              selectedTeams: [],
              ports: { builtIn: {} },
              setting: {
                color: '#B2B7E2',
              },
            },
          },
        });
      });
    });

    describe('Text node', () => {
      it('should generate text node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.Text, 'Test Text');

        expect(result).toMatchObject({
          type: ENodeType.Text,
          data: {
            properties: {
              texts: ['Test Text'],
              ports: { builtIn: {} },
            },
          },
        });
      });
    });

    describe('QnA node', () => {
      it('should generate QnA node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.QnA, 'Test QnA');

        expect(result).toMatchObject({
          type: ENodeType.QnA,
          data: {
            properties: {
              mode: 'specified',
              articleDisplayType: 'single',
              inputType: 'select',
              inputDataType: 'base',
              language: 'jp',
              answerLimit: 1,
              notFoundPath: false,
              ports: { builtIn: {} },
            },
          },
        });
      });
    });

    describe('Capture node', () => {
      it('should generate capture node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.Capture, 'Test Capture');

        expect(result).toMatchObject({
          type: ENodeType.Capture,
          data: {
            properties: {
              intentScope: 'GLOBAL',
              capture: {
                variable: '',
                type: '',
              },
              ports: { builtIn: {} },
            },
          },
        });
      });
    });

    describe('Buttons node', () => {
      it('should generate buttons node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.Buttons, 'Test Button');

        expect(result).toMatchObject({
          type: ENodeType.Buttons,
          data: {
            properties: {
              text: '',
              buttons: [
                {
                  id: 'mockShortId',
                  label: 'Test Button',
                  port: 'mockShortId',
                },
              ],
              noMatchPath: false,
              intentScope: 'GLOBAL',
              ports: {
                builtIn: {},
                dynamic: {},
              },
            },
          },
        });
      });
    });

    describe('Condition node', () => {
      it('should generate condition node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.Condition, 'Test Condition');

        expect(result).toMatchObject({
          type: ENodeType.Condition,
          data: {
            properties: {
              expressions: [
                {
                  expression: 'Test Condition',
                  port: 'mockShortId',
                },
              ],
              ports: {
                builtIn: {},
                dynamic: {},
              },
            },
          },
        });
      });
    });

    describe('Form node', () => {
      it('should generate form node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.Form, 'Test Form');

        expect(result).toMatchObject({
          type: ENodeType.Form,
          data: {
            properties: {
              name: '',
              submit: {
                label: '',
              },
              variable: '',
              inputs: [],
              ports: { builtIn: {} },
              fallbackPath: false,
            },
          },
        });
      });
    });

    describe('Variables node', () => {
      it('should generate variables node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.Variables, 'Test Variables');

        expect(result).toMatchObject({
          type: ENodeType.Variables,
          data: {
            properties: {
              variables: [],
              ports: { builtIn: {} },
            },
          },
        });
      });
    });

    describe('Email node', () => {
      it('should generate email node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.Email, 'Test Email');

        expect(result).toMatchObject({
          type: ENodeType.Email,
          data: {
            properties: {
              name: '',
              to: [],
              cc: [],
              bcc: [],
              title: '',
              body: '',
              ports: { builtIn: {} },
            },
          },
        });
      });
    });

    describe('Api node', () => {
      it('should generate api node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.Api, 'Test Api');

        expect(result).toMatchObject({
          type: ENodeType.Api,
          data: {
            properties: {
              method: 'GET',
              url: '',
              content: '{}',
              headers: [],
              mappings: [],
              params: [],
              bodyType: 'none',
              body: [],
              expectedStatus: [],
              ports: { builtIn: {} },
            },
          },
        });
      });
    });

    describe('Carousel node', () => {
      it('should generate carousel node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.Carousel, 'Test Carousel');

        expect(result).toMatchObject({
          type: ENodeType.Carousel,
          data: {
            properties: {
              cards: [
                {
                  id: 'mockShortId',
                  title: 'Title card',
                  description: '',
                  image: {
                    url: '',
                    alt: '',
                  },
                  buttons: [],
                  port: 'mockShortId',
                },
              ],
              intentScope: 'GLOBAL',
              ports: { builtIn: {}, dynamic: {} },
            },
          },
        });
      });
    });

    describe('Intent node', () => {
      it('should generate intent node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.Intent, 'Test Intent');

        expect(result).toMatchObject({
          type: ENodeType.Intent,
          data: {
            properties: {
              intentScope: 'GLOBAL',
              intentId: '',
              ports: { builtIn: {} },
            },
          },
        });
      });
    });

    describe('Choice node', () => {
      it('should generate choice node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.Choice, 'Test Choice');

        expect(result).toMatchObject({
          type: ENodeType.Choice,
          data: {
            properties: {
              choices: [
                {
                  id: 'mockShortId',
                  port: 'mockShortId',
                },
              ],
              noMatchPath: false,
              noMatch: '',
              ports: {
                builtIn: {},
                dynamic: {},
              },
            },
          },
        });
      });
    });

    describe('Table nodes', () => {
      it('should generate FindRecord node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.FindRecord, 'Test FindRecord');

        expect(result).toMatchObject({
          type: ENodeType.Table,
          data: {
            properties: {
              operation: 'findRecord',
              payload: {
                type: 'findRecord',
              },
              ports: { builtIn: {} },
            },
          },
        });
      });

      it('should generate InsertRecord node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.InsertRecord, 'Test InsertRecord');

        expect(result).toMatchObject({
          type: ENodeType.Table,
          data: {
            properties: {
              operation: 'insertRecord',
              payload: {
                type: 'insertRecord',
              },
              ports: { builtIn: {} },
            },
          },
        });
      });

      it('should generate UpdateRecord node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.UpdateRecord, 'Test UpdateRecord');

        expect(result).toMatchObject({
          type: ENodeType.Table,
          data: {
            properties: {
              operation: 'updateRecord',
              payload: {
                type: 'updateRecord',
              },
              ports: { builtIn: {} },
            },
          },
        });
      });

      it('should generate DeleteRecord node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.DeleteRecord, 'Test DeleteRecord');

        expect(result).toMatchObject({
          type: ENodeType.Table,
          data: {
            properties: {
              operation: 'deleteRecord',
              payload: {
                type: 'deleteRecord',
              },
              ports: { builtIn: {} },
            },
          },
        });
      });
    });

    describe('Entity node', () => {
      it('should generate entity node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.Entity, 'Test Entity');

        expect(result).toMatchObject({
          type: ENodeType.Entity,
          data: {
            properties: {
              entities: [],
              notFoundPath: false,
              retry: 3,
              variableName: '',
              ports: { builtIn: {} },
            },
          },
        });
      });
    });

    describe('CustomTemplate node', () => {
      it('should generate customTemplate node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.CustomTemplate, 'Test CustomTemplate');

        expect(result).toMatchObject({
          type: ENodeType.CustomTemplate,
          data: {
            properties: {
              platform: '',
              template: '',
              fallbackPath: false,
              ports: { builtIn: {} },
            },
          },
        });
      });
    });

    describe('Gai node', () => {
      it('should generate gai node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.Gai, 'Test Gai');

        expect(result).toMatchObject({
          type: ENodeType.Gai,
          data: {
            properties: {
              prompt: '',
              variableName: '',
              outputType: 'text',
              promptType: 'system',
              fallbackPath: false,
              ports: { builtIn: {} },
            },
          },
        });
      });
    });

    describe('Script node', () => {
      it('should generate script node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.Script, 'Test Script');

        expect(result).toMatchObject({
          type: ENodeType.Script,
          data: {
            properties: {
              script: '',
              paths: [],
              fallbackPath: true,
              variableName: '',
              ports: { builtIn: {} },
            },
          },
        });
      });
    });

    describe('Dynamic node', () => {
      it('should generate dynamic node with correct properties', () => {
        const result = generateReactFlowNode(ENodeType.Dynamic, 'Test Dynamic');

        expect(result).toMatchObject({
          type: ENodeType.Dynamic,
          data: {
            properties: {
              text: '',
              labelAttribute: '',
              inputVariable: '',
              outputVariable: '',
              valueAttribute: '',
              buttons: [],
              ports: { builtIn: {}, dynamic: {} },
            },
          },
        });
      });
    });

    describe('Unknown node type', () => {
      it('should handle unknown node type gracefully', () => {
        const result = generateReactFlowNode('unknown' as ENodeType, 'Test Unknown');

        expect(result).toMatchObject({
          id: 'mockId',
          type: 'unknown',
          position: { x: 0, y: 0 },
          data: {
            properties: {},
          },
        });
        expect(result.width).toBeUndefined();
      });
    });

    describe('Edge cases', () => {
      it('should handle empty name', () => {
        const result = generateReactFlowNode(ENodeType.Text, '');

        expect(result.data.properties.texts).toEqual(['']);
      });

      it('should handle special characters in name', () => {
        const result = generateReactFlowNode(ENodeType.Text, 'Test & Special < > " \' Characters');

        expect(result.data.properties.texts).toEqual(['Test & Special < > " \' Characters']);
      });

      it('should handle very long name', () => {
        const longName = 'A'.repeat(1000);
        const result = generateReactFlowNode(ENodeType.Text, longName);

        expect(result.data.properties.texts).toEqual([longName]);
      });

      it('should generate unique IDs for each call', () => {
        const result1 = generateReactFlowNode(ENodeType.Text, 'Test 1');
        const result2 = generateReactFlowNode(ENodeType.Text, 'Test 2');

        expect(result1.id).toBe('mockId');
        expect(result2.id).toBe('mockId');
      });

      it('should generate unique short IDs for buttons', () => {
        const result = generateReactFlowNode(ENodeType.Buttons, 'Test Buttons');

        expect(result.data.properties.buttons[0].id).toBe('mockShortId');
        expect(result.data.properties.buttons[0].port).toBe('mockShortId');
      });

      it('should generate unique short IDs for conditions', () => {
        const result = generateReactFlowNode(ENodeType.Condition, 'Test Condition');

        expect(result.data.properties.expressions[0].port).toBe('mockShortId');
      });

      it('should generate unique short IDs for choices', () => {
        const result = generateReactFlowNode(ENodeType.Choice, 'Test Choice');

        expect(result.data.properties.choices[0].id).toBe('mockShortId');
        expect(result.data.properties.choices[0].port).toBe('mockShortId');
      });

      it('should generate unique short IDs for carousel', () => {
        const result = generateReactFlowNode(ENodeType.Carousel, 'Test Carousel');

        expect(result.data.properties.cards[0].id).toBe('mockShortId');
        expect(result.data.properties.cards[0].port).toBe('mockShortId');
      });
    });

    describe('Port configurations', () => {
      it('should have builtIn ports for all step nodes', () => {
        const stepNodes = [
          ENodeType.Text,
          ENodeType.Email,
          ENodeType.Buttons,
          ENodeType.Form,
          ENodeType.QnA,
          ENodeType.Capture,
          ENodeType.Variables,
          ENodeType.Carousel,
          ENodeType.Api,
          ENodeType.Condition,
          ENodeType.Entity,
          ENodeType.CustomTemplate,
          ENodeType.Gai,
          ENodeType.Script,
        ];

        stepNodes.forEach((nodeType) => {
          const result = generateReactFlowNode(nodeType, 'Test');
          expect(result.data.properties.ports.builtIn).toBeDefined();
        });
      });

      it('should have dynamic ports for nodes that support them', () => {
        const dynamicNodes = [
          ENodeType.Buttons,
          ENodeType.Choice,
          ENodeType.Carousel,
          ENodeType.Condition,
          ENodeType.Dynamic,
        ];

        dynamicNodes.forEach((nodeType) => {
          const result = generateReactFlowNode(nodeType, 'Test');
          expect(result.data.properties.ports.dynamic).toBeDefined();
        });
      });
    });
  });

  describe('generateReactFlowEdge', () => {
    const source = 'source-node-id';
    const target = 'target-node-id';
    const sourceHandle = 'source-handle-id';

    it('should generate Action edge with correct properties', () => {
      const result = generateReactFlowEdge(source, target, sourceHandle, EdgeTypes.Action);

      expect(result).toMatchObject({
        id: 'mockShortId',
        source,
        target,
        sourceHandle,
        selectable: false,
        deletable: false,
        type: EdgeTypes.Action,
        style: {
          stroke: '#B2B7E2',
          strokeWidth: 2,
        },
        data: {
          hover: false,
          type: EdgeTypes.Action,
          setting: {
            color: '#B2B7E2',
          },
        },
        markerEnd: {
          type: 'arrowclosed',
          color: '#B2B7E2',
        },
      });
    });

    it('should generate Menu edge with correct properties', () => {
      const result = generateReactFlowEdge(source, target, sourceHandle, EdgeTypes.Menu);

      expect(result).toMatchObject({
        id: 'mockShortId',
        source,
        target,
        sourceHandle,
        type: EdgeTypes.Menu,
        style: {
          stroke: '#B2B7E2',
          strokeWidth: 2,
        },
        data: {
          hover: false,
          type: EdgeTypes.Menu,
          setting: {
            color: '#B2B7E2',
          },
        },
      });
      expect(result.selectable).toBeUndefined();
      expect(result.deletable).toBeUndefined();
      expect(result.markerEnd).toBeUndefined();
    });

    it('should generate Goto edge with correct properties', () => {
      const result = generateReactFlowEdge(source, target, sourceHandle, EdgeTypes.Goto);

      expect(result).toMatchObject({
        id: 'mockShortId',
        source,
        target,
        sourceHandle,
        type: EdgeTypes.Goto,
        style: {
          stroke: '#B2B7E2',
          strokeWidth: 2,
        },
        data: {
          hover: false,
          type: EdgeTypes.Goto,
          setting: {
            color: '#B2B7E2',
          },
        },
        markerEnd: {
          type: 'arrowclosed',
          color: '#B2B7E2',
        },
      });
      expect(result.selectable).toBeUndefined();
      expect(result.deletable).toBeUndefined();
    });

    it('should use first color from COLOR_PALETTE_KEYS', () => {
      const result = generateReactFlowEdge(source, target, sourceHandle, EdgeTypes.Action);

      expect(result.style?.stroke).toBe('#B2B7E2');
      expect(result.data?.setting.color).toBe('#B2B7E2');
      expect((result.markerEnd as any)?.color).toBe('#B2B7E2');
    });

    it('should handle different source and target IDs', () => {
      const customSource = 'custom-source';
      const customTarget = 'custom-target';
      const customHandle = 'custom-handle';

      const result = generateReactFlowEdge(
        customSource,
        customTarget,
        customHandle,
        EdgeTypes.Action
      );

      expect(result.source).toBe(customSource);
      expect(result.target).toBe(customTarget);
      expect(result.sourceHandle).toBe(customHandle);
    });

    it('should generate unique IDs for each edge', () => {
      const result1 = generateReactFlowEdge(source, target, sourceHandle, EdgeTypes.Action);
      const result2 = generateReactFlowEdge(source, target, sourceHandle, EdgeTypes.Menu);

      expect(result1.id).toBe('mockShortId');
      expect(result2.id).toBe('mockShortId');
    });

    it('should set correct stroke width', () => {
      const result = generateReactFlowEdge(source, target, sourceHandle, EdgeTypes.Action);

      expect(result.style?.strokeWidth).toBe(2);
    });

    it('should set hover to false by default', () => {
      const result = generateReactFlowEdge(source, target, sourceHandle, EdgeTypes.Action);

      expect(result.data?.hover).toBe(false);
    });

    it('should set correct marker type for Action edge', () => {
      const result = generateReactFlowEdge(source, target, sourceHandle, EdgeTypes.Action);

      expect((result.markerEnd as any)?.type).toBe('arrowclosed');
    });

    it('should set correct marker type for Goto edge', () => {
      const result = generateReactFlowEdge(source, target, sourceHandle, EdgeTypes.Goto);

      expect((result.markerEnd as any)?.type).toBe('arrowclosed');
    });

    it('should not have markerEnd for Menu edge', () => {
      const result = generateReactFlowEdge(source, target, sourceHandle, EdgeTypes.Menu);

      expect(result.markerEnd).toBeUndefined();
    });

    it('should not have selectable and deletable for Menu edge', () => {
      const result = generateReactFlowEdge(source, target, sourceHandle, EdgeTypes.Menu);

      expect(result.selectable).toBeUndefined();
      expect(result.deletable).toBeUndefined();
    });

    it('should not have selectable and deletable for Goto edge', () => {
      const result = generateReactFlowEdge(source, target, sourceHandle, EdgeTypes.Goto);

      expect(result.selectable).toBeUndefined();
      expect(result.deletable).toBeUndefined();
    });

    it('should have selectable and deletable set to false for Action edge', () => {
      const result = generateReactFlowEdge(source, target, sourceHandle, EdgeTypes.Action);

      expect(result.selectable).toBe(false);
      expect(result.deletable).toBe(false);
    });
  });
});
