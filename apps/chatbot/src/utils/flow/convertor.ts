import {
  ACTION_NODE_LARGE_WIDTH,
  ACTION_NODE_SMALL_WIDTH,
  BASIC_NODES,
  BUILT_IN_PORT_ELSE,
  BUILT_IN_PORT_FAILURE,
  BUILT_IN_PORT_FINISH,
  BUILT_IN_PORT_NEXT,
  BUILT_IN_PORT_SUCCESS,
  COLOR_PALETTE_KEYS,
  NEED_REVALIDATE_DATA_NODES,
  SINGLE_NODES,
  STEP_NODES,
  WIDTH_NODE_BLOCK,
  WIDTH_STEP,
} from '@/constants/flow';
import {
  BUILT_IN_PORT,
  DYNAMIC_PORT,
  ENodeType,
  EdgeTypes,
  type NodesObject,
  PORT_TYPE,
  type PortTypes,
  type TBlockData,
  type TBuiltInNode,
  type TButton,
  type TCarousel,
  type TChoice,
  type TCondition,
  type TEdge,
  type TPath,
  type TPort,
  type TPorts,
  type TReactFlowNode,
} from '@/types';
import { MarkerType } from '@xyflow/react';
import { get, isEmpty, omit, set } from 'lodash';
import { randomShortId } from '../id';
import {
  getActionNodesInDecaData,
  getKeyDynamicData,
  getStepPositionY,
  getStepsInstanceByParent,
  isEnabledActionHandler,
  isLargeWidthAction,
} from './shared';

export const convertClientFlowToApiFlow = (nodes: TReactFlowNode[], edges: TEdge[]) => {
  const nodesObject: NodesObject = {};

  nodes.forEach((node) => {
    if (node.type === ENodeType.Menu) return;
    const data = node.data?.properties;
    data.ports = { [BUILT_IN_PORT]: {} };
    if (node.type === ENodeType.Block) {
      const stepInstances = getStepsInstanceByParent(nodes, node);
      data.steps = stepInstances.getIds();
    }
    nodesObject[node.id] = {
      ID: node.id,
      type: node.type,
      parentId: node.parentId,
      portId: data.portId,
      coords: [node.position.x, node.position.y],
      data: omit(data, 'portId', 'destinationSourceHandle'),
    };
  });

  edges.forEach((edge) => {
    const { source: nodeId, sourceHandle, type } = edge;

    if (sourceHandle && type !== EdgeTypes.Menu) {
      set(nodesObject[nodeId], `data.ports.${sourceHandle}`, {
        id: edge.id,
        type: edge.type,
        target: edge.target,
        setting: {
          color: edge.data?.setting?.color,
        },
      });
    }
  });

  return nodesObject;
};

export const convertApiDataToReactFlowData = (
  nodesObject: NodesObject
): {
  nodes: TReactFlowNode[];
  edges: TEdge[];
} => {
  let edges: TEdge[] = [];
  const nodes: TReactFlowNode[] = [];

  Object.values(nodesObject).forEach((n) => {
    // Skip step and action nodes, It will be handled inside the parent node
    if (STEP_NODES.includes(n.type as ENodeType) || n.type === ENodeType.Action) {
      return;
    }

    if ([...SINGLE_NODES, ...BASIC_NODES].includes(n.type)) {
      const [x, y] = n.coords;
      const node: TReactFlowNode = {
        id: n.ID,
        type: n.type,
        data: {
          properties: n.data,
        },
        position: { x, y },
      };
      if (n.data.ports) {
        const reactFlowEdges = convertApiDataEdgeToReactFlowEdge(n.ID, n.data.ports);
        edges = edges.concat(reactFlowEdges);
      }
      const { nodes: actionNodes, edges: actionEdges } = convertActionNodesFromDecaDataToFlowData(
        n.ID,
        nodesObject
      );
      edges = edges.concat(actionEdges);
      return nodes.push(node, ...actionNodes);
    }

    if (n.type === ENodeType.Block) {
      const [x, y] = n.coords;
      const data = n.data as TBlockData;
      const parent: TReactFlowNode<ENodeType.Block> = {
        id: n.ID,
        type: n.type,
        data: {
          properties: data,
        },
        width: WIDTH_NODE_BLOCK,
        position: { x, y },
      };
      set(parent, 'data.properties.setting.line.richMenu', {
        id: get(data, 'setting.line.richMenu.id', ''),
        enabled: get(data, 'setting.line.richMenu.enabled', false),
        forceDisable: get(data, 'setting.line.richMenu.forceDisable', false),
      });
      const steps = data.steps.filter((stepID) => nodesObject[stepID]);

      if (!steps?.length) {
        delete nodesObject[n.ID];
        return;
      }
      data.steps = steps;

      /* 
        The ordering of a block and steps inside the block must stand next to each other 
        https://github.com/xyflow/xyflow/issues/3432
      */

      const children = steps.map((stepID, index) => {
        const step = nodesObject[stepID];
        // Re-update parentId
        step.parentId = n.ID;
        const posX = get(step, 'coords', [0, 0])[0];
        const posY = getStepPositionY({
          steps: steps as unknown as TReactFlowNode[],
          index,
        });

        if (step.data.ports) {
          const reactFlowEdges = convertApiDataEdgeToReactFlowEdge(step.ID, step.data.ports);
          edges = edges.concat(reactFlowEdges);
        }

        if (NEED_REVALIDATE_DATA_NODES.includes(step.type)) {
          const keyDynamicData = getKeyDynamicData(step.type);
          const normalizedStepData = get(step.data, keyDynamicData, []).map((data) => ({
            ...data,
            id: data.id ?? randomShortId(),
          }));
          set(step.data, keyDynamicData, normalizedStepData);
        }
        const newNode: TReactFlowNode = {
          id: step.ID,
          type: step.type,
          parentId: n.ID,
          data: {
            canHandle: index === steps.length - 1,
            properties: step.data,
            stepIndex: index,
          },
          width: WIDTH_STEP,
          height: step.height,
          position: { x: posX, y: posY },
        };

        return newNode;
      });

      const { nodes: actions, edges: actionEdges } = convertActionNodesFromDecaDataToFlowData(
        steps.at(-1) || steps[0],
        nodesObject
      );
      edges = edges.concat(actionEdges);

      return nodes.push(parent, ...children, ...actions);
    }
  });

  return { nodes, edges };
};

const getEdgeType = (portType: PortTypes) => {
  switch (portType) {
    case PORT_TYPE.action:
      return EdgeTypes.Action;
    case PORT_TYPE.menu:
      return EdgeTypes.Menu;
    default:
      return EdgeTypes.Goto;
  }
};

const convertApiDataEdgeToReactFlowEdge = (id: string, ports: TPorts) => {
  const edges: TEdge[] = [];
  Object.entries(ports).forEach(([portType, port]) => {
    Object.entries(port).forEach(([portId, data]) => {
      if (!data?.target) return;
      const edgeType = getEdgeType(data.type);
      const color = data.setting?.color ?? COLOR_PALETTE_KEYS[0];
      edges.push({
        id: data.id,
        target: data.target,
        markerEnd: {
          type: MarkerType.ArrowClosed,
          color,
        },
        data: {
          setting: {
            color,
          },
          hover: false,
          type: edgeType,
        },
        style: {
          stroke: color,
          strokeWidth: 2,
        },
        source: id,
        sourceHandle: `${portType}.${portId}`,
        type: edgeType,
        ...(edgeType === EdgeTypes.Action && {
          selectable: false,
          deletable: false,
        }),
      });
    });
  });
  return edges;
};

const getDynamicData = (
  nodesObject: NodesObject,
  nodeId: string
): Array<TButton | TCondition | TCarousel | TChoice | TPath | TButton> => {
  const currentNode = nodesObject[nodeId];
  const dynamicData = get(
    nodesObject[currentNode.ID],
    `data.${getKeyDynamicData(currentNode.type)}`,
    []
  );
  return dynamicData;
};

const createActionNodesForPort = (
  decaActionNodes: TBuiltInNode[],
  firstActionNode: TBuiltInNode,
  portId: string
) => {
  const actions: TReactFlowNode<ENodeType.Action>[] = [];

  decaActionNodes.forEach((action) => {
    if (firstActionNode.coords[1] === action.coords[1]) {
      const actionType = get(action.data, 'actionType', '');
      actions.push({
        id: action.ID,
        type: ENodeType.Action,
        parentId: action.parentId,
        draggable: false,
        style: {
          width: isLargeWidthAction(actionType) ? ACTION_NODE_LARGE_WIDTH : ACTION_NODE_SMALL_WIDTH,
        },
        data: {
          canHandle: isEnabledActionHandler(actionType),
          properties: {
            ...action.data,
            portId,
            setting: { color: action.data.setting?.color ?? COLOR_PALETTE_KEYS[0] },
          },
        },
        position: { x: action.coords[0], y: action.coords[1] },
      });
    }
  });
  return actions;
};

export const convertActionNodesFromDecaDataToFlowData = (
  nodeId: string,
  nodesObject: NodesObject
) => {
  const nodes: TReactFlowNode<ENodeType.Action>[] = [];
  const edges: TEdge[] = [];
  const decaActionNodes = getActionNodesInDecaData(nodeId, nodesObject);
  if (isEmpty(decaActionNodes)) return { nodes: [], edges: [] };

  const dynamicData = getDynamicData(nodesObject, nodeId);
  const ports = get(nodesObject[nodeId], 'data.ports', {});
  const dynamicPorts = get(ports, `${DYNAMIC_PORT}`, {});

  // Handle dynamic ports
  dynamicData.forEach((data) => {
    const portId = get(data, 'port', '');
    const portData = dynamicPorts[portId] as TPort;
    if (!portData) return;
    const firstActionNode = nodesObject[portData.target];
    if (!firstActionNode) return;
    const newActions = createActionNodesForPort(
      decaActionNodes,
      firstActionNode,
      `${DYNAMIC_PORT}.${portId}`
    );
    nodes.push(...newActions);
  });

  // Handle built-in ports
  const elsePort = get(ports, `${BUILT_IN_PORT_ELSE}`) as TPort;
  const nextPort = get(ports, `${BUILT_IN_PORT_NEXT}`) as TPort;
  const failurePort = get(ports, `${BUILT_IN_PORT_FAILURE}`) as TPort;
  const successPort = get(ports, `${BUILT_IN_PORT_SUCCESS}`) as TPort;
  const finishPort = get(ports, `${BUILT_IN_PORT_FINISH}`) as TPort;

  const listBuiltInPorts = [elsePort, nextPort, failurePort, successPort, finishPort];
  const listBuiltInPortIds = [
    PORT_TYPE.else,
    PORT_TYPE.next,
    PORT_TYPE.failure,
    PORT_TYPE.success,
    PORT_TYPE.finish,
  ];

  listBuiltInPorts.forEach((port, index) => {
    if (!port) return;
    const firstActionNode = nodesObject[port.target];
    if (!firstActionNode) return;
    const newActions = createActionNodesForPort(
      decaActionNodes,
      firstActionNode,
      `${BUILT_IN_PORT}.${listBuiltInPortIds[index]}`
    );
    nodes.push(...newActions);
  });

  decaActionNodes.forEach((action) => {
    if (action.data.ports) {
      const reactFlowEdges = convertApiDataEdgeToReactFlowEdge(action.ID, action.data.ports);
      edges.push(...reactFlowEdges);
    }
  });

  return { nodes, edges };
};
