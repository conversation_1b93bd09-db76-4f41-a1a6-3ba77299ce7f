export const getMenuPosition = (
  labelX: number,
  sourceX: number,
  sourceY: number,
  targetX: number,
  targetY: number,
  offset: number
) => {
  let positionY = 0;

  if (sourceX >= targetX) {
    positionY = (sourceY + targetY) / 2 - offset;
  }

  if (sourceX < targetX) {
    positionY = sourceY > targetY ? targetY : sourceY;
    positionY -= offset;
  }

  return {
    x: labelX,
    y: positionY,
  };
};
