import type { XYPosition } from '@xyflow/react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the dependencies
vi.mock('@/constants/flow', () => ({
  ACTION_NODE_ARROW_OFFSET: 20,
  ACTION_NODE_HEIGHT: 32,
  ACTION_NODE_LARGE_WIDTH: 200,
  ACTION_NODE_SMALL_WIDTH: 100,
  DefaultActionOptions: ['intent', 'node', 'end', 'url', 'variable'],
  STEP_NODES: ['text', 'email', 'buttons', 'carousel'],
  COLOR_PALETTE_KEYS: ['#B2B7E2', '#94C4FA', '#E0C6D9', '#FDC8CB', '#C6D6C9', '#FFDB7A', '#D6B4E9'],
}));

vi.mock('@/types', () => ({
  EnumActionTypes: {
    intent: 'intent',
    node: 'node',
    goto: 'goto',
    end: 'end',
    postback: 'postback',
    url: 'url',
    variable: 'variable',
  },
  EnumIntentScope: {
    GLOBAL: 'GLOBAL',
    LOCAL: 'LOCAL',
  },
  ENodeType: {
    Action: 'action',
    Text: 'text',
    Email: 'email',
    Buttons: 'buttons',
    Carousel: 'carousel',
    Block: 'block',
  },
}));

vi.mock('../id', () => ({
  randomId: vi.fn(() => 'random-id-123'),
}));

vi.mock('./shared', () => ({
  getAllActionNodes: vi.fn(),
}));

// Import after mocking
import {
  generateActionNodeByType,
  getActionNodeByPortId,
  getActionNodeBySourceAndSourceHandle,
  getActionNodesByPortId,
  getActionOffsetVertical,
  getActionType,
  getAvailableActionOptions,
  getNextActionNodePosition,
  updateActionPositionX,
  updateActionPositionY,
} from './action';

import {
  ACTION_NODE_ARROW_OFFSET,
  ACTION_NODE_LARGE_WIDTH,
  ACTION_NODE_SMALL_WIDTH,
  DefaultActionOptions,
} from '@/constants/flow';
import { ENodeType, EnumActionTypes, EnumIntentScope } from '@/types';
import { getAllActionNodes } from './shared';

describe('Flow Action Utils', () => {
  const mockReactFlowInstance = {
    getNodes: vi.fn(),
    getEdges: vi.fn(),
    getNode: vi.fn(),
    screenToFlowPosition: vi.fn(),
  } as any;

  // Mock document.getElementById
  const mockGetElementById = vi.fn();
  Object.defineProperty(document, 'getElementById', {
    value: mockGetElementById,
    writable: true,
  });

  // Mock screenToFlowPosition function
  const mockScreenToFlowPosition = vi.fn(({ x, y }) => ({ x: x + 100, y: y + 100 }));

  const mockNodes = [
    {
      id: 'node1',
      type: ENodeType.Action,
      position: { x: 100, y: 100 },
      data: { properties: { actionType: 'intent', portId: 'port1' } },
      measured: { width: 100, height: 32 },
    },
    {
      id: 'node2',
      type: ENodeType.Action,
      position: { x: 200, y: 100 },
      data: { properties: { actionType: 'url', portId: 'port1' } },
      measured: { width: 200, height: 32 },
    },
    {
      id: 'node3',
      type: ENodeType.Action,
      position: { x: 300, y: 200 },
      data: { properties: { actionType: 'node', portId: 'port2' } },
      measured: { width: 200, height: 32 },
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockReactFlowInstance.getNodes.mockReturnValue(mockNodes);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('getActionOffsetVertical', () => {
    it('should calculate action offset vertical correctly', () => {
      const mockElement = {
        getBoundingClientRect: vi.fn(() => ({ x: 50, y: 100 })),
        offsetHeight: 40,
      };
      const getElementByIdSpy = vi
        .spyOn(document, 'getElementById')
        .mockReturnValue(mockElement as any);

      const mockNode = {
        id: 'node1',
        internals: { positionAbsolute: { x: 100, y: 200 } },
      } as any;

      mockScreenToFlowPosition.mockReturnValue({ x: 60, y: 110 });

      const result = getActionOffsetVertical({
        node: mockNode,
        sourceHandle: 'handle1',
        screenToFlowPosition: mockScreenToFlowPosition,
      });

      expect(getElementByIdSpy).toHaveBeenCalledWith('node1.handle1');
      expect(mockScreenToFlowPosition).toHaveBeenCalledWith({
        x: 50,
        y: 100,
      });
      // Math.abs(200 - 110 + 16 - 20) = Math.abs(86) = 86
      expect(result).toBe(86);
    });

    it('should handle missing DOM element', () => {
      vi.spyOn(document, 'getElementById').mockReturnValue(null);

      const mockNode = {
        id: 'node1',
        internals: { positionAbsolute: { x: 100, y: 200 } },
      } as any;

      mockScreenToFlowPosition.mockReturnValue({ x: 0, y: 0 });

      const result = getActionOffsetVertical({
        node: mockNode,
        sourceHandle: 'handle1',
        screenToFlowPosition: mockScreenToFlowPosition,
      });

      expect(result).toBe(216); // Math.abs(200 - 0 + 16 - 0)
    });

    it('should handle node without positionAbsolute', () => {
      const mockElement = {
        getBoundingClientRect: vi.fn(() => ({ x: 50, y: 100 })),
        offsetHeight: 40,
      };
      vi.spyOn(document, 'getElementById').mockReturnValue(mockElement as any);

      const mockNode = {
        id: 'node1',
        internals: {},
      } as any;

      mockScreenToFlowPosition.mockReturnValue({ x: 60, y: 110 });

      const result = getActionOffsetVertical({
        node: mockNode,
        sourceHandle: 'handle1',
        screenToFlowPosition: mockScreenToFlowPosition,
      });

      // Math.abs(0 - 110 + 32/2 - 40/2) = Math.abs(0 - 110 + 16 - 20) = Math.abs(-114) = 114
      expect(result).toBe(114);
    });

    it('should handle element with zero offsetHeight', () => {
      const mockElement = {
        getBoundingClientRect: vi.fn(() => ({ x: 50, y: 100 })),
        offsetHeight: 0,
      };
      vi.spyOn(document, 'getElementById').mockReturnValue(mockElement as any);

      const mockNode = {
        id: 'node1',
        internals: { positionAbsolute: { x: 100, y: 200 } },
      } as any;

      mockScreenToFlowPosition.mockReturnValue({ x: 60, y: 110 });

      const result = getActionOffsetVertical({
        node: mockNode,
        sourceHandle: 'handle1',
        screenToFlowPosition: mockScreenToFlowPosition,
      });

      // Math.abs(200 - 110 + 16 - 0) = Math.abs(106) = 106
      expect(result).toBe(106);
    });

    it('should handle element with undefined getBoundingClientRect', () => {
      const mockElement = {
        getBoundingClientRect: vi.fn(() => undefined),
        offsetHeight: 40,
      };
      vi.spyOn(document, 'getElementById').mockReturnValue(mockElement as any);

      const mockNode = {
        id: 'node1',
        internals: { positionAbsolute: { x: 100, y: 200 } },
      } as any;

      mockScreenToFlowPosition.mockReturnValue({ x: 0, y: 0 });

      const result = getActionOffsetVertical({
        node: mockNode,
        sourceHandle: 'handle1',
        screenToFlowPosition: mockScreenToFlowPosition,
      });

      // Math.abs(200 - 0 + 16 - 20) = Math.abs(196) = 196
      expect(result).toBe(196);
    });
  });

  describe('getAvailableActionOptions', () => {
    it('should return default options when action has no parentId', () => {
      const actionNode = {
        id: 'action1',
        parentId: undefined,
        data: { properties: {} },
      } as any;

      const result = getAvailableActionOptions(mockNodes, actionNode);
      expect(result).toEqual(DefaultActionOptions);
    });

    it('should filter out used action types', () => {
      const actionNode = {
        id: 'action1',
        parentId: 'parent1',
        data: { properties: { portId: 'port1' } },
      } as any;

      const testNodes = [
        {
          id: 'node1',
          type: ENodeType.Action,
          parentId: 'parent1',
          position: { x: 100, y: 100 },
          data: { properties: { actionType: 'intent', portId: 'port1' } },
        },
        {
          id: 'node2',
          type: ENodeType.Action,
          parentId: 'parent1',
          position: { x: 200, y: 100 },
          data: { properties: { actionType: 'url', portId: 'port1' } },
        },
      ];

      // Mock getAllActionNodes to return the test nodes
      (getAllActionNodes as any).mockReturnValue(testNodes);

      const result = getAvailableActionOptions(testNodes, actionNode);
      expect(result).toEqual(['node', 'end', 'variable']);
      expect(getAllActionNodes).toHaveBeenCalledWith(testNodes, ['parent1']);
    });

    it('should handle missing portId in action node', () => {
      const actionNode = {
        id: 'action1',
        parentId: 'parent1',
        data: { properties: {} },
      } as any;

      // Mock getAllActionNodes to return empty array
      (getAllActionNodes as any).mockReturnValue([]);

      const result = getAvailableActionOptions([], actionNode);
      expect(result).toEqual(DefaultActionOptions);
      expect(getAllActionNodes).toHaveBeenCalledWith([], ['parent1']);
    });

    it('should handle getAllActionNodes returning empty array', () => {
      const actionNode = {
        id: 'action1',
        parentId: 'parent1',
        data: { properties: { portId: 'port1' } },
      } as any;

      // Mock getAllActionNodes to return empty array
      (getAllActionNodes as any).mockReturnValue([]);

      const result = getAvailableActionOptions(mockNodes, actionNode);
      expect(result).toEqual(DefaultActionOptions);
      expect(getAllActionNodes).toHaveBeenCalledWith(mockNodes, ['parent1']);
    });

    it('should handle getAllActionNodes returning nodes with different parentIds', () => {
      const actionNode = {
        id: 'action1',
        parentId: 'parent1',
        data: { properties: { portId: 'port1' } },
      } as any;

      const testNodes = [
        {
          id: 'node1',
          type: ENodeType.Action,
          parentId: 'parent1',
          position: { x: 100, y: 100 },
          data: { properties: { actionType: 'intent', portId: 'port1' } },
        },
        {
          id: 'node2',
          type: ENodeType.Action,
          parentId: 'parent2', // Different parentId
          position: { x: 200, y: 100 },
          data: { properties: { actionType: 'url', portId: 'port1' } },
        },
        {
          id: 'node3',
          type: ENodeType.Action,
          parentId: 'parent1',
          position: { x: 300, y: 200 },
          data: { properties: { actionType: 'node', portId: 'port2' } }, // Different portId
        },
      ];

      // Mock getAllActionNodes to return only nodes with parentId 'parent1'
      (getAllActionNodes as any).mockReturnValue([
        testNodes[0], // node1 with parentId 'parent1' and portId 'port1'
        testNodes[2], // node3 with parentId 'parent1' but different portId 'port2'
      ]);

      const result = getAvailableActionOptions(testNodes, actionNode);

      // Should filter out 'intent' (from node1) but keep others since node3 has different portId
      expect(result).toEqual(['node', 'end', 'url', 'variable']);
      expect(getAllActionNodes).toHaveBeenCalledWith(testNodes, ['parent1']);
    });

    it('should handle getAllActionNodes returning nodes but none match the portId', () => {
      const actionNode = {
        id: 'action1',
        parentId: 'parent1',
        data: { properties: { portId: 'port1' } },
      } as any;

      const testNodes = [
        {
          id: 'node1',
          type: ENodeType.Action,
          parentId: 'parent1',
          position: { x: 100, y: 100 },
          data: { properties: { actionType: 'intent', portId: 'port2' } }, // Different portId
        },
        {
          id: 'node2',
          type: ENodeType.Action,
          parentId: 'parent1',
          position: { x: 200, y: 100 },
          data: { properties: { actionType: 'url', portId: 'port3' } }, // Different portId
        },
      ];

      // Mock getAllActionNodes to return all nodes with parentId 'parent1'
      (getAllActionNodes as any).mockReturnValue(testNodes);

      const result = getAvailableActionOptions(testNodes, actionNode);

      // Should return all default options since no nodes match the portId 'port1'
      expect(result).toEqual(DefaultActionOptions);
      expect(getAllActionNodes).toHaveBeenCalledWith(testNodes, ['parent1']);
    });

    it('should handle getAllActionNodes returning nodes with mixed action types and portIds', () => {
      const actionNode = {
        id: 'action1',
        parentId: 'parent1',
        data: { properties: { portId: 'port1' } },
      } as any;

      const testNodes = [
        {
          id: 'node1',
          type: ENodeType.Action,
          parentId: 'parent1',
          position: { x: 100, y: 100 },
          data: { properties: { actionType: 'intent', portId: 'port1' } },
        },
        {
          id: 'node2',
          type: ENodeType.Action,
          parentId: 'parent1',
          position: { x: 200, y: 100 },
          data: { properties: { actionType: 'url', portId: 'port1' } },
        },
        {
          id: 'node3',
          type: ENodeType.Action,
          parentId: 'parent1',
          position: { x: 300, y: 200 },
          data: { properties: { actionType: 'variable', portId: 'port2' } }, // Different portId
        },
        {
          id: 'node4',
          type: ENodeType.Action,
          parentId: 'parent1',
          position: { x: 400, y: 200 },
          data: { properties: { actionType: 'end', portId: 'port1' } },
        },
      ];

      // Mock getAllActionNodes to return all nodes with parentId 'parent1'
      (getAllActionNodes as any).mockReturnValue(testNodes);

      const result = getAvailableActionOptions(testNodes, actionNode);

      // Should filter out 'intent', 'url', and 'end' (from nodes with portId 'port1')
      // but keep 'variable' and 'node' since 'variable' has different portId
      expect(result).toEqual(['node', 'variable']);
      expect(getAllActionNodes).toHaveBeenCalledWith(testNodes, ['parent1']);
    });
  });

  describe('getActionType', () => {
    it('should return action type from node', () => {
      const actionNode = {
        data: { properties: { actionType: EnumActionTypes.intent } },
      } as any;

      const result = getActionType(actionNode);
      expect(result).toBe(EnumActionTypes.intent);
    });

    it('should return empty string when action type is missing', () => {
      const actionNode = {
        data: { properties: {} },
      } as any;

      const result = getActionType(actionNode);
      expect(result).toBe('');
    });

    it('should handle undefined action node', () => {
      const result = getActionType(undefined);
      expect(result).toBe('');
    });

    it('should handle node with missing data', () => {
      const actionNode = {} as any;
      const result = getActionType(actionNode);
      expect(result).toBe('');
    });
  });

  describe('getActionNodesByPortId', () => {
    it('should return action nodes with matching port id', () => {
      const result = getActionNodesByPortId(mockNodes, 'port1');
      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('node1');
      expect(result[1].id).toBe('node2');
    });

    it('should return empty array when no matching nodes found', () => {
      const result = getActionNodesByPortId(mockNodes, 'nonexistent');
      expect(result).toHaveLength(0);
    });

    it('should filter out non-action nodes', () => {
      const testNodes = [
        {
          id: 'node1',
          type: ENodeType.Text,
          position: { x: 100, y: 100 },
          data: { properties: { portId: 'port1' } },
        },
        {
          id: 'node2',
          type: ENodeType.Action,
          position: { x: 200, y: 100 },
          data: { properties: { portId: 'port1' } },
        },
      ];

      const result = getActionNodesByPortId(testNodes, 'port1');
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('node2');
    });
  });

  describe('generateActionNodeByType', () => {
    const baseParams = {
      parentId: 'parent1',
      position: { x: 100, y: 200 } as XYPosition,
      portId: 'port1',
    };

    it('should generate intent action node', () => {
      const result = generateActionNodeByType({
        ...baseParams,
        type: EnumActionTypes.intent,
      });

      expect(result.id).toBe('random-id-123');
      expect(result.type).toBe(ENodeType.Action);
      expect(result.position).toEqual(baseParams.position);
      expect(result.parentId).toBe(baseParams.parentId);
      expect(result.draggable).toBe(false);
      expect(result.width).toBe(ACTION_NODE_SMALL_WIDTH);
      expect(result.data.canHandle).toBe(false);
      expect(result.data.properties.actionType).toBe(EnumActionTypes.intent);
      expect(result.data.properties.intentScope).toBe(EnumIntentScope.GLOBAL);
      expect(result.data.properties.portId).toBe(baseParams.portId);
    });

    it('should generate node action node', () => {
      const result = generateActionNodeByType({
        ...baseParams,
        type: EnumActionTypes.node,
      });

      expect(result.width).toBe(ACTION_NODE_LARGE_WIDTH);
      expect(result.data.canHandle).toBe(false);
      expect(result.data.properties.actionType).toBe(EnumActionTypes.node);
    });

    it('should generate end action node', () => {
      const result = generateActionNodeByType({
        ...baseParams,
        type: EnumActionTypes.end,
      });

      expect(result.width).toBe(ACTION_NODE_SMALL_WIDTH);
      expect(result.data.canHandle).toBe(false);
      expect(result.data.properties.actionType).toBe(EnumActionTypes.end);
    });

    it('should generate url action node', () => {
      const result = generateActionNodeByType({
        ...baseParams,
        type: EnumActionTypes.url,
      });

      expect(result.width).toBe(ACTION_NODE_LARGE_WIDTH);
      expect(result.data.canHandle).toBe(true);
      expect(result.data.properties.actionType).toBe(EnumActionTypes.url);
    });

    it('should generate variable action node', () => {
      const result = generateActionNodeByType({
        ...baseParams,
        type: EnumActionTypes.variable,
      });

      expect(result.width).toBe(ACTION_NODE_LARGE_WIDTH);
      expect(result.data.canHandle).toBe(true);
      expect(result.data.properties.actionType).toBe(EnumActionTypes.variable);
      expect(result.data.properties.variables).toEqual([]);
    });

    it('should handle unknown action type', () => {
      const result = generateActionNodeByType({
        ...baseParams,
        type: 'unknown' as any,
      });

      expect(result.id).toBe('random-id-123');
      expect(result.type).toBe(ENodeType.Action);
      expect(result.width).toBeUndefined();
      expect(result.data.canHandle).toBeUndefined();
    });

    it('should generate action node with correct default properties', () => {
      const result = generateActionNodeByType({
        ...baseParams,
        type: EnumActionTypes.intent,
      });

      expect(result.data.properties.setting.color).toBe('#B2B7E2'); // First color from COLOR_PALETTE_KEYS
      expect(result.data.properties.portId).toBe(baseParams.portId);
      expect(result.data.properties.actionType).toBe(EnumActionTypes.intent);
    });

    it('should handle variable action type with empty variables array', () => {
      const result = generateActionNodeByType({
        ...baseParams,
        type: EnumActionTypes.variable,
      });

      expect(result.data.properties.variables).toEqual([]);
      expect(result.data.canHandle).toBe(true);
      expect(result.width).toBe(ACTION_NODE_LARGE_WIDTH);
    });

    it('should preserve existing properties when spreading', () => {
      const result = generateActionNodeByType({
        ...baseParams,
        type: EnumActionTypes.intent,
      });

      // Should preserve the setting.color property when spreading
      expect(result.data.properties.setting.color).toBe('#B2B7E2');
      expect(result.data.properties.intentScope).toBe(EnumIntentScope.GLOBAL);
    });
  });

  describe('getNextActionNodePosition', () => {
    it('should calculate position for chaining action', () => {
      const node = {
        id: 'node1',
        type: ENodeType.Action,
        position: { x: 100, y: 200 },
        measured: { width: 150 },
      } as any;

      const result = getNextActionNodePosition(mockScreenToFlowPosition, 'handle1', node);

      expect(result).toEqual({
        x: 100 + 150 + ACTION_NODE_ARROW_OFFSET,
        y: 200,
      });
    });

    it('should calculate position for step node', () => {
      const mockElement = {
        getBoundingClientRect: vi.fn(() => ({ x: 50, y: 100 })),
        offsetHeight: 40,
      };
      vi.spyOn(document, 'getElementById').mockReturnValue(mockElement as any);

      const node = {
        id: 'node1',
        type: ENodeType.Text,
        position: { x: 100, y: 200 },
        measured: { width: 150 },
        internals: { positionAbsolute: { x: 100, y: 200 } },
      } as any;

      mockScreenToFlowPosition.mockReturnValue({ x: 60, y: 110 });

      const result = getNextActionNodePosition(mockScreenToFlowPosition, 'handle1', node);

      expect(result.x).toBe(100 + 150 + ACTION_NODE_ARROW_OFFSET);
      expect(result.y).toBe(86); // Calculated from getActionOffsetVertical
    });

    it('should handle non-step node', () => {
      const mockElement = {
        getBoundingClientRect: vi.fn(() => ({ x: 50, y: 100 })),
        offsetHeight: 40,
      };
      vi.spyOn(document, 'getElementById').mockReturnValue(mockElement as any);

      const node = {
        id: 'node1',
        type: ENodeType.Block,
        position: { x: 100, y: 200 },
        measured: { width: 150 },
        internals: { positionAbsolute: { x: 100, y: 200 } },
      } as any;

      mockScreenToFlowPosition.mockReturnValue({ x: 60, y: 110 });

      const result = getNextActionNodePosition(mockScreenToFlowPosition, 'handle1', node);

      expect(result.x).toBe(0 + 150 + ACTION_NODE_ARROW_OFFSET);
      expect(result.y).toBe(86);
    });

    it('should handle node without measured width', () => {
      const node = {
        id: 'node1',
        type: ENodeType.Action,
        position: { x: 100, y: 200 },
        measured: {},
      } as any;

      const result = getNextActionNodePosition(mockScreenToFlowPosition, 'handle1', node);

      expect(result).toEqual({
        x: 100 + 0 + ACTION_NODE_ARROW_OFFSET,
        y: 200,
      });
    });
  });

  describe('getActionNodeByPortId', () => {
    it('should find node by port id', () => {
      mockReactFlowInstance.getNodes.mockReturnValue([
        { id: 'node1', data: { properties: { portId: 'port1' } } },
        { id: 'node2', data: { properties: { portId: 'port2' } } },
      ]);

      const result = getActionNodeByPortId(mockReactFlowInstance, 'port1');
      expect(result?.id).toBe('node1');
    });

    it('should return undefined when node not found', () => {
      mockReactFlowInstance.getNodes.mockReturnValue([
        { id: 'node1', data: { properties: { portId: 'port1' } } },
      ]);

      const result = getActionNodeByPortId(mockReactFlowInstance, 'nonexistent');
      expect(result).toBeUndefined();
    });

    it('should handle null reactFlowInstance', () => {
      const result = getActionNodeByPortId(null, 'port1');
      expect(result).toBeUndefined();
    });
  });

  describe('getActionNodeBySourceAndSourceHandle', () => {
    it('should find action node by source and handle', () => {
      const mockEdges = [
        { id: 'edge1', source: 'node1', target: 'node2', sourceHandle: 'handle1' },
        { id: 'edge2', source: 'node1', target: 'node3', sourceHandle: 'handle2' },
      ];

      mockReactFlowInstance.getEdges.mockReturnValue(mockEdges);
      mockReactFlowInstance.getNode.mockReturnValue({ id: 'node2', type: ENodeType.Action });

      const result = getActionNodeBySourceAndSourceHandle(
        mockReactFlowInstance,
        'handle1',
        'node1'
      );

      expect(result).toEqual({ id: 'node2', type: ENodeType.Action });
      expect(mockReactFlowInstance.getNode).toHaveBeenCalledWith('node2');
    });

    it('should return undefined when edge not found', () => {
      mockReactFlowInstance.getEdges.mockReturnValue([]);
      mockReactFlowInstance.getNode.mockReturnValue(undefined);

      const result = getActionNodeBySourceAndSourceHandle(
        mockReactFlowInstance,
        'handle1',
        'node1'
      );

      expect(result).toBeUndefined();
      expect(mockReactFlowInstance.getNode).toHaveBeenCalledWith('');
    });

    it('should handle null reactFlowInstance', () => {
      const result = getActionNodeBySourceAndSourceHandle(null, 'handle1', 'node1');
      expect(result).toBeUndefined();
    });

    it('should handle edge without target', () => {
      const mockEdges = [{ id: 'edge1', source: 'node1', sourceHandle: 'handle1' }];

      mockReactFlowInstance.getEdges.mockReturnValue(mockEdges);
      mockReactFlowInstance.getNode.mockReturnValue(undefined);

      const result = getActionNodeBySourceAndSourceHandle(
        mockReactFlowInstance,
        'handle1',
        'node1'
      );

      expect(result).toBeUndefined();
    });
  });

  describe('updateActionPositionX', () => {
    it('should update action positions for step node parent', () => {
      const mapNodesUpdate = new Map();
      const actions = [
        {
          id: 'action1',
          position: { x: 0, y: 100 },
          measured: { width: 100 },
        },
        {
          id: 'action2',
          position: { x: 0, y: 100 },
          measured: { width: 200 },
        },
        {
          id: 'action3',
          position: { x: 0, y: 200 },
          measured: { width: 150 },
        },
      ] as any[];

      const parentNode = {
        id: 'parent1',
        type: ENodeType.Text,
        position: { x: 50, y: 50 },
        measured: { width: 100 },
      } as any;

      updateActionPositionX(mapNodesUpdate, actions, parentNode);

      expect(mapNodesUpdate.size).toBe(3);

      const action1 = mapNodesUpdate.get('action1');
      expect(action1.position.x).toBe(50 + 100 + ACTION_NODE_ARROW_OFFSET);

      const action2 = mapNodesUpdate.get('action2');
      expect(action2.position.x).toBe(
        50 + 100 + ACTION_NODE_ARROW_OFFSET + 100 + ACTION_NODE_ARROW_OFFSET
      );

      const action3 = mapNodesUpdate.get('action3');
      // action3 is at y=200, so it's in a different group and starts from parentNode.position.x
      expect(action3.position.x).toBe(50 + 100 + ACTION_NODE_ARROW_OFFSET);
    });

    it('should handle non-step node parent', () => {
      const mapNodesUpdate = new Map();
      const actions = [
        {
          id: 'action1',
          position: { x: 0, y: 100 },
          measured: { width: 100 },
        },
      ] as any[];

      const parentNode = {
        id: 'parent1',
        type: ENodeType.Block,
        position: { x: 50, y: 50 },
        measured: { width: 100 },
      } as any;

      updateActionPositionX(mapNodesUpdate, actions, parentNode);

      const action1 = mapNodesUpdate.get('action1');
      expect(action1.position.x).toBe(0 + 100 + ACTION_NODE_ARROW_OFFSET);
    });

    it('should handle actions without measured width', () => {
      const mapNodesUpdate = new Map();
      const actions = [
        {
          id: 'action1',
          position: { x: 0, y: 100 },
        },
      ] as any[];

      const parentNode = {
        id: 'parent1',
        type: ENodeType.Text,
        position: { x: 50, y: 50 },
        measured: { width: 100 },
      } as any;

      updateActionPositionX(mapNodesUpdate, actions, parentNode);

      const action1 = mapNodesUpdate.get('action1');
      expect(action1.position.x).toBe(50 + 100 + ACTION_NODE_ARROW_OFFSET);
    });

    it('should handle empty actions array', () => {
      const mapNodesUpdate = new Map();
      const actions = [] as any[];
      const parentNode = {
        id: 'parent1',
        type: ENodeType.Text,
        position: { x: 50, y: 50 },
      } as any;

      updateActionPositionX(mapNodesUpdate, actions, parentNode);
      expect(mapNodesUpdate.size).toBe(0);
    });

    it('should use default width when parent node has no measured width', () => {
      const mapNodesUpdate = new Map();
      const actions = [
        {
          id: 'action1',
          position: { x: 0, y: 100 },
          measured: { width: 150 },
        },
      ] as any[];

      const parentNode = {
        id: 'parent1',
        type: ENodeType.Text,
        position: { x: 50, y: 50 },
        // No measured property
      } as any;

      updateActionPositionX(mapNodesUpdate, actions, parentNode);

      const action1 = mapNodesUpdate.get('action1');
      // Should use ACTION_NODE_SMALL_WIDTH (100) as default
      expect(action1.position.x).toBe(50 + ACTION_NODE_SMALL_WIDTH + ACTION_NODE_ARROW_OFFSET);
    });

    it('should handle actions with undefined measured width', () => {
      const mapNodesUpdate = new Map();
      const actions = [
        {
          id: 'action1',
          position: { x: 0, y: 100 },
          measured: { width: 150 },
        },
        {
          id: 'action2',
          position: { x: 0, y: 100 },
          measured: undefined, // No measured property
        },
      ] as any[];

      const parentNode = {
        id: 'parent1',
        type: ENodeType.Text,
        position: { x: 50, y: 50 },
        measured: { width: 100 },
      } as any;

      updateActionPositionX(mapNodesUpdate, actions, parentNode);

      const action1 = mapNodesUpdate.get('action1');
      const action2 = mapNodesUpdate.get('action2');

      expect(action1.position.x).toBe(50 + 100 + ACTION_NODE_ARROW_OFFSET);
      // action2 should use ACTION_NODE_SMALL_WIDTH as default
      expect(action2.position.x).toBe(
        50 + 100 + ACTION_NODE_ARROW_OFFSET + 150 + ACTION_NODE_ARROW_OFFSET
      );
    });

    it('should handle actions with null measured width', () => {
      const mapNodesUpdate = new Map();
      const actions = [
        {
          id: 'action1',
          position: { x: 0, y: 100 },
          measured: { width: null },
        },
      ] as any[];

      const parentNode = {
        id: 'parent1',
        type: ENodeType.Text,
        position: { x: 50, y: 50 },
        measured: { width: 100 },
      } as any;

      updateActionPositionX(mapNodesUpdate, actions, parentNode);

      const action1 = mapNodesUpdate.get('action1');
      // Should use ACTION_NODE_SMALL_WIDTH as default when width is null
      expect(action1.position.x).toBe(50 + 100 + ACTION_NODE_ARROW_OFFSET);
    });

    it('should handle actions with zero measured width', () => {
      const mapNodesUpdate = new Map();
      const actions = [
        {
          id: 'action1',
          position: { x: 0, y: 100 },
          measured: { width: 0 },
        },
      ] as any[];

      const parentNode = {
        id: 'parent1',
        type: ENodeType.Text,
        position: { x: 50, y: 50 },
        measured: { width: 100 },
      } as any;

      updateActionPositionX(mapNodesUpdate, actions, parentNode);

      const action1 = mapNodesUpdate.get('action1');
      // Should use ACTION_NODE_SMALL_WIDTH as default when width is 0
      expect(action1.position.x).toBe(50 + 100 + ACTION_NODE_ARROW_OFFSET);
    });
  });

  describe('updateActionPositionY', () => {
    it('should update action Y positions', () => {
      const mockElement = {
        getBoundingClientRect: vi.fn(() => ({ x: 50, y: 100 })),
        offsetHeight: 40,
      };
      vi.spyOn(document, 'getElementById').mockReturnValue(mockElement as any);

      const mapNodesUpdate = new Map();
      const node = {
        id: 'node1',
        internals: { positionAbsolute: { x: 100, y: 200 } },
      } as any;

      const actions = [
        {
          id: 'action1',
          position: { x: 100, y: 0 },
          data: { properties: { portId: 'port1' } },
        },
        {
          id: 'action2',
          position: { x: 200, y: 0 },
          data: { properties: { portId: 'port2' } },
        },
      ];

      (getAllActionNodes as any).mockReturnValue(actions);
      mockReactFlowInstance.screenToFlowPosition.mockReturnValue({ x: 60, y: 110 });

      updateActionPositionY(mockReactFlowInstance, mapNodesUpdate, node);

      expect(getAllActionNodes).toHaveBeenCalledWith(mockReactFlowInstance.getNodes(), ['node1']);
      expect(mapNodesUpdate.size).toBe(2);

      const action1 = mapNodesUpdate.get('action1');
      expect(action1.position.y).toBe(86);

      const action2 = mapNodesUpdate.get('action2');
      expect(action2.position.y).toBe(86);
    });

    it('should handle empty actions array', () => {
      const mapNodesUpdate = new Map();
      const node = { id: 'node1' } as any;

      (getAllActionNodes as any).mockReturnValue([]);

      updateActionPositionY(mockReactFlowInstance, mapNodesUpdate, node);

      expect(mapNodesUpdate.size).toBe(0);
    });

    it('should handle actions with missing portId', () => {
      const mockElement = {
        getBoundingClientRect: vi.fn(() => ({ x: 50, y: 100 })),
        offsetHeight: 40,
      };
      vi.spyOn(document, 'getElementById').mockReturnValue(mockElement as any);

      const mapNodesUpdate = new Map();
      const node = {
        id: 'node1',
        internals: { positionAbsolute: { x: 100, y: 200 } },
      } as any;

      const actions = [
        {
          id: 'action1',
          position: { x: 100, y: 0 },
          data: { properties: {} }, // Missing portId
        },
      ];

      (getAllActionNodes as any).mockReturnValue(actions);
      mockReactFlowInstance.screenToFlowPosition.mockReturnValue({ x: 60, y: 110 });

      updateActionPositionY(mockReactFlowInstance, mapNodesUpdate, node);

      expect(getAllActionNodes).toHaveBeenCalledWith(mockReactFlowInstance.getNodes(), ['node1']);
      expect(mapNodesUpdate.size).toBe(1);

      const action1 = mapNodesUpdate.get('action1');
      expect(action1.position.y).toBe(86);
    });

    it('should handle actions with undefined data properties', () => {
      const mockElement = {
        getBoundingClientRect: vi.fn(() => ({ x: 50, y: 100 })),
        offsetHeight: 40,
      };
      vi.spyOn(document, 'getElementById').mockReturnValue(mockElement as any);

      const mapNodesUpdate = new Map();
      const node = {
        id: 'node1',
        internals: { positionAbsolute: { x: 100, y: 200 } },
      } as any;

      const actions = [
        {
          id: 'action1',
          position: { x: 100, y: 0 },
          data: { properties: {} }, // Empty properties object
        },
      ];

      (getAllActionNodes as any).mockReturnValue(actions);
      mockReactFlowInstance.screenToFlowPosition.mockReturnValue({ x: 60, y: 110 });

      updateActionPositionY(mockReactFlowInstance, mapNodesUpdate, node);

      expect(getAllActionNodes).toHaveBeenCalledWith(mockReactFlowInstance.getNodes(), ['node1']);
      expect(mapNodesUpdate.size).toBe(1);

      const action1 = mapNodesUpdate.get('action1');
      expect(action1.position.y).toBe(86);
    });
  });
});
