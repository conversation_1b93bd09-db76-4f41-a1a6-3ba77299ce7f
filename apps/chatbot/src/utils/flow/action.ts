import {
  ACTION_NODE_ARROW_OFFSET,
  ACTION_NODE_HEIGHT,
  ACTION_NODE_LARGE_WIDTH,
  ACTION_NODE_SMALL_WIDTH,
  COLOR_PALETTE_KEYS,
  DefaultActionOptions,
  STEP_NODES,
} from '@/constants/flow';
import {
  ENodeType,
  EnumActionTypes,
  EnumIntentScope,
  type TInternalNode,
  type TReactFlowInstance,
  type TReactFlowNode,
} from '@/types';
import type { XYPosition } from '@xyflow/react';
import { get, groupBy, sortBy } from 'lodash';
import { randomId } from '../id';
import { getAllActionNodes } from './shared';

export const getActionOffsetVertical = ({
  node,
  sourceHandle,
  screenToFlowPosition,
}: {
  node: TInternalNode;
  sourceHandle: string;
  screenToFlowPosition: TReactFlowInstance['screenToFlowPosition'];
}): number => {
  const sourceElement = document.getElementById(`${node.id}.${sourceHandle}`);
  const sourceRect = sourceElement?.getBoundingClientRect();
  const offsetHeight = (sourceElement?.offsetHeight ?? 0) / 2;
  const position = screenToFlowPosition({
    x: sourceRect?.x ?? 0,
    y: sourceRect?.y ?? 0,
  });
  const parentPosition = node.internals.positionAbsolute || { x: 0, y: 0 };

  return Math.abs(parentPosition.y - position.y + ACTION_NODE_HEIGHT / 2 - offsetHeight);
};

export const getAvailableActionOptions = (
  nodes: TReactFlowNode[],
  actionNode: TReactFlowNode
): EnumActionTypes[] => {
  if (!actionNode.parentId) return DefaultActionOptions;

  const allActionNodes = getAllActionNodes(nodes, [actionNode.parentId]);
  const actionNodes = getActionNodesByPortId(
    allActionNodes,
    get(actionNode, 'data.properties.portId', '')
  );

  const availableActionOptions = [...DefaultActionOptions];

  for (const action of actionNodes) {
    const actionType = getActionType(action);
    const index = availableActionOptions.findIndex((option) => option === actionType);

    if (index > -1) {
      availableActionOptions.splice(index, 1);
    }
  }

  return availableActionOptions;
};

export const getActionType = (actionNode?: TReactFlowNode): EnumActionTypes => {
  return get(actionNode, 'data.properties.actionType', '');
};

export const getActionNodesByPortId = (nodes: TReactFlowNode[], portId: string) => {
  return nodes.filter(
    (node) => node.type === ENodeType.Action && node.data.properties.portId === portId
  );
};

export const generateActionNodeByType = ({
  type,
  parentId,
  position,
  portId,
}: {
  type: EnumActionTypes;
  parentId: string;
  position: XYPosition;
  portId: string;
}) => {
  const newActionNode: TReactFlowNode<ENodeType.Action> = {
    id: randomId(),
    type: ENodeType.Action,
    position,
    parentId,
    draggable: false,
    data: {
      properties: {
        actionType: type,
        portId,
        setting: {
          color: COLOR_PALETTE_KEYS[0],
        },
      },
    },
  };

  switch (type) {
    case EnumActionTypes.intent:
      newActionNode.width = ACTION_NODE_SMALL_WIDTH;
      newActionNode.data.canHandle = false;
      newActionNode.data.properties = {
        ...newActionNode.data.properties,
        intentScope: EnumIntentScope.GLOBAL,
      };
      break;
    case EnumActionTypes.node:
      newActionNode.width = ACTION_NODE_LARGE_WIDTH;
      newActionNode.data.canHandle = false;
      break;
    case EnumActionTypes.end:
      newActionNode.width = ACTION_NODE_SMALL_WIDTH;
      newActionNode.data.canHandle = false;
      break;
    case EnumActionTypes.url:
      newActionNode.width = ACTION_NODE_LARGE_WIDTH;
      newActionNode.data.canHandle = true;
      break;
    case EnumActionTypes.variable:
      newActionNode.width = ACTION_NODE_LARGE_WIDTH;
      newActionNode.data.canHandle = true;
      newActionNode.data.properties = {
        ...newActionNode.data.properties,
        variables: [],
      };
      break;
    default:
      break;
  }

  return newActionNode;
};

export const getNextActionNodePosition = (
  screenToFlowPosition: TReactFlowInstance['screenToFlowPosition'],
  sourceHandle: string,
  node: TInternalNode
) => {
  const isChainingAction = node.type === ENodeType.Action;

  const posY = isChainingAction
    ? node.position.y
    : getActionOffsetVertical({
        node,
        screenToFlowPosition,
        sourceHandle,
      });

  const currentPositionX = [...STEP_NODES, ENodeType.Action].includes(node.type as ENodeType)
    ? node.position.x
    : 0;

  const posX = currentPositionX + (node.measured.width ?? 0);

  return {
    x: posX + ACTION_NODE_ARROW_OFFSET,
    y: posY,
  };
};

export const getActionNodeByPortId = (
  reactFlowInstance: TReactFlowInstance | null,
  portId: string
) => {
  if (!reactFlowInstance) return;
  const { getNodes } = reactFlowInstance;
  const node = getNodes().find((node) => node.data.properties.portId === portId);
  return node;
};

export const getActionNodeBySourceAndSourceHandle = (
  reactFlowInstance: TReactFlowInstance | null,
  sourceHandle: string,
  source: string
) => {
  if (!reactFlowInstance) return;
  const { getNode, getEdges } = reactFlowInstance;
  const edge = getEdges().find(
    (edge) => edge.source === source && edge.sourceHandle === sourceHandle
  );
  return getNode(edge?.target || '');
};

export const updateActionPositionX = (
  mapNodesUpdate: Map<string, TReactFlowNode>,
  actions: TReactFlowNode[],
  parentNode: TReactFlowNode
) => {
  const groupedActionsByPositionY = groupBy(actions, 'position.y');
  Object.values(groupedActionsByPositionY).forEach((actions) => {
    let prevActionPosX = STEP_NODES.includes(parentNode.type as ENodeType)
      ? parentNode.position.x
      : 0;
    let previousNode = parentNode;
    const sortedActionsByPositionX = sortBy(actions, 'position.x');
    sortedActionsByPositionX.map((actionNode) => {
      const posX =
        prevActionPosX +
        (previousNode.measured?.width || ACTION_NODE_SMALL_WIDTH) +
        ACTION_NODE_ARROW_OFFSET;
      actionNode.position = {
        x: posX,
        y: actionNode.position.y,
      };
      prevActionPosX = posX;
      previousNode = actionNode;
      mapNodesUpdate.set(actionNode.id, actionNode);
    });
  });
};

export const updateActionPositionY = (
  reactFlowInstance: TReactFlowInstance,
  mapNodesUpdate: Map<string, TReactFlowNode>,
  node: TInternalNode
) => {
  const actions = getAllActionNodes(reactFlowInstance.getNodes(), [node.id]);

  actions.forEach((action) => {
    const portId = action.data.properties.portId;
    action.position.y = getActionOffsetVertical({
      node,
      sourceHandle: portId,
      screenToFlowPosition: reactFlowInstance.screenToFlowPosition,
    });
    mapNodesUpdate.set(action.id, action);
  });
};
