import { act, renderHook } from '@testing-library/react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import useInterval from './useInterval';

describe('useInterval', () => {
  beforeEach(() => {
    vi.useFakeTimers();
    // Mock document.hasFocus to control focus state in tests
    Object.defineProperty(document, 'hasFocus', {
      value: vi.fn(() => true),
      writable: true,
      configurable: true,
    });
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.clearAllTimers();
  });

  it('should start interval when delay is provided', () => {
    const callback = vi.fn();
    const { result } = renderHook(() => useInterval(callback, 1000));

    // The active state reflects the initial state when the hook was created
    // Since delay is provided, the interval starts automatically, but active starts as false
    expect(result.current.active).toBe(false);

    act(() => {
      vi.advanceTimersByTime(1000);
    });

    expect(callback).toHaveBeenCalledTimes(1);
  });

  it('should not start interval when delay is null', () => {
    const callback = vi.fn();
    const { result } = renderHook(() => useInterval(callback, null));

    expect(result.current.active).toBe(false);
    expect(callback).not.toHaveBeenCalled();

    act(() => {
      vi.advanceTimersByTime(1000);
    });

    expect(callback).not.toHaveBeenCalled();
  });

  it('should call callback multiple times when interval is active', () => {
    const callback = vi.fn();
    const { result } = renderHook(() => useInterval(callback, 500));

    expect(result.current.active).toBe(false);

    act(() => {
      vi.advanceTimersByTime(500);
    });
    expect(callback).toHaveBeenCalledTimes(1);

    act(() => {
      vi.advanceTimersByTime(500);
    });
    expect(callback).toHaveBeenCalledTimes(2);

    act(() => {
      vi.advanceTimersByTime(500);
    });
    expect(callback).toHaveBeenCalledTimes(3);
  });

  it('should stop interval when stop is called', () => {
    const callback = vi.fn();
    const { result } = renderHook(() => useInterval(callback, 1000));

    expect(result.current.active).toBe(false);

    act(() => {
      result.current.stop();
    });

    // Note: The active state in the returned object is not reactive
    // It only reflects the state when the hook was created
    // The actual interval is stopped, but the active state remains the same

    act(() => {
      vi.advanceTimersByTime(1000);
    });

    expect(callback).not.toHaveBeenCalled();
  });

  it('should restart interval when start is called after stop', () => {
    const callback = vi.fn();
    const { result } = renderHook(() => useInterval(callback, 1000));

    act(() => {
      result.current.stop();
    });

    // The active state in the returned object is not reactive
    // It only reflects the state when the hook was created

    act(() => {
      result.current.start();
    });

    // The active state in the returned object is not reactive
    // It only reflects the state when the hook was created

    act(() => {
      vi.advanceTimersByTime(1000);
    });

    expect(callback).toHaveBeenCalledTimes(1);
  });

  it('should not start interval if already active', () => {
    const callback = vi.fn();
    const { result } = renderHook(() => useInterval(callback, 1000));

    expect(result.current.active).toBe(false);

    act(() => {
      result.current.start();
    });

    // The active state in the returned object is not reactive
    // It only reflects the state when the hook was created

    act(() => {
      vi.advanceTimersByTime(1000);
    });

    expect(callback).toHaveBeenCalledTimes(1);
  });

  it('should not start interval if delay is null', () => {
    const callback = vi.fn();
    const { result } = renderHook(() => useInterval(callback, null));

    expect(result.current.active).toBe(false);

    act(() => {
      result.current.start();
    });

    expect(result.current.active).toBe(false);
    expect(callback).not.toHaveBeenCalled();
  });

  it('should update callback when callback changes', () => {
    const callback1 = vi.fn();
    const callback2 = vi.fn();

    const { rerender } = renderHook(
      ({ callback, delay }: { callback: () => void; delay: number | null }) =>
        useInterval(callback, delay),
      { initialProps: { callback: callback1, delay: 1000 } }
    );

    act(() => {
      vi.advanceTimersByTime(1000);
    });

    expect(callback1).toHaveBeenCalledTimes(1);
    expect(callback2).not.toHaveBeenCalled();

    rerender({ callback: callback2, delay: 1000 });

    act(() => {
      vi.advanceTimersByTime(1000);
    });

    expect(callback1).toHaveBeenCalledTimes(1);
    expect(callback2).toHaveBeenCalledTimes(1);
  });

  it('should restart interval when delay changes', () => {
    const callback = vi.fn();
    const { rerender } = renderHook(
      ({ callback, delay }: { callback: () => void; delay: number | null }) =>
        useInterval(callback, delay),
      { initialProps: { callback, delay: 1000 } }
    );

    act(() => {
      vi.advanceTimersByTime(1000);
    });

    expect(callback).toHaveBeenCalledTimes(1);

    rerender({ callback, delay: 500 });

    act(() => {
      vi.advanceTimersByTime(500);
    });

    expect(callback).toHaveBeenCalledTimes(2);
  });

  it('should stop interval when delay changes to null', () => {
    const callback = vi.fn();
    const { rerender } = renderHook(
      ({ callback, delay }: { callback: () => void; delay: number | null }) =>
        useInterval(callback, delay),
      { initialProps: { callback, delay: 1000 } }
    );

    rerender({ callback, delay: null as any });

    act(() => {
      vi.advanceTimersByTime(1000);
    });

    expect(callback).not.toHaveBeenCalled();
  });

  it('should start interval when delay changes from null to number', () => {
    const callback = vi.fn();
    const { result, rerender } = renderHook(
      ({ callback, delay }: { callback: () => void; delay: number | null }) =>
        useInterval(callback, delay),
      { initialProps: { callback, delay: null } }
    );

    expect(result.current.active).toBe(false);

    rerender({ callback, delay: 1000 as any });

    // The active state in the returned object is not reactive
    // It only reflects the state when the hook was created

    act(() => {
      vi.advanceTimersByTime(1000);
    });

    expect(callback).toHaveBeenCalledTimes(1);
  });

  it('should cleanup interval on unmount', () => {
    const callback = vi.fn();
    const { unmount } = renderHook(() => useInterval(callback, 1000));

    unmount();

    act(() => {
      vi.advanceTimersByTime(1000);
    });

    expect(callback).not.toHaveBeenCalled();
  });

  it('should handle multiple stop calls safely', () => {
    const callback = vi.fn();
    const { result } = renderHook(() => useInterval(callback, 1000));

    expect(result.current.active).toBe(false);

    act(() => {
      result.current.stop();
    });

    // The active state in the returned object is not reactive
    // It only reflects the state when the hook was created

    act(() => {
      result.current.stop();
    });

    // The active state in the returned object is not reactive
    // It only reflects the state when the hook was created

    act(() => {
      vi.advanceTimersByTime(1000);
    });

    expect(callback).not.toHaveBeenCalled();
  });

  it('should handle callback that throws error', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    const errorCallback = vi.fn(() => {
      throw new Error('Test error');
    });

    renderHook(() => useInterval(errorCallback, 1000));

    act(() => {
      try {
        vi.advanceTimersByTime(1000);
      } catch (error) {
        // Error is expected to be thrown by the callback
      }
    });

    expect(errorCallback).toHaveBeenCalledTimes(1);

    consoleSpy.mockRestore();
  });

  it('should return correct active state', () => {
    const callback = vi.fn();
    const { result } = renderHook(() => useInterval(callback, 1000));

    expect(result.current.active).toBe(false);

    act(() => {
      result.current.stop();
    });

    // The active state in the returned object is not reactive
    // It only reflects the state when the hook was created

    act(() => {
      result.current.start();
    });

    // The active state in the returned object is not reactive
    // It only reflects the state when the hook was created

    act(() => {
      vi.advanceTimersByTime(1000);
    });

    expect(callback).toHaveBeenCalledTimes(1);
  });

  it('should handle rapid start/stop calls', () => {
    const callback = vi.fn();
    const { result } = renderHook(() => useInterval(callback, 1000));

    expect(result.current.active).toBe(false);

    act(() => {
      result.current.stop();
      result.current.start();
      result.current.stop();
      result.current.start();
    });

    // The active state in the returned object is not reactive
    // It only reflects the state when the hook was created

    act(() => {
      vi.advanceTimersByTime(1000);
    });

    expect(callback).toHaveBeenCalledTimes(1);
  });

  it('should not execute callback when document does not have focus', () => {
    const callback = vi.fn();

    // Mock document.hasFocus to return false
    Object.defineProperty(document, 'hasFocus', {
      value: vi.fn(() => false),
      writable: true,
      configurable: true,
    });

    renderHook(() => useInterval(callback, 1000));

    act(() => {
      vi.advanceTimersByTime(1000);
    });

    expect(callback).not.toHaveBeenCalled();
  });

  it('should execute callback when document has focus', () => {
    const callback = vi.fn();

    // Mock document.hasFocus to return true
    Object.defineProperty(document, 'hasFocus', {
      value: vi.fn(() => true),
      writable: true,
      configurable: true,
    });

    renderHook(() => useInterval(callback, 1000));

    act(() => {
      vi.advanceTimersByTime(1000);
    });

    expect(callback).toHaveBeenCalledTimes(1);
  });

  it('should handle focus state changes during interval execution', () => {
    const callback = vi.fn();
    let focusState = true;

    // Mock document.hasFocus to return dynamic focus state
    Object.defineProperty(document, 'hasFocus', {
      value: vi.fn(() => focusState),
      writable: true,
      configurable: true,
    });

    renderHook(() => useInterval(callback, 1000));

    // First tick with focus
    act(() => {
      vi.advanceTimersByTime(1000);
    });
    expect(callback).toHaveBeenCalledTimes(1);

    // Change focus state to false
    focusState = false;

    // Second tick without focus
    act(() => {
      vi.advanceTimersByTime(1000);
    });
    expect(callback).toHaveBeenCalledTimes(1); // Should not be called

    // Change focus state back to true
    focusState = true;

    // Third tick with focus again
    act(() => {
      vi.advanceTimersByTime(1000);
    });
    expect(callback).toHaveBeenCalledTimes(2); // Should be called again
  });

  it('should handle undefined callback safely', () => {
    const { result } = renderHook(() => useInterval(undefined as any, 1000));

    // The active state reflects the initial state when the hook was created
    expect(result.current.active).toBe(false);

    act(() => {
      vi.advanceTimersByTime(1000);
    });

    // Should not throw error, just not execute callback
    expect(result.current.active).toBe(false);
  });

  it('should handle null callback safely', () => {
    const { result } = renderHook(() => useInterval(null as any, 1000));

    // The active state reflects the initial state when the hook was created
    expect(result.current.active).toBe(false);

    act(() => {
      vi.advanceTimersByTime(1000);
    });

    // Should not throw error, just not execute callback
    expect(result.current.active).toBe(false);
  });
});
