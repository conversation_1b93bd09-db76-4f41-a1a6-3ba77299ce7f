import {
  BUILT_IN_PORT,
  ENodeType,
  EdgeTypes,
  EnumActionTypes,
  FLOW_TYPES,
  type FlowCategory,
  PORT_TYPE,
  type TColorPaletteSetConfig,
  type TXYCoords,
} from '@/types';
import { FieldEnum } from '@/types/nodes';
import type { MantineTransitionStyles } from '@mantine/core/lib/components/Transition/transitions';
import {
  IconAlertTriangle,
  IconClock,
  IconDoorExit,
  IconFlag,
  IconStepInto,
} from '@tabler/icons-react';
import { type DefaultEdgeOptions, MarkerType } from '@xyflow/react';
import { Colors } from './themeConfiguration';

export const DEFAULT_NODE_IDS = {
  [FLOW_TYPES.start]: '000start000000000000000000',
  [FLOW_TYPES.end]: '000end00000000000000000000',
  [FLOW_TYPES.error]: '000error000000000000000000',
  [FLOW_TYPES.timeout]: '000timeout0000000000000000',
};

export const DEFAULT_NODE_ICONS = {
  [FLOW_TYPES.start]: IconFlag,
  [FLOW_TYPES.end]: IconDoorExit,
  [FLOW_TYPES.error]: IconAlertTriangle,
  [FLOW_TYPES.timeout]: IconClock,
  [FLOW_TYPES.normal]: IconStepInto,
};

export const OFFSET_EDGE_LABEL_COLOR_PICKER = 80;
export const OFFSET_EDGE_LABEL_TOOLBAR = 30;
export const Z_INDEX_EDGE_LABEL = 1001;
export const FLOW_UPDATE_INTERVAL = 10000;
export const TITLE_HEIGHT = 20;
export const TITLE_WIDTH = 186;
export const PADDING_NODE = 8;
export const WIDTH_STEP = 220;
export const WIDTH_NODE_BLOCK = 236;
export const HEIGHT_NODE_BLOCK = 87;
export const FIRST_ITEM_POSITION: TXYCoords = [8, 37];
export const MIN_HEIGHT_STEP = 46;
export const CONTEXT_MENU_WIDTH = 200;
export const FLOW_KEY = 'flow_';
export const FLOW_STATUS_KEY = 'flow_status_';
export const SPACE_ACTIVE_DRAG = 10;
export const ACTION_NODE_ARROW_OFFSET = 30;
export const ACTION_NODE_HEIGHT = 30;
export const ACTION_NODE_LARGE_WIDTH = 160;
export const ACTION_NODE_SMALL_WIDTH = 120;
export const FIELD_LIST_OFFSET_HEIGHT = 450;
export const HEADER_HEIGHT = 113;
export const FLOW_EDITOR_OFFSET_LEFT = 240;
export const FLOW_EDITOR_OFFSET_TOP = 80;
export const PANEL_CONTEXT_MENU_DELAY = 100;
export const FLOW_OFFSET_TOP = 50;
export const FLOW_OFFSET_LEFT = 240;
export const FLOW_EDITOR_VIEWPORT_PADDING = 8;
export const FLOW_EDITOR_VIEWPORT_DURATION = 300;
export const FLOW_EDITOR_ANIMATION_DURATION = 200;

export const BUILT_IN_PORT_NEXT = `${BUILT_IN_PORT}.${PORT_TYPE.next}`;
export const BUILT_IN_PORT_ELSE = `${BUILT_IN_PORT}.${PORT_TYPE.else}`;
export const BUILT_IN_PORT_SUCCESS = `${BUILT_IN_PORT}.${PORT_TYPE.success}`;
export const BUILT_IN_PORT_FAILURE = `${BUILT_IN_PORT}.${PORT_TYPE.failure}`;
export const BUILT_IN_PORT_FINISH = `${BUILT_IN_PORT}.${PORT_TYPE.finish}`;
export const BUILT_IN_PORT_ACTION = `${BUILT_IN_PORT}.${PORT_TYPE.action}`;
export const BUILT_IN_PORT_GOTO = `${BUILT_IN_PORT}.${PORT_TYPE.goto}`;
export const BUILT_IN_PORT_NODE = `${BUILT_IN_PORT}.${PORT_TYPE.node}`;

export const FADE_RIGHT_IN: MantineTransitionStyles = {
  in: { opacity: 1, transform: 'translateX(0)' },
  out: { opacity: 0, transform: 'translateX(-10px)' },
  common: { transformOrigin: 'right' },
  transitionProperty: 'opacity, transform',
};

export const FADE_TOP_IN: MantineTransitionStyles = {
  in: { opacity: 1, transform: 'translateY(0)' },
  out: { opacity: 0, transform: 'translateY(-10px)' },
  common: { transformOrigin: 'top' },
  transitionProperty: 'opacity, transform',
};

export const connectionLineStyle = {
  strokeWidth: 1,
  stroke: Colors.decaNavy[5],
};

export const defaultEdgeOptions: DefaultEdgeOptions = {
  type: EdgeTypes.Goto,
  markerEnd: {
    type: MarkerType.ArrowClosed,
  },
};

export const SHADOW_NODE_DATA_TYPE = {
  id: '',
  parentId: '',
  data: {
    properties: {},
  },
  position: { x: 0, y: 0 },
};

export const SHADOW_FLOW_DATA_TYPE = {
  botId: '',
  id: '',
  name: 'Flow 1',
  nodes: {},
  category: 'topic' as FlowCategory,
  type: FLOW_TYPES.normal,
  configs: {
    viewport: {
      x: 100,
      y: 10,
      zoom: 0.85,
    },
  },
};

const {
  Block,
  Buttons,
  Capture,
  Choice,
  Email,
  End,
  Finish,
  Form,
  QnA,
  Livechat,
  Start,
  Text,
  Variables,
  Carousel,
  Api,
  Condition,
  Intent,
  Table,
  FindRecord,
  InsertRecord,
  UpdateRecord,
  DeleteRecord,
  Entity,
  CustomTemplate,
  Gai,
  Script,
  Dynamic,
  Action,
  Menu,
} = ENodeType;

export const BASIC_NODES = [Start, Finish, End];

export const SINGLE_NODES = [Livechat, Intent];

export const NODE_SKIP_DRAGGING = [...BASIC_NODES, ...SINGLE_NODES];

export const NODE_UN_CLICKABLE = [...BASIC_NODES, Menu];

export const UNIQUE_NODES = [
  Dynamic,
  Buttons,
  Carousel,
  Form,
  Capture,
  Condition,
  Api,
  Choice,
  CustomTemplate,
  Gai,
  Script,
];

export const STEP_NODES = [
  Buttons,
  Capture,
  Choice,
  Email,
  Form,
  Text,
  Variables,
  QnA,
  Carousel,
  Api,
  Condition,
  Table,
  FindRecord,
  InsertRecord,
  UpdateRecord,
  DeleteRecord,
  Entity,
  CustomTemplate,
  Gai,
  Script,
  Dynamic,
];

export const DYNAMIC_NODES = [Dynamic, Buttons, Choice, Carousel, Condition, Script];

export const NEED_REVALIDATE_DATA_NODES = [
  Condition,
  Buttons,
  Carousel,
  Choice,
  Script,
  Dynamic,
  Form,
  Entity,
];

export const COUNT_NODES = [Intent, Block, Livechat, Action];

export const SELECTABLE_NODES = [Block, ...SINGLE_NODES];

export const SEARCHABLE_NODES = [Block, ...BASIC_NODES, ...SINGLE_NODES, ...STEP_NODES];

export const SHOW_NODE_MENU_TOOLBAR = [Block, Livechat, Action];

export const DefaultActionOptions = [
  EnumActionTypes.node,
  EnumActionTypes.url,
  EnumActionTypes.variable,
  EnumActionTypes.end,
];
export const TABLE_IGNORE_FIELDS = [
  FieldEnum.createdAt,
  FieldEnum.updatedAt,
  FieldEnum.autoId,
  FieldEnum.createdBy,
  FieldEnum.updatedBy,
];

export const NODE_NAME_MAP = {
  [Text]: 'flowToolbarText',
  [Buttons]: 'flowToolbarButton',
  [Capture]: 'flowToolbarCaptureInput',
  [Form]: 'flowToolbarForm',
  [QnA]: 'flowToolbarKnowledgeBase',
  [Livechat]: 'flowToolbarConnectLivechat',
  [Variables]: 'flowToolbarSetVariable',
  [Carousel]: 'flowToolbarCarousel',
  [Api]: 'flowToolbarApi',
  [Condition]: 'flowToolbarCondition',
  [Intent]: 'flowToolbarIntents',
  [Table]: 'flowToolbarTable',
  [FindRecord]: 'flowToolbarFindRecord',
  [InsertRecord]: 'flowToolbarInsertRecord',
  [UpdateRecord]: 'flowToolbarUpdateRecord',
  [DeleteRecord]: 'flowToolbarDeleteRecord',
  [Entity]: 'flowToolbarAICapture',
  [CustomTemplate]: 'customTemplate',
  [Gai]: 'genAi',
  [Email]: 'flowToolbarSendEmail',
  [Script]: 'flowToolbarScript',
  [Dynamic]: 'flowToolbarDynamicButton',
  [Choice]: 'flowToolbarCaptureIntent',
};

export const ACTION_NAME_MAP = {
  [EnumActionTypes.node]: 'actionTypes.node',
  [EnumActionTypes.url]: 'actionTypes.url',
  [EnumActionTypes.variable]: 'actionTypes.variable',
  [EnumActionTypes.end]: 'actionTypes.end',
};

export const SYSTEM_VARIABLES = [
  'lastEvent',
  'lastResponses',
  'lastUtterance',
  'lastTextResponses',
  'lastApiResponse',
  'platform',
  'userId',
  'bot.id',
  'bot.name',
  'integration.id',
  'integration.name',
  'integration.extra.chatboxId',
  'lastLivechatMessages',
];

export const COLOR_PALETTE_KEYS = [
  '#B2B7E2',
  '#94C4FA',
  '#E0C6D9',
  '#FDC8CB',
  '#C6D6C9',
  '#FFDB7A',
  '#D6B4E9',
];

export const COLOR_PALETTE_SET: Record<string, TColorPaletteSetConfig> = {
  '#B2B7E2': {
    color: '#B2B7E2',
    backgroundColor: '#F2F2F6',
    lineSelected: '#1D2088',
  },
  '#94C4FA': {
    color: '#94C4FA',
    backgroundColor: '#E1EFFE',
    lineSelected: '#1F84F4',
  },
  '#E0C6D9': {
    color: '#E0C6D9',
    backgroundColor: '#F8F2F6',
    lineSelected: '#A81B8D',
  },
  '#FDC8CB': {
    color: '#FDC8CB',
    backgroundColor: '#FEF2F3',
    lineSelected: '#F93549',
  },
  '#C6D6C9': {
    color: '#C6D6C9',
    backgroundColor: '#F2F5F3',
    lineSelected: '#00833E',
  },
  '#FFDB7A': {
    color: '#FFDB7A',
    backgroundColor: '#FFF1CC',
    lineSelected: '#E4BB00',
  },
  '#D6B4E9': {
    color: '#D6B4E9',
    backgroundColor: '#F0E8FF',
    lineSelected: '#AE67C9',
  },
};
