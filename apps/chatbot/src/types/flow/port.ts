export const BY_KEY_PORT = 'byKey';
export const BUILT_IN_PORT = 'builtIn';
export const DYNAMIC_PORT = 'dynamic';
export const PORT_TYPE = {
  next: 'next',
  success: 'success',
  failure: 'failure',
  finish: 'finish',
  else: 'else',
  action: 'action',
  goto: 'goto',
  node: 'node',
  menu: 'menu',
} as const;

export type PortTypes = keyof typeof PORT_TYPE;

export type TPort = {
  id: string;
  type: PortTypes;
  target: string;
  actionIds?: string[];
  setting?: {
    color?: string;
  };
};

export type TPorts = {
  [BUILT_IN_PORT]?: {
    next?: TPort | null;
    else?: TPort | null;
    success?: TPort | null;
    failure?: TPort | null;
    finish?: TPort | null;
  };
  [DYNAMIC_PORT]?: {
    [key: string]: TPort;
  };
};
