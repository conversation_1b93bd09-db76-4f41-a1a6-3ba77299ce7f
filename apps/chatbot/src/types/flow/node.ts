import type {
  AIAdvancedSettings,
  ArticleDisplayType,
  Entity,
  EnumAPIStatusOptions,
  KbInputDataType,
  OutputType,
  TActions,
  TApiStepBodyType,
  TApiStepMethod,
  TButton,
  TCarousel,
  TChoice,
  TCondition,
  TDeleteTableRecord,
  TFindTableRecord,
  TInputField,
  TInsertTableRecord,
  TKBQnA,
  TPath,
  TRelatedArticleConfiguration,
  TTable,
  TTableBase,
  TUpdateTableRecord,
  TVariableNodeData,
  TableOperation,
} from '../nodes';

import type { TPorts } from './port';

export enum ENodeType {
  Start = 'start',
  End = 'end',
  Finish = 'finish',
  Text = 'text',
  Email = 'email',
  Block = 'block',
  Form = 'form',
  Buttons = 'buttons',
  QnA = 'qna',
  Livechat = 'livechat',
  Capture = 'capture',
  Variables = 'variables',
  Choice = 'choice',
  Action = 'action',
  Carousel = 'carousel',
  Api = 'api',
  Condition = 'condition',
  Intent = 'intent',
  Menu = 'menu',
  Table = 'table',
  FindRecord = 'findRecord',
  InsertRecord = 'insertRecord',
  UpdateRecord = 'updateRecord',
  DeleteRecord = 'deleteRecord',
  Entity = 'entity',
  CustomTemplate = 'customTemplate',
  Gai = 'gai',
  Script = 'script',
  Dynamic = 'dynamic',
}

export enum INTENT_SCOPE {
  GLOBAL = 'GLOBAL',
  LOCAL = 'LOCAL',
}

export type IntentScope = keyof typeof INTENT_SCOPE;

export type TXYCoords = [number, number];

export type TBaseNodeData = {
  ports?: TPorts;
  portId?: string;
  setting?: {
    color?: string;
  };
};

export type TBaseStepData = {
  stepIndex?: number;
};

export type TBuiltInNode = {
  ID: string;
  type: ENodeType;
  parentId?: string;
  portId?: string;
  data: Partial<
    | TStartNodeData
    | TBlockData
    | TEmailStepData
    | TKBStepData
    | TButtonStepData
    | TTextStepData
    | TCaptureInputStepData
    | TChoiceStepData
    | TSetVariableData
    | TActionData
    | TCarouselStepData
    | TLiveChatNodeData
    | TConditionStepData
    | TFormStepData
    | TApiStepData
    | TIntentData
    | TCustomTemplateStepData
    | TGaiStepData
    | TScriptStepData
    | TDynamicStepData
    | TEntityStepData
    | TTableData
  >;
  coords: TXYCoords;
  width?: number;
  height?: number;
};

export enum StartType {
  Start = 'start',
  Timeout = 'timeout',
  Error = 'error',
  ExplicitEnd = 'explicitEnd',
}

export type TActionData = TBaseNodeData & TActions;

export type TStartNodeData = TBaseNodeData & {
  startType: StartType;
};

export type TBlockData = TBaseNodeData & {
  name: string;
  steps: string[];
  setting?: TBlockNodeSetting;
};

export type TBlockNodeSetting = {
  color?: string;
  line?: {
    richMenu?: {
      id?: string;
      name?: string;
      enabled?: boolean;
      forceDisable?: boolean;
    };
  };
};

export type TEmailStepData = TBaseNodeData &
  TBaseStepData & {
    name?: string;
    to: string[];
    cc?: string[];
    bcc?: string[];
    title?: string;
    body?: string;
  };

export type TKBStepData = TBaseNodeData &
  TBaseStepData & {
    mode: 'specified' | 'search' | 'rag';
    knowledgebaseID?: string;
    knowledgebaseIDs?: string[];
    documentId?: string;
    documentIds?: string[];
    folderIds?: string[];
    inputType: 'text' | 'select';
    inputDataType?: KbInputDataType;
    qnaIDs: string[];
    qna?: TKBQnA;
    answerLimit?: number;
    language?: string;
    model?: string;
    notFoundPath?: boolean;
    notFoundPathName?: string;
    notFoundText?: string;
    variable?: string;
    useVariable?: boolean;
    articleDisplayType?: ArticleDisplayType;
    relatedArticleConfiguration?: TRelatedArticleConfiguration;
    scoreThreshold?: number;
    similarityThreshold?: number;
    outputVariableName?: string;
    storeOutputInVariable?: boolean;
    sendResponseToUser?: boolean;
    prompt?: string;
  };

export type TButtonStepData = TBaseNodeData &
  TBaseStepData & {
    text?: string;
    buttons: TButton[];
    canSearchIntent?: boolean;
  };

export type TConditionStepData = TBaseNodeData &
  TBaseStepData & {
    expressions: TCondition[];
    noMatch?: string;
  };

export type TCarouselStepData = TBaseNodeData &
  TBaseStepData & {
    cards: TCarousel[];
    layout?: string;
    noMatch?: string;
    intentScope: string;
  };

export type TTextStepData = TBaseNodeData &
  TBaseStepData & {
    texts: string[];
  };

export type TCaptureInputStepData = TBaseNodeData &
  TBaseStepData & {
    intentScope?: string;
    capture: {
      type?: string;
      variable?: string;
    };
  };

export type TChoiceStepData = TBaseNodeData &
  TBaseStepData & {
    choices: Array<TChoice>;
    noMatchPath?: boolean;
    canSearchIntent?: boolean;
  };

export type TSetVariableData = TBaseNodeData &
  TBaseStepData & {
    name?: string;
    variables: TVariableNodeData[];
  };

export type TLiveChatNodeData = TBaseNodeData & {
  name: string;
  fetchConversationHistory?: boolean;
  enableTeamSelection?: boolean;
  teamSelectionText?: string;
  selectedTeams?: string[];
  setting?: {
    color?: string;
  };
};

export type TFormStepData = TBaseNodeData &
  TBaseStepData & {
    name?: string;
    inputs: TInputField[];
    variable?: string;
    submit?: {
      label?: string;
    };
    fallbackPath?: boolean;
  };

export type TApiStepData = TBaseNodeData &
  TBaseStepData & {
    url?: string;
    method: TApiStepMethod;
    content?: string;
    expectedStatus?: Array<number>;
    headers?: Array<{ key: string; val: string }>;
    mappings?: Array<{ variable: string; path: string }>;
    body?: Array<{ key: string; val: string }>;
    params?: Array<{ key: string; val: string }>;
    bodyType: TApiStepBodyType;
    status?: EnumAPIStatusOptions;
    useDecaDefaultToken?: boolean;
  };

export type TIntentData = TBaseNodeData & {
  name?: string;
  intentId: string;
  intentScope: IntentScope;
};

export type TTableData = TBaseNodeData &
  TBaseStepData & {
    operation: TableOperation;
    base?: TTableBase;
    baseId?: string;
    table?: TTable;
    tableId?: string;
    records?: Record<string, any>;
    payload?: TFindTableRecord | TInsertTableRecord | TUpdateTableRecord | TDeleteTableRecord;
  };

export type TEntityStepData = TBaseNodeData &
  TBaseStepData & {
    entities: Entity[];
    prompt?: string;
    variableName: string | null;
    retry?: number;
    missOutText?: string;
    notFoundPath: boolean;
  };

export type TCustomTemplateStepData = TBaseNodeData &
  TBaseStepData & {
    platform: string;
    template: string;
    altText?: string;
    fallbackPath: boolean;
  };

export type TGaiStepData = TBaseNodeData &
  TBaseStepData & {
    prompt: string;
    promptType?: 'user' | 'system'; // default system
    variableName: string | null;
    outputExample?: string;
    outputType: OutputType; // default text
    advancedSettings?: AIAdvancedSettings;
    fallbackPath: boolean; // default false
  };

export type TScriptStepData = TBaseNodeData &
  TBaseStepData & {
    script: string;
    variableName?: string;
    paths: TPath[];
    fallbackPath: boolean;
  };

export type TDynamicStepData = TBaseNodeData &
  TBaseStepData & {
    text?: string;
    inputVariable: string | null;
    labelAttribute?: string;
    valueAttribute?: string;
    buttons?: TButton[];
    outputVariable: string | null;
  };
