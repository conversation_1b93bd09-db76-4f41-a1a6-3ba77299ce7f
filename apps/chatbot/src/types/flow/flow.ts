export const FLOW_TYPES = {
  start: 'start',
  timeout: 'timeout',
  error: 'error',
  end: 'end',
  normal: 'normal',
} as const;

export type FlowType = keyof typeof FLOW_TYPES;

export type FlowCategory = 'topic' | 'component' | 'snippet';

export type TVariable = {
  id: string;
  name: string;
  defaultValue?: string;
  description?: string;
};

export type FlowError = {
  path: string;
  message: string;
};

export enum TPendingDeleteStatus {
  IDLE = 'idle',
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  FINISHED = 'finished',
}

export enum EDropEngine {
  DropBlockToBlock = 'dropBlockToBlock',
  DropStepToBlock = 'dropStepToBlock',
  DropStepToOutsideBlock = 'dropStepToOutsideBlock',
  DropStepToOldBlock = 'dropStepToOldBlock',
  DropStepFromToolbarToBlock = 'dropStepFromToolbarToBlock',
  DropStepFromToolbarToPanel = 'dropStepFromToolbarToPanel',
  ResetNodeState = 'resetNodeState',
}

export type TColorPaletteSetConfig = {
  color: string;
  backgroundColor: string;
  lineSelected: string;
};
