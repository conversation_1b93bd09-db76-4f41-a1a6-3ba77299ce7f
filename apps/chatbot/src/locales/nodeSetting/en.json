{"action": "Action", "actionTitle": "Action", "actionTypes": {"end": "End", "node": "Go to Block", "postback": "API", "url": "Open URL", "variable": "Set variable"}, "actionURLPlaceholder": "Enter URL", "actionURLTitle": "URL", "apiNode": {"applyTo": "Apply to", "captureResponse": "Capture Response", "contentType": "Content", "contentTypeFormKeyValue": "Form", "contentTypeFormURL": "Form URL Encoded", "contentTypeJSON": "JSON", "contentTypeNone": "None", "contentTypePlainText": "Plain Text", "customStatusCode": "Custom", "customStatusCodePlaceholder": "Status code number, seperated by comma", "defaultStatusCode": "<PERSON><PERSON><PERSON>", "enterAPIContent": "Enter API content", "enterKey": "Enter key", "enterKeyValue": "Enter Key Value", "enterParameterKey": "Enter parameter key", "enterValueOrVariable": "Enter value or '{{ variable }}'", "fail": "Fail", "headers": "Headers", "invalidCustomStatusCode": "Invalid status code", "parameters": "Parameters", "requestURL": "Request URL", "requestURLPlaceholder": "Request URL or '{{ variable }}'", "selectMappingVariable": "Select variable", "selectVariable": "Select variable", "success": "Success", "successStatusCode": "Success status code", "useDecaDefaultToken": "Default DECA API Token", "title": "API"}, "blockNode": {"hideAllRichMenu": "Hide all rich menu", "lineRichMenu": "LINE rich menu", "richMenuId": "Rich menu ID", "richMenuIdPlaceholder": "Enter rich menu ID", "title": "Node setting"}, "btnNodeNameTitle": "Button Label", "btnNode": {"searchIntent": "Allow user input and search intents", "storeInputInVariable": "Store input in variable", "textButtons": "Text", "textButtonsDescription": "※In LINE, buttons cannot be displayed alone. Therefore, if no text is set, default text will be used automatically."}, "btnTitle": "Button title", "button": "<PERSON><PERSON>", "buttonCard": "<PERSON><PERSON>", "buttonNamePlaceholder": "Enter button label", "buttonNameTitle": "Button label", "buttonTitle": "<PERSON><PERSON>", "cancel": "Cancel", "captureInputNode": {"label": "Variable (store result)", "placeholder": "Capture input", "selected": "Capture {variable}", "selectMappingVariable": "Select variable", "title": "Capture Input"}, "captureIntentNode": {"intent": "Intent", "noMatch": "No match", "searchIntent": "Search from intent nodes", "selectIntentPlaceholder": "Select intent", "title": "Capture Intent"}, "captureIntentNoMatch": "No match", "captureIntentPlaceholder": "Capture intent", "cardTitle": "Card title", "carousel": "Carousel", "conditionNode": {"condition": "Condition", "conditionNameTitle": "Condition", "conditionsTitle": "Conditions", "conditionTitle": "Condition", "else": "Else", "noMatch": "No match"}, "description": "Description", "dragImageText": "Drag images here or click to select files", "dynamicNode": {"attributeToBeSetAsTitle": "Attribute to be set as Title", "attributeToBeSetAsVariable": "Attribute to be set as Variable", "text": "Text", "textDescription": "※In LINE, buttons cannot be displayed alone. Therefore, if no text is set, default text will be used automatically.", "textPlaceholder": "Enter text", "title": "Dynamic Button", "variable": "Variable to extract value ( must be array )", "variablePlaceholder": "Select variable", "variableStored": "Variable to store value"}, "emailNode": {"bcc": "BCC", "bccPlaceholder": "Enter BCC email", "body": "Message", "bodyPlaceholder": "Add content and varients", "cc": "CC", "ccPlaceholder": "Enter CC email", "emailExistedMessage": "Email already existed", "name": "Set label", "namePlaceholder": "Enter label", "nameRequired": "Label is required", "recipient": "Recipient", "settingTitle": "Send email", "title": "Title", "titlePlaceholder": "Enter email title", "to": "Send to email", "toPlaceholder": "Enter email", "toRequired": "Email is required", "validation": "Invalid email"}, "enterDesc": "Enter card description", "enterTitle": "Enter card title", "entityNode": {"confirmMessage": "Editing the prompt will discard any changes made to the Entity & Property names in the interface, and changes to the prompt will not be reflected in the Entity & Property settings.", "confirmTitle": "Edit the prompt", "enterEntity": "Enter entity", "enterPropertyName": "Enter Property name", "entity": "Entity", "newEntity": "New Entity", "propertyName": "Property name", "title": "Capture Input", "variableFieldLabel": "Variable (store result)", "variableFieldPlaceholder": "Select Variable"}, "formNode": {"inputFieldTitle": "Input field", "label": "Set label", "mandatoryLabel": "Mandatory", "name": "Form", "nameInputLabel": "Name", "optionLabel": "Item label", "optionLabelPlaceholder": "Enter Input label", "optionValue": "Value", "optionValuePlaceholder": "Enter value", "placeholder": "Enter label", "placeholderInputLabel": "Place holder", "selectFieldTitle": "Dropdown value", "selectLabel": "Dropdown label", "selectName": "Dropdown name", "selectType": "Dropdown", "submitButtonLabel": "Submit button label", "submitButtonPlaceholder": "Enter button label", "textAreaLabel": "Textarea input label", "textAreaType": "Text Area", "textInputLabel": "Text input label", "textType": "Text Input", "typeInputLabel": "Type", "validationEmailOption": "Email", "validationNumberOption": "Number", "validationStringOption": "Text", "variableFieldLabel": "Variable", "variableFieldPlaceholder": "Select variable"}, "genAi": {"fallbackPath": "Fallback branch", "frequencyPenalty": "Frequency Penalty", "json": "JSON", "jsonPlaceholder": "Enter JSON", "outputType": "Output", "presencePenalty": "Presence penalty", "prompt": "Prompt", "promptError": "Maximum {max} characters", "promptPlaceholder": "Enter prompt", "showAdvancedSetting": "Show advanced setting", "streamingSupport": "Streaming support", "temperature": "Temperature", "text": "Text", "title": "Generative AI", "topP": "Top P", "validate": "Maximum {max} characters ({remained} remaining)", "variable": "Variable", "variablePlaceholder": "Select variable"}, "gotoBlockAction": {"block": "Go to block", "flow": "Select flow", "noBlocksFound": "No blocks found", "noFlowsFound": "No flows found", "selectBlock": "Select block", "selectFlow": "Select flow"}, "inputError": "Please enter a valid value", "intentNode": {"globalScope": "Global Scope", "intentScope": "Intent <PERSON>", "localScope": "Local Scope", "placeholder": "Intent", "title": "Intent", "utterances": "Utterances"}, "kbNode": {"accuracyThresholdSetting": "Accuracy threshold setting", "action": "Action", "answerType": "Answer type", "article": "Article", "ascendants": "Ascendants", "back": "Back", "cancel": "Cancel", "confirm": "Confirm", "customDataField": "Custom data field", "customDataKey": "Custom data key", "descendants": "Descendants", "document": "Document", "enterCustomDataKey": "Enter custom data key", "enterId": "Enter ID", "enterKbId": "Enter Knowledge Base ID (or variable)", "enterValue": "Enter value", "foldersAndKbs": "Folders and knowledge bases", "foldersKnowledgeBasesAndDocuments": "Folders, Knowledge bases and Document", "generativeAi": "Generative AI", "kbType": "Knowledge Base", "knowledgeBase": "Knowledge Base", "maxNumberOfReferenceArticle": "Maximum number of reference article", "maxNumberOfReferenceArticlePlaceholder": "Number of Article", "next": "Next", "noMatch": "No Match", "notFoundPath": "Not Found", "notFoundQnA": "No QnA found", "numberRelatedArticle": "Number of related article", "numberRelatedArticlePlaceholder": "Number of Article", "operatorFeedback": "Operators' feedback", "preview": "Preview", "previewNoResult": "No related articles were found.", "previewSearchResult": "Preview search result", "prompt": "Prompt", "promptHelpText": "Must include '{'question'}' and '{'documents'}' section", "promptPlaceholder": "Enter prompt", "qna": "QnA", "relatedArticle": "Related article label", "relatedArticlePlaceholder": "Enter list label", "relevance": "Relevance", "search": "Search", "searchArticle": "Search article", "searchArticlePlaceholder": "Enter search word", "searchFolderAndKb": "Search folders and knowledge bases", "searchFoldersKnowledgeBasesAndDocuments": "Search Folders, Knowledge bases and Document", "searchFromKb": "Search from Knowledge base", "select": "Select", "selectArticle": "Select Article", "selectedArticle": "Selected article", "selectedFoldersAndKbs": "Selected folders and knowledge bases", "selectedItems": "Selected items", "selectedQna": "Selected Q&A", "selectFoldersKnowledgeBasesAndDocuments": "Select Folders, Knowledge bases and Document", "selectKb": "Select knowledgeBase", "selectKbsAndDocuments": "Select KBs and Documents", "selectKnowledgeBase": "Select Knowledge base", "selectKnowledgeBasesOrDocuments": "Knowledge Bases or Documents", "selectQnA": "Select QnA", "selectVariable": "Select variable", "setResultToVariable": "Set result to variable", "setResultToVariableOnly": "Set result to variable only", "showAdvancedSetting": "Show advanced setting", "showReferenceArticles": "Show reference articles", "showRelatedArticle": "Show the related article list", "showResultToUser": "Show result to user", "showSelectedArticle": "Show the single selected article", "similarityThresholdSetting": "Similarity threshold setting", "sortBy": "Sort by", "sortByPlaceholder": "Select sort option", "streamingSupport": "Streaming support", "title": "Article Knowledge base", "titleOfArticle": "Title of article", "typeId": "Type ID (or variable)", "typeKbId": "Type KB ID (or variable)", "userFeedback": "Number of user feedback", "useVariableForGenerating": "Use variable for generating", "useVariableForSearching": "Use variable for searching", "value": "Value", "variable": "Variable", "viewCount": "Number of user views"}, "liveChatNode": {"enableTeamSelection": "Enable team selection", "exceedingQueue": "Exceeding queue", "failedToConnect": "Failed to connect", "finishingLivechat": "Finishing live chat", "searchTeamName": "Search team name", "selectAll": "Select all", "selectTeamToShow": "Select team to show", "selectTeamToShowDescription": "If no team is selected, it will go to the 'Failed to connect' branch.", "storeHistory": "Store history in system variable", "successfulConnected": "Successful connected", "teamSelectionText": "Text", "teamSelectionTextDescription": "※In LINE, buttons cannot be displayed alone. Therefore, if no text is set, default text will be used automatically.", "teamSelectionTextPlaceholder": "Please select team", "title": "Livechat connect"}, "save": "Save", "scriptNode": {"default": "<PERSON><PERSON><PERSON>", "fail": "Fail", "pathNameMustBeUnique": "Path names must be unique", "paths": "Paths", "script": "<PERSON><PERSON><PERSON>", "variable": "Variable", "variablePlaceholder": "Select variable"}, "setVariableNode": {"enterValue": "Enter value", "label": "Set label", "name": "Set", "placeholder": "Enter label", "selectVariable": "Select variable", "value": "Value", "variable": "Variable", "variableLabel": "Set variable"}, "tableNode": {"base": "Base", "basePlaceholder": "Select base", "column": "Column", "columnTable": "Column ID", "decimalPlaces": "Please enter a valid number with {places} decimal places", "nameTable": "Name", "notFoundRecords": "No records found", "orderBy": "Order By", "orderByPlaceholder": "Edit text", "recordId": "Record ID", "recordIdPlaceholder": "Enter ID", "searchCondition": "Search Condition (JSON)", "searchConditionPlaceholder": "Edit text", "searchPlaceholder": "Search column name", "table": "Table", "tableDetail": "Table Detail", "tablePlaceholder": "Select table", "updateProperty": "Update property", "validate": "Validate", "variable": "Set result to variable", "variablePlaceholder": "Select variable"}, "templateNode": {"altText": "Alternative text message", "altTextPlaceholder": "It will appear in the device's notifications, talk list, and quote messages as an alternative to the Flex Message.", "customTemplate": "Custom template", "customTemplatePlaceholder": "Enter custom template", "fallbackPath": "Fallback branch for unsupported platform", "linePlatform": "Line Flex message", "linePlatformDecs": "Please paste the data created and tested in the\nFlex Message Simulator.", "platform": "Platform select", "platformPlaceholder": "Select platform", "title": "Custom Template"}, "text": "Text", "textPlaceholder": "Enter text", "title": "Title", "tree": {"root": "All Knowledge Bases"}, "validationMessages": {"invalidURL": "Invalid URL format. Please enter a valid URL, including the protocol (e.g., http:// or https://) and correct domain."}}