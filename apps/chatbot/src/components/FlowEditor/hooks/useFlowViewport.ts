import { FLOW_EDITOR_VIEWPORT_DURATION, FLOW_EDITOR_VIEWPORT_PADDING } from '@/constants/flow';
import { useFlowContext } from '@/contexts';
import { useOnViewportChange } from '@xyflow/react';
import { useCallback, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useNodeHandlersContext } from '../contexts';

export const useFlowViewport = () => {
  const [searchParams] = useSearchParams();
  const {
    reactFlowInstance,
    isFlowEdited,
    activeFlow,
    nodeStartId,
    setNodeStartId,
    setPanelContextMenu,
  } = useFlowContext();
  const {
    editor: { onNodeClick },
  } = useNodeHandlersContext();

  const onViewportChange = useCallback(() => {
    isFlowEdited.current = true;
  }, []);

  const onViewportStart = useCallback(() => {
    setPanelContextMenu((prev) => ({ ...prev, open: false }));
  }, [setPanelContextMenu]);

  useOnViewportChange({
    onStart: onViewportStart,
    onEnd: onViewportChange,
  });

  useEffect(() => {
    if (!reactFlowInstance || !nodeStartId) return;
    const { fitView, getNode } = reactFlowInstance;
    fitView({
      nodes: [{ id: nodeStartId }],
      duration: FLOW_EDITOR_VIEWPORT_DURATION,
      padding: FLOW_EDITOR_VIEWPORT_PADDING,
    });
    setNodeStartId('');

    const node = getNode(nodeStartId);
    if (!node) return;
    setTimeout(() => {
      onNodeClick({} as any, node);
    }, FLOW_EDITOR_VIEWPORT_DURATION);
  }, [nodeStartId, reactFlowInstance, setNodeStartId, onNodeClick]);

  useEffect(() => {
    if (!reactFlowInstance) return;
    const { fitView, setViewport, getNode } = reactFlowInstance;
    const targetZoomId = searchParams.get('node');

    if (!targetZoomId) {
      activeFlow?.configs.viewport && setViewport(activeFlow?.configs.viewport);
    } else {
      setTimeout(() => {
        fitView({
          nodes: [{ id: targetZoomId }],
          duration: FLOW_EDITOR_VIEWPORT_DURATION,
          padding: FLOW_EDITOR_VIEWPORT_PADDING,
        });
      }, 0);
      setTimeout(() => {
        const node = getNode(targetZoomId);
        if (!node) return;
        onNodeClick({} as any, node);
      }, FLOW_EDITOR_VIEWPORT_DURATION);
    }
  }, [reactFlowInstance, activeFlow]);

  return {};
};
