import { COLOR_PALETTE_SET } from '@/constants/flow';
import { useFlowContext } from '@/contexts';
import {
  ENodeType,
  EdgeTypes,
  type TEdge,
  type TEdgeMouseHandler,
  type THandleEdgeHover,
  type TOnEdgesChange,
  TPendingDeleteStatus,
  type TReactFlowNode,
} from '@/types';
import { findTargetNode, generateReactFlowEdge, getPositionFromScreen } from '@/utils/flow';
import { randomId } from '@/utils/id';
import {
  MarkerType,
  type OnConnectEnd,
  type OnConnectStart,
  addEdge,
  applyEdgeChanges,
} from '@xyflow/react';
import get from 'lodash/get';
import keyBy from 'lodash/keyBy';
import set from 'lodash/set';
import { useCallback, useRef } from 'react';

export const useEdgeHandlers = () => {
  const {
    reactFlowInstance,
    selectedEdges,
    edgeActive,
    selectedNodes,
    pendingDeleteStatus,
    isFlowEdited,
    setPendingDeleteStatus,
    setIsConnecting,
    resetAfterConnect,
    resetFlowEditorState,
  } = useFlowContext();
  const connectionRef = useRef<MouseEvent | TouchEvent | null>(null);
  const hoverEdgeRef = useRef<TEdge | null>(null);

  const handleEdgeHover = useCallback<THandleEdgeHover>(
    (edge: TEdge, hover: boolean) => {
      if (!reactFlowInstance) return;
      reactFlowInstance.setEdges((edges) =>
        edges.map((el) => {
          if (el.id === edge.id && el.type === EdgeTypes.Goto) {
            const color = get(
              COLOR_PALETTE_SET[el.data?.setting?.color ?? ''],
              el.selected || hover ? 'lineSelected' : 'color'
            );

            el.markerEnd = {
              type: MarkerType.ArrowClosed,
              color,
            };
            set(el, 'style.stroke', color);

            return { ...el };
          }
          return el;
        })
      );
    },
    [reactFlowInstance]
  );

  const onEdgeClick = useCallback(() => {
    resetFlowEditorState();
  }, [resetFlowEditorState]);

  const onEdgeMouseEnter = useCallback<TEdgeMouseHandler>(
    (_, edge) => {
      handleEdgeHover(edge, true);
      hoverEdgeRef.current = edge;
    },
    [handleEdgeHover]
  );

  const onEdgeMouseLeave = useCallback<TEdgeMouseHandler>(
    (_, edge) => {
      handleEdgeHover(edge, false);
      hoverEdgeRef.current = null;
    },
    [handleEdgeHover]
  );

  const onConnectEnd = useCallback<OnConnectEnd>(
    (event, connectionState) => {
      if (!reactFlowInstance) return;

      isFlowEdited.current = true;

      const { setNodes, setEdges, getNode, getNodes, screenToFlowPosition } = reactFlowInstance;

      const position = getPositionFromScreen(screenToFlowPosition, event);
      const targetNode = findTargetNode(getNodes(), position);
      const source = get(connectionState, 'fromNode.id', '');
      const sourceNode = getNode(source);
      if (targetNode && sourceNode?.parentId !== targetNode.id) {
        const sourceHandle = get(connectionState, 'fromHandle.id', '');
        setEdges((edges) => {
          const newEdges = addEdge(
            generateReactFlowEdge(source, targetNode.id, sourceHandle, EdgeTypes.Goto),
            edges
          );
          return newEdges;
        });
      } else {
        const newMenuNode: TReactFlowNode = {
          id: randomId(),
          type: ENodeType.Menu,
          position: {
            x: position.x,
            y: position.y,
          },
          style: {
            zIndex: 1001,
          },
          data: {
            properties: {},
          },
        };

        setNodes((nodes) => {
          return nodes.concat(newMenuNode);
        });

        setEdges((edges) => {
          const newEdges = addEdge(
            generateReactFlowEdge(
              get(connectionState, 'fromNode.id', ''),
              newMenuNode.id,
              get(connectionState, 'fromHandle.id', ''),
              EdgeTypes.Menu
            ),
            edges
          );
          return newEdges;
        });
      }
      resetAfterConnect();
    },
    [reactFlowInstance, resetAfterConnect]
  );

  const onConnectStart = useCallback<OnConnectStart>(
    (event, params) => {
      if (!reactFlowInstance) return;
      resetFlowEditorState();
      setIsConnecting(true);
      connectionRef.current = event;

      const { setEdges } = reactFlowInstance;
      edgeActive.current = params;

      const { handleType, nodeId, handleId } = params;
      if (handleType && handleId && nodeId !== handleId) {
        setEdges((els) =>
          els.filter((el) => el[handleType] !== nodeId || el.sourceHandle !== handleId)
        );
      }
    },
    [reactFlowInstance, setIsConnecting, resetFlowEditorState]
  );

  const onEdgesChange = useCallback<TOnEdgesChange>(
    (changes) => {
      if (!reactFlowInstance) return;
      const { setEdges } = reactFlowInstance;

      if (pendingDeleteStatus === TPendingDeleteStatus.PENDING) return;

      if (pendingDeleteStatus === TPendingDeleteStatus.CONFIRMED) {
        setEdges((prev) => applyEdgeChanges(changes, prev));
        setPendingDeleteStatus(TPendingDeleteStatus.FINISHED);
        return;
      }

      if (!selectedEdges.length && selectedNodes.length) {
        return;
      }

      if (changes.every((change) => change.type === 'select')) {
        const changesToMap = keyBy(changes, 'id');
        setEdges((prev) =>
          prev.map((el) => {
            const change = changesToMap[el.id];
            if (change) {
              el.selected = change.selected;
              const color = get(
                COLOR_PALETTE_SET[el.data?.setting?.color ?? ''],
                change.selected ? 'lineSelected' : 'color'
              ) as string;

              set(el, 'style.stroke', color);
              el.markerEnd = {
                type: MarkerType.ArrowClosed,
                color,
              };
              return { ...el };
            }
            return el;
          })
        );
        return;
      }

      setEdges((prev) => applyEdgeChanges(changes, prev));
    },
    [reactFlowInstance, pendingDeleteStatus, selectedEdges, selectedNodes, setPendingDeleteStatus]
  );

  return {
    onEdgeMouseEnter,
    onEdgeMouseLeave,
    onEdgeClick,
    onConnectEnd,
    onConnectStart,
    onEdgesChange,
  };
};
