import { ENodeType, EnumActionTypes } from '@/types';
import { TPendingDeleteStatus } from '@/types/flow/flow';
import { act, render } from '@testing-library/react';
import type { MouseEvent } from 'react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { NodeHandlersProvider, useNodeHandlersContext } from './useNodeHandlers';

// Mock all dependencies
vi.mock('@/constants/flow', () => ({
  BASIC_NODES: ['start', 'finish', 'end'],
  NODE_SKIP_DRAGGING: ['start', 'finish', 'end', 'livechat', 'intent'],
  NODE_UN_CLICKABLE: ['start', 'finish', 'end', 'menu'],
  SINGLE_NODES: ['livechat', 'intent'],
  STEP_NODES: [
    'buttons',
    'capture',
    'choice',
    'email',
    'form',
    'text',
    'variables',
    'qna',
    'carousel',
    'api',
    'condition',
    'table',
    'findRecord',
    'insertRecord',
    'updateRecord',
    'deleteRecord',
    'entity',
    'customTemplate',
    'gai',
    'script',
    'dynamic',
  ],
  PANEL_CONTEXT_MENU_DELAY: 100,
  SHADOW_NODE_DATA_TYPE: {
    id: '',
    parentId: '',
    data: { properties: {} },
    position: { x: 0, y: 0 },
  },
}));

vi.mock('@/utils/flow', () => ({
  canDropToBlock: vi.fn(() => true),
  createDndEngine: vi.fn(() => ({
    execute: vi.fn(),
  })),
  dropSingleNodeFromToolbarToPanel: vi.fn(),
  findEdgesByNodeIds: vi.fn(() => []),
  findTargetBlockNode: vi.fn(() => null),
  generateReactFlowNode: vi.fn(() => ({ id: 'test-node', type: 'text', position: { x: 0, y: 0 } })),
  getContextMenuPosition: vi.fn(() => ({ x: 0, y: 0 })),
  getPositionFromScreen: vi.fn(() => ({ x: 0, y: 0 })),
  isDragItemOutsideFlowEditor: vi.fn(() => false),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: (key: string) => key }),
}));

// Mock the t function used in the hook

vi.mock('@xyflow/react', () => ({
  applyNodeChanges: vi.fn((_changes, nodes) => nodes),
}));

const mockSetSearchParams = vi.fn();
vi.mock('react-router-dom', () => ({
  useSearchParams: () => [new URLSearchParams(), mockSetSearchParams],
}));

vi.mock('lodash', () => ({
  delay: vi.fn((fn, ms) => {
    // Use real setTimeout for proper coverage tracking
    return setTimeout(fn, ms);
  }),
  get: vi.fn((obj, path) => {
    // Simple implementation of lodash get for dot notation paths
    if (typeof path === 'string') {
      return path.split('.').reduce((current, key) => current?.[key], obj);
    }
    return obj?.[path];
  }),
  sum: vi.fn((arr) => arr.reduce((a, b) => a + b, 0)),
}));

// Mock contexts
const mockUseAppContext = vi.fn();
const mockUseFlowContext = vi.fn();

vi.mock('@/contexts', () => ({
  useAppContext: () => mockUseAppContext(),
  useFlowContext: () => mockUseFlowContext(),
}));

// Test component that uses the hook
function TestComponent() {
  useNodeHandlersContext();
  return <div data-testid='test-component' />;
}

function renderWithProvider() {
  return render(
    <NodeHandlersProvider>
      <TestComponent />
    </NodeHandlersProvider>
  );
}

describe('useNodeHandlers', () => {
  let mockFlowContext: any;

  beforeEach(() => {
    vi.useFakeTimers();
    mockFlowContext = {
      flowRef: {
        current: { getBoundingClientRect: () => ({ x: 0, y: 0, width: 100, height: 100 }) },
      },
      selectedNode: null,
      isFlowEdited: { current: false },
      nodeActiveData: { current: null },
      reactFlowInstance: {
        getInternalNode: vi.fn(),
        getNodes: vi.fn(() => []),
        getEdges: vi.fn(() => []),
        screenToFlowPosition: vi.fn(() => ({ x: 0, y: 0 })),
        setNodes: vi.fn(),
        getNode: vi.fn(),
      },
      pendingDeleteStatus: TPendingDeleteStatus.IDLE,
      isOpeningSettingDrawer: false,
      dataPendingRemoveRef: { current: { nodes: [], edges: [] } },
      activeToolbarItem: null,
      toolbarItemDraggingRef: {
        current: { getBoundingClientRect: () => ({ x: 0, y: 0, width: 100, height: 100 }) },
      },
      contextMenu: { open: false },
      setActiveToolbarItem: vi.fn(),
      setDragging: vi.fn(),
      resetFlowEditorState: vi.fn(),
      setPanelContextMenu: vi.fn(),
      setContextMenu: vi.fn(),
      setSelectedNode: vi.fn(),
      setSelectedNodes: vi.fn(),
      setOpenSettingDrawer: vi.fn(),
      setPendingDeleteStatus: vi.fn(),
      setIsOpeningSettingDrawer: vi.fn(),
      setIsAllowDropToTargetBlock: vi.fn(),
      settings: { limitations: { cardsPerNode: 10 } },
    };

    // Mock the context hooks
    mockUseAppContext.mockReturnValue({
      settings: { limitations: { cardsPerNode: 10 } },
    });
    mockUseFlowContext.mockReturnValue(mockFlowContext);
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.clearAllMocks();
    // Reset the mock context to original state
    mockFlowContext = {
      flowRef: {
        current: { getBoundingClientRect: () => ({ x: 0, y: 0, width: 100, height: 100 }) },
      },
      selectedNode: null,
      isFlowEdited: { current: false },
      nodeActiveData: { current: null },
      reactFlowInstance: {
        getInternalNode: vi.fn(),
        getNodes: vi.fn(() => []),
        getEdges: vi.fn(() => []),
        screenToFlowPosition: vi.fn(() => ({ x: 0, y: 0 })),
        setNodes: vi.fn(),
        getNode: vi.fn(),
      },
      pendingDeleteStatus: TPendingDeleteStatus.IDLE,
      isOpeningSettingDrawer: false,
      dataPendingRemoveRef: { current: { nodes: [], edges: [] } },
      activeToolbarItem: null,
      toolbarItemDraggingRef: {
        current: { getBoundingClientRect: () => ({ x: 0, y: 0, width: 100, height: 100 }) },
      },
      contextMenu: { open: false },
      setActiveToolbarItem: vi.fn(),
      setDragging: vi.fn(),
      resetFlowEditorState: vi.fn(),
      setPanelContextMenu: vi.fn(),
      setContextMenu: vi.fn(),
      setSelectedNode: vi.fn(),
      setSelectedNodes: vi.fn(),
      setOpenSettingDrawer: vi.fn(),
      setPendingDeleteStatus: vi.fn(),
      setIsOpeningSettingDrawer: vi.fn(),
      setIsAllowDropToTargetBlock: vi.fn(),
      settings: { limitations: { cardsPerNode: 10 } },
    };
    mockUseFlowContext.mockReturnValue(mockFlowContext);
  });

  describe('NodeHandlersProvider', () => {
    it('should provide context to children', () => {
      const { getByTestId } = renderWithProvider();
      expect(getByTestId('test-component')).toBeInTheDocument();
    });

    it('should throw error when used outside provider', () => {
      const consoleError = vi.spyOn(console, 'error').mockImplementation(() => {});

      const TestComponentWithoutProvider = () => {
        useNodeHandlersContext();
        return <div />;
      };

      expect(() => render(<TestComponentWithoutProvider />)).toThrow(
        'useNodeHandlersContext must be used inside NodeHandlersProvider'
      );

      consoleError.mockRestore();
    });
  });

  describe('useNodeHandlers hook', () => {
    it('should return editor and toolbar handlers', () => {
      const { getByTestId } = renderWithProvider();
      expect(getByTestId('test-component')).toBeInTheDocument();
    });
  });

  describe('onNodeDragStop', () => {
    it('should handle skip dragging nodes', () => {
      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Start,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { clientX: 0, clientY: 0 } as MouseEvent;

        handlers.editor.onNodeDragStop(event, node, []);

        expect(mockFlowContext.setDragging).toHaveBeenCalledWith(false);
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle single nodes', () => {
      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Livechat,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { clientX: 0, clientY: 0 } as MouseEvent;

        handlers.editor.onNodeDragStop(event, node, []);

        expect(mockFlowContext.setDragging).toHaveBeenCalledWith(false);
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle missing reactFlowInstance', () => {
      mockFlowContext.reactFlowInstance = null;

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { clientX: 0, clientY: 0 } as MouseEvent;

        handlers.editor.onNodeDragStop(event, node, []);

        expect(mockFlowContext.setDragging).toHaveBeenCalledWith(false);
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle missing nodeActiveData', () => {
      mockFlowContext.nodeActiveData.current = null;

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { clientX: 0, clientY: 0 } as MouseEvent;

        handlers.editor.onNodeDragStop(event, node, []);

        expect(mockFlowContext.setDragging).toHaveBeenCalledWith(false);
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle step node with no target and parentId - DropStepToOutsideBlock', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);
      const mockDndEngine = { execute: vi.fn() };
      mockFlowUtils.createDndEngine.mockReturnValue(mockDndEngine);
      mockFlowUtils.findTargetBlockNode.mockReturnValue(undefined);

      // Set up proper mock context
      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        nodeActiveData: {
          current: {
            id: 'test',
            type: ENodeType.Text,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
        },
        reactFlowInstance: {
          ...mockFlowContext.reactFlowInstance,
          getInternalNode: vi.fn().mockImplementation((nodeId) => {
            if (nodeId === 'test') {
              return {
                id: 'test',
                type: ENodeType.Text,
                position: { x: 0, y: 0 },
                data: { properties: {} },
              };
            }
            return null; // For empty string or other IDs
          }),
        },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Text, // This is a step node
          position: { x: 0, y: 0 },
          data: { properties: {} },
          parentId: 'parent-block', // Has parentId
        };
        const event = { clientX: 0, clientY: 0 } as MouseEvent;

        handlers.editor.onNodeDragStop(event, node, []);

        expect(mockDndEngine.execute).toHaveBeenCalledWith('dropStepToOutsideBlock');
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle non-step node with target and can drop - DropBlockToBlock', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);
      const mockDndEngine = { execute: vi.fn() };
      mockFlowUtils.createDndEngine.mockReturnValue(mockDndEngine);
      mockFlowUtils.findTargetBlockNode.mockReturnValue({
        id: 'target-block',
        type: ENodeType.Block,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });
      mockFlowUtils.canDropToBlock.mockReturnValue(true);

      // Set up proper mock context
      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        nodeActiveData: {
          current: {
            id: 'test',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
        },
        reactFlowInstance: {
          ...mockFlowContext.reactFlowInstance,
          getInternalNode: vi
            .fn()
            .mockReturnValueOnce({
              id: 'test',
              type: ENodeType.Block,
              position: { x: 0, y: 0 },
              data: { properties: {} },
            })
            .mockReturnValueOnce({
              id: 'target-block',
              type: ENodeType.Block,
              position: { x: 0, y: 0 },
              data: { properties: {} },
            }),
        },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Block,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { clientX: 0, clientY: 0 } as MouseEvent;

        handlers.editor.onNodeDragStop(event, node, []);

        expect(mockDndEngine.execute).toHaveBeenCalledWith('dropBlockToBlock');
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle step node with different parent and can drop - DropStepToBlock', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);
      const mockDndEngine = { execute: vi.fn() };
      mockFlowUtils.createDndEngine.mockReturnValue(mockDndEngine);
      mockFlowUtils.findTargetBlockNode.mockReturnValue({
        id: 'target-block',
        type: ENodeType.Block,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });
      mockFlowUtils.canDropToBlock.mockReturnValue(true);

      // Set up proper mock context
      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        nodeActiveData: {
          current: {
            id: 'test',
            type: ENodeType.Text,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
        },
        reactFlowInstance: {
          ...mockFlowContext.reactFlowInstance,
          getInternalNode: vi.fn().mockImplementation((nodeId) => {
            if (nodeId === 'test') {
              return {
                id: 'test',
                type: ENodeType.Text,
                position: { x: 0, y: 0 },
                data: { properties: {} },
              };
            }
            if (nodeId === 'target-block') {
              return {
                id: 'target-block',
                type: ENodeType.Block,
                position: { x: 0, y: 0 },
                data: { properties: {} },
              };
            }
            return null;
          }),
        },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
          parentId: 'different-parent',
        };
        const event = { clientX: 0, clientY: 0 } as MouseEvent;

        handlers.editor.onNodeDragStop(event, node, []);

        expect(mockDndEngine.execute).toHaveBeenCalledWith('dropStepToBlock');
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle step node with same parent - DropStepToOldBlock', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);
      const mockDndEngine = { execute: vi.fn() };
      mockFlowUtils.createDndEngine.mockReturnValue(mockDndEngine);
      mockFlowUtils.findTargetBlockNode.mockReturnValue({
        id: 'same-parent',
        type: ENodeType.Block,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });

      // Set up proper mock context
      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        nodeActiveData: {
          current: {
            id: 'test',
            type: ENodeType.Text,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
        },
        reactFlowInstance: {
          ...mockFlowContext.reactFlowInstance,
          getInternalNode: vi.fn().mockImplementation((nodeId) => {
            if (nodeId === 'test') {
              return {
                id: 'test',
                type: ENodeType.Text,
                position: { x: 0, y: 0 },
                data: { properties: {} },
              };
            }
            if (nodeId === 'same-parent') {
              return {
                id: 'same-parent',
                type: ENodeType.Block,
                position: { x: 0, y: 0 },
                data: { properties: {} },
              };
            }
            return null;
          }),
        },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
          parentId: 'same-parent',
        };
        const event = { clientX: 0, clientY: 0 } as MouseEvent;

        handlers.editor.onNodeDragStop(event, node, []);

        expect(mockDndEngine.execute).toHaveBeenCalledWith('dropStepToOldBlock');
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle non-step node without target - ResetNodeState', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);
      const mockDndEngine = { execute: vi.fn() };
      mockFlowUtils.createDndEngine.mockReturnValue(mockDndEngine);
      mockFlowUtils.findTargetBlockNode.mockReturnValue({
        id: 'target-block',
        type: ENodeType.Block,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });
      mockFlowUtils.canDropToBlock.mockReturnValue(false); // Can't drop

      // Set up proper mock context
      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        nodeActiveData: {
          current: {
            id: 'test',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
        },
        reactFlowInstance: {
          ...mockFlowContext.reactFlowInstance,
          getInternalNode: vi.fn().mockImplementation((nodeId) => {
            if (nodeId === 'test') {
              return {
                id: 'test',
                type: ENodeType.Block,
                position: { x: 0, y: 0 },
                data: { properties: {} },
              };
            }
            if (nodeId === 'target-block') {
              return {
                id: 'target-block',
                type: ENodeType.Block,
                position: { x: 0, y: 0 },
                data: { properties: {} },
              };
            }
            return null;
          }),
        },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Block, // Non-step node
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { clientX: 0, clientY: 0 } as MouseEvent;

        handlers.editor.onNodeDragStop(event, node, []);

        expect(mockDndEngine.execute).toHaveBeenCalledWith('resetNodeState');
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle same node id as target', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);
      mockFlowUtils.findTargetBlockNode.mockReturnValue({
        id: 'test',
        type: ENodeType.Block,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { clientX: 0, clientY: 0 } as MouseEvent;

        handlers.editor.onNodeDragStop(event, node, []);

        expect(mockFlowContext.setDragging).toHaveBeenCalledWith(false);
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle same node id as target - covering line 88', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);
      mockFlowUtils.findTargetBlockNode.mockReturnValue({
        id: 'test-node',
        type: ENodeType.Block,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });

      // Set up proper mock context
      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        nodeActiveData: {
          current: {
            id: 'test-node',
            type: ENodeType.Text,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
        },
        reactFlowInstance: {
          ...mockFlowContext.reactFlowInstance,
          getInternalNode: vi.fn().mockImplementation((nodeId) => {
            if (nodeId === 'test-node') {
              return {
                id: 'test-node',
                type: ENodeType.Text,
                position: { x: 0, y: 0 },
                data: { properties: {} },
              };
            }
            return null;
          }),
        },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test-node', // Same as targetNodeId
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { clientX: 0, clientY: 0 } as MouseEvent;

        handlers.editor.onNodeDragStop(event, node, []);

        expect(mockFlowContext.setDragging).toHaveBeenCalledWith(false);
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle missing dragNode', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);
      mockFlowUtils.findTargetBlockNode.mockReturnValue({
        id: 'target-block',
        type: ENodeType.Block,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { clientX: 0, clientY: 0 } as MouseEvent;

        // Mock getInternalNode to return null
        mockFlowContext.reactFlowInstance.getInternalNode.mockReturnValue(null);

        handlers.editor.onNodeDragStop(event, node, []);

        expect(mockFlowContext.setDragging).toHaveBeenCalledWith(false);
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle missing dragNode - covering line 93', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);
      mockFlowUtils.findTargetBlockNode.mockReturnValue({
        id: 'target-block',
        type: ENodeType.Block,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });

      // Set up proper mock context
      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        nodeActiveData: {
          current: {
            id: 'test-node',
            type: ENodeType.Text,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
        },
        reactFlowInstance: {
          ...mockFlowContext.reactFlowInstance,
          getInternalNode: vi.fn().mockImplementation((nodeId) => {
            if (nodeId === 'test-node') {
              return null; // Return null for dragNode to trigger line 93
            }
            if (nodeId === 'target-block') {
              return {
                id: 'target-block',
                type: ENodeType.Block,
                position: { x: 0, y: 0 },
                data: { properties: {} },
              };
            }
            return null;
          }),
        },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test-node',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { clientX: 0, clientY: 0 } as MouseEvent;

        handlers.editor.onNodeDragStop(event, node, []);

        expect(mockFlowContext.setDragging).toHaveBeenCalledWith(false);
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle missing targetNode - early return', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);
      mockFlowUtils.findTargetBlockNode.mockReturnValue(undefined);

      // Set up proper mock context
      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        nodeActiveData: {
          current: {
            id: 'test',
            type: ENodeType.Text,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
        },
        reactFlowInstance: {
          ...mockFlowContext.reactFlowInstance,
          getInternalNode: vi.fn().mockImplementation((nodeId) => {
            if (nodeId === 'test') {
              return {
                id: 'test',
                type: ENodeType.Text,
                position: { x: 0, y: 0 },
                data: { properties: {} },
              };
            }
            return null; // For empty string or other IDs
          }),
        },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { clientX: 0, clientY: 0 } as MouseEvent;

        handlers.editor.onNodeDragStop(event, node, []);

        expect(mockFlowContext.setDragging).toHaveBeenCalledWith(false);
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });
  });

  describe('onNodeDrag', () => {
    it('should handle skip dragging nodes', () => {
      // Create a fresh mock context for this test
      const freshMockContext = {
        ...mockFlowContext,
        reactFlowInstance: {
          ...mockFlowContext.reactFlowInstance,
          setNodes: vi.fn(),
        },
      };
      mockUseFlowContext.mockReturnValue(freshMockContext);

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Start,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { clientX: 0, clientY: 0 } as MouseEvent;

        handlers.editor.onNodeDrag(event, node, []);

        // For skip dragging nodes, setNodes should not be called
        expect(freshMockContext.reactFlowInstance.setNodes).not.toHaveBeenCalled();
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle single nodes', () => {
      // Create a fresh mock context for this test
      const freshMockContext = {
        ...mockFlowContext,
        reactFlowInstance: {
          ...mockFlowContext.reactFlowInstance,
          setNodes: vi.fn(),
        },
      };
      mockUseFlowContext.mockReturnValue(freshMockContext);

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Livechat,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { clientX: 0, clientY: 0 } as MouseEvent;

        handlers.editor.onNodeDrag(event, node, []);

        expect(freshMockContext.reactFlowInstance.setNodes).not.toHaveBeenCalled();
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle missing reactFlowInstance', () => {
      mockFlowContext.reactFlowInstance = null;

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { clientX: 0, clientY: 0 } as MouseEvent;

        handlers.editor.onNodeDrag(event, node, []);

        // Should not throw error
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });
  });

  describe('onNodeDragStart', () => {
    it('should set dragging state and reset flow editor', () => {
      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };

        handlers.editor.onNodeDragStart({} as MouseEvent, node, []);

        expect(mockFlowContext.setDragging).toHaveBeenCalledWith(true);
        expect(mockFlowContext.resetFlowEditorState).toHaveBeenCalled();
        expect(mockFlowContext.nodeActiveData.current).toBe(node);
        expect(mockFlowContext.isFlowEdited.current).toBe(true);
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });
  });

  describe('onNodesDelete', () => {
    it('should handle missing reactFlowInstance', () => {
      // Update the mock context with null reactFlowInstance
      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        reactFlowInstance: null,
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const nodes = [
          { id: 'test', type: ENodeType.Text, position: { x: 0, y: 0 }, data: { properties: {} } },
        ];

        handlers.editor.onNodesDelete(nodes);

        // Should not call setPendingDeleteStatus when reactFlowInstance is null
        expect(mockFlowContext.setPendingDeleteStatus).not.toHaveBeenCalled();
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should set pending delete status and collect nodes/edges', async () => {
      const flowUtils = await import('@/utils/flow');
      vi.mocked(flowUtils.findEdgesByNodeIds).mockReturnValue([
        { id: 'edge1', source: 'test', target: 'other' },
      ]);

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const nodes = [
          { id: 'test', type: ENodeType.Text, position: { x: 0, y: 0 }, data: { properties: {} } },
        ];

        mockFlowContext.reactFlowInstance.getEdges.mockReturnValue([
          { id: 'edge1', source: 'test', target: 'other' },
        ]);

        handlers.editor.onNodesDelete(nodes);

        expect(mockFlowContext.setPendingDeleteStatus).toHaveBeenCalledWith(
          TPendingDeleteStatus.PENDING
        );
        expect(mockFlowContext.dataPendingRemoveRef.current.nodes).toEqual([
          { id: 'test', type: 'remove' },
        ]);
        expect(mockFlowContext.dataPendingRemoveRef.current.edges).toEqual([
          { id: 'edge1', type: 'remove' },
        ]);
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });
  });

  describe('onNodeClick', () => {
    it('should prevent default when same node is selected', () => {
      // Update the mock context with selected node
      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        selectedNode: { id: 'test', type: 'text' },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { preventDefault: vi.fn() } as any;

        const result = handlers.editor.onNodeClick(event, node);

        // The function should return the preventDefault call
        expect(result).toBe(event.preventDefault());
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should prevent default when context menu is open', () => {
      mockFlowContext.contextMenu.open = true;

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { preventDefault: vi.fn() } as any;

        handlers.editor.onNodeClick(event, node);

        expect(event.preventDefault).toHaveBeenCalled();
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle end action type', () => {
      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: { actionType: EnumActionTypes.end } },
        };
        const event = { preventDefault: vi.fn() } as any;

        handlers.editor.onNodeClick(event, node);

        // For end action type, it should return early without delay
        expect(mockSetSearchParams).toHaveBeenCalledWith(expect.any(Function));
        expect(mockFlowContext.setSelectedNodes).toHaveBeenCalledWith([node]);
        expect(mockFlowContext.setSelectedNode).toHaveBeenCalledWith(node);
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle end action type with early return - covering lines 207-210', () => {
      // Clear mocks to ensure clean state
      vi.clearAllMocks();

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'end-action-node',
          type: ENodeType.Text,
          position: { x: 100, y: 100 },
          data: { properties: { actionType: EnumActionTypes.end } },
        };
        const event = { preventDefault: vi.fn() } as any;

        // Call onNodeClick and capture the return value
        const result = handlers.editor.onNodeClick(event, node);

        // Verify that setSearchParams was called
        expect(mockSetSearchParams).toHaveBeenCalledWith(expect.any(Function));

        // Verify that setSelectedNodes and setSelectedNode were called immediately
        expect(mockFlowContext.setSelectedNodes).toHaveBeenCalledWith([node]);
        expect(mockFlowContext.setSelectedNode).toHaveBeenCalledWith(node);

        // Verify that the function returns early (no delay callbacks)
        expect(result).toBeUndefined();

        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should test setSearchParams callback function', () => {
      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test-node',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { preventDefault: vi.fn() } as any;

        handlers.editor.onNodeClick(event, node);

        // Test the setSearchParams callback function
        const setSearchParamsCallback = mockSetSearchParams.mock.calls[0][0];
        const mockPrev = new URLSearchParams();
        const result = setSearchParamsCallback(mockPrev);

        expect(result.get('node')).toBe('test-node');
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should test delay callback for end action type', async () => {
      // Clear mocks to ensure clean state
      vi.clearAllMocks();

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test-end-node',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: { actionType: EnumActionTypes.end } },
        };
        const event = { preventDefault: vi.fn() } as any;

        handlers.editor.onNodeClick(event, node);

        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );

      // Wait for the delay to complete
      await act(async () => {
        await vi.runAllTimersAsync();
      });

      expect(mockFlowContext.setSelectedNodes).toHaveBeenCalledWith([
        {
          id: 'test-end-node',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: { actionType: EnumActionTypes.end } },
        },
      ]);
      expect(mockFlowContext.setSelectedNode).toHaveBeenCalledWith({
        id: 'test-end-node',
        type: ENodeType.Text,
        position: { x: 0, y: 0 },
        data: { properties: { actionType: EnumActionTypes.end } },
      });
    });

    it('should test delay callback execution with different end action node', async () => {
      // Clear mocks to ensure clean state
      vi.clearAllMocks();

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'another-end-node',
          type: ENodeType.Text,
          position: { x: 100, y: 100 },
          data: { properties: { actionType: EnumActionTypes.end } },
        };
        const event = { preventDefault: vi.fn() } as any;

        handlers.editor.onNodeClick(event, node);

        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );

      // Wait for the delay to complete
      await act(async () => {
        await vi.runAllTimersAsync();
      });

      // Verify the delay callback was executed
      expect(mockFlowContext.setSelectedNodes).toHaveBeenCalledWith([
        {
          id: 'another-end-node',
          type: ENodeType.Text,
          position: { x: 100, y: 100 },
          data: { properties: { actionType: EnumActionTypes.end } },
        },
      ]);
      expect(mockFlowContext.setSelectedNode).toHaveBeenCalledWith({
        id: 'another-end-node',
        type: ENodeType.Text,
        position: { x: 100, y: 100 },
        data: { properties: { actionType: EnumActionTypes.end } },
      });
    });

    it('should handle unclickable nodes', () => {
      // Reset mocks before this test
      vi.clearAllMocks();

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Start,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { preventDefault: vi.fn() } as any;

        handlers.editor.onNodeClick(event, node);

        expect(mockFlowContext.setIsOpeningSettingDrawer).toHaveBeenCalledWith(false);
        expect(mockFlowContext.setOpenSettingDrawer).toHaveBeenCalledWith(false);
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle normal node click', async () => {
      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { preventDefault: vi.fn() } as any;

        handlers.editor.onNodeClick(event, node);

        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );

      // Wait for the delay to complete
      await act(async () => {
        await vi.runAllTimersAsync();
      });

      expect(mockSetSearchParams).toHaveBeenCalledWith(expect.any(Function));
      expect(mockFlowContext.setSelectedNodes).toHaveBeenCalledWith([
        {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        },
      ]);
      expect(mockFlowContext.setSelectedNode).toHaveBeenCalledWith({
        id: 'test',
        type: ENodeType.Text,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });
      expect(mockFlowContext.setIsOpeningSettingDrawer).toHaveBeenCalledWith(true);
      expect(mockFlowContext.setOpenSettingDrawer).toHaveBeenCalledWith(true);
    });

    it('should handle normal node click with isOpeningSettingDrawer true', async () => {
      // Update the mock context with isOpeningSettingDrawer true
      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        isOpeningSettingDrawer: true,
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { preventDefault: vi.fn() } as any;

        handlers.editor.onNodeClick(event, node);

        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );

      // Wait for the delay to complete
      await act(async () => {
        await vi.runAllTimersAsync();
      });

      expect(mockSetSearchParams).toHaveBeenCalledWith(expect.any(Function));
      expect(mockFlowContext.setSelectedNodes).toHaveBeenCalledWith([
        {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        },
      ]);
      expect(mockFlowContext.setSelectedNode).toHaveBeenCalledWith({
        id: 'test',
        type: ENodeType.Text,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });
      expect(mockFlowContext.setIsOpeningSettingDrawer).toHaveBeenCalledWith(true);
      expect(mockFlowContext.setOpenSettingDrawer).toHaveBeenCalledWith(true);
    });
  });

  describe('onNodeContextMenu', () => {
    it('should prevent default and close menus', () => {
      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { preventDefault: vi.fn() } as any;

        handlers.editor.onNodeContextMenu(event, node);

        expect(event.preventDefault).toHaveBeenCalled();
        expect(mockFlowContext.setContextMenu).toHaveBeenCalledWith(expect.any(Function));
        expect(mockFlowContext.setPanelContextMenu).toHaveBeenCalledWith(expect.any(Function));
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle missing flowRef', () => {
      mockFlowContext.flowRef.current = null;

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { preventDefault: vi.fn() } as any;

        handlers.editor.onNodeContextMenu(event, node);

        expect(mockFlowContext.setSelectedNodes).not.toHaveBeenCalled();
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle null node', () => {
      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = null as any;
        const event = { preventDefault: vi.fn() } as any;

        handlers.editor.onNodeContextMenu(event, node);

        expect(mockFlowContext.setSelectedNodes).not.toHaveBeenCalled();
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle basic nodes', () => {
      // Reset mocks before this test
      vi.clearAllMocks();

      // Create a fresh mock context for this test
      const freshMockContext = {
        ...mockFlowContext,
        setSelectedNodes: vi.fn(),
      };
      mockUseFlowContext.mockReturnValue(freshMockContext);

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Start,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { preventDefault: vi.fn() } as any;

        handlers.editor.onNodeContextMenu(event, node);

        expect(freshMockContext.setSelectedNodes).not.toHaveBeenCalled();
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should open context menu for valid nodes', async () => {
      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { preventDefault: vi.fn() } as any;

        handlers.editor.onNodeContextMenu(event, node);

        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );

      // Wait for the delay to complete
      await act(async () => {
        await vi.runAllTimersAsync();
      });

      expect(mockFlowContext.setSelectedNodes).toHaveBeenCalledWith([
        {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        },
      ]);
      expect(mockFlowContext.setContextMenu).toHaveBeenCalledWith({
        open: true,
        node: {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        },
        position: { x: 0, y: 0 },
      });
    });
  });

  describe('onNodesChange', () => {
    it('should handle missing reactFlowInstance', () => {
      mockFlowContext.reactFlowInstance = null;

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const changes = [{ id: 'test', type: 'remove' as const }];

        handlers.editor.onNodesChange(changes);

        // Should not throw error when reactFlowInstance is null
        expect(true).toBe(true);
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle pending delete status', () => {
      mockFlowContext.pendingDeleteStatus = TPendingDeleteStatus.PENDING;

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const changes = [{ id: 'test', type: 'remove' as const }];

        handlers.editor.onNodesChange(changes);

        expect(mockFlowContext.reactFlowInstance.setNodes).not.toHaveBeenCalled();
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle confirmed delete status', () => {
      // Update the mock context with confirmed status
      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        pendingDeleteStatus: TPendingDeleteStatus.CONFIRMED,
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const changes = [{ id: 'test', type: 'remove' as const }];

        handlers.editor.onNodesChange(changes);

        expect(mockFlowContext.reactFlowInstance.setNodes).toHaveBeenCalled();
        expect(mockFlowContext.setPendingDeleteStatus).toHaveBeenCalledWith(
          TPendingDeleteStatus.FINISHED
        );
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle remove menu nodes', () => {
      mockFlowContext.pendingDeleteStatus = TPendingDeleteStatus.IDLE;
      mockFlowContext.reactFlowInstance.getNode.mockReturnValue({ type: 'menu' });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const changes = [{ id: 'test', type: 'remove' as const }];

        handlers.editor.onNodesChange(changes);

        // Should call setNodes for menu nodes when all changes are remove and all are menu nodes
        expect(mockFlowContext.reactFlowInstance.setNodes).toHaveBeenCalled();
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle non-remove changes', () => {
      mockFlowContext.pendingDeleteStatus = TPendingDeleteStatus.IDLE;

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const changes = [{ id: 'test', type: 'position' as const, position: { x: 0, y: 0 } }];

        handlers.editor.onNodesChange(changes);

        expect(mockFlowContext.reactFlowInstance.setNodes).toHaveBeenCalled();
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle remove changes with non-menu nodes', () => {
      mockFlowContext.pendingDeleteStatus = TPendingDeleteStatus.IDLE;
      mockFlowContext.reactFlowInstance.getNode.mockReturnValue({ type: 'text' });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const changes = [{ id: 'test', type: 'remove' as const }];

        handlers.editor.onNodesChange(changes);

        // Should not call setNodes for non-menu nodes
        expect(mockFlowContext.reactFlowInstance.setNodes).not.toHaveBeenCalled();
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });
  });

  describe('handleToolbarDragStart', () => {
    it('should set dragging state and active toolbar item', () => {
      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const event = {
          active: { data: { current: { type: ENodeType.Text } } },
          activatorEvent: {} as any,
          collisions: null,
          delta: { x: 0, y: 0 },
          over: null,
        } as any;

        handlers.toolbar.handleToolbarDragStart(event);

        expect(mockFlowContext.setDragging).toHaveBeenCalledWith(true);
        expect(mockFlowContext.resetFlowEditorState).toHaveBeenCalled();
        expect(mockFlowContext.setActiveToolbarItem).toHaveBeenCalledWith({ type: ENodeType.Text });
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });
  });

  describe('handleToolbarDragMove', () => {
    it('should handle missing reactFlowInstance', () => {
      mockFlowContext.reactFlowInstance = null;

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const event = {} as any;

        handlers.toolbar.handleToolbarDragMove(event);

        // Should not throw error when reactFlowInstance is null
        expect(true).toBe(true);
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle missing toolbar item', () => {
      mockFlowContext.activeToolbarItem = null;

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const event = {} as any;

        handlers.toolbar.handleToolbarDragMove(event);

        expect(mockFlowContext.reactFlowInstance.setNodes).not.toHaveBeenCalled();
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle single nodes', () => {
      // Update the mock context with single node type
      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        activeToolbarItem: { type: 'Livechat' },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const event = {} as any;

        // Call the function and verify it returns early
        const result = handlers.toolbar.handleToolbarDragMove(event);

        // The function should return undefined (early return)
        expect(result).toBeUndefined();
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle single nodes - covering line 297', () => {
      // Update the mock context with single node type
      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        activeToolbarItem: { type: 'intent' }, // Another single node type
        reactFlowInstance: {
          ...mockFlowContext.reactFlowInstance,
          setNodes: vi.fn(),
        },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const event = {} as any;

        // Call the function and verify it returns early
        const result = handlers.toolbar.handleToolbarDragMove(event);

        // The function should return undefined (early return)
        expect(result).toBeUndefined();
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle step nodes with target block', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);

      // Clear any existing mocks
      vi.clearAllMocks();

      mockFlowUtils.findTargetBlockNode.mockReturnValue({
        id: 'block-node',
        type: ENodeType.Block,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });
      mockFlowUtils.canDropToBlock.mockReturnValue(true);

      // Update the mock context with the required properties
      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        activeToolbarItem: { type: 'text' },
        settings: { limitations: { cardsPerNode: 10 } },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const event = {} as any;

        handlers.toolbar.handleToolbarDragMove(event);

        expect(mockFlowUtils.findTargetBlockNode).toHaveBeenCalled();
        expect(mockFlowUtils.canDropToBlock).toHaveBeenCalled();
        // Note: setIsAllowDropToTargetBlock is not called in the current implementation
        // expect(mockFlowContext.setIsAllowDropToTargetBlock).toHaveBeenCalledWith(true);
        expect(mockFlowContext.reactFlowInstance.setNodes).toHaveBeenCalledWith(
          expect.any(Function)
        );

        // Test the setNodes callback function
        const setNodesCallback = mockFlowContext.reactFlowInstance.setNodes.mock.calls[0][0];
        const mockNodes = [
          {
            id: 'block-node',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {}, isAddNewStep: false, isStepInvalid: false },
          },
          {
            id: 'other-node',
            type: ENodeType.Text,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
        ];
        const result = setNodesCallback(mockNodes);

        expect(result).toEqual([
          {
            id: 'block-node',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {}, isAddNewStep: true, isStepInvalid: false },
          },
          {
            id: 'other-node',
            type: ENodeType.Text,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
        ]);

        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle step nodes without target block', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);

      mockFlowUtils.findTargetBlockNode.mockReturnValue(undefined);

      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        activeToolbarItem: { type: ENodeType.Text },
        settings: { limitations: { cardsPerNode: 10 } },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const event = {} as any;

        handlers.toolbar.handleToolbarDragMove(event);

        expect(mockFlowUtils.findTargetBlockNode).toHaveBeenCalled();
        expect(mockFlowUtils.canDropToBlock).not.toHaveBeenCalled();
        expect(mockFlowContext.setIsAllowDropToTargetBlock).not.toHaveBeenCalled();
        expect(mockFlowContext.reactFlowInstance.setNodes).toHaveBeenCalled();
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle complex block node updates with different scenarios', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);

      mockFlowUtils.findTargetBlockNode.mockReturnValue({
        id: 'target-block',
        type: ENodeType.Block,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });
      mockFlowUtils.canDropToBlock.mockReturnValue(true);

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
          parentId: 'different-parent',
        };
        const event = { clientX: 0, clientY: 0 } as MouseEvent;

        // Mock getNode to return a node with height
        mockFlowContext.reactFlowInstance.getNode.mockReturnValue({
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        });

        handlers.editor.onNodeDrag(event, node, []);

        expect(mockFlowContext.reactFlowInstance.setNodes).toHaveBeenCalledWith(
          expect.any(Function)
        );

        // Test the setNodes callback function
        const setNodesCallback = mockFlowContext.reactFlowInstance.setNodes.mock.calls[0][0];
        const mockNodes = [
          {
            id: 'target-block',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {}, isAddNewStep: false, isStepInvalid: false },
            height: 100,
          },
          {
            id: 'other-block',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {}, isAddNewStep: false, isStepInvalid: false },
            height: 100,
          },
          {
            id: 'test',
            type: ENodeType.Text,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
        ];
        const result = setNodesCallback(mockNodes);

        expect(result).toEqual([
          {
            id: 'target-block',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {}, isAddNewStep: true, isStepInvalid: false },
            height: 148, // 100 + 48
          },
          {
            id: 'other-block',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {}, isAddNewStep: false, isStepInvalid: false },
            height: 100,
          },
          {
            id: 'test',
            type: ENodeType.Text,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
        ]);

        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle node with same id as target', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);

      mockFlowUtils.findTargetBlockNode.mockReturnValue({
        id: 'test',
        type: ENodeType.Block,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        };
        const event = { clientX: 0, clientY: 0 } as MouseEvent;

        handlers.editor.onNodeDrag(event, node, []);

        expect(mockFlowContext.reactFlowInstance.setNodes).toHaveBeenCalledWith(
          expect.any(Function)
        );

        // Test the setNodes callback function
        const setNodesCallback = mockFlowContext.reactFlowInstance.setNodes.mock.calls[0][0];
        const mockNodes = [
          {
            id: 'test',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {}, isAddNewStep: false, isStepInvalid: false },
            height: 100,
          },
        ];
        const result = setNodesCallback(mockNodes);

        // Should return the same node without changes when node.id === targetNodeId
        expect(result).toEqual([
          {
            id: 'test',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {}, isAddNewStep: false, isStepInvalid: false },
            height: 100,
          },
        ]);

        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle node drag with needExpandHeight false', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);

      mockFlowUtils.findTargetBlockNode.mockReturnValue({
        id: 'target-block',
        type: ENodeType.Block,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });
      mockFlowUtils.canDropToBlock.mockReturnValue(false); // Can't drop, so needExpandHeight will be false

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const node = {
          id: 'test',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
          parentId: 'target-block', // Same as target, so needExpandHeight will be false
        };
        const event = { clientX: 0, clientY: 0 } as MouseEvent;

        handlers.editor.onNodeDrag(event, node, []);

        expect(mockFlowContext.reactFlowInstance.setNodes).toHaveBeenCalledWith(
          expect.any(Function)
        );

        // Test the setNodes callback function
        const setNodesCallback = mockFlowContext.reactFlowInstance.setNodes.mock.calls[0][0];
        const mockNodes = [
          {
            id: 'target-block',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {}, isAddNewStep: false, isStepInvalid: false },
            height: 100,
          },
        ];
        const result = setNodesCallback(mockNodes);

        // Should not expand height when needExpandHeight is false
        expect(result).toEqual([
          {
            id: 'target-block',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {}, isAddNewStep: false, isStepInvalid: true },
            height: 100, // Height should remain the same
          },
        ]);

        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle step nodes with target block but cannot drop', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);

      mockFlowUtils.findTargetBlockNode.mockReturnValue({
        id: 'block-node',
        type: ENodeType.Block,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });
      mockFlowUtils.canDropToBlock.mockReturnValue(false);

      // Update the mock context with the required properties
      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        activeToolbarItem: { type: 'text' },
        settings: { limitations: { cardsPerNode: 10 } },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const event = {} as any;

        handlers.toolbar.handleToolbarDragMove(event);

        expect(mockFlowUtils.findTargetBlockNode).toHaveBeenCalled();
        expect(mockFlowUtils.canDropToBlock).toHaveBeenCalled();
        // Note: setIsAllowDropToTargetBlock is not called in the current implementation
        // expect(mockFlowContext.setIsAllowDropToTargetBlock).toHaveBeenCalledWith(false);
        expect(mockFlowContext.reactFlowInstance.setNodes).toHaveBeenCalledWith(
          expect.any(Function)
        );

        // Test the setNodes callback function when isDropToBlock is false
        const setNodesCallback = mockFlowContext.reactFlowInstance.setNodes.mock.calls[0][0];
        const mockNodes = [
          {
            id: 'block-node',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {}, isAddNewStep: false, isStepInvalid: false },
          },
          {
            id: 'other-node',
            type: ENodeType.Text,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
        ];
        const result = setNodesCallback(mockNodes);

        expect(result).toEqual([
          {
            id: 'block-node',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {}, isAddNewStep: false, isStepInvalid: true },
          },
          {
            id: 'other-node',
            type: ENodeType.Text,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
        ]);

        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle toolbar drag move with no target node', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);

      mockFlowUtils.findTargetBlockNode.mockReturnValue(undefined);

      // Update the mock context with the required properties
      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        activeToolbarItem: { type: 'text' },
        settings: { limitations: { cardsPerNode: 10 } },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const event = {} as any;

        handlers.toolbar.handleToolbarDragMove(event);

        expect(mockFlowUtils.findTargetBlockNode).toHaveBeenCalled();
        expect(mockFlowUtils.canDropToBlock).not.toHaveBeenCalled();
        expect(mockFlowContext.setIsAllowDropToTargetBlock).not.toHaveBeenCalled();
        expect(mockFlowContext.reactFlowInstance.setNodes).toHaveBeenCalledWith(
          expect.any(Function)
        );

        // Test the setNodes callback function when no target node
        const setNodesCallback = mockFlowContext.reactFlowInstance.setNodes.mock.calls[0][0];
        const mockNodes = [
          {
            id: 'block-node',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
        ];
        const result = setNodesCallback(mockNodes);

        expect(result).toEqual([
          {
            id: 'block-node',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
        ]);

        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle toolbar drag move with target node and can drop', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);

      mockFlowUtils.findTargetBlockNode.mockReturnValue({
        id: 'block-node',
        type: ENodeType.Block,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });
      mockFlowUtils.canDropToBlock.mockReturnValue(true);

      // Update the mock context with the required properties
      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        activeToolbarItem: { type: 'text' },
        settings: { limitations: { cardsPerNode: 10 } },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const event = {} as any;

        handlers.toolbar.handleToolbarDragMove(event);

        expect(mockFlowUtils.findTargetBlockNode).toHaveBeenCalled();
        expect(mockFlowUtils.canDropToBlock).toHaveBeenCalled();
        // Note: setIsAllowDropToTargetBlock is not called in the current implementation
        // expect(mockFlowContext.setIsAllowDropToTargetBlock).toHaveBeenCalledWith(true);
        expect(mockFlowContext.reactFlowInstance.setNodes).toHaveBeenCalledWith(
          expect.any(Function)
        );

        // Test the setNodes callback function when can drop
        const setNodesCallback = mockFlowContext.reactFlowInstance.setNodes.mock.calls[0][0];
        const mockNodes = [
          {
            id: 'block-node',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
          {
            id: 'other-block',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
        ];
        const result = setNodesCallback(mockNodes);

        expect(result).toEqual([
          {
            id: 'block-node',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {}, isAddNewStep: true, isStepInvalid: false },
          },
          {
            id: 'other-block',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {}, isAddNewStep: false, isStepInvalid: false },
          },
        ]);

        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });
  });

  describe('handleToolbarDragEnd', () => {
    it('should handle missing reactFlowInstance', () => {
      mockFlowContext.reactFlowInstance = null;

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const event = {} as any;

        handlers.toolbar.handleToolbarDragEnd(event);

        expect(mockFlowContext.setDragging).toHaveBeenCalledWith(false);
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle missing toolbar item', () => {
      mockFlowContext.activeToolbarItem = null;

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const event = {} as any;

        handlers.toolbar.handleToolbarDragEnd(event);

        expect(mockFlowContext.setDragging).toHaveBeenCalledWith(false);
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle drag outside flow editor', async () => {
      const flowUtils = await import('@/utils/flow');
      vi.mocked(flowUtils.isDragItemOutsideFlowEditor).mockReturnValue(true);

      // Update the mock context with active toolbar item
      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        activeToolbarItem: { type: ENodeType.Text },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const event = {} as any;

        handlers.toolbar.handleToolbarDragEnd(event);

        expect(mockFlowContext.setActiveToolbarItem).toHaveBeenCalledWith(null);
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle single nodes', async () => {
      const flowUtils = await import('@/utils/flow');
      vi.mocked(flowUtils.generateReactFlowNode).mockReturnValue({
        id: 'test',
        type: ENodeType.Livechat,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });
      vi.mocked(flowUtils.dropSingleNodeFromToolbarToPanel).mockImplementation(() => {});
      vi.mocked(flowUtils.isDragItemOutsideFlowEditor).mockReturnValue(false);

      // Update the mock context with active toolbar item
      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        activeToolbarItem: { type: ENodeType.Livechat },
        isFlowEdited: { current: false },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const event = {} as any;

        handlers.toolbar.handleToolbarDragEnd(event);

        // Should call dropSingleNodeFromToolbarToPanel for single nodes
        expect(flowUtils.dropSingleNodeFromToolbarToPanel).toHaveBeenCalled();
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should set selected node and open setting drawer', async () => {
      const flowUtils = await import('@/utils/flow');
      vi.mocked(flowUtils.generateReactFlowNode).mockReturnValue({
        id: 'test',
        type: ENodeType.Text,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });
      vi.mocked(flowUtils.isDragItemOutsideFlowEditor).mockReturnValue(false);

      // Update the mock context with active toolbar item
      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        activeToolbarItem: { type: ENodeType.Text },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const event = {} as any;

        handlers.toolbar.handleToolbarDragEnd(event);

        expect(mockFlowContext.setActiveToolbarItem).toHaveBeenCalledWith(null);
        // The setTimeout calls are tested separately
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle drop to block for step nodes', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);

      mockFlowUtils.isDragItemOutsideFlowEditor.mockReturnValue(false);
      mockFlowUtils.generateReactFlowNode.mockReturnValue({
        id: 'test-node',
        type: ENodeType.Text,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });
      mockFlowUtils.findTargetBlockNode.mockReturnValue({
        id: 'block-node',
        type: ENodeType.Block,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });
      mockFlowUtils.createDndEngine.mockReturnValue({ execute: vi.fn() });

      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        activeToolbarItem: { type: ENodeType.Text },
        isAllowDropToTargetBlock: true,
        isFlowEdited: { current: false },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const event = {} as any;

        handlers.toolbar.handleToolbarDragEnd(event);

        expect(mockFlowUtils.createDndEngine).toHaveBeenCalledWith({
          reactFlowInstance: mockFlowContext.reactFlowInstance,
          dragNode: {
            id: 'test-node',
            type: ENodeType.Text,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
          targetNode: {
            id: 'block-node',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
          t: expect.any(Function),
        });

        const dndEngine = mockFlowUtils.createDndEngine.mock.results[0].value;
        expect(dndEngine.execute).toHaveBeenCalledWith('dropStepFromToolbarToBlock');
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should handle drop to panel for step nodes', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);

      mockFlowUtils.isDragItemOutsideFlowEditor.mockReturnValue(false);
      // Mock generateReactFlowNode to return different objects for different calls
      mockFlowUtils.generateReactFlowNode
        .mockReturnValueOnce({
          id: 'test-node',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        }) // First call for drag node
        .mockReturnValueOnce({
          id: 'block-node',
          type: ENodeType.Block,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        }); // Second call for target node
      mockFlowUtils.findTargetBlockNode.mockReturnValue(undefined); // No target node found
      mockFlowUtils.createDndEngine.mockReturnValue({ execute: vi.fn() });

      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        activeToolbarItem: { type: ENodeType.Text },
        isAllowDropToTargetBlock: false,
        isFlowEdited: { current: false },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const event = {} as any;

        handlers.toolbar.handleToolbarDragEnd(event);

        expect(mockFlowUtils.createDndEngine).toHaveBeenCalledWith({
          reactFlowInstance: mockFlowContext.reactFlowInstance,
          dragNode: {
            id: 'test-node',
            type: ENodeType.Text,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
          targetNode: {
            id: 'block-node',
            type: ENodeType.Block,
            position: { x: 0, y: 0 },
            data: { properties: {} },
          },
          t: expect.any(Function),
        });

        const dndEngine = mockFlowUtils.createDndEngine.mock.results[0].value;
        expect(dndEngine.execute).toHaveBeenCalledWith('dropStepFromToolbarToPanel');
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should execute setTimeout callback for setting selected node and opening drawer', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);

      mockFlowUtils.isDragItemOutsideFlowEditor.mockReturnValue(false);
      mockFlowUtils.generateReactFlowNode.mockReturnValue({
        id: 'test-node',
        type: ENodeType.Text,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });
      mockFlowUtils.findTargetBlockNode.mockReturnValue(undefined);
      mockFlowUtils.createDndEngine.mockReturnValue({ execute: vi.fn() });

      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        activeToolbarItem: { type: ENodeType.Text },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const event = {} as any;

        handlers.toolbar.handleToolbarDragEnd(event);

        // Advance timers to execute setTimeout callback
        act(() => {
          vi.runAllTimers();
        });

        expect(mockFlowContext.setSelectedNode).toHaveBeenCalledWith({
          id: 'test-node',
          type: ENodeType.Text,
          position: { x: 0, y: 0 },
          data: { properties: {} },
        });
        expect(mockFlowContext.setIsOpeningSettingDrawer).toHaveBeenCalledWith(true);
        expect(mockFlowContext.setOpenSettingDrawer).toHaveBeenCalledWith(true);
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });

    it('should set isFlowEdited to true', async () => {
      const flowUtils = await import('@/utils/flow');
      const mockFlowUtils = vi.mocked(flowUtils);

      mockFlowUtils.isDragItemOutsideFlowEditor.mockReturnValue(false);
      mockFlowUtils.generateReactFlowNode.mockReturnValue({
        id: 'test-node',
        type: ENodeType.Text,
        position: { x: 0, y: 0 },
        data: { properties: {} },
      });
      mockFlowUtils.findTargetBlockNode.mockReturnValue(undefined);
      mockFlowUtils.createDndEngine.mockReturnValue({ execute: vi.fn() });

      mockUseFlowContext.mockReturnValue({
        ...mockFlowContext,
        activeToolbarItem: { type: ENodeType.Text },
      });

      const TestComponentWithHandlers = () => {
        const handlers = useNodeHandlersContext();
        const event = {} as any;

        handlers.toolbar.handleToolbarDragEnd(event);

        expect(mockFlowContext.isFlowEdited.current).toBe(true);
        return <div data-testid='test-component' />;
      };

      render(
        <NodeHandlersProvider>
          <TestComponentWithHandlers />
        </NodeHandlersProvider>
      );
    });
  });
});
