import {
  BASIC_NODES,
  NODE_SKIP_DRAGGING,
  NODE_UN_CLICKABLE,
  PANEL_CONTEXT_MENU_DELAY,
  SHADOW_NODE_DATA_TYPE,
  SINGLE_NODES,
  STEP_NODES,
} from '@/constants/flow';
import { useAppContext, useFlowContext } from '@/contexts';
import {
  EDropEngine,
  ENodeType,
  EnumActionTypes,
  type FlowToolbarItems,
  type TInternalNode,
  type TNodeMouseHandler,
  type TOnNodeDrag,
  type TOnNodesChange,
  type TOnNodesDelete,
  TPendingDeleteStatus,
} from '@/types';
import {
  canDropToBlock,
  createDndEngine,
  dropSingleNodeFromToolbarToPanel,
  findEdgesByNodeIds,
  findTargetBlockNode,
  generateReactFlowNode,
  getContextMenuPosition,
  getPositionFromScreen,
  isDragItemOutsideFlowEditor,
} from '@/utils/flow';
import type { DragEndEvent, DragMoveEvent, DragStartEvent } from '@dnd-kit/core';
import { useTranslate } from '@tolgee/react';
import { applyNodeChanges } from '@xyflow/react';
import { delay, get, sum } from 'lodash';
import { createContext, useCallback, useContext, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

const useNodeHandlers = () => {
  const { t } = useTranslate('flowEditor');
  const [_, setSearchParams] = useSearchParams();
  const { settings } = useAppContext();
  const {
    flowRef,
    selectedNode,
    isFlowEdited,
    nodeActiveData,
    reactFlowInstance,
    pendingDeleteStatus,
    isOpeningSettingDrawer,
    dataPendingRemoveRef,
    activeToolbarItem,
    toolbarItemDraggingRef,
    contextMenu,
    setActiveToolbarItem,
    setDragging,
    resetFlowEditorState,
    setPanelContextMenu,
    setContextMenu,
    setSelectedNode,
    setSelectedNodes,
    setOpenSettingDrawer,
    setPendingDeleteStatus,
    setIsOpeningSettingDrawer,
  } = useFlowContext();

  const [isAllowDropToTargetBlock, setIsAllowDropToTargetBlock] = useState<boolean>(true);

  const onNodeDragStop = useCallback<TOnNodeDrag>(
    (event, node) => {
      setDragging(false);

      const isSkipDragging = NODE_SKIP_DRAGGING.includes(node.type);
      const isSingleNode = SINGLE_NODES.includes(node.type);
      if (isSkipDragging || isSingleNode || !reactFlowInstance || !nodeActiveData.current) return;
      const { getInternalNode, getNodes, screenToFlowPosition } = reactFlowInstance;
      const nodes = getNodes();

      const parentId = node.parentId;
      const isStepNode = STEP_NODES.includes(node.type);

      const ignoreNodeIds = isStepNode ? [] : [parentId || node.id];
      const position = getPositionFromScreen(screenToFlowPosition, event);
      const targetNodeId = findTargetBlockNode(nodes, position, ignoreNodeIds)?.id;

      if (node.id === targetNodeId) return;

      const dragNode = getInternalNode(node.id);
      const targetNode = getInternalNode(targetNodeId ?? '');

      if (!dragNode) return;

      const _dndEngineInstance = createDndEngine({
        reactFlowInstance,
        dragNode,
        targetNode,
        t,
      });

      if (isStepNode && !targetNode && parentId) {
        return _dndEngineInstance.execute(EDropEngine.DropStepToOutsideBlock);
      }

      if (!targetNode) return;

      const maxLength = settings?.limitations?.cardsPerNode;
      const isDropToBlock = canDropToBlock(nodes, targetNode, dragNode, maxLength);

      if (!isStepNode && targetNode && isDropToBlock) {
        return _dndEngineInstance.execute(EDropEngine.DropBlockToBlock);
      }

      if (isStepNode && parentId && targetNodeId !== parentId && isDropToBlock) {
        return _dndEngineInstance.execute(EDropEngine.DropStepToBlock);
      }

      if (isStepNode) {
        dragNode.position = nodeActiveData.current.position;
        return _dndEngineInstance.execute(EDropEngine.DropStepToOldBlock);
      }

      return _dndEngineInstance.execute(EDropEngine.ResetNodeState);
    },
    [settings, reactFlowInstance, setDragging, t]
  );

  const onNodeDrag = useCallback<TOnNodeDrag>(
    (event, node) => {
      const isSkipDragging = NODE_SKIP_DRAGGING.includes(node.type);
      const isSingleNode = SINGLE_NODES.includes(node.type);
      if (isSkipDragging || isSingleNode || !reactFlowInstance) return;
      const { getNode, setNodes, getNodes, screenToFlowPosition } = reactFlowInstance;
      const nodes = getNodes();

      const position = getPositionFromScreen(screenToFlowPosition, event);
      const targetNode = findTargetBlockNode(nodes, position, [node.parentId ?? node.id]);
      const targetNodeId = targetNode?.id;

      const dragNode = getNode(node.id);

      const maxLength = settings?.limitations?.cardsPerNode;
      const isDropToBlock = canDropToBlock(nodes, targetNode, dragNode, maxLength);

      setNodes((ns) =>
        ns.map((n) => {
          if (n.type !== ENodeType.Block) return n;
          if (n.id !== targetNodeId) {
            n.data.isStepInvalid = false;
            n.data.isAddNewStep = false;
            return n;
          }
          if (node.id === targetNodeId) return n;
          const needExpandHeight = isDropToBlock && node.parentId !== targetNodeId;

          n.data.isAddNewStep = needExpandHeight;
          n.data.isStepInvalid = !isDropToBlock;
          n.height = needExpandHeight ? sum([48, n.height]) : n.height;
          return n;
        })
      );
    },
    [settings, reactFlowInstance]
  );

  const onNodeDragStart = useCallback<TOnNodeDrag>(
    (_, node) => {
      setDragging(true);
      resetFlowEditorState();
      nodeActiveData.current = node;
      isFlowEdited.current = true;
    },
    [resetFlowEditorState, setDragging]
  );

  const onNodesDelete = useCallback<TOnNodesDelete>(
    (nodes) => {
      if (!reactFlowInstance) return;
      setPendingDeleteStatus(TPendingDeleteStatus.PENDING);
      dataPendingRemoveRef.current.nodes = nodes.map((node) => ({
        id: node.id,
        type: 'remove',
      }));
      dataPendingRemoveRef.current.edges = findEdgesByNodeIds(
        reactFlowInstance.getEdges(),
        nodes.map((node) => node.id)
      ).map((e) => ({
        id: e.id,
        type: 'remove',
      }));
    },
    [reactFlowInstance, setPendingDeleteStatus]
  );

  const onNodeClick = useCallback<TNodeMouseHandler>(
    (e, node) => {
      if (selectedNode?.id === node.id || contextMenu.open) return e.preventDefault();
      resetFlowEditorState();

      setSearchParams((prev) => {
        prev.set('node', node.id);
        return prev;
      });

      if (get(node, 'data.properties.actionType') === EnumActionTypes.end) {
        setSelectedNodes([node]);
        setSelectedNode(node);
        return;
      }

      if (NODE_UN_CLICKABLE.includes(node.type)) {
        setIsOpeningSettingDrawer(false);
        setOpenSettingDrawer(false);
        return;
      }

      delay(
        () => {
          setSelectedNodes([node]);
          setSelectedNode(node);
          setIsOpeningSettingDrawer(true);
          setOpenSettingDrawer(true);
        },
        isOpeningSettingDrawer ? 200 : 0
      );
    },
    [
      selectedNode,
      contextMenu,
      isOpeningSettingDrawer,
      setSelectedNode,
      setOpenSettingDrawer,
      resetFlowEditorState,
    ]
  );

  const onNodeContextMenu = useCallback<TNodeMouseHandler>(
    (event, node) => {
      event.preventDefault();
      setContextMenu((prev) => ({ ...prev, open: false }));
      setPanelContextMenu((prev) => ({ ...prev, open: false }));
      if (!flowRef.current || !node || BASIC_NODES.includes(node.type)) return;
      setSelectedNodes([node]);
      const panel = flowRef.current.getBoundingClientRect();
      delay(() => {
        setContextMenu({
          open: true,
          node,
          position: getContextMenuPosition(event, panel),
        });
      }, PANEL_CONTEXT_MENU_DELAY);
    },
    [flowRef, setPanelContextMenu, setContextMenu, setSelectedNodes]
  );

  const onNodesChange = useCallback<TOnNodesChange>(
    (changes) => {
      if (!reactFlowInstance) return;
      const { setNodes, getNode } = reactFlowInstance;

      if (pendingDeleteStatus === TPendingDeleteStatus.PENDING) return;

      if (pendingDeleteStatus === TPendingDeleteStatus.CONFIRMED) {
        setNodes((prev) => applyNodeChanges(changes, prev));
        setPendingDeleteStatus(TPendingDeleteStatus.FINISHED);
        return;
      }
      if (pendingDeleteStatus === TPendingDeleteStatus.IDLE) {
        const isRemoveNode = changes.every((change) => change.type === 'remove');
        if (isRemoveNode) {
          const isRemoveMenuNode = changes.every((change) => {
            const node = getNode(change.id);
            return node?.type === ENodeType.Menu;
          });
          if (!isRemoveMenuNode) return;
          return setNodes((prev) => applyNodeChanges(changes, prev));
        }
      }
      setNodes((prev) => applyNodeChanges(changes, prev));
    },
    [reactFlowInstance, pendingDeleteStatus, setPendingDeleteStatus]
  );

  const handleToolbarDragStart = useCallback(
    (event: DragStartEvent) => {
      setDragging(true);
      resetFlowEditorState();
      setActiveToolbarItem(event.active.data.current as FlowToolbarItems);
    },
    [setDragging, setActiveToolbarItem, resetFlowEditorState]
  );

  const handleToolbarDragMove = useCallback(
    (_: DragMoveEvent) => {
      if (!reactFlowInstance || !toolbarItemDraggingRef.current || !activeToolbarItem) return;
      if (SINGLE_NODES.includes(activeToolbarItem.type)) return;
      const { setNodes, getNodes, screenToFlowPosition } = reactFlowInstance;
      const nodes = getNodes();

      const { x, y } = toolbarItemDraggingRef.current.getBoundingClientRect();
      const position = getPositionFromScreen(screenToFlowPosition, {
        clientX: x,
        clientY: y,
      } as any);

      const targetNode = findTargetBlockNode(nodes, position);

      let isDropToBlock = false;

      if (targetNode) {
        const dragNode = {
          ...SHADOW_NODE_DATA_TYPE,
          type: activeToolbarItem.type,
        };

        const maxLength = settings?.limitations?.cardsPerNode;
        isDropToBlock = canDropToBlock(nodes, targetNode, dragNode, maxLength);

        setIsAllowDropToTargetBlock(isDropToBlock);
      }

      setNodes((ns) =>
        ns.map((n) => {
          if (n.type !== ENodeType.Block) return n;
          return {
            ...n,
            data: {
              ...n.data,
              isAddNewStep: targetNode && n.id === targetNode.id && isDropToBlock,
              isStepInvalid: targetNode && n.id === targetNode.id && !isDropToBlock,
            },
          };
        })
      );
    },
    [reactFlowInstance, activeToolbarItem, setIsAllowDropToTargetBlock, settings]
  );

  const handleToolbarDragEnd = useCallback(
    (_: DragEndEvent) => {
      setDragging(false);
      if (!reactFlowInstance || !toolbarItemDraggingRef.current || !activeToolbarItem) return;
      isFlowEdited.current = true;
      const { x, y } = toolbarItemDraggingRef.current.getBoundingClientRect();
      const { screenToFlowPosition, getNodes } = reactFlowInstance;
      const position = getPositionFromScreen(screenToFlowPosition, {
        clientX: x,
        clientY: y,
      } as MouseEvent);

      if (isDragItemOutsideFlowEditor({ x, y })) return setActiveToolbarItem(null);

      const activeItemType = activeToolbarItem.type;
      const dragNode = generateReactFlowNode(activeItemType, t(`defaultLabel.${activeItemType}`));

      if (SINGLE_NODES.includes(activeItemType)) {
        dragNode.position = position;
        dropSingleNodeFromToolbarToPanel({
          reactFlowInstance,
          dragNode: dragNode as TInternalNode,
        });
      }

      const isStepNode = STEP_NODES.includes(activeItemType);
      if (isStepNode) {
        let targetNode = findTargetBlockNode(getNodes(), position);
        const isDropToBlock = targetNode && isAllowDropToTargetBlock;

        if (!isDropToBlock) {
          targetNode = generateReactFlowNode(ENodeType.Block, t(`defaultLabel.${ENodeType.Block}`));
          targetNode.position = position;
        }

        const _dndEngineInstance = createDndEngine({
          reactFlowInstance,
          dragNode: dragNode as TInternalNode,
          targetNode: targetNode as TInternalNode,
          t,
        });

        if (isDropToBlock) {
          _dndEngineInstance.execute(EDropEngine.DropStepFromToolbarToBlock);
        } else {
          _dndEngineInstance.execute(EDropEngine.DropStepFromToolbarToPanel);
        }
      }

      setActiveToolbarItem(null);
      setTimeout(() => {
        setSelectedNode(dragNode);
        setIsOpeningSettingDrawer(true);
        setOpenSettingDrawer(true);
      }, 0);
    },
    [
      activeToolbarItem,
      isAllowDropToTargetBlock,
      reactFlowInstance,
      setDragging,
      setSelectedNode,
      setIsOpeningSettingDrawer,
      setOpenSettingDrawer,
    ]
  );

  return {
    editor: {
      onNodeDrag,
      onNodeDragStop,
      onNodeDragStart,
      onNodesDelete,
      onNodeClick,
      onNodeContextMenu,
      onNodesChange,
    },
    toolbar: {
      handleToolbarDragStart,
      handleToolbarDragMove,
      handleToolbarDragEnd,
    },
  };
};

type ContextType = ReturnType<typeof useNodeHandlers>;

const context = createContext<ContextType | null>(null);

export const NodeHandlersProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useNodeHandlers();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useNodeHandlersContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useNodeHandlersContext must be used inside NodeHandlersProvider');
  }

  return value;
};
