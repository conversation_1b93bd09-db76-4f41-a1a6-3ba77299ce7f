import { COLOR_PALETTE_KEYS, FADE_TOP_IN, FLOW_EDITOR_ANIMATION_DURATION } from '@/constants/flow';
import { useFlowContext } from '@/contexts';
import { type ENodeType, TPendingDeleteStatus, type TReactFlowNodeProps } from '@/types';
import { findEdgesByNodeIds, getAllChildrenNodesFromNodeIds, onCopyInPanel } from '@/utils/flow';
import { ActionIcon, Box, Transition, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCircleFilled, IconCopy, IconTextRecognition, IconTrash } from '@tabler/icons-react';
import { NodeToolbar } from '@xyflow/react';
import get from 'lodash/get';
import { useCallback, useEffect, useMemo, useState } from 'react';
import NodeMenuColorToolbar from './NodeMenuColorToolbar';

const useStyles = createStyles((theme) => ({
  root: {
    display: 'flex',
    background: theme.colors.decaMono[1],
    borderRadius: theme.radius.lg,
    boxShadow: `0px 2px 8px 0px ${theme.colors.decaGrey[0]}`,
    padding: `${rem(4)} ${rem(16)}`,
    height: rem(40),
    alignItems: 'center',
    gap: rem(10),
  },
  iconBtn: {
    color: theme.black,
    background: 'transparent',
    cursor: 'pointer',
    '&:hover': {
      color: theme.black,
      background: 'transparent',
    },
  },
  divider: {
    width: rem(2),
    height: rem(24),
    background: theme.black,
    opacity: 0.3,
    borderRadius: rem(1),
  },
}));

const NodeMenuToolbar = (props: TReactFlowNodeProps<ENodeType.Block>) => {
  const { id, data } = props;
  const { classes } = useStyles();
  const [showColorPicker, setShowColorPicker] = useState(false);
  const {
    reactFlowInstance,
    dataPendingRemoveRef,
    contextMenu,
    dataCopiedRef,
    setEditingTitleNodeID,
    setPendingDeleteStatus,
    resetFlowEditorState,
  } = useFlowContext();

  const rename = useCallback(() => {
    setEditingTitleNodeID(id);
    resetFlowEditorState();
  }, [id, setEditingTitleNodeID, resetFlowEditorState]);

  const handleOnNodeDelete = useCallback(() => {
    if (!contextMenu.node || !reactFlowInstance) return;
    const { getNodes, getEdges } = reactFlowInstance;
    const nodesDeleting = getAllChildrenNodesFromNodeIds(getNodes(), [contextMenu.node]);

    dataPendingRemoveRef.current.nodes = nodesDeleting.map((node) => ({
      id: node.id,
      type: 'remove',
    }));

    dataPendingRemoveRef.current.edges = findEdgesByNodeIds(
      getEdges() || [],
      nodesDeleting.map((node) => node.id)
    ).map((e) => ({
      id: e.id,
      type: 'remove',
    }));

    setPendingDeleteStatus(TPendingDeleteStatus.PENDING);
  }, [contextMenu.node, reactFlowInstance]);

  const handleOnNodeCopy = useCallback(() => {
    if (!contextMenu.node) return;
    onCopyInPanel([contextMenu.node], dataCopiedRef);
  }, [contextMenu.node]);

  const isVisible = useMemo(
    () => contextMenu.open && contextMenu.node?.id === id,
    [contextMenu.open, contextMenu.node, id]
  );

  useEffect(() => {
    setShowColorPicker(false);
  }, [contextMenu.open]);

  return (
    <>
      <NodeMenuColorToolbar {...props} showColorPicker={showColorPicker} />
      <NodeToolbar isVisible>
        <Transition
          mounted={isVisible}
          transition={FADE_TOP_IN}
          duration={FLOW_EDITOR_ANIMATION_DURATION}
          timingFunction='ease'
        >
          {(styles) => (
            <Box style={styles} className={classes.root}>
              <ActionIcon
                variant='subtle'
                size={24}
                className={classes.iconBtn}
                aria-label='Color'
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setShowColorPicker((prev) => !prev);
                }}
              >
                <IconCircleFilled
                  size={24}
                  color={get(data, 'properties.setting.color', COLOR_PALETTE_KEYS[0]) as string}
                />
              </ActionIcon>
              <Box className={classes.divider} />
              <ActionIcon
                variant='subtle'
                size={24}
                className={classes.iconBtn}
                aria-label='Rename'
                onClick={rename}
              >
                <IconTextRecognition size={24} stroke={1.5} />
              </ActionIcon>
              <ActionIcon
                variant='subtle'
                size={24}
                className={classes.iconBtn}
                aria-label='Copy'
                onClick={handleOnNodeCopy}
              >
                <IconCopy size={24} stroke={1.5} />
              </ActionIcon>
              <ActionIcon
                variant='subtle'
                size={24}
                className={classes.iconBtn}
                aria-label='Delete'
                onClick={handleOnNodeDelete}
              >
                <IconTrash size={24} stroke={1.5} />
              </ActionIcon>
            </Box>
          )}
        </Transition>
      </NodeToolbar>
    </>
  );
};

export default NodeMenuToolbar;
