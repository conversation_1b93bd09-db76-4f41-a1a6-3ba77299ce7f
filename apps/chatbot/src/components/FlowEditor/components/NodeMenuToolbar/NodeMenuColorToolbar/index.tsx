import { COLOR_PALETTE_KEYS } from '@/constants/flow';
import { useFlowContext } from '@/contexts';
import type { ENodeType, TReactFlowNodeProps } from '@/types';
import { NodeToolbar } from '@xyflow/react';
import get from 'lodash/get';
import set from 'lodash/set';
import { useCallback } from 'react';
import MenuColor from '../../MenuColor';

type NodeMenuColorToolbarProps = TReactFlowNodeProps<ENodeType.Block> & {
  showColorPicker: boolean;
};

const NodeMenuColorToolbar = (props: NodeMenuColorToolbarProps) => {
  const { data, showColorPicker, id } = props;
  const { reactFlowInstance } = useFlowContext();

  const handleChangeColor = useCallback(
    (color: string) => {
      set(data, 'properties.setting.color', color);
      reactFlowInstance?.updateNodeData(id, data);
    },
    [data, id, reactFlowInstance]
  );

  return (
    <NodeToolbar isVisible offset={60}>
      <MenuColor
        showColorPicker={showColorPicker}
        activeColor={get(data, 'properties.setting.color', COLOR_PALETTE_KEYS[0]) as string}
        onColorChange={handleChangeColor}
      />
    </NodeToolbar>
  );
};

export default NodeMenuColorToolbar;
