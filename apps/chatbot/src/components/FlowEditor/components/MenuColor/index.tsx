import { COLOR_PALETTE_KEYS, FADE_TOP_IN, FLOW_EDITOR_ANIMATION_DURATION } from '@/constants/flow';
import { ActionIcon, Box, Transition, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCircleFilled } from '@tabler/icons-react';

const useStyles = createStyles((theme) => ({
  root: {
    display: 'flex',
    background: theme.colors.decaMono[1],
    borderRadius: theme.radius.lg,
    boxShadow: `0px 2px 8px 0px ${theme.colors.decaGrey[0]}`,
    padding: `${rem(4)} ${rem(16)}`,
    height: rem(40),
    alignItems: 'center',
    gap: rem(2),
  },
  iconBtn: {
    color: theme.black,
    background: 'transparent',
    borderRadius: '100%',
    cursor: 'pointer',
    '&:hover': {
      color: theme.black,
      background: 'transparent',
    },
  },
  iconBtnActive: {
    border: `1px solid ${theme.colors.decaNavy[5]}`,
  },
}));

type MenuColorProps = {
  showColorPicker: boolean;
  activeColor: string;
  onColorChange: (color: string) => void;
};

const MenuColor = (props: MenuColorProps) => {
  const { showColorPicker, activeColor, onColorChange } = props;
  const { classes, cx } = useStyles();

  return (
    <Transition
      mounted={showColorPicker}
      transition={FADE_TOP_IN}
      duration={FLOW_EDITOR_ANIMATION_DURATION}
      timingFunction='ease'
    >
      {(styles) => (
        <Box style={styles} className={classes.root}>
          {COLOR_PALETTE_KEYS.map((color) => (
            <ActionIcon
              key={color}
              variant='subtle'
              size={24}
              className={cx(classes.iconBtn, {
                [classes.iconBtnActive]: activeColor === color,
              })}
              aria-label='Color'
              onClick={() => onColorChange(color)}
            >
              <IconCircleFilled size={24} color={color} />
            </ActionIcon>
          ))}
        </Box>
      )}
    </Transition>
  );
};

export default MenuColor;
