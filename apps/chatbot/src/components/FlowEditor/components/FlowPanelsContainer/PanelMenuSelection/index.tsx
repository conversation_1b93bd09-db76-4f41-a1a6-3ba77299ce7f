import { FADE_TOP_IN, FLOW_EDITOR_ANIMATION_DURATION } from '@/constants/flow';
import { useFlowContext } from '@/contexts';
import { TPendingDeleteStatus } from '@/types';
import { findEdgesByNodeIds, getAllChildrenNodesFromNodeIds, onCopy } from '@/utils/flow';
import { Transition } from '@mantine/core';
import { ActionIcon, Box, Flex, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCopy, IconTrash } from '@tabler/icons-react';
import { useCallback } from 'react';

const useStyles = createStyles((theme) => ({
  root: {
    background: theme.colors.decaGrey[6],
    borderRadius: theme.radius.lg,
    boxShadow: `0px 2px 8px 0px ${theme.colors.decaGrey[0]}`,
    padding: `${rem(8)} ${rem(24)}`,
    height: rem(48),
    display: 'flex',
    alignItems: 'center',
    gap: rem(16),
  },
  iconBtn: {
    color: theme.white,
    background: 'transparent',
    '&:hover': {
      color: theme.white,
      background: theme.colors.decaGrey[6],
    },
  },
  divider: {
    width: rem(2),
    height: rem(24),
    background: theme.white,
    opacity: 0.3,
    borderRadius: rem(1),
  },
}));

const PanelMenuSelection = () => {
  const {
    setPendingDeleteStatus,
    selectedNodes,
    reactFlowInstance,
    dataPendingRemoveRef,
    dataCopiedRef,
    openPanelMenuSelection,
  } = useFlowContext();
  const { classes } = useStyles();

  const onDelete = useCallback(() => {
    if (!reactFlowInstance) return;
    const nodesDeleting = getAllChildrenNodesFromNodeIds(
      reactFlowInstance.getNodes(),
      selectedNodes
    );
    if (!nodesDeleting) return;

    dataPendingRemoveRef.current.nodes = nodesDeleting.map((node) => ({
      id: node.id,
      type: 'remove',
    }));

    const edgesDeleting = findEdgesByNodeIds(
      reactFlowInstance.getEdges(),
      nodesDeleting.map((node) => node.id)
    );

    dataPendingRemoveRef.current.edges = edgesDeleting.map((e) => ({
      id: e.id,
      type: 'remove',
    }));

    setPendingDeleteStatus(TPendingDeleteStatus.PENDING);
  }, [selectedNodes, reactFlowInstance]);

  const handleOnCopy = useCallback(() => {
    if (!reactFlowInstance) return;
    onCopy(reactFlowInstance, dataCopiedRef);
  }, [reactFlowInstance]);

  return (
    <Transition
      mounted={openPanelMenuSelection}
      transition={FADE_TOP_IN}
      duration={FLOW_EDITOR_ANIMATION_DURATION}
      timingFunction={'ease'}
    >
      {(styles) => (
        <Box style={styles} className={classes.root}>
          <Flex align='center' gap={16}>
            <ActionIcon
              variant='subtle'
              size={24}
              className={classes.iconBtn}
              aria-label='Copy'
              onClick={handleOnCopy}
            >
              <IconCopy size={24} stroke={1.5} />
            </ActionIcon>
            <Box className={classes.divider} />
            <ActionIcon
              variant='subtle'
              size={24}
              className={classes.iconBtn}
              aria-label='Delete'
              onClick={onDelete}
            >
              <IconTrash size={24} stroke={1.5} />
            </ActionIcon>
          </Flex>
        </Box>
      )}
    </Transition>
  );
};

export default PanelMenuSelection;
