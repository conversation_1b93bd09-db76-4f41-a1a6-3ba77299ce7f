import { HEADER_HEIGHT } from '@/constants/common';
import { FLOW_OFFSET_LEFT, FLOW_OFFSET_TOP } from '@/constants/flow';
import { Flex, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { Background, Panel } from '@xyflow/react';
import FlowPanel from './FlowSidebar';
import FlowTopBar from './FlowTopBar';
import PanelFlowAlert from './PanelFlowAlert';
import FlowCount from './PanelFlowCount';
import FlowId from './PanelFlowId';
import PanelMenuSelection from './PanelMenuSelection';
import NodeSearch from './PanelNodeSearch';
import PanelSidebarNodeData from './PanelSidebarNodeData';
import PanelToolbar from './PanelToolbar';

export const useStyles = createStyles((theme) => ({
  sidebarNodeData: {
    margin: 0,
    height: '100%',
  },
  bg: {
    backgroundColor: theme.colors.silverFox[2],
  },
  flowPanel: {
    zIndex: 100,
    margin: 0,
    height: `calc(100vh - ${HEADER_HEIGHT})`,
    width: rem(FLOW_OFFSET_LEFT),
  },
  topBar: {
    zIndex: 99,
    width: '100%',
    margin: 0,
  },
}));

export const FlowPanelsContainer = () => {
  const { classes } = useStyles();

  return (
    <>
      <Background gap={16} className={classes.bg} />

      <Panel position='top-left' className={classes.flowPanel}>
        <FlowPanel />
      </Panel>
      <Panel position='top-left'>
        <PanelToolbar />
      </Panel>
      <Panel position='top-right' className={classes.sidebarNodeData}>
        <PanelSidebarNodeData />
      </Panel>
      <Panel position='top-right'>
        <PanelFlowAlert />
      </Panel>
      <Panel position='top-left' className={classes.topBar}>
        <FlowTopBar />
      </Panel>
      <Panel
        style={{
          left: '50%',
          transform: `translate(0, ${FLOW_OFFSET_TOP}px)`,
        }}
      >
        <PanelMenuSelection />
      </Panel>
      <Panel position='top-center'>
        <NodeSearch />
      </Panel>
      <Panel position='bottom-left'>
        <Flex gap={rem(10)} align={'center'} ml={rem(FLOW_OFFSET_LEFT)}>
          <FlowCount />
          <FlowId />
        </Flex>
      </Panel>
    </>
  );
};
