import {
  FADE_TOP_IN,
  FLOW_EDITOR_ANIMATION_DURATION,
  FLOW_EDITOR_VIEWPORT_DURATION,
  FLOW_OFFSET_LEFT,
  FLOW_OFFSET_TOP,
  SEARCHABLE_NODES,
} from '@/constants/flow';
import { flowToolbarConfiguration } from '@/constants/toolbar';
import { useFlowContext } from '@/contexts/FlowContext';
import type { ENodeType, TReactFlowNode } from '@/types';
import { Box, Flex, type OptionsFilter, Transition, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaSelect } from '@resola-ai/ui';
import { IconSearch } from '@tabler/icons-react';
import { useNodes } from '@xyflow/react';
import get from 'lodash/get';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useNodeHandlersContext } from '../../../contexts/useNodeHandlers';
import SearchOption from './SeachOption';

const useStyles = createStyles((theme) => ({
  root: {
    marginLeft: rem(FLOW_OFFSET_LEFT),
    marginTop: rem(FLOW_OFFSET_TOP),
  },
  input: {
    border: `1px solid ${theme.colors.decaLight[3]}`,
    height: rem(47),
    width: rem(480),
    borderRadius: rem(12),
    padding: `${rem(12)} ${rem(16)}`,
    paddingLeft: rem(40),
    fontSize: rem(16),
    boxShadow: `0px 2px 8px 0px ${theme.colors.decaLight[3]}`,

    '&:focus': {
      borderColor: theme.colors.decaBlue[5],
    },
  },

  dropdown: {
    border: `1px solid ${theme.colors.decaLight[3]}`,
    boxShadow: `0px 2px 8px 0px ${theme.colors.decaLight[3]}`,
    borderRadius: rem(12),
  },

  option: {
    padding: `${rem(4)} ${rem(8)}`,
    fontSize: rem(14),
    color: theme.colors.decaGrey[5],
    borderRadius: rem(8),
    margin: rem(4),

    '&:hover': {
      backgroundColor: theme.colors.decaLight[1],
    },

    '&[data-combobox-selected]': {
      backgroundColor: theme.colors.decaLight[1],
    },
  },
}));

const getTextDataFromObject = (obj: object): string[] => {
  return Object.values(obj).flatMap((value) => {
    if (typeof value === 'string') return [value];
    if (typeof value === 'object' && value !== null) return getTextDataFromObject(value);
    return [];
  });
};

type SearchNodeData = {
  id: string;
  parentId: string;
  name: string;
  textData: string[];
};

export default function NodeSearch() {
  const { classes } = useStyles();
  const nodeIconMap = useRef<Map<string, JSX.Element>>(new Map());
  const nodes = useNodes<TReactFlowNode>();
  const {
    editor: { onNodeClick },
  } = useNodeHandlersContext();

  const { isNodeSearchOpened, toggleIsNodeSearchOpened, reactFlowInstance, setNodeStartId } =
    useFlowContext();

  const ref = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isNodeSearchOpened) {
      ref.current?.focus();
    }
  }, [isNodeSearchOpened]);

  const handleBlur = useCallback(() => {
    setTimeout(() => {
      toggleIsNodeSearchOpened(false);
    }, 100);
  }, [toggleIsNodeSearchOpened]);

  const nodeDataMap = useMemo(() => {
    if (!nodes.length) return new Map();
    const map = new Map<string, SearchNodeData>();
    nodes.map((node) => {
      const nodeData = node.data;
      const textData = getTextDataFromObject(nodeData);
      nodeIconMap.current.set(
        node.id,
        flowToolbarConfiguration
          .flatMap((item) => item.items)
          .find((item) => item?.type === node.type)?.icon ?? <Box w={20} h={20} />
      );
      if (SEARCHABLE_NODES.includes(node.type as ENodeType)) {
        map.set(node.id, {
          id: node.id,
          parentId: node.parentId ?? '',
          name: get(node, 'data.properties.name', node.type),
          textData,
        });
      }
    });
    return map;
  }, [nodes]);

  const defaultData = useMemo(() => {
    return Array.from(nodeDataMap.values(), (node: SearchNodeData) => ({
      label: node.parentId ? `${nodeDataMap.get(node.parentId)?.name}` : `${node.name}`,
      value: node.id,
    }));
  }, [nodeDataMap]);

  const handleSelectNode = useCallback(
    async (value: string | null) => {
      if (!value || !reactFlowInstance) return;
      toggleIsNodeSearchOpened(false);
      const node = reactFlowInstance.getNode(value);
      if (!node) return;
      setNodeStartId(node.id);
      setTimeout(() => {
        onNodeClick({} as any, node);
      }, FLOW_EDITOR_VIEWPORT_DURATION);
    },
    [onNodeClick, reactFlowInstance, toggleIsNodeSearchOpened, setNodeStartId]
  );

  const optionsFilter: OptionsFilter = ({ options, search }) => {
    const formattedSearch = search.toLowerCase().trim();
    return options.filter((option) => {
      if ('label' in option) {
        const label = option.label.toLowerCase().trim();
        return label.includes(formattedSearch);
      }
      return false;
    });
  };

  return (
    <Transition
      mounted={isNodeSearchOpened}
      transition={FADE_TOP_IN}
      duration={FLOW_EDITOR_ANIMATION_DURATION}
      timingFunction={'ease'}
    >
      {(styles) => (
        <DecaSelect
          style={styles}
          ref={ref}
          searchable
          filter={optionsFilter}
          onChange={handleSelectNode}
          leftSection={
            <Flex align='center' justify='center' ml={rem(12)}>
              <IconSearch size={20} />
            </Flex>
          }
          rightSection={null}
          data={defaultData}
          limit={5}
          renderOption={({ option }) => (
            <SearchOption option={option} nodeIconMap={nodeIconMap.current} />
          )}
          classNames={{
            root: classes.root,
            input: classes.input,
            dropdown: classes.dropdown,
            option: classes.option,
          }}
          comboboxProps={{ transitionProps: { transition: 'pop', duration: 200 } }}
          placeholder='Search...'
          onBlur={handleBlur}
        />
      )}
    </Transition>
  );
}
