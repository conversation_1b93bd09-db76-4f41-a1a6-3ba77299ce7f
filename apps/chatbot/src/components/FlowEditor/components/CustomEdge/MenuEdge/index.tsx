import { COLOR_PALETTE_KEYS } from '@/constants/flow';
import { BaseEdge, type EdgeProps, getSmoothStepPath } from '@xyflow/react';
import { memo } from 'react';

const MenuEdge = ({
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
}: EdgeProps) => {
  const [edgePath] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  return (
    <BaseEdge
      path={edgePath}
      style={{ ...style, stroke: COLOR_PALETTE_KEYS[0], strokeWidth: 2 }}
      interactionWidth={10}
    />
  );
};

export default memo(MenuEdge);
