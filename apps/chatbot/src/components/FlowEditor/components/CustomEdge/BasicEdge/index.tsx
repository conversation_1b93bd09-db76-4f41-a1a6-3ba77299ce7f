import type { TEdge } from '@/types';
import { BaseEdge, type EdgeProps, getSmoothStepPath } from '@xyflow/react';
import { memo, useEffect, useState } from 'react';
import EdgeLabelColorPicker from '../../EdgeLabelRender/EdgeLabelColorPicker';
import EdgeLabelToolbar from '../../EdgeLabelRender/EdgeLabelToolbar';

const BasicEdge = (props: EdgeProps<TEdge>) => {
  const {
    sourceX,
    sourceY,
    targetX,
    targetY,
    sourcePosition,
    targetPosition,
    selected,
    style = {},
    markerEnd,
  } = props;
  const [showColorPicker, setShowColorPicker] = useState(false);

  const [edgePath, labelX] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
    // centerX: selected ? (sourceX + targetX) / 2 - 100 : undefined, // TODO: for drag edge
    // centerY: selected ? (sourceY + sourceY) / 2 - 100 : undefined,
  });

  useEffect(() => {
    setShowColorPicker(false);
  }, [selected]);

  return (
    <>
      <BaseEdge path={edgePath} markerEnd={markerEnd} style={{ ...style }} interactionWidth={40} />
      <EdgeLabelToolbar {...props} labelX={labelX} setShowColorPicker={setShowColorPicker} />
      <EdgeLabelColorPicker {...props} labelX={labelX} showColorPicker={showColorPicker} />
    </>
  );
};

export default memo(BasicEdge);
