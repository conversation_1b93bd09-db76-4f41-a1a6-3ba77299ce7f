import { BaseEdge, type EdgeProps, getStraightPath } from '@xyflow/react';
import { memo } from 'react';

const ActionEdge = ({ sourceX, sourceY, targetX, targetY, style, markerEnd }: EdgeProps) => {
  const [edgePath] = getStraightPath({
    sourceX,
    sourceY,
    targetX,
    targetY,
  });

  return (
    <BaseEdge path={edgePath} markerEnd={markerEnd} style={{ ...style }} interactionWidth={10} />
  );
};

export default memo(ActionEdge);
