import {
  COLOR_PALETTE_KEYS,
  COLOR_PALETTE_SET,
  OFFSET_EDGE_LABEL_COLOR_PICKER,
  Z_INDEX_EDGE_LABEL,
} from '@/constants/flow';
import { useFlowContext } from '@/contexts';
import { getMenuPosition } from '@/utils/flow/edge';
import { Box } from '@mantine/core';
import { EdgeLabelRenderer, type EdgeProps, MarkerType } from '@xyflow/react';
import get from 'lodash/get';
import set from 'lodash/set';
import { useCallback, useMemo } from 'react';
import MenuColor from '../../MenuColor';

type EdgeLabelColorPickerProps = EdgeProps & {
  labelX: number;
  showColorPicker: boolean;
};

export default function EdgeLabelColorPicker({
  id,
  data,
  sourceX,
  sourceY,
  targetX,
  targetY,
  labelX,
  showColorPicker,
}: EdgeLabelColorPickerProps) {
  const { reactFlowInstance, isFlowEdited } = useFlowContext();

  const handleChangeColor = useCallback(
    (color: string) => {
      if (!reactFlowInstance) return;
      const { setEdges } = reactFlowInstance;
      setEdges((edges) =>
        edges.map((el) => {
          if (el.id === id) {
            const colorSelected = get(COLOR_PALETTE_SET[color], 'lineSelected');
            el.markerEnd = {
              type: MarkerType.ArrowClosed,
              color: colorSelected,
            };
            set(el, 'style.stroke', colorSelected);
            set(el, 'data.setting.color', color);
            return { ...el };
          }
          return el;
        })
      );
      isFlowEdited.current = true;
    },
    [reactFlowInstance, id]
  );

  const menuPosition = useMemo(
    () =>
      getMenuPosition(labelX, sourceX, sourceY, targetX, targetY, OFFSET_EDGE_LABEL_COLOR_PICKER),
    [labelX, sourceX, sourceY, targetX, targetY]
  );

  return (
    <EdgeLabelRenderer>
      <Box
        style={{
          position: 'absolute',
          zIndex: Z_INDEX_EDGE_LABEL,
          pointerEvents: 'all',
          transform: `translate(-50%, -50%) translate(${menuPosition.x}px,${menuPosition.y}px)`,
        }}
      >
        <MenuColor
          showColorPicker={showColorPicker}
          activeColor={get(data, 'setting.color', COLOR_PALETTE_KEYS[0]) as string}
          onColorChange={handleChangeColor}
        />
      </Box>
    </EdgeLabelRenderer>
  );
}
