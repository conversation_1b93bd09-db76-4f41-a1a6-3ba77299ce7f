import {
  COLOR_PALETTE_KEYS,
  FADE_TOP_IN,
  FLOW_EDITOR_ANIMATION_DURATION,
  OFFSET_EDGE_LABEL_TOOLBAR,
  Z_INDEX_EDGE_LABEL,
} from '@/constants/flow';
import { useFlowContext } from '@/contexts';
import { getMenuPosition } from '@/utils/flow/edge';
import { ActionIcon, Box, Transition, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCircleFilled, IconTrash } from '@tabler/icons-react';
import { EdgeLabelRenderer, type EdgeProps } from '@xyflow/react';
import get from 'lodash/get';
import {
  type Dispatch,
  type MouseEventHandler,
  type SetStateAction,
  useCallback,
  useMemo,
} from 'react';

const useStyles = createStyles((theme) => ({
  root: {
    display: 'flex',
    background: theme.colors.decaMono[1],
    borderRadius: theme.radius.lg,
    boxShadow: `0px 2px 8px 0px ${theme.colors.decaGrey[0]}`,
    padding: `${rem(4)} ${rem(16)}`,
    height: rem(40),
    alignItems: 'center',
    gap: rem(16),
  },
  iconBtn: {
    color: theme.black,
    background: 'transparent',
    cursor: 'pointer',
    '&:hover': {
      color: theme.black,
      background: 'transparent',
    },
  },
  divider: {
    width: rem(2),
    height: rem(24),
    background: theme.black,
    opacity: 0.3,
    borderRadius: rem(1),
  },
}));

type EdgeLabelToolbarProps = EdgeProps & {
  labelX: number;
  setShowColorPicker: Dispatch<SetStateAction<boolean>>;
};

export default function EdgeLabelToolbar({
  id,
  data,
  selected,
  sourceX,
  sourceY,
  targetX,
  targetY,
  labelX,
  setShowColorPicker,
}: EdgeLabelToolbarProps) {
  const { classes } = useStyles();
  const { reactFlowInstance, isFlowEdited } = useFlowContext();

  const handleDeleteEdge = useCallback<MouseEventHandler<HTMLButtonElement>>(
    (e) => {
      e.preventDefault();
      e.stopPropagation();
      if (!reactFlowInstance) return;
      const { setEdges } = reactFlowInstance;
      setEdges((edges) => edges.filter((el) => el.id !== id));
      isFlowEdited.current = true;
    },
    [reactFlowInstance, id]
  );

  const menuPosition = useMemo(
    () => getMenuPosition(labelX, sourceX, sourceY, targetX, targetY, OFFSET_EDGE_LABEL_TOOLBAR),
    [sourceX, targetX, sourceY, targetY, labelX]
  );

  return (
    <EdgeLabelRenderer>
      <Box
        style={{
          position: 'absolute',
          zIndex: Z_INDEX_EDGE_LABEL,
          pointerEvents: 'all',
          transform: `translate(-50%, -50%) translate(${menuPosition.x}px,${menuPosition.y}px)`,
        }}
      >
        <Transition
          mounted={!!selected}
          transition={FADE_TOP_IN}
          duration={FLOW_EDITOR_ANIMATION_DURATION}
          timingFunction={'ease'}
        >
          {(styles) => (
            <Box style={styles} className={classes.root}>
              <ActionIcon
                variant='subtle'
                size={24}
                className={classes.iconBtn}
                aria-label='Color'
                onClick={() => setShowColorPicker((prev) => !prev)}
              >
                <IconCircleFilled
                  size={24}
                  color={get(data, 'setting.color', COLOR_PALETTE_KEYS[0]) as string}
                />
              </ActionIcon>
              <Box className={classes.divider} />
              <ActionIcon
                variant='subtle'
                size={24}
                className={classes.iconBtn}
                aria-label='Delete'
                onClick={handleDeleteEdge}
              >
                <IconTrash size={24} stroke={1.5} />
              </ActionIcon>
            </Box>
          )}
        </Transition>
      </Box>
    </EdgeLabelRenderer>
  );
}
