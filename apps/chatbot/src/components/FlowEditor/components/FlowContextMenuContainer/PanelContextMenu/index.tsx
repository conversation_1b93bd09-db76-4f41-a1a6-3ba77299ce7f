import { FADE_TOP_IN, FLOW_EDITOR_ANIMATION_DURATION } from '@/constants/flow';
import { useFlowContext } from '@/contexts/FlowContext';
import { useContextMenuStyles } from '@/styles/flow-editor/useContextMenuStyles';
import { onPaste } from '@/utils/flow';
import { Box, Divider, Flex, Transition, rem } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import { Fragment, useMemo } from 'react';

enum Action {
  PASTE = 'paste',
}

type MenuItem = {
  name: string;
  enabled: boolean;
  action: Action;
};

const MenuItems: MenuItem[] = [
  {
    name: 'paste',
    enabled: true,
    action: Action.PASTE,
  },
];

const PanelContextMenu = () => {
  const { t } = useTranslate('flowEditor');
  const { classes, cx } = useContextMenuStyles();
  const { reactFlowInstance, dataCopiedRef, mouseEventRef, panelContextMenu } = useFlowContext();

  const handleClick = (item: MenuItem) => {
    if (!reactFlowInstance) return;
    switch (item.action) {
      case Action.PASTE:
        onPaste(reactFlowInstance, dataCopiedRef, mouseEventRef);
        break;
      default:
        break;
    }
  };

  const menuItems = useMemo(
    () =>
      MenuItems.map((item) => {
        switch (item.action) {
          case Action.PASTE:
            return {
              ...item,
              enabled: !!dataCopiedRef.current.nodes.length,
            };
          default:
            return item;
        }
      }),
    [dataCopiedRef.current.nodes.length]
  );

  return (
    <Transition
      mounted={panelContextMenu.open}
      transition={FADE_TOP_IN}
      duration={FLOW_EDITOR_ANIMATION_DURATION}
      timingFunction={'ease'}
    >
      {(styles) => (
        <Box style={{ ...styles, ...panelContextMenu.position }} className={classes.contextMenu}>
          {menuItems.map((item, index) => (
            <Fragment key={item.name}>
              <Flex
                className={cx(classes.item, {
                  [classes.disabled]: !item.enabled,
                })}
                p={rem(8)}
                gap={rem(8)}
                onClick={() => handleClick(item)}
              >
                {t(item.name)}
              </Flex>
              {menuItems.length !== index + 1 && <Divider />}
            </Fragment>
          ))}
        </Box>
      )}
    </Transition>
  );
};

export default PanelContextMenu;
