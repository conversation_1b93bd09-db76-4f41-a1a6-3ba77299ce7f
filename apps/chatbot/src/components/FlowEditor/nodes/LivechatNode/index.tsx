import { TooltipText } from '@/components';
import {
  BUILT_IN_PORT_ELSE,
  BUILT_IN_PORT_FAILURE,
  BUILT_IN_PORT_FINISH,
  COLOR_PALETTE_KEYS,
  COLOR_PALETTE_SET,
} from '@/constants/flow';
import { useFlowContext } from '@/contexts/FlowContext';
import { withStepNode } from '@/hocs';
import { useBlockNodeStyles } from '@/styles/useBlockNodeStyles';
import type { ENodeType, TReactFlowNodeProps } from '@/types';
import { Box, Divider, Flex, Input, Text, rem } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import { memo, useCallback, useEffect, useRef, useState } from 'react';
import FlowEditorIcon from '../../components/FlowEditorIcon';
import SourceEdge from '../../components/SourceEdge';
import TargetEdge from '../../components/TargetEdge';

function LivechatNode({
  id,
  type,
  data: {
    properties: { name, setting },
  },
  className,
}: TReactFlowNodeProps<ENodeType.Livechat>) {
  const { classes, cx } = useBlockNodeStyles({
    isSelected: Boolean(className),
    colorConfig: COLOR_PALETTE_SET[setting?.color ?? COLOR_PALETTE_KEYS[0]],
  });
  const { t } = useTranslate('nodeSetting');
  const { reactFlowInstance, editingTitleNodeID, setEditingTitleNodeID, isFlowEdited } =
    useFlowContext();
  const [title, setTitle] = useState(name);
  const lastTitle = useRef('');

  const onChange = useCallback((value) => {
    setTitle(value);
  }, []);

  const onBlur = useCallback(() => {
    if (!title) setTitle(lastTitle.current);
    isFlowEdited.current = true;
    setEditingTitleNodeID('');
    if (!reactFlowInstance) return;
    const node = reactFlowInstance.getNode(id);
    if (node) {
      node.data.properties.name = title.trim();
      reactFlowInstance.updateNodeData(id, node.data);
    }
  }, [title, setEditingTitleNodeID, id]);

  useEffect(() => {
    if (editingTitleNodeID !== id) lastTitle.current = title;
  }, [editingTitleNodeID, id, title]);

  return (
    <Box className={cx(classes.container, className)}>
      <Box className={classes.handle}>
        <TargetEdge />
      </Box>
      {editingTitleNodeID === id ? (
        <Input
          className={classes.input}
          value={title}
          onChange={(e) => onChange(e.target.value)}
          onBlur={onBlur}
          onKeyUp={(e) => e.key === 'Enter' && onBlur()}
          autoFocus
        />
      ) : (
        <TooltipText
          text={title}
          onClick={() => setEditingTitleNodeID(id)}
          classNames={classes.title}
        />
      )}
      <Box className={classes.content}>
        <Box w={'100%'}>
          <Flex gap={rem(12)} align={'center'} pos='relative' p={rem(8)}>
            <FlowEditorIcon icon='IconHeadset' />
            <Text>{t('liveChatNode.successfulConnected')}</Text>
          </Flex>
          <Divider />
          <Flex pos='relative' py={rem(8)}>
            <Text ml={rem(28)}>{t('liveChatNode.failedToConnect')}</Text>
            <SourceEdge type={type} source={id} sourceHandle={BUILT_IN_PORT_FAILURE} />
          </Flex>
          <Divider />
          <Flex pos='relative' py={rem(8)}>
            <Text ml={rem(28)}>{t('liveChatNode.finishingLivechat')}</Text>
            <SourceEdge type={type} source={id} sourceHandle={BUILT_IN_PORT_FINISH} />
          </Flex>
          <Divider />
          <Flex pos='relative' py={rem(8)}>
            <Text ml={rem(28)}>{t('liveChatNode.exceedingQueue')}</Text>
            <SourceEdge type={type} source={id} sourceHandle={BUILT_IN_PORT_ELSE} />
          </Flex>
        </Box>
      </Box>
    </Box>
  );
}

export default memo(withStepNode(LivechatNode));
