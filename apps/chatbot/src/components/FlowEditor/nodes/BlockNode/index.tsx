import { TooltipText } from '@/components';
import { COLOR_PALETTE_KEYS, COLOR_PALETTE_SET, TITLE_HEIGHT } from '@/constants/flow';
import { useFlowContext } from '@/contexts/FlowContext';
import withStepNode from '@/hocs/withStepNode';
import { useBlockNodeStyles } from '@/styles/useBlockNodeStyles';
import type { ENodeType, TReactFlowNodeProps } from '@/types';
import { Box, Flex, Input, rem } from '@mantine/core';
import { IconSettings } from '@tabler/icons-react';
import { useNodes } from '@xyflow/react';
import get from 'lodash/get';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import StepPlaceHolder from '../../components/StepPlaceHolder';
import TargetEdge from '../../components/TargetEdge';

const BlockNode = ({
  id,
  className,
  data: {
    properties: { name, setting },
    isAddNewStep,
    isStepInvalid,
  },
}: TReactFlowNodeProps<ENodeType.Block>) => {
  const { classes, cx } = useBlockNodeStyles({
    isSelected: Boolean(className),
    isStepInvalid,
    colorConfig: COLOR_PALETTE_SET[setting?.color ?? COLOR_PALETTE_KEYS[0]],
  });
  const nodes = useNodes();
  const {
    reactFlowInstance,
    editingTitleNodeID,
    setEditingTitleNodeID,
    isFlowEdited,
    selectedNode,
    dragging,
  } = useFlowContext();
  const [title, setTitle] = useState(name);
  const [isHover, setIsHover] = useState(false);
  const lastTitle = useRef('');

  const childrenNodes = useMemo(() => {
    return nodes.filter((node) => node.parentId === id);
  }, [nodes, id]);

  const onChange = useCallback((value) => {
    setTitle(value);
  }, []);

  const onBlur = useCallback(() => {
    if (!title) setTitle(lastTitle.current);
    isFlowEdited.current = true;
    setEditingTitleNodeID('');
    if (!reactFlowInstance) return;
    const node = reactFlowInstance.getNode(id);
    if (node) {
      node.data.properties.name = title.trim();
      reactFlowInstance.updateNodeData(id, node.data);
    }
  }, [id, title, setEditingTitleNodeID, reactFlowInstance]);

  const generateChildren = useCallback(() => {
    return childrenNodes.map((node) => <StepPlaceHolder key={node.id} node={node} />);
  }, [childrenNodes, id]);

  const isRichMenuEnabled =
    !!get(setting, 'line.richMenu.enabled') || !!get(setting, 'line.richMenu.forceDisable');

  const isRichMenuIconActive = isHover || isRichMenuEnabled || selectedNode?.id === id;

  useEffect(() => {
    if (editingTitleNodeID !== id) {
      lastTitle.current = title;
    }
  }, [editingTitleNodeID, id, title]);

  return (
    <Box
      className={cx(classes.container, className)}
      onMouseEnter={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
    >
      <Box className={classes.handle}>
        <TargetEdge />
      </Box>
      <Flex justify='space-between' align='center' gap={rem(8)} h={rem(TITLE_HEIGHT)}>
        {editingTitleNodeID === id ? (
          <Input
            className={classes.input}
            value={title}
            onChange={(e) => onChange(e.target.value)}
            onBlur={onBlur}
            onKeyUp={(e) => e.key === 'Enter' && onBlur()}
            autoFocus
          />
        ) : (
          <TooltipText
            text={title}
            onClick={() => setEditingTitleNodeID(id)}
            classNames={classes.title}
          />
        )}
        <Box w={rem(24)} h={rem(24)}>
          <IconSettings
            className={cx(classes.richMenuIcon, {
              [classes.richMenuIconActive]: isRichMenuIconActive,
              [classes.richMenuEnabled]: isRichMenuEnabled,
            })}
          />
        </Box>
      </Flex>
      {generateChildren()}
      {isAddNewStep && dragging && <StepPlaceHolder />}
    </Box>
  );
};

export default memo(withStepNode(BlockNode));
