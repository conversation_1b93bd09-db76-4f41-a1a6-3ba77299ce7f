import ButtonList from '@/components/FlowEditor/components/ButtonList';
import { FADE_RIGHT_IN } from '@/constants/flow';
import { useFlowContext } from '@/contexts';
import { dynamicStepStructureSchema } from '@/schemas';
import { tolgee } from '@/services/tolgee';
import type { ENodeType, TSidebarNodeSetting } from '@/types';
import { flowValidator } from '@/utils/flow';
import { zodResolver } from '@hookform/resolvers/zod';
import { Box, Divider, Flex, Text, Tooltip, Transition, rem } from '@mantine/core';
import { BlockNoteMarkdown } from '@resola-ai/blocknote-editor';
import { DecaSelectRHF } from '@resola-ai/ui';
import { IconDatabase, IconInfoCircle } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import get from 'lodash/get';
import { useCallback, useEffect, useMemo } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { TextInput } from 'react-hook-form-mantine';
import { useDynamicNodeSettingsStyles } from './useDynamicNodeSettingsStyles';

const DynamicNodeSettings: React.FC<TSidebarNodeSetting<ENodeType.Dynamic>> = ({
  node,
  submit,
}) => {
  const { classes } = useDynamicNodeSettingsStyles();
  const { t } = useTranslate('nodeSetting');
  const { variables, openSettingDrawer, mapFlowErrorsRef } = useFlowContext();

  const methods = useForm({
    mode: 'onBlur',
    defaultValues: node.data.properties,
    resolver: zodResolver(dynamicStepStructureSchema),
  });

  const {
    getValues,
    control,
    watch,
    setValue,
    trigger,
    reset,
    formState: { errors },
  } = methods;

  const watchInputVariable = watch('inputVariable');

  const onSubmit = useCallback(() => {
    /* Use trigger to trigger validation.
        Don't use handleSubmit because it will allowed submit even if there have errors
    */
    trigger();
    const values = getValues();

    const newData = {
      ...values,
      text: values.text?.replace(/\n$/, ''),
    };

    flowValidator(dynamicStepStructureSchema, mapFlowErrorsRef, newData, node.id);

    submit(newData);
  }, [getValues, submit, node.id]);

  const onBlockNoteChange = useCallback(
    (markdown: string) => {
      setValue('text', markdown);
    },
    [setValue]
  );

  useEffect(() => {
    // Add delay to wait for new setting to be mounted, and avoid update old setting
    setTimeout(() => {
      reset({
        ...node.data.properties,
        inputVariable: node.data.properties.inputVariable || null,
        outputVariable: node.data.properties.outputVariable || null,
      });
      if (mapFlowErrorsRef.current.get(node.id)?.length) {
        trigger();
      } else {
        onSubmit();
      }
    }, 0);
  }, [node.id]);

  useEffect(
    () => () => {
      onSubmit();
    },
    []
  );

  const initialText = useMemo(() => get(node, 'data.properties.text', ' '), [node]);

  return (
    <Transition
      mounted={openSettingDrawer}
      transition={FADE_RIGHT_IN}
      duration={300}
      timingFunction={'ease'}
    >
      {(styles) => (
        <Box style={styles}>
          <FormProvider {...methods}>
            <Box className={classes.container}>
              <Flex align='center'>
                <Text className={classes.title}>{t('dynamicNode.title')}</Text>
              </Flex>
              <Divider />
              <Flex direction='column'>
                <Flex direction='column' gap={rem(4)}>
                  <Text fz={rem(14)} fw={500}>
                    {t('dynamicNode.text')}
                  </Text>
                  <Text fz={rem(12)} color='dimmed'>
                    {`"${t('dynamicNode.textDescription')}"`}
                  </Text>
                  <BlockNoteMarkdown
                    className={classes.editor}
                    initialMarkdown={initialText}
                    language={tolgee.getLanguage()}
                    onChange={onBlockNoteChange}
                    onBlur={onSubmit}
                  />
                </Flex>
              </Flex>
              <Divider />
              <DecaSelectRHF
                searchable
                className={classes.fieldControl}
                label={t('dynamicNode.variable')}
                placeholder={t('dynamicNode.variablePlaceholder')}
                control={control}
                error={!!errors.inputVariable}
                name={'inputVariable'}
                data={variables.map(({ name }) => name)}
                leftSection={<IconDatabase size={18} />}
                onChange={onSubmit}
              />
              {watchInputVariable && (
                <Flex direction='column' gap={rem(12)} className={classes.groupAttribute}>
                  <TextInput
                    className={classes.fieldControl}
                    label={
                      <Flex align='center' gap={rem(4)}>
                        {t('dynamicNode.attributeToBeSetAsTitle')}
                        <Tooltip label={t('dynamicNode.attributeToBeSetAsTitle')}>
                          <IconInfoCircle size={16} />
                        </Tooltip>
                      </Flex>
                    }
                    control={control}
                    name={'labelAttribute'}
                    onBlur={onSubmit}
                  />
                  <TextInput
                    className={classes.fieldControl}
                    label={
                      <Flex align='center' gap={rem(4)}>
                        {t('dynamicNode.attributeToBeSetAsVariable')}
                        <Tooltip label={t('dynamicNode.attributeToBeSetAsVariable')}>
                          <IconInfoCircle size={16} />
                        </Tooltip>
                      </Flex>
                    }
                    control={control}
                    name={'valueAttribute'}
                    onBlur={onSubmit}
                  />
                </Flex>
              )}
              <Divider />
              <DecaSelectRHF
                searchable
                className={classes.fieldControl}
                label={t('dynamicNode.variableStored')}
                placeholder={t('dynamicNode.variablePlaceholder')}
                control={control}
                error={!!errors.outputVariable}
                name={'outputVariable'}
                data={variables.map(({ name }) => name)}
                leftSection={<IconDatabase size={18} />}
                onChange={onSubmit}
              />
              <Divider />
              <Box>
                <ButtonList node={node} onSubmit={onSubmit} canDeleteLastButton />
              </Box>
            </Box>
          </FormProvider>
        </Box>
      )}
    </Transition>
  );
};

export default DynamicNodeSettings;
