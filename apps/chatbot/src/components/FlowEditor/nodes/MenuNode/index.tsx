import { Box, Divider, Flex, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { Handle, Position, addEdge } from '@xyflow/react';
import { Fragment, memo, useCallback, useMemo } from 'react';

import { DefaultActionOptions } from '@/constants/flow';
import { useFlowContext } from '@/contexts/FlowContext';
import { ENodeType, EdgeTypes, type EnumActionTypes, type TReactFlowNodeProps } from '@/types';
import {
  findEdgesByTarget,
  generateActionNodeByType,
  generateReactFlowEdge,
  getAvailableActionOptions,
  getNextActionNodePosition,
} from '@/utils/flow';
import { randomId } from '@/utils/id';
import { ActionTypeIcons } from '../../components/ActionSelector';

const useStyles = createStyles((theme) => ({
  container: {
    borderRadius: rem(4),
    border: `1px solid ${theme.colors.silverFox[5]}`,
    color: theme.colors.silverFox[9],
    background: theme.colors.silverFox[0],
    fontSize: rem(12),
    height: 'inherit',
  },
  content: {
    display: 'flex',
    gap: rem(8),
    justifyContent: 'space-between',
    alignContent: 'center',
  },
  targetEdge: {
    position: 'relative',
    top: `${rem(0)} !important`,
    left: `${rem(-2)} !important`,
    opacity: 0,
  },
  item: {
    '&:hover': {
      backgroundColor: theme.colors.silverFox[2],
      cursor: 'pointer',
    },
  },
}));

const MenuNode = ({ id: menuNodeId }: TReactFlowNodeProps<ENodeType.Menu>) => {
  const { t } = useTranslate('nodeSetting');
  const { classes } = useStyles();
  const { reactFlowInstance } = useFlowContext();

  const handleSelect = useCallback(
    (actionType: EnumActionTypes) => {
      if (!reactFlowInstance) return;
      const { getInternalNode, setNodes, setEdges, getEdges, screenToFlowPosition } =
        reactFlowInstance;
      const edges = getEdges();
      const { source, sourceHandle } = findEdgesByTarget(edges, menuNodeId)[0] || {};
      if (!source || !sourceHandle) return;
      const sourceInternalNode = getInternalNode(source || '');

      if (!sourceInternalNode) return;
      const isChainingAction = sourceInternalNode.type === ENodeType.Action;

      const position = getNextActionNodePosition(
        screenToFlowPosition,
        sourceHandle,
        sourceInternalNode
      );

      const newActionNodeId = randomId();
      const parentId = isChainingAction ? sourceInternalNode.parentId || '' : sourceInternalNode.id;
      const portId = isChainingAction ? sourceInternalNode.data.properties.portId : sourceHandle;

      setNodes((nodes) => {
        const newActionNode = generateActionNodeByType({
          type: actionType,
          parentId,
          position,
          portId,
        });
        newActionNode.id = newActionNodeId;
        return nodes.concat(newActionNode).filter((node) => node.type !== ENodeType.Menu);
      });

      setEdges((edges) => {
        const newEdges = addEdge(
          generateReactFlowEdge(source, newActionNodeId, sourceHandle, EdgeTypes.Action),
          edges
        );
        return newEdges.filter((edge) => edge.type !== EdgeTypes.Menu);
      });
    },
    [reactFlowInstance]
  );

  const customOptions = useMemo(() => {
    if (!reactFlowInstance) return [];
    const { getEdges, getNode, getNodes } = reactFlowInstance;
    const { source } = findEdgesByTarget(getEdges(), menuNodeId)[0] || {};
    const sourceNode = getNode(source);

    if (!sourceNode) return [];
    if (sourceNode.type !== ENodeType.Action) return DefaultActionOptions;
    return getAvailableActionOptions(getNodes(), sourceNode);
  }, [reactFlowInstance]);

  return (
    <div className={classes.container}>
      <Box pos='absolute' top={0} left={0}>
        <Handle
          type='target'
          position={Position.Left}
          isConnectableStart={false}
          className={classes.targetEdge}
        />
      </Box>
      {customOptions.map((action, index) => (
        <Fragment key={action}>
          <Flex
            className={classes.item}
            p={rem(8)}
            gap={rem(8)}
            onClick={() => handleSelect(action)}
          >
            {ActionTypeIcons[action]}
            {t(`actionTypes.${action}`)}
          </Flex>
          {DefaultActionOptions.length !== index + 1 && <Divider />}
        </Fragment>
      ))}
    </div>
  );
};

export default memo(MenuNode);
