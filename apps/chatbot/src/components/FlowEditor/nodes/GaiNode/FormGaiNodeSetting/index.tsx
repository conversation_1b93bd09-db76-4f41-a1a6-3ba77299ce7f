import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/components';
import { AppConfig } from '@/configs';
import { FLOW_EDITOR_ANIMATION_DURATION } from '@/constants/flow';
import { useFlowContext } from '@/contexts';
import {
  ADVANCED_SETTINGS,
  GaiThresholdAdvancedSettings,
  OutputType,
  type TReactFlowNode,
} from '@/types';
import { Divider, Flex, Stack, Text, Transition, rem } from '@mantine/core';
import { useToggle } from '@mantine/hooks';
import { DecaSelectRHF } from '@resola-ai/ui';
import { Colors } from '@resola-ai/ui/constants';
import { IconChevronDown, IconDatabase } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import debounce from 'lodash/debounce';
import isEmpty from 'lodash/isEmpty';
import { useCallback, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import { JsonInput, Radio, Switch, Textarea } from 'react-hook-form-mantine';
import { useFormGaiNodeSettingStyles } from './useFormGaiNodeSettingStyles';

const { ENABLE_STREAMING_SUPPORT } = AppConfig;

const formatNumber = (num: number) => num.toLocaleString();
const PROMPT_LENGTH = 10000;

const FormGaiNodeSetting = ({ onSubmit, node }: { onSubmit: () => void; node: TReactFlowNode }) => {
  const { t } = useTranslate('nodeSetting');
  const { classes } = useFormGaiNodeSettingStyles();
  const { variables, onPortToggleElse } = useFlowContext();
  const [showAdvancedSettings, toggleShowAdvancedSettings] = useToggle();
  const {
    control,
    getValues,
    setValue,
    watch,
    formState: { errors },
  } = useFormContext();

  const outputType = watch('outputType');
  const prompt = watch('prompt');

  const onToggleFallback = useCallback(() => {
    const values = getValues();
    if (values.fallbackPath) return onSubmit();
    onPortToggleElse(node, onSubmit);
  }, [getValues, node, onSubmit, onPortToggleElse]);

  const onChangeOutputType = useCallback(
    (value: string) => {
      const outputExample = getValues('outputExample');
      if (value === OutputType.json && isEmpty(outputExample)) {
        setValue('outputExample', '{}');
      }
      onSubmit();
    },
    [setValue, onSubmit, getValues]
  );

  const debounceSubmit = debounce(() => {
    onSubmit();
  }, 300);

  const remainedPromptCharacters = useMemo(() => {
    return PROMPT_LENGTH - (prompt ? prompt.length : 0);
  }, [prompt]);

  return (
    <>
      <Flex direction='column'>
        <Text className={classes.label}>{t('genAi.prompt')}</Text>
        <Text className={classes.warning}>
          {remainedPromptCharacters < 0
            ? t('genAi.promptError', { max: formatNumber(PROMPT_LENGTH) })
            : t('genAi.validate', {
                max: formatNumber(PROMPT_LENGTH),
                remained: formatNumber(remainedPromptCharacters),
              })}
        </Text>
        <Textarea
          className={classes.fieldControl}
          control={control}
          name='prompt'
          placeholder={t('genAi.promptPlaceholder')}
          autosize
          error={!!errors.prompt}
          minRows={5}
          maxRows={10}
          onBlur={onSubmit}
        />
      </Flex>

      <Divider />
      <Radio.Group
        name='outputType'
        control={control}
        label={t('genAi.outputType')}
        onChange={onChangeOutputType}
      >
        <Stack gap={rem(28)} mt={rem(24)} mb={rem(12)}>
          <Radio.Item value={OutputType.text} label={t('genAi.text')} />
          <Radio.Item value={OutputType.json} label={t('genAi.json')} />
        </Stack>
      </Radio.Group>
      {outputType === OutputType.json && (
        <JsonInput
          className={classes.fieldControl}
          control={control}
          name='outputExample'
          label={t('genAi.json')}
          placeholder={t('genAi.jsonPlaceholder')}
          autosize
          minRows={5}
          maxRows={10}
          onBlur={() => setTimeout(onSubmit)} //Save after formatOnBlur
          formatOnBlur
        />
      )}
      <Divider />
      <DecaSelectRHF
        searchable
        className={classes.fieldControl}
        label={t('genAi.variable')}
        placeholder={t('genAi.variablePlaceholder')}
        control={control}
        error={!!errors.variableName}
        name={'variableName'}
        data={variables.map(({ name }) => name)}
        leftSection={<IconDatabase size={18} />}
        onChange={onSubmit}
      />
      <Divider />
      {ENABLE_STREAMING_SUPPORT && (
        <Flex justify={'space-between'} align={'center'} gap={rem(20)}>
          <Text fz={rem(14)} fw={500}>
            {t('genAi.streamingSupport')}
          </Text>
          <Switch name='stream' control={control} onChange={onSubmit} />
        </Flex>
      )}
      <Flex justify={'space-between'} align={'center'} gap={rem(20)}>
        <Text fz={rem(14)} fw={500}>
          {t('genAi.fallbackPath')}
        </Text>
        <Switch name='fallbackPath' control={control} onChange={onToggleFallback} />
      </Flex>
      <Flex
        mt='xs'
        align={'center'}
        gap={rem(4)}
        sx={{ cursor: 'pointer', userSelect: 'none', color: Colors.decaBlue[5] }}
        onClick={() => toggleShowAdvancedSettings()}
      >
        <Text fz={rem(14)} fw={500}>
          {t('genAi.showAdvancedSetting')}
        </Text>
        <Flex
          align={'center'}
          justify={'center'}
          sx={{
            transform: showAdvancedSettings ? 'rotate(180deg)' : 'rotate(0deg)',
            transition: 'transform 0.3s ease',
          }}
        >
          <IconChevronDown size={16} />
        </Flex>
      </Flex>
      <Transition
        mounted={showAdvancedSettings}
        transition='scale-y'
        duration={FLOW_EDITOR_ANIMATION_DURATION}
        timingFunction='ease'
      >
        {(styles) => (
          <Stack gap={rem(16)} mb={rem(12)} style={styles}>
            {ADVANCED_SETTINGS.map((setting) => (
              <SliderField
                key={setting.name}
                title={t(setting.titleKey)}
                sx={{ width: '100%' }}
                step={GaiThresholdAdvancedSettings[setting.name].step}
                min={GaiThresholdAdvancedSettings[setting.name].min}
                max={GaiThresholdAdvancedSettings[setting.name].max}
                defaultValue={GaiThresholdAdvancedSettings[setting.name].default}
                showLabelOnHover
                control={control}
                name={`advancedSettings.${setting.name}`}
                marks={setting.marks}
                onChange={debounceSubmit}
              />
            ))}
          </Stack>
        )}
      </Transition>
    </>
  );
};

export default FormGaiNodeSetting;
