import { useRefHeight } from '@/hooks';
import { zodResolver } from '@hookform/resolvers/zod';
import { Box, Divider, ScrollArea, Text, Transition } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import isEmpty from 'lodash/isEmpty';
import type React from 'react';
import { useCallback, useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import { FADE_RIGHT_IN } from '@/constants/flow';
import { useFlowContext } from '@/contexts';
import { apiSchema } from '@/schemas';
import { useAPINodeStyles } from '@/styles/useAPINodeStyles';
import {
  CONSTANT_API_BODY_TYPES,
  type ENodeType,
  EnumAPIStatusOptions,
  EnumApiStepBody,
  type TApiStepData,
  type TSidebarNodeSetting,
} from '@/types';
import { normalizeApiStepData } from '@/utils/flow';
import { <PERSON><PERSON><PERSON><PERSON>ield, JSO<PERSON>ield, PlainTextField } from '../APIFields';
import {
  APIContentType,
  APIHeaders,
  APIMappings,
  APIParameters,
  APIRequest,
  APISuccessStatus,
} from '../APISections';

const OFFSET_SCROLL_AREA_HEIGHT = 76;

const APINodeSettings: React.FC<TSidebarNodeSetting<ENodeType.Api>> = ({ node, submit }) => {
  const { t } = useTranslate('nodeSetting');
  const { classes } = useAPINodeStyles();
  const { openSettingDrawer } = useFlowContext();
  const [ref, height] = useRefHeight<HTMLDivElement>(OFFSET_SCROLL_AREA_HEIGHT);

  const action = useForm<TApiStepData>({
    defaultValues: node.data.properties,
    mode: 'onBlur',
    resolver: zodResolver(apiSchema),
  });

  const { control, getValues, setValue, reset, watch } = action;

  /**
   * Handle form submit to update Step data
   */
  const handleSubmit = useCallback(
    (isSettingClose = false) => {
      const values = getValues();
      const { bodyType, content, status, expectedStatus, useDecaDefaultToken } = values;

      const normalizeData = isSettingClose ? normalizeApiStepData(values) : values;

      const updatedData = {
        ...normalizeData,
        body:
          bodyType === EnumApiStepBody.text || bodyType === EnumApiStepBody.json
            ? []
            : normalizeData.body,
        content:
          bodyType === EnumApiStepBody.text || bodyType === EnumApiStepBody.json ? content : '{}',
        expectedStatus: status === EnumAPIStatusOptions.custom ? expectedStatus?.map(Number) : [],
        useDecaDefaultToken: useDecaDefaultToken || false,
      };

      submit(updatedData);
    },
    [getValues, submit]
  );

  /**
   * Render body content based on body type
   * @params void
   * @returns JSX.Element
   */
  const renderBodyContent = useCallback(
    (bodyType: string) => {
      const currentBodyType = CONSTANT_API_BODY_TYPES[bodyType];
      const defaultValue =
        getValues(currentBodyType.mappingField as any) || currentBodyType.defaultValue;

      switch (currentBodyType.value) {
        case EnumApiStepBody.formData:
        case EnumApiStepBody.formUrlencoded: {
          return (
            <BodyFormField
              value={defaultValue}
              label={currentBodyType.label}
              control={control}
              fieldName='body'
              setValue={setValue}
              handleSubmit={handleSubmit}
            />
          );
        }
        case EnumApiStepBody.json: {
          return (
            <JSONField
              value={defaultValue}
              label={currentBodyType.label}
              control={control}
              fieldName='content'
              setValue={setValue}
              handleSubmit={handleSubmit}
            />
          );
        }
        case EnumApiStepBody.text: {
          return (
            <PlainTextField
              value={defaultValue}
              label={currentBodyType.label}
              control={control}
              fieldName='content'
              setValue={setValue}
              handleSubmit={handleSubmit}
            />
          );
        }

        default:
          return null;
      }
    },
    [setValue, handleSubmit, getValues, control]
  );

  /**
   * Reset form data when step data changed
   */
  useEffect(() => {
    // Add delay to wait for new setting to be mounted, and avoid update old setting
    setTimeout(() => {
      reset({
        ...node.data.properties,
        status: isEmpty(node.data.properties.expectedStatus as number[])
          ? EnumAPIStatusOptions.default
          : EnumAPIStatusOptions.custom,
      });
    }, 0);
  }, [node.id]);

  useEffect(
    () => () => {
      handleSubmit();
    },
    []
  );

  return (
    <Transition
      mounted={openSettingDrawer}
      transition={FADE_RIGHT_IN}
      duration={300}
      timingFunction={'ease'}
    >
      {(styles) => (
        <Box style={styles} ref={ref} component='section' className={classes.root}>
          <FormProvider {...action}>
            <header className={classes.header}>
              <Text className={classes.title}>{t('apiNode.title')}</Text>
            </header>
            <Box component='section' className={classes.content}>
              <ScrollArea h={height} className={classes.scrollWrapper}>
                <Box className={classes.formSection}>
                  <APIRequest handleSubmit={handleSubmit} />
                </Box>

                <Divider className={classes.divider} />
                <APIHeaders
                  control={control}
                  fieldName='headers'
                  setValue={setValue}
                  handleSubmit={handleSubmit}
                />

                <Divider className={classes.divider} />
                <APIParameters
                  control={control}
                  fieldName='params'
                  setValue={setValue}
                  handleSubmit={handleSubmit}
                />

                <Divider className={classes.divider} />
                <APIMappings
                  control={control}
                  fieldName='mappings'
                  setValue={setValue}
                  handleSubmit={handleSubmit}
                />

                <Divider className={classes.divider} />
                <Box className={classes.formSection}>
                  <Box className={classes.formHeading}>
                    <Text className={classes.formTitle}>{t('apiNode.contentType')}</Text>
                  </Box>
                  <Box className={classes.settingRow}>
                    <APIContentType />
                  </Box>
                </Box>

                {watch('bodyType') !== EnumApiStepBody.none ? (
                  <>
                    <Divider className={classes.divider} />
                    {renderBodyContent(watch('bodyType'))}
                  </>
                ) : null}

                <Divider className={classes.divider} />
                <Box className={classes.formSection}>
                  <Box className={classes.formHeading}>
                    <Text className={classes.formTitle}>{t('apiNode.successStatusCode')}</Text>
                  </Box>
                  <Box className={classes.settingRow}>
                    <APISuccessStatus currentStatus={watch('status')} />
                  </Box>
                </Box>
              </ScrollArea>
            </Box>
          </FormProvider>
        </Box>
      )}
    </Transition>
  );
};

export default APINodeSettings;
