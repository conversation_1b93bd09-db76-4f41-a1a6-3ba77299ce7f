import {
  ACTION_NODE_HEIGHT,
  BASIC_NODES,
  BUILT_IN_PORT_NEXT,
  COLOR_PALETTE_KEYS,
  COLOR_PALETTE_SET,
} from '@/constants/flow';
import { useFlowContext } from '@/contexts/FlowContext';
import {
  type ENodeType,
  EnumActionTypes,
  type TActionData,
  type TColorPaletteSetConfig,
  type TFlow,
  type TReactFlowNodeProps,
} from '@/types';
import { getNodeInfoFromFlows } from '@/utils/flow';
import { Flex, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { type TFnType, useTranslate } from '@tolgee/react';
import { Handle, Position } from '@xyflow/react';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import map from 'lodash/map';
import { useMemo } from 'react';
import { memo } from 'react';
import { NodeMenuToolbar } from '../../components';
import { ActionTypeIcons } from '../../components/ActionSelector';
import SourceEdge from '../../components/SourceEdge';

const useStyles = createStyles(
  (theme, { colorConfig }: { colorConfig: TColorPaletteSetConfig }) => ({
    button: {
      border: `solid ${rem(1)} ${colorConfig.color}`,
      backgroundColor: colorConfig.backgroundColor,
      color: colorConfig.lineSelected,
      padding: rem(8),
      borderRadius: rem(8),
      fontSize: rem(12),
      lineHeight: rem(12),
      height: ACTION_NODE_HEIGHT,
      fontWeight: 500,
      cursor: 'pointer',
      transition: 'all 0.2s ease-in-out',
      '.mantine-Button-icon': {
        marginRight: rem(8),
        svg: {
          strokeWidth: rem(1.5),
        },
      },
      '&:hover': {
        boxShadow: `0 0 ${rem(4)} ${rem(2)} ${theme.colors.decaLight[3]} !important`,
      },
    },
    selected: {
      borderColor: colorConfig.lineSelected,
      color: colorConfig.lineSelected,
    },
    targetEdge: {
      opacity: 0,
      left: `${rem(2)} !important`,
    },
    actionIcon: {
      minWidth: rem(16),
    },
    actionValue: {
      textOverflow: 'ellipsis',
      overflow: 'hidden',
      lineHeight: rem(16),
      whiteSpace: 'nowrap',
    },
  })
);

const getActionValue = (
  actionData: TActionData,
  flows: TFlow[],
  t: TFnType,
  activeFlow: TFlow | null
) => {
  switch (actionData?.actionType) {
    case EnumActionTypes.url:
      return actionData?.url || '';

    case EnumActionTypes.variable: {
      const variableValues =
        map(actionData?.variables, ({ variable, value }) => `{{${variable}}} = ${value}`) ?? [];
      return variableValues.join(', ') || '';
    }

    case EnumActionTypes.node: {
      const { flow: flowId, target: nodeId } = actionData;
      if (!flowId || !nodeId) return '';

      const { flow, node } = getNodeInfoFromFlows(flows, flowId, nodeId, activeFlow);
      const nodeType = get(node, 'type', '');
      let nodeName = get(node, 'data.name', '') || nodeType;

      // Handle translation for basic nodes (start, finish, end)
      if (nodeType && BASIC_NODES.includes(nodeType)) {
        nodeName = t(nodeType, { ns: 'flowEditor' });
      }

      const nodeValue =
        flow?.name && nodeName
          ? `${t(flow.name.toLowerCase(), { ns: 'flowEditor' })}: ${nodeName}`
          : '';

      return nodeValue ? (
        <Text tt='capitalize' fz={rem(12)} fw={500}>
          {nodeValue}
        </Text>
      ) : (
        ''
      );
    }

    default:
      return '';
  }
};

const ActionNode = (props: TReactFlowNodeProps<ENodeType.Action>) => {
  const {
    id,
    type,
    data: { canHandle, properties },
    className,
  } = props;
  const { t } = useTranslate('nodeSetting');
  const { selectedNode, flows, activeFlow, selectedNodes } = useFlowContext();
  const { classes, cx } = useStyles({
    colorConfig: COLOR_PALETTE_SET[properties?.setting?.color ?? COLOR_PALETTE_KEYS[0]],
  });

  const actionValue = useMemo(() => {
    if (isEmpty(activeFlow)) return '';
    return getActionValue(properties, flows, t, activeFlow);
  }, [properties, flows, activeFlow]);

  return (
    <>
      <NodeMenuToolbar {...props} />
      <Flex
        align='center'
        gap={rem(6)}
        className={cx(
          className,
          classes.button,
          (selectedNode?.id === id || selectedNodes.some((node) => node.id === id)) &&
            classes.selected
        )}
      >
        <Handle
          type='target'
          position={Position.Left}
          isConnectableStart={false}
          className={classes.targetEdge}
        />
        <span className={classes.actionIcon}>{ActionTypeIcons[properties?.actionType]}</span>
        <span className={classes.actionValue}>
          {actionValue ? actionValue : t(`actionTypes.${properties?.actionType}`)}
        </span>
        {canHandle && <SourceEdge type={type} source={id} sourceHandle={BUILT_IN_PORT_NEXT} />}
      </Flex>
    </>
  );
};

export default memo(ActionNode);
