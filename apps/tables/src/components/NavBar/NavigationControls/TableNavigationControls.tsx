import { type CreateFormSubmitCallback, CreateModal, notifications } from '@/components/Common';
import TableImportModal from '@/components/TableImport';
import { ANIMATED_TRASH_IMAGE_URL } from '@/constants';
import { useAppContext } from '@/contexts/AppContext';
import { useTablesMutation, useTablesQuery } from '@/hooks';
import type { Table } from '@/types';
import { getTablePath } from '@/utils';
import { Divider, ScrollArea, Skeleton, Stack, rem, useMantineTheme } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { CustomImage } from '@resola-ai/ui';
import {
  IconChevronDown,
  IconChevronRight,
  IconChevronsRight,
  IconFileTypeCsv,
  IconTable,
} from '@tabler/icons-react';
import { IconChevronsLeft } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { type MouseEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import NavbarWrapper from './NavbarWrapper';
import { ACTIVE_ITEM_SELECTOR, NavigationItem, NavigationItemTitle } from './NavigationItem';
import NavigationMenuIcon, {
  type MenuActionCallback,
  type NavigationMenuPosition,
} from './NavigationMenuIcon';

const useStyles = createStyles((_) => ({
  trashIcon: {
    width: rem(20),
    height: rem(20),
  },
}));

const NUMBER_OF_SKELETON_ITEMS = 8;

interface TableNavigationItemProps {
  table: Table;
  index: number;
  onMenuChange?: MenuActionCallback;
  onActiveChange?: (active: boolean) => void;
}

const TableNavigationItem = (props: TableNavigationItemProps) => {
  const { table, index, onMenuChange, onActiveChange } = props;
  const [menuOpened, { open: openMenu, close: closeMenu }] = useDisclosure(false);
  const [position, setPosition] = useState<NavigationMenuPosition | undefined>(undefined);
  const { pendingClearTableIds } = useAppContext();
  const { classes } = useStyles();

  const isClearingData = useMemo(() => {
    return pendingClearTableIds.includes(table.id);
  }, [pendingClearTableIds, table.id]);

  const path = useMemo(() => {
    return getTablePath(table.baseId, table.id);
  }, [table.baseId, table.id]);

  const handleContextMenu = useCallback(
    (e: MouseEvent<HTMLDivElement>) => {
      e.preventDefault();
      setPosition({ x: e.clientX, y: e.clientY });
      openMenu();
    },
    [openMenu]
  );

  return (
    <NavigationItem
      key={table.id}
      as='link'
      path={path}
      text={table.name}
      minimizeIcon={index + 1}
      onContextMenu={handleContextMenu}
      rightIconVisible={isClearingData}
      onActiveChange={onActiveChange}
      rightIcon={
        isClearingData ? (
          <CustomImage url={ANIMATED_TRASH_IMAGE_URL} className={classes.trashIcon} />
        ) : (
          <NavigationMenuIcon
            table={table}
            onChange={onMenuChange}
            opened={menuOpened}
            onOpen={openMenu}
            onClose={closeMenu}
            position={position}
          />
        )
      }
    />
  );
};

const BaseNavigationControls = () => {
  const { t } = useTranslate('common');
  const { showCreateNewItems, showSidebar, toggleCreateNewItems, toggleSidebar } = useAppContext();
  const theme = useMantineTheme();
  const [activeTableId, setActiveTableId] = useState<string | null>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const hasScrollIntoView = useRef(false);

  const { baseId, tableId } = useParams();
  const navigate = useNavigate();

  const { tables, isLoading, isValidating, isInitialLoading } = useTablesQuery(baseId);
  const { createTable } = useTablesMutation();

  const [createTableModalOpened, { open: openCreateTableModal, close: closeCreateTableModal }] =
    useDisclosure();
  const [tableImportModalOpened, { open: openTableImportModal, close: closeTableImportModal }] =
    useDisclosure();

  const shouldNavigateToFirstTable = useMemo(() => {
    if (isLoading || isValidating) return false;

    const hasLoadedTables = tables?.length > 0;
    const hasValidTableSelected = tableId && tables?.some((table) => table.id === tableId);

    return hasLoadedTables && !hasValidTableSelected;
  }, [isLoading, isValidating, tables, tableId]);

  const navigateToFirstTable = useCallback(() => {
    if (!shouldNavigateToFirstTable) return;
    const firstTablePath = getTablePath(tables[0].baseId, tables[0].id);
    navigate(firstTablePath, { replace: true });
  }, [shouldNavigateToFirstTable, navigate, tables]);

  const handleCreateTable: CreateFormSubmitCallback = useCallback(
    async ({ formData, closeModal }) => {
      if (!baseId) return;

      const res = await createTable(baseId, { name: formData.name, fields: [] });

      if (res.isOk()) {
        const table = res.value.data;
        notifications.show({
          message: t('success.table.create', { tableName: table.name }),
          status: 'success',
        });
        if (!table.id || !table.baseId) return;
        closeModal();
      }
    },
    [baseId, createTable]
  );

  const renderTableItems = useMemo(() => {
    if (isInitialLoading) {
      return Array.from({ length: NUMBER_OF_SKELETON_ITEMS }).map((_, index) => (
        // biome-ignore lint/suspicious/noArrayIndexKey: <explanation> render skeleton items
        <Skeleton key={index} h={40} w='100%' radius='md' />
      ));
    }

    return tables?.map((table, index) => (
      <TableNavigationItem
        key={table.id}
        table={table}
        index={index}
        onActiveChange={() => setActiveTableId(table.id)}
      />
    ));
  }, [isInitialLoading, tables]);

  useEffect(() => {
    navigateToFirstTable();
  }, [navigateToFirstTable]);

  useEffect(() => {
    const scrollArea = scrollAreaRef.current;
    if (!scrollArea || !activeTableId) return;
    const tableItem = scrollArea.querySelector(ACTIVE_ITEM_SELECTOR);
    if (tableItem) {
      tableItem.scrollIntoView({ block: hasScrollIntoView.current ? 'nearest' : 'start' });
      hasScrollIntoView.current = true;
    }
  }, [activeTableId]);

  return (
    <NavbarWrapper>
      <NavigationItemTitle
        text={t('navigation.title.table')}
        rightIcon={showSidebar ? <IconChevronsLeft /> : <IconChevronsRight />}
        onIconClick={toggleSidebar}
      />

      <ScrollArea
        ref={scrollAreaRef}
        scrollHideDelay={100}
        offsetScrollbars={false}
        px={rem(8)}
        mt={rem(6)}
        mx={rem(-8)}
        sx={{ flex: 1 }}
        scrollbarSize={4}
        styles={{
          viewport: {
            '&>div': {
              display: 'block !important',
            },
          },
        }}
      >
        <Stack gap={rem(6)}>{renderTableItems}</Stack>
      </ScrollArea>

      <Divider w='100%' my={rem(20)} />

      <NavigationItemTitle
        text={t('navigation.title.createNew')}
        rightIcon={
          showCreateNewItems ? (
            <IconChevronDown strokeWidth={3} />
          ) : (
            <IconChevronRight strokeWidth={3} />
          )
        }
        onIconClick={toggleCreateNewItems}
      />

      <Stack gap={rem(6)} mt={rem(6)}>
        {showCreateNewItems ? (
          <>
            <NavigationItem
              as='button'
              onClick={openCreateTableModal}
              text={t('navigation.createNew.table')}
              leftIcon={<IconTable color={theme.colors.blue[4]} />}
            />
            <NavigationItem
              as='button'
              onClick={openTableImportModal}
              text={t('navigation.createNew.importCSV')}
              leftIcon={<IconFileTypeCsv color={theme.colors.decaGreen[4]} />}
            />
          </>
        ) : null}
      </Stack>

      <CreateModal
        opened={createTableModalOpened}
        onCancel={closeCreateTableModal}
        onSubmit={handleCreateTable}
        inputLabel={t('modal.createTableLabel')}
        submitText={t('modal.createBtn')}
        cancelText={t('modal.cancelBtn')}
      />

      <TableImportModal opened={tableImportModalOpened} onClose={closeTableImportModal} />
    </NavbarWrapper>
  );
};

export default BaseNavigationControls;
