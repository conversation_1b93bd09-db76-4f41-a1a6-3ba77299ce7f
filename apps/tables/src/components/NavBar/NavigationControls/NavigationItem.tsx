import { TableTooltip } from '@/components/Common';
import { useAppContext } from '@/contexts';
import { ensureTrailingSlash } from '@/utils';
import { ActionIcon, Box, Center, Text, UnstyledButton, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { usePathParams } from '@resola-ai/ui/hooks';
import { type HTMLAttributes, type ReactNode, useEffect } from 'react';
import { Link, useMatch } from 'react-router-dom';

const ACTIVE_ITEM_SELECTOR = '[data-active="true"]';

const useStyles = createStyles((theme) => ({
  navigationItem: {
    '--navigation-item-height': rem(40),
    '--navigation-item-padding': `${rem(10)} ${rem(8)} ${rem(10)} ${rem(12)}`,
    '--navigation-item-gap': rem(12),
    '--navigation-item-font-size': theme.fontSizes.md,
    '--navigation-item-color': theme.colors.decaGrey[8],
    '--navigation-item-bg': 'transparent',
    '--navigation-item-border-radius': rem(8),
    position: 'relative',
    textDecoration: 'none',
    display: 'flex',
    alignItems: 'center',
    flexShrink: 0,
    fontSize: 'var(--navigation-item-font-size)',
    gap: 'var(--navigation-item-gap)',
    height: 'var(--navigation-item-height)',
    color: 'var(--navigation-item-color)',
    backgroundColor: 'var(--navigation-item-bg)',
    padding: 'var(--navigation-item-padding)',
    whiteSpace: 'nowrap',
    borderRadius: 'var(--navigation-item-border-radius)',
    '&:hover': {
      '--navigation-item-bg': theme.colors.decaNavy[0],
      '--navigation-item-color': theme.colors.decaNavy[5],
    },
    '&[data-collapse="true"]': {
      justifyContent: 'center',
      padding: 0,
      alignItems: 'center',
    },
    '&[data-active="true"], &[data-focus="true"]': {
      '--navigation-item-bg': theme.colors.decaNavy[0],
      '--navigation-item-color': theme.colors.decaNavy[5],
    },
  },
  linkOverlay: {
    position: 'absolute',
    inset: 0,
  },
  rightIcon: {
    position: 'absolute',
    display: 'none',
    right: rem(8),
    '& > button': {
      color: 'var(--navigation-item-color)',
    },
    svg: {
      height: rem(16),
      width: rem(16),
    },
    '[data-collapse="true"] &': {
      inset: 0,
      z: 10,
      '& > button': {
        width: '100%',
        height: '100%',
        opacity: 0,
      },
    },
    '&[data-visible="true"]': {
      display: 'block',
    },
  },
  leftIcon: {
    color: 'var(--navigation-item-color)',
    svg: {
      height: rem(18),
      width: rem(18),
    },
  },

  menuTitleItem: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: `${rem(4)} ${rem(8)}`,
    color: theme.colors.decaGrey[9],
    fontSize: rem(12),
    height: rem(28),
    flexShrink: 0,
    '& svg': {
      width: rem(12),
      height: rem(12),
    },
    '&[data-expanded="false"]': {
      margin: '0 auto',
      padding: 0,
      '& .nav-title': {
        display: 'none',
      },
    },
  },
  menuTitleItemBold: {
    fontSize: rem(10),
    fontWeight: 700,
  },
}));

interface NavigationItemTitleProps {
  bold?: boolean;
  rightIcon: ReactNode;
  onIconClick?: () => void;
  text: string;
  className?: string;
}

const NavigationItemTitle = ({
  text,
  rightIcon,
  onIconClick,
  className,
  bold,
}: NavigationItemTitleProps) => {
  const { classes, cx } = useStyles();
  const { showSidebar } = useAppContext();

  return (
    <Box
      className={cx(classes.menuTitleItem, bold && classes.menuTitleItemBold, className)}
      data-expanded={showSidebar}
    >
      <Text className='nav-title' fz='inherit' fw='inherit'>
        {text}
      </Text>
      <ActionIcon onClick={onIconClick} variant='subtle' color='decaGrey.4'>
        {rightIcon}
      </ActionIcon>
    </Box>
  );
};

interface NavigationItemProps extends HTMLAttributes<HTMLElement> {
  path?: string;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  minimizeIcon?: ReactNode;
  text: string;
  active?: boolean;
  rightIconVisible?: boolean;
  as: 'button' | 'link';
  onActiveChange?: (active: boolean) => void;
}

const NavigationItem = ({
  as,
  path,
  leftIcon,
  rightIcon,
  minimizeIcon,
  text,
  active,
  rightIconVisible,
  onActiveChange,
  ...rest
}: NavigationItemProps) => {
  const { classes } = useStyles();
  const { createPathWithLngParam } = usePathParams();
  const { showSidebar } = useAppContext();

  const isValidLink = as === 'link' && !!path;
  const Component = isValidLink ? Box : UnstyledButton;
  const match = useMatch({
    path: isValidLink ? path : '',
    end: true,
  });
  const isActive = !!match;

  useEffect(() => {
    onActiveChange?.(isActive);
  }, [isActive, onActiveChange]);

  return (
    <TableTooltip position='top' label={text}>
      <Component
        data-active={isActive}
        data-collapse={!showSidebar}
        className={classes.navigationItem}
        {...rest}
      >
        {isValidLink ? (
          <Link
            aria-label={text}
            to={createPathWithLngParam(ensureTrailingSlash(path))}
            className={classes.linkOverlay}
          />
        ) : null}

        {leftIcon ? <Center className={classes.leftIcon}>{leftIcon}</Center> : null}

        {!showSidebar && !leftIcon && minimizeIcon ? minimizeIcon : null}
        {showSidebar ? (
          <Text truncate w={`calc(100% - ${rem(24)})`} fw='inherit'>
            {text}
          </Text>
        ) : null}

        <Box className={classes.rightIcon} data-visible={isActive || rightIconVisible}>
          {rightIcon}
        </Box>
      </Component>
    </TableTooltip>
  );
};

export { NavigationItem, NavigationItemTitle, ACTIVE_ITEM_SELECTOR };
