/** @type {import('next').NextConfig} */

const isStaticExport = process.env.ENABLE_STATIC_EXPORT === 'true';

module.exports = {
  reactStrictMode: false,
  // Only use output: 'export' for static mode
  ...(isStaticExport && {
    output: 'export',
  }),
  images: {
    unoptimized: true,
  },
  // Only use assetPrefix and basePath for static mode
  ...(isStaticExport && {
    assetPrefix: '.',
    basePath: '',
  }),
  // Package optimization for static export
  ...(isStaticExport && {
    experimental: {
      optimizePackageImports: [
        '@resola-ai/services-shared',
        '@resola-ai/ui',
        '@resola-ai/models',
        '@resola-ai/chatwindow',
      ],
    },
  }),
  trailingSlash: false,
  transpilePackages: [
    '@resola-ai/services-shared',
    '@resola-ai/ui',
    '@resola-ai/models',
    '@resola-ai/chatwindow',
  ],
  compiler: {
    emotion: true,
  },
};
