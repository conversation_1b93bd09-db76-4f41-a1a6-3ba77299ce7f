/**
 * DecaPage Embed Script
 * Allows embedding of DecaPage builder pages into any website via iframe
 *
 * @version 1.0.0
 * <AUTHOR> Team
 */
(function (window, document) {
  'use strict';

  // ============================================================================
  // CONFIGURATION & CONSTANTS
  // ============================================================================

  /**
   * Default configuration for embeds
   * @type {Object}
   */
  const DEFAULT_CONFIG = {
    baseUrl: window.location.protocol + '//' + window.location.host,
    width: '100%',
    height: '100%',
    frameBorder: '0',
    scrolling: 'auto',
    allowFullscreen: true,
    sandbox:
      'allow-scripts allow-same-origin allow-forms allow-popups allow-top-navigation-by-user-activation',
    className: 'decapage-embed-iframe',
    loadingText: 'Loading...',
    errorText: 'Failed to load content',
    responsive: true,
    autoHeight: false,
    minHeight: '300px',
    maxHeight: 'none',
  };

  /**
   * CSS styles for embed components
   * @type {string}
   */
  const EMBED_STYLES = `
    .decapage-embed-wrapper {
      position: relative;
      width: 100%;
      box-sizing: border-box;
    }
    
    .decapage-embed-iframe {
      border: none;
      display: block;
      transition: opacity 0.3s ease;
    }
    
    .decapage-embed-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      color: #666;
      font-size: 14px;
      z-index: 1;
    }
    
    .decapage-embed-error {
      padding: 20px;
      text-align: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      color: #d32f2f;
      background-color: #ffebee;
      border: 1px solid #ffcdd2;
      border-radius: 4px;
      font-size: 14px;
    }
    
    .decapage-embed-responsive {
      position: relative;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
    
    .decapage-embed-responsive iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
    
    @media (max-width: 768px) {
      .decapage-embed-wrapper {
        margin: 0 -10px;
      }
    }
  `;

  /**
   * Selector for auto-initialization
   * @type {string}
   */
  const EMBED_SELECTOR = '[data-deca-page-embed-config]';

  /**
   * Style element ID to prevent duplicate injection
   * @type {string}
   */
  const STYLE_ELEMENT_ID = 'decapage-embed-styles';

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  /**
   * Extends target object with source object properties
   * @param {Object} target - Target object
   * @param {Object} source - Source object
   * @returns {Object} Extended target object
   */
  function extend(target, source) {
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        target[key] = source[key];
      }
    }
    return target;
  }

  /**
   * Creates a DOM element with attributes and content
   * @param {string} tag - HTML tag name
   * @param {Object} attributes - Element attributes
   * @param {string} content - Element content
   * @returns {HTMLElement} Created element
   */
  function createElement(tag, attributes, content) {
    const element = document.createElement(tag);

    if (attributes) {
      for (const attr in attributes) {
        if (attributes.hasOwnProperty(attr)) {
          element.setAttribute(attr, attributes[attr]);
        }
      }
    }

    if (content) {
      element.innerHTML = content;
    }

    return element;
  }

  /**
   * Safely parses a float value
   * @param {string|number} value - Value to parse
   * @returns {number} Parsed float or NaN
   */
  function safeParseFloat(value) {
    const parsed = parseFloat(value);
    return isNaN(parsed) ? undefined : parsed;
  }

  /**
   * Safely parses an integer value
   * @param {string|number} value - Value to parse
   * @returns {number} Parsed integer or undefined
   */
  function safeParseInt(value) {
    const parsed = parseInt(value);
    return isNaN(parsed) ? undefined : parsed;
  }

  // ============================================================================
  // STYLING FUNCTIONS
  // ============================================================================

  /**
   * Injects CSS styles into the document head (only once)
   */
  function injectStyles() {
    // Check if styles already injected
    if (document.getElementById(STYLE_ELEMENT_ID)) {
      return;
    }

    const styleSheet = createElement('style', {
      id: STYLE_ELEMENT_ID,
      type: 'text/css',
    });

    // Handle IE compatibility
    if (styleSheet.styleSheet) {
      styleSheet.styleSheet.cssText = EMBED_STYLES;
    } else {
      styleSheet.appendChild(document.createTextNode(EMBED_STYLES));
    }

    document.head.appendChild(styleSheet);
  }

  // ============================================================================
  // URL HANDLING
  // ============================================================================

  /**
   * Constructs the full URL for a page slug
   * @param {string} slug - Page slug
   * @param {string} baseUrl - Base URL
   * @returns {string} Complete page URL
   */
  function buildPageUrl(slug, baseUrl) {
    // If it's already a full URL, return as-is
    if (slug.startsWith('http://') || slug.startsWith('https://')) {
      return slug;
    }

    // Clean up slug and baseUrl
    const cleanSlug = slug.replace(/^\/+|\/+$/g, '');
    const cleanBaseUrl = baseUrl.replace(/\/+$/, '');

    // Handle root/index page
    if (cleanSlug === 'index' || cleanSlug === '') {
      return cleanBaseUrl + '/';
    }

    // Handle slug with .html extension or query parameters
    if (cleanSlug.includes('.html') || cleanSlug.includes('?')) {
      return cleanBaseUrl + '/' + cleanSlug;
    }

    // Default case - add .html extension
    return cleanBaseUrl + '/' + cleanSlug + '.html';
  }

  // ============================================================================
  // DOM CREATION FUNCTIONS
  // ============================================================================

  /**
   * Creates an iframe element with the specified configuration
   * @param {Object} config - Iframe configuration
   * @returns {HTMLIFrameElement} Created iframe
   */
  function createIframe(config) {
    const iframe = createElement('iframe', {
      src: config.src,
      frameborder: config.frameBorder,
      scrolling: config.scrolling,
      class: config.className,
      sandbox: config.sandbox,
      style: `opacity: 0; width: ${config.width}; height: ${config.height};`,
      allow: 'clipboard-write; clipboard-read',
    });

    if (config.allowFullscreen) {
      iframe.setAttribute('allowfullscreen', '');
    }

    return iframe;
  }

  /**
   * Creates a loading indicator element
   * @param {string} loadingText - Loading message
   * @returns {HTMLElement} Loading element
   */
  function createLoadingElement(loadingText) {
    return createElement(
      'div',
      {
        class: 'decapage-embed-loading',
      },
      loadingText
    );
  }

  /**
   * Creates an error display element
   * @param {string} errorText - Error message
   * @returns {HTMLElement} Error element
   */
  function createErrorElement(errorText) {
    return createElement(
      'div',
      {
        class: 'decapage-embed-error',
      },
      errorText
    );
  }

  /**
   * Creates a responsive container for the iframe
   * @param {number} aspectRatio - Aspect ratio for responsive sizing
   * @returns {HTMLElement} Responsive container
   */
  function createResponsiveContainer(aspectRatio) {
    return createElement('div', {
      class: 'decapage-embed-responsive',
      style: `padding-bottom: ${100 / aspectRatio}%;`,
    });
  }

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  /**
   * Handles iframe load event
   * @param {HTMLIFrameElement} iframe - The iframe element
   * @param {HTMLElement} wrapper - Wrapper element
   * @param {HTMLElement} loadingElement - Loading indicator element
   */
  function handleIframeLoad(iframe, wrapper, loadingElement) {
    iframe.style.opacity = '1';

    if (loadingElement && loadingElement.parentNode) {
      loadingElement.parentNode.removeChild(loadingElement);
    }
  }

  /**
   * Handles iframe error event
   * @param {HTMLElement} wrapper - Wrapper element
   * @param {Object} config - Embed configuration
   */
  function handleIframeError(wrapper, config) {
    wrapper.innerHTML = '';
    const errorElement = createErrorElement(config.errorText);
    wrapper.appendChild(errorElement);
  }

  /**
   * Sets up auto-height adjustment using postMessage communication
   * @param {HTMLIFrameElement} iframe - The iframe element
   * @param {Object} config - Embed configuration
   */
  function setupAutoHeight(iframe, config) {
    if (!config.autoHeight) return null;

    // Listen for height messages from iframe
    function handleHeightMessage(event) {
      if (!event.data || event.data.type !== 'deca-iframe-height-change') return;

      const height = event.data.height;
      if (typeof height !== 'number' || height <= 0) return;

      // Apply min/max height constraints
      const minHeight = safeParseInt(config.minHeight) || 300;
      const maxHeight = config.maxHeight === 'none' ? Infinity : safeParseInt(config.maxHeight);

      let adjustedHeight = Math.max(height, minHeight);
      if (maxHeight !== Infinity && maxHeight) {
        adjustedHeight = Math.min(adjustedHeight, maxHeight);
      }

      iframe.style.height = adjustedHeight + 'px';
    }

    window.addEventListener('message', handleHeightMessage);

    // Return cleanup function
    return () => window.removeEventListener('message', handleHeightMessage);
  }

  // ============================================================================
  // CONFIGURATION PROCESSING
  // ============================================================================

  /**
   * Processes and validates embed configuration
   * @param {Object} options - Raw configuration options
   * @returns {Object} Processed configuration
   */
  function processConfig(options) {
    const config = extend({}, DEFAULT_CONFIG);
    extend(config, options || {});

    // Validate required fields
    if (!config.slug) {
      console.error('DecaPage Embed: slug is required');
      return null;
    }
    // Build the source URL normally
    config.src = buildPageUrl(config.slug, config.baseUrl);

    // Check for saved iframe URL if URL binding is enabled
    if (config.urlBinding && config.urlParam) {
      const savedUrl = getInitialIframeUrl(config.urlParam, config);
      if (savedUrl) {
        config.src = savedUrl;
        return config;
      }
    }

    return config;
  }

  /**
   * Extracts configuration from dataset attributes
   * @param {DOMStringMap} dataset - Element dataset
   * @returns {Object} Configuration object
   */
  function extractConfigFromDataset(dataset) {
    try {
      // Parse the JSON configuration
      const configJson = dataset.decaPageEmbedConfig;
      if (!configJson) {
        console.error('DecaPage Embed: Missing data-deca-page-embed-config attribute');
        return null;
      }

      const config = JSON.parse(configJson);

      // Map the new config format to the internal format
      return {
        slug: config.url,
        baseUrl: config.baseUrl,
        width: config.width,
        height: config.height,
        responsive: config.responsive,
        aspectRatio: safeParseFloat(config.aspectRatio),
        autoHeight: config.autoHeight,
        minHeight: config.minHeight,
        maxHeight: config.maxHeight,
        loadingText: config.loadingText,
        errorText: config.errorText,
        // URL binding configuration
        urlBinding: config.urlBinding,
        urlParam: config.urlParam,
        allowedDomains: config.allowedDomains,
      };
    } catch (e) {
      console.error('DecaPage Embed: Failed to parse configuration:', e);
      return null;
    }
  }

  // ============================================================================
  // SIMPLE URL BINDING
  // ============================================================================

  /**
   * Validates if a URL belongs to an allowed domain
   * @param {string} url - URL to validate
   * @param {Array|string} allowedDomains - Allowed domains (array or single domain)
   * @returns {boolean} True if domain is allowed
   */
  function isAllowedDomain(url, allowedDomains) {
    try {
      const urlObj = new URL(url);
      const hostWithPort = urlObj.host.toLowerCase(); // Use host instead of hostname to include port

      // Convert single domain to array for consistency
      const domains = Array.isArray(allowedDomains) ? allowedDomains : [allowedDomains];

      // Check if domain matches any allowed domain
      return domains.some(allowedDomain => {
        const normalizedDomain = allowedDomain.toLowerCase();
        // Exact match or subdomain match
        return hostWithPort === normalizedDomain || hostWithPort.endsWith('.' + normalizedDomain);
      });
    } catch (e) {
      return false;
    }
  }

  /**
   * Extracts path and query from URL (without domain)
   * @param {string} url - Full URL
   * @returns {string} Path and query portion
   */
  function extractPathFromUrl(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.pathname + urlObj.search + urlObj.hash;
    } catch (e) {
      return '';
    }
  }

  /**
   * Constructs secure URL by combining allowed domain with path
   * @param {string} path - Path portion of URL
   * @param {string} baseDomain - Base domain to use
   * @returns {string} Complete secure URL
   */
  function constructSecureUrl(path, baseDomain) {
    try {
      // Ensure baseDomain has protocol
      let domain = baseDomain;
      if (!domain.startsWith('http://') && !domain.startsWith('https://')) {
        domain = 'https://' + domain;
      }

      // Remove trailing slash from domain
      domain = domain.replace(/\/$/, '');

      // Ensure path starts with /
      const normalizedPath = path.startsWith('/') ? path : '/' + path;

      return domain + normalizedPath;
    } catch (e) {
      return baseDomain;
    }
  }

  /**
   * Simple URL binding - updates parent URL when iframe URL changes
   * @param {HTMLIFrameElement} iframe - The iframe element
   * @param {string} urlParam - URL parameter name to use
   * @param {Object} config - Embed configuration with security settings
   */
  function setupSimpleUrlBinding(iframe, urlParam, config) {
    // Get allowed domains from config
    const baseIframeUrl = iframe.src;
    const _url = new URL(baseIframeUrl);
    const allowedDomains = config.allowedDomains || [_url.host, window.location.host];

    // Listen for messages from iframe
    function handleMessage(event) {
      if (event.data.type === 'deca-redirect-to' && event.data.redirectTo) {
        window.location.href = event.data.redirectTo;
        return;
      }
      if (event.data.type === 'deca-auto-height') {
        const height = Number(event.data.height);
        if (!isNaN(height) && height > 0 && height <= 10000) {
          iframe.style.height = height + 'px';
        }
        return;
      }
      if (!event.data || event.data.type !== 'deca-iframe-url-change') return;
      window.scrollTo(0, 0);
      iframe.style.removeProperty('height');

      const iframeUrl = event.data.url;
      if (!iframeUrl) return;

      // Validate domain security
      if (!isAllowedDomain(iframeUrl, allowedDomains)) {
        console.warn('DecaPage Embed: Blocked URL from disallowed domain:', iframeUrl);
        return;
      }

      // Extract only the path portion for storage
      const pathPortion = extractPathFromUrl(iframeUrl);
      if (!pathPortion) return;

      // Update parent URL with only the path portion
      try {
        const parentUrl = new URL(window.location);
        parentUrl.searchParams.set(urlParam, encodeURIComponent(pathPortion));
        window.history.replaceState(null, '', parentUrl.toString());
        iframe.contentWindow?.postMessage(
          {
            type: 'deca-parent-url',
            url: parentUrl.toString(),
          },
          '*'
        );
      } catch (e) {
        console.warn('Failed to update parent URL:', e);
      }
    }

    window.addEventListener('message', handleMessage);

    // Return cleanup function
    return () => window.removeEventListener('message', handleMessage);
  }

  function setupResizeWindow(iframe) {
    function calculateResponsiveBreakpoints(parentWidth, iframeWidth) {
      // Safety check: prevent division by zero
      if (parentWidth === 0 || iframeWidth === 0) {
        // Return standard breakpoints as fallback
        return {
          tablet: '768px',
          desktop: '1200px'
        };
      }
      
      const ratio = iframeWidth / parentWidth;
      
      // Standard breakpoints for full-width scenario
      const standardTablet = 768;
      const standardDesktop = 1200;
      
      // Scale breakpoints proportionally with minimum bounds
      const tabletBreakpoint = Math.max(320, Math.round(standardTablet * ratio));
      const desktopBreakpoint = Math.max(480, Math.round(standardDesktop * ratio));
      
      return {
        tablet: `${tabletBreakpoint}px`,
        desktop: `${desktopBreakpoint}px`
      };
    }

    function handleResize() {
      const parentWidth = window.innerWidth;
      const iframeWidth = iframe.offsetWidth;
      
      // Don't send if iframe not loaded yet
      if (iframeWidth === 0) return;
      
      const breakpoints = calculateResponsiveBreakpoints(parentWidth, iframeWidth);

      iframe.contentWindow?.postMessage(
        {
          type: 'deca-responsive-breakpoints',
          breakpoints: breakpoints
        },
        '*'
      );
    }
    
    window.addEventListener('resize', handleResize);
    
    // Send initial breakpoints immediately
    handleResize();
    
    // Return cleanup function to remove event listeners
    return function cleanup() {
      window.removeEventListener('resize', handleResize);
    };
  }

  /**
   * Get initial iframe URL from parent URL parameter (with security validation)
   * @param {string} urlParam - URL parameter name
   * @param {Object} config - Embed configuration with security settings
   * @returns {string|null} Secure iframe URL or null
   */
  function getInitialIframeUrl(urlParam, config) {
    try {
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.get('embed_id')) {
        urlParams.set(urlParam, `url?id=${urlParams.get('embed_id')}`)
      }
      const encodedPath = urlParams.get(urlParam);
      if (!encodedPath) return null;
      const pathPortion = decodeURIComponent(encodedPath);
      console.log('DecaPage Embed: Decoded path portion:', pathPortion);
      // Get base domain for reconstruction
      const baseDomain = config.src || window.location.protocol + '//' + window.location.host;

      // Construct secure URL
      const secureUrl = constructSecureUrl(pathPortion, baseDomain);

      // Validate the constructed URL against allowed domains
      const _url = new URL(config.src);
      const allowedDomains = config.allowedDomains || [_url.host, window.location.host];
      console.log('DecaPage Embed: Validating against allowed domains:', allowedDomains);

      if (!isAllowedDomain(secureUrl, allowedDomains)) {
        console.warn('DecaPage Embed: Blocked URL construction for disallowed domain');
        return null;
      }

      console.log('DecaPage Embed: Successfully validated URL:', secureUrl);
      return secureUrl;
    } catch (e) {
      console.warn('DecaPage Embed: Failed to construct secure URL:', e);
      return null;
    }
  }

  // ============================================================================
  // MAIN EMBED FUNCTION
  // ============================================================================

  /**
   * Main function to embed a page into a DOM element
   * @param {HTMLElement} element - Target DOM element
   * @param {Object} options - Embed options
   * @returns {Object|null} Embed instance with control methods
   */
  function embedPage(element, options) {
    // Process and validate configuration
    const config = processConfig(options);
    if (!config) return null;

    // Ensure styles are injected
    injectStyles();

    // Create wrapper element
    const wrapper = createElement('div', {
      class: 'decapage-embed-wrapper',
    });

    // Set wrapper dimensions if not using responsive mode
    if (!config.responsive || !config.aspectRatio) {
      wrapper.style.width = config.width;
      wrapper.style.height = config.height;
    }

    // Create and add loading indicator
    const loadingElement = createLoadingElement(config.loadingText);
    wrapper.appendChild(loadingElement);

    // Create iframe
    const iframe = createIframe(config);

    // Handle responsive layout
    if (config.responsive && config.aspectRatio) {
      const responsiveContainer = createResponsiveContainer(config.aspectRatio);
      responsiveContainer.appendChild(iframe);
      wrapper.appendChild(responsiveContainer);
    } else {
      wrapper.appendChild(iframe);
    }

    // Setup URL binding if enabled
    let urlBindingCleanup = null;
    if (config.urlBinding) {
      urlBindingCleanup = setupSimpleUrlBinding(iframe, config.urlParam, config);
    }

    // Setup resize window and store cleanup function
    let resizeWindowCleanup = null;
    
    // Set up message listener immediately to avoid race condition
    function handleIframeMessage(event) {
      if (event.data.type === 'deca-iframe-ready') {
        // Clean up any existing resize window setup before creating new one
        if (resizeWindowCleanup) {
          resizeWindowCleanup();
        }
        resizeWindowCleanup = setupResizeWindow(iframe);
      }
    }
    
    window.addEventListener('message', handleIframeMessage);

    // Attach event handlers
    iframe.onload = () => {
      handleIframeLoad(iframe, wrapper, loadingElement);
    };
    iframe.onerror = () => handleIframeError(wrapper, config);

    // Setup auto-height adjustment
    const autoHeightCleanup = setupAutoHeight(iframe, config);

    // Replace target element with wrapper
    if (element.parentNode) {
      element.parentNode.replaceChild(wrapper, element);
    }

    // Return embed instance with control methods
    return {
      iframe,
      wrapper,
      config,
      /**
       * Reloads the iframe content
       */
      reload() {
        iframe.src = iframe.src;
      },
      /**
       * Destroys the embed and removes it from DOM
       */
      destroy() {
        // Cleanup URL binding
        if (urlBindingCleanup) {
          urlBindingCleanup();
        }
        // Cleanup auto-height adjustment
        if (autoHeightCleanup) {
          autoHeightCleanup();
        }
        // Cleanup resize window
        if (resizeWindowCleanup) {
          resizeWindowCleanup();
        }
        // Cleanup iframe message listener
        window.removeEventListener('message', handleIframeMessage);
        
        if (wrapper.parentNode) {
          wrapper.parentNode.removeChild(wrapper);
        }
      },
    };
  }

  // ============================================================================
  // AUTO-INITIALIZATION
  // ============================================================================

  /**
   * Automatically initializes all embed elements found in the document
   */
  function autoInit() {
    const elements = document.querySelectorAll(EMBED_SELECTOR);

    Array.prototype.forEach.call(elements, element => {
      const rawConfig = extractConfigFromDataset(element.dataset);

      // Skip if config extraction failed
      if (!rawConfig) return;

      // Clean up undefined values
      const cleanConfig = {};
      for (const key in rawConfig) {
        if (rawConfig[key] !== undefined && rawConfig[key] !== '') {
          cleanConfig[key] = rawConfig[key];
        }
      }

      embedPage(element, cleanConfig);
    });
  }

  // ============================================================================
  // GLOBAL API
  // ============================================================================

  /**
   * Global DecaPage Embed API
   */
  window.decapageEmbed = {
    /**
     * Embeds a page into the specified element
     * @param {HTMLElement} element - Target element
     * @param {Object} options - Embed options
     * @returns {Object|null} Embed instance
     */
    embed: embedPage,

    /**
     * Manually initializes all embed elements
     */
    init: autoInit,

    /**
     * Library version
     */
    version: '1.0.0',
  };

  // ============================================================================
  // INITIALIZATION
  // ============================================================================

  /**
   * Auto-initialize when DOM is ready
   */
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', autoInit);
  } else {
    // DOM already loaded
    autoInit();
  }
})(window, document);
