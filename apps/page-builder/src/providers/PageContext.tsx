import type React from 'react';
import { type ReactNode, createContext, useContext } from 'react';

interface PageContextType {
  page: Record<string, any>;
  pages: Record<string, any>[];
}

const PageContext = createContext<PageContextType | undefined>(undefined);

interface PageProviderProps {
  children: ReactNode;
  page: Record<string, any>;
  pages: Record<string, any>[];
}

export const PageProvider: React.FC<PageProviderProps> = ({ children, page, pages }) => {
  const value: PageContextType = {
    page,
    pages,
  };

  return <PageContext.Provider value={value}>{children}</PageContext.Provider>;
};

export const usePageContext = (): PageContextType => {
  const context = useContext(PageContext);
  if (context === undefined) {
    throw new Error('usePageContext must be used within a PageProvider');
  }
  return context;
};

export default PageContext;
