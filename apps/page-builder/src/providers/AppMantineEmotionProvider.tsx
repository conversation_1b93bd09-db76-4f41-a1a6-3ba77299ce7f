import { emotionCache } from '@/cache';
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider, emotionTransform } from '@mantine/emotion';
import { themeConfigurations } from '@resola-ai/ui/constants';

export const AppMantineEmotionProvider = ({ children }: { children: React.ReactNode }) => {
  return (
    <MantineEmotionProvider cache={emotionCache}>
      <MantineProvider
        withGlobalClasses
        stylesTransform={emotionTransform}
        theme={themeConfigurations}
      >
        {children}
      </MantineProvider>
    </MantineEmotionProvider>
  );
};
