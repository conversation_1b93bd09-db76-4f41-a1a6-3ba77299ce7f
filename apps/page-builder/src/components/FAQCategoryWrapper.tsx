import { useCategoriesData } from '@/hooks';
import {
  IllustrationCategoryElement,
  PictureCategoryElement,
} from '@resola-ai/ui/components/PageBuilder';
import { IllustrationCategorySkeleton, PictureCategorySkeleton } from './skeletons';

const FAQCategoryWrapper = (props: Record<string, any>) => {
  const { elementId, siteId, type } = props;
  const { categories, isValidating } = useCategoriesData(siteId);

  if (isValidating) {
    return type === 'picture' ? <PictureCategorySkeleton /> : <IllustrationCategorySkeleton />;
  }

  const _categories =
    categories?.find((category: any) => category.elementId === elementId)?.categories || [];
  const _props = { ...props, categories: _categories };

  return type === 'picture' ? (
    <PictureCategoryElement {..._props} />
  ) : (
    <IllustrationCategoryElement {..._props} />
  );
};

export default FAQCategoryWrapper;
