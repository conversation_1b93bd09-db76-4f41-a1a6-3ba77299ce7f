import type { Integration } from '@/types/intergration';
import type { Page } from '@/types/page';
import { getGoogleAnalyticsIntergration } from '@/utils/page-utils';
import Head from 'next/head';
interface MetaProps {
  url?: string;
  image: string;
  title: string;
  description: string;
  favicon?: string;
  integrations: Integration[];
  page: Page;
}

function Meta({ url = '', image, title, description, favicon, integrations, page }: MetaProps) {
  // integrations is optional, so we need to pass an empty array if it's not provided
  let googleAnalyticsMeasurementId = '';
  let globalData = {};
  try {
    const googleAnalyticsIntegration = getGoogleAnalyticsIntergration(integrations || []);
    if (googleAnalyticsIntegration) {
      googleAnalyticsMeasurementId = googleAnalyticsIntegration.resource_id;
      globalData = {
        faq_article_view:
          googleAnalyticsIntegration?.options?.service_options?.faq_article_view || false,
        faq_search: googleAnalyticsIntegration?.options?.service_options?.faq_search || false,
      };
    }
  } catch (error) {
    console.error('Error in Meta:', error);
  }

  return (
    <Head>
      <link rel='icon' href={favicon || '/favicon.ico'} />
      {/* Standard meta tags */}
      <title>{title}</title>
      <meta name='description' content={description} />
      <meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=no' />

      {/* Open Graph / Facebook meta tags */}
      <meta property='og:type' content='website' />
      <meta property='og:url' content={url} />
      <meta property='og:title' content={title} />
      <meta property='og:description' content={description} />
      <meta property='og:image' content={image} />

      {/* Twitter meta tags */}
      <meta name='twitter:card' content={image} />
      <meta name='twitter:url' content={url} />
      <meta name='twitter:title' content={title} />
      <meta name='twitter:description' content={description} />
      <meta name='twitter:image' content={image} />

      {googleAnalyticsMeasurementId && (
        <>
          {/* Global data */}
          <script
            dangerouslySetInnerHTML={{
              __html: `
              window.PAGE_INTEGRATIONS = ${JSON.stringify(globalData)};
            `,
            }}
          />
          {/* GA4 script */}
          <script
            async
            src={`https://www.googletagmanager.com/gtag/js?id=${googleAnalyticsMeasurementId}`}
          />
          <script
            dangerouslySetInnerHTML={{
              __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${googleAnalyticsMeasurementId}');
            `,
            }}
          />
          {/* GA4 script */}
          {/* Listen for GA4 script load */}
          <script
            dangerouslySetInnerHTML={{
              __html: `
              (function() {
                var script = document.querySelector('script[src*="googletagmanager.com/gtag/js"]');
                if (script) {
                  script.onload = function() {
                    window.PAGE_INTEGRATIONS.is_loaded_ga4 = true;
                  };
                }
              })();
            `,
            }}
          />
        </>
      )}
    </Head>
  );
}

export default Meta;
