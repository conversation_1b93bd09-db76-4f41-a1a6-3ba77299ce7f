import { generateShortenUrl, sendViewEvent } from '@/api/site';
import { useArticleDetail } from '@/hooks';
import { useViewport } from '@/hooks/useViewport';
import { usePageContext } from '@/providers';
import type { VoteType } from '@/types/page';
import { sendGAEvent } from '@/utils/page-utils';
import { ArticleDetailElement as ArticleDetailElementUI } from '@resola-ai/ui/components/PageBuilder';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

const ArticleDetailElement = (props: Record<string, any>) => {
  const [copied, setCopied] = useState<boolean>(false);
  const [feedbackStatus, setFeedbackStatus] = useState<string>();
  const deviceType = useViewport();
  const searchParams = useSearchParams();
  const baseId = searchParams.get('faq_base_id');
  const articleId = searchParams.get('faq_article_id');
  const ref = searchParams.get('ref');
  const { pages, page } = usePageContext();
  const { article } = useArticleDetail(baseId as string, articleId as string);

  const getFeedbackFromStorage = (articleId: string): string => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem(`feedback_${articleId}`);
      return stored || '';
    }
    return '';
  };

  const saveFeedbackToStorage = (articleId: string, feedbackType: VoteType): void => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(`feedback_${articleId}`, feedbackType);
    }
  };

  useEffect(() => {
    if (article && articleId) {
      sendViewEvent({
        article_id: articleId,
        page_id: page.id,
        page_title: page.name,
        event_type: 'view',
        device_type: deviceType,
      });

      let source = 'direct';
      const sourcePage = pages.find((p) => document.referrer?.includes(p.url));
      if ((ref && ref === 'quick_search') || sourcePage?.type === 'faq_search_result') {
        source = 'search';
      } else if (sourcePage?.type === 'faq_category_list') {
        source = 'category';
      }

      sendGAEvent('faq_article_view', {
        article_id: articleId,
        article_title: article.title,
        url: window.location.href,
        source: source,
      });

      const previousFeedback = getFeedbackFromStorage(articleId);
      if (previousFeedback) {
        setFeedbackStatus(previousFeedback);
      }
    }
  }, [article, articleId]);

  const fetchShortenUrl = async () => {
    const currentLink =
      window.top === window ? window.location.href : (window as any).BASE_PARENT_URL || '';
    const shorten = await generateShortenUrl({
      url: currentLink,
      resource_id: process.env.NEXT_PUBLIC_SITE_ID as string,
      resource_type: 'site',
    });

    if (shorten?.id) {
      setCopied(true);
      setTimeout(() => {
        setCopied(false);
      }, 5000);
      if (window.top === window) {
        return `${`${window.location.protocol}//${window.location.host}`}/url?id=${shorten.id}`;
      }
      const link = new URL(currentLink);
      link.searchParams.delete('embed_state');
      link.searchParams.set('embed_id', shorten?.id);
      return link.toString();
    }
    return '';
  };

  const handleFeedback = (type: VoteType) => {
    sendViewEvent({
      article_id: articleId,
      page_id: page.id,
      page_title: page.name,
      event_type: type,
      device_type: deviceType,
    });
    setFeedbackStatus(type);
    if (articleId) {
      saveFeedbackToStorage(articleId, type);
    }
  };

  return (
    <ArticleDetailElementUI
      {...props}
      onShortenUrl={fetchShortenUrl}
      onFeedback={handleFeedback}
      isCopied={copied}
      feedbackStatus={feedbackStatus}
      article={article}
    />
  );
};

export default ArticleDetailElement;
