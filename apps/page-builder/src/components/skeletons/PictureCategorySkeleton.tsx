import { Box, SimpleGrid, Skeleton } from '@mantine/core';

const PictureCategorySkeleton = () => {
  return (
    <SimpleGrid cols={3} spacing='md'>
      {Array.from({ length: 6 }).map((_, index) => (
        <Box
          key={index}
          style={{ border: '1px solid #e9ecef', borderRadius: '8px', overflow: 'hidden' }}
        >
          {/* Picture area */}
          <Skeleton height={200} />

          {/* Info area */}
          <Box p='md'>
            <Skeleton height={20} width='70%' mb='sm' />
            <Skeleton height={16} width='90%' mb='xs' />
            <Skeleton height={16} width='60%' mb='sm' />
            <Skeleton height={14} width='40%' />
          </Box>
        </Box>
      ))}
    </SimpleGrid>
  );
};

export default PictureCategorySkeleton;
