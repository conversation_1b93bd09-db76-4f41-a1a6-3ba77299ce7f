import { Box, Flex, SimpleGrid, Skeleton } from '@mantine/core';

const IllustrationCategorySkeleton = () => {
  return (
    <SimpleGrid cols={3} spacing='md'>
      {Array.from({ length: 6 }).map((_, index) => (
        <Flex
          key={index}
          align='center'
          p='md'
          style={{ border: '1px solid #e9ecef', borderRadius: '8px' }}
        >
          {/* Icon area */}
          <Skeleton height={48} width={48} mr='md' />

          {/* Info area */}
          <Box flex={1}>
            <Skeleton height={18} width='60%' mb='xs' />
            <Skeleton height={14} width='40%' />
          </Box>
        </Flex>
      ))}
    </SimpleGrid>
  );
};

export default IllustrationCategorySkeleton;
