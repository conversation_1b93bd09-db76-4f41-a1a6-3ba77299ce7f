import { Element, useNode } from '@craftjs/core';
import { Flex } from '@mantine/core';
import ContainerElement from '@resola-ai/ui/components/PageBuilder/ContainerElement';
import { BackgroundType } from '@resola-ai/ui/types/pageBuilder';
import {
  createResponsiveValue,
  generateResponsiveDimension,
  generateResponsivePadding,
  generateResponsiveStyles,
} from '@resola-ai/ui/utils';

const getBackgroundSize = (objectFit: string) => {
  return ['cover', 'fill'].includes(objectFit) ? 'cover' : 'contain';
};

const ContainerWrapper = (props: Record<string, any>) => {
  const {
    direction,
    width,
    height,
    padding,
    align,
    justify,
    gap,
    mediaUrl,
    objectFit,
    backgroundType,
    backgroundColor,
    overlay,
  } = props;
  const { linkedNodes } = useNode((node) => ({
    linkedNodes: node.data.linkedNodes,
  }));

  return (
    <Flex
      styles={generateResponsiveStyles({
        flexDirection: direction ?? 'column',
        width: generateResponsiveDimension(width),
        height: generateResponsiveDimension(height),
        alignItems: align ?? 'flex-start',
        justifyContent: justify ?? 'flex-start',
        gap: gap
          ? createResponsiveValue(`${gap.mobile}px`, `${gap.tablet}px`, `${gap.desktop}px`)
          : '0px',
        padding: generateResponsivePadding(padding),
        ...(backgroundType === BackgroundType.Image && {
          backgroundImage: `url(${mediaUrl})`,
        }),
        ...(backgroundType === BackgroundType.Color && {
          backgroundColor: backgroundColor,
        }),
        ...(backgroundType === BackgroundType.None && {
          backgroundColor: 'transparent',
        }),
        backgroundSize: createResponsiveValue(
          getBackgroundSize(objectFit?.mobile),
          getBackgroundSize(objectFit?.tablet),
          getBackgroundSize(objectFit?.desktop)
        ),
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        position: 'relative',
        '&::before': overlay?.enabled
          ? {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: overlay.color,
              opacity: overlay.opacity / 100,
              pointerEvents: 'none',
              zIndex: 1,
            }
          : {},
        '& > *': {
          position: 'relative',
          zIndex: 2,
        },
      })}
    >
      {Object.keys(linkedNodes).map((id) => (
        <Element is={ContainerElement} key={id} id={id} />
      ))}
    </Flex>
  );
};

export default ContainerWrapper;
