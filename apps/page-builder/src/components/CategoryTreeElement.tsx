import { useCategoriesData } from '@/hooks';
import { useViewport } from '@/hooks/useViewport';
import { findNestedCategoryOrArticleById } from '@/utils/page-utils';
import { CategoryTreeElement as CategoryTreeElementUI } from '@resola-ai/ui/components/PageBuilder';
import { isEmpty } from 'lodash';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

const CategoryTreeElement = (props: Record<string, any>) => {
  const router = useRouter();
  const deviceType = useViewport();
  const {
    siteId,
    padding,
    width,
    articleDetailSlug,
    categoryListSlug,
    styles,
    showRightDivider,
    dividerColor,
  } = props;
  const {
    element_id,
    faq_article_id = '',
    faq_category_id = '',
    faq_sub_category_id = '',
  } = router.query;

  // const [categories, setCategories] = useState<any>([]);
  const [selectedCategories, setSelectedCategories] = useState();
  const { categories } = useCategoriesData(siteId);

  // useEffect(() => {
  //   if (siteId) {
  //     const getSiteCategories = async (siteId: string) => {
  //       await getCategories(siteId).then(res => {
  //         if (res) {
  //           setCategories(res);
  //         }
  //       });
  //     };
  //     getSiteCategories(siteId);
  //   }
  // }, [siteId]);

  useEffect(() => {
    const { categories: foundCategories } = findNestedCategoryOrArticleById(
      categories ?? [],
      faq_category_id as string,
      faq_sub_category_id as string,
      faq_article_id as string,
      element_id as string
    );
    setSelectedCategories(foundCategories);
  }, [categories]);

  if (isEmpty(selectedCategories)) return null;

  return (
    <CategoryTreeElementUI
      padding={padding}
      width={width}
      categories={(selectedCategories as any)?.categories}
      selectedElement={element_id as string}
      selectedArticle={faq_article_id as string}
      selectedCategory={faq_category_id as string}
      selectedSubCategory={faq_sub_category_id as string}
      articleDetailSlug={articleDetailSlug}
      categoryListSlug={categoryListSlug}
      styles={styles}
      showRightDivider={showRightDivider}
      dividerColor={dividerColor}
      deviceType={deviceType}
    />
  );
};

export default CategoryTreeElement;
