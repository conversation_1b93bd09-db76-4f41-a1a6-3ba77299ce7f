import { sendViewEvent } from '@/api/site';
import { useConditionalArticles } from '@/hooks';
import { useViewport } from '@/hooks/useViewport';
import { usePageContext } from '@/providers';
import { sendGAEvent } from '@/utils/page-utils';
import { FlatArticlesElement as FlatArticlesElementUI } from '@resola-ai/ui/components/PageBuilder';

const FlatArticlesElement = (props: Record<string, any>) => {
  const { displayMode, selectedArticles, maximumArticles, elementId, siteId } = props;
  const { articles } = useConditionalArticles(
    selectedArticles,
    displayMode,
    maximumArticles,
    elementId,
    siteId
  );
  const _props = { ...props, selectedArticles: articles };
  const deviceType = useViewport();
  const { page } = usePageContext();

  const handleChangeArticle = (value: string) => {
    sendViewEvent({
      article_id: value,
      page_id: page?.id,
      page_title: page?.name,
      event_type: 'view',
      device_type: deviceType,
    });
    const selectedArticle = articles.find((article: any) => article.value === value);
    if (selectedArticle) {
      sendGAEvent('faq_article_view', {
        article_id: value,
        article_title: selectedArticle.label,
        url: window.location.href,
        source: 'article_list',
      });
    }
  };
  return <FlatArticlesElementUI onChangeArticle={handleChangeArticle} {..._props} />;
};

export default FlatArticlesElement;
