import { useArticleDetail, useCategoriesData } from '@/hooks';
import { findNestedCategoryOrArticleById } from '@/utils/page-utils';
import { BreadcrumbElement as BreadcrumbElementUI } from '@resola-ai/ui/components/PageBuilder';
import { FAQCategoryType } from '@resola-ai/ui/types/pageBuilder';
import { useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';

const BreadcrumbElement = (props: Record<string, any>) => {
  const { siteId, categoryListSlug, articleDetailSlug, ...rest } = props;
  const searchParams = useSearchParams();
  const articleId = searchParams.get('faq_article_id');
  const baseId = searchParams.get('faq_base_id');
  const categoryId = searchParams.get('faq_category_id');
  const subCategoryId = searchParams.get('faq_sub_category_id');
  const elementId = searchParams.get('element_id');

  const [categoriesData, setCategoriesData] = useState([]);

  const { categories } = useCategoriesData(siteId);
  const { article } = useArticleDetail(baseId as string, articleId as string);

  useEffect(() => {
    const { categories: foundCategories } = findNestedCategoryOrArticleById(
      categories ?? [],
      categoryId as string,
      subCategoryId as string,
      articleId as string,
      elementId as string
    );
    setCategoriesData(foundCategories?.categories);
  }, [categories]);

  const getUrl = (parentCategoryId, currentCategoryId, currentArticleId, categoryType) => {
    if (categoryType === FAQCategoryType.Category) {
      return categoryListSlug ? `${categoryListSlug}?faq_category_id=${currentCategoryId}` : '#';
    }
    if (categoryType === FAQCategoryType.SubCategory) {
      return categoryListSlug
        ? `${categoryListSlug}?faq_category_id=${parentCategoryId}&faq_sub_category_id=${currentCategoryId}`
        : '#';
    }
    return articleDetailSlug
      ? `${articleDetailSlug}?faq_article_id=${currentArticleId}&faq_base_id=${currentCategoryId}`
      : '#';
  };

  const breadcrumbMap = useMemo(() => {
    const map = new Map();
    if (!categoriesData) return map;
    categoriesData?.forEach((category) => {
      const addArticlesToMap = (
        category: Record<string, any>,
        breadcrumbs: { label: string; url: string }[] = [],
        categoryType: string = FAQCategoryType.Category,
        parentCategory: any = null
      ) => {
        let urlCategory = '';
        if (category.subType === 'article') {
          category.data?.forEach((article) => {
            urlCategory = getUrl(parentCategory?.id, category?.id, article?.value, categoryType);
            map.set(article.value, {
              ...article,
              breadcrumbs: [
                ...breadcrumbs,
                { label: category.name, url: urlCategory },
                { label: article.label },
              ],
            });
          });
        } else if (category.subType === 'category') {
          category.data?.forEach((subCategory: any) => {
            urlCategory = getUrl(parentCategory?.id, category?.id, null, categoryType);
            addArticlesToMap(
              subCategory,
              [...breadcrumbs, { label: category.name, url: urlCategory }],
              FAQCategoryType.SubCategory,
              category
            );
          });
        }
        map.set(category.id, {
          ...category,
          breadcrumbs: [...breadcrumbs, { label: category.name, url: urlCategory }],
        });
      };
      addArticlesToMap(category);
    });

    return map;
  }, [categoriesData]);

  const selectedArticle = breadcrumbMap.get(articleId);
  const selectedCategory = breadcrumbMap.get(subCategoryId || categoryId);
  const searchResultBreadcrumbs =
    articleId &&
    typeof window !== 'undefined' &&
    document.referrer &&
    document.referrer?.includes('faq-search-result')
      ? [
          { label: 'searchResults', url: document.referrer, isTranslateLabel: true },
          {
            label: (article as any)?.title || selectedArticle?.label,
            url: typeof window !== 'undefined' ? location.href : '',
          },
        ]
      : null;

  return (
    <BreadcrumbElementUI
      {...rest}
      breadcrumbs={
        searchResultBreadcrumbs ||
        selectedArticle?.breadcrumbs ||
        selectedCategory?.breadcrumbs ||
        []
      }
    />
  );
};

export default BreadcrumbElement;
