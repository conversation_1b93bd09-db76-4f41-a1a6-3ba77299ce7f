import ChatwindowElement from '@/components/ChatwindowElement';
import Meta from '@/components/Meta';
import type { Integration } from '@/types/intergration';
import type { Page } from '@/types/page';
import { componentResolver, getChatwindowIntegrationId } from '@/utils/page-utils';
import { Editor, Frame } from '@craftjs/core';
import { updateGlobalBreakpoints } from '@resola-ai/ui/utils/pageBuilder';
import { useEffect, useState } from 'react';

interface PageRendererProps {
  page: Record<string, any>;
  site?: Record<string, any>;
  siteSetting: Record<string, any>;
  integrations: Integration[];
  orgId: string;
}

export default function PageRenderer({
  page,
  site,
  siteSetting,
  integrations,
  orgId,
}: PageRendererProps) {
  const [breakpointKey, setBreakpointKey] = useState(0);

  useEffect(() => {
    function handleParentMessage(event: MessageEvent) {
      // Validate message structure
      if (
        !event.data ||
        event.data.type !== 'deca-responsive-breakpoints' ||
        !event.data.breakpoints ||
        typeof event.data.breakpoints !== 'object'
      ) {
        return;
      }

      const { tablet, desktop } = event.data.breakpoints;

      // Validate required properties exist
      if (tablet === undefined || desktop === undefined) {
        return;
      }

      // Helper function to safely parse breakpoint values
      function parseBreakpoint(value: any, fallback: number): number {
        // Handle string values (e.g., "768px", "1200px")
        if (typeof value === 'string') {
          const numericValue = Number.parseInt(value.replace('px', ''));
          return Number.isNaN(numericValue) ? fallback : numericValue;
        }

        // Handle numeric values
        if (typeof value === 'number') {
          return Number.isNaN(value) || !Number.isFinite(value) ? fallback : Math.round(value);
        }

        // Invalid type, use fallback
        return fallback;
      }

      const newBreakpoints = {
        tablet: parseBreakpoint(tablet, 768),
        desktop: parseBreakpoint(desktop, 1200),
      };

      // Additional validation: ensure reasonable values
      if (
        newBreakpoints.tablet <= 0 ||
        newBreakpoints.tablet > 5000 ||
        newBreakpoints.desktop <= 0 ||
        newBreakpoints.desktop > 5000
      ) {
        return; // Reject unreasonable breakpoint values
      }

      // Ensure desktop breakpoint is >= tablet breakpoint
      if (newBreakpoints.desktop < newBreakpoints.tablet) {
        return; // Reject invalid breakpoint ordering
      }

      const hasChanged = updateGlobalBreakpoints(newBreakpoints);
      if (hasChanged) {
        setBreakpointKey((prev) => prev + 1);
      }
    }

    window.addEventListener('message', handleParentMessage);

    // Signal to parent that we're ready to receive messages
    window.parent?.postMessage({ type: 'deca-iframe-ready' }, '*');

    return () => window.removeEventListener('message', handleParentMessage);
  }, []);

  if (!page) {
    return null;
  }

  let chatwindowIntegrationId = '';
  try {
    chatwindowIntegrationId = getChatwindowIntegrationId(integrations, page);
  } catch (error) {
    console.error('Error in PageRenderer:', error);
  }

  const { theme } = siteSetting;

  return (
    <>
      <Meta
        url=''
        title={`${site?.name ? `${site?.name} - ` : ''}${page.name}`}
        description={page?.metadata?.description || siteSetting?.description || ''}
        image={page?.metadata?.image || site?.visual_assets?.thumbnail || ''}
        favicon={site?.visual_assets?.favicon || siteSetting?.visual_assets?.favicon || ''}
        integrations={integrations}
        page={page as unknown as Page}
      />
      <div
        style={{
          fontFamily: `'${theme?.content?.typography?.body?.font_family || ''}'`,
        }}
      >
        <Editor key={breakpointKey} resolver={componentResolver} enabled={false}>
          <Frame data={page.content} />
        </Editor>
        {chatwindowIntegrationId && (
          <ChatwindowElement boxId={chatwindowIntegrationId} orgId={orgId} />
        )}
      </div>
    </>
  );
}
