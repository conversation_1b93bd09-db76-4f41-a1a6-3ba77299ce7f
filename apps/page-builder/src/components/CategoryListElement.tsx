import { useCategoriesData } from '@/hooks';
import { findNestedCategoryOrArticleById } from '@/utils/page-utils';
import { CategoryListElement as CategoryListElementUI } from '@resola-ai/ui/components/PageBuilder';
import {
  type Category,
  type CategoryArticle,
  FAQCategoryType,
} from '@resola-ai/ui/types/pageBuilder';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

const CategoryListElement = (props: Record<string, any>) => {
  const router = useRouter();
  const {
    siteId,
    type,
    padding,
    contentPadding,
    backgroundColor,
    showDescription,
    showPicture,
    textColor,
    iconColor,
    containerBorderColor,
    articleDetailSlug,
    contentBackgroundColor,
  } = props;
  const { element_id, faq_category_id, faq_sub_category_id } = router.query;

  const [selectedCategory, setSelectedCategory] = useState();
  const [selectedSubCategory, setSelectedSubCategory] = useState();

  const { categories } = useCategoriesData(siteId);

  useEffect(() => {
    const { category: foundCategory, subCategory: foundSubCategory } =
      findNestedCategoryOrArticleById(
        categories ?? [],
        faq_category_id as string,
        faq_sub_category_id as string,
        '',
        element_id as string
      );

    setSelectedCategory(foundCategory);
    setSelectedSubCategory(foundSubCategory);
  }, [categories]);

  const handleClick = (item: Category | CategoryArticle) => {
    if (item.type === FAQCategoryType.Article && !item.subType) {
      window.location.href = `${articleDetailSlug}?faq_article_id=${(item as CategoryArticle).value}&faq_base_id=${(item as CategoryArticle).parentId}`;
    } else {
      window.location.href = `${router.query.slug}?faq_category_id=${faq_category_id}&faq_sub_category_id=${item.id}&element_id=${element_id}`;
    }
  };

  return (
    <CategoryListElementUI
      selectedCategory={faq_sub_category_id ? selectedSubCategory : (selectedCategory as any)}
      type={type}
      padding={padding}
      contentPadding={contentPadding}
      backgroundColor={backgroundColor}
      showDescription={showDescription}
      showPicture={showPicture}
      textColor={textColor}
      iconColor={iconColor}
      containerBorderColor={containerBorderColor}
      contentBackgroundColor={contentBackgroundColor}
      onClickArticle={handleClick}
    />
  );
};

export default CategoryListElement;
