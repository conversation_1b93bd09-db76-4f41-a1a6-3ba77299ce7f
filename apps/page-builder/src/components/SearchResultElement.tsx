import { searchArticles } from '@/api/site';
import { sendGAEvent } from '@/utils/page-utils';
import { SearchResultElement as SearchResultElementUI } from '@resola-ai/ui/components/PageBuilder/FAQSearchResultElement';
// @ts-ignore
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

const SearchResultElement = (props: Record<string, any>) => {
  const {
    categoryType,
    maxWidth,
    backgroundColor,
    illustrationType,
    iconSize,
    iconColor,
    iconBgColor,
    hasMaxWidth,
    articleDetailSlug,
    borderColor,
    padding,
    itemBackgroundColor,
    siteId,
    textColor,
  } = props;

  const searchParams = useSearchParams();
  const faqQuery = searchParams.get('faq_query');
  const elementId = searchParams.get('element_id');

  const [searchResults, setSearchResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!faqQuery) {
      setSearchResults([]);
      return;
    }

    setIsLoading(true);

    const performSearch = async () => {
      try {
        // Use siteId from props, fallback to environment variable
        const targetSiteId = siteId || process.env.NEXT_PUBLIC_SITE_ID;

        if (!targetSiteId) {
          console.error('No siteId available for search');
          setSearchResults([]);
          return;
        }

        const data = await searchArticles({
          query: faqQuery,
          site_id: targetSiteId,
        });

        sendGAEvent('faq_search', { search_term: faqQuery, search_result: data.length });

        const formattedResults = data.map((article: any) => ({
          title: article.title,
          description: article.description,
          faq_article_id: article.id,
          element_id: elementId,
          faq_base_id: article.base_id,
        }));

        setSearchResults(formattedResults);
      } catch (error) {
        console.error('Error performing search:', error);
        setSearchResults([]);
      } finally {
        setIsLoading(false);
      }
    };

    performSearch();
  }, [faqQuery, elementId, siteId]);

  return (
    <SearchResultElementUI
      searchResults={searchResults}
      maxWidth={maxWidth}
      backgroundColor={backgroundColor}
      categoryType={categoryType}
      illustrationType={illustrationType}
      iconSize={iconSize}
      iconColor={iconColor}
      iconBgColor={iconBgColor}
      hasMaxWidth={hasMaxWidth}
      articleDetailSlug={articleDetailSlug}
      isLoading={isLoading}
      borderColor={borderColor}
      padding={padding}
      itemBackgroundColor={itemBackgroundColor}
      textColor={textColor}
    />
  );
};

export default SearchResultElement;
