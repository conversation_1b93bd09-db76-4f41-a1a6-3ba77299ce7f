import { getSiteData, searchArticles } from '@/api/site';
import { ArticleDisplayModes } from '@/types/enum';
import { useEffect, useMemo, useRef } from 'react';
import useSWRMutation from 'swr/mutation';
const THRESHOLD = 100;
const ARTICLES_DATA_TYPE = 'faq_article_list';

const useMostViewedArticles = (maxArticles = 5) => {
  const { data, trigger, isMutating } = useSWRMutation('integrations/kb/search', async () => {
    const data = await searchArticles({
      sort: 'view_count',
      take: maxArticles,
      threshold: THRESHOLD,
    });
    if (data) {
      return data.map((article: any) => ({
        label: article.title,
        content: article.content,
        contentRaw: article.content_raw,
        value: article.id,
        type: 'article',
      }));
    }
    return [];
  });

  return {
    data,
    isMutating,
    trigger,
  };
};

const useLatestArticles = (siteId, elementId = '') => {
  const isCallingRef = useRef(false);

  const { data, trigger, isMutating } = useSWRMutation(
    siteId ? `/sites/${siteId}` : null,
    async () => {
      if (isCallingRef.current) {
        return;
      }
      isCallingRef.current = true;
      const response = await getSiteData(siteId, ARTICLES_DATA_TYPE);
      return response?.find((item) => item.elementId === elementId)?.selectedArticles || [];
    }
  );

  // Create a safe trigger function to avoid multiple calls
  const safeTrigger = async () => {
    if (isCallingRef.current || isMutating) {
      return;
    }
    await trigger();
  };

  return {
    data,
    trigger: safeTrigger,
    isFetching: isMutating || data === undefined,
  };
};

export const useConditionalArticles = (
  selectedArticles,
  displayMode,
  maximumArticles,
  elementId,
  siteId
) => {
  const { data, trigger, isMutating } = useMostViewedArticles(maximumArticles);
  const {
    data: latestArticles,
    trigger: triggerLatestArticles,
    isFetching: isFetchingLatestArticles,
  } = useLatestArticles(siteId, elementId);

  // Auto-trigger fetch data when displayMode is changed
  useEffect(() => {
    if (displayMode === ArticleDisplayModes.MostViewed) {
      trigger();
    }
    if (displayMode === ArticleDisplayModes.All) {
      triggerLatestArticles();
    }
  }, [displayMode, maximumArticles]);

  // Determine which articles to show based on display mode
  // For All display mode, we use the latest articles which is fetched from the site data endpoint
  const articles = useMemo(() => {
    if (displayMode === ArticleDisplayModes.MostViewed) {
      return data;
    }
    if (displayMode === ArticleDisplayModes.All) {
      return latestArticles;
    }
    return selectedArticles;
  }, [displayMode, data, latestArticles, selectedArticles]);

  return {
    articles,
    isFetching: isMutating || isFetchingLatestArticles,
  };
};
