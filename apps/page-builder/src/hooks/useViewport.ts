import {
  getGlobalBreakpoints,
  subscribeToBreakpointChanges,
} from '@resola-ai/ui/utils/pageBuilder';
import { useEffect, useState } from 'react';

export type ViewportSize = 'mobile' | 'tablet' | 'desktop';

/**
 * Hook that returns the current viewport size based on global breakpoints
 * @returns Current viewport size: 'mobile', 'tablet', or 'desktop'
 */
export function useViewport(): ViewportSize {
  const [viewport, setViewport] = useState<ViewportSize>(() => {
    // Initial calculation on mount
    if (typeof window === 'undefined') return 'desktop'; // SSR fallback

    const breakpoints = getGlobalBreakpoints();
    const width = window.innerWidth;
    if (width >= breakpoints.desktop) return 'desktop';
    if (width >= breakpoints.tablet) return 'tablet';
    return 'mobile';
  });

  useEffect(() => {
    function calculateViewport() {
      const breakpoints = getGlobalBreakpoints();
      const width = window.innerWidth;

      let newViewport: ViewportSize;
      if (width >= breakpoints.desktop) {
        newViewport = 'desktop';
      } else if (width >= breakpoints.tablet) {
        newViewport = 'tablet';
      } else {
        newViewport = 'mobile';
      }

      setViewport((current) => (current !== newViewport ? newViewport : current));
    }

    // Listen for window resize
    window.addEventListener('resize', calculateViewport);

    // Listen for breakpoint changes
    const unsubscribeBreakpoints = subscribeToBreakpointChanges(calculateViewport);

    // Calculate initial viewport on mount
    calculateViewport();

    return () => {
      window.removeEventListener('resize', calculateViewport);
      unsubscribeBreakpoints();
    };
  }, []);

  return viewport;
}
