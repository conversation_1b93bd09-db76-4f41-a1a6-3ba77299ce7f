import { useEffect, useRef } from 'react';

/**
 * Auto-height hook for embedded React/Next.js apps
 * Automatically measures and sends height updates to parent frame
 */
export function useAutoHeight(enabled = true) {
  const lastHeightRef = useRef<number>(0);
  const isEmbeddedRef = useRef<boolean>(false);

  useEffect(() => {
    // Check if we're in an iframe
    isEmbeddedRef.current = window.self !== window.top;

    if (!enabled || !isEmbeddedRef.current) return;

    /**
     * Measures the current page height
     */
    const measureHeight = (): number => {
      return Math.max(
        document.body.scrollHeight,
        document.body.offsetHeight,
        document.documentElement.clientHeight,
        document.documentElement.scrollHeight,
        document.documentElement.offsetHeight
      );
    };

    /**
     * Sends height update to parent frame
     */
    const sendHeightUpdate = (height: number): void => {
      try {
        window.parent.postMessage(
          {
            type: 'deca-iframe-height-change',
            height: height,
          },
          '*'
        );
      } catch (e) {
        // Ignore errors - parent might not be listening
      }
    };

    /**
     * Checks if height has changed and sends update if needed
     */
    const checkAndUpdateHeight = (): void => {
      const currentHeight = measureHeight();
      if (currentHeight !== lastHeightRef.current) {
        lastHeightRef.current = currentHeight;
        sendHeightUpdate(currentHeight);
      }
    };

    // Send initial height
    const initialTimer = setTimeout(checkAndUpdateHeight, 100);

    // Set up height monitoring
    const cleanupFunctions: (() => void)[] = [];

    // Monitor for height changes using ResizeObserver if available
    if (window.ResizeObserver) {
      const resizeObserver = new ResizeObserver(() => {
        checkAndUpdateHeight();
      });
      resizeObserver.observe(document.body);
      resizeObserver.observe(document.documentElement);

      cleanupFunctions.push(() => resizeObserver.disconnect());
    } else {
      // Fallback to periodic checking
      const interval = setInterval(checkAndUpdateHeight, 250);
      cleanupFunctions.push(() => clearInterval(interval));
    }

    // Event listeners for specific events that might change height
    const events = ['load', 'resize'] as const;
    events.forEach((event) => {
      window.addEventListener(event, checkAndUpdateHeight);
      cleanupFunctions.push(() => window.removeEventListener(event, checkAndUpdateHeight));
    });

    // Monitor for DOM changes
    if (window.MutationObserver) {
      let timeout: NodeJS.Timeout;
      const mutationObserver = new MutationObserver(() => {
        // Debounce the height check
        clearTimeout(timeout);
        timeout = setTimeout(checkAndUpdateHeight, 50);
      });

      mutationObserver.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'class'],
      });

      cleanupFunctions.push(() => {
        clearTimeout(timeout);
        mutationObserver.disconnect();
      });
    }

    // Cleanup function
    return () => {
      clearTimeout(initialTimer);
      cleanupFunctions.forEach((cleanup) => cleanup());
    };
  }, [enabled]);

  return {
    isEmbedded: isEmbeddedRef.current,
  };
}
