import { getArticleDetail } from '@/api/site';
import useSWR from 'swr';

export const useArticleDetail = (baseId: string, articleId: string) => {
  const { data, isValidating } = useSWR(
    baseId && articleId
      ? [`/integrations/kb/bases/${baseId}/articles/${articleId}`, 'article_detail']
      : null,
    () => getArticleDetail(baseId, articleId),
    {
      revalidateOnFocus: false,
    }
  );

  return {
    article: data,
    isValidating,
  };
};
