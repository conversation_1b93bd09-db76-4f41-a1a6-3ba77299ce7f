export const camelToSnake = (obj) => {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }

  const newObj = Array.isArray(obj) ? [] : {};

  for (const key in obj) {
    const newKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
    newObj[newKey] = camelToSnake(obj[key]);
  }

  return newObj;
};

export const snakeToCamel = (obj) => {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }

  const newObj = Array.isArray(obj) ? [] : {};

  for (const key in obj) {
    const newKey = key.replace(/_([a-z])/g, (_, char) => char.toUpperCase());
    newObj[newKey] = snakeToCamel(obj[key]);
  }

  return newObj;
};

export const removeNullProps = (obj: Record<string, any> | undefined) => {
  if (!obj) return obj;

  const clean = (obj) => {
    if (obj?.props) {
      Object.keys(obj.props).forEach((key) => {
        const val = obj.props[key];
        if (
          val === null ||
          val === undefined ||
          (Array.isArray(val) && val.length === 0) ||
          (typeof val === 'object' && !Array.isArray(val) && Object.keys(val).length === 0)
        ) {
          delete obj.props[key];
        }
      });
    }
  };

  Object.values(obj).forEach((val) => {
    if (typeof val === 'object' && val !== null) {
      clean(val);
    }
  });
  clean(obj);
  return obj;
};
