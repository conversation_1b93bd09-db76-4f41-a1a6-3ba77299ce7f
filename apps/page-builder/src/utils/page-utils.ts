import { getPage, getPages } from '@/api/page';
import { getSite, getSiteSetting } from '@/api/site';
import { getTemplate, getTemplateMetadata, getTemplatePages } from '@/api/template';
import {
  ArticleDetailElement,
  BreadcrumbElement,
  CategoryListElement,
  CategoryTreeElement,
  ContainerWrapper,
  FAQCategoryWrapper,
  SearchElement,
} from '@/components';
import SearchResultElement from '@/components/SearchResultElement';
import Section from '@/components/Section';
import { LinkDestinationType } from '@/types/enum';
import { type Integration, IntegrationType } from '@/types/intergration';
import type { Link } from '@/types/site';
import { removeNullProps, snakeToCamel } from '@/utils/data-transform';
import {
  ButtonElement,
  ContainerElement,
  DividerElement,
  MapElement,
  MediaElement,
  TextElement,
} from '@resola-ai/ui/components/PageBuilder';
import { ButtonVariantType } from '@resola-ai/ui/types/pageBuilder';
import dynamic from 'next/dynamic';

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    PAGE_INTEGRATIONS?: {
      is_loaded_ga4?: boolean;
      faq_search?: boolean;
      faq_article_view?: boolean;
    };
  }
}

const BoxyArticlesElement = dynamic(() => import('@/components/BoxyArticlesElement'), {
  ssr: false,
});
const FlatArticlesElement = dynamic(() => import('@/components/FlatArticlesElement'), {
  ssr: false,
});

const buttonVariantMap = {
  [ButtonVariantType.Primary]: 'primary',
  [ButtonVariantType.Secondary]: 'secondary',
  [ButtonVariantType.Text]: 'text',
};

export const componentResolver = {
  TextElement,
  ButtonElement,
  MediaEmbed: MediaElement,
  DividerElement,
  SearchElement,
  ContainerElement,
  ContainerWrapper,
  ArticleDetailEmbed: ArticleDetailElement,
  PictureCategoryElement: FAQCategoryWrapper,
  IllustrationCategoryElement: FAQCategoryWrapper,
  MapEmbed: MapElement,
  CategoryTreeElement,
  CategoryListElement,
  SearchResultElement,
  Section,
  BreadcrumbElement,
  FlatArticlesElement,
  BoxyArticlesElement,
};

const applyBoxColors = (box: any, themeColorsMap: Record<string, string>) => {
  if (!box) return null;

  return {
    ...box,
    backgroundColor: themeColorsMap[box?.backgroundColor] ?? themeColorsMap.background,
    textColor: themeColorsMap[box?.textColor] ?? themeColorsMap.foreground,
  };
};

const PROPS_USING_THEME_COLOR = [
  'color',
  'borderColor',
  'buttonColor',
  'textColor',
  'backgroundColor',
  'accentColor',
  'iconColor',
  'illustrationBackgroundColor',
  'dividerColor',
  'iconBgColor',
];

const applyThemeColors = (themeColorsMap: Record<string, string>, props: Record<string, any>) => {
  Object.keys(props).forEach((key) => {
    if (props[key] && PROPS_USING_THEME_COLOR.includes(key)) {
      if (typeof props[key] === 'object' && 'desktop' in props[key]) {
        props[key] = {
          desktop: themeColorsMap[props[key].desktop] ?? props[key].desktop,
          tablet: themeColorsMap[props[key].tablet] ?? props[key].tablet,
          mobile: themeColorsMap[props[key].mobile] ?? props[key].mobile,
        };
      } else {
        props[key] = themeColorsMap[props[key]] ?? props[key];
      }
    }
  });

  return props;
};

const getLink = (linkData: Link, pages: Record<string, any>[]) => {
  if (linkData.destination === LinkDestinationType.Page) {
    const _pageUrl = pages.find((p) => p.logical_id === linkData.targetId)?.url;
    return { ...linkData, url: getSlug(_pageUrl) };
  }
  return linkData;
};

const getSlug = (slug: string) => slug;
export const sendGAEvent = (eventName: string, data: Record<string, any>) => {
  try {
    const integration = window?.PAGE_INTEGRATIONS;
    if (!integration?.is_loaded_ga4 || !window.gtag) {
      return;
    }

    const isEventEnabled =
      (eventName === 'faq_search' && integration.faq_search) ||
      (eventName === 'faq_article_view' && integration.faq_article_view);

    if (isEventEnabled) {
      window.gtag('event', eventName, data);
    }
  } catch (error) {
    console.warn('Failed to send GA event:', error);
  }
};

export const enhanceComponentProps = ({
  key,
  component,
  pages,
  siteSetting,
  siteId,
}: {
  key: string;
  component: Record<string, any>;
  pages: Record<string, any>[];
  siteSetting: Record<string, any>;
  siteId: string;
}) => {
  const themeColorsMap = siteSetting?.theme?.content?.colors?.reduce((acc, color) => {
    acc[color.name] = color.color;
    return acc;
  }, {});
  component.props = applyThemeColors(themeColorsMap, component.props);

  switch (component.type?.resolvedName) {
    case 'SearchElement':
      return {
        ...component,
        props: {
          ...component.props,
          elementId: key,
          searchResultPageSlug: getSlug(pages.find((p) => p.type === 'faq_search_result')?.url),
          articleDetailSlug: getSlug(pages.find((p) => p.type === 'faq_article_detail')?.url),
          borderColor: component.props.borderColor || themeColorsMap.border,
          backgroundColor: component.props.backgroundColor || themeColorsMap.background,
          textColor: component.props.textColor || themeColorsMap.foreground,
          buttonColor: component.props.buttonColor || themeColorsMap.accent,
          placeholderColor: themeColorsMap.text,
          siteId: siteId,
        },
      };
    case 'SearchResultElement':
      return {
        ...component,
        props: {
          ...component.props,
          elementId: key,
          siteId: siteId,
          articleDetailSlug: getSlug(pages.find((p) => p.type === 'faq_article_detail')?.url),
          borderColor: component.props.borderColor || themeColorsMap.border,
          backgroundColor: component.props.backgroundColor || themeColorsMap.background,
          iconColor: component.props.iconColor || themeColorsMap.foreground,
          iconBgColor: component.props.iconBgColor || themeColorsMap.background,
          categoryType: component.props.categoryType || 'box',
          illustrationType: component.props.illustrationType || 'icon',
          textColor: themeColorsMap.foreground,
          itemBackgroundColor: themeColorsMap.background,
        },
      };
    case 'PictureCategoryElement':
    case 'IllustrationCategoryElement':
      return {
        ...component,
        props: {
          ...component.props,
          siteId: siteId,
          elementId: key,
          url: getSlug(pages.find((p) => p.type === 'faq_category_list')?.url),
          titleColor: themeColorsMap.foreground,
          descriptionColor: themeColorsMap.text,
        },
      };
    case 'ArticleDetailEmbed':
      return {
        ...component,
        props: {
          ...component.props,
          elementId: key,
          textColor: themeColorsMap.foreground,
          dividerColor: themeColorsMap.border,
          contentBackgroundColor:
            component.props.contentBackgroundColor || themeColorsMap.background,
          borderColor: themeColorsMap.border,
          fontFamily: siteSetting?.theme?.content?.typography?.body?.font_family,
        },
      };
    case 'CategoryTreeElement':
      return {
        ...component,
        props: {
          ...component.props,
          siteId: siteId,
          articleDetailSlug: getSlug(pages.find((p) => p.type === 'faq_article_detail')?.url),
          categoryListSlug: getSlug(pages.find((p) => p.type === 'faq_category_list')?.url),
          styles: {
            root: {
              backgroundColor: component.props.backgroundColor || themeColorsMap.background,
            },
            item: {
              borderColor: `${component.props.dividerColor || themeColorsMap.border} !important`,
            },
            chevron: {
              color: themeColorsMap.text,
            },
            label: {
              color: `${themeColorsMap.foreground}`,
            },
            control: {
              '&:hover': {
                backgroundColor: `${themeColorsMap.secondary} !important`,
              },
            },
          },
        },
      };
    case 'CategoryListElement':
      return {
        ...component,
        props: {
          ...component.props,
          siteId: siteId,
          articleDetailSlug: getSlug(pages.find((p) => p.type === 'faq_article_detail')?.url),
          textColor: themeColorsMap.foreground,
          iconColor: themeColorsMap.text,
          containerBorderColor: themeColorsMap.border,
          contentBackgroundColor: themeColorsMap.background,
        },
      };
    case 'BreadcrumbElement':
      return {
        ...component,
        props: {
          ...component.props,
          siteId: siteId,
          articleDetailSlug: getSlug(pages.find((p) => p.type === 'faq_article_detail')?.url),
          categoryListSlug: getSlug(pages.find((p) => p.type === 'faq_category_list')?.url),
          borderColor: themeColorsMap?.border,
          textColor: themeColorsMap.foreground,
        },
      };
    case 'ButtonElement': {
      const buttonSettings = siteSetting?.theme?.content?.button;
      const buttonColors = buttonSettings[buttonVariantMap[component.props.variant]];
      return {
        ...component,
        props: {
          ...component.props,
          link: component.props.link ? getLink(component.props.link, pages) : null,
          color: component.props.color || themeColorsMap[buttonColors?.background_color],
          textColor: component.props.textColor || themeColorsMap[buttonColors?.text_color],
          borderColor: themeColorsMap[buttonColors?.border_color],
          radius: buttonSettings.border_radius,
        },
      };
    }
    case 'TextElement':
      return {
        ...component,
        props: {
          ...component.props,
          link: component.props.link ? getLink(component.props.link, pages) : null,
          typographySettings: siteSetting?.theme?.content?.typography,
          color: component.props.color ?? themeColorsMap.foreground,
        },
      };
    case 'MediaEmbed':
      return {
        ...component,
        props: {
          ...component.props,
          link: component.props.link ? getLink(component.props.link, pages) : null,
        },
      };
    case 'BoxyArticlesElement':
    case 'FlatArticlesElement':
      return {
        ...component,
        props: {
          ...component.props,
          siteId: siteId,
          elementId: key,
          answerBox: applyBoxColors(component.props.answerBox, themeColorsMap),
          questionBox: applyBoxColors(component.props.questionBox, themeColorsMap),
        },
      };
    case 'DividerElement':
      return {
        ...component,
        props: {
          ...component.props,
          color: component.props.color ?? themeColorsMap.border,
        },
      };
    case 'ContainerElement':
      return {
        ...component,
        props: {
          ...component.props,
          link: component.props.link ? getLink(component.props.link, pages) : null,
        },
      };
    default:
      return component;
  }
};

export const processTemplateData = async (templateId: string, slug: string) => {
  const [pageMetadata, page, pages] = await Promise.all([
    getTemplateMetadata(templateId),
    getTemplate(templateId, slug),
    getTemplatePages(templateId),
  ]);

  const siteSetting = { theme: pageMetadata.theme };

  return {
    site: pageMetadata,
    pages,
    siteSetting,
    page: {
      ...page,
      content: removeNullProps(
        Object.fromEntries(
          Object.entries(page.content).map(([key, value]) => {
            const camelValue = snakeToCamel(value);
            return [
              key,
              enhanceComponentProps({
                key,
                component: camelValue,
                pages,
                siteSetting,
                siteId: templateId,
              }),
            ];
          })
        )
      ),
    },
  };
};

export const processPageData = async (siteId: string, slug: string, versionId?: string) => {
  const [page, pages, site, siteSetting] = await Promise.all([
    getPage(siteId, slug, versionId),
    getPages(siteId, versionId),
    getSite(siteId, versionId),
    getSiteSetting(siteId, versionId),
  ]);

  if (!page) return null;

  return {
    site,
    siteSetting,
    pages: pages,
    page: {
      ...page,
      content: removeNullProps(
        Object.fromEntries(
          Object.entries(page.content).map(([key, value]) => {
            const camelValue = snakeToCamel(value);
            return [
              key,
              enhanceComponentProps({
                key,
                component: camelValue,
                pages,
                siteSetting,
                siteId,
              }),
            ];
          })
        )
      ),
    },
  };
};

export const getChatwindowIntegrationId = (
  integrations: Integration[],
  page: Record<string, any>
) => {
  try {
    const chatwindowIntegration = integrations.find(
      (integration) => integration.type === IntegrationType.DECA_CHATWINDOW
    );
    if (chatwindowIntegration) {
      const { is_enabled, options, resource_id } = chatwindowIntegration;
      // if not enabled, return empty string
      if (!is_enabled) return '';

      const pageScope = options?.page_scope || 'all_pages';
      if (pageScope === 'all_pages') {
        return resource_id;
      }
      const pageIds = options?.page_ids;
      if (pageIds?.includes(page?.logical_id)) {
        return resource_id;
      }
    }

    return '';
  } catch (error) {
    return '';
  }
};

export const getGoogleAnalyticsIntergration = (integrations: Integration[]) => {
  const googleAnalyticsIntegration = integrations.find(
    (integration) => integration.type === IntegrationType.GOOGLE_ANALYTICS
  );
  if (googleAnalyticsIntegration) {
    const { is_enabled } = googleAnalyticsIntegration;
    if (!is_enabled) return null;

    return googleAnalyticsIntegration;
  }
  return null;
};

export const findNestedCategoryOrArticleById = (
  categoriesWithElementIdData = [],
  categoryId = '',
  subCategoryId = '',
  articleId = '',
  elementId = ''
) => {
  let foundCategories;
  let foundCategory;
  let foundSubCategory;
  let foundArticle;
  let isFoundFirstMatch = false;

  if (!Array.isArray(categoriesWithElementIdData))
    return { categories: [], category: null, subCategory: null, article: null };

  (categoriesWithElementIdData as any)?.forEach((elementCategories) => {
    if (isFoundFirstMatch) return;

    elementCategories?.categories?.forEach((category) => {
      if (isFoundFirstMatch) return;

      if (categoryId && !subCategoryId && !articleId) {
        if (category.id === categoryId) {
          foundCategory = category;
          foundCategories = elementCategories;
          isFoundFirstMatch = true;
        }
      } else {
        if (subCategoryId && !articleId) {
          if (category?.subType === 'category') {
            category?.data?.forEach((subCategory) => {
              if (isFoundFirstMatch) return;

              if (subCategory?.id === subCategoryId) {
                foundSubCategory = subCategory;
                foundCategory = category;
                foundCategories = elementCategories;
                isFoundFirstMatch = true;
              }
            });
          }
        } else {
          if (category?.subType === 'category') {
            category?.data?.forEach((subCategory) => {
              if (isFoundFirstMatch) return;

              subCategory?.data?.forEach((article) => {
                if (isFoundFirstMatch) return;

                if (article?.value === articleId) {
                  foundArticle = article;
                  foundSubCategory = subCategory;
                  foundCategory = category;
                  foundCategories = elementCategories;
                  isFoundFirstMatch = true;
                }
              });
            });
          } else {
            category?.data?.forEach((article) => {
              if (isFoundFirstMatch) return;

              if (article?.value === articleId) {
                foundArticle = article;
                foundCategory = category;
                foundCategories = elementCategories;
                isFoundFirstMatch = true;
              }
            });
          }
        }
      }
    });
  });

  const matchedByElementIdCategories = elementId
    ? (categoriesWithElementIdData as any)?.find((category) => category.elementId === elementId)
    : null;
  const matchedByElementIdCategory =
    matchedByElementIdCategories && categoryId
      ? (matchedByElementIdCategories as any)?.categories?.find(
          (category) => category?.id === categoryId
        )
      : null;
  const matchedByElementIdSubCategory =
    matchedByElementIdCategory && subCategoryId
      ? (matchedByElementIdCategory as any)?.data?.find(
          (subCategory) => subCategory.id === subCategoryId
        )
      : null;
  const matchedByElementIdArticle =
    (matchedByElementIdCategory || matchedByElementIdSubCategory) && articleId
      ? (matchedByElementIdSubCategory as any)?.data?.find(
          (article) => article?.value === articleId
        ) ||
        (matchedByElementIdCategory?.subType === 'article'
          ? (matchedByElementIdCategory as any)?.data?.find(
              (article) => article?.value === articleId
            )
          : null)
      : null;

  const isMatchedByElementId =
    elementId &&
    matchedByElementIdCategories &&
    (!categoryId || matchedByElementIdCategory) &&
    (!subCategoryId || matchedByElementIdSubCategory) &&
    (!articleId || matchedByElementIdArticle);

  if (isMatchedByElementId) {
    return {
      categories: matchedByElementIdCategories,
      category: matchedByElementIdCategory,
      subCategory: matchedByElementIdSubCategory,
      article: matchedByElementIdArticle,
    };
  }
  return {
    categories: foundCategories || categoriesWithElementIdData?.[0],
    category: foundCategory,
    subCategory: foundSubCategory,
    article: foundArticle,
  };
};
