import { searchArticles } from '@/api/site';
import { useConditionalArticles } from '@/hooks/useConditionalArticles';
import { ArticleDisplayModes } from '@/types/enum';
import { renderHook, waitFor } from '@testing-library/react';
import useSWRMutation from 'swr/mutation';

// Mock SWR
jest.mock('swr/mutation');
const mockUseSWRMutation = useSWRMutation as jest.MockedFunction<typeof useSWRMutation>;

// Mock searchArticles API
jest.mock('@/api/site', () => ({
  searchArticles: jest.fn(),
  getSiteData: jest.fn(),
}));
const mockSearchArticles = searchArticles as jest.MockedFunction<typeof searchArticles>;
const mockGetSiteData = require('@/api/site').getSiteData as jest.MockedFunction<any>;

describe('useConditionalArticles', () => {
  const mockTriggerMostViewed = jest.fn();
  const mockTriggerLatest = jest.fn();

  const mockMostViewedSWRReturn = {
    data: undefined,
    trigger: mockTriggerMostViewed,
    isMutating: false,
    reset: jest.fn(),
    error: undefined,
  };

  const mockLatestSWRReturn = {
    data: undefined,
    trigger: mockTriggerLatest,
    isMutating: false,
    reset: jest.fn(),
    error: undefined,
  };

  const setupMocks = (
    mostViewedData?: any,
    latestData?: any,
    mostViewedMutating = false,
    latestMutating = false
  ) => {
    // Calculate isFetching for latest articles based on the actual hook logic
    const latestIsFetching = latestMutating || latestData === undefined;

    mockUseSWRMutation
      .mockReturnValueOnce({
        data: mostViewedData,
        trigger: mockTriggerMostViewed,
        isMutating: mostViewedMutating,
        reset: jest.fn(),
        error: undefined,
      })
      .mockReturnValueOnce({
        data: latestData,
        trigger: mockTriggerLatest,
        isMutating: latestMutating,
        reset: jest.fn(),
        error: undefined,
      });
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockGetSiteData.mockResolvedValue([]);
    mockSearchArticles.mockResolvedValue([]);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('with ArticleDisplayModes.All', () => {
    it('should return latestArticles when display mode is All', () => {
      const selectedArticles = [
        { id: '1', title: 'Selected Article 1', content: 'Content 1' },
        { id: '2', title: 'Selected Article 2', content: 'Content 2' },
      ];

      const latestArticles = [
        { id: '3', title: 'Latest Article 1', content: 'Latest Content 1' },
        { id: '4', title: 'Latest Article 2', content: 'Latest Content 2' },
      ];

      setupMocks(undefined, latestArticles);

      const { result } = renderHook(() =>
        useConditionalArticles(selectedArticles, ArticleDisplayModes.All, 5, 'element1', 'site1')
      );

      expect(result.current.articles).toEqual(latestArticles);
      expect(result.current.isFetching).toBe(false);
    });

    it('should trigger latest articles API call for All mode', () => {
      const selectedArticles = [{ id: '1', title: 'Article 1', content: 'Content 1' }];

      setupMocks(undefined, []);

      renderHook(() =>
        useConditionalArticles(selectedArticles, ArticleDisplayModes.All, 3, 'element1', 'site1')
      );

      expect(mockTriggerLatest).toHaveBeenCalled();
      expect(mockTriggerMostViewed).not.toHaveBeenCalled();
    });
  });

  describe('with ArticleDisplayModes.MostViewed', () => {
    it('should trigger API call when display mode is MostViewed', async () => {
      setupMocks([], undefined);

      const { result } = renderHook(() =>
        useConditionalArticles([], ArticleDisplayModes.MostViewed, 5, '', '')
      );

      await waitFor(() => {
        expect(mockTriggerMostViewed).toHaveBeenCalledTimes(1);
      });

      expect(mockTriggerLatest).not.toHaveBeenCalled();
      // isFetching should be true because latestData is undefined (isFetching = isMutating || data === undefined)
      expect(result.current.isFetching).toBe(true);
    });

    it('should return API data when display mode is MostViewed', () => {
      const mockApiData = [
        { label: 'Most Viewed 1', content: 'Content 1', value: '1', type: 'article' },
        { label: 'Most Viewed 2', content: 'Content 2', value: '2', type: 'article' },
      ];

      setupMocks(mockApiData, undefined);

      const { result } = renderHook(() =>
        useConditionalArticles([], ArticleDisplayModes.MostViewed, 3, '', '')
      );

      expect(result.current.articles).toEqual(mockApiData);
    });

    it('should handle isMutating state correctly', () => {
      setupMocks(undefined, undefined, true, false);

      const { result } = renderHook(() =>
        useConditionalArticles([], ArticleDisplayModes.MostViewed, 5, '', '')
      );

      expect(result.current.isFetching).toBe(true);
    });

    it('should re-trigger when maximumArticles changes', async () => {
      setupMocks([], undefined);

      const { rerender } = renderHook(
        ({ maxArticles }) =>
          useConditionalArticles([], ArticleDisplayModes.MostViewed, maxArticles, '', ''),
        { initialProps: { maxArticles: 3 } }
      );

      await waitFor(() => {
        expect(mockTriggerMostViewed).toHaveBeenCalledTimes(1);
      });

      // Change maximumArticles - need to setup mocks again for rerender
      setupMocks([], undefined);
      rerender({ maxArticles: 5 });

      await waitFor(() => {
        expect(mockTriggerMostViewed).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('useMostViewedArticles integration', () => {
    it('should call useSWRMutation with correct key', () => {
      setupMocks([], undefined);

      renderHook(() => useConditionalArticles([], ArticleDisplayModes.MostViewed, 5, '', ''));

      expect(mockUseSWRMutation).toHaveBeenCalledWith(
        'integrations/kb/search',
        expect.any(Function)
      );
    });

    it('should call searchArticles with correct parameters when triggered', async () => {
      const mockData = [
        { id: '1', title: 'Article 1', content: 'Content 1', content_raw: 'Raw 1' },
        { id: '2', title: 'Article 2', content: 'Content 2', content_raw: 'Raw 2' },
      ];

      mockSearchArticles.mockResolvedValue(mockData);
      setupMocks([], undefined);

      renderHook(() => useConditionalArticles([], ArticleDisplayModes.MostViewed, 3, '', ''));

      // We need to get the actual function passed to useSWRMutation
      const swrFn = mockUseSWRMutation.mock.calls[0][1];

      // Call the SWR function directly to test it
      const result = await (swrFn as any)();

      expect(mockSearchArticles).toHaveBeenCalledWith({
        sort: 'view_count',
        take: 3,
        threshold: 100,
      });

      expect(result).toEqual([
        {
          label: 'Article 1',
          content: 'Content 1',
          contentRaw: 'Raw 1',
          value: '1',
          type: 'article',
        },
        {
          label: 'Article 2',
          content: 'Content 2',
          contentRaw: 'Raw 2',
          value: '2',
          type: 'article',
        },
      ]);
    });

    it('should handle empty API response', async () => {
      mockSearchArticles.mockResolvedValue(null);
      setupMocks([], undefined);

      renderHook(() => useConditionalArticles([], ArticleDisplayModes.MostViewed, 5, '', ''));

      const swrFn = mockUseSWRMutation.mock.calls[0][1];

      const result = await (swrFn as any)();

      expect(result).toEqual([]);
    });

    it('should handle API errors gracefully', async () => {
      mockSearchArticles.mockRejectedValue(new Error('API Error'));
      setupMocks([], undefined);

      renderHook(() => useConditionalArticles([], ArticleDisplayModes.MostViewed, 5, '', ''));

      const swrFn = mockUseSWRMutation.mock.calls[0][1];

      await expect((swrFn as any)()).rejects.toThrow('API Error');
    });
  });

  describe('display mode switching', () => {
    it('should switch between latest articles and most viewed data based on display mode', () => {
      const selectedArticles = [
        { id: 'selected1', title: 'Selected Article', content: 'Selected Content' },
      ];

      const latestArticles = [
        { id: 'latest1', title: 'Latest Article', content: 'Latest Content' },
      ];

      const mostViewedData = [
        {
          label: 'Most Viewed Article',
          content: 'Most Viewed Content',
          value: 'mv1',
          type: 'article',
        },
      ];

      // First render with All mode
      setupMocks(undefined, latestArticles);

      const { result, rerender } = renderHook(
        ({ displayMode }) =>
          useConditionalArticles(selectedArticles, displayMode, 5, 'element1', 'site1'),
        { initialProps: { displayMode: ArticleDisplayModes.All } }
      );

      // Initially should show latest articles (not selected articles)
      expect(result.current.articles).toEqual(latestArticles);

      // Switch to MostViewed mode - need to mock again for rerender
      setupMocks(mostViewedData, undefined);

      rerender({ displayMode: ArticleDisplayModes.MostViewed });

      expect(result.current.articles).toEqual(mostViewedData);
    });

    it('should return undefined when no data is available in MostViewed mode', () => {
      setupMocks(undefined, undefined);

      const { result } = renderHook(() =>
        useConditionalArticles([], ArticleDisplayModes.MostViewed, 5, '', '')
      );

      expect(result.current.articles).toBeUndefined();
    });
  });

  describe('edge cases', () => {
    it('should handle zero maxArticles', async () => {
      mockSearchArticles.mockResolvedValue([]);
      setupMocks([], undefined);

      renderHook(() => useConditionalArticles([], ArticleDisplayModes.MostViewed, 0, '', ''));

      const swrFn = mockUseSWRMutation.mock.calls[0][1];

      await (swrFn as any)();

      expect(mockSearchArticles).toHaveBeenCalledWith({
        sort: 'view_count',
        take: 0,
        threshold: 100,
      });
    });

    it('should handle negative maxArticles', async () => {
      mockSearchArticles.mockResolvedValue([]);
      setupMocks([], undefined);

      renderHook(() => useConditionalArticles([], ArticleDisplayModes.MostViewed, -1, '', ''));

      const swrFn = mockUseSWRMutation.mock.calls[0][1];

      await (swrFn as any)();

      expect(mockSearchArticles).toHaveBeenCalledWith({
        sort: 'view_count',
        take: -1,
        threshold: 100,
      });
    });

    it('should handle empty selectedArticles array', () => {
      // For All mode, it should return latestArticles, not selectedArticles
      const emptyLatestArticles = [];

      setupMocks(undefined, emptyLatestArticles);

      const { result } = renderHook(() =>
        useConditionalArticles([], ArticleDisplayModes.All, 5, 'element1', 'site1')
      );

      expect(result.current.articles).toEqual([]);
    });

    it('should handle null selectedArticles with different display mode', () => {
      // Test with a mode that actually returns selectedArticles (neither All nor MostViewed)
      setupMocks(undefined, undefined);

      const { result } = renderHook(() => useConditionalArticles(null, 'custom' as any, 5, '', ''));

      expect(result.current.articles).toBeNull();
    });
  });
});
