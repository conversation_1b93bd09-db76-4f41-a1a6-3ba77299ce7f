import { useUrlBinding } from '@/hooks/useUrlBinding';
import { renderHook } from '@testing-library/react';
import { useRouter } from 'next/router';

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;

describe('useUrlBinding', () => {
  let mockRouterEvents: { on: jest.Mock; off: jest.Mock };
  let mockRouter: any;
  let mockPostMessage: jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock router events
    mockRouterEvents = {
      on: jest.fn(),
      off: jest.fn(),
    };

    mockRouter = {
      events: mockRouterEvents,
      push: jest.fn(),
      replace: jest.fn(),
      query: {},
      pathname: '/test',
      asPath: '/test',
    };

    mockUseRouter.mockReturnValue(mockRouter);

    // Mock postMessage
    mockPostMessage = jest.fn();
    Object.defineProperty(window, 'parent', {
      value: { postMessage: mockPostMessage },
      writable: true,
      configurable: true,
    });

    // Mock window properties for iframe detection
    Object.defineProperty(window, 'self', {
      value: window,
      writable: true,
      configurable: true,
    });
    Object.defineProperty(window, 'top', {
      value: {}, // Different from window.self to simulate iframe
      writable: true,
      configurable: true,
    });

    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: { href: 'https://example.com/test' },
      writable: true,
      configurable: true,
    });

    // Mock event listeners
    jest.spyOn(window, 'addEventListener').mockImplementation();
    jest.spyOn(window, 'removeEventListener').mockImplementation();

    // Mock console.error to reduce noise
    jest.spyOn(console, 'error').mockImplementation();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('iframe detection', () => {
    it('should detect non-iframe environment correctly', () => {
      // Set up non-iframe environment (window.self === window.top)
      Object.defineProperty(window, 'self', { value: window, configurable: true });
      Object.defineProperty(window, 'top', { value: window, configurable: true });

      const { result } = renderHook(() => useUrlBinding());

      expect(result.current.isEmbedded).toBe(false);
    });
  });

  describe('when enabled', () => {
    beforeEach(() => {
      // Ensure iframe environment for these tests
      Object.defineProperty(window, 'self', { value: window, configurable: true });
      Object.defineProperty(window, 'top', { value: {}, configurable: true });
    });

    it('should set up router event listeners when enabled and in iframe', () => {
      renderHook(() => useUrlBinding(true));

      expect(mockRouterEvents.on).toHaveBeenCalledWith('routeChangeComplete', expect.any(Function));
      expect(mockRouterEvents.on).toHaveBeenCalledWith('hashChangeComplete', expect.any(Function));
    });

    it('should send initial URL notification when enabled and in iframe', () => {
      renderHook(() => useUrlBinding(true));

      expect(mockPostMessage).toHaveBeenCalledWith(
        {
          type: 'deca-iframe-url-change',
          url: 'https://example.com/test',
        },
        '*'
      );
    });

    it('should handle postMessage errors gracefully', () => {
      mockPostMessage.mockImplementation(() => {
        throw new Error('PostMessage error');
      });

      expect(() => {
        renderHook(() => useUrlBinding(true));
      }).not.toThrow();

      expect(console.error).toHaveBeenCalledWith(
        'Error notifying parent of URL change:',
        expect.any(Error)
      );
    });
  });

  describe('when disabled', () => {
    it('should not set up URL monitoring when disabled', () => {
      renderHook(() => useUrlBinding(false));

      expect(mockPostMessage).not.toHaveBeenCalled();
      expect(mockRouterEvents.on).not.toHaveBeenCalled();
    });

    it('should still set up message listener when disabled', () => {
      renderHook(() => useUrlBinding(false));

      // Message listener for parent URL should still be set up
      expect(window.addEventListener).toHaveBeenCalledWith('message', expect.any(Function));
    });
  });

  describe('when not in iframe', () => {
    beforeEach(() => {
      // Set up non-iframe environment
      Object.defineProperty(window, 'self', { value: window, configurable: true });
      Object.defineProperty(window, 'top', { value: window, configurable: true });
    });

    it('should not set up URL monitoring when not in iframe', () => {
      renderHook(() => useUrlBinding(true));

      expect(mockPostMessage).not.toHaveBeenCalled();
      expect(mockRouterEvents.on).not.toHaveBeenCalled();
    });

    it('should still set up message listener when not in iframe', () => {
      renderHook(() => useUrlBinding(true));

      expect(window.addEventListener).toHaveBeenCalledWith('message', expect.any(Function));
    });
  });

  describe('cleanup', () => {
    beforeEach(() => {
      // Ensure iframe environment
      Object.defineProperty(window, 'self', { value: window, configurable: true });
      Object.defineProperty(window, 'top', { value: {}, configurable: true });
    });

    it('should cleanup router event listeners on unmount', () => {
      const { unmount } = renderHook(() => useUrlBinding(true));

      unmount();

      expect(mockRouterEvents.off).toHaveBeenCalledWith(
        'routeChangeComplete',
        expect.any(Function)
      );
      expect(mockRouterEvents.off).toHaveBeenCalledWith('hashChangeComplete', expect.any(Function));
    });

    it('should cleanup message listener on unmount', () => {
      const { unmount } = renderHook(() => useUrlBinding(true));

      unmount();

      expect(window.removeEventListener).toHaveBeenCalledWith('message', expect.any(Function));
      expect((window as any).BASE_PARENT_URL).toBe('');
    });

    it('should cleanup when enabled changes', () => {
      const { rerender } = renderHook(({ enabled }) => useUrlBinding(enabled), {
        initialProps: { enabled: true },
      });

      // Change to disabled
      rerender({ enabled: false });

      expect(mockRouterEvents.off).toHaveBeenCalled();
    });
  });

  describe('message handling', () => {
    it('should set up message listener', () => {
      renderHook(() => useUrlBinding(true));

      expect(window.addEventListener).toHaveBeenCalledWith('message', expect.any(Function));
    });

    it('should handle parent URL messages', () => {
      renderHook(() => useUrlBinding(true));

      // Get the message handler
      const addEventListenerCall = (window.addEventListener as jest.Mock).mock.calls.find(
        (call) => call[0] === 'message'
      );
      expect(addEventListenerCall).toBeDefined();

      const messageHandler = addEventListenerCall[1];

      // Simulate receiving parent URL message
      const mockEvent = {
        data: {
          type: 'deca-parent-url',
          url: 'https://parent.com/page',
        },
      };

      messageHandler(mockEvent);

      expect((window as any).BASE_PARENT_URL).toBe('https://parent.com/page');
    });

    it('should ignore non-deca-parent-url messages', () => {
      renderHook(() => useUrlBinding(true));

      const messageHandler = (window.addEventListener as jest.Mock).mock.calls.find(
        (call) => call[0] === 'message'
      )[1];

      const mockEvent = {
        data: {
          type: 'other-message',
          url: 'https://other.com',
        },
      };

      messageHandler(mockEvent);

      expect((window as any).BASE_PARENT_URL).not.toBe('https://other.com');
    });

    it('should handle invalid message data gracefully', () => {
      renderHook(() => useUrlBinding(true));

      const messageHandler = (window.addEventListener as jest.Mock).mock.calls.find(
        (call) => call[0] === 'message'
      )[1];

      // Test various invalid data scenarios
      const invalidEvents = [
        { data: null },
        { data: undefined },
        { data: 'invalid-string' },
        { data: { type: 'wrong-type' } },
        {},
      ];

      invalidEvents.forEach((mockEvent) => {
        expect(() => messageHandler(mockEvent)).not.toThrow();
      });
    });
  });

  describe('router dependency changes', () => {
    beforeEach(() => {
      // Ensure iframe environment
      Object.defineProperty(window, 'self', { value: window, configurable: true });
      Object.defineProperty(window, 'top', { value: {}, configurable: true });
    });

    it('should handle router events dependency changes', () => {
      const newRouterEvents = {
        on: jest.fn(),
        off: jest.fn(),
      };

      const { rerender } = renderHook(() => useUrlBinding(true));

      // Update router events
      mockUseRouter.mockReturnValue({
        ...mockRouter,
        events: newRouterEvents,
      });

      rerender();

      // Should remove old listeners and add new ones
      expect(mockRouterEvents.off).toHaveBeenCalled();
      expect(newRouterEvents.on).toHaveBeenCalled();
    });
  });

  describe('edge cases', () => {
    it('should handle various URL formats', () => {
      const testUrls = [
        'https://example.com/simple',
        'https://example.com/path?query=value&other=123',
        'https://example.com/path#hash',
        'https://example.com/path?query=value#hash',
        'https://sub.example.com/complex/path?a=1&b=2#section',
      ];

      testUrls.forEach((url) => {
        Object.defineProperty(window, 'location', {
          value: { href: url },
          configurable: true,
        });

        // Ensure iframe environment
        Object.defineProperty(window, 'self', { value: window, configurable: true });
        Object.defineProperty(window, 'top', { value: {}, configurable: true });

        mockPostMessage.mockClear();

        renderHook(() => useUrlBinding(true));

        expect(mockPostMessage).toHaveBeenCalledWith(
          {
            type: 'deca-iframe-url-change',
            url: url,
          },
          '*'
        );
      });
    });

    it('should handle missing window properties gracefully', () => {
      // Test with undefined location.href
      Object.defineProperty(window, 'location', {
        value: { href: undefined },
        configurable: true,
      });

      expect(() => {
        renderHook(() => useUrlBinding(true));
      }).not.toThrow();
    });

    it('should detect and notify URL changes via checkUrlChange', () => {
      // Set initial URL
      Object.defineProperty(window, 'location', {
        value: { href: 'http://localhost/initial' },
        configurable: true,
      });

      renderHook(() => useUrlBinding(true));

      // Clear initial call
      mockPostMessage.mockClear();

      // Change URL to trigger checkUrlChange logic (lines 37-40)
      Object.defineProperty(window, 'location', {
        value: { href: 'http://localhost/changed' },
        configurable: true,
      });

      // Trigger a router event to call checkUrlChange
      const routeChangeHandler = mockRouterEvents.on.mock.calls.find(
        (call) => call[0] === 'routeChangeComplete'
      )[1];

      // Act the setTimeout call (line 53)
      jest.useFakeTimers();
      routeChangeHandler();
      jest.advanceTimersByTime(50);

      // Should detect URL change and notify parent
      expect(mockPostMessage).toHaveBeenCalledWith(
        {
          type: 'deca-iframe-url-change',
          url: 'http://localhost/changed',
        },
        '*'
      );

      jest.useRealTimers();
    });

    it('should call checkUrlChange via setTimeout on router events', () => {
      const { rerender } = renderHook(() => useUrlBinding(true));

      // Get the router event handler
      const routeChangeHandler = mockRouterEvents.on.mock.calls.find(
        (call) => call[0] === 'routeChangeComplete'
      )[1];

      // Mock setTimeout to verify line 53 is covered
      const mockSetTimeout = jest.spyOn(global, 'setTimeout');

      // Trigger router event
      routeChangeHandler();

      // Verify setTimeout was called with checkUrlChange and 50ms delay (line 53)
      expect(mockSetTimeout).toHaveBeenCalledWith(expect.any(Function), 50);

      mockSetTimeout.mockRestore();
    });

    it('should handle hash change events with setTimeout', () => {
      renderHook(() => useUrlBinding(true));

      // Get the hash change handler
      const hashChangeHandler = mockRouterEvents.on.mock.calls.find(
        (call) => call[0] === 'hashChangeComplete'
      )[1];

      // Mock setTimeout
      const mockSetTimeout = jest.spyOn(global, 'setTimeout');

      // Trigger hash change event
      hashChangeHandler();

      // Verify setTimeout was called (line 53 coverage)
      expect(mockSetTimeout).toHaveBeenCalledWith(expect.any(Function), 50);

      mockSetTimeout.mockRestore();
    });
  });
});
