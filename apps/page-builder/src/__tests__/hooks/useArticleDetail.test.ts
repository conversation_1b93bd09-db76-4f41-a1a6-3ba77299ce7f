import { getArticleDetail } from '@/api/site';
import { useArticleDetail } from '@/hooks/useArticleDetail';
import { renderHook } from '@testing-library/react';
import useSWR from 'swr';

// Mock dependencies
jest.mock('swr');
jest.mock('@/api/site');

const mockUseSWR = useSWR as jest.MockedFunction<typeof useSWR>;
const mockGetArticleDetail = getArticleDetail as jest.MockedFunction<typeof getArticleDetail>;

describe('useArticleDetail', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return article data and loading state when both baseId and articleId are provided', () => {
    const mockArticle = { id: '1', title: 'Test Article', content: 'Test content' };

    mockUseSWR.mockReturnValue({
      data: mockArticle,
      error: undefined,
      isValidating: false,
      isLoading: false,
      mutate: jest.fn(),
    } as any);

    const { result } = renderHook(() => useArticleDetail('base123', 'article456'));

    expect(mockUseSWR).toHaveBeenCalledWith(
      ['/integrations/kb/bases/base123/articles/article456', 'article_detail'],
      expect.any(Function),
      { revalidateOnFocus: false }
    );
    expect(result.current.article).toBe(mockArticle);
    expect(result.current.isValidating).toBe(false);
  });

  it('should return undefined key when baseId is empty', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      error: undefined,
      isValidating: false,
      isLoading: false,
      mutate: jest.fn(),
    } as any);

    renderHook(() => useArticleDetail('', 'article456'));

    expect(mockUseSWR).toHaveBeenCalledWith(null, expect.any(Function), {
      revalidateOnFocus: false,
    });
  });

  it('should return undefined key when articleId is empty', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      error: undefined,
      isValidating: false,
      isLoading: false,
      mutate: jest.fn(),
    } as any);

    renderHook(() => useArticleDetail('base123', ''));

    expect(mockUseSWR).toHaveBeenCalledWith(null, expect.any(Function), {
      revalidateOnFocus: false,
    });
  });

  it('should return undefined key when both baseId and articleId are empty', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      error: undefined,
      isValidating: false,
      isLoading: false,
      mutate: jest.fn(),
    } as any);

    renderHook(() => useArticleDetail('', ''));

    expect(mockUseSWR).toHaveBeenCalledWith(null, expect.any(Function), {
      revalidateOnFocus: false,
    });
  });

  it('should return loading state when isValidating is true', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      error: undefined,
      isValidating: true,
      isLoading: false,
      mutate: jest.fn(),
    } as any);

    const { result } = renderHook(() => useArticleDetail('base123', 'article456'));

    expect(result.current.article).toBeUndefined();
    expect(result.current.isValidating).toBe(true);
  });

  it('should call getArticleDetail with correct parameters when fetcher is invoked', () => {
    const mockArticle = { id: '1', title: 'Test Article' };
    mockGetArticleDetail.mockResolvedValue(mockArticle);
    mockUseSWR.mockReturnValue({
      data: mockArticle,
      error: undefined,
      isValidating: false,
      isLoading: false,
      mutate: jest.fn(),
    } as any);

    renderHook(() => useArticleDetail('base123', 'article456'));

    // Get the fetcher function passed to useSWR
    const fetcherFunction = mockUseSWR.mock.calls[0][1];

    // Call the fetcher function
    if (fetcherFunction) {
      fetcherFunction();
    }

    expect(mockGetArticleDetail).toHaveBeenCalledWith('base123', 'article456');
  });
});
