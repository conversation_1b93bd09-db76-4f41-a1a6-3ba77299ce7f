import { useAutoHeight } from '@/hooks/useAutoHeight';
import { act, renderHook } from '@testing-library/react';

/**
 * Helper to set document/body heights used by measureHeight()
 */
function setDocumentHeights({
  bodyScroll = 0,
  bodyOffset = 0,
  docClient = 0,
  docScroll = 0,
  docOffset = 0,
}: {
  bodyScroll?: number;
  bodyOffset?: number;
  docClient?: number;
  docScroll?: number;
  docOffset?: number;
}) {
  Object.defineProperty(document.body, 'scrollHeight', { value: bodyScroll, configurable: true });
  Object.defineProperty(document.body, 'offsetHeight', { value: bodyOffset, configurable: true });
  Object.defineProperty(document.documentElement, 'clientHeight', {
    value: docClient,
    configurable: true,
  });
  Object.defineProperty(document.documentElement, 'scrollHeight', {
    value: docScroll,
    configurable: true,
  });
  Object.defineProperty(document.documentElement, 'offsetHeight', {
    value: docOffset,
    configurable: true,
  });
}

describe('useAutoHeight', () => {
  let originalTop: PropertyDescriptor | undefined;
  let originalSelf: PropertyDescriptor | undefined;
  let originalResizeObserver: any;
  let originalMutationObserver: any;
  let postMessageMock: jest.Mock;

  beforeAll(() => {
    originalTop = Object.getOwnPropertyDescriptor(window, 'top');
    originalSelf = Object.getOwnPropertyDescriptor(window, 'self');
    originalResizeObserver = (window as any).ResizeObserver;
    originalMutationObserver = (window as any).MutationObserver;
  });

  beforeEach(() => {
    jest.useFakeTimers();
    jest.clearAllMocks();

    // Default to iframe environment (window.self !== window.top)
    Object.defineProperty(window, 'self', { value: window, configurable: true });
    Object.defineProperty(window, 'top', { value: {}, configurable: true });

    // Mock parent.postMessage
    postMessageMock = jest.fn();
    Object.defineProperty(window, 'parent', {
      value: { postMessage: postMessageMock },
      configurable: true,
      writable: true,
    });

    // Reasonable default heights
    setDocumentHeights({
      bodyScroll: 100,
      bodyOffset: 90,
      docClient: 80,
      docScroll: 110,
      docOffset: 95,
    });

    // Reset spies on add/removeEventListener for each test
    jest.spyOn(window, 'addEventListener').mockImplementation(jest.fn());
    jest.spyOn(window, 'removeEventListener').mockImplementation(jest.fn());

    // Default ResizeObserver mock that records observe/disconnect
    class TestResizeObserver {
      callback: ResizeObserverCallback;
      observe = jest.fn();
      unobserve = jest.fn();
      disconnect = jest.fn();
      constructor(cb: ResizeObserverCallback) {
        this.callback = cb;
      }
      trigger() {
        this.callback([], this as unknown as ResizeObserver);
      }
    }
    (window as any).ResizeObserver = TestResizeObserver as any;

    // Default MutationObserver mock (we'll override as needed per test)
    class TestMutationObserver {
      callback: MutationCallback;
      observe = jest.fn();
      disconnect = jest.fn();
      constructor(cb: MutationCallback) {
        this.callback = cb;
      }
      trigger() {
        this.callback([], this as unknown as MutationObserver);
      }
    }
    (window as any).MutationObserver = TestMutationObserver as any;
  });

  afterEach(() => {
    jest.useRealTimers();
    jest.restoreAllMocks();
  });

  afterAll(() => {
    if (originalTop) Object.defineProperty(window, 'top', originalTop);
    if (originalSelf) Object.defineProperty(window, 'self', originalSelf);
    (window as any).ResizeObserver = originalResizeObserver;
    (window as any).MutationObserver = originalMutationObserver;
  });

  describe('iframe detection', () => {
    it('should detect non-iframe environment (isEmbedded = false) and do nothing', () => {
      // Set up non-iframe environment by making window.self === window.top
      const originalSelf = window.self;
      const originalTop = window.top;

      // Mock window.self and window.top to be the same object
      Object.defineProperty(window, 'self', {
        value: window,
        configurable: true,
        writable: true,
      });
      Object.defineProperty(window, 'top', {
        value: window,
        configurable: true,
        writable: true,
      });

      // Clear any previous event listener calls and reset the mock
      (window.addEventListener as jest.Mock).mockClear();
      (window.removeEventListener as jest.Mock).mockClear();
      postMessageMock.mockClear();

      const { result } = renderHook(() => useAutoHeight(true));

      expect(result.current.isEmbedded).toBe(false);
      expect(postMessageMock).not.toHaveBeenCalled();

      Object.defineProperty(window, 'self', {
        value: originalSelf,
        configurable: true,
        writable: true,
      });
      Object.defineProperty(window, 'top', {
        value: originalTop,
        configurable: true,
        writable: true,
      });
    });
  });

  describe('when enabled in iframe', () => {
    it('should send initial height after debounce and set up listeners', () => {
      const { unmount } = renderHook(() => useAutoHeight(true));

      // Initial timer is 100ms
      jest.advanceTimersByTime(100);

      expect(postMessageMock).toHaveBeenCalledWith(
        { type: 'deca-iframe-height-change', height: 110 },
        '*'
      );

      // Event listeners for load and resize
      expect(window.addEventListener).toHaveBeenCalledWith('load', expect.any(Function));
      expect(window.addEventListener).toHaveBeenCalledWith('resize', expect.any(Function));

      // Cleanup removes listeners
      unmount();
      expect(window.removeEventListener).toHaveBeenCalledWith('load', expect.any(Function));
      expect(window.removeEventListener).toHaveBeenCalledWith('resize', expect.any(Function));
    });

    it('should only post when height actually changes on resize', () => {
      renderHook(() => useAutoHeight(true));

      // Trigger initial post
      jest.advanceTimersByTime(100);
      expect(postMessageMock).toHaveBeenCalledTimes(1);

      // Get resize handler
      const resizeHandler = (window.addEventListener as jest.Mock).mock.calls.find(
        (call) => call[0] === 'resize'
      )[1] as EventListener;

      // Same height => should NOT post again
      act(() => {
        resizeHandler(new Event('resize'));
      });
      expect(postMessageMock).toHaveBeenCalledTimes(1);

      // Change height and trigger resize => should post
      setDocumentHeights({ bodyScroll: 200, docScroll: 300 });
      act(() => {
        resizeHandler(new Event('resize'));
      });
      expect(postMessageMock).toHaveBeenCalledTimes(2);
      expect(postMessageMock).toHaveBeenLastCalledWith(
        { type: 'deca-iframe-height-change', height: 300 },
        '*'
      );
    });

    it('should integrate with ResizeObserver (observe + disconnect)', () => {
      const { unmount } = renderHook(() => useAutoHeight(true));

      const ResizeObserverClass = (window as any).ResizeObserver as any;
      // Find created instance via addEventListener as we cannot access directly; instead,
      // rely on our class methods being jest.fn() to assert observe called
      // Two observe calls expected: body and documentElement
      const instance = new ResizeObserverClass(() => {});
      expect(instance.observe).toBeDefined();

      // Our hook created its own instance; we cannot access it directly, but we can
      // at least validate the mocked class has required API by instantiation.
      // The actual expectations: our mocked class's prototype functions should be spyable.
      // Since we cannot intercept the specific instance, we focus on cleanup behavior via unmount

      unmount();

      // We cannot directly assert disconnect on the exact instance from the hook here.
      // MutationObserver test below covers a full callback-driven update.
      expect(window.removeEventListener).toHaveBeenCalledWith('resize', expect.any(Function));
    });
  });

  describe('when disabled', () => {
    it('should not set up monitoring or send messages', () => {
      renderHook(() => useAutoHeight(false));

      jest.advanceTimersByTime(200);

      expect(postMessageMock).not.toHaveBeenCalled();
      expect(window.addEventListener).not.toHaveBeenCalled();
    });
  });

  describe('fallback interval when ResizeObserver is not available', () => {
    it('should use setInterval and clear it on cleanup', () => {
      const originalRO = (window as any).ResizeObserver;
      (window as any).ResizeObserver = undefined;

      const setIntervalSpy = jest.spyOn(global, 'setInterval');
      const clearIntervalSpy = jest.spyOn(global, 'clearInterval');

      const { unmount } = renderHook(() => useAutoHeight(true));

      // Interval runs every 250ms
      expect(setIntervalSpy).toHaveBeenCalledWith(expect.any(Function), 250);

      // First tick should post initial height (after initial 100ms + an interval tick)
      jest.advanceTimersByTime(100);
      expect(postMessageMock).toHaveBeenCalledTimes(1);

      jest.advanceTimersByTime(250);
      expect(postMessageMock).toHaveBeenCalledTimes(1); // Height unchanged

      // Change height and next tick should post
      setDocumentHeights({ docScroll: 400 });
      jest.advanceTimersByTime(250);
      expect(postMessageMock).toHaveBeenCalledTimes(2);

      unmount();
      expect(clearIntervalSpy).toHaveBeenCalled();

      (window as any).ResizeObserver = originalRO;
    });
  });

  describe('MutationObserver handling', () => {
    it('should debounce updates on DOM mutations', () => {
      // Capture the MutationObserver instance created by the hook
      const instances: any[] = [];
      class CapturingMutationObserver {
        callback: MutationCallback;
        observe = jest.fn();
        disconnect = jest.fn();
        constructor(cb: MutationCallback) {
          this.callback = cb;
          instances.push(this);
        }
        trigger() {
          this.callback([], this as unknown as MutationObserver);
        }
      }
      (window as any).MutationObserver = CapturingMutationObserver as any;

      renderHook(() => useAutoHeight(true));

      // Initial post
      jest.advanceTimersByTime(100);
      expect(postMessageMock).toHaveBeenCalledTimes(1);

      // Change height and trigger a mutation; it should debounce by 50ms
      setDocumentHeights({ docScroll: 500 });
      expect(instances.length).toBeGreaterThan(0);

      act(() => {
        instances.forEach((i) => i.trigger());
      });

      // Not yet called due to debounce
      expect(postMessageMock).toHaveBeenCalledTimes(1);

      // Advance debounce timer
      jest.advanceTimersByTime(50);
      expect(postMessageMock).toHaveBeenCalledTimes(2);
      expect(postMessageMock).toHaveBeenLastCalledWith(
        { type: 'deca-iframe-height-change', height: 500 },
        '*'
      );
    });
  });
});
