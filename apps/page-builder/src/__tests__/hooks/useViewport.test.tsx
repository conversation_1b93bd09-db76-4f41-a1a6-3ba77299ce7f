import { useViewport } from '@/hooks/useViewport';
import {
  getGlobalBreakpoints,
  subscribeToBreakpointChanges,
} from '@resola-ai/ui/utils/pageBuilder';
import { act, renderHook } from '@testing-library/react';

// Mock the external UI utilities
jest.mock('@resola-ai/ui/utils/pageBuilder', () => ({
  getGlobalBreakpoints: jest.fn(),
  subscribeToBreakpointChanges: jest.fn(),
}));

const mockGetGlobalBreakpoints = getGlobalBreakpoints as jest.MockedFunction<
  typeof getGlobalBreakpoints
>;
const mockSubscribeToBreakpointChanges = subscribeToBreakpointChanges as jest.MockedFunction<
  typeof subscribeToBreakpointChanges
>;

describe('useViewport', () => {
  const defaultBreakpoints = {
    mobile: 480,
    tablet: 768,
    desktop: 1024,
  };

  let mockUnsubscribe: jest.Mock;
  let originalInnerWidth: PropertyDescriptor | undefined;

  beforeAll(() => {
    // Store original descriptor
    originalInnerWidth = Object.getOwnPropertyDescriptor(window, 'innerWidth');
  });

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock the breakpoints
    mockGetGlobalBreakpoints.mockReturnValue(defaultBreakpoints);

    // Mock the subscription
    mockUnsubscribe = jest.fn();
    mockSubscribeToBreakpointChanges.mockReturnValue(mockUnsubscribe);

    // Set default window width
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1200,
    });

    // Mock event listeners to avoid actual DOM manipulation
    jest.spyOn(window, 'addEventListener').mockImplementation();
    jest.spyOn(window, 'removeEventListener').mockImplementation();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  afterAll(() => {
    // Restore original descriptor if it existed
    if (originalInnerWidth) {
      Object.defineProperty(window, 'innerWidth', originalInnerWidth);
    }
  });

  describe('initial viewport calculation', () => {
    it('should return desktop for wide screens', () => {
      Object.defineProperty(window, 'innerWidth', { value: 1200, configurable: true });

      const { result } = renderHook(() => useViewport());

      expect(result.current).toBe('desktop');
    });

    it('should return tablet for medium screens', () => {
      Object.defineProperty(window, 'innerWidth', { value: 800, configurable: true });

      const { result } = renderHook(() => useViewport());

      expect(result.current).toBe('tablet');
    });

    it('should return mobile for narrow screens', () => {
      Object.defineProperty(window, 'innerWidth', { value: 400, configurable: true });

      const { result } = renderHook(() => useViewport());

      expect(result.current).toBe('mobile');
    });

    it('should use exact breakpoint values correctly', () => {
      // Test exact desktop breakpoint
      Object.defineProperty(window, 'innerWidth', { value: 1024, configurable: true });
      const { result: desktopResult } = renderHook(() => useViewport());
      expect(desktopResult.current).toBe('desktop');

      // Test exact tablet breakpoint
      Object.defineProperty(window, 'innerWidth', { value: 768, configurable: true });
      const { result: tabletResult } = renderHook(() => useViewport());
      expect(tabletResult.current).toBe('tablet');

      // Test exact mobile breakpoint
      Object.defineProperty(window, 'innerWidth', { value: 480, configurable: true });
      const { result: mobileResult } = renderHook(() => useViewport());
      expect(mobileResult.current).toBe('mobile');
    });

    it('should handle edge cases', () => {
      // Very small screen
      Object.defineProperty(window, 'innerWidth', { value: 320, configurable: true });
      const { result: smallResult } = renderHook(() => useViewport());
      expect(smallResult.current).toBe('mobile');

      // Very large screen
      Object.defineProperty(window, 'innerWidth', { value: 2000, configurable: true });
      const { result: largeResult } = renderHook(() => useViewport());
      expect(largeResult.current).toBe('desktop');
    });
  });

  describe('custom breakpoints', () => {
    it('should work with custom breakpoint values', () => {
      const customBreakpoints = {
        mobile: 600,
        tablet: 900,
        desktop: 1200,
      };
      mockGetGlobalBreakpoints.mockReturnValue(customBreakpoints);

      Object.defineProperty(window, 'innerWidth', { value: 800, configurable: true });
      const { result } = renderHook(() => useViewport());

      expect(result.current).toBe('mobile'); // 800 < 900
    });

    it('should handle zero breakpoints', () => {
      const zeroBreakpoints = {
        mobile: 0,
        tablet: 0,
        desktop: 0,
      };
      mockGetGlobalBreakpoints.mockReturnValue(zeroBreakpoints);

      Object.defineProperty(window, 'innerWidth', { value: 100, configurable: true });
      const { result } = renderHook(() => useViewport());

      expect(result.current).toBe('desktop'); // All breakpoints are 0, so desktop
    });
  });

  describe('hook lifecycle', () => {
    it('should call getGlobalBreakpoints on initialization', () => {
      renderHook(() => useViewport());

      expect(mockGetGlobalBreakpoints).toHaveBeenCalled();
    });

    it('should set up event listeners', () => {
      renderHook(() => useViewport());

      expect(window.addEventListener).toHaveBeenCalledWith('resize', expect.any(Function));
      expect(mockSubscribeToBreakpointChanges).toHaveBeenCalledWith(expect.any(Function));
    });

    it('should cleanup on unmount', () => {
      const { unmount } = renderHook(() => useViewport());

      unmount();

      expect(window.removeEventListener).toHaveBeenCalledWith('resize', expect.any(Function));
      expect(mockUnsubscribe).toHaveBeenCalled();
    });
  });

  describe('breakpoint changes', () => {
    it('should respond to breakpoint subscription callback', () => {
      Object.defineProperty(window, 'innerWidth', { value: 800, configurable: true });
      const { result } = renderHook(() => useViewport());

      expect(result.current).toBe('tablet');

      // Get the breakpoint change callback
      const breakpointChangeCallback = mockSubscribeToBreakpointChanges.mock.calls[0][0];

      // Change breakpoints so 800 is now mobile
      const newBreakpoints = {
        mobile: 600,
        tablet: 900,
        desktop: 1200,
      };
      mockGetGlobalBreakpoints.mockReturnValue(newBreakpoints);

      act(() => {
        breakpointChangeCallback();
      });

      expect(result.current).toBe('mobile'); // 800 < 900 with new breakpoints
    });
  });

  describe('error handling', () => {
    it('should handle missing breakpoint properties', () => {
      mockGetGlobalBreakpoints.mockReturnValue({
        tablet: undefined as any,
        desktop: 1024,
      } as any);

      Object.defineProperty(window, 'innerWidth', { value: 800, configurable: true });

      expect(() => {
        const { result } = renderHook(() => useViewport());
        expect(['mobile', 'tablet', 'desktop']).toContain(result.current);
      }).not.toThrow();
    });
  });

  describe('SSR handling', () => {
    it('should return desktop as fallback during SSR', () => {
      // Mock SSR environment
      const originalWindow = (global as any).window;
      (global as any).window = undefined;

      const { result } = renderHook(() => useViewport());

      expect(result.current).toBe('desktop');

      // Restore window
      (global as any).window = originalWindow;
    });
  });
});
