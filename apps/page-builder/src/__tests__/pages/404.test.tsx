import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import NotFoundPage from '@/pages/404';

// Mock Mantine components
jest.mock('@mantine/core', () => ({
  Box: jest.fn(({ children, ...props }) => (
    <div data-testid='mantine-box' {...props}>
      {children}
    </div>
  )),
  Flex: jest.fn(({ children, ...props }) => (
    <div data-testid='mantine-flex' {...props}>
      {children}
    </div>
  )),
  Title: jest.fn(({ children, ...props }) => (
    <h1 data-testid='mantine-title' {...props}>
      {children}
    </h1>
  )),
  Text: jest.fn(({ children, ...props }) => (
    <p data-testid='mantine-text' {...props}>
      {children}
    </p>
  )),
}));

// Mock next/image
jest.mock('next/image', () => ({
  __esModule: true,
  default: jest.fn((props) => (
    <img
      {...props}
      src={typeof props.src === 'string' ? props.src : props.src.src}
      alt={props.alt || 'Mock image'}
      data-testid='not-found-image'
    />
  )),
}));

// Mock the SVG import
jest.mock('@/assets/not-found.svg', () => ({
  __esModule: true,
  default: '/mocked-not-found.svg',
}));

describe('NotFoundPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render without crashing', () => {
    expect(() => render(<NotFoundPage />)).not.toThrow();
  });

  it('should render the not found page structure', () => {
    render(<NotFoundPage />);

    // Check that the main containers are rendered
    expect(screen.getByTestId('mantine-flex')).toBeInTheDocument();
    expect(screen.getByTestId('mantine-box')).toBeInTheDocument();
  });

  it('should display the Japanese error message', () => {
    render(<NotFoundPage />);

    // Check the main title
    const title = screen.getByText('現在このウェブサイトにアクセスすることはできません。');
    expect(title).toBeInTheDocument();

    // Check the description text
    const description = screen.getByText(
      /URLが壊れているか、未公開か、間違っている可能性があります/
    );
    expect(description).toBeInTheDocument();
  });

  it('should render the not found image', () => {
    render(<NotFoundPage />);

    const image = screen.getByTestId('not-found-image');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('alt', 'Not found');
    expect(image).toHaveAttribute('src', '/mocked-not-found.svg');
  });

  it('should have proper component structure', () => {
    render(<NotFoundPage />);

    // Check that the components are rendered correctly
    expect(screen.getByTestId('mantine-flex')).toBeInTheDocument();
    expect(screen.getByTestId('mantine-box')).toBeInTheDocument();
    expect(screen.getByTestId('mantine-title')).toBeInTheDocument();
    expect(screen.getByTestId('mantine-text')).toBeInTheDocument();
  });

  it('should render all text content correctly', () => {
    render(<NotFoundPage />);

    // Verify both Japanese text elements are present
    expect(
      screen.getByText('現在このウェブサイトにアクセスすることはできません。')
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        'URLが壊れているか、未公開か、間違っている可能性があります。URLを確認するか、しばらくしてからもう一度お試しください。'
      )
    ).toBeInTheDocument();
  });

  it('should handle component remounting', () => {
    const { unmount } = render(<NotFoundPage />);

    // Unmount and mount fresh to test stability
    unmount();
    render(<NotFoundPage />);

    expect(
      screen.getByText('現在このウェブサイトにアクセスすることはできません。')
    ).toBeInTheDocument();
  });

  it('should be accessible', () => {
    render(<NotFoundPage />);

    // Check that the image has proper alt text
    const image = screen.getByRole('img');
    expect(image).toHaveAttribute('alt', 'Not found');

    // Check that text content is readable
    const title = screen.getByText('現在このウェブサイトにアクセスすることはできません。');
    expect(title).toBeVisible();
  });

  it('should render with expected component hierarchy', () => {
    render(<NotFoundPage />);

    // Verify the component structure
    const flexContainer = screen.getByTestId('mantine-flex');
    const boxContainer = screen.getByTestId('mantine-box');
    const title = screen.getByTestId('mantine-title');
    const text = screen.getByTestId('mantine-text');
    const image = screen.getByTestId('not-found-image');

    expect(flexContainer).toBeInTheDocument();
    expect(boxContainer).toBeInTheDocument();
    expect(title).toBeInTheDocument();
    expect(text).toBeInTheDocument();
    expect(image).toBeInTheDocument();
  });
});
