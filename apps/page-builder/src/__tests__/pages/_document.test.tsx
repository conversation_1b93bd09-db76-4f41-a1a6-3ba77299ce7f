import { render } from '@testing-library/react';

jest.mock('@/api/site', () => ({
  getSiteSetting: jest.fn(),
}));

// Mock Mantine emotion integration to avoid complex server-side behavior
jest.mock('@mantine/emotion', () => ({
  createGetInitialProps: () => async () => ({
    styles: [],
    html: '<html></html>',
    head: [],
  }),
}));

// Mock next/document primitives to be plain components for render
jest.mock('next/document', () => {
  const React = require('react');
  return {
    __esModule: true,
    default: (props: any) => <html {...props} />, // not used directly
    Html: ({ children, ...rest }: any) => (
      <div data-testid='Html' {...rest}>
        {children}
      </div>
    ),
    Head: ({ children, ...rest }: any) => (
      <div data-testid='Head' {...rest}>
        {children}
      </div>
    ),
    Main: () => <div data-testid='Main' />,
    NextScript: () => <div data-testid='NextScript' />,
  };
});

describe('pages/_document', () => {
  beforeEach(() => {
    jest.resetModules();
    process.env.NEXT_PUBLIC_SITE_ID = 'site-1';
  });

  it('should render with header/footer code and embed script (smoke)', () => {
    const Document = require('@/pages/_document').default;

    const { container, getByTestId } = render(
      <Document
        headerCode="<meta name='x' content='y'/>"
        footerCode="<div id='f'></div>"
        embedScriptUrl='https://cdn.example/script.js'
      />
    );

    expect(getByTestId('Head')).toBeInTheDocument();
    expect(getByTestId('Main')).toBeInTheDocument();
    expect(getByTestId('NextScript')).toBeInTheDocument();

    const html = container.innerHTML.replace(/\n|\s+/g, ' ');
    expect(html.includes('meta name="x" content="y"')).toBe(true);
    expect(html.includes('id="f"')).toBe(true);
    expect(html.includes('https://cdn.example/script.js')).toBe(true);
  });

  it('getInitialProps should fetch site settings successfully', async () => {
    const { getSiteSetting } = require('@/api/site');
    (getSiteSetting as jest.Mock).mockResolvedValue({ html: { header: '<h1/>', footer: '<f/>' } });

    const mod = require('@/pages/_document');

    const result = await mod.default.getInitialProps({} as any);

    expect(getSiteSetting).toHaveBeenCalledWith('site-1');
    expect(result).toEqual(expect.objectContaining({ headerCode: '<h1/>', footerCode: '<f/>' }));
  });

  it('getInitialProps should handle errors gracefully', async () => {
    const { getSiteSetting } = require('@/api/site');
    (getSiteSetting as jest.Mock).mockRejectedValue(new Error('fail'));

    const mod = require('@/pages/_document');

    const result = await mod.default.getInitialProps({} as any);

    expect(result).toEqual(expect.objectContaining({ headerCode: '', footerCode: '' }));
  });
});
