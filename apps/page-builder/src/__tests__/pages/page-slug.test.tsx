import type React from 'react';

// Mocks to lighten PageRenderer usage in page
jest.mock('@craftjs/core', () => ({
  Editor: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='editor'>{children}</div>
  ),
  Frame: ({ data }: { data: any }) => <div data-testid='frame' data-content={data} />,
}));

jest.mock('@/components/Meta', () => () => <div data-testid='meta' />);

jest.mock('@/components/ChatwindowElement', () => () => <div data-testid='chatwindow' />);

jest.mock('@resola-ai/ui/utils/pageBuilder', () => ({
  updateGlobalBreakpoints: jest.fn().mockReturnValue(false),
}));

// API and utils mocks
jest.mock('@/api/page', () => ({
  getPages: jest.fn(),
  getPage: jest.fn(),
}));

jest.mock('@/api/intergration', () => ({
  getIntegrations: jest.fn(),
}));

jest.mock('@/utils/page-utils', () => ({
  processPageData: jest.fn(),
  componentResolver: {},
  getChatwindowIntegrationId: jest.fn().mockReturnValue(''),
}));

jest.mock('@resola-ai/services-shared', () => ({
  axiosService: {
    setOrgId: jest.fn(),
  },
}));

describe('pages/[slug]', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    process.env.NEXT_PUBLIC_SITE_ID = 'site-1';
    process.env.NEXT_PUBLIC_ORG_ID = 'org-1';
  });

  describe('getStaticPaths', () => {
    it('should return empty paths when static export disabled', async () => {
      process.env.ENABLE_STATIC_EXPORT = 'false';
      const { getStaticPaths } = require('@/pages/[slug]');

      const res = await getStaticPaths({} as any);
      expect(res).toEqual({ paths: [], fallback: false });
    });

    it('should fetch pages and build paths when enabled', async () => {
      process.env.ENABLE_STATIC_EXPORT = 'true';
      const { getPages } = require('@/api/page');
      (getPages as jest.Mock).mockResolvedValue([{ url: 'home' }, { url: 'about' }]);

      const { getStaticPaths } = require('@/pages/[slug]');
      const res = await getStaticPaths({} as any);

      expect(res).toEqual({
        paths: [{ params: { slug: 'home' } }, { params: { slug: 'about' } }],
        fallback: false,
      });
    });

    it('should handle missing site id and errors gracefully', async () => {
      process.env.ENABLE_STATIC_EXPORT = 'true';
      process.env.NEXT_PUBLIC_SITE_ID = '' as any;

      const { getStaticPaths } = require('@/pages/[slug]');
      const res = await getStaticPaths({} as any);

      expect(res).toEqual({ paths: [], fallback: false });
    });
  });

  describe('getStaticProps', () => {
    it('should return notFound when slug missing', async () => {
      const { getStaticProps } = require('@/pages/[slug]');
      const res = await getStaticProps({ params: {} } as any);
      expect(res).toEqual({ notFound: true });
    });

    it('should return props when page data exists', async () => {
      const { processPageData } = require('@/utils/page-utils');
      (processPageData as jest.Mock).mockResolvedValue({
        page: { id: 'p1' },
        pages: [],
        siteSetting: {},
        site: {},
      });
      const { getIntegrations } = require('@/api/intergration');
      (getIntegrations as jest.Mock).mockResolvedValue([{ id: 'i1' }]);

      const { getStaticProps } = require('@/pages/[slug]');

      const res = await getStaticProps({ params: { slug: 'home' } } as any);

      expect(res).toEqual({
        props: expect.objectContaining({
          page: { id: 'p1' },
          pages: [],
          site: {},
          siteSetting: {},
          integrations: [{ id: 'i1' }],
          orgId: 'org-1',
        }),
      });
    });

    it('should return notFound when page data is null', async () => {
      const { processPageData } = require('@/utils/page-utils');
      (processPageData as jest.Mock).mockResolvedValue(null);

      const { getStaticProps } = require('@/pages/[slug]');
      const res = await getStaticProps({ params: { slug: 'x' } } as any);
      expect(res).toEqual({ notFound: true, props: {} });
    });

    it('should handle errors and return notFound', async () => {
      const { processPageData } = require('@/utils/page-utils');
      (processPageData as jest.Mock).mockRejectedValue(new Error('boom'));

      const { getStaticProps } = require('@/pages/[slug]');
      const res = await getStaticProps({ params: { slug: 'x' } } as any);
      expect(res).toEqual({ notFound: true, props: {} });
    });
  });
});
