import { render, waitFor } from '@testing-library/react';

jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

jest.mock('@/api/site', () => ({
  getDataByShortenId: jest.fn(),
}));

describe('pages/url', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should redirect by setting window.location.href when top-level', async () => {
    const { useRouter } = require('next/router');
    (useRouter as jest.Mock).mockReturnValue({ query: { id: 'abc' } });

    const { getDataByShortenId } = require('@/api/site');
    (getDataByShortenId as jest.Mock).mockResolvedValue({ url: 'https://example.com/abc' });

    Object.defineProperty(window, 'top', { value: window, configurable: true });

    let hrefSetTo: string | undefined;
    const originalLocation = window.location;
    Object.defineProperty(window, 'location', {
      configurable: true,
      value: {
        get href() {
          return originalLocation.href;
        },
        set href(value: string) {
          hrefSetTo = value;
        },
      },
    });

    const Comp = require('@/pages/url').default;
    render(<Comp />);

    await waitFor(() => {
      expect(hrefSetTo).toBe('https://example.com/abc');
    });
  });

  it('should postMessage to parent when inside iframe', async () => {
    const { useRouter } = require('next/router');
    (useRouter as jest.Mock).mockReturnValue({ query: { id: 'abc' } });

    const { getDataByShortenId } = require('@/api/site');
    (getDataByShortenId as jest.Mock).mockResolvedValue({ url: 'https://example.com/abc' });

    const mockPostMessage = jest.fn();
    const mockParent = { postMessage: mockPostMessage };

    // Store original and override window.top
    const originalDescriptor = Object.getOwnPropertyDescriptor(window, 'top');
    Object.defineProperty(window, 'top', {
      value: undefined,
      configurable: true,
      writable: true,
    });
    Object.defineProperty(window, 'top', {
      value: mockParent,
      configurable: true,
      writable: true,
    });

    const Comp = require('@/pages/url').default;
    render(<Comp />);

    // Wait for the API call to complete and the component to process the URL
    await waitFor(() => {
      expect(getDataByShortenId).toHaveBeenCalledWith('abc');
    });

    await waitFor(() => {
      expect(mockPostMessage).toHaveBeenCalledWith(
        { type: 'deca-redirect-to', redirectTo: 'https://example.com/abc' },
        '*'
      );
    });

    // Restore original window.top
    if (originalDescriptor) {
      Object.defineProperty(window, 'top', originalDescriptor);
    }
  });
});
