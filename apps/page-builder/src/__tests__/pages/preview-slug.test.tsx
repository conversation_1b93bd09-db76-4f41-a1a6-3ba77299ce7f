const mockAxios = {
  setAccessToken: jest.fn(),
  setOrgId: jest.fn(),
};

jest.mock('@resola-ai/services-shared', () => ({
  axiosService: mockAxios,
}));

jest.mock('@/utils/page-utils', () => ({
  processPageData: jest.fn(),
  processTemplateData: jest.fn(),
}));

jest.mock('@/api/intergration', () => ({
  getIntegrations: jest.fn(),
}));

// Mock PageRenderer to a simple component
jest.mock('@/components/PageRenderer', () => () => <div data-testid='page-renderer' />);

function importModuleWithEnv(isStaticExport: string | undefined) {
  jest.resetModules();
  if (isStaticExport !== undefined) {
    process.env.ENABLE_STATIC_EXPORT = isStaticExport;
  } else {
    process.env.ENABLE_STATIC_EXPORT = undefined;
  }
  return require('@/pages/preview/[slug]');
}

describe('pages/preview/[slug]', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('getServerSideProps should be defined when static export disabled', async () => {
    const mod = importModuleWithEnv('false');
    expect(typeof mod.getServerSideProps).toBe('function');
  });

  it('getServerSideProps should be undefined when static export enabled', async () => {
    const mod = importModuleWithEnv('true');
    expect(mod.getServerSideProps).toBeUndefined();
  });

  describe('getServerSideProps behavior', () => {
    it('should return notFound when slug missing', async () => {
      const mod = importModuleWithEnv('false');
      const res = await mod.getServerSideProps({ params: {}, req: { cookies: {} } } as any);
      expect(res).toEqual({ notFound: true });
    });

    it('should require accessToken cookie for both template and page preview', async () => {
      const mod = importModuleWithEnv('false');
      const res = await mod.getServerSideProps({
        params: { slug: 'home' },
        req: {
          cookies: {},
          url: 'http://localhost/preview/home',
        },
      } as any);
      expect(res).toEqual({
        notFound: true,
        props: { error: expect.stringContaining('Access token is required') },
      });
    });

    it('should require orgId cookie for both template and page preview', async () => {
      const mod = importModuleWithEnv('false');
      const res = await mod.getServerSideProps({
        params: { slug: 'home' },
        req: {
          cookies: { preview_access_token: 'token' },
          url: 'http://localhost/preview/home',
        },
      } as any);
      expect(res).toEqual({
        notFound: true,
        props: { error: expect.stringContaining('Org ID is required') },
      });
    });

    describe('Template preview scenarios', () => {
      it('should process template data when templateId is provided in cookies', async () => {
        const mod = importModuleWithEnv('false');
        const { processTemplateData } = require('@/utils/page-utils');
        (processTemplateData as jest.Mock).mockResolvedValue({
          page: { id: 't1' },
          pages: [],
          site: { id: 'template' },
          siteSetting: { theme: 'default' },
        });

        const res = await mod.getServerSideProps({
          params: { slug: 'home' },
          req: {
            cookies: {
              preview_access_token: 'token',
              preview_org_id: 'org',
              preview_template_id: 'template123',
            },
            url: 'http://localhost/preview/home',
          },
        } as any);

        expect(processTemplateData).toHaveBeenCalledWith('template123', 'home');
        expect(res).toEqual({
          props: expect.objectContaining({
            page: { id: 't1' },
            site: { id: 'template' },
            siteSetting: { theme: 'default' },
            isPreview: true,
            orgId: 'org',
            accessToken: 'token',
          }),
        });
      });

      it('should handle template processing errors gracefully', async () => {
        const mod = importModuleWithEnv('false');
        const { processTemplateData } = require('@/utils/page-utils');
        (processTemplateData as jest.Mock).mockRejectedValue(new Error('Template not found'));

        const res = await mod.getServerSideProps({
          params: { slug: 'home' },
          req: {
            cookies: {
              preview_access_token: 'token',
              preview_org_id: 'org',
              preview_template_id: 'invalid',
            },
            url: 'http://localhost/preview/home',
          },
        } as any);

        expect(res).toEqual({
          notFound: true,
          props: expect.objectContaining({
            error: expect.stringContaining('Failed to load preview page'),
          }),
        });
      });

      it('should handle malformed URL gracefully', async () => {
        const mod = importModuleWithEnv('false');

        const res = await mod.getServerSideProps({
          params: { slug: 'home' },
          req: {
            cookies: { preview_access_token: 'token', preview_org_id: 'org' },
            url: 'invalid-url',
          },
        } as any);

        // Should still require siteId since templateId couldn't be extracted
        expect(res).toEqual({
          notFound: true,
          props: { error: expect.stringContaining('Site ID is required') },
        });
      });
    });

    describe('Page preview scenarios', () => {
      it('should require siteId cookie when no templateId is provided', async () => {
        const mod = importModuleWithEnv('false');
        const res = await mod.getServerSideProps({
          params: { slug: 'home' },
          req: {
            cookies: { preview_access_token: 'token', preview_org_id: 'org' },
            url: 'http://localhost/preview/home',
          },
        } as any);
        expect(res).toEqual({
          notFound: true,
          props: { error: expect.stringContaining('Site ID is required') },
        });
      });

      it('should return props when page data loads successfully', async () => {
        const mod = importModuleWithEnv('false');
        const { processPageData } = require('@/utils/page-utils');
        (processPageData as jest.Mock).mockResolvedValue({
          page: { id: 'p1' },
          pages: [],
          site: { id: 'site1' },
          siteSetting: { theme: 'dark' },
        });
        const { getIntegrations } = require('@/api/intergration');
        (getIntegrations as jest.Mock).mockResolvedValue([{ id: 'i1' }]);

        const res = await mod.getServerSideProps({
          params: { slug: 'home' },
          req: {
            cookies: {
              preview_site_id: 'site',
              preview_access_token: 'token',
              preview_org_id: 'org',
            },
            url: 'http://localhost/preview/home',
          },
        } as any);

        expect(mockAxios.setAccessToken).toHaveBeenCalledWith('token');
        expect(mockAxios.setOrgId).toHaveBeenCalledWith('org');
        expect(processPageData).toHaveBeenCalledWith('site', 'home', '');
        expect(getIntegrations).toHaveBeenCalledWith({ site_id: 'site' });

        expect(res).toEqual({
          props: expect.objectContaining({
            page: { id: 'p1' },
            site: { id: 'site1' },
            siteSetting: { theme: 'dark' },
            integrations: [{ id: 'i1' }],
            isPreview: true,
            orgId: 'org',
            accessToken: 'token',
          }),
        });
      });

      it('should handle page processing errors and return notFound with message', async () => {
        const mod = importModuleWithEnv('false');
        const { processPageData } = require('@/utils/page-utils');
        (processPageData as jest.Mock).mockRejectedValue(new Error('Page not found'));

        const res = await mod.getServerSideProps({
          params: { slug: 'home' },
          req: {
            cookies: {
              preview_site_id: 'site',
              preview_access_token: 'token',
              preview_org_id: 'org',
            },
            url: 'http://localhost/preview/home',
          },
        } as any);

        expect(res).toEqual({
          notFound: true,
          props: expect.objectContaining({
            error: expect.stringContaining('Failed to load preview page'),
          }),
        });
      });

      it('should return notFound when page data is null', async () => {
        const mod = importModuleWithEnv('false');
        const { processPageData } = require('@/utils/page-utils');
        (processPageData as jest.Mock).mockResolvedValue(null);

        const res = await mod.getServerSideProps({
          params: { slug: 'home' },
          req: {
            cookies: {
              preview_site_id: 'site',
              preview_access_token: 'token',
              preview_org_id: 'org',
            },
            url: 'http://localhost/preview/home',
          },
        } as any);

        expect(res).toEqual({ notFound: true, props: {} });
      });
    });

    describe('Local development scenarios', () => {
      it('should require cookies even in local environment', async () => {
        const mod = importModuleWithEnv('false');
        const res = await mod.getServerSideProps({
          params: { slug: 'home' },
          req: {
            cookies: {},
            url: 'http://localhost/preview/home',
          },
        } as any);

        expect(res).toEqual({
          notFound: true,
          props: { error: expect.stringContaining('Access token is required') },
        });
      });
    });
  });
});
