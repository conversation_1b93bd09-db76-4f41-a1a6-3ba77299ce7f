import { render } from '@testing-library/react';

// Mock CSS imports to avoid Jest parsing CSS
jest.mock('@mantine/core/styles.css', () => ({}), { virtual: true });
jest.mock('@/styles/global.css', () => ({}), { virtual: true });

jest.mock('@resola-ai/services-shared', () => ({
  axiosService: {
    init: jest.fn(),
    setAccessToken: jest.fn(),
    setOrgId: jest.fn(),
  },
  datadogService: {
    init: jest.fn(),
  },
}));

jest.mock('@/hooks/useUrlBinding', () => ({
  useUrlBinding: jest.fn(),
}));

jest.mock('@/hooks/useAutoHeight', () => ({
  useAutoHeight: jest.fn(),
}));

const DummyComponent = (props: any) => <div data-testid='dummy'>Dummy {props.foo}</div>;

describe('pages/_app', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    process.env.BUILD_ACCESS_TOKEN = 'token';
    process.env.NEXT_PUBLIC_ORG_ID = 'org-123';
    process.env.NEXT_PUBLIC_DATADOG_APPLICATION_ID = 'dda';
    process.env.NEXT_PUBLIC_DATADOG_CLIENT_TOKEN = 'ddc';
    process.env.NEXT_PUBLIC_DATADOG_SITE = 'dds';
    process.env.NEXT_PUBLIC_DATADOG_SERVICE = 'ddsvc';
    process.env.NEXT_PUBLIC_DATADOG_ENV = 'dde';
  });

  it('should initialize services and render Component within provider', async () => {
    const { axiosService, datadogService } = require('@resola-ai/services-shared');
    const { useUrlBinding } = require('@/hooks/useUrlBinding');
    const { useAutoHeight } = require('@/hooks/useAutoHeight');

    const App = require('@/pages/_app').default;

    const { getByTestId } = render(
      <App Component={DummyComponent as any} pageProps={{ foo: 'bar' }} />
    );

    expect(getByTestId('dummy').textContent).toContain('Dummy bar');

    // Service initializations at module load
    expect(axiosService.init).toHaveBeenCalled();
    expect(axiosService.setAccessToken).toHaveBeenCalledWith('token');
    expect(axiosService.setOrgId).toHaveBeenCalledWith('org-123');
    expect(datadogService.init).toHaveBeenCalledWith(
      expect.objectContaining({
        applicationId: 'dda',
        clientToken: 'ddc',
        site: 'dds',
        service: 'ddsvc',
        env: 'dde',
      })
    );

    // Hooks called
    expect(useUrlBinding).toHaveBeenCalledWith(true);
    expect(useAutoHeight).toHaveBeenCalledWith(true);
  });
});
