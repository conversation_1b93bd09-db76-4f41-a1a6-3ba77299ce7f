import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import ContainerWrapper from '@/components/ContainerWrapper';
import { BackgroundType } from '@resola-ai/ui/types/pageBuilder';

// Mock all dependencies
jest.mock('@craftjs/core', () => ({
  useNode: jest.fn(),
  Element: jest.fn(({ id, ...props }) => (
    <div data-testid={`craft-element-${id}`} {...props}>
      Element-{id}
    </div>
  )),
}));

jest.mock('@mantine/core', () => ({
  Flex: jest.fn(({ children, styles, ...props }) => (
    <div data-testid='mantine-flex' data-styles={JSON.stringify(styles)} {...props}>
      {children}
    </div>
  )),
}));

jest.mock('@resola-ai/ui/utils', () => ({
  generateResponsiveStyles: jest.fn((styles) => styles),
  createResponsiveValue: jest.fn((mobile, tablet, desktop) => ({
    mobile,
    tablet,
    desktop,
  })),
  generateResponsivePadding: jest.fn((padding) => padding),
  generateResponsiveDimension: jest.fn((dimension) => dimension),
}));

jest.mock('@resola-ai/ui/components/PageBuilder/ContainerElement', () =>
  jest.fn(() => <div data-testid='container-element'>ContainerElement</div>)
);

const mockUseNode = require('@craftjs/core').useNode;

describe('ContainerWrapper', () => {
  const mockLinkedNodes = {
    'node-1': { id: 'node-1' },
    'node-2': { id: 'node-2' },
    'node-3': { id: 'node-3' },
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Default useNode mock
    mockUseNode.mockReturnValue({
      linkedNodes: mockLinkedNodes,
    });
  });

  it('should render without crashing', () => {
    expect(() => render(<ContainerWrapper />)).not.toThrow();
  });

  it('should render Mantine Flex component', () => {
    const { getByTestId } = render(<ContainerWrapper />);
    expect(getByTestId('mantine-flex')).toBeInTheDocument();
  });

  it('should render all linked nodes as CraftJS Elements', () => {
    const { getByTestId } = render(<ContainerWrapper />);

    expect(getByTestId('craft-element-node-1')).toBeInTheDocument();
    expect(getByTestId('craft-element-node-2')).toBeInTheDocument();
    expect(getByTestId('craft-element-node-3')).toBeInTheDocument();
  });

  it('should handle empty linked nodes', () => {
    mockUseNode.mockReturnValue({
      linkedNodes: {},
    });

    const { queryByTestId, getByTestId } = render(<ContainerWrapper />);

    expect(queryByTestId('craft-element-node-1')).not.toBeInTheDocument();
    expect(getByTestId('mantine-flex')).toBeInTheDocument();
  });

  it('should apply default flexDirection and alignment', () => {
    const { getByTestId } = render(<ContainerWrapper />);

    const flexElement = getByTestId('mantine-flex');
    const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

    expect(styles.flexDirection).toBe('column');
    expect(styles.alignItems).toBe('flex-start');
    expect(styles.justifyContent).toBe('flex-start');
  });

  it('should apply custom direction, align, and justify props', () => {
    const props = {
      direction: 'row',
      align: 'center',
      justify: 'space-between',
    };

    const { getByTestId } = render(<ContainerWrapper {...props} />);

    const flexElement = getByTestId('mantine-flex');
    const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

    expect(styles.flexDirection).toBe('row');
    expect(styles.alignItems).toBe('center');
    expect(styles.justifyContent).toBe('space-between');
  });

  it('should apply responsive gap styles', () => {
    const props = {
      gap: {
        mobile: 10,
        tablet: 20,
        desktop: 30,
      },
    };

    const { getByTestId } = render(<ContainerWrapper {...props} />);

    const flexElement = getByTestId('mantine-flex');
    const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

    expect(styles.gap).toEqual({
      mobile: '10px',
      tablet: '20px',
      desktop: '30px',
    });
  });

  it('should handle missing gap with default value', () => {
    const { getByTestId } = render(<ContainerWrapper />);

    const flexElement = getByTestId('mantine-flex');
    const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

    expect(styles.gap).toBe('0px');
  });

  it('should apply width and height dimensions', () => {
    const props = {
      width: '100%',
      height: '500px',
    };

    const { getByTestId } = render(<ContainerWrapper {...props} />);

    const flexElement = getByTestId('mantine-flex');
    const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

    expect(styles.width).toBe('100%');
    expect(styles.height).toBe('500px');
  });

  it('should apply background image when backgroundType is Image', () => {
    const props = {
      backgroundType: BackgroundType.Image,
      mediaUrl: 'https://example.com/image.jpg',
    };

    const { getByTestId } = render(<ContainerWrapper {...props} />);

    const flexElement = getByTestId('mantine-flex');
    const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

    expect(styles.backgroundImage).toBe('url(https://example.com/image.jpg)');
  });

  it('should apply background color when backgroundType is Color', () => {
    const props = {
      backgroundType: BackgroundType.Color,
      backgroundColor: '#ff0000',
    };

    const { getByTestId } = render(<ContainerWrapper {...props} />);

    const flexElement = getByTestId('mantine-flex');
    const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

    expect(styles.backgroundColor).toBe('#ff0000');
  });

  it('should apply transparent background when backgroundType is None', () => {
    const props = {
      backgroundType: BackgroundType.None,
    };

    const { getByTestId } = render(<ContainerWrapper {...props} />);

    const flexElement = getByTestId('mantine-flex');
    const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

    expect(styles.backgroundColor).toBe('transparent');
  });

  it('should apply object fit styles for background size', () => {
    const props = {
      objectFit: {
        mobile: 'cover',
        tablet: 'contain',
        desktop: 'fill',
      },
    };

    const { getByTestId } = render(<ContainerWrapper {...props} />);

    const flexElement = getByTestId('mantine-flex');
    const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

    expect(styles.backgroundSize).toEqual({
      mobile: 'cover',
      tablet: 'contain',
      desktop: 'cover', // 'fill' maps to 'cover'
    });
  });

  it('should apply overlay styles when overlay is enabled', () => {
    const props = {
      overlay: {
        enabled: true,
        color: 'rgba(0, 0, 0, 0.5)',
        opacity: 50,
      },
    };

    const { getByTestId } = render(<ContainerWrapper {...props} />);

    const flexElement = getByTestId('mantine-flex');
    const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

    expect(styles['&::before']).toEqual({
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      opacity: 0.5, // 50 / 100
      pointerEvents: 'none',
      zIndex: 1,
    });
  });

  it('should not apply overlay styles when overlay is disabled', () => {
    const props = {
      overlay: {
        enabled: false,
        color: 'rgba(0, 0, 0, 0.5)',
        opacity: 50,
      },
    };

    const { getByTestId } = render(<ContainerWrapper {...props} />);

    const flexElement = getByTestId('mantine-flex');
    const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

    expect(styles['&::before']).toEqual({});
  });

  it('should apply padding styles', () => {
    const props = {
      padding: {
        mobile: 10,
        tablet: 20,
        desktop: 30,
      },
    };

    const { getByTestId } = render(<ContainerWrapper {...props} />);

    const flexElement = getByTestId('mantine-flex');
    const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

    expect(styles.padding).toEqual({
      mobile: 10,
      tablet: 20,
      desktop: 30,
    });
  });

  it('should apply background positioning and repeat styles', () => {
    const { getByTestId } = render(<ContainerWrapper />);

    const flexElement = getByTestId('mantine-flex');
    const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

    expect(styles.backgroundPosition).toBe('center');
    expect(styles.backgroundRepeat).toBe('no-repeat');
    expect(styles.position).toBe('relative');
  });

  it('should apply z-index styles for children', () => {
    const { getByTestId } = render(<ContainerWrapper />);

    const flexElement = getByTestId('mantine-flex');
    const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

    expect(styles['& > *']).toEqual({
      position: 'relative',
      zIndex: 2,
    });
  });

  it('should handle complex prop combinations', () => {
    const props = {
      direction: 'row',
      width: '100%',
      height: '400px',
      padding: { mobile: 15, tablet: 25, desktop: 35 },
      align: 'center',
      justify: 'space-around',
      gap: { mobile: 5, tablet: 10, desktop: 15 },
      backgroundType: BackgroundType.Image,
      mediaUrl: 'https://example.com/bg.jpg',
      objectFit: { mobile: 'cover', tablet: 'cover', desktop: 'contain' },
      overlay: {
        enabled: true,
        color: '#000000',
        opacity: 75,
      },
    };

    const { getByTestId } = render(<ContainerWrapper {...props} />);

    const flexElement = getByTestId('mantine-flex');
    const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

    expect(styles.flexDirection).toBe('row');
    expect(styles.alignItems).toBe('center');
    expect(styles.justifyContent).toBe('space-around');
    expect(styles.backgroundImage).toBe('url(https://example.com/bg.jpg)');
    expect(styles['&::before'].opacity).toBe(0.75);
  });

  describe('getBackgroundSize utility', () => {
    it('should return cover for cover objectFit', () => {
      const props = {
        objectFit: { mobile: 'cover' },
      };

      const { getByTestId } = render(<ContainerWrapper {...props} />);
      const flexElement = getByTestId('mantine-flex');
      const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

      expect(styles.backgroundSize.mobile).toBe('cover');
    });

    it('should return cover for fill objectFit', () => {
      const props = {
        objectFit: { mobile: 'fill' },
      };

      const { getByTestId } = render(<ContainerWrapper {...props} />);
      const flexElement = getByTestId('mantine-flex');
      const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

      expect(styles.backgroundSize.mobile).toBe('cover');
    });

    it('should return contain for other objectFit values', () => {
      const props = {
        objectFit: { mobile: 'scale-down' },
      };

      const { getByTestId } = render(<ContainerWrapper {...props} />);
      const flexElement = getByTestId('mantine-flex');
      const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

      expect(styles.backgroundSize.mobile).toBe('contain');
    });

    it('should handle undefined objectFit', () => {
      const { getByTestId } = render(<ContainerWrapper />);

      // Should not crash when objectFit is undefined
      expect(getByTestId('mantine-flex')).toBeInTheDocument();
    });

    it('should handle null objectFit values', () => {
      const props = {
        objectFit: {
          mobile: null,
          tablet: null,
          desktop: null,
        },
      };

      const { getByTestId } = render(<ContainerWrapper {...props} />);
      const flexElement = getByTestId('mantine-flex');
      const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

      expect(styles.backgroundSize).toEqual({
        mobile: 'contain', // null maps to 'contain'
        tablet: 'contain',
        desktop: 'contain',
      });
    });

    it('should handle empty string objectFit values', () => {
      const props = {
        objectFit: {
          mobile: '',
          tablet: '',
          desktop: '',
        },
      };

      const { getByTestId } = render(<ContainerWrapper {...props} />);
      const flexElement = getByTestId('mantine-flex');
      const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

      expect(styles.backgroundSize).toEqual({
        mobile: 'contain', // empty string maps to 'contain'
        tablet: 'contain',
        desktop: 'contain',
      });
    });

    it('should handle mixed objectFit values', () => {
      const props = {
        objectFit: {
          mobile: 'cover',
          tablet: null,
          desktop: 'invalid-value',
        },
      };

      const { getByTestId } = render(<ContainerWrapper {...props} />);
      const flexElement = getByTestId('mantine-flex');
      const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

      expect(styles.backgroundSize).toEqual({
        mobile: 'cover',
        tablet: 'contain', // null maps to 'contain'
        desktop: 'contain', // invalid value maps to 'contain'
      });
    });

    it('should handle all edge case objectFit values', () => {
      const edgeCases = [
        { value: false, expected: 'contain' },
        { value: 0, expected: 'contain' },
        { value: undefined, expected: 'contain' },
        { value: 'none', expected: 'contain' },
        { value: 'auto', expected: 'contain' },
        { value: 'inherit', expected: 'contain' },
        { value: 'initial', expected: 'contain' },
      ];

      edgeCases.forEach(({ value, expected }, index) => {
        const props = {
          objectFit: { mobile: value },
        };

        const { container } = render(<ContainerWrapper {...props} />);
        const flexElement = container.querySelector('[data-testid="mantine-flex"]');
        const styles = JSON.parse(flexElement?.getAttribute('data-styles') || '{}');

        expect(styles.backgroundSize.mobile).toBe(expected);
      });
    });
  });

  describe('useNode hook integration', () => {
    it('should handle useNode returning different linkedNodes structures', () => {
      // Test with complex linkedNodes structure
      const complexLinkedNodes = {
        'container-1': {
          id: 'container-1',
          type: 'div',
          props: { className: 'test' },
        },
        'container-2': {
          id: 'container-2',
          type: 'section',
          children: ['child-1', 'child-2'],
        },
      };

      mockUseNode.mockReturnValue({ linkedNodes: complexLinkedNodes });

      const { getByTestId } = render(<ContainerWrapper />);

      expect(getByTestId('craft-element-container-1')).toBeInTheDocument();
      expect(getByTestId('craft-element-container-2')).toBeInTheDocument();
    });

    it('should handle useNode with null linkedNodes', () => {
      mockUseNode.mockReturnValue({ linkedNodes: null });

      expect(() => render(<ContainerWrapper />)).toThrow();
    });

    it('should handle useNode with undefined linkedNodes', () => {
      mockUseNode.mockReturnValue({ linkedNodes: undefined });

      expect(() => render(<ContainerWrapper />)).toThrow();
    });

    it('should handle useNode returning non-object linkedNodes', () => {
      mockUseNode.mockReturnValue({ linkedNodes: 'invalid' });

      // Object.keys('invalid') returns ['0', '1', '2', '3', '4', '5', '6'] - doesn't throw
      const { container } = render(<ContainerWrapper />);
      const elements = container.querySelectorAll('[data-testid^="craft-element-"]');
      expect(elements).toHaveLength(7); // String 'invalid' has 7 characters
    });
  });

  describe('edge cases and error handling', () => {
    it('should handle malformed gap object', () => {
      const props = {
        gap: {
          mobile: null,
          tablet: undefined,
          desktop: 'invalid',
        },
      };

      const { getByTestId } = render(<ContainerWrapper {...props} />);
      const flexElement = getByTestId('mantine-flex');
      const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

      expect(styles.gap).toEqual({
        mobile: 'nullpx',
        tablet: 'undefinedpx',
        desktop: 'invalidpx',
      });
    });

    it('should handle gap with zero values', () => {
      const props = {
        gap: {
          mobile: 0,
          tablet: 0,
          desktop: 0,
        },
      };

      const { getByTestId } = render(<ContainerWrapper {...props} />);
      const flexElement = getByTestId('mantine-flex');
      const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

      expect(styles.gap).toEqual({
        mobile: '0px',
        tablet: '0px',
        desktop: '0px',
      });
    });

    it('should handle all backgroundType enum values', () => {
      // Test each background type explicitly
      const backgroundTypes = [BackgroundType.Image, BackgroundType.Color, BackgroundType.None];

      backgroundTypes.forEach((bgType) => {
        const props = {
          backgroundType: bgType,
          mediaUrl: 'test.jpg',
          backgroundColor: '#ff0000',
        };

        const { container } = render(<ContainerWrapper {...props} />);
        const flexElement = container.querySelector('[data-testid="mantine-flex"]');
        const styles = JSON.parse(flexElement?.getAttribute('data-styles') || '{}');

        if (bgType === BackgroundType.Image) {
          expect(styles.backgroundImage).toBe('url(test.jpg)');
        } else if (bgType === BackgroundType.Color) {
          expect(styles.backgroundColor).toBe('#ff0000');
        } else if (bgType === BackgroundType.None) {
          expect(styles.backgroundColor).toBe('transparent');
        }
      });
    });

    it('should handle invalid backgroundType values', () => {
      const props = {
        backgroundType: 'invalid',
        mediaUrl: 'test.jpg',
        backgroundColor: '#ff0000',
      };

      const { getByTestId } = render(<ContainerWrapper {...props} />);
      const flexElement = getByTestId('mantine-flex');
      const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

      // Should not apply any background-specific styles for invalid type
      expect(styles.backgroundImage).toBeUndefined();
      expect(styles.backgroundColor).toBeUndefined();
    });

    it('should handle overlay with zero opacity', () => {
      const props = {
        overlay: {
          enabled: true,
          color: '#000000',
          opacity: 0,
        },
      };

      const { getByTestId } = render(<ContainerWrapper {...props} />);
      const flexElement = getByTestId('mantine-flex');
      const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

      expect(styles['&::before'].opacity).toBe(0);
    });

    it('should handle overlay with maximum opacity', () => {
      const props = {
        overlay: {
          enabled: true,
          color: '#ffffff',
          opacity: 100,
        },
      };

      const { getByTestId } = render(<ContainerWrapper {...props} />);
      const flexElement = getByTestId('mantine-flex');
      const styles = JSON.parse(flexElement.getAttribute('data-styles') || '{}');

      expect(styles['&::before'].opacity).toBe(1);
    });
  });

  it('should render different numbers of linked nodes', () => {
    const testCases = [
      {},
      { 'single-node': {} },
      { 'node-a': {}, 'node-b': {}, 'node-c': {}, 'node-d': {}, 'node-e': {} },
    ];

    testCases.forEach((linkedNodes, index) => {
      mockUseNode.mockReturnValue({ linkedNodes });

      const { container } = render(<ContainerWrapper key={index} />);
      const elements = container.querySelectorAll('[data-testid^="craft-element-"]');

      expect(elements).toHaveLength(Object.keys(linkedNodes).length);
    });
  });
});
