import { fireEvent, render } from '@testing-library/react';
import '@testing-library/jest-dom';
import { sendViewEvent } from '@/api/site';
import FlatArticlesElement from '@/components/FlatArticlesElement';
import { useConditionalArticles } from '@/hooks';
import { useViewport } from '@/hooks/useViewport';
import { usePageContext } from '@/providers';
import { ArticleDisplayModes } from '@/types/enum';
import { sendGAEvent } from '@/utils/page-utils';

// Mock all dependencies
jest.mock('@/hooks', () => ({
  useConditionalArticles: jest.fn(),
}));

jest.mock('@/hooks/useViewport', () => ({
  useViewport: jest.fn(),
}));

jest.mock('@/providers', () => ({
  usePageContext: jest.fn(),
}));

jest.mock('@/api/site', () => ({
  sendViewEvent: jest.fn(),
}));

jest.mock('@/utils/page-utils', () => ({
  sendGAEvent: jest.fn(),
}));

jest.mock('@resola-ai/ui/components/PageBuilder', () => ({
  FlatArticlesElement: jest.fn((props) => (
    <div data-testid='flat-articles-ui'>
      <button
        data-testid='article-button'
        onClick={() => props.onChangeArticle?.('article-123')}
        type='button'
      >
        Change Article
      </button>
      <div data-testid='articles-data'>{JSON.stringify(props.selectedArticles)}</div>
      <div data-testid='display-mode'>{props.displayMode}</div>
      <div data-testid='maximum-articles'>{props.maximumArticles}</div>
    </div>
  )),
}));

// Mock window.location
Object.defineProperty(window, 'location', {
  value: { href: 'https://example.com/test-page' },
  writable: true,
});

const mockUseArticles = useConditionalArticles as jest.MockedFunction<
  typeof useConditionalArticles
>;
const mockUseViewport = useViewport as jest.MockedFunction<typeof useViewport>;
const mockUsePageContext = usePageContext as jest.MockedFunction<typeof usePageContext>;
const mockSendViewEvent = sendViewEvent as jest.MockedFunction<typeof sendViewEvent>;
const mockSendGAEvent = sendGAEvent as jest.MockedFunction<typeof sendGAEvent>;

describe('FlatArticlesElement', () => {
  const mockArticles = [
    { value: 'article-123', label: 'Test Article 1', content: 'Content 1' },
    { value: 'article-456', label: 'Test Article 2', content: 'Content 2' },
  ];

  const mockPage = { id: 'current-page', name: 'Current Page' };

  beforeEach(() => {
    jest.clearAllMocks();

    // Set up default mock returns
    mockUseArticles.mockReturnValue({
      articles: mockArticles,
      isFetching: false,
    });

    mockUseViewport.mockReturnValue('desktop');
    mockUsePageContext.mockReturnValue({
      page: mockPage,
      pages: [],
    });
  });

  it('should render without crashing', () => {
    const props = {
      displayMode: ArticleDisplayModes.All,
      selectedArticles: mockArticles,
      maximumArticles: 5,
    };

    expect(() => render(<FlatArticlesElement {...props} />)).not.toThrow();
  });

  it('should render the FlatArticlesElementUI component', () => {
    const props = {
      displayMode: ArticleDisplayModes.All,
      selectedArticles: mockArticles,
      maximumArticles: 5,
    };

    const { getByTestId } = render(<FlatArticlesElement {...props} />);

    expect(getByTestId('flat-articles-ui')).toBeInTheDocument();
  });

  it('should call useArticles hook with correct parameters', () => {
    const props = {
      displayMode: ArticleDisplayModes.MostViewed,
      selectedArticles: [],
      maximumArticles: 10,
    };

    render(<FlatArticlesElement {...props} />);

    expect(mockUseArticles).toHaveBeenCalledWith(
      [],
      ArticleDisplayModes.MostViewed,
      10,
      undefined,
      undefined
    );
  });

  it('should pass processed articles to UI component', () => {
    const props = {
      displayMode: ArticleDisplayModes.All,
      selectedArticles: mockArticles,
      maximumArticles: 5,
    };

    const { getByTestId } = render(<FlatArticlesElement {...props} />);

    const articlesData = getByTestId('articles-data');
    expect(articlesData.textContent).toBe(JSON.stringify(mockArticles));
  });

  it('should handle article change and send view event', () => {
    const props = {
      displayMode: ArticleDisplayModes.All,
      selectedArticles: mockArticles,
      maximumArticles: 5,
    };

    const { getByTestId } = render(<FlatArticlesElement {...props} />);

    fireEvent.click(getByTestId('article-button'));

    expect(mockSendViewEvent).toHaveBeenCalledWith({
      article_id: 'article-123',
      page_id: 'current-page',
      page_title: 'Current Page',
      event_type: 'view',
      device_type: 'desktop',
    });
  });

  it('should send GA event when article is changed', () => {
    const props = {
      displayMode: ArticleDisplayModes.All,
      selectedArticles: mockArticles,
      maximumArticles: 5,
    };

    const { getByTestId } = render(<FlatArticlesElement {...props} />);

    fireEvent.click(getByTestId('article-button'));

    expect(mockSendGAEvent).toHaveBeenCalledWith('faq_article_view', {
      article_id: 'article-123',
      article_title: 'Test Article 1',
      url: 'https://example.com/test-page',
      source: 'article_list',
    });
  });

  it('should handle article change when article is not found', () => {
    const props = {
      displayMode: ArticleDisplayModes.All,
      selectedArticles: [],
      maximumArticles: 5,
    };

    mockUseArticles.mockReturnValue({
      articles: [],
      isFetching: false,
    });

    const { getByTestId } = render(<FlatArticlesElement {...props} />);

    fireEvent.click(getByTestId('article-button'));

    // Should still send view event even if article is not found
    expect(mockSendViewEvent).toHaveBeenCalledWith({
      article_id: 'article-123',
      page_id: 'current-page',
      page_title: 'Current Page',
      event_type: 'view',
      device_type: 'desktop',
    });

    // Should not send GA event if article is not found
    expect(mockSendGAEvent).not.toHaveBeenCalled();
  });

  it('should handle different device types', () => {
    mockUseViewport.mockReturnValue('mobile');

    const props = {
      displayMode: ArticleDisplayModes.All,
      selectedArticles: mockArticles,
      maximumArticles: 5,
    };

    const { getByTestId } = render(<FlatArticlesElement {...props} />);

    fireEvent.click(getByTestId('article-button'));

    expect(mockSendViewEvent).toHaveBeenCalledWith({
      article_id: 'article-123',
      page_id: 'current-page',
      page_title: 'Current Page',
      event_type: 'view',
      device_type: 'mobile',
    });
  });

  it('should handle different display modes', () => {
    const displayModes = [ArticleDisplayModes.All, ArticleDisplayModes.MostViewed];

    displayModes.forEach((mode) => {
      jest.clearAllMocks();

      const props = {
        displayMode: mode,
        selectedArticles: mockArticles,
        maximumArticles: 5,
      };

      render(<FlatArticlesElement {...props} />);

      expect(mockUseArticles).toHaveBeenCalledWith(mockArticles, mode, 5, undefined, undefined);
    });
  });

  it('should handle different maximum articles values', () => {
    const maxArticlesValues = [1, 5, 10, 20];

    maxArticlesValues.forEach((maxArticles) => {
      jest.clearAllMocks();

      const props = {
        displayMode: ArticleDisplayModes.All,
        selectedArticles: mockArticles,
        maximumArticles: maxArticles,
      };

      render(<FlatArticlesElement {...props} />);

      expect(mockUseArticles).toHaveBeenCalledWith(
        mockArticles,
        ArticleDisplayModes.All,
        maxArticles,
        undefined,
        undefined
      );
    });
  });

  it('should pass all props to UI component', () => {
    const props = {
      displayMode: ArticleDisplayModes.MostViewed,
      selectedArticles: mockArticles,
      maximumArticles: 10,
      customProp: 'custom-value',
    };

    const { getByTestId } = render(<FlatArticlesElement {...props} />);

    expect(getByTestId('display-mode')).toHaveTextContent(ArticleDisplayModes.MostViewed);
    expect(getByTestId('maximum-articles')).toHaveTextContent('10');
  });

  it('should handle empty selectedArticles array', () => {
    const props = {
      displayMode: ArticleDisplayModes.All,
      selectedArticles: [],
      maximumArticles: 5,
    };

    mockUseArticles.mockReturnValue({
      articles: [],
      isFetching: false,
    });

    const { getByTestId } = render(<FlatArticlesElement {...props} />);

    const articlesData = getByTestId('articles-data');
    expect(articlesData.textContent).toBe('[]');
  });

  it('should handle loading state from useArticles', () => {
    const props = {
      displayMode: ArticleDisplayModes.All,
      selectedArticles: mockArticles,
      maximumArticles: 5,
    };

    mockUseArticles.mockReturnValue({
      articles: mockArticles,
      isFetching: true,
    });

    expect(() => render(<FlatArticlesElement {...props} />)).not.toThrow();
  });

  it('should handle null or undefined page context', () => {
    mockUsePageContext.mockReturnValue({
      page: { id: 'current-page', name: 'Current Page' },
      pages: [],
    });

    const props = {
      displayMode: ArticleDisplayModes.All,
      selectedArticles: mockArticles,
      maximumArticles: 5,
    };

    const { getByTestId } = render(<FlatArticlesElement {...props} />);

    fireEvent.click(getByTestId('article-button'));

    // Should handle gracefully without throwing errors
    expect(mockSendViewEvent).toHaveBeenCalledWith({
      article_id: 'article-123',
      page_id: 'current-page',
      page_title: 'Current Page',
      event_type: 'view',
      device_type: 'desktop',
    });
  });

  it('should handle missing page properties', () => {
    mockUsePageContext.mockReturnValue({
      page: {},
      pages: [],
    });

    const props = {
      displayMode: ArticleDisplayModes.All,
      selectedArticles: mockArticles,
      maximumArticles: 5,
    };

    const { getByTestId } = render(<FlatArticlesElement {...props} />);

    fireEvent.click(getByTestId('article-button'));

    expect(mockSendViewEvent).toHaveBeenCalledWith({
      article_id: 'article-123',
      page_id: undefined,
      page_title: undefined,
      event_type: 'view',
      device_type: 'desktop',
    });
  });

  it('should handle multiple article changes', () => {
    const props = {
      displayMode: ArticleDisplayModes.All,
      selectedArticles: mockArticles,
      maximumArticles: 5,
    };

    const { getByTestId } = render(<FlatArticlesElement {...props} />);

    // Click multiple times
    fireEvent.click(getByTestId('article-button'));
    fireEvent.click(getByTestId('article-button'));
    fireEvent.click(getByTestId('article-button'));

    expect(mockSendViewEvent).toHaveBeenCalledTimes(3);
    expect(mockSendGAEvent).toHaveBeenCalledTimes(3);
  });

  it('should handle different article IDs in change handler', () => {
    const { FlatArticlesElement: MockUI } = require('@resola-ai/ui/components/PageBuilder');

    // Mock the UI component to call with different article ID
    MockUI.mockImplementation((props) => (
      <div data-testid='flat-articles-ui'>
        <button
          data-testid='article-button-different'
          onClick={() => props.onChangeArticle?.('article-456')}
          type='button'
        >
          Change Article
        </button>
      </div>
    ));

    const props = {
      displayMode: ArticleDisplayModes.All,
      selectedArticles: mockArticles,
      maximumArticles: 5,
    };

    const { getByTestId } = render(<FlatArticlesElement {...props} />);

    fireEvent.click(getByTestId('article-button-different'));

    expect(mockSendViewEvent).toHaveBeenCalledWith({
      article_id: 'article-456',
      page_id: 'current-page',
      page_title: 'Current Page',
      event_type: 'view',
      device_type: 'desktop',
    });

    expect(mockSendGAEvent).toHaveBeenCalledWith('faq_article_view', {
      article_id: 'article-456',
      article_title: 'Test Article 2',
      url: 'https://example.com/test-page',
      source: 'article_list',
    });
  });

  it('should handle tablet device type', () => {
    // Reset the mock to use the default button
    const { FlatArticlesElement: MockUI } = require('@resola-ai/ui/components/PageBuilder');
    MockUI.mockImplementation((props) => (
      <div data-testid='flat-articles-ui'>
        <button
          data-testid='article-button'
          onClick={() => props.onChangeArticle?.('article-123')}
          type='button'
        >
          Change Article
        </button>
      </div>
    ));

    mockUseViewport.mockReturnValue('tablet');

    const props = {
      displayMode: ArticleDisplayModes.All,
      selectedArticles: mockArticles,
      maximumArticles: 5,
    };

    const { getByTestId } = render(<FlatArticlesElement {...props} />);

    fireEvent.click(getByTestId('article-button'));

    expect(mockSendViewEvent).toHaveBeenCalledWith({
      article_id: 'article-123',
      page_id: 'current-page',
      page_title: 'Current Page',
      event_type: 'view',
      device_type: 'tablet',
    });
  });

  it('should handle undefined device type', () => {
    // Reset the mock to use the default button
    const { FlatArticlesElement: MockUI } = require('@resola-ai/ui/components/PageBuilder');
    MockUI.mockImplementation((props) => (
      <div data-testid='flat-articles-ui'>
        <button
          data-testid='article-button'
          onClick={() => props.onChangeArticle?.('article-123')}
          type='button'
        >
          Change Article
        </button>
      </div>
    ));

    mockUseViewport.mockReturnValue('desktop');

    const props = {
      displayMode: ArticleDisplayModes.All,
      selectedArticles: mockArticles,
      maximumArticles: 5,
    };

    const { getByTestId } = render(<FlatArticlesElement {...props} />);

    fireEvent.click(getByTestId('article-button'));

    expect(mockSendViewEvent).toHaveBeenCalledWith({
      article_id: 'article-123',
      page_id: 'current-page',
      page_title: 'Current Page',
      event_type: 'view',
      device_type: 'desktop',
    });
  });

  it('should handle article with missing label property', () => {
    const articlesWithMissingLabel = [
      { value: 'article-123', content: 'Content 1' }, // missing label
      { value: 'article-456', label: 'Test Article 2', content: 'Content 2' },
    ];

    mockUseArticles.mockReturnValue({
      articles: articlesWithMissingLabel,
      isFetching: false,
    });

    const { FlatArticlesElement: MockUI } = require('@resola-ai/ui/components/PageBuilder');

    // Mock the UI component to call with article that has missing label
    MockUI.mockImplementation((props) => (
      <div data-testid='flat-articles-ui'>
        <button
          data-testid='article-button-missing-label'
          onClick={() => props.onChangeArticle?.('article-123')}
          type='button'
        >
          Change Article
        </button>
      </div>
    ));

    const props = {
      displayMode: ArticleDisplayModes.All,
      selectedArticles: articlesWithMissingLabel,
      maximumArticles: 5,
    };

    const { getByTestId } = render(<FlatArticlesElement {...props} />);

    fireEvent.click(getByTestId('article-button-missing-label'));

    expect(mockSendViewEvent).toHaveBeenCalledWith({
      article_id: 'article-123',
      page_id: 'current-page',
      page_title: 'Current Page',
      event_type: 'view',
      device_type: 'desktop',
    });

    // Should still send GA event but with undefined article_title when label is missing
    expect(mockSendGAEvent).toHaveBeenCalledWith('faq_article_view', {
      article_id: 'article-123',
      article_title: undefined,
      url: 'https://example.com/test-page',
      source: 'article_list',
    });
  });

  it('should handle undefined onChangeArticle callback', () => {
    const { FlatArticlesElement: MockUI } = require('@resola-ai/ui/components/PageBuilder');

    // Mock the UI component without onChangeArticle callback
    MockUI.mockImplementation((props) => (
      <div data-testid='flat-articles-ui'>
        <button
          data-testid='article-button-no-callback'
          onClick={() => props.onChangeArticle?.()}
          type='button'
        >
          Change Article
        </button>
      </div>
    ));

    const props = {
      displayMode: ArticleDisplayModes.All,
      selectedArticles: mockArticles,
      maximumArticles: 5,
    };

    const { getByTestId } = render(<FlatArticlesElement {...props} />);

    // Should not throw when calling without arguments
    expect(() => fireEvent.click(getByTestId('article-button-no-callback'))).not.toThrow();
  });

  it('should handle null page context', () => {
    // Reset the mock to use the default button
    const { FlatArticlesElement: MockUI } = require('@resola-ai/ui/components/PageBuilder');
    MockUI.mockImplementation((props) => (
      <div data-testid='flat-articles-ui'>
        <button
          data-testid='article-button'
          onClick={() => props.onChangeArticle?.('article-123')}
          type='button'
        >
          Change Article
        </button>
      </div>
    ));

    mockUsePageContext.mockReturnValue({
      page: null as any,
      pages: [],
    });

    const props = {
      displayMode: ArticleDisplayModes.All,
      selectedArticles: mockArticles,
      maximumArticles: 5,
    };

    const { getByTestId } = render(<FlatArticlesElement {...props} />);

    fireEvent.click(getByTestId('article-button'));

    expect(mockSendViewEvent).toHaveBeenCalledWith({
      article_id: 'article-123',
      page_id: undefined,
      page_title: undefined,
      event_type: 'view',
      device_type: 'desktop',
    });
  });

  it('should handle completely undefined page context', () => {
    // Reset the mock to use the default button
    const { FlatArticlesElement: MockUI } = require('@resola-ai/ui/components/PageBuilder');
    MockUI.mockImplementation((props) => (
      <div data-testid='flat-articles-ui'>
        <button
          data-testid='article-button'
          onClick={() => props.onChangeArticle?.('article-123')}
          type='button'
        >
          Change Article
        </button>
      </div>
    ));

    mockUsePageContext.mockReturnValue({
      page: undefined as any,
      pages: [],
    });

    const props = {
      displayMode: ArticleDisplayModes.All,
      selectedArticles: mockArticles,
      maximumArticles: 5,
    };

    const { getByTestId } = render(<FlatArticlesElement {...props} />);

    fireEvent.click(getByTestId('article-button'));

    expect(mockSendViewEvent).toHaveBeenCalledWith({
      article_id: 'article-123',
      page_id: undefined,
      page_title: undefined,
      event_type: 'view',
      device_type: 'desktop',
    });
  });
});
