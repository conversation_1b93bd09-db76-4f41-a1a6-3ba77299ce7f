import SearchElement from '@/components/SearchElement';
import { sendGAEvent } from '@/utils/page-utils';
import { render } from '@testing-library/react';

// Mock the utility function
jest.mock('@/utils/page-utils', () => ({
  sendGAEvent: jest.fn(),
}));

// Mock the UI component
jest.mock('@resola-ai/ui/components/PageBuilder', () => ({
  SearchElement: jest.fn((props) => {
    // Simulate the UI component structure with onSearch prop
    return (
      <div data-testid='search-element'>
        <button
          type='button'
          data-testid='search-trigger'
          onClick={() =>
            props.onSearch &&
            props.onSearch({
              query: 'test query',
              articles: [1, 2, 3],
            })
          }
        >
          Search
        </button>
      </div>
    );
  }),
}));

const mockSendGAEvent = sendGAEvent as jest.MockedFunction<typeof sendGAEvent>;

describe('SearchElement', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render without crashing', () => {
    expect(() => render(<SearchElement />)).not.toThrow();
  });

  it('should render the SearchElementUI component', () => {
    const { getByTestId } = render(<SearchElement />);

    expect(getByTestId('search-element')).toBeInTheDocument();
  });

  it('should pass all props to SearchElementUI', () => {
    const testProps = {
      placeholder: 'Search articles...',
      variant: 'outlined',
      size: 'large',
      customProp: 'customValue',
    };

    expect(() => render(<SearchElement {...testProps} />)).not.toThrow();
  });

  it('should provide onSearch handler to SearchElementUI', () => {
    const { getByTestId } = render(<SearchElement />);

    // The mock SearchElementUI should have received onSearch prop
    const searchTrigger = getByTestId('search-trigger');
    expect(searchTrigger).toBeInTheDocument();
  });

  describe('search event handling', () => {
    it('should call sendGAEvent when search is triggered', () => {
      const { getByTestId } = render(<SearchElement />);

      const searchTrigger = getByTestId('search-trigger');
      searchTrigger.click();

      expect(mockSendGAEvent).toHaveBeenCalledWith('faq_search', {
        search_term: 'test query',
        search_result: 3,
      });
    });

    it('should handle valid search scenarios', () => {
      // Test the handleSearch function by importing it directly
      const { sendGAEvent } = require('@/utils/page-utils');

      // Create a standalone handleSearch function like in the component
      const handleSearch = (data: Record<string, any>) => {
        sendGAEvent('faq_search', { search_term: data.query, search_result: data.articles.length });
      };

      // Clear previous calls
      mockSendGAEvent.mockClear();

      // Test empty query with empty articles
      handleSearch({ query: '', articles: [] });
      expect(mockSendGAEvent).toHaveBeenCalledWith('faq_search', {
        search_term: '',
        search_result: 0,
      });

      // Test search with multiple articles
      mockSendGAEvent.mockClear();
      handleSearch({ query: 'react hooks', articles: new Array(15).fill({}) });
      expect(mockSendGAEvent).toHaveBeenCalledWith('faq_search', {
        search_term: 'react hooks',
        search_result: 15,
      });

      // Test single article
      mockSendGAEvent.mockClear();
      handleSearch({ query: 'javascript', articles: [{}] });
      expect(mockSendGAEvent).toHaveBeenCalledWith('faq_search', {
        search_term: 'javascript',
        search_result: 1,
      });
    });
  });

  describe('component integration', () => {
    it('should maintain all props when passing to UI component', () => {
      const complexProps = {
        placeholder: 'Search knowledge base...',
        autoComplete: 'off',
        debounceMs: 300,
        maxResults: 10,
        theme: 'dark',
        onFocus: jest.fn(),
        onBlur: jest.fn(),
        className: 'custom-search',
        style: { width: '100%' },
      };

      expect(() => render(<SearchElement {...complexProps} />)).not.toThrow();
    });

    it('should handle edge case props', () => {
      const edgeProps = {
        null: null,
        undefined: undefined,
        emptyString: '',
        zero: 0,
        false: false,
        emptyArray: [],
        emptyObject: {},
      };

      expect(() => render(<SearchElement {...edgeProps} />)).not.toThrow();
    });
  });
});
