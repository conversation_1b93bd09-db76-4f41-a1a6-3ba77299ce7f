import { render, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { getSiteData, searchArticles } from '@/api/site';
import SearchResultElement from '@/components/SearchResultElement';
import { sendGAEvent } from '@/utils/page-utils';

// Mock all dependencies
jest.mock('@/api/site', () => ({
  searchArticles: jest.fn(),
  getSiteData: jest.fn(),
}));

jest.mock('@/utils/page-utils', () => ({
  sendGAEvent: jest.fn(),
}));

jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn(),
}));

jest.mock('@resola-ai/ui/components/PageBuilder/FAQSearchResultElement', () => ({
  SearchResultElement: jest.fn((props) => (
    <div data-testid='search-result-ui'>
      <div data-testid='search-results'>{JSON.stringify(props.searchResults)}</div>
      <div data-testid='loading-state'>{props.isLoading ? 'Loading' : 'Not Loading'}</div>
    </div>
  )),
}));

// Mock environment variable
const originalEnv = process.env;

const mockSearchArticles = searchArticles as jest.MockedFunction<typeof searchArticles>;
const mockGetSiteData = getSiteData as jest.MockedFunction<typeof getSiteData>;
const mockSendGAEvent = sendGAEvent as jest.MockedFunction<typeof sendGAEvent>;

describe('SearchResultElement', () => {
  const mockSearchParams = {
    get: jest.fn(),
  };

  const mockArticleData = [
    {
      id: 'article-1',
      title: 'Test Article 1',
      description: 'Description 1',
      base_id: 'base-1',
    },
    {
      id: 'article-2',
      title: 'Test Article 2',
      description: 'Description 2',
      base_id: 'base-2',
    },
  ];

  const mockSiteData = [
    {
      element_id: 'element-123',
      articles: [{ value: 'article-1' }, { value: 'article-2' }],
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    process.env = { ...originalEnv };

    // Mock useSearchParams
    const { useSearchParams } = require('next/navigation');
    useSearchParams.mockReturnValue(mockSearchParams);

    // Default mock returns
    mockSearchParams.get.mockImplementation((key) => {
      const params = {
        faq_query: 'test query',
        element_id: 'element-123',
      };
      return params[key] || null;
    });

    mockGetSiteData.mockResolvedValue(mockSiteData);
    mockSearchArticles.mockResolvedValue(mockArticleData);
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  it('should render without crashing', () => {
    expect(() => render(<SearchResultElement />)).not.toThrow();
  });

  it('should render the SearchResultElementUI component', () => {
    const { getByTestId } = render(<SearchResultElement />);
    expect(getByTestId('search-result-ui')).toBeInTheDocument();
  });

  it('should handle missing search query', () => {
    mockSearchParams.get.mockImplementation((key) => {
      if (key === 'faq_query') return null;
      if (key === 'element_id') return 'element-123';
      return null;
    });

    const { getByTestId } = render(<SearchResultElement />);

    const searchResults = getByTestId('search-results');
    expect(searchResults.textContent).toBe('[]');
  });

  it('should perform search when query is provided', async () => {
    const props = {
      siteId: 'site-123',
    };

    render(<SearchResultElement {...props} />);

    await waitFor(() => {
      expect(mockSearchArticles).toHaveBeenCalledWith({
        query: 'test query',
        site_id: 'site-123',
      });
    });
  });

  it('should use environment variable when siteId prop is not provided', async () => {
    process.env.NEXT_PUBLIC_SITE_ID = 'env-site-123';

    render(<SearchResultElement />);

    await waitFor(() => {
      expect(mockSearchArticles).toHaveBeenCalledWith({
        query: 'test query',
        site_id: 'env-site-123',
      });
    });
  });

  it('should handle missing siteId gracefully', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    // No siteId prop and no environment variable
    process.env.NEXT_PUBLIC_SITE_ID = undefined;

    render(<SearchResultElement />);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('No siteId available for search');
    });

    consoleSpy.mockRestore();
  });

  it('should format search results correctly', async () => {
    const props = {
      siteId: 'site-123',
    };

    const { getByTestId } = render(<SearchResultElement {...props} />);

    await waitFor(() => {
      const searchResults = getByTestId('search-results');
      const results = JSON.parse(searchResults.textContent);

      expect(results).toEqual([
        {
          title: 'Test Article 1',
          description: 'Description 1',
          faq_article_id: 'article-1',
          element_id: 'element-123',
          faq_base_id: 'base-1',
        },
        {
          title: 'Test Article 2',
          description: 'Description 2',
          faq_article_id: 'article-2',
          element_id: 'element-123',
          faq_base_id: 'base-2',
        },
      ]);
    });
  });

  it('should send Google Analytics event on successful search', async () => {
    const props = {
      siteId: 'site-123',
    };

    render(<SearchResultElement {...props} />);

    await waitFor(() => {
      expect(mockSendGAEvent).toHaveBeenCalledWith('faq_search', {
        search_term: 'test query',
        search_result: 2,
      });
    });
  });

  it('should handle search API errors gracefully', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    mockSearchArticles.mockRejectedValue(new Error('Search API Error'));

    const props = {
      siteId: 'site-123',
    };

    const { getByTestId } = render(<SearchResultElement {...props} />);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Error performing search:', expect.any(Error));

      const searchResults = getByTestId('search-results');
      expect(searchResults.textContent).toBe('[]');
    });

    consoleSpy.mockRestore();
  });

  it('should handle searchArticles API errors gracefully', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    mockSearchArticles.mockRejectedValue(new Error('Search API Error'));

    const props = {
      siteId: 'site-123',
    };

    const { getByTestId } = render(<SearchResultElement {...props} />);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Error performing search:', expect.any(Error));

      const searchResults = getByTestId('search-results');
      expect(searchResults.textContent).toBe('[]');
    });

    consoleSpy.mockRestore();
  });

  it('should handle empty siteData response', async () => {
    mockGetSiteData.mockResolvedValue([]);

    const props = {
      siteId: 'site-123',
    };

    render(<SearchResultElement {...props} />);

    await waitFor(() => {
      expect(mockSearchArticles).toHaveBeenCalledWith({
        query: 'test query',
        site_id: 'site-123',
      });
    });
  });

  it('should handle element not found in siteData', async () => {
    mockGetSiteData.mockResolvedValue([
      {
        element_id: 'different-element',
        articles: [{ value: 'article-1' }],
      },
    ]);

    const props = {
      siteId: 'site-123',
    };

    render(<SearchResultElement {...props} />);

    await waitFor(() => {
      expect(mockSearchArticles).toHaveBeenCalledWith({
        query: 'test query',
        site_id: 'site-123',
      });
    });
  });

  it('should show loading state during search', async () => {
    // Make the API call hang to test loading state
    let resolveSearch;
    const searchPromise = new Promise((resolve) => {
      resolveSearch = resolve;
    });
    mockSearchArticles.mockReturnValue(searchPromise);

    const props = {
      siteId: 'site-123',
    };

    const { getByTestId } = render(<SearchResultElement {...props} />);

    // Should show loading initially
    await waitFor(() => {
      const loadingState = getByTestId('loading-state');
      expect(loadingState.textContent).toBe('Loading');
    });

    // Resolve the search
    resolveSearch(mockArticleData);

    // Should stop loading
    await waitFor(() => {
      const loadingState = getByTestId('loading-state');
      expect(loadingState.textContent).toBe('Not Loading');
    });
  });

  it('should handle different search query changes', async () => {
    const props = {
      siteId: 'site-123',
    };

    // First render with initial query
    const { rerender } = render(<SearchResultElement {...props} />);

    await waitFor(() => {
      expect(mockSearchArticles).toHaveBeenCalledWith({
        query: 'test query',
        site_id: 'site-123',
      });
    });

    // Change the search query
    mockSearchParams.get.mockImplementation((key) => {
      const params = {
        faq_query: 'new query',
        element_id: 'element-123',
      };
      return params[key] || null;
    });

    mockSearchArticles.mockClear();

    // Rerender to trigger new search
    rerender(<SearchResultElement {...props} />);

    await waitFor(() => {
      expect(mockSearchArticles).toHaveBeenCalledWith({
        query: 'new query',
        site_id: 'site-123',
      });
    });
  });

  it('should pass all props to UI component', async () => {
    const props = {
      siteId: 'site-123',
      maxWidth: '800px',
      backgroundColor: '#ffffff',
      categoryType: 'grid',
      illustrationType: 'icon',
      iconSize: 'large',
      iconColor: '#333333',
      iconBgColor: '#f0f0f0',
      hasMaxWidth: true,
      articleDetailSlug: '/article-detail',
      borderColor: '#dddddd',
      padding: '16px',
      itemBackgroundColor: '#fafafa',
    };

    const { getByTestId } = render(<SearchResultElement {...props} />);

    // Should render UI component with all props passed through
    expect(getByTestId('search-result-ui')).toBeInTheDocument();
  });

  it('should handle empty search results', async () => {
    mockSearchArticles.mockResolvedValue([]);

    const props = {
      siteId: 'site-123',
    };

    const { getByTestId } = render(<SearchResultElement {...props} />);

    await waitFor(() => {
      const searchResults = getByTestId('search-results');
      expect(searchResults.textContent).toBe('[]');

      expect(mockSendGAEvent).toHaveBeenCalledWith('faq_search', {
        search_term: 'test query',
        search_result: 0,
      });
    });
  });
});
