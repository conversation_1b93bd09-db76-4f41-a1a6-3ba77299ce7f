import { generateShortenUrl, getArticleDetail, sendViewEvent } from '@/api/site';
import ArticleDetailElement from '@/components/ArticleDetailElement';
import { useArticleDetail } from '@/hooks';
import { useViewport } from '@/hooks/useViewport';
import { usePageContext } from '@/providers';
import { sendGAEvent } from '@/utils/page-utils';
import '@testing-library/jest-dom';
import { fireEvent, render, waitFor } from '@testing-library/react';

// Mock all dependencies
jest.mock('@/api/site', () => ({
  generateShortenUrl: jest.fn(),
  getArticleDetail: jest.fn(),
  sendViewEvent: jest.fn(),
}));

jest.mock('@/hooks/useViewport', () => ({
  useViewport: jest.fn(),
}));

jest.mock('@/hooks', () => ({
  useArticleDetail: jest.fn(),
}));

jest.mock('@/providers', () => ({
  usePageContext: jest.fn(),
}));

jest.mock('@/utils/page-utils', () => ({
  sendGAEvent: jest.fn(),
}));

jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn(),
}));

jest.mock('@resola-ai/ui/components/PageBuilder', () => ({
  ArticleDetailElement: jest.fn((props) => (
    <div data-testid='article-detail-ui'>
      <div data-testid='article-data'>{JSON.stringify(props.article)}</div>
      <div data-testid='copied-state'>{props.isCopied ? 'Copied' : 'Not Copied'}</div>
      <div data-testid='feedback-status'>{props.feedbackStatus || 'No Feedback'}</div>
      <button data-testid='shorten-url-button' onClick={() => props.onShortenUrl?.()} type='button'>
        Share/Copy
      </button>
      <button
        data-testid='feedback-helpful-button'
        onClick={() => props.onFeedback?.('helpful')}
        type='button'
      >
        Helpful
      </button>
      <button
        data-testid='feedback-not-helpful-button'
        onClick={() => props.onFeedback?.('not_helpful')}
        type='button'
      >
        Not Helpful
      </button>
    </div>
  )),
}));

const mockGenerateShortenUrl = generateShortenUrl as jest.MockedFunction<typeof generateShortenUrl>;
const mockGetArticleDetail = getArticleDetail as jest.MockedFunction<typeof getArticleDetail>;
const mockSendViewEvent = sendViewEvent as jest.MockedFunction<typeof sendViewEvent>;
const mockUseViewport = useViewport as jest.MockedFunction<typeof useViewport>;
const mockUseArticleDetail = useArticleDetail as jest.MockedFunction<typeof useArticleDetail>;
const mockUsePageContext = usePageContext as jest.MockedFunction<typeof usePageContext>;
const mockSendGAEvent = sendGAEvent as jest.MockedFunction<typeof sendGAEvent>;

describe('ArticleDetailElement', () => {
  const mockSearchParams = {
    get: jest.fn(),
  };

  const mockArticle = {
    id: 'article-123',
    title: 'Test Article',
    content_raw: 'Article content',
    description: 'Article description',
    updated_at: '2023-09-21',
  };

  const mockPages = [
    { id: 'page-1', name: 'Page 1', url: '/page-1', type: 'faq_search_result' },
    { id: 'page-2', name: 'Page 2', url: '/page-2', type: 'faq_category_list' },
  ];

  const mockPage = { id: 'current-page', name: 'Current Page' };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock useSearchParams
    const { useSearchParams } = require('next/navigation');
    useSearchParams.mockReturnValue(mockSearchParams);

    // Default search params
    mockSearchParams.get.mockImplementation((key) => {
      const params = {
        faq_base_id: 'base-123',
        faq_article_id: 'article-123',
        ref: 'search',
      };
      return params[key] || null;
    });

    // Default mocks
    mockUseViewport.mockReturnValue('desktop');
    mockUseArticleDetail.mockReturnValue({
      article: mockArticle,
      isValidating: false,
    });
    mockUsePageContext.mockReturnValue({
      page: mockPage,
      pages: mockPages,
    });

    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(),
        setItem: jest.fn(),
      },
      writable: true,
    });

    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: { href: 'https://example.com/test-page' },
      writable: true,
    });

    // Mock document.referrer to match search result page
    Object.defineProperty(document, 'referrer', {
      value: 'https://example.com/page-1',
      writable: true,
    });

    // Set up default mock returns
    mockGenerateShortenUrl.mockResolvedValue({ id: 'short-url-123' });
  });

  it('should render without crashing', () => {
    expect(() => render(<ArticleDetailElement />)).not.toThrow();
  });

  it('should render the ArticleDetailElementUI component', () => {
    const { getByTestId } = render(<ArticleDetailElement />);
    expect(getByTestId('article-detail-ui')).toBeInTheDocument();
  });

  it('should fetch article details when baseId and articleId are provided', async () => {
    render(<ArticleDetailElement />);

    await waitFor(() => {
      expect(mockUseArticleDetail).toHaveBeenCalledWith('base-123', 'article-123');
    });
  });

  it('should send view event when article is loaded', async () => {
    render(<ArticleDetailElement />);

    await waitFor(() => {
      expect(mockSendViewEvent).toHaveBeenCalledWith({
        article_id: 'article-123',
        page_id: 'current-page',
        page_title: 'Current Page',
        event_type: 'view',
        device_type: 'desktop',
      });
    });
  });

  it('should send GA event when article is loaded from search', async () => {
    render(<ArticleDetailElement />);

    await waitFor(() => {
      expect(mockSendGAEvent).toHaveBeenCalledWith('faq_article_view', {
        article_id: 'article-123',
        article_title: 'Test Article',
        url: 'https://example.com/test-page',
        source: 'search',
      });
    });
  });

  it('should send GA event with correct source when ref is quick_search', async () => {
    mockSearchParams.get.mockImplementation((key) => {
      const params = {
        faq_base_id: 'base-123',
        faq_article_id: 'article-123',
        ref: 'quick_search',
      };
      return params[key] || null;
    });

    render(<ArticleDetailElement />);

    await waitFor(() => {
      expect(mockSendGAEvent).toHaveBeenCalledWith('faq_article_view', {
        article_id: 'article-123',
        article_title: 'Test Article',
        url: 'https://example.com/test-page',
        source: 'search',
      });
    });
  });

  it('should send GA event with category source when referrer is from category page', async () => {
    Object.defineProperty(document, 'referrer', {
      value: 'https://example.com/page-2',
      writable: true,
    });

    render(<ArticleDetailElement />);

    await waitFor(() => {
      expect(mockSendGAEvent).toHaveBeenCalledWith('faq_article_view', {
        article_id: 'article-123',
        article_title: 'Test Article',
        url: 'https://example.com/test-page',
        source: 'category',
      });
    });
  });

  it('should send GA event with direct source when no specific referrer', async () => {
    Object.defineProperty(document, 'referrer', {
      value: 'https://external-site.com',
      writable: true,
    });

    render(<ArticleDetailElement />);

    await waitFor(() => {
      expect(mockSendGAEvent).toHaveBeenCalledWith('faq_article_view', {
        article_id: 'article-123',
        article_title: 'Test Article',
        url: 'https://example.com/test-page',
        source: 'direct',
      });
    });
  });

  it('should load previous feedback from localStorage', async () => {
    const localStorageMock = {
      getItem: jest.fn().mockReturnValue('helpful'),
      setItem: jest.fn(),
    };
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true,
    });

    const { getByTestId } = render(<ArticleDetailElement />);

    await waitFor(() => {
      expect(localStorageMock.getItem).toHaveBeenCalledWith('feedback_article-123');
      expect(getByTestId('feedback-status')).toHaveTextContent('helpful');
    });
  });

  it('should handle feedback submission', async () => {
    const { getByTestId } = render(<ArticleDetailElement />);

    await waitFor(() => {
      expect(getByTestId('article-detail-ui')).toBeInTheDocument();
    });

    fireEvent.click(getByTestId('feedback-helpful-button'));

    expect(mockSendViewEvent).toHaveBeenCalledWith({
      article_id: 'article-123',
      page_id: 'current-page',
      page_title: 'Current Page',
      event_type: 'helpful',
      device_type: 'desktop',
    });
  });

  it('should save feedback to localStorage', async () => {
    const localStorageMock = {
      getItem: jest.fn().mockReturnValue(null),
      setItem: jest.fn(),
    };
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true,
    });

    const { getByTestId } = render(<ArticleDetailElement />);

    await waitFor(() => {
      expect(getByTestId('article-detail-ui')).toBeInTheDocument();
    });

    fireEvent.click(getByTestId('feedback-not-helpful-button'));

    expect(localStorageMock.setItem).toHaveBeenCalledWith('feedback_article-123', 'not_helpful');
  });

  it('should handle shorten URL generation', async () => {
    const { getByTestId } = render(<ArticleDetailElement />);

    await waitFor(() => {
      expect(getByTestId('article-detail-ui')).toBeInTheDocument();
    });

    fireEvent.click(getByTestId('shorten-url-button'));

    await waitFor(() => {
      expect(mockGenerateShortenUrl).toHaveBeenCalledWith({
        url: 'https://example.com/test-page',
        resource_id: process.env.NEXT_PUBLIC_SITE_ID,
        resource_type: 'site',
      });
    });
  });

  it('should handle shorten URL generation in embedded mode', async () => {
    // Mock embedded environment
    Object.defineProperty(window, 'top', {
      value: { location: { href: 'https://parent.com' } },
      writable: true,
    });
    Object.defineProperty(window, 'BASE_PARENT_URL', {
      value: 'https://parent.com/embed',
      writable: true,
    });

    const { getByTestId } = render(<ArticleDetailElement />);

    await waitFor(() => {
      expect(getByTestId('article-detail-ui')).toBeInTheDocument();
    });

    fireEvent.click(getByTestId('shorten-url-button'));

    await waitFor(() => {
      expect(mockGenerateShortenUrl).toHaveBeenCalledWith({
        url: 'https://parent.com/embed',
        resource_id: process.env.NEXT_PUBLIC_SITE_ID,
        resource_type: 'site',
      });
    });
  });

  it('should not fetch article details when baseId or articleId is missing', () => {
    mockSearchParams.get.mockReturnValue(null);

    render(<ArticleDetailElement />);

    expect(mockUseArticleDetail).toHaveBeenCalledWith(null, null);
  });
});
