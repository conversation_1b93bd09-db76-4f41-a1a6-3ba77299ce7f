import { render, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { getCategories } from '@/api/site';
import CategoryTreeElement from '@/components/CategoryTreeElement';
import { useViewport } from '@/hooks/useViewport';
import { findNestedCategoryOrArticleById } from '@/utils/page-utils';

// Mock all dependencies
jest.mock('@/api/site', () => ({
  getCategories: jest.fn(),
}));

jest.mock('@/utils/page-utils', () => ({
  findNestedCategoryOrArticleById: jest.fn(),
}));

jest.mock('@/hooks/useViewport', () => ({
  useViewport: jest.fn(),
}));

jest.mock('@/hooks', () => ({
  useCategoriesData: jest.fn(),
}));

jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

jest.mock('lodash', () => ({
  isEmpty: jest.fn(),
}));

jest.mock('@resola-ai/ui/components/PageBuilder', () => ({
  CategoryTreeElement: jest.fn((props) => (
    <div data-testid='category-tree-ui'>
      <div data-testid='categories-data'>{JSON.stringify(props.categories || [])}</div>
      <div data-testid='selected-element'>{props.selectedElement}</div>
      <div data-testid='selected-article'>{props.selectedArticle}</div>
      <div data-testid='selected-category'>{props.selectedCategory}</div>
      <div data-testid='selected-sub-category'>{props.selectedSubCategory}</div>
      <div data-testid='device-type'>{props.deviceType}</div>
    </div>
  )),
}));

const mockGetCategories = getCategories as jest.MockedFunction<typeof getCategories>;
const mockFindNestedCategoryOrArticleById = findNestedCategoryOrArticleById as jest.MockedFunction<
  typeof findNestedCategoryOrArticleById
>;
const mockUseViewport = useViewport as jest.MockedFunction<typeof useViewport>;
const mockUseCategoriesData = require('@/hooks').useCategoriesData as jest.MockedFunction<any>;

describe('CategoryTreeElement', () => {
  const mockRouter = {
    query: {
      element_id: 'element-123',
      faq_article_id: 'article-123',
      faq_category_id: 'cat-123',
      faq_sub_category_id: 'subcat-123',
    } as any,
    push: jest.fn(),
    replace: jest.fn(),
    reload: jest.fn(),
    back: jest.fn(),
    prefetch: jest.fn(),
    beforePopState: jest.fn(),
    events: {
      on: jest.fn(),
      off: jest.fn(),
      emit: jest.fn(),
    },
    isFallback: false,
    isReady: true,
  };

  const mockCategories = [
    {
      id: 'cat-1',
      name: 'Category 1',
      articles: [],
      categories: [
        {
          id: 'subcat-1',
          name: 'Sub Category 1',
          articles: [{ id: 'article-1', title: 'Article 1' }],
        },
      ],
    },
    {
      id: 'cat-2',
      name: 'Category 2',
      articles: [],
    },
  ];

  const mockSelectedCategories = {
    categories: mockCategories,
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock useRouter
    const { useRouter } = require('next/router');
    useRouter.mockReturnValue(mockRouter);

    // Mock lodash isEmpty
    const { isEmpty } = require('lodash');
    isEmpty.mockReturnValue(false);

    // Default mocks
    mockGetCategories.mockResolvedValue(mockCategories);
    mockUseViewport.mockReturnValue('desktop');
    mockUseCategoriesData.mockReturnValue({
      categories: mockCategories,
      isValidating: false,
    });
    mockFindNestedCategoryOrArticleById.mockReturnValue({
      categories: mockSelectedCategories,
      category: null,
      subCategory: null,
      article: null,
    });
  });

  it('should render without crashing', () => {
    expect(() => render(<CategoryTreeElement />)).not.toThrow();
  });

  it('should fetch categories when siteId is provided', async () => {
    const props = { siteId: 'site-123' };
    render(<CategoryTreeElement {...props} />);

    expect(mockUseCategoriesData).toHaveBeenCalledWith('site-123');
  });

  it('should not fetch categories when siteId is not provided', () => {
    render(<CategoryTreeElement />);
    expect(mockUseCategoriesData).toHaveBeenCalledWith(undefined);
  });

  it('should call findNestedCategoryOrArticleById with router query parameters', async () => {
    const props = { siteId: 'site-123' };
    render(<CategoryTreeElement {...props} />);

    await waitFor(() => {
      expect(mockFindNestedCategoryOrArticleById).toHaveBeenCalledWith(
        mockCategories,
        'cat-123',
        'subcat-123',
        'article-123',
        'element-123'
      );
    });
  });

  it('should render CategoryTreeElementUI when selectedCategories is not empty', async () => {
    const props = { siteId: 'site-123' };
    const { getByTestId } = render(<CategoryTreeElement {...props} />);

    await waitFor(() => {
      expect(getByTestId('category-tree-ui')).toBeInTheDocument();
    });
  });

  it('should return null when selectedCategories is empty', () => {
    const { isEmpty } = require('lodash');
    isEmpty.mockReturnValue(true);

    const { queryByTestId } = render(<CategoryTreeElement siteId='site-123' />);
    expect(queryByTestId('category-tree-ui')).not.toBeInTheDocument();
  });

  it('should pass correct router query parameters to UI component', async () => {
    const props = { siteId: 'site-123' };
    const { getByTestId } = render(<CategoryTreeElement {...props} />);

    await waitFor(() => {
      expect(getByTestId('selected-element').textContent).toBe('element-123');
      expect(getByTestId('selected-article').textContent).toBe('article-123');
      expect(getByTestId('selected-category').textContent).toBe('cat-123');
      expect(getByTestId('selected-sub-category').textContent).toBe('subcat-123');
    });
  });

  it('should pass device type from useViewport to UI component', async () => {
    mockUseViewport.mockReturnValue('mobile');

    const props = { siteId: 'site-123' };
    const { getByTestId } = render(<CategoryTreeElement {...props} />);

    await waitFor(() => {
      expect(getByTestId('device-type').textContent).toBe('mobile');
    });
  });

  it('should pass categories data to UI component', async () => {
    const props = { siteId: 'site-123' };
    const { getByTestId } = render(<CategoryTreeElement {...props} />);

    await waitFor(() => {
      const categoriesData = getByTestId('categories-data');
      const parsedData = JSON.parse(categoriesData.textContent || '[]');
      expect(parsedData).toEqual(mockCategories);
    });
  });

  it('should handle missing router query parameters', async () => {
    mockRouter.query = {};

    const props = { siteId: 'site-123' };
    render(<CategoryTreeElement {...props} />);

    await waitFor(() => {
      expect(mockFindNestedCategoryOrArticleById).toHaveBeenCalledWith(
        mockCategories,
        '',
        '',
        '',
        undefined
      );
    });
  });

  it('should handle empty categories response', async () => {
    // Mock useCategoriesData to return empty categories
    mockUseCategoriesData.mockReturnValue({
      categories: [],
      isValidating: false,
    });

    // Reset router query for this test
    mockRouter.query = {
      element_id: 'element-123',
      faq_article_id: 'article-123',
      faq_category_id: 'cat-123',
      faq_sub_category_id: 'subcat-123',
    };

    const props = { siteId: 'site-123' };
    render(<CategoryTreeElement {...props} />);

    await waitFor(() => {
      expect(mockFindNestedCategoryOrArticleById).toHaveBeenCalledWith(
        [],
        'cat-123',
        'subcat-123',
        'article-123',
        'element-123'
      );
    });
  });

  it('should handle null categories response', async () => {
    // Mock useCategoriesData to return null categories
    mockUseCategoriesData.mockReturnValue({
      categories: null,
      isValidating: false,
    });

    const props = { siteId: 'site-123' };
    render(<CategoryTreeElement {...props} />);

    await waitFor(() => {
      expect(mockFindNestedCategoryOrArticleById).toHaveBeenCalledWith(
        [],
        'cat-123',
        'subcat-123',
        'article-123',
        'element-123'
      );
    });
  });

  it('should pass all styling props to UI component', async () => {
    const props = {
      siteId: 'site-123',
      padding: '20px',
      width: '300px',
      articleDetailSlug: 'article-detail',
      categoryListSlug: 'category-list',
      styles: { color: 'blue' },
      showRightDivider: true,
      dividerColor: '#ccc',
    };

    render(<CategoryTreeElement {...props} />);

    // Component should render without issues with all props
    await waitFor(() => {
      expect(document.querySelector('[data-testid="category-tree-ui"]')).toBeInTheDocument();
    });
  });

  it('should handle different viewport sizes', async () => {
    const viewportSizes = ['mobile', 'tablet', 'desktop'];

    for (const size of viewportSizes) {
      mockUseViewport.mockClear();
      mockUseViewport.mockReturnValue(size as any);

      // Use a separate container for each render to avoid DOM conflicts
      const { getByTestId, unmount } = render(<CategoryTreeElement siteId='site-123' />);

      await waitFor(() => {
        expect(getByTestId('device-type').textContent).toBe(size);
      });

      // Clean up the component before next iteration
      unmount();
    }
  });

  it('should update when siteId changes', async () => {
    // First render with site-123
    const { rerender } = render(<CategoryTreeElement siteId='site-123' />);

    // Verify useCategoriesData was called initially
    expect(mockUseCategoriesData).toHaveBeenCalledWith('site-123');

    // Clear the mock to track new calls
    mockUseCategoriesData.mockClear();

    // Change siteId
    rerender(<CategoryTreeElement siteId='site-456' />);

    // Verify useCategoriesData was called with new siteId
    await waitFor(() => {
      expect(mockUseCategoriesData).toHaveBeenCalledWith('site-456');
    });
  });

  it('should handle complex router query scenarios', async () => {
    mockRouter.query = {
      element_id: 'complex-element-123',
      faq_article_id: 'complex-article-456',
      faq_category_id: 'complex-cat-789',
      faq_sub_category_id: 'complex-subcat-101',
      other_param: 'should-be-ignored',
    };

    const props = { siteId: 'site-123' };
    render(<CategoryTreeElement {...props} />);

    await waitFor(() => {
      expect(mockFindNestedCategoryOrArticleById).toHaveBeenCalledWith(
        mockCategories,
        'complex-cat-789',
        'complex-subcat-101',
        'complex-article-456',
        'complex-element-123'
      );
    });
  });
});
