import Section from '@/components/Section';
import { render, screen } from '@testing-library/react';

describe('Section', () => {
  it('should render without crashing', () => {
    expect(() => render(<Section />)).not.toThrow();
  });

  it('should render children content', () => {
    render(
      <Section>
        <div data-testid='child-content'>Test Content</div>
      </Section>
    );

    expect(screen.getByTestId('child-content')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('should render multiple children', () => {
    render(
      <Section>
        <div data-testid='child-1'>First Child</div>
        <div data-testid='child-2'>Second Child</div>
        <span data-testid='child-3'>Third Child</span>
      </Section>
    );

    expect(screen.getByTestId('child-1')).toBeInTheDocument();
    expect(screen.getByTestId('child-2')).toBeInTheDocument();
    expect(screen.getByTestId('child-3')).toBeInTheDocument();
    expect(screen.getByText('First Child')).toBeInTheDocument();
    expect(screen.getByText('Second Child')).toBeInTheDocument();
    expect(screen.getByText('Third Child')).toBeInTheDocument();
  });

  it('should render string children', () => {
    render(<Section>Plain text content</Section>);

    expect(screen.getByText('Plain text content')).toBeInTheDocument();
  });

  it('should render with mixed content types', () => {
    render(
      <Section>
        Some text
        <strong data-testid='bold-text'>Bold text</strong>
        <div data-testid='div-content'>Div content</div>
      </Section>
    );

    expect(screen.getByText('Some text')).toBeInTheDocument();
    expect(screen.getByTestId('bold-text')).toBeInTheDocument();
    expect(screen.getByTestId('div-content')).toBeInTheDocument();
  });

  it('should handle empty children', () => {
    render(<Section />);
    // Should render without crashing - Section is just a fragment wrapper
    expect(document.body).toBeInTheDocument();
  });

  it('should handle null children', () => {
    render(<Section>{null}</Section>);
    expect(document.body).toBeInTheDocument();
  });

  it('should handle undefined children', () => {
    render(<Section>{undefined}</Section>);
    expect(document.body).toBeInTheDocument();
  });

  it('should handle complex nested children', () => {
    render(
      <Section>
        <div data-testid='level-1'>
          <div data-testid='level-2'>
            <span data-testid='level-3'>Deeply nested content</span>
          </div>
        </div>
      </Section>
    );

    expect(screen.getByTestId('level-1')).toBeInTheDocument();
    expect(screen.getByTestId('level-2')).toBeInTheDocument();
    expect(screen.getByTestId('level-3')).toBeInTheDocument();
    expect(screen.getByText('Deeply nested content')).toBeInTheDocument();
  });

  it('should handle props (even though they are not used)', () => {
    const props = {
      id: 'test-id',
      className: 'test-class',
      'data-custom': 'custom-value',
    };

    expect(() =>
      render(
        <Section {...props}>
          <div data-testid='content'>Content with props</div>
        </Section>
      )
    ).not.toThrow();

    expect(screen.getByTestId('content')).toBeInTheDocument();
  });
});
