import { render, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CategoryListElement from '@/components/CategoryListElement';

// Mock all dependencies
jest.mock('@/api/site', () => ({
  getCategories: jest.fn().mockResolvedValue([]),
}));

jest.mock('@/hooks', () => ({
  useCategoriesData: jest.fn(),
}));

jest.mock('@/utils/page-utils', () => ({
  findNestedCategoryOrArticleById: jest.fn().mockReturnValue({
    category: null,
    subCategory: null,
    article: null,
  }),
}));

jest.mock('next/router', () => ({
  useRouter: jest.fn().mockReturnValue({
    query: {},
    push: jest.fn(),
    pathname: '/test',
    route: '/test',
    asPath: '/test',
  }),
}));

jest.mock('@resola-ai/ui/components/PageBuilder', () => ({
  CategoryListElement: jest.fn((props) => (
    <div data-testid='category-list-ui'>
      Category List Component
      <div data-testid='selected-category'>
        {props.selectedCategory ? 'Has Category' : 'No Category'}
      </div>
      <div data-testid='handle-click'>{JSON.stringify(props.onItemClick)}</div>
    </div>
  )),
}));

// Mock window.location
const mockWindowLocation = {
  href: 'https://example.com/test',
};
Object.defineProperty(window, 'location', {
  value: mockWindowLocation,
  writable: true,
});

describe('CategoryListElement', () => {
  const mockUseCategoriesData = require('@/hooks').useCategoriesData;
  const mockFindNestedCategoryOrArticleById =
    require('@/utils/page-utils').findNestedCategoryOrArticleById;

  beforeEach(() => {
    jest.clearAllMocks();
    mockWindowLocation.href = 'https://example.com/test';

    // Default mock implementations
    mockUseCategoriesData.mockReturnValue({
      categories: [],
      isValidating: false,
    });
    mockFindNestedCategoryOrArticleById.mockReturnValue({
      category: null,
      subCategory: null,
      article: null,
    });
  });

  it('should render without crashing', () => {
    expect(() => render(<CategoryListElement />)).not.toThrow();
  });

  it('should render the CategoryListElementUI component', () => {
    const { getByTestId } = render(<CategoryListElement />);
    expect(getByTestId('category-list-ui')).toBeInTheDocument();
  });

  it('should handle props correctly', () => {
    const props = {
      siteId: 'site-123',
      type: 'grid',
      showDescription: true,
      showPicture: false,
      articleDetailSlug: '/article-detail',
    };

    expect(() => render(<CategoryListElement {...props} />)).not.toThrow();
  });

  it('should call useCategoriesData when siteId is provided', async () => {
    const props = {
      siteId: 'site-123',
    };

    render(<CategoryListElement {...props} />);

    await waitFor(() => {
      expect(mockUseCategoriesData).toHaveBeenCalledWith('site-123');
    });
  });

  it('should not call useCategoriesData when siteId is not provided', () => {
    render(<CategoryListElement />);

    expect(mockUseCategoriesData).toHaveBeenCalledWith(undefined);
  });

  it('should handle different siteId values', async () => {
    const { rerender } = render(<CategoryListElement siteId='site-1' />);

    await waitFor(() => {
      expect(mockUseCategoriesData).toHaveBeenCalledWith('site-1');
    });

    mockUseCategoriesData.mockClear();

    rerender(<CategoryListElement siteId='site-2' />);

    await waitFor(() => {
      expect(mockUseCategoriesData).toHaveBeenCalledWith('site-2');
    });
  });

  describe('handleClick navigation (lines 58-61)', () => {
    it('should pass onClickArticle function to UI component', () => {
      const { useRouter } = require('next/router');
      const mockRouter = {
        query: {
          element_id: 'element-1',
          faq_category_id: 'category-123',
          slug: 'test-page',
        },
        push: jest.fn(),
        pathname: '/test',
      };
      useRouter.mockReturnValue(mockRouter);

      const props = {
        siteId: 'site-123',
        articleDetailSlug: '/article-detail',
      };

      const {
        CategoryListElement: MockCategoryListElement,
      } = require('@resola-ai/ui/components/PageBuilder');

      render(<CategoryListElement {...props} />);

      // Verify that onClickArticle function is passed to the UI component
      const lastCall =
        MockCategoryListElement.mock.calls[MockCategoryListElement.mock.calls.length - 1];
      expect(typeof lastCall[0].onClickArticle).toBe('function');
    });

    it('should provide correct props structure to UI component', () => {
      const { useRouter } = require('next/router');
      const mockRouter = {
        query: {
          element_id: 'element-1',
          faq_category_id: 'category-123',
          slug: 'test-page',
        },
        push: jest.fn(),
        pathname: '/test',
      };
      useRouter.mockReturnValue(mockRouter);

      const props = {
        siteId: 'site-123',
        articleDetailSlug: '/article-detail',
        type: 'grid',
        showDescription: true,
        showPicture: false,
      };

      const {
        CategoryListElement: MockCategoryListElement,
      } = require('@resola-ai/ui/components/PageBuilder');

      render(<CategoryListElement {...props} />);

      // Verify that all expected props are passed to the UI component
      const lastCall =
        MockCategoryListElement.mock.calls[MockCategoryListElement.mock.calls.length - 1];
      const passedProps = lastCall[0];

      expect(passedProps).toHaveProperty('onClickArticle');
      expect(passedProps).toHaveProperty('type', 'grid');
      expect(passedProps).toHaveProperty('showDescription', true);
      expect(passedProps).toHaveProperty('showPicture', false);
      expect(typeof passedProps.onClickArticle).toBe('function');
    });

    it('should handle router query changes', () => {
      const { useRouter } = require('next/router');

      // Initial router state
      const mockRouter1 = {
        query: { element_id: 'element-1', slug: 'page-1' },
        push: jest.fn(),
        pathname: '/test',
      };
      useRouter.mockReturnValue(mockRouter1);

      const { rerender } = render(<CategoryListElement siteId='site-123' />);

      // Change router state
      const mockRouter2 = {
        query: { element_id: 'element-2', slug: 'page-2' },
        push: jest.fn(),
        pathname: '/test',
      };
      useRouter.mockReturnValue(mockRouter2);

      // Component should handle the change gracefully
      expect(() => rerender(<CategoryListElement siteId='site-123' />)).not.toThrow();
    });
  });

  it('should render with various props combinations', () => {
    const testProps = [
      { siteId: 'site-1', type: 'list' },
      { siteId: 'site-2', showDescription: true },
      { siteId: 'site-3', showPicture: true },
      { siteId: 'site-4', articleDetailSlug: '/custom-article' },
      {
        siteId: 'site-5',
        type: 'grid',
        padding: '16px',
        backgroundColor: '#f5f5f5',
        textColor: '#333',
      },
    ];

    testProps.forEach((props) => {
      expect(() => render(<CategoryListElement {...props} />)).not.toThrow();
    });
  });

  it('should handle missing props gracefully', () => {
    expect(() => render(<CategoryListElement />)).not.toThrow();
  });

  it('should handle empty or null props', () => {
    const testCases = [{ siteId: null }, { siteId: undefined }, { siteId: '' }, {}];

    testCases.forEach((props) => {
      expect(() => render(<CategoryListElement {...props} />)).not.toThrow();
    });
  });

  it('should work with router query parameters', () => {
    const { useRouter } = require('next/router');

    useRouter.mockReturnValue({
      query: {
        element_id: 'element-1',
        faq_category_id: 'category-123',
        faq_sub_category_id: 'sub-456',
        slug: 'test-page',
      },
      push: jest.fn(),
      pathname: '/test',
    });

    expect(() => render(<CategoryListElement siteId='site-123' />)).not.toThrow();
  });
});
