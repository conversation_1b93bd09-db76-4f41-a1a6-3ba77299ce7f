import Page<PERSON>enderer from '@/components/PageRenderer';
import { render, screen } from '@testing-library/react';
import type React from 'react';
import { mockIntegrations, mockPageProps } from '../fixtures/mockData';

// Mock the CraftJS Editor
jest.mock('@craftjs/core', () => ({
  Editor: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='craftjs-editor'>{children}</div>
  ),
  Frame: ({ data }: { data: string }) => <div data-testid='craftjs-frame' data-content={data} />,
}));

// Mock the Meta component
jest.mock('@/components/Meta', () => {
  return function MockMeta(props: any) {
    return <div data-testid='meta-component' data-props={JSON.stringify(props)} />;
  };
});

// Mock the ChatwindowElement component
jest.mock('@/components/ChatwindowElement', () => {
  return function MockChatwindowElement(props: any) {
    return <div data-testid='chatwindow-element' data-props={JSON.stringify(props)} />;
  };
});

// Mock updateGlobalBreakpoints
jest.mock('@resola-ai/ui/utils/pageBuilder', () => ({
  updateGlobalBreakpoints: jest.fn().mockReturnValue(true),
}));

// Mock getChatwindowIntegrationId
jest.mock('@/utils/page-utils', () => ({
  componentResolver: {},
  getChatwindowIntegrationId: jest.fn().mockReturnValue('chat123'),
}));

import { getChatwindowIntegrationId } from '@/utils/page-utils';
// Import the mocked functions
import { updateGlobalBreakpoints } from '@resola-ai/ui/utils/pageBuilder';

const mockUpdateGlobalBreakpoints = updateGlobalBreakpoints as jest.MockedFunction<
  typeof updateGlobalBreakpoints
>;
const mockGetChatwindowIntegrationId = getChatwindowIntegrationId as jest.MockedFunction<
  typeof getChatwindowIntegrationId
>;

describe('PageRenderer', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Clean up event listeners after each test
    jest.clearAllMocks();
  });

  it('should render without crashing', () => {
    render(<PageRenderer {...mockPageProps} />);

    expect(screen.getByTestId('craftjs-editor')).toBeInTheDocument();
    expect(screen.getByTestId('craftjs-frame')).toBeInTheDocument();
  });

  it('should return null when page is not provided', () => {
    const { container } = render(<PageRenderer {...mockPageProps} page={null as any} />);

    expect(container.firstChild).toBeNull();
  });

  it('should render Meta component with correct props', () => {
    render(<PageRenderer {...mockPageProps} />);

    const metaComponent = screen.getByTestId('meta-component');
    expect(metaComponent).toBeInTheDocument();

    const metaProps = JSON.parse(metaComponent.getAttribute('data-props') || '{}');
    expect(metaProps.title).toBe('Test Site - Test Page');
    expect(metaProps.description).toBe('Test page description');
    expect(metaProps.image).toBe('https://example.com/image.jpg');
    expect(metaProps.favicon).toBe('https://example.com/favicon.ico');
  });

  it('should handle missing site name in title', () => {
    const propsWithoutSite = { ...mockPageProps, site: undefined };
    render(<PageRenderer {...propsWithoutSite} />);

    const metaComponent = screen.getByTestId('meta-component');
    const metaProps = JSON.parse(metaComponent.getAttribute('data-props') || '{}');
    expect(metaProps.title).toBe('Test Page');
  });

  it('should use fallback values for Meta component', () => {
    const pageWithoutMetadata = {
      ...mockPageProps.page,
      metadata: {},
    };
    const propsWithFallbacks = {
      ...mockPageProps,
      page: pageWithoutMetadata,
      site: { ...mockPageProps.site, visual_assets: {} },
    };

    render(<PageRenderer {...propsWithFallbacks} />);

    const metaComponent = screen.getByTestId('meta-component');
    const metaProps = JSON.parse(metaComponent.getAttribute('data-props') || '{}');
    expect(metaProps.description).toBe('Default site description');
    expect(metaProps.favicon).toBe('https://example.com/default-favicon.ico');
  });

  it('should apply theme font family to container', () => {
    render(<PageRenderer {...mockPageProps} />);

    const container = screen.getByTestId('craftjs-editor').parentElement;
    expect(container).toHaveStyle({ fontFamily: "'Arial'" });
  });

  it('should render ChatwindowElement when integration is found', () => {
    render(<PageRenderer {...mockPageProps} />);

    expect(mockGetChatwindowIntegrationId).toHaveBeenCalledWith(
      mockIntegrations,
      mockPageProps.page
    );

    const chatwindowElement = screen.getByTestId('chatwindow-element');
    expect(chatwindowElement).toBeInTheDocument();

    const chatwindowProps = JSON.parse(chatwindowElement.getAttribute('data-props') || '{}');
    expect(chatwindowProps.boxId).toBe('chat123');
    expect(chatwindowProps.orgId).toBe('org123');
  });

  it('should not render ChatwindowElement when integration is not found', () => {
    mockGetChatwindowIntegrationId.mockReturnValueOnce('');

    render(<PageRenderer {...mockPageProps} />);

    expect(screen.queryByTestId('chatwindow-element')).not.toBeInTheDocument();
  });

  it('should handle getChatwindowIntegrationId error gracefully', () => {
    mockGetChatwindowIntegrationId.mockImplementationOnce(() => {
      throw new Error('Test error');
    });

    render(<PageRenderer {...mockPageProps} />);

    // Should still render the main components
    expect(screen.getByTestId('craftjs-editor')).toBeInTheDocument();
    expect(screen.queryByTestId('chatwindow-element')).not.toBeInTheDocument();
  });

  it('should send ready message to parent on mount', () => {
    render(<PageRenderer {...mockPageProps} />);

    expect(window.parent.postMessage).toHaveBeenCalledWith({ type: 'deca-iframe-ready' }, '*');
  });

  it('should pass page content to CraftJS Frame', () => {
    render(<PageRenderer {...mockPageProps} />);

    const frame = screen.getByTestId('craftjs-frame');
    expect(frame).toHaveAttribute('data-content', mockPageProps.page.content);
  });

  describe('responsive breakpoints message handling', () => {
    let addEventListenerSpy: jest.SpyInstance;
    let removeEventListenerSpy: jest.SpyInstance;

    beforeEach(() => {
      addEventListenerSpy = jest.spyOn(window, 'addEventListener');
      removeEventListenerSpy = jest.spyOn(window, 'removeEventListener');
      mockUpdateGlobalBreakpoints.mockReturnValue(true);
    });

    afterEach(() => {
      addEventListenerSpy.mockRestore();
      removeEventListenerSpy.mockRestore();
    });

    it('should set up message event listener on mount', () => {
      render(<PageRenderer {...mockPageProps} />);

      expect(addEventListenerSpy).toHaveBeenCalledWith('message', expect.any(Function));
    });

    it('should handle valid breakpoint message', () => {
      render(<PageRenderer {...mockPageProps} />);

      const messageHandler = addEventListenerSpy.mock.calls[0][1];
      const event = {
        data: {
          type: 'deca-responsive-breakpoints',
          breakpoints: {
            tablet: 768,
            desktop: 1200,
          },
        },
      };

      messageHandler(event);

      expect(mockUpdateGlobalBreakpoints).toHaveBeenCalledWith({
        tablet: 768,
        desktop: 1200,
      });
    });

    it('should handle string breakpoint values with px', () => {
      render(<PageRenderer {...mockPageProps} />);

      const messageHandler = addEventListenerSpy.mock.calls[0][1];
      const event = {
        data: {
          type: 'deca-responsive-breakpoints',
          breakpoints: {
            tablet: '900px',
            desktop: '1400px',
          },
        },
      };

      messageHandler(event);

      expect(mockUpdateGlobalBreakpoints).toHaveBeenCalledWith({
        tablet: 900,
        desktop: 1400,
      });
    });

    it('should use fallback values for invalid string breakpoints', () => {
      render(<PageRenderer {...mockPageProps} />);

      const messageHandler = addEventListenerSpy.mock.calls[0][1];
      const event = {
        data: {
          type: 'deca-responsive-breakpoints',
          breakpoints: {
            tablet: 'invalid',
            desktop: 'also-invalid',
          },
        },
      };

      messageHandler(event);

      expect(mockUpdateGlobalBreakpoints).toHaveBeenCalledWith({
        tablet: 768, // fallback
        desktop: 1200, // fallback
      });
    });

    it('should handle numeric breakpoint values', () => {
      render(<PageRenderer {...mockPageProps} />);

      const messageHandler = addEventListenerSpy.mock.calls[0][1];
      const event = {
        data: {
          type: 'deca-responsive-breakpoints',
          breakpoints: {
            tablet: 850.5,
            desktop: 1300.7,
          },
        },
      };

      messageHandler(event);

      expect(mockUpdateGlobalBreakpoints).toHaveBeenCalledWith({
        tablet: 851, // rounded
        desktop: 1301, // rounded
      });
    });

    it('should use fallback for NaN and infinite values', () => {
      render(<PageRenderer {...mockPageProps} />);

      const messageHandler = addEventListenerSpy.mock.calls[0][1];
      const event = {
        data: {
          type: 'deca-responsive-breakpoints',
          breakpoints: {
            tablet: Number.NaN,
            desktop: Number.POSITIVE_INFINITY,
          },
        },
      };

      messageHandler(event);

      expect(mockUpdateGlobalBreakpoints).toHaveBeenCalledWith({
        tablet: 768, // fallback
        desktop: 1200, // fallback
      });
    });

    it('should use fallback for invalid types', () => {
      render(<PageRenderer {...mockPageProps} />);

      const messageHandler = addEventListenerSpy.mock.calls[0][1];
      const event = {
        data: {
          type: 'deca-responsive-breakpoints',
          breakpoints: {
            tablet: {},
            desktop: [],
          },
        },
      };

      messageHandler(event);

      expect(mockUpdateGlobalBreakpoints).toHaveBeenCalledWith({
        tablet: 768, // fallback
        desktop: 1200, // fallback
      });
    });

    it('should reject unreasonable breakpoint values', () => {
      render(<PageRenderer {...mockPageProps} />);

      const messageHandler = addEventListenerSpy.mock.calls[0][1];

      // Test negative values
      let event = {
        data: {
          type: 'deca-responsive-breakpoints',
          breakpoints: {
            tablet: -100,
            desktop: 1200,
          },
        },
      };
      messageHandler(event);
      expect(mockUpdateGlobalBreakpoints).not.toHaveBeenCalled();

      // Test too large values
      event = {
        data: {
          type: 'deca-responsive-breakpoints',
          breakpoints: {
            tablet: 768,
            desktop: 6000,
          },
        },
      };
      messageHandler(event);
      expect(mockUpdateGlobalBreakpoints).not.toHaveBeenCalled();
    });

    it('should reject invalid breakpoint ordering (desktop < tablet)', () => {
      render(<PageRenderer {...mockPageProps} />);

      const messageHandler = addEventListenerSpy.mock.calls[0][1];
      const event = {
        data: {
          type: 'deca-responsive-breakpoints',
          breakpoints: {
            tablet: 1200,
            desktop: 768, // desktop < tablet
          },
        },
      };

      messageHandler(event);

      expect(mockUpdateGlobalBreakpoints).not.toHaveBeenCalled();
    });

    it('should ignore messages with wrong type', () => {
      render(<PageRenderer {...mockPageProps} />);

      const messageHandler = addEventListenerSpy.mock.calls[0][1];
      const event = {
        data: {
          type: 'other-message-type',
          breakpoints: {
            tablet: 768,
            desktop: 1200,
          },
        },
      };

      messageHandler(event);

      expect(mockUpdateGlobalBreakpoints).not.toHaveBeenCalled();
    });

    it('should ignore messages without data', () => {
      render(<PageRenderer {...mockPageProps} />);

      const messageHandler = addEventListenerSpy.mock.calls[0][1];
      const event = {};

      messageHandler(event);

      expect(mockUpdateGlobalBreakpoints).not.toHaveBeenCalled();
    });

    it('should ignore messages without breakpoints', () => {
      render(<PageRenderer {...mockPageProps} />);

      const messageHandler = addEventListenerSpy.mock.calls[0][1];
      const event = {
        data: {
          type: 'deca-responsive-breakpoints',
        },
      };

      messageHandler(event);

      expect(mockUpdateGlobalBreakpoints).not.toHaveBeenCalled();
    });

    it('should ignore messages with invalid breakpoints object', () => {
      render(<PageRenderer {...mockPageProps} />);

      const messageHandler = addEventListenerSpy.mock.calls[0][1];
      const event = {
        data: {
          type: 'deca-responsive-breakpoints',
          breakpoints: 'not-an-object',
        },
      };

      messageHandler(event);

      expect(mockUpdateGlobalBreakpoints).not.toHaveBeenCalled();
    });

    it('should ignore messages with missing tablet or desktop properties', () => {
      render(<PageRenderer {...mockPageProps} />);

      const messageHandler = addEventListenerSpy.mock.calls[0][1];

      // Missing tablet
      let event = {
        data: {
          type: 'deca-responsive-breakpoints',
          breakpoints: {
            desktop: 1200,
          } as any,
        },
      };
      messageHandler(event);
      expect(mockUpdateGlobalBreakpoints).not.toHaveBeenCalled();

      // Missing desktop
      event = {
        data: {
          type: 'deca-responsive-breakpoints',
          breakpoints: {
            tablet: 768,
          } as any,
        },
      };
      messageHandler(event);
      expect(mockUpdateGlobalBreakpoints).not.toHaveBeenCalled();
    });

    it('should not update breakpoint key when updateGlobalBreakpoints returns false', () => {
      mockUpdateGlobalBreakpoints.mockReturnValueOnce(false);

      const { rerender } = render(<PageRenderer {...mockPageProps} />);

      const messageHandler = addEventListenerSpy.mock.calls[0][1];
      const event = {
        data: {
          type: 'deca-responsive-breakpoints',
          breakpoints: {
            tablet: 768,
            desktop: 1200,
          },
        },
      };

      messageHandler(event);

      expect(mockUpdateGlobalBreakpoints).toHaveBeenCalled();
      // Component should not re-render with new key since breakpoints didn't change
      rerender(<PageRenderer {...mockPageProps} />);
    });

    it('should update breakpoint key when updateGlobalBreakpoints returns true', () => {
      mockUpdateGlobalBreakpoints.mockReturnValueOnce(true);

      render(<PageRenderer {...mockPageProps} />);

      const messageHandler = addEventListenerSpy.mock.calls[0][1];
      const event = {
        data: {
          type: 'deca-responsive-breakpoints',
          breakpoints: {
            tablet: 800,
            desktop: 1300,
          },
        },
      };

      messageHandler(event);

      expect(mockUpdateGlobalBreakpoints).toHaveBeenCalledWith({
        tablet: 800,
        desktop: 1300,
      });
    });

    it('should cleanup event listener on unmount', () => {
      const { unmount } = render(<PageRenderer {...mockPageProps} />);

      const messageHandler = addEventListenerSpy.mock.calls[0][1];

      unmount();

      expect(removeEventListenerSpy).toHaveBeenCalledWith('message', messageHandler);
    });
  });

  describe('theme and styling', () => {
    it('should apply custom font family from theme', () => {
      const customTheme = {
        content: {
          typography: {
            body: {
              font_family: 'Custom Font',
            },
          },
        },
      };

      const propsWithCustomTheme = {
        ...mockPageProps,
        siteSetting: {
          ...mockPageProps.siteSetting,
          theme: customTheme,
        },
      };

      render(<PageRenderer {...propsWithCustomTheme} />);

      const container = screen.getByTestId('craftjs-editor').parentElement;
      expect(container).toHaveStyle({ fontFamily: "'Custom Font'" });
    });

    it('should handle missing font family gracefully', () => {
      const propsWithoutFont = {
        ...mockPageProps,
        siteSetting: {
          ...mockPageProps.siteSetting,
          theme: {},
        },
      };

      render(<PageRenderer {...propsWithoutFont} />);

      const container = screen.getByTestId('craftjs-editor').parentElement;
      expect(container).toHaveStyle({ fontFamily: "''" });
    });

    it('should handle missing theme gracefully', () => {
      const propsWithoutTheme = {
        ...mockPageProps,
        siteSetting: {},
      };

      render(<PageRenderer {...propsWithoutTheme} />);

      const container = screen.getByTestId('craftjs-editor').parentElement;
      expect(container).toHaveStyle({ fontFamily: "''" });
    });
  });

  describe('breakpoint key functionality', () => {
    it('should pass breakpoint key to CraftJS Editor', () => {
      render(<PageRenderer {...mockPageProps} />);

      // The Editor component should receive a key prop
      expect(screen.getByTestId('craftjs-editor')).toBeInTheDocument();
    });

    it('should re-render Editor when breakpoints change', () => {
      const addEventListenerSpy = jest.spyOn(window, 'addEventListener');
      mockUpdateGlobalBreakpoints.mockReturnValue(true);

      render(<PageRenderer {...mockPageProps} />);

      const messageHandler = addEventListenerSpy.mock.calls[0][1] as (event: MessageEvent) => void;
      const event = {
        data: {
          type: 'deca-responsive-breakpoints',
          breakpoints: {
            tablet: 800,
            desktop: 1300,
          },
        },
      } as MessageEvent;

      // Trigger breakpoint change
      messageHandler(event);

      // Editor should still be present (re-rendered with new key)
      expect(screen.getByTestId('craftjs-editor')).toBeInTheDocument();

      addEventListenerSpy.mockRestore();
    });
  });
});
