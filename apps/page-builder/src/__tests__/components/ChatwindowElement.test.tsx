import ChatwindowElement from '@/components/ChatwindowElement';
import { render, screen } from '@testing-library/react';

// Mock Next.js dynamic import
jest.mock('next/dynamic', () => {
  return (importFunc: any, options: any) => {
    return ({ boxId, orgId, envConfig, isMobileView, ...props }: any) => (
      <div
        data-testid='chatwindow'
        data-box-id={boxId}
        data-org-id={orgId}
        data-env-config={JSON.stringify(envConfig || {})}
        data-is-mobile-view={isMobileView}
        {...props}
      >
        Mocked Chatwindow Component
      </div>
    );
  };
});

// Mock useViewport hook
jest.mock('@/hooks/useViewport', () => ({
  useViewport: jest.fn(),
}));

// Mock useSearchParams hook
jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn(),
}));

import { useViewport } from '@/hooks/useViewport';
import { useSearchParams } from 'next/navigation';

const mockUseViewport = useViewport as jest.MockedFunction<typeof useViewport>;
const mockUseSearchParams = useSearchParams as jest.MockedFunction<typeof useSearchParams>;

describe('ChatwindowElement', () => {
  const defaultProps = {
    boxId: 'chat123',
    orgId: 'org456',
  };

  const mockSearchParams = {
    get: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    jest.spyOn(console, 'error').mockImplementation();

    // Default mocks
    mockUseViewport.mockReturnValue('desktop');
    mockUseSearchParams.mockReturnValue(mockSearchParams);
    mockSearchParams.get.mockReturnValue(null);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('prop validation', () => {
    it('should render when both boxId and orgId are provided', () => {
      render(<ChatwindowElement {...defaultProps} />);

      const chatwindow = screen.getByTestId('chatwindow');
      expect(chatwindow).toBeInTheDocument();
      expect(chatwindow).toHaveAttribute('data-box-id', 'chat123');
      expect(chatwindow).toHaveAttribute('data-org-id', 'org456');
    });

    it('should return null when boxId is missing', () => {
      const propsWithoutBoxId = { orgId: 'org456' } as any;

      const { container } = render(<ChatwindowElement {...propsWithoutBoxId} />);

      expect(container.firstChild).toBeNull();
      expect(console.error).toHaveBeenCalledWith(
        '[ChatwindowElement] Both `boxId` and `orgId` props are required.'
      );
    });

    it('should return null when orgId is missing', () => {
      const propsWithoutOrgId = { boxId: 'chat123' } as any;

      const { container } = render(<ChatwindowElement {...propsWithoutOrgId} />);

      expect(container.firstChild).toBeNull();
      expect(console.error).toHaveBeenCalledWith(
        '[ChatwindowElement] Both `boxId` and `orgId` props are required.'
      );
    });

    it('should return null when both props are missing', () => {
      const { container } = render(<ChatwindowElement {...({} as any)} />);

      expect(container.firstChild).toBeNull();
      expect(console.error).toHaveBeenCalledWith(
        '[ChatwindowElement] Both `boxId` and `orgId` props are required.'
      );
    });

    it('should return null when props are empty strings', () => {
      const emptyProps = { boxId: '', orgId: '' };

      const { container } = render(<ChatwindowElement {...emptyProps} />);

      expect(container.firstChild).toBeNull();
      expect(console.error).toHaveBeenCalledWith(
        '[ChatwindowElement] Both `boxId` and `orgId` props are required.'
      );
    });

    it('should return null when props are falsy values', () => {
      const falsyProps = [
        { boxId: null, orgId: 'org456' },
        { boxId: undefined, orgId: 'org456' },
        { boxId: 'chat123', orgId: null },
        { boxId: 'chat123', orgId: undefined },
      ];

      falsyProps.forEach((props) => {
        const { container } = render(<ChatwindowElement {...(props as any)} />);
        expect(container.firstChild).toBeNull();
      });
    });

    it('should render with whitespace strings (truthy values)', () => {
      const whitespaceProps = { boxId: '   ', orgId: '   ' };

      render(<ChatwindowElement {...whitespaceProps} />);

      const chatwindow = screen.getByTestId('chatwindow');
      expect(chatwindow).toBeInTheDocument();
      expect(chatwindow).toHaveAttribute('data-box-id', '   ');
      expect(chatwindow).toHaveAttribute('data-org-id', '   ');
      expect(console.error).not.toHaveBeenCalled();
    });
  });

  describe('rendering and styling', () => {
    it('should render wrapper div with correct styles', () => {
      render(<ChatwindowElement {...defaultProps} />);

      const wrapper = screen.getByTestId('chatwindow').parentElement;
      expect(wrapper).toHaveStyle({
        position: 'relative',
      });
    });

    it('should render with different valid boxId and orgId', () => {
      const differentProps = {
        boxId: 'different-chat-123',
        orgId: 'different-org-456',
      };

      render(<ChatwindowElement {...differentProps} />);

      const chatwindow = screen.getByTestId('chatwindow');
      expect(chatwindow).toBeInTheDocument();
      expect(chatwindow).toHaveAttribute('data-box-id', 'different-chat-123');
      expect(chatwindow).toHaveAttribute('data-org-id', 'different-org-456');
    });

    it('should set data-is-mobile attribute on wrapper', () => {
      render(<ChatwindowElement {...defaultProps} />);

      const wrapper = screen.getByTestId('chatwindow').parentElement;
      expect(wrapper).toHaveAttribute('data-is-mobile');
    });
  });

  describe('environment configuration', () => {
    const originalEnv = process.env;

    beforeEach(() => {
      // Reset environment
      process.env = { ...originalEnv };
    });

    afterEach(() => {
      process.env = originalEnv;
    });

    it('should pass complete envConfig to Chatwindow component', () => {
      // Mock all environment variables
      process.env = {
        ...originalEnv,
        NEXT_PUBLIC_CHATWINDOW_ACCESS_TOKEN: 'test-access-token',
        BUILD_ACCESS_TOKEN: 'build-access-token',
        NEXT_PUBLIC_CHATWINDOW_API_SERVER_URL: 'https://api.test.com',
        NEXT_PUBLIC_CHATWINDOW_DEFAULT_ORGANIZATION_NAME: 'test-org',
        NEXT_PUBLIC_CHATWINDOW_CLIENT_CDN_PREFIX: 'test-cdn-prefix',
        NEXT_PUBLIC_CHATWINDOW_CDN_URL: 'https://cdn.test.com',
        NEXT_PUBLIC_CHATBOT_API_SERVER_URL: 'https://chatbot.test.com',
        NEXT_PUBLIC_LIVECHAT_API_SERVER_URL: 'https://livechat.test.com',
        NEXT_PUBLIC_CHATWINDOW_HEALTH_CHECK_INTERVAL: '10000',
        NEXT_PUBLIC_CHATWINDOW_API_MAPPING_URL: 'https://mapping.test.com',
        NEXT_PUBLIC_CHATWINDOW_IS_MAINTENANCE_MODE: 'false',
        NEXT_PUBLIC_WEBSOCKET_URL: 'wss://websocket.test.com',
        NEXT_PUBLIC_CHATWINDOW_TOLGEE_URL: 'https://tolgee.test.com',
        NEXT_PUBLIC_CHATWINDOW_TOLGEE_KEY: 'test-tolgee-key',
        NEXT_PUBLIC_CHATWINDOW_TOLGEE_TOOLS: 'true',
      };

      render(<ChatwindowElement {...defaultProps} />);

      const chatwindow = screen.getByTestId('chatwindow');
      const envConfigAttr = chatwindow.getAttribute('data-env-config');
      const receivedEnvConfig = JSON.parse(envConfigAttr || '{}');

      expect(receivedEnvConfig).toEqual({
        ACCESS_TOKEN: 'test-access-token',
        API_URL: 'https://api.test.com',
        ORG_ID: 'test-org',
        CLIENT_CDN_PREFIX: 'test-cdn-prefix',
        CDN_URL: 'https://cdn.test.com',
        CHATBOT_API_URL: 'https://chatbot.test.com',
        LIVECHAT_API_URL: 'https://livechat.test.com',
        HEALTH_CHECK_INTERVAL: '10000',
        API_MAPPING_URL: 'https://mapping.test.com',
        IS_MAINTENANCE_MODE: 'false',
        WEBSOCKET_URL: 'wss://websocket.test.com',
        TOLGEE_URL: 'https://tolgee.test.com',
        TOLGEE_KEY: 'test-tolgee-key',
        TOLGEE_TOOLS_ENABLED: 'true',
      });
    });

    it('should handle missing environment variables with fallbacks', () => {
      // Mock environment with minimal variables
      process.env = {
        ...originalEnv,
        BUILD_ACCESS_TOKEN: 'fallback-token',
        NEXT_PUBLIC_CHATWINDOW_ACCESS_TOKEN: undefined,
        NEXT_PUBLIC_CHATWINDOW_HEALTH_CHECK_INTERVAL: undefined,
      };

      render(<ChatwindowElement {...defaultProps} />);

      const chatwindow = screen.getByTestId('chatwindow');
      const envConfigAttr = chatwindow.getAttribute('data-env-config');
      const receivedEnvConfig = JSON.parse(envConfigAttr || '{}');

      expect(receivedEnvConfig.ACCESS_TOKEN).toBe('fallback-token');
      expect(receivedEnvConfig.HEALTH_CHECK_INTERVAL).toBe(5000); // Default value
    });

    it('should use default health check interval when not provided', () => {
      process.env = {
        ...originalEnv,
        NEXT_PUBLIC_CHATWINDOW_HEALTH_CHECK_INTERVAL: undefined,
      };

      render(<ChatwindowElement {...defaultProps} />);

      const chatwindow = screen.getByTestId('chatwindow');
      const envConfigAttr = chatwindow.getAttribute('data-env-config');
      const receivedEnvConfig = JSON.parse(envConfigAttr || '{}');

      expect(receivedEnvConfig.HEALTH_CHECK_INTERVAL).toBe(5000);
    });

    it('should handle all environment variables as undefined', () => {
      // Clear all relevant env vars
      const envVarsToClear = [
        'NEXT_PUBLIC_CHATWINDOW_ACCESS_TOKEN',
        'BUILD_ACCESS_TOKEN',
        'NEXT_PUBLIC_CHATWINDOW_API_SERVER_URL',
        'NEXT_PUBLIC_CHATWINDOW_DEFAULT_ORGANIZATION_NAME',
        'NEXT_PUBLIC_CHATWINDOW_CLIENT_CDN_PREFIX',
        'NEXT_PUBLIC_CHATWINDOW_CDN_URL',
        'NEXT_PUBLIC_CHATBOT_API_SERVER_URL',
        'NEXT_PUBLIC_LIVECHAT_API_SERVER_URL',
        'NEXT_PUBLIC_CHATWINDOW_HEALTH_CHECK_INTERVAL',
        'NEXT_PUBLIC_CHATWINDOW_API_MAPPING_URL',
        'NEXT_PUBLIC_CHATWINDOW_IS_MAINTENANCE_MODE',
        'NEXT_PUBLIC_WEBSOCKET_URL',
        'NEXT_PUBLIC_CHATWINDOW_TOLGEE_URL',
        'NEXT_PUBLIC_CHATWINDOW_TOLGEE_KEY',
        'NEXT_PUBLIC_CHATWINDOW_TOLGEE_TOOLS',
      ];

      envVarsToClear.forEach((varName) => {
        delete process.env[varName];
      });

      render(<ChatwindowElement {...defaultProps} />);

      const chatwindow = screen.getByTestId('chatwindow');
      expect(chatwindow).toBeInTheDocument();

      const envConfigAttr = chatwindow.getAttribute('data-env-config');
      const receivedEnvConfig = JSON.parse(envConfigAttr || '{}');

      expect(receivedEnvConfig.ACCESS_TOKEN).toBeUndefined();
      expect(receivedEnvConfig.HEALTH_CHECK_INTERVAL).toBe(5000);
    });
  });

  describe('mobile view detection', () => {
    it('should detect mobile view from useViewport hook', () => {
      mockUseViewport.mockReturnValue('mobile');

      render(<ChatwindowElement {...defaultProps} />);

      const chatwindow = screen.getByTestId('chatwindow');
      expect(chatwindow).toHaveAttribute('data-is-mobile-view', 'true');
    });

    it('should detect tablet view from useViewport hook', () => {
      mockUseViewport.mockReturnValue('tablet');

      render(<ChatwindowElement {...defaultProps} />);

      const chatwindow = screen.getByTestId('chatwindow');
      expect(chatwindow).toHaveAttribute('data-is-mobile-view', 'true');
    });

    it('should detect desktop view from useViewport hook', () => {
      mockUseViewport.mockReturnValue('desktop');

      render(<ChatwindowElement {...defaultProps} />);

      const chatwindow = screen.getByTestId('chatwindow');
      expect(chatwindow).toHaveAttribute('data-is-mobile-view', 'false');
    });

    it('should prioritize builderPreviewDevice over useViewport', () => {
      mockUseViewport.mockReturnValue('desktop');
      mockSearchParams.get.mockReturnValue('mobile');

      render(<ChatwindowElement {...defaultProps} />);

      const chatwindow = screen.getByTestId('chatwindow');
      expect(chatwindow).toHaveAttribute('data-is-mobile-view', 'true');
    });

    it('should handle builderPreviewDevice with tablet value', () => {
      mockUseViewport.mockReturnValue('desktop');
      mockSearchParams.get.mockReturnValue('tablet');

      render(<ChatwindowElement {...defaultProps} />);

      const chatwindow = screen.getByTestId('chatwindow');
      expect(chatwindow).toHaveAttribute('data-is-mobile-view', 'true');
    });

    it('should handle builderPreviewDevice with desktop value', () => {
      mockUseViewport.mockReturnValue('mobile');
      mockSearchParams.get.mockReturnValue('desktop');

      render(<ChatwindowElement {...defaultProps} />);

      const chatwindow = screen.getByTestId('chatwindow');
      expect(chatwindow).toHaveAttribute('data-is-mobile-view', 'false');
    });

    it('should fallback to useViewport when builderPreviewDevice is null', () => {
      mockUseViewport.mockReturnValue('mobile');
      mockSearchParams.get.mockReturnValue(null);

      render(<ChatwindowElement {...defaultProps} />);

      const chatwindow = screen.getByTestId('chatwindow');
      expect(chatwindow).toHaveAttribute('data-is-mobile-view', 'true');
    });

    it('should fallback to useViewport when builderPreviewDevice is empty string', () => {
      mockUseViewport.mockReturnValue('tablet');
      mockSearchParams.get.mockReturnValue('');

      render(<ChatwindowElement {...defaultProps} />);

      const chatwindow = screen.getByTestId('chatwindow');
      expect(chatwindow).toHaveAttribute('data-is-mobile-view', 'true');
    });
  });

  describe('integration tests', () => {
    it('should pass all required props to Chatwindow component', () => {
      mockUseViewport.mockReturnValue('mobile');

      render(<ChatwindowElement {...defaultProps} />);

      const chatwindow = screen.getByTestId('chatwindow');
      expect(chatwindow).toHaveAttribute('data-box-id', 'chat123');
      expect(chatwindow).toHaveAttribute('data-org-id', 'org456');
      expect(chatwindow).toHaveAttribute('data-is-mobile-view', 'true');

      const envConfigAttr = chatwindow.getAttribute('data-env-config');
      expect(envConfigAttr).toBeTruthy();
      expect(() => JSON.parse(envConfigAttr || '{}')).not.toThrow();
    });

    it('should handle complex environment variable combinations', () => {
      process.env.NEXT_PUBLIC_CHATWINDOW_ACCESS_TOKEN = 'token1';
      process.env.BUILD_ACCESS_TOKEN = 'token2';
      process.env.NEXT_PUBLIC_WEBSOCKET_URL = 'ws://fallback';
      process.env.NEXT_PUBLIC_CHATWINDOW_WEBSOCKET_URL = 'ws://primary';

      render(<ChatwindowElement {...defaultProps} />);

      const chatwindow = screen.getByTestId('chatwindow');
      const envConfigAttr = chatwindow.getAttribute('data-env-config');
      const receivedEnvConfig = JSON.parse(envConfigAttr || '{}');

      expect(receivedEnvConfig.ACCESS_TOKEN).toBe('token1'); // Primary takes precedence
      expect(receivedEnvConfig.WEBSOCKET_URL).toBe('ws://primary'); // Primary takes precedence
    });
  });
});
