import { AppConfig } from '@/configs/app';

describe('AppConfig', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    // Reset modules to ensure fresh imports
    jest.resetModules();
    process.env = { ...originalEnv };
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  it('should be defined', () => {
    expect(AppConfig).toBeDefined();
    expect(typeof AppConfig).toBe('object');
  });

  it('should have all required properties', () => {
    expect(AppConfig).toHaveProperty('IS_PRODUCTION');
    expect(AppConfig).toHaveProperty('AUTH0');
    expect(AppConfig).toHaveProperty('DEFAULT_ORGANIZATION_NAME');
    expect(AppConfig).toHaveProperty('PUBLIC_URL');
    expect(AppConfig).toHaveProperty('CDN_URL');
    expect(AppConfig).toHaveProperty('EXCLUDED_BASE_PATH_DOMAINS');
    expect(AppConfig).toHaveProperty('INCLUDED_BASE_PATH_DOMAINS');
    expect(AppConfig).toHaveProperty('WEBSOCKET_URL');
    expect(AppConfig).toHaveProperty('API_SERVER_URL');
    expect(AppConfig).toHaveProperty('NO_PERMISSION_REDIRECT_URL');
    expect(AppConfig).toHaveProperty('SKIP_ORG_NAME_DOMAINS');
    expect(AppConfig).toHaveProperty('SENTRY_DSN');
    expect(AppConfig).toHaveProperty('BASE_PATH');
    expect(AppConfig).toHaveProperty('TOLGEE_URL');
    expect(AppConfig).toHaveProperty('TOLGEE_KEY');
    expect(AppConfig).toHaveProperty('TOLGEE_TOOLS_ENABLED');
    expect(AppConfig).toHaveProperty('BUILD_ACCESS_TOKEN');
    expect(AppConfig).toHaveProperty('NEXT_PUBLIC_SITE_ID');
  });

  it('should use default values when environment variables are not set', () => {
    // Clear environment variables
    process.env.NEXT_PUBLIC_NODE_ENV = undefined;
    process.env.NEXT_PUBLIC_AUTH0_DOMAIN = undefined;
    process.env.NEXT_PUBLIC_DEFAULT_ORGANIZATION_NAME = undefined;
    process.env.NEXT_PUBLIC_PUBLIC_URL = undefined;
    process.env.NEXT_PUBLIC_EXCLUDED_BASE_PATH_DOMAINS = undefined;
    process.env.NEXT_PUBLIC_INCLUDED_BASE_PATH_DOMAINS = undefined;
    process.env.VITE_TOLGEE_TOOLS_ENABLED = undefined;

    // Re-require the module to get fresh config
    const { AppConfig: freshConfig } = require('@/configs/app');

    expect(freshConfig.IS_PRODUCTION).toBe(false);
    expect(freshConfig.AUTH0.DOMAIN).toBe('');
    expect(freshConfig.DEFAULT_ORGANIZATION_NAME).toBe('resola');
    expect(freshConfig.PUBLIC_URL).toBe('');
    expect(freshConfig.EXCLUDED_BASE_PATH_DOMAINS).toEqual([]);
    expect(freshConfig.INCLUDED_BASE_PATH_DOMAINS).toEqual([]);
    expect(freshConfig.NO_PERMISSION_REDIRECT_URL).toBe('https://deca-dev.com');
    expect(freshConfig.TOLGEE_TOOLS_ENABLED).toBe(false);
  });

  it('should correctly parse IS_PRODUCTION from environment', () => {
    // Test production environment
    process.env.NEXT_PUBLIC_NODE_ENV = 'production';
    const { AppConfig: prodConfig } = require('@/configs/app');
    expect(prodConfig.IS_PRODUCTION).toBe(true);

    // Reset and test non-production
    jest.resetModules();
    process.env.NEXT_PUBLIC_NODE_ENV = 'development';
    const { AppConfig: devConfig } = require('@/configs/app');
    expect(devConfig.IS_PRODUCTION).toBe(false);
  });

  it('should correctly parse AUTH0 configuration', () => {
    process.env.NEXT_PUBLIC_AUTH0_DOMAIN = 'test-domain.auth0.com';
    process.env.NEXT_PUBLIC_AUTH0_CLIENT_ID = 'test-client-id';
    process.env.NEXT_PUBLIC_AUTH0_AUDIENCE = 'test-audience';
    process.env.NEXT_PUBLIC_AUTH0_SCOPE = 'openid profile email';

    const { AppConfig: authConfig } = require('@/configs/app');

    expect(authConfig.AUTH0.DOMAIN).toBe('test-domain.auth0.com');
    expect(authConfig.AUTH0.CLIENT_ID).toBe('test-client-id');
    expect(authConfig.AUTH0.AUDIENCE).toBe('test-audience');
    expect(authConfig.AUTH0.SCOPE).toBe('openid profile email');
  });

  it('should correctly parse comma-separated domain lists', () => {
    process.env.NEXT_PUBLIC_EXCLUDED_BASE_PATH_DOMAINS = 'domain1.com,domain2.com,domain3.com';
    process.env.NEXT_PUBLIC_INCLUDED_BASE_PATH_DOMAINS = 'include1.com,include2.com';
    process.env.NEXT_PUBLIC_SKIP_ORG_NAME_DOMAINS = 'skip1.com,skip2.com,skip3.com';

    const { AppConfig: domainConfig } = require('@/configs/app');

    expect(domainConfig.EXCLUDED_BASE_PATH_DOMAINS).toEqual([
      'domain1.com',
      'domain2.com',
      'domain3.com',
    ]);
    expect(domainConfig.INCLUDED_BASE_PATH_DOMAINS).toEqual(['include1.com', 'include2.com']);
    expect(domainConfig.SKIP_ORG_NAME_DOMAINS).toEqual(['skip1.com', 'skip2.com', 'skip3.com']);
  });

  it('should handle empty domain lists', () => {
    process.env.NEXT_PUBLIC_EXCLUDED_BASE_PATH_DOMAINS = '';
    process.env.NEXT_PUBLIC_INCLUDED_BASE_PATH_DOMAINS = '';
    process.env.NEXT_PUBLIC_SKIP_ORG_NAME_DOMAINS = '';

    const { AppConfig: emptyConfig } = require('@/configs/app');

    expect(emptyConfig.EXCLUDED_BASE_PATH_DOMAINS).toEqual([]);
    expect(emptyConfig.INCLUDED_BASE_PATH_DOMAINS).toEqual([]);
    expect(emptyConfig.SKIP_ORG_NAME_DOMAINS).toEqual([]);
  });

  it('should correctly parse TOLGEE_TOOLS_ENABLED boolean', () => {
    // Test true value
    process.env.VITE_TOLGEE_TOOLS_ENABLED = 'true';
    const { AppConfig: trueConfig } = require('@/configs/app');
    expect(trueConfig.TOLGEE_TOOLS_ENABLED).toBe(true);

    // Test false value
    jest.resetModules();
    process.env.VITE_TOLGEE_TOOLS_ENABLED = 'false';
    const { AppConfig: falseConfig } = require('@/configs/app');
    expect(falseConfig.TOLGEE_TOOLS_ENABLED).toBe(false);

    // Test other value
    jest.resetModules();
    process.env.VITE_TOLGEE_TOOLS_ENABLED = 'anything';
    const { AppConfig: otherConfig } = require('@/configs/app');
    expect(otherConfig.TOLGEE_TOOLS_ENABLED).toBe(false);
  });

  it('should use environment variables when provided', () => {
    process.env.NEXT_PUBLIC_DEFAULT_ORGANIZATION_NAME = 'custom-org';
    process.env.NEXT_PUBLIC_PUBLIC_URL = 'https://custom.com';
    process.env.NEXT_PUBLIC_CDN_PREFIX = 'https://cdn.custom.com';
    process.env.NEXT_PUBLIC_WEBSOCKET_URL = 'wss://ws.custom.com';
    process.env.NEXT_PUBLIC_API_SERVER_URL = 'https://api.custom.com';
    process.env.NEXT_PUBLIC_SENTRY_DSN = 'https://sentry.custom.com/123';
    process.env.NEXT_PUBLIC_BASE_PATH = '/custom-path/';
    process.env.VITE_TOLGEE_URL = 'https://tolgee.custom.com';
    process.env.VITE_TOLGEE_KEY = 'custom-tolgee-key';
    process.env.BUILD_ACCESS_TOKEN = 'build-token-123';
    process.env.NEXT_PUBLIC_SITE_ID = 'site-123';

    const { AppConfig: customConfig } = require('@/configs/app');

    expect(customConfig.DEFAULT_ORGANIZATION_NAME).toBe('custom-org');
    expect(customConfig.PUBLIC_URL).toBe('https://custom.com');
    expect(customConfig.CDN_URL).toBe('https://cdn.custom.com');
    expect(customConfig.WEBSOCKET_URL).toBe('wss://ws.custom.com');
    expect(customConfig.API_SERVER_URL).toBe('https://api.custom.com');
    expect(customConfig.SENTRY_DSN).toBe('https://sentry.custom.com/123');
    expect(customConfig.BASE_PATH).toBe('/custom-path/');
    expect(customConfig.TOLGEE_URL).toBe('https://tolgee.custom.com');
    expect(customConfig.TOLGEE_KEY).toBe('custom-tolgee-key');
    expect(customConfig.BUILD_ACCESS_TOKEN).toBe('build-token-123');
    expect(customConfig.NEXT_PUBLIC_SITE_ID).toBe('site-123');
  });

  it('should handle undefined values for optional properties', () => {
    process.env.BUILD_ACCESS_TOKEN = undefined;
    process.env.NEXT_PUBLIC_SITE_ID = undefined;

    const { AppConfig: undefinedConfig } = require('@/configs/app');

    expect(undefinedConfig.BUILD_ACCESS_TOKEN).toBeUndefined();
    expect(undefinedConfig.NEXT_PUBLIC_SITE_ID).toBeUndefined();
  });

  it('should have correct default values', () => {
    expect(AppConfig.NO_PERMISSION_REDIRECT_URL).toBe('https://deca-dev.com');
    expect(AppConfig.DEFAULT_ORGANIZATION_NAME).toBe('resola');
    expect(AppConfig.BASE_PATH).toBe('/');
    expect(AppConfig.TOLGEE_URL).toBe('https://app.tolgee.io');
  });

  it('should handle special NO_PERMISSION_REDIRECT_URL environment variable', () => {
    process.env.NEXT_PUBLIC_NO_PERMISSION_REDIRECT_URL = 'https://custom-redirect.com';

    const { AppConfig: customRedirectConfig } = require('@/configs/app');

    expect(customRedirectConfig.NO_PERMISSION_REDIRECT_URL).toBe('https://custom-redirect.com');
  });
});
