import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import { AppMantineEmotionProvider } from '@/providers/AppMantineEmotionProvider';

// Mock the external dependencies
jest.mock('@/cache', () => ({
  emotionCache: {
    key: 'mantine',
    container: null,
    insertionPoint: undefined,
  },
}));

jest.mock('@mantine/core', () => ({
  MantineProvider: jest.fn(({ children, ...props }) => (
    <div data-testid='mantine-provider' data-props={JSON.stringify(props)}>
      {children}
    </div>
  )),
}));

jest.mock('@mantine/emotion', () => ({
  MantineEmotionProvider: jest.fn(({ children, cache }) => (
    <div data-testid='emotion-provider' data-cache={JSON.stringify(cache)}>
      {children}
    </div>
  )),
  emotionTransform: jest.fn().mockImplementation((styles) => styles),
}));

jest.mock('@resola-ai/ui/constants', () => ({
  themeConfigurations: {
    colors: {
      primary: ['#e3f2fd', '#bbdefb', '#90caf9'],
    },
    fontFamily: 'Inter, sans-serif',
    headings: {
      fontFamily: 'Inter, sans-serif',
    },
  },
}));

import { emotionCache } from '@/cache';
// Import the mocked modules to access them in tests
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider, emotionTransform } from '@mantine/emotion';
import { themeConfigurations } from '@resola-ai/ui/constants';

const mockMantineProvider = MantineProvider as jest.MockedFunction<typeof MantineProvider>;
const mockEmotionProvider = MantineEmotionProvider as jest.MockedFunction<
  typeof MantineEmotionProvider
>;
const mockEmotionTransform = emotionTransform as any;

describe('AppMantineEmotionProvider', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render without crashing', () => {
    const TestChild = () => <div data-testid='test-child'>Test Child</div>;

    expect(() =>
      render(
        <AppMantineEmotionProvider>
          <TestChild />
        </AppMantineEmotionProvider>
      )
    ).not.toThrow();
  });

  it('should render children content correctly', () => {
    const TestChild = () => <div data-testid='test-child'>Test Child Content</div>;

    const { getByTestId } = render(
      <AppMantineEmotionProvider>
        <TestChild />
      </AppMantineEmotionProvider>
    );

    expect(getByTestId('test-child')).toBeInTheDocument();
    expect(getByTestId('test-child')).toHaveTextContent('Test Child Content');
  });

  it('should render MantineEmotionProvider with emotion cache', () => {
    const TestChild = () => <div>Test</div>;

    const { getByTestId } = render(
      <AppMantineEmotionProvider>
        <TestChild />
      </AppMantineEmotionProvider>
    );

    expect(getByTestId('emotion-provider')).toBeInTheDocument();
    expect(mockEmotionProvider).toHaveBeenCalledWith(
      expect.objectContaining({
        cache: emotionCache,
        children: expect.anything(),
      }),
      expect.anything()
    );
  });

  it('should render MantineProvider with correct props', () => {
    const TestChild = () => <div>Test</div>;

    const { getByTestId } = render(
      <AppMantineEmotionProvider>
        <TestChild />
      </AppMantineEmotionProvider>
    );

    expect(getByTestId('mantine-provider')).toBeInTheDocument();
    expect(mockMantineProvider).toHaveBeenCalledWith(
      expect.objectContaining({
        withGlobalClasses: true,
        stylesTransform: emotionTransform,
        theme: themeConfigurations,
        children: expect.anything(),
      }),
      expect.anything()
    );
  });

  it('should pass emotion cache to MantineEmotionProvider', () => {
    const TestChild = () => <div>Test</div>;

    render(
      <AppMantineEmotionProvider>
        <TestChild />
      </AppMantineEmotionProvider>
    );

    expect(mockEmotionProvider).toHaveBeenCalledWith(
      expect.objectContaining({
        cache: emotionCache,
      }),
      expect.anything()
    );
  });

  it('should pass theme configurations to MantineProvider', () => {
    const TestChild = () => <div>Test</div>;

    render(
      <AppMantineEmotionProvider>
        <TestChild />
      </AppMantineEmotionProvider>
    );

    expect(mockMantineProvider).toHaveBeenCalledWith(
      expect.objectContaining({
        theme: themeConfigurations,
      }),
      expect.anything()
    );
  });

  it('should enable global classes in MantineProvider', () => {
    const TestChild = () => <div>Test</div>;

    render(
      <AppMantineEmotionProvider>
        <TestChild />
      </AppMantineEmotionProvider>
    );

    expect(mockMantineProvider).toHaveBeenCalledWith(
      expect.objectContaining({
        withGlobalClasses: true,
      }),
      expect.anything()
    );
  });

  it('should use emotion transform for styles', () => {
    const TestChild = () => <div>Test</div>;

    render(
      <AppMantineEmotionProvider>
        <TestChild />
      </AppMantineEmotionProvider>
    );

    expect(mockMantineProvider).toHaveBeenCalledWith(
      expect.objectContaining({
        stylesTransform: emotionTransform,
      }),
      expect.anything()
    );
  });

  it('should handle multiple children', () => {
    const ChildOne = () => <div data-testid='child-one'>Child One</div>;
    const ChildTwo = () => <div data-testid='child-two'>Child Two</div>;

    const { getByTestId } = render(
      <AppMantineEmotionProvider>
        <ChildOne />
        <ChildTwo />
      </AppMantineEmotionProvider>
    );

    expect(getByTestId('child-one')).toBeInTheDocument();
    expect(getByTestId('child-two')).toBeInTheDocument();
  });

  it('should handle nested components', () => {
    const NestedChild = () => <div data-testid='nested-child'>Nested</div>;
    const ParentChild = () => (
      <div data-testid='parent-child'>
        Parent
        <NestedChild />
      </div>
    );

    const { getByTestId } = render(
      <AppMantineEmotionProvider>
        <ParentChild />
      </AppMantineEmotionProvider>
    );

    expect(getByTestId('parent-child')).toBeInTheDocument();
    expect(getByTestId('nested-child')).toBeInTheDocument();
  });

  it('should render provider hierarchy correctly', () => {
    const TestChild = () => <div data-testid='test-child'>Test</div>;

    const { getByTestId } = render(
      <AppMantineEmotionProvider>
        <TestChild />
      </AppMantineEmotionProvider>
    );

    // Emotion provider should be the outer wrapper
    const emotionProvider = getByTestId('emotion-provider');
    expect(emotionProvider).toBeInTheDocument();

    // Mantine provider should be inside emotion provider
    const mantineProvider = getByTestId('mantine-provider');
    expect(mantineProvider).toBeInTheDocument();
    expect(emotionProvider).toContainElement(mantineProvider);

    // Test child should be inside mantine provider
    const testChild = getByTestId('test-child');
    expect(testChild).toBeInTheDocument();
    expect(mantineProvider).toContainElement(testChild);
  });

  it('should handle empty children', () => {
    expect(() =>
      render(<AppMantineEmotionProvider>{null}</AppMantineEmotionProvider>)
    ).not.toThrow();
  });

  it('should handle undefined children', () => {
    expect(() =>
      render(<AppMantineEmotionProvider>{undefined}</AppMantineEmotionProvider>)
    ).not.toThrow();
  });

  it('should handle string children', () => {
    const { container } = render(
      <AppMantineEmotionProvider>Test String Child</AppMantineEmotionProvider>
    );

    expect(container).toHaveTextContent('Test String Child');
  });

  it('should handle fragment children', () => {
    const { getByTestId } = render(
      <AppMantineEmotionProvider>
        <>
          <div data-testid='fragment-child-1'>Fragment Child 1</div>
          <div data-testid='fragment-child-2'>Fragment Child 2</div>
        </>
      </AppMantineEmotionProvider>
    );

    expect(getByTestId('fragment-child-1')).toBeInTheDocument();
    expect(getByTestId('fragment-child-2')).toBeInTheDocument();
  });

  it('should call providers only once', () => {
    const TestChild = () => <div>Test</div>;

    render(
      <AppMantineEmotionProvider>
        <TestChild />
      </AppMantineEmotionProvider>
    );

    expect(mockEmotionProvider).toHaveBeenCalledTimes(1);
    expect(mockMantineProvider).toHaveBeenCalledTimes(1);
  });

  it('should re-render correctly when children change', () => {
    const TestChild = ({ text }: { text: string }) => <div data-testid='test-child'>{text}</div>;

    const { getByTestId, rerender } = render(
      <AppMantineEmotionProvider>
        <TestChild text='Initial' />
      </AppMantineEmotionProvider>
    );

    expect(getByTestId('test-child')).toHaveTextContent('Initial');

    rerender(
      <AppMantineEmotionProvider>
        <TestChild text='Updated' />
      </AppMantineEmotionProvider>
    );

    expect(getByTestId('test-child')).toHaveTextContent('Updated');
  });

  describe('Integration', () => {
    it('should provide access to emotion cache through data attributes', () => {
      const TestChild = () => <div>Test</div>;

      const { getByTestId } = render(
        <AppMantineEmotionProvider>
          <TestChild />
        </AppMantineEmotionProvider>
      );

      const emotionProvider = getByTestId('emotion-provider');
      const cacheData = JSON.parse(emotionProvider.getAttribute('data-cache') || '{}');

      expect(cacheData).toEqual(emotionCache);
    });

    it('should provide access to mantine props through data attributes', () => {
      const TestChild = () => <div>Test</div>;

      const { getByTestId } = render(
        <AppMantineEmotionProvider>
          <TestChild />
        </AppMantineEmotionProvider>
      );

      const mantineProvider = getByTestId('mantine-provider');
      const propsData = JSON.parse(mantineProvider.getAttribute('data-props') || '{}');

      expect(propsData).toEqual({
        withGlobalClasses: true,
        theme: themeConfigurations,
      });
    });

    it('should maintain provider functionality across re-renders', () => {
      const TestChild = ({ count }: { count: number }) => <div data-testid='counter'>{count}</div>;

      const { getByTestId, rerender } = render(
        <AppMantineEmotionProvider>
          <TestChild count={1} />
        </AppMantineEmotionProvider>
      );

      expect(getByTestId('counter')).toHaveTextContent('1');

      // Re-render multiple times to ensure stability
      for (let i = 2; i <= 5; i++) {
        rerender(
          <AppMantineEmotionProvider>
            <TestChild count={i} />
          </AppMantineEmotionProvider>
        );
        expect(getByTestId('counter')).toHaveTextContent(String(i));
      }

      // Providers should maintain their call count
      expect(mockEmotionProvider).toHaveBeenCalledTimes(5);
      expect(mockMantineProvider).toHaveBeenCalledTimes(5);
    });
  });
});
