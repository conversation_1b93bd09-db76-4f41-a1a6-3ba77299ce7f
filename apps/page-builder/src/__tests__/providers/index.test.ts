import { PageProvider, usePageContext } from '@/providers/index';

describe('providers/index', () => {
  it('should export PageProvider', () => {
    expect(PageProvider).toBeDefined();
    expect(typeof PageProvider).toBe('function');
  });

  it('should export usePageContext', () => {
    expect(usePageContext).toBeDefined();
    expect(typeof usePageContext).toBe('function');
  });

  it('should export all providers from index', () => {
    const imports = require('@/providers/index');
    expect(imports.PageProvider).toBeDefined();
    expect(imports.usePageContext).toBeDefined();
  });

  it('should be importable without errors', () => {
    expect(() => {
      require('@/providers/index');
    }).not.toThrow();
  });

  it('should maintain export structure', () => {
    const exported = require('@/providers/index');
    expect(Object.keys(exported)).toEqual(['PageProvider', 'usePageContext']);
  });
});
