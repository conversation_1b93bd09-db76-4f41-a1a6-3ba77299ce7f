// Mock axios service using inline factory function
jest.mock('@resola-ai/services-shared', () => ({
  axiosService: {
    instance: {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
    },
  },
}));

import { getPage, getPages } from '@/api/page';
import { axiosService } from '@resola-ai/services-shared';
import { mockApiResponses } from '../fixtures/mockData';

// Type the mocked axiosService
const mockAxiosService = axiosService as jest.Mocked<typeof axiosService>;

describe('Page API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getPages', () => {
    it('should fetch pages for a site', async () => {
      const mockResponse = {
        data: {
          response: mockApiResponses.pages,
        },
      };

      mockAxiosService.instance.get.mockResolvedValueOnce(mockResponse);

      const result = await getPages('site123');

      expect(mockAxiosService.instance.get).toHaveBeenCalledWith('/sites/site123/pages');
      expect(result).toEqual(mockApiResponses.pages);
    });

    it('should handle API errors', async () => {
      const error = new Error('API Error');
      mockAxiosService.instance.get.mockRejectedValueOnce(error);

      await expect(getPages('site123')).rejects.toThrow('API Error');
      expect(mockAxiosService.instance.get).toHaveBeenCalledWith('/sites/site123/pages');
    });
  });

  describe('getPage', () => {
    it('should fetch a specific page', async () => {
      const mockResponse = {
        data: {
          response: mockApiResponses.page,
        },
      };

      mockAxiosService.instance.get.mockResolvedValueOnce(mockResponse);

      const result = await getPage('site123', 'page456');

      expect(mockAxiosService.instance.get).toHaveBeenCalledWith('/sites/site123/pages/page456');
      expect(result).toEqual(mockApiResponses.page);
    });

    it('should handle API errors', async () => {
      const error = new Error('Page not found');
      mockAxiosService.instance.get.mockRejectedValueOnce(error);

      await expect(getPage('site123', 'page456')).rejects.toThrow('Page not found');
      expect(mockAxiosService.instance.get).toHaveBeenCalledWith('/sites/site123/pages/page456');
    });

    it('should handle empty response', async () => {
      const mockResponse = {
        data: {
          response: null,
        },
      };

      mockAxiosService.instance.get.mockResolvedValueOnce(mockResponse);

      const result = await getPage('site123', 'page456');

      expect(result).toBeNull();
    });
  });
});
