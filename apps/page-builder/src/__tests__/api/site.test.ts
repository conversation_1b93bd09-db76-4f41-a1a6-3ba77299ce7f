// Mock axios service using inline factory function
jest.mock('@resola-ai/services-shared', () => ({
  axiosService: {
    instance: {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
    },
  },
}));

// Mock data transform utility
jest.mock('@/utils/data-transform', () => ({
  snakeToCamel: jest.fn(),
}));

import {
  generateShortenUrl,
  getArticleDetail,
  getCategories,
  getDataByShortenId,
  getSite,
  getSiteData,
  getSiteSetting,
  searchArticles,
  sendViewEvent,
} from '@/api/site';
import { snakeToCamel } from '@/utils/data-transform';
import { axiosService } from '@resola-ai/services-shared';

// Type the mocked dependencies
const mockAxiosService = axiosService as jest.Mocked<typeof axiosService>;
const mockSnakeToCamel = snakeToCamel as jest.MockedFunction<typeof snakeToCamel>;

describe('Site API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock console methods to reduce noise in tests
    jest.spyOn(console, 'error').mockImplementation();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('getCategories', () => {
    const mockCategories = [
      {
        id: '1',
        name: 'Category 1',
        slug: 'category-1',
        created_at: '2023-01-01',
      },
      {
        id: '2',
        name: 'Category 2',
        slug: 'category-2',
        created_at: '2023-01-02',
      },
    ];

    const transformedCategories = [
      {
        id: '1',
        name: 'Category 1',
        slug: 'category-1',
        createdAt: '2023-01-01',
      },
      {
        id: '2',
        name: 'Category 2',
        slug: 'category-2',
        createdAt: '2023-01-02',
      },
    ];

    it('should fetch and transform categories', async () => {
      (mockAxiosService.instance.get as jest.Mock).mockResolvedValue({
        data: { response: mockCategories },
      });
      mockSnakeToCamel.mockImplementation((item) =>
        item === mockCategories[0] ? transformedCategories[0] : transformedCategories[1]
      );

      const result = await getCategories('site1');

      expect(mockAxiosService.instance.get).toHaveBeenCalledWith(
        '/sites/site1/data?data_type=faq_categories'
      );
      expect(mockSnakeToCamel).toHaveBeenCalledTimes(2);
      expect(result).toEqual(transformedCategories);
    });

    it('should handle empty categories response', async () => {
      (mockAxiosService.instance.get as jest.Mock).mockResolvedValue({
        data: { response: null },
      });

      const result = await getCategories('site1');

      expect(result).toEqual([]);
    });

    it('should handle missing response data', async () => {
      (mockAxiosService.instance.get as jest.Mock).mockResolvedValue({
        data: {},
      });

      const result = await getCategories('site1');

      expect(result).toEqual([]);
    });

    it('should handle API errors', async () => {
      const apiError = new Error('API Error');
      (mockAxiosService.instance.get as jest.Mock).mockRejectedValue(apiError);

      await expect(getCategories('site1')).rejects.toThrow('API Error');
    });
  });

  describe('getSiteData', () => {
    const mockSiteDataArray = [
      { id: '1', title: 'Article 1', element_id: 'el1' },
      { id: '2', title: 'Article 2', element_id: 'el2' },
    ];

    const transformedSiteData = [
      { id: '1', title: 'Article 1', elementId: 'el1' },
      { id: '2', title: 'Article 2', elementId: 'el2' },
    ];

    it('should fetch site data by data type', async () => {
      (mockAxiosService.instance.get as jest.Mock).mockResolvedValue({
        data: { response: mockSiteDataArray },
      });
      mockSnakeToCamel.mockImplementation((item) =>
        item === mockSiteDataArray[0] ? transformedSiteData[0] : transformedSiteData[1]
      );

      const result = await getSiteData('site1', 'faq_articles');

      expect(mockAxiosService.instance.get).toHaveBeenCalledWith(
        '/sites/site1/data?data_type=faq_articles'
      );
      expect(mockSnakeToCamel).toHaveBeenCalledTimes(2);
      expect(result).toEqual(transformedSiteData);
    });

    it('should handle different data types', async () => {
      const mockCategoryData = [{ id: '1', name: 'Category 1', created_at: '2023-01-01' }];
      const transformedCategoryData = [{ id: '1', name: 'Category 1', createdAt: '2023-01-01' }];

      (mockAxiosService.instance.get as jest.Mock).mockResolvedValue({
        data: { response: mockCategoryData },
      });
      mockSnakeToCamel.mockReturnValue(transformedCategoryData[0]);

      const result = await getSiteData('site2', 'faq_categories');

      expect(mockAxiosService.instance.get).toHaveBeenCalledWith(
        '/sites/site2/data?data_type=faq_categories'
      );
      expect(result).toEqual(transformedCategoryData);
    });

    it('should handle API errors', async () => {
      const apiError = new Error('Site data error');
      (mockAxiosService.instance.get as jest.Mock).mockRejectedValue(apiError);

      await expect(getSiteData('site1', 'faq_articles')).rejects.toThrow('Site data error');
    });
  });

  describe('getSite', () => {
    const mockSite = {
      id: 'site1',
      name: 'Test Site',
      domain: 'test.example.com',
      settings: {},
    };

    it('should fetch site information', async () => {
      (mockAxiosService.instance.get as jest.Mock).mockResolvedValue({
        data: { response: mockSite },
      });

      const result = await getSite('site1');

      expect(mockAxiosService.instance.get).toHaveBeenCalledWith('/sites/site1');
      expect(result).toEqual(mockSite);
    });

    it('should handle API errors', async () => {
      const apiError = new Error('Site fetch error');
      (mockAxiosService.instance.get as jest.Mock).mockRejectedValue(apiError);

      await expect(getSite('site1')).rejects.toThrow('Site fetch error');
    });
  });

  describe('getSiteSetting', () => {
    const mockSettings = {
      theme: {
        colors: { primary: '#007bff' },
        typography: { fontFamily: 'Arial' },
      },
      layout: { showHeader: true },
    };

    it('should fetch site settings', async () => {
      (mockAxiosService.instance.get as jest.Mock).mockResolvedValue({
        data: { response: mockSettings },
      });

      const result = await getSiteSetting('site1');

      expect(mockAxiosService.instance.get).toHaveBeenCalledWith('/sites/site1/setting');
      expect(result).toEqual(mockSettings);
    });

    it('should handle API errors', async () => {
      const apiError = new Error('Settings fetch error');
      (mockAxiosService.instance.get as jest.Mock).mockRejectedValue(apiError);

      await expect(getSiteSetting('site1')).rejects.toThrow('Settings fetch error');
    });
  });

  describe('searchArticles', () => {
    const searchData = {
      query: 'test search',
      siteId: 'site1',
      categoryId: 'cat1',
    };

    const mockSearchResults = {
      data: [
        { id: '1', title: 'Search Result 1', content: 'Content 1' },
        { id: '2', title: 'Search Result 2', content: 'Content 2' },
      ],
    };

    it('should search articles successfully', async () => {
      (mockAxiosService.instance.post as jest.Mock).mockResolvedValue({
        data: { response: mockSearchResults },
      });

      const result = await searchArticles(searchData);

      expect(mockAxiosService.instance.post).toHaveBeenCalledWith(
        '/integrations/kb/search',
        searchData
      );
      expect(result).toEqual(mockSearchResults.data);
    });

    it('should handle search errors gracefully', async () => {
      const searchError = new Error('Search failed');
      (mockAxiosService.instance.post as jest.Mock).mockRejectedValue(searchError);

      const result = await searchArticles(searchData);

      expect(console.error).toHaveBeenCalledWith('Error fetching articles:', searchError);
      expect(result).toEqual([]);
    });

    it('should handle empty search results', async () => {
      (mockAxiosService.instance.post as jest.Mock).mockResolvedValue({
        data: { response: { data: [] } },
      });

      const result = await searchArticles(searchData);

      expect(result).toEqual([]);
    });

    it('should handle missing response data', async () => {
      (mockAxiosService.instance.post as jest.Mock).mockResolvedValue({
        data: {},
      });

      const result = await searchArticles(searchData);

      expect(result).toBeUndefined();
    });
  });

  describe('getArticleDetail', () => {
    const mockArticle = {
      id: 'article1',
      title: 'Test Article',
      content: 'Article content here',
      author: 'Test Author',
      publishedAt: '2023-01-01',
    };

    it('should fetch article details', async () => {
      (mockAxiosService.instance.get as jest.Mock).mockResolvedValue({
        data: { response: mockArticle },
      });

      const result = await getArticleDetail('base1', 'article1');

      expect(mockAxiosService.instance.get).toHaveBeenCalledWith(
        '/integrations/kb/bases/base1/articles/article1'
      );
      expect(result).toEqual(mockArticle);
    });

    it('should handle API errors', async () => {
      const apiError = new Error('Article fetch error');
      (mockAxiosService.instance.get as jest.Mock).mockRejectedValue(apiError);

      await expect(getArticleDetail('base1', 'article1')).rejects.toThrow('Article fetch error');
    });
  });

  describe('generateShortenUrl', () => {
    const urlData = {
      originalUrl: 'https://example.com/very/long/url',
      expiresAt: '2024-01-01',
    };

    const mockShortenResponse = {
      shortenId: 'abc123',
      shortenUrl: 'https://short.ly/abc123',
      originalUrl: 'https://example.com/very/long/url',
    };

    it('should generate shortened URL successfully', async () => {
      (mockAxiosService.instance.post as jest.Mock).mockResolvedValue({
        data: { response: mockShortenResponse },
      });

      const result = await generateShortenUrl(urlData);

      expect(mockAxiosService.instance.post).toHaveBeenCalledWith('/general/shorten', urlData);
      expect(result).toEqual(mockShortenResponse);
    });

    it('should handle URL generation errors gracefully', async () => {
      const urlError = new Error('URL generation failed');
      (mockAxiosService.instance.post as jest.Mock).mockRejectedValue(urlError);

      const result = await generateShortenUrl(urlData);

      expect(console.error).toHaveBeenCalledWith('Error fetching shorten data:', urlError);
      expect(result).toEqual([]);
    });

    it('should handle missing response data', async () => {
      (mockAxiosService.instance.post as jest.Mock).mockResolvedValue({
        data: {},
      });

      const result = await generateShortenUrl(urlData);

      expect(result).toBeUndefined();
    });
  });

  describe('getDataByShortenId', () => {
    const mockShortenData = {
      shortenId: 'abc123',
      originalUrl: 'https://example.com/original',
      createdAt: '2023-01-01',
      clicks: 5,
    };

    it('should fetch data by shorten ID', async () => {
      (mockAxiosService.instance.get as jest.Mock).mockResolvedValue({
        data: { response: mockShortenData },
      });

      const result = await getDataByShortenId('abc123');

      expect(mockAxiosService.instance.get).toHaveBeenCalledWith('/general/shorten/abc123');
      expect(result).toEqual(mockShortenData);
    });

    it('should handle API errors', async () => {
      const apiError = new Error('Shorten data fetch error');
      (mockAxiosService.instance.get as jest.Mock).mockRejectedValue(apiError);

      await expect(getDataByShortenId('abc123')).rejects.toThrow('Shorten data fetch error');
    });
  });

  describe('sendViewEvent', () => {
    const eventData = {
      articleId: 'article1',
      baseId: 'base1',
      userId: 'user1',
      timestamp: Date.now(),
    };

    it('should send view event successfully', async () => {
      (mockAxiosService.instance.post as jest.Mock).mockResolvedValue({
        data: { success: true },
      });

      const result = await sendViewEvent(eventData);

      expect(mockAxiosService.instance.post).toHaveBeenCalledWith(
        '/integrations/kb/events',
        eventData
      );
      expect(result).toBeUndefined(); // Function doesn't return anything on success
    });

    it('should handle view event errors gracefully', async () => {
      const eventError = new Error('Event sending failed');
      (mockAxiosService.instance.post as jest.Mock).mockRejectedValue(eventError);

      const result = await sendViewEvent(eventData);

      expect(console.error).toHaveBeenCalledWith('Error sending event:', eventError);
      expect(result).toEqual([]);
    });

    it('should handle network errors', async () => {
      const networkError = new Error('Network error');
      (mockAxiosService.instance.post as jest.Mock).mockRejectedValue(networkError);

      const result = await sendViewEvent(eventData);

      expect(console.error).toHaveBeenCalledWith('Error sending event:', networkError);
      expect(result).toEqual([]);
    });
  });
});
