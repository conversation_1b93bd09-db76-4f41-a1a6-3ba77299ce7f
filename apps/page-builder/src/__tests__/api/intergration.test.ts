// Mock axios service using inline factory function
jest.mock('@resola-ai/services-shared', () => ({
  axiosService: {
    instance: {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
    },
  },
}));

import { getIntegrations } from '@/api/intergration';
import { IntegrationType } from '@/types/intergration';
import { axiosService } from '@resola-ai/services-shared';

// Type the mocked axiosService
const mockAxiosService = axiosService as jest.Mocked<typeof axiosService>;

describe('Integration API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getIntegrations', () => {
    const mockIntegrations = [
      {
        id: '1',
        title: 'Test Integration 1',
        type: IntegrationType.DECA_CHATWINDOW,
        site_id: 'site1',
        resource_id: 'resource1',
        is_enabled: true,
        script_url: 'https://example.com/script1.js',
        options: {
          page_scope: 'all_pages' as const,
          page_ids: [],
        },
        status: 'connected' as const,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-02T00:00:00Z',
      },
      {
        id: '2',
        title: 'Test Integration 2',
        type: IntegrationType.GOOGLE_ANALYTICS,
        site_id: 'site1',
        resource_id: 'G-123456789',
        is_enabled: true,
        script_url: '',
        options: {
          page_scope: 'specific_pages' as const,
          page_ids: ['page1', 'page2'],
          service_options: {
            faq_search: true,
            faq_article_view: false,
          },
        },
        status: 'connected' as const,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-02T00:00:00Z',
      },
    ];

    it('should fetch integrations with site_id filter', async () => {
      const filter = { site_id: 'site1' };

      mockAxiosService.instance.get.mockResolvedValue({
        data: { response: mockIntegrations },
      });

      const result = await getIntegrations(filter);

      expect(mockAxiosService.instance.get).toHaveBeenCalledWith('/integrations', {
        params: {
          filter: JSON.stringify(filter),
        },
      });
      expect(result).toEqual(mockIntegrations);
    });

    it('should fetch integrations with site_id and type filter', async () => {
      const filter = { site_id: 'site1', type: 'chatwindow' };
      const filteredIntegrations = [mockIntegrations[0]]; // Only chatwindow integration

      mockAxiosService.instance.get.mockResolvedValue({
        data: { response: filteredIntegrations },
      });

      const result = await getIntegrations(filter);

      expect(mockAxiosService.instance.get).toHaveBeenCalledWith('/integrations', {
        params: {
          filter: JSON.stringify(filter),
        },
      });
      expect(result).toEqual(filteredIntegrations);
    });

    it('should return empty array when no integrations found', async () => {
      const filter = { site_id: 'nonexistent' };

      mockAxiosService.instance.get.mockResolvedValue({
        data: { response: [] },
      });

      const result = await getIntegrations(filter);

      expect(mockAxiosService.instance.get).toHaveBeenCalledWith('/integrations', {
        params: {
          filter: JSON.stringify(filter),
        },
      });
      expect(result).toEqual([]);
    });

    it('should handle API errors', async () => {
      const filter = { site_id: 'site1' };
      const apiError = new Error('API Error');

      mockAxiosService.instance.get.mockRejectedValue(apiError);

      await expect(getIntegrations(filter)).rejects.toThrow('API Error');
      expect(mockAxiosService.instance.get).toHaveBeenCalledWith('/integrations', {
        params: {
          filter: JSON.stringify(filter),
        },
      });
    });

    it('should handle missing response data gracefully', async () => {
      const filter = { site_id: 'site1' };

      mockAxiosService.instance.get.mockResolvedValue({
        data: {},
      });

      const result = await getIntegrations(filter);

      expect(result).toBeUndefined();
    });

    it('should serialize complex filter objects correctly', async () => {
      const complexFilter = {
        site_id: 'site1',
        type: 'analytics',
        extra_param: 'value',
      };

      mockAxiosService.instance.get.mockResolvedValue({
        data: { response: [] },
      });

      await getIntegrations(complexFilter);

      expect(mockAxiosService.instance.get).toHaveBeenCalledWith('/integrations', {
        params: {
          filter: JSON.stringify(complexFilter),
        },
      });
    });

    it('should handle different integration types', async () => {
      const googleAnalyticsIntegration = {
        id: '3',
        title: 'GA4 Integration',
        type: IntegrationType.GOOGLE_ANALYTICS,
        site_id: 'site2',
        resource_id: 'G-987654321',
        is_enabled: false,
        script_url: '',
        options: {
          page_scope: 'all_pages' as const,
          page_ids: [],
        },
        status: 'disconnected' as const,
        created_at: '2023-02-01T00:00:00Z',
        updated_at: '2023-02-02T00:00:00Z',
      };

      const filter = { site_id: 'site2', type: 'google_analytics' };

      mockAxiosService.instance.get.mockResolvedValue({
        data: { response: [googleAnalyticsIntegration] },
      });

      const result = await getIntegrations(filter);

      expect(result).toEqual([googleAnalyticsIntegration]);
      expect(result[0].type).toBe(IntegrationType.GOOGLE_ANALYTICS);
      expect(result[0].is_enabled).toBe(false);
      expect(result[0].status).toBe('disconnected');
    });
  });
});
