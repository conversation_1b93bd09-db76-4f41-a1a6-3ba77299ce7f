// Mock data for page-builder tests
import { IntegrationType } from '@/types/intergration';

export const mockPage = {
  id: '123',
  name: 'Test Page',
  content: JSON.stringify({
    ROOT: {
      type: { resolvedName: 'Container' },
      nodes: ['node1'],
      props: {},
      custom: {},
      parent: null,
      isCanvas: true,
      displayName: 'Container',
      hidden: false,
      linkedNodes: {},
    },
    node1: {
      type: { resolvedName: 'TextElement' },
      props: { text: 'Hello World' },
      custom: {},
      parent: 'ROOT',
      isCanvas: false,
      displayName: 'Text',
      hidden: false,
      linkedNodes: {},
    },
  }),
  metadata: {
    description: 'Test page description',
    image: 'https://example.com/image.jpg',
  },
};

export const mockSite = {
  id: 'site123',
  name: 'Test Site',
  visual_assets: {
    favicon: 'https://example.com/favicon.ico',
    thumbnail: 'https://example.com/thumbnail.jpg',
  },
};

export const mockSiteSetting = {
  description: 'Default site description',
  theme: {
    content: {
      typography: {
        body: {
          font_family: 'Arial',
        },
      },
    },
  },
  visual_assets: {
    favicon: 'https://example.com/default-favicon.ico',
  },
};

export const mockIntegrations = [
  {
    id: 'int1',
    title: 'Chatwindow Integration',
    site_id: 'site123',
    type: IntegrationType.DECA_CHATWINDOW,
    resource_id: 'resource1',
    is_enabled: true,
    script_url: 'https://example.com/script.js',
    options: {
      page_scope: 'all_pages' as const,
      page_ids: [],
    },
    status: 'connected' as const,
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  },
  {
    id: 'int2',
    title: 'Google Analytics Integration',
    site_id: 'site123',
    type: IntegrationType.GOOGLE_ANALYTICS,
    resource_id: 'resource2',
    is_enabled: true,
    script_url: 'https://example.com/ga-script.js',
    options: {
      page_scope: 'all_pages' as const,
      page_ids: [],
      service_options: {
        faq_article_view: true,
        faq_search: true,
      },
    },
    status: 'connected' as const,
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  },
];

export const mockCategories = [
  {
    id: 'cat1',
    name: 'Category 1',
    description: 'First category',
    categories: [
      {
        id: 'subcat1',
        name: 'Subcategory 1',
        categories: [],
        articles: [
          {
            id: 'article1',
            title: 'Article 1',
            content: 'Article content',
          },
        ],
      },
    ],
    articles: [],
  },
];

export const mockArticle = {
  id: 'article1',
  title: 'Test Article',
  content: 'Test article content',
  metadata: {
    description: 'Article description',
  },
};

export const mockThemeColors = {
  primary: '#007bff',
  secondary: '#6c757d',
  background: '#ffffff',
  foreground: '#000000',
};

export const mockPageProps = {
  page: mockPage,
  site: mockSite,
  siteSetting: mockSiteSetting,
  integrations: mockIntegrations,
  orgId: 'org123',
};

export const mockSearchParams = new URLSearchParams({
  faq_article_id: 'article1',
  faq_base_id: 'base1',
  faq_category_id: 'cat1',
  faq_sub_category_id: 'subcat1',
  element_id: 'element1',
});

export const mockRouterQuery = {
  slug: 'test-page',
  element_id: 'element1',
  faq_category_id: 'cat1',
  faq_sub_category_id: 'subcat1',
};

// Mock API responses
export const mockApiResponses = {
  pages: [mockPage],
  page: mockPage,
  categories: mockCategories,
  article: mockArticle,
  integrations: mockIntegrations,
};
