import { LinkDestinationType } from '@/types/enum';
import { type Integration, IntegrationType } from '@/types/intergration';
import {
  componentResolver,
  enhanceComponentProps,
  findNestedCategoryOrArticleById,
  getChatwindowIntegrationId,
  getGoogleAnalyticsIntergration,
  processPageData,
  sendGAEvent,
} from '@/utils/page-utils';
import { ButtonVariantType } from '@resola-ai/ui/types/pageBuilder';

// Mock the API calls
jest.mock('@/api/page', () => ({
  getPages: jest.fn(),
  getPage: jest.fn(),
}));

jest.mock('@/api/site', () => ({
  getSite: jest.fn(),
  getSiteSetting: jest.fn(),
}));

jest.mock('@/api/intergration', () => ({
  getIntegrations: jest.fn(),
}));

// Mock the UI components to avoid encoding issues
jest.mock('@resola-ai/ui/components/PageBuilder', () => ({
  TextElement: 'TextElement',
  ButtonElement: 'ButtonElement',
  MediaElement: 'MediaElement',
  DividerElement: 'DividerElement',
  PictureCategoryElement: 'PictureCategoryElement',
  IllustrationCategoryElement: 'IllustrationCategoryElement',
  MapElement: 'MapElement',
  ContainerElement: 'ContainerElement',
}));

// Mock the local components
jest.mock('@/components', () => ({
  ContainerWrapper: 'ContainerWrapper',
  CategoryListElement: 'CategoryListElement',
  CategoryTreeElement: 'CategoryTreeElement',
  ArticleDetailElement: 'ArticleDetailElement',
  BreadcrumbElement: 'BreadcrumbElement',
  SearchElement: 'SearchElement',
}));

jest.mock('@/components/SearchResultElement', () => 'SearchResultElement');
jest.mock('@/components/Section', () => 'Section');

// Mock next/dynamic
jest.mock('next/dynamic', () => {
  return (importFunc: any, options: any) => {
    const Component = () => 'Mocked Dynamic Component';
    return Component;
  };
});

// Import mocked API functions
import { getPage, getPages } from '@/api/page';
import { getSite, getSiteSetting } from '@/api/site';

const mockGetPage = getPage as jest.MockedFunction<typeof getPage>;
const mockGetPages = getPages as jest.MockedFunction<typeof getPages>;
const mockGetSite = getSite as jest.MockedFunction<typeof getSite>;
const mockGetSiteSetting = getSiteSetting as jest.MockedFunction<typeof getSiteSetting>;

// Mock global window object
const mockGtag = jest.fn();
Object.defineProperty(window, 'gtag', {
  value: mockGtag,
  writable: true,
});

Object.defineProperty(window, 'PAGE_INTEGRATIONS', {
  value: {
    is_loaded_ga4: true,
    faq_search: true,
    faq_article_view: true,
  },
  writable: true,
});

describe('page-utils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('componentResolver', () => {
    it('should contain all required component mappings', () => {
      expect(componentResolver).toHaveProperty('TextElement');
      expect(componentResolver).toHaveProperty('ButtonElement');
      expect(componentResolver).toHaveProperty('MediaEmbed');
      expect(componentResolver).toHaveProperty('DividerElement');
      expect(componentResolver).toHaveProperty('SearchElement');
      expect(componentResolver).toHaveProperty('ContainerElement');
      expect(componentResolver).toHaveProperty('ContainerWrapper');
      expect(componentResolver).toHaveProperty('ArticleDetailEmbed');
      expect(componentResolver).toHaveProperty('CategoryTreeElement');
      expect(componentResolver).toHaveProperty('CategoryListElement');
      expect(componentResolver).toHaveProperty('SearchResultElement');
      expect(componentResolver).toHaveProperty('BreadcrumbElement');
      expect(componentResolver).toHaveProperty('FlatArticlesElement');
      expect(componentResolver).toHaveProperty('BoxyArticlesElement');
    });
  });

  describe('sendGAEvent', () => {
    it('should send GA event when gtag is available and event is enabled', () => {
      const eventData = { test: 'data' };

      sendGAEvent('faq_search', eventData);

      expect(mockGtag).toHaveBeenCalledWith('event', 'faq_search', eventData);
    });

    it('should send faq_article_view event when enabled', () => {
      const eventData = { article_id: '123' };

      sendGAEvent('faq_article_view', eventData);

      expect(mockGtag).toHaveBeenCalledWith('event', 'faq_article_view', eventData);
    });

    it('should not send event when GA4 is not loaded', () => {
      window.PAGE_INTEGRATIONS = { is_loaded_ga4: false, faq_search: true, faq_article_view: true };

      sendGAEvent('faq_search', {});

      expect(mockGtag).not.toHaveBeenCalled();

      // Restore
      window.PAGE_INTEGRATIONS = { is_loaded_ga4: true, faq_search: true, faq_article_view: true };
    });

    it('should not send event when event is disabled', () => {
      window.PAGE_INTEGRATIONS = {
        is_loaded_ga4: true,
        faq_search: false,
        faq_article_view: false,
      };

      sendGAEvent('faq_search', {});

      expect(mockGtag).not.toHaveBeenCalled();

      // Restore
      window.PAGE_INTEGRATIONS = { is_loaded_ga4: true, faq_search: true, faq_article_view: true };
    });

    it('should handle errors gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      mockGtag.mockImplementation(() => {
        throw new Error('GA error');
      });

      sendGAEvent('faq_search', {});

      expect(consoleSpy).toHaveBeenCalledWith('Failed to send GA event:', expect.any(Error));

      consoleSpy.mockRestore();
    });
  });

  describe('enhanceComponentProps', () => {
    const mockSiteSetting = {
      theme: {
        content: {
          colors: [
            { name: 'primary', color: '#007bff' },
            { name: 'background', color: '#ffffff' },
            { name: 'foreground', color: '#000000' },
            { name: 'border', color: '#e0e0e0' },
            { name: 'accent', color: '#ffc107' },
            { name: 'text', color: '#666666' },
            { name: 'secondary', color: '#6c757d' },
          ],
          typography: {
            body: { font_family: 'Arial, sans-serif' },
          },
          button: {
            border_radius: 4,
            primary: {
              background_color: 'primary',
              text_color: 'background',
              border_color: 'primary',
            },
            secondary: {
              background_color: 'secondary',
              text_color: 'foreground',
              border_color: 'secondary',
            },
            text: {
              background_color: 'transparent',
              text_color: 'primary',
              border_color: 'transparent',
            },
          },
        },
      },
    };

    const mockPages = [
      { logical_id: 'search-result', url: 'search-results', type: 'faq_search_result' },
      { logical_id: 'article-detail', url: 'article-detail', type: 'faq_article_detail' },
      { logical_id: 'category-list', url: 'categories', type: 'faq_category_list' },
      { logical_id: 'page1', url: 'page-1', type: 'page' },
    ];

    const mockSiteData = { id: 'site1', name: 'Test Site' };

    it('should enhance SearchElement props', () => {
      const component = {
        type: { resolvedName: 'SearchElement' },
        props: {
          borderColor: 'custom-border',
          customProp: 'value',
        },
      };

      const result = enhanceComponentProps({
        key: 'search1',
        component,
        pages: mockPages,
        siteSetting: mockSiteSetting,
        siteId: 'site1',
      });

      expect(result.props).toMatchObject({
        elementId: 'search1',
        searchResultPageSlug: 'search-results',
        articleDetailSlug: 'article-detail',
        borderColor: 'custom-border',
        backgroundColor: '#ffffff',
        textColor: '#000000',
        buttonColor: '#ffc107',
        placeholderColor: '#666666',
        siteId: 'site1',
        customProp: 'value',
      });
    });

    it('should enhance SearchResultElement props', () => {
      const component = {
        type: { resolvedName: 'SearchResultElement' },
        props: {
          iconColor: 'custom-icon',
          categoryType: 'list',
        },
      };

      const result = enhanceComponentProps({
        key: 'searchResult1',
        component,
        pages: mockPages,
        siteSetting: mockSiteSetting,
        siteId: 'site1',
      });

      expect(result.props).toMatchObject({
        elementId: 'searchResult1',
        siteId: 'site1',
        articleDetailSlug: 'article-detail',
        iconColor: 'custom-icon',
        categoryType: 'list',
        illustrationType: 'icon',
        textColor: '#000000',
        itemBackgroundColor: '#ffffff',
      });
    });

    it('should enhance PictureCategoryElement props', () => {
      const component = {
        type: { resolvedName: 'PictureCategoryElement' },
        props: { customProp: 'value' },
      };

      const result = enhanceComponentProps({
        key: 'category1',
        component,
        pages: mockPages,
        siteSetting: mockSiteSetting,
        siteId: 'site1',
      });

      expect(result.props).toMatchObject({
        elementId: 'category1',
        url: 'categories',
        titleColor: '#000000',
        descriptionColor: '#666666',
        customProp: 'value',
      });
    });

    it('should enhance IllustrationCategoryElement props', () => {
      const component = {
        type: { resolvedName: 'IllustrationCategoryElement' },
        props: { customProp: 'value' },
      };

      const result = enhanceComponentProps({
        key: 'illustration1',
        component,
        pages: mockPages,
        siteSetting: mockSiteSetting,
        siteId: 'site1',
      });

      expect(result.props).toMatchObject({
        elementId: 'illustration1',
        url: 'categories',
        titleColor: '#000000',
        descriptionColor: '#666666',
      });
    });

    it('should enhance ArticleDetailEmbed props', () => {
      const component = {
        type: { resolvedName: 'ArticleDetailEmbed' },
        props: { customProp: 'value' },
      };

      const result = enhanceComponentProps({
        key: 'article1',
        component,
        pages: mockPages,
        siteSetting: mockSiteSetting,
        siteId: 'site1',
      });

      expect(result.props).toMatchObject({
        elementId: 'article1',
        textColor: '#000000',
        dividerColor: '#e0e0e0',
        contentBackgroundColor: '#ffffff',
        borderColor: '#e0e0e0',
        fontFamily: 'Arial, sans-serif',
      });
    });

    it('should enhance CategoryTreeElement props', () => {
      const component = {
        type: { resolvedName: 'CategoryTreeElement' },
        props: {
          backgroundColor: 'custom-bg',
          dividerColor: 'custom-divider',
        },
      };

      const result = enhanceComponentProps({
        key: 'tree1',
        component,
        pages: mockPages,
        siteSetting: mockSiteSetting,
        siteId: 'site1',
      });

      expect(result.props).toMatchObject({
        siteId: 'site1',
        articleDetailSlug: 'article-detail',
        categoryListSlug: 'categories',
        styles: {
          root: { backgroundColor: 'custom-bg' },
          item: { borderColor: 'custom-divider !important' },
          chevron: { color: '#666666' },
          label: { color: '#000000' },
          control: { '&:hover': { backgroundColor: '#6c757d !important' } },
        },
      });
    });

    it('should enhance CategoryListElement props', () => {
      const component = {
        type: { resolvedName: 'CategoryListElement' },
        props: { customProp: 'value' },
      };

      const result = enhanceComponentProps({
        key: 'list1',
        component,
        pages: mockPages,
        siteSetting: mockSiteSetting,
        siteId: 'site1',
      });

      expect(result.props).toMatchObject({
        siteId: 'site1',
        articleDetailSlug: 'article-detail',
        textColor: '#000000',
        iconColor: '#666666',
        containerBorderColor: '#e0e0e0',
        contentBackgroundColor: '#ffffff',
      });
    });

    it('should enhance BreadcrumbElement props', () => {
      const component = {
        type: { resolvedName: 'BreadcrumbElement' },
        props: { customProp: 'value' },
      };

      const result = enhanceComponentProps({
        key: 'breadcrumb1',
        component,
        pages: mockPages,
        siteSetting: mockSiteSetting,
        siteId: 'site1',
      });

      expect(result.props).toMatchObject({
        siteId: 'site1',
        articleDetailSlug: 'article-detail',
        categoryListSlug: 'categories',
        borderColor: '#e0e0e0',
      });
    });

    it('should enhance ButtonElement props with primary variant', () => {
      const component = {
        type: { resolvedName: 'ButtonElement' },
        props: {
          variant: ButtonVariantType.Primary,
          link: {
            destination: LinkDestinationType.Page,
            targetId: 'page1',
            url: '',
            text: 'Test Link',
          },
        },
      };

      const result = enhanceComponentProps({
        key: 'button1',
        component,
        pages: mockPages,
        siteSetting: mockSiteSetting,
        siteId: 'site1',
      });

      expect(result.props).toMatchObject({
        link: {
          destination: LinkDestinationType.Page,
          targetId: 'page1',
          url: 'page-1',
          text: 'Test Link',
        },
        color: '#007bff',
        textColor: '#ffffff',
        borderColor: '#007bff',
        radius: 4,
      });
    });

    it('should enhance ButtonElement props with external link', () => {
      const component = {
        type: { resolvedName: 'ButtonElement' },
        props: {
          variant: ButtonVariantType.Secondary,
          link: {
            destination: LinkDestinationType.Url,
            url: 'https://example.com',
            text: 'External Link',
          },
        },
      };

      const result = enhanceComponentProps({
        key: 'button2',
        component,
        pages: mockPages,
        siteSetting: mockSiteSetting,
        siteId: 'site1',
      });

      expect(result.props.link).toEqual({
        destination: LinkDestinationType.Url,
        url: 'https://example.com',
        text: 'External Link',
      });
    });

    it('should enhance TextElement props', () => {
      const component = {
        type: { resolvedName: 'TextElement' },
        props: {
          color: 'custom-color',
          link: {
            destination: LinkDestinationType.Page,
            targetId: 'page1',
          },
        },
      };

      const result = enhanceComponentProps({
        key: 'text1',
        component,
        pages: mockPages,
        siteSetting: mockSiteSetting,
        siteId: 'site1',
      });

      expect(result.props).toMatchObject({
        link: {
          destination: LinkDestinationType.Page,
          targetId: 'page1',
          url: 'page-1',
        },
        typographySettings: mockSiteSetting.theme.content.typography,
        color: 'custom-color',
      });
    });

    it('should enhance MediaEmbed props', () => {
      const component = {
        type: { resolvedName: 'MediaEmbed' },
        props: {
          link: {
            destination: LinkDestinationType.Page,
            targetId: 'page1',
          },
        },
      };

      const result = enhanceComponentProps({
        key: 'media1',
        component,
        pages: mockPages,
        siteSetting: mockSiteSetting,
        siteId: 'site1',
      });

      expect(result.props.link).toEqual({
        destination: LinkDestinationType.Page,
        targetId: 'page1',
        url: 'page-1',
      });
    });

    it('should enhance BoxyArticlesElement props', () => {
      const component = {
        type: { resolvedName: 'BoxyArticlesElement' },
        props: {
          answerBox: {
            backgroundColor: 'primary',
            textColor: 'foreground',
          },
          questionBox: {
            backgroundColor: 'secondary',
          },
        },
      };

      const result = enhanceComponentProps({
        key: 'boxy1',
        component,
        pages: mockPages,
        siteSetting: mockSiteSetting,
        siteId: 'site1',
      });

      expect(result.props.answerBox).toEqual({
        backgroundColor: '#007bff',
        textColor: '#000000',
      });
      expect(result.props.questionBox).toEqual({
        backgroundColor: '#6c757d',
        textColor: '#000000',
      });
    });

    it('should enhance FlatArticlesElement props', () => {
      const component = {
        type: { resolvedName: 'FlatArticlesElement' },
        props: {
          answerBox: null,
          questionBox: {
            backgroundColor: 'accent',
          },
        },
      };

      const result = enhanceComponentProps({
        key: 'flat1',
        component,
        pages: mockPages,
        siteSetting: mockSiteSetting,
        siteId: 'site1',
      });

      expect(result.props.answerBox).toBeNull();
      expect(result.props.questionBox).toEqual({
        backgroundColor: '#ffc107',
        textColor: '#000000',
      });
    });

    it('should enhance DividerElement props', () => {
      const component = {
        type: { resolvedName: 'DividerElement' },
        props: { customProp: 'value' },
      };

      const result = enhanceComponentProps({
        key: 'divider1',
        component,
        pages: mockPages,
        siteSetting: mockSiteSetting,
        siteId: 'site1',
      });

      expect(result.props).toMatchObject({
        color: '#e0e0e0',
        customProp: 'value',
      });
    });

    it('should enhance ContainerElement props', () => {
      const component = {
        type: { resolvedName: 'ContainerElement' },
        props: {
          link: {
            destination: LinkDestinationType.Page,
            targetId: 'page1',
          },
        },
      };

      const result = enhanceComponentProps({
        key: 'container1',
        component,
        pages: mockPages,
        siteSetting: mockSiteSetting,
        siteId: 'site1',
      });

      expect(result.props.link).toEqual({
        destination: LinkDestinationType.Page,
        targetId: 'page1',
        url: 'page-1',
      });
    });

    it('should return component unchanged for unknown types', () => {
      const component = {
        type: { resolvedName: 'UnknownElement' },
        props: { customProp: 'value' },
      };

      const result = enhanceComponentProps({
        key: 'unknown1',
        component,
        pages: mockPages,
        siteSetting: mockSiteSetting,
        siteId: 'site1',
      });

      expect(result).toEqual(component);
    });

    it('should handle components with no theme color properties', () => {
      const component = {
        type: { resolvedName: 'UnknownElement' },
        props: {
          customProp: 'value',
          nonColorProp: 'test',
        },
      };

      const result = enhanceComponentProps({
        key: 'unknown1',
        component,
        pages: mockPages,
        siteSetting: mockSiteSetting,
        siteId: 'site1',
      });

      // Should return component unchanged for unknown types
      expect(result).toEqual(component);
      expect(result.props.customProp).toBe('value');
      expect(result.props.nonColorProp).toBe('test');
    });

    it('should handle responsive color objects', () => {
      const component = {
        type: { resolvedName: 'TextElement' },
        props: {
          color: {
            desktop: 'primary',
            tablet: 'secondary',
            mobile: 'accent',
          },
        },
      };

      const result = enhanceComponentProps({
        key: 'text1',
        component,
        pages: mockPages,
        siteSetting: mockSiteSetting,
        siteId: 'site1',
      });

      expect(result.props.color).toEqual({
        desktop: '#007bff',
        tablet: '#6c757d',
        mobile: '#ffc107',
      });
    });

    it('should handle missing pages gracefully', () => {
      const component = {
        type: { resolvedName: 'SearchElement' },
        props: {},
      };

      const result = enhanceComponentProps({
        key: 'search1',
        component,
        pages: [],
        siteSetting: mockSiteSetting,
        siteId: 'site1',
      });

      expect(result.props.searchResultPageSlug).toBeUndefined();
      expect(result.props.articleDetailSlug).toBeUndefined();
    });
  });

  describe('processPageData', () => {
    it('should process page data successfully', async () => {
      const mockPage = {
        logical_id: 'page1',
        content: {
          component1: {
            type: { resolved_name: 'TextElement' },
            props: { color: 'primary' },
          },
        },
      };

      const mockPages = [{ logical_id: 'page1', url: 'test', type: 'page' }];
      const mockSite = { id: 'site1', name: 'Test Site' };
      const mockSiteSetting = {
        theme: {
          content: {
            colors: [{ name: 'primary', color: '#007bff' }],
          },
        },
      };

      mockGetPage.mockResolvedValue(mockPage);
      mockGetPages.mockResolvedValue(mockPages);
      mockGetSite.mockResolvedValue(mockSite);
      mockGetSiteSetting.mockResolvedValue(mockSiteSetting);

      const result = await processPageData('site1', 'test-page');

      expect(result).toBeDefined();
      expect(result?.site).toEqual(mockSite);
      expect(result?.siteSetting).toEqual(mockSiteSetting);
      expect(result?.pages).toEqual(mockPages);
      expect(result?.page.content).toBeDefined();

      expect(mockGetPage).toHaveBeenCalledWith('site1', 'test-page', undefined);
      expect(mockGetPages).toHaveBeenCalledWith('site1', undefined);
      expect(mockGetSite).toHaveBeenCalledWith('site1', undefined);
      expect(mockGetSiteSetting).toHaveBeenCalledWith('site1', undefined);
    });

    it('should return null when page is not found', async () => {
      mockGetPage.mockResolvedValue(null as any);
      mockGetPages.mockResolvedValue([]);
      mockGetSite.mockResolvedValue({});
      mockGetSiteSetting.mockResolvedValue({});

      const result = await processPageData('site1', 'non-existent');

      expect(result).toBeNull();
    });

    it('should handle API errors gracefully', async () => {
      mockGetPage.mockRejectedValue(new Error('API Error'));
      mockGetPages.mockResolvedValue([]);
      mockGetSite.mockResolvedValue({});
      mockGetSiteSetting.mockResolvedValue({});

      await expect(processPageData('site1', 'test')).rejects.toThrow('API Error');
    });
  });

  describe('getChatwindowIntegrationId', () => {
    it('should return chatwindow integration ID when found', () => {
      const integrations: Integration[] = [
        {
          id: '1',
          title: 'Test Integration',
          type: IntegrationType.DECA_CHATWINDOW,
          site_id: 'site1',
          resource_id: 'chat123',
          is_enabled: true,
          script_url: '',
          options: {
            page_scope: 'all_pages' as const,
            page_ids: [],
          },
          status: 'connected',
          created_at: '2023-01-01',
          updated_at: '2023-01-01',
        },
      ];

      const page = { id: 'page1', title: 'Test Page', logical_id: 'page1' };

      const result = getChatwindowIntegrationId(integrations, page);

      expect(result).toBe('chat123');
    });

    it('should return empty string when no chatwindow integration found', () => {
      const integrations: Integration[] = [
        {
          id: '2',
          title: 'GA Integration',
          type: IntegrationType.GOOGLE_ANALYTICS,
          site_id: 'site1',
          resource_id: 'ga456',
          is_enabled: true,
          script_url: '',
          options: {
            page_scope: 'all_pages' as const,
            page_ids: [],
          },
          status: 'connected',
          created_at: '2023-01-01',
          updated_at: '2023-01-01',
        },
      ];

      const page = { id: 'page1', title: 'Test Page' };

      const result = getChatwindowIntegrationId(integrations, page);

      expect(result).toBe('');
    });

    it('should handle empty integrations array', () => {
      const result = getChatwindowIntegrationId([], {});
      expect(result).toBe('');
    });

    // NEW TESTS FOR UNCOVERED LINES

    it('should handle specific page scope with matching page ID (lines 395-397)', () => {
      const integrations: Integration[] = [
        {
          id: '1',
          title: 'Test Integration',
          type: IntegrationType.DECA_CHATWINDOW,
          site_id: 'site1',
          resource_id: 'chat123',
          is_enabled: true,
          script_url: '',
          options: {
            page_scope: 'specific_pages' as const,
            page_ids: ['page1', 'page2'],
          },
          status: 'connected',
          created_at: '2023-01-01',
          updated_at: '2023-01-01',
        },
      ];

      const page = { id: 'page1', title: 'Test Page', logical_id: 'page1' };

      const result = getChatwindowIntegrationId(integrations, page);

      expect(result).toBe('chat123');
    });

    it('should return empty string for specific page scope with non-matching page ID (lines 395-397)', () => {
      const integrations: Integration[] = [
        {
          id: '1',
          title: 'Test Integration',
          type: IntegrationType.DECA_CHATWINDOW,
          site_id: 'site1',
          resource_id: 'chat123',
          is_enabled: true,
          script_url: '',
          options: {
            page_scope: 'specific_pages' as const,
            page_ids: ['page2', 'page3'],
          },
          status: 'connected',
          created_at: '2023-01-01',
          updated_at: '2023-01-01',
        },
      ];

      const page = { id: 'page1', title: 'Test Page', logical_id: 'page1' };

      const result = getChatwindowIntegrationId(integrations, page);

      expect(result).toBe('');
    });

    it('should handle integration with malformed options that throws error (line 404)', () => {
      const integrations: Integration[] = [
        {
          id: '1',
          title: 'Test Integration',
          type: IntegrationType.DECA_CHATWINDOW,
          site_id: 'site1',
          resource_id: 'chat123',
          is_enabled: true,
          script_url: '',
          options: null as any, // This will cause an error when accessing options?.page_scope
          status: 'connected',
          created_at: '2023-01-01',
          updated_at: '2023-01-01',
        },
      ];

      const page = { id: 'page1', title: 'Test Page', logical_id: 'page1' };

      // Mock the function to throw an error by providing invalid data
      const invalidIntegrations = [
        {
          ...integrations[0],
          options: undefined,
        },
      ];

      // Temporarily replace options with getter that throws
      Object.defineProperty(invalidIntegrations[0], 'options', {
        get() {
          throw new Error('Options error');
        },
        configurable: true,
      });

      const result = getChatwindowIntegrationId(invalidIntegrations as any, page);

      expect(result).toBe('');
    });
  });

  describe('getGoogleAnalyticsIntergration', () => {
    it('should return Google Analytics integration when found', () => {
      const gaIntegration = {
        id: '1',
        title: 'GA4 Integration',
        type: IntegrationType.GOOGLE_ANALYTICS,
        site_id: 'site1',
        resource_id: 'G-123456789',
        is_enabled: true,
        script_url: '',
        options: {
          page_scope: 'all_pages' as const,
          page_ids: [],
          service_options: {
            faq_search: true,
            faq_article_view: false,
          },
        },
        status: 'connected' as const,
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
      };

      const integrations: Integration[] = [gaIntegration];

      const result = getGoogleAnalyticsIntergration(integrations);

      expect(result).toEqual(gaIntegration);
    });

    it('should return null when no GA integration found', () => {
      const integrations: Integration[] = [];

      const result = getGoogleAnalyticsIntergration(integrations);

      expect(result).toBeNull();
    });

    it('should handle empty integrations array', () => {
      const result = getGoogleAnalyticsIntergration([]);
      expect(result).toBeNull();
    });
  });

  describe('findNestedCategoryOrArticleById', () => {
    it('should handle empty categories array', () => {
      const result = findNestedCategoryOrArticleById([], 'cat1', '', '', '');

      expect(result.categories).toBeUndefined();
      expect(result.article).toBeUndefined();
    });

    it('should return result structure for any input', () => {
      const mockData: any[] = [{ categories: [] }];
      const result = findNestedCategoryOrArticleById(mockData as any, 'test', '', '', '');

      expect(result).toHaveProperty('categories');
      expect(result).toHaveProperty('category');
      expect(result).toHaveProperty('subCategory');
      expect(result).toHaveProperty('article');
    });

    // NEW TESTS FOR UNCOVERED LINES

    it('should handle non-array input (line 435)', () => {
      const nonArrayInput = { categories: [] } as any;
      const result = findNestedCategoryOrArticleById(nonArrayInput, 'cat1', '', '', '');

      expect(result).toEqual({
        categories: [],
        category: null,
        subCategory: null,
        article: null,
      });
    });

    it('should find category by categoryId only (lines 441-448)', () => {
      const mockData = [
        {
          elementId: 'element1',
          categories: [
            {
              id: 'cat1',
              name: 'Category 1',
              subType: 'category',
              data: [],
            },
            {
              id: 'cat2',
              name: 'Category 2',
              subType: 'category',
              data: [],
            },
          ],
        },
      ] as any;

      const result = findNestedCategoryOrArticleById(mockData, 'cat1', '', '', '');

      expect(result.category).toEqual({
        id: 'cat1',
        name: 'Category 1',
        subType: 'category',
        data: [],
      });
      expect(result.categories).toEqual(mockData[0]);
      expect(result.subCategory).toBeUndefined();
      expect(result.article).toBeUndefined();
    });

    it('should find subcategory by categoryId and subCategoryId (lines 450-462)', () => {
      const mockData = [
        {
          elementId: 'element1',
          categories: [
            {
              id: 'cat1',
              name: 'Category 1',
              subType: 'category',
              data: [
                {
                  id: 'subcat1',
                  name: 'SubCategory 1',
                  data: [],
                },
                {
                  id: 'subcat2',
                  name: 'SubCategory 2',
                  data: [],
                },
              ],
            },
          ],
        },
      ] as any;

      const result = findNestedCategoryOrArticleById(mockData, 'cat1', 'subcat1', '', '');

      expect(result.category).toEqual(mockData[0].categories[0]);
      expect(result.subCategory).toEqual({
        id: 'subcat1',
        name: 'SubCategory 1',
        data: [],
      });
      expect(result.categories).toEqual(mockData[0]);
      expect(result.article).toBeUndefined();
    });

    it('should find article in subcategory (lines 464-479)', () => {
      const mockData = [
        {
          elementId: 'element1',
          categories: [
            {
              id: 'cat1',
              name: 'Category 1',
              subType: 'category',
              data: [
                {
                  id: 'subcat1',
                  name: 'SubCategory 1',
                  data: [
                    {
                      value: 'article1',
                      title: 'Article 1',
                    },
                    {
                      value: 'article2',
                      title: 'Article 2',
                    },
                  ],
                },
              ],
            },
          ],
        },
      ] as any;

      const result = findNestedCategoryOrArticleById(mockData, 'cat1', 'subcat1', 'article1', '');

      expect(result.category).toEqual(mockData[0].categories[0]);
      expect(result.subCategory).toEqual(mockData[0].categories[0].data[0]);
      expect(result.article).toEqual({
        value: 'article1',
        title: 'Article 1',
      });
      expect(result.categories).toEqual(mockData[0]);
    });

    it('should find article directly in category (lines 480-490)', () => {
      const mockData = [
        {
          elementId: 'element1',
          categories: [
            {
              id: 'cat1',
              name: 'Category 1',
              subType: 'article',
              data: [
                {
                  value: 'article1',
                  title: 'Article 1',
                },
                {
                  value: 'article2',
                  title: 'Article 2',
                },
              ],
            },
          ],
        },
      ] as any;

      const result = findNestedCategoryOrArticleById(mockData, 'cat1', '', 'article1', '');

      expect(result.category).toEqual(mockData[0].categories[0]);
      expect(result.article).toEqual({
        value: 'article1',
        title: 'Article 1',
      });
      expect(result.subCategory).toBeUndefined();
      expect(result.categories).toEqual(mockData[0]);
    });

    it('should find by elementId and return matched categories (lines 498,530)', () => {
      const mockData = [
        {
          elementId: 'element1',
          categories: [
            {
              id: 'cat1',
              name: 'Category 1',
              subType: 'category',
              data: [],
            },
          ],
        },
        {
          elementId: 'element2',
          categories: [
            {
              id: 'cat2',
              name: 'Category 2',
              subType: 'category',
              data: [],
            },
          ],
        },
      ] as any;

      const result = findNestedCategoryOrArticleById(mockData, '', '', '', 'element2');

      expect(result.categories).toEqual(mockData[1]);
      expect(result.category).toBeNull();
      expect(result.subCategory).toBeNull();
      expect(result.article).toBeNull();
    });

    it('should find by elementId and categoryId (line 503)', () => {
      const mockData = [
        {
          elementId: 'element1',
          categories: [
            {
              id: 'cat1',
              name: 'Category 1',
              subType: 'category',
              data: [],
            },
            {
              id: 'cat2',
              name: 'Category 2',
              subType: 'category',
              data: [],
            },
          ],
        },
      ] as any;

      const result = findNestedCategoryOrArticleById(mockData, 'cat2', '', '', 'element1');

      expect(result.categories).toEqual(mockData[0]);
      expect(result.category).toEqual(mockData[0].categories[1]);
      expect(result.subCategory).toBeNull();
      expect(result.article).toBeNull();
    });

    it('should find by elementId, categoryId and subCategoryId (line 509)', () => {
      const mockData = [
        {
          elementId: 'element1',
          categories: [
            {
              id: 'cat1',
              name: 'Category 1',
              subType: 'category',
              data: [
                {
                  id: 'subcat1',
                  name: 'SubCategory 1',
                  data: [],
                },
                {
                  id: 'subcat2',
                  name: 'SubCategory 2',
                  data: [],
                },
              ],
            },
          ],
        },
      ] as any;

      const result = findNestedCategoryOrArticleById(mockData, 'cat1', 'subcat2', '', 'element1');

      expect(result.categories).toEqual(mockData[0]);
      expect(result.category).toEqual(mockData[0].categories[0]);
      expect(result.subCategory).toEqual(mockData[0].categories[0].data[1]);
      expect(result.article).toBeNull();
    });

    it('should find article with elementId matching in subcategory (lines 515-516)', () => {
      const mockData = [
        {
          elementId: 'element1',
          categories: [
            {
              id: 'cat1',
              name: 'Category 1',
              subType: 'category',
              data: [
                {
                  id: 'subcat1',
                  name: 'SubCategory 1',
                  data: [
                    {
                      value: 'article1',
                      title: 'Article 1',
                    },
                  ],
                },
              ],
            },
          ],
        },
      ] as any;

      const result = findNestedCategoryOrArticleById(
        mockData,
        'cat1',
        'subcat1',
        'article1',
        'element1'
      );

      expect(result.categories).toEqual(mockData[0]);
      expect(result.category).toEqual(mockData[0].categories[0]);
      expect(result.subCategory).toEqual(mockData[0].categories[0].data[0]);
      expect(result.article).toEqual({
        value: 'article1',
        title: 'Article 1',
      });
    });

    it('should find article with elementId matching directly in category (lines 517-518)', () => {
      const mockData = [
        {
          elementId: 'element1',
          categories: [
            {
              id: 'cat1',
              name: 'Category 1',
              subType: 'article',
              data: [
                {
                  value: 'article1',
                  title: 'Article 1',
                },
                {
                  value: 'article2',
                  title: 'Article 2',
                },
              ],
            },
          ],
        },
      ] as any;

      const result = findNestedCategoryOrArticleById(mockData, 'cat1', '', 'article2', 'element1');

      expect(result.categories).toEqual(mockData[0]);
      expect(result.category).toEqual(mockData[0].categories[0]);
      expect(result.subCategory).toBeNull();
      expect(result.article).toEqual({
        value: 'article2',
        title: 'Article 2',
      });
    });

    it('should stop at first match using isFoundFirstMatch flag', () => {
      const mockData = [
        {
          elementId: 'element1',
          categories: [
            {
              id: 'cat1',
              name: 'Category 1',
              subType: 'category',
              data: [],
            },
          ],
        },
        {
          elementId: 'element2',
          categories: [
            {
              id: 'cat1', // Same ID but should not be found due to first match
              name: 'Category 1 Duplicate',
              subType: 'category',
              data: [],
            },
          ],
        },
      ] as any;

      const result = findNestedCategoryOrArticleById(mockData, 'cat1', '', '', '');

      // Should return the first match, not the duplicate
      expect(result.category).toEqual({
        id: 'cat1',
        name: 'Category 1',
        subType: 'category',
        data: [],
      });
      expect(result.categories).toEqual(mockData[0]);
    });
  });
});
