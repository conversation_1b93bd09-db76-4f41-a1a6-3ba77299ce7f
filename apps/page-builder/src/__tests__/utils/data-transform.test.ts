import { camelToSnake, removeNullProps, snakeToCamel } from '@/utils/data-transform';

describe('data-transform utilities', () => {
  describe('removeNullProps', () => {
    it('should remove null and undefined properties from props', () => {
      const input = {
        someKey: {
          props: {
            a: 'value',
            b: null,
            c: undefined,
            d: 0,
            e: false,
            f: '',
          },
        },
      };

      const result = removeNullProps(input);
      expect(result).toBeDefined();
      expect(result!.someKey.props).toEqual({
        a: 'value',
        d: 0,
        e: false,
        f: '',
      });
    });

    it('should remove empty arrays and objects from props', () => {
      const input = {
        component: {
          props: {
            validProp: 'value',
            emptyArray: [],
            emptyObject: {},
            nonEmptyArray: [1, 2, 3],
            nonEmptyObject: { key: 'value' },
          },
        },
      };

      const result = removeNullProps(input);
      expect(result).toBeDefined();
      expect(result!.component.props).toEqual({
        validProp: 'value',
        nonEmptyArray: [1, 2, 3],
        nonEmptyObject: { key: 'value' },
      });
    });

    it('should handle objects without props', () => {
      const input = {
        someKey: {
          otherProperty: 'value',
        },
      };

      const result = removeNullProps(input);
      expect(result).toEqual(input);
    });

    it('should handle undefined input', () => {
      const result = removeNullProps(undefined);
      expect(result).toBeUndefined();
    });

    it('should handle nested objects', () => {
      const input = {
        parent: {
          props: {
            validProp: 'value',
            nullProp: null,
          },
        },
        child: {
          props: {
            anotherProp: 'another value',
            undefinedProp: undefined,
          },
        },
      };

      const result = removeNullProps(input);
      expect(result).toBeDefined();

      expect(result!.parent.props).toEqual({
        validProp: 'value',
      });
      expect(result!.child.props).toEqual({
        anotherProp: 'another value',
      });
    });
  });

  describe('snakeToCamel', () => {
    it('should convert object keys from snake_case to camelCase', () => {
      const input = {
        snake_case_key: 'value',
        another_key: 'another value',
      };

      const result = snakeToCamel(input);

      expect(result).toEqual({
        snakeCaseKey: 'value',
        anotherKey: 'another value',
      });
    });

    it('should handle nested objects', () => {
      const input = {
        outer_key: {
          inner_snake_key: 'value',
          another_inner_key: 'another value',
        },
      };

      const result = snakeToCamel(input);

      expect(result).toEqual({
        outerKey: {
          innerSnakeKey: 'value',
          anotherInnerKey: 'another value',
        },
      });
    });

    it('should handle arrays', () => {
      const input = {
        array_key: [{ snake_item: 'value1' }, { another_snake_item: 'value2' }],
      };

      const result = snakeToCamel(input);

      expect(result).toEqual({
        arrayKey: [{ snakeItem: 'value1' }, { anotherSnakeItem: 'value2' }],
      });
    });

    it('should handle primitive values', () => {
      expect(snakeToCamel('string')).toBe('string');
      expect(snakeToCamel(123)).toBe(123);
      expect(snakeToCamel(null)).toBe(null);
      expect(snakeToCamel(undefined)).toBe(undefined);
    });

    it('should handle empty objects and arrays', () => {
      expect(snakeToCamel({})).toEqual({});
      expect(snakeToCamel([])).toEqual([]);
    });
  });

  describe('camelToSnake', () => {
    it('should convert object keys from camelCase to snake_case', () => {
      const input = {
        camelCaseKey: 'value',
        anotherKey: 'another value',
      };

      const result = camelToSnake(input);

      expect(result).toEqual({
        camel_case_key: 'value',
        another_key: 'another value',
      });
    });

    it('should handle nested objects', () => {
      const input = {
        outerKey: {
          innerCamelKey: 'value',
          anotherInnerKey: 'another value',
        },
      };

      const result = camelToSnake(input);

      expect(result).toEqual({
        outer_key: {
          inner_camel_key: 'value',
          another_inner_key: 'another value',
        },
      });
    });

    it('should handle primitive values', () => {
      expect(camelToSnake('string')).toBe('string');
      expect(camelToSnake(123)).toBe(123);
      expect(camelToSnake(null)).toBe(null);
      expect(camelToSnake(undefined)).toBe(undefined);
    });
  });
});
