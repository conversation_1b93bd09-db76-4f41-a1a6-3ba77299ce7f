import { emotionCache } from '@/cache';

// Mock @emotion/cache
jest.mock('@emotion/cache', () => {
  const mockCache = {
    key: 'css',
    sheet: { container: null },
    insert: jest.fn(),
    nonce: undefined,
  };
  return {
    __esModule: true,
    default: jest.fn(() => mockCache),
  };
});

describe('emotionCache', () => {
  it('should be defined', () => {
    expect(emotionCache).toBeDefined();
  });

  it('should have correct properties', () => {
    expect(emotionCache).toHaveProperty('key', 'css');
    expect(emotionCache).toHaveProperty('sheet');
    expect(emotionCache).toHaveProperty('insert');
  });

  it('should be created with correct configuration', () => {
    const createCache = require('@emotion/cache').default;
    expect(createCache).toHaveBeenCalledWith({ key: 'css' });
  });

  it('should be the same instance when imported multiple times', () => {
    // Re-import to test singleton behavior
    const { emotionCache: emotionCache2 } = require('@/cache');
    expect(emotionCache).toBe(emotionCache2);
  });

  it('should have the expected cache structure', () => {
    expect(typeof emotionCache.insert).toBe('function');
    expect(emotionCache.key).toBe('css');
    expect(emotionCache.sheet).toBeDefined();
  });
});
