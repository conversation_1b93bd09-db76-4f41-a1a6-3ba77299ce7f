import type { ISuccessList } from '@resola-ai/models';

export interface ISuccessListWithPagination<T> extends ISuccessList<T> {
  pagination: IPagination;
}

export interface IPagination {
  totalItem: number;
  totalPage: number;
  perPage: number;
  currentPage: number;
}

export interface AssetApiResponse {
  response: {
    id: string;
    name: string;
    mime_type: string;
    size: number;
    path: string;
    url: string;
    resource_id: string;
    resource_type: string;
  };
}
