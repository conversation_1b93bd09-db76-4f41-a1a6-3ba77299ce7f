import type { FAQCategoryType } from '@resola-ai/ui/types/pageBuilder';

export interface Article {
  label: string;
  value: string;
  path: string[];
}

export interface CategoryArticle {
  label?: string;
  name: string;
  type: FAQCategoryType.Article;
  description: string;
  id: string;
  value: string;
  parent_dir: string;
  parent_dir_path: string;
  parent_dir_breadcrumb_array: string[];
}

export interface Category {
  name: string;
  description: string;
  type: Omit<FAQCategoryType, 'none'>;
  subType: FAQCategoryType;
  id?: string;
  data: Category[] | CategoryArticle[];
  image?: string;
}
