import { getIntegrations } from '@/api/intergration';
import Page<PERSON>enderer from '@/components/PageRenderer';
import { AppConfig } from '@/configs/app';
import { PageProvider } from '@/providers';
import type { Integration } from '@/types/intergration';
import { processPageData, processTemplateData } from '@/utils/page-utils';
import { axiosService } from '@resola-ai/services-shared';
import type { GetServerSideProps } from 'next';
import { useEffect } from 'react';

const isStaticExport = process.env.ENABLE_STATIC_EXPORT === 'true';

export default function PreviewPage({
  page,
  pages,
  site,
  siteSetting,
  integrations,
  orgId,
  accessToken,
}: {
  page: Record<string, any>;
  pages: Record<string, any>[];
  site?: Record<string, any>;
  siteSetting: Record<string, any>;
  integrations: Integration[];
  orgId: string;
  accessToken: string;
}) {
  useEffect(() => {
    axiosService.setAccessToken(accessToken);
    axiosService.setOrgId(orgId);
  }, [orgId, accessToken]);

  return (
    <PageProvider page={page} pages={pages}>
      <PageRenderer
        page={page}
        site={site}
        siteSetting={siteSetting}
        integrations={integrations}
        orgId={orgId}
      />
    </PageProvider>
  );
}

export const getServerSideProps: GetServerSideProps | undefined = !isStaticExport
  ? async ({ params, req }) => {
      if (!params?.slug) return { notFound: true };
      const siteId = req.cookies.preview_site_id;
      const accessToken = req.cookies.preview_access_token;
      const orgId = req.cookies.preview_org_id;
      const templateId = req.cookies.preview_template_id || '';
      let isTemplate = false;
      let versionId = '';

      try {
        // Extract query params from url
        // Second parameter is the base url, it's required for new URL to work, What ever it is, it will be ignored
        const url = new URL(req.url as string, AppConfig.BASE_URL || 'http://localhost');
        versionId = url.searchParams.get('versionId') || '';
        if (templateId) {
          // isTemplate is true when having query params templateId
          isTemplate = true;
        }
      } catch (e) {
        console.error('Error in getServerSideProps:', e);
      }

      if (!accessToken) {
        return {
          notFound: true,
          props: {
            error:
              'Access token is required for preview mode. Please set preview_access_token cookie',
          },
        };
      }

      if (!orgId) {
        return {
          notFound: true,
          props: {
            error: 'Org ID is required for preview mode. Please set preview_org_id cookie',
          },
        };
      }

      try {
        axiosService.setAccessToken(accessToken);
        axiosService.setOrgId(orgId as string);

        // If it's a template, we need to process the template data
        if (isTemplate) {
          const template = await processTemplateData(templateId, params.slug as string);
          if (!template) return { notFound: true, props: {} };
          // If it's a template, we need to process the template data
          return {
            props: {
              ...template,
              isPreview: true,
              orgId,
              accessToken,
            },
          };
        }
        // End of template processing

        if (!siteId) {
          return {
            notFound: true,
            props: {
              error: 'Site ID is required for preview mode. Please set preview_site_id cookie',
            },
          };
        }

        const [result, integrations] = await Promise.all([
          processPageData(siteId, params.slug as string, versionId),
          getIntegrations({ site_id: siteId }),
        ]);

        if (!result) return { notFound: true, props: {} };

        return {
          props: {
            ...result,
            isPreview: true,
            integrations,
            orgId,
            accessToken,
          },
        };
      } catch (e) {
        console.error('Error in getServerSideProps:', e);
        return {
          notFound: true,
          props: {
            error: 'Failed to load preview page',
          },
        };
      }
    }
  : undefined;
