import { AppConfig } from '@/configs';
import { AppMantineEmotionProvider } from '@/providers/AppMantineEmotionProvider';
import { axiosService, datadogService } from '@resola-ai/services-shared';
import type { AppProps } from 'next/app';
import '@mantine/core/styles.css';
import '@/styles/global.css';
import { useAutoHeight } from '@/hooks/useAutoHeight';
import { useUrlBinding } from '@/hooks/useUrlBinding';
import { version as pageBuilderVersion } from '../../package.json';

axiosService.init(AppConfig.API_SERVER_URL);
axiosService.setAccessToken(process.env.BUILD_ACCESS_TOKEN as string);
axiosService.setOrgId(process.env.NEXT_PUBLIC_ORG_ID as string);

// Initialize Datadog service
/* eslint-disable turbo/no-undeclared-env-vars */
datadogService.init({
  applicationId: process.env.NEXT_PUBLIC_DATADOG_APPLICATION_ID ?? '',
  clientToken: process.env.NEXT_PUBLIC_DATADOG_CLIENT_TOKEN ?? '',
  site: process.env.NEXT_PUBLIC_DATADOG_SITE ?? '',
  service: process.env.NEXT_PUBLIC_DATADOG_SERVICE ?? '',
  env: process.env.NEXT_PUBLIC_DATADOG_ENV ?? '',
  version: pageBuilderVersion,
});

function AppWithUrlBinding({ Component, pageProps }: AppProps) {
  // Embed hooks features
  useUrlBinding(true);
  useAutoHeight(true);
  // End of embed hooks features

  return (
    <AppMantineEmotionProvider>
      <Component {...pageProps} />
    </AppMantineEmotionProvider>
  );
}

export default AppWithUrlBinding;
