import { getIntegrations } from '@/api/intergration';
import { getPages } from '@/api/page';
import PageRenderer from '@/components/PageRenderer';
import { PageProvider } from '@/providers';
import type { Integration } from '@/types/intergration';
import { processPageData } from '@/utils/page-utils';
import { axiosService } from '@resola-ai/services-shared';
import type { GetStaticPaths, GetStaticProps } from 'next';

export default function Page({
  page,
  pages,
  site,
  siteSetting,
  integrations,
  orgId,
}: {
  page: Record<string, any>;
  pages: Record<string, any>[];
  site?: Record<string, any>;
  siteSetting: Record<string, any>;
  integrations: Integration[];
  orgId: string;
}) {
  return (
    <PageProvider page={page} pages={pages}>
      <PageRenderer
        page={page}
        site={site}
        siteSetting={siteSetting}
        integrations={integrations}
        orgId={orgId}
      />
    </PageProvider>
  );
}

export const getStaticPaths: GetStaticPaths = async () => {
  try {
    if (process.env.ENABLE_STATIC_EXPORT !== 'true') {
      return {
        paths: [],
        fallback: false,
      };
    }
    const siteId = process.env.NEXT_PUBLIC_SITE_ID as string;
    axiosService.setOrgId(process.env.NEXT_PUBLIC_ORG_ID as string);
    if (!siteId) {
      throw new Error('NEXT_PUBLIC_SITE_ID environment variable is not defined');
    }
    const pages = await getPages(siteId);
    const paths = pages.map((page) => ({
      params: { slug: page.url },
    }));

    return {
      paths,
      fallback: false,
    };
  } catch (error) {
    console.error('Error in getStaticPaths:', error);
    return {
      paths: [],
      fallback: false,
    };
  }
};

export const getStaticProps: GetStaticProps = async ({ params }) => {
  const siteId = process.env.NEXT_PUBLIC_SITE_ID as string;
  if (!params?.slug) return { notFound: true };

  try {
    const [result, integrations] = await Promise.all([
      processPageData(siteId, params.slug as string),
      getIntegrations({ site_id: siteId }),
    ]);

    if (!result) return { notFound: true, props: {} };

    return {
      props: {
        ...result,
        integrations,
        orgId: process.env.NEXT_PUBLIC_ORG_ID as string,
      },
    };
  } catch (e) {
    console.error('Error in getStaticProps:', e);
    return { notFound: true, props: {} };
  }
};
