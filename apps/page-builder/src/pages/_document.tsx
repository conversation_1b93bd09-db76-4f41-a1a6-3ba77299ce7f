import { getSiteSetting } from '@/api/site';
import { emotionCache } from '@/cache';
import createEmotionServer from '@emotion/server/create-instance';
import { ColorSchemeScript } from '@mantine/core';
import { createGetInitialProps } from '@mantine/emotion';
import NextDocument, { Head, Html, Main, NextScript, type DocumentContext } from 'next/document';

type CustomProps = {
  headerCode?: string;
  footerCode?: string;
  embedScriptUrl?: string;
};

export default function Document(props: CustomProps) {
  const { headerCode = '', footerCode = '', embedScriptUrl = '' } = props;

  return (
    <Html lang='en' suppressHydrationWarning>
      <Head>
        <ColorSchemeScript defaultColorScheme='auto' />
        <link
          href='https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@400;500;700&display=swap'
          rel='stylesheet'
        />
        <link
          href='https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+JP&family=Kaisei+Decol&family=Kiwi+Maru&family=LXGW+WenKai+TC&family=M+PLUS+1:wght@100..900&family=M+PLUS+2:wght@100..900&family=Murecho:wght@100..900&family=Noto+Serif+JP:wght@200..900&family=Shippori+Mincho&family=Zen+Maru+Gothic&family=Zen+Old+Mincho&display=swap'
          rel='stylesheet'
        />
        <div dangerouslySetInnerHTML={{ __html: headerCode }} />
        {embedScriptUrl && <script src={embedScriptUrl} async defer />}
      </Head>
      <body>
        <Main />
        <NextScript />
        <div dangerouslySetInnerHTML={{ __html: footerCode }} />
      </body>
    </Html>
  );
}

const stylesServer = createEmotionServer(emotionCache);

Document.getInitialProps = async (ctx: DocumentContext) => {
  const initialProps = await createGetInitialProps(NextDocument, stylesServer)(ctx);
  const siteId = process.env.NEXT_PUBLIC_SITE_ID as string;

  try {
    const siteSetting = await getSiteSetting(siteId);
    return {
      ...initialProps,
      headerCode: siteSetting?.html?.header || '',
      footerCode: siteSetting?.html?.footer || '',
    };
  } catch (error) {
    return {
      ...initialProps,
      headerCode: '',
      footerCode: '',
    };
  }
};
