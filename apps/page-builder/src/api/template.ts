import type { Template } from '@/types';
import { axiosService } from '@resola-ai/services-shared';

// Get page settings, theme, etc.
export const getTemplateMetadata = async (siteId: string) => {
  const templates = await axiosService.instance.get(`/templates/${siteId}`);
  return templates.data.response;
};

// Getting Page data as template which same data will get getPage. To get 'content' of page to render
export const getTemplate = async (templateId: string, logicalIdOrSlug: string) => {
  const template = await axiosService.instance.get(
    `/templates/${templateId}/pages/${logicalIdOrSlug}`
  );
  return template.data.response as Template;
};

// Get all pages of template
export const getTemplatePages = async (templateId: string) => {
  const templates = await axiosService.instance.get(`/templates/${templateId}/pages`);
  return templates.data.response;
};
