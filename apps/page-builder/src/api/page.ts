import type { Page } from '@/types/page';
import { axiosService } from '@resola-ai/services-shared';

export const getPages = async (siteId: string, versionId?: string) => {
  const url = versionId
    ? `/sites/${siteId}/pages?version_id=${versionId}`
    : `/sites/${siteId}/pages`;
  const pages = await axiosService.instance.get(url);
  return pages.data.response;
};

export const getPage = async (siteId: string, pageId: string, versionId?: string) => {
  const url = versionId
    ? `/sites/${siteId}/pages/${pageId}?version_id=${versionId}`
    : `/sites/${siteId}/pages/${pageId}`;
  const page = await axiosService.instance.get(url);
  return page.data.response as Page;
};
