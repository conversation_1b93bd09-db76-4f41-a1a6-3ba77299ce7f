/**
 * @jest-environment jsdom
 */
/** @type {import('jest').Config} */
const config = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/setup-jest.js'],
  transform: {
    '\\.(js|jsx|ts|tsx)$': ['babel-jest', { configFile: './jest-babel.config.js' }],
  },
  transformIgnorePatterns: ['node_modules/(?!@ngrx|(?!@blocknote/react)|ng-dynamic)'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  moduleDirectories: ['node_modules', '<rootDir>'],
  roots: ['<rootDir>'],
  testRegex: '(/__tests__/.*\\.(test|spec))\\.[jt]sx?$',
  testPathIgnorePatterns: ['/node_modules/', '/fixtures/', 'testUtils', 'mockData'],
  verbose: true,
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.test.{js,jsx,ts,tsx}',
    '!src/**/*.spec.{js,jsx,ts,tsx}',
    '!src/__tests__/fixtures/**',
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'json', 'html', 'lcov', 'text-summary'],
};

module.exports = config;
