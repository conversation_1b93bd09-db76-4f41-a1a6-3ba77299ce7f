{"editor.codeActionsOnSave": {"source.fixAll.biome": "explicit", "source.organizeImports.biome": "explicit"}, "editor.defaultFormatter": "biomejs.biome", "eslint.workingDirectories": [{"mode": "auto"}], "files.exclude": {"**/.turbo": true, "**/coverage": false, "**/dist": true, "**/node_modules": true}, "files.watcherExclude": {"**/.turbo": true, "**/coverage": false, "**/dist": true, "**/node_modules": true}, "IDX.aI.enableInlineCompletion": true, "IDX.aI.enableCodebaseIndexing": true, "sonarlint.connectedMode.project": {"connectionId": "resola", "projectKey": "resola-ai_deca-apps"}, "cSpell.words": ["craftjs", "Infor", "tolgee"], "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}}